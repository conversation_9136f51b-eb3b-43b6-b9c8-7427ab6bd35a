const { defineConfig } = require('@vue/cli-service')
const { join } = require("path"); // eslint-disable-line
const webpack = require('webpack')
const path = require('path')
const resolve = (dir) => join(__dirname, dir)
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin')
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')

module.exports = defineConfig({
  publicPath: './',
  lintOnSave: false,
  productionSourceMap: false,
  transpileDependencies: true, //  ['js-base64']
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    host: '0.0.0.0',
    port: 18080,
    open: true,
    client: {
      overlay: false
    }
  },
  chainWebpack: (config) => {
    if (process.env.use_analyzer) {
      config
        .plugin('webpack-bundle-analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);
    }

    config.plugin('monaco-editor').use(MonacoWebpackPlugin, [
      {
        // Languages are loaded on demand at runtime
        languages: ['json', 'javascript', 'html', 'xml', 'python', 'markdown']
      }
    ]);
    // svg
    config.module.rule('svg').exclude.add(resolve('src/assets/images/svg-icon/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/images/svg-icon/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'svg-[name]'
      })
      .end();

    config.plugin('xcTime').use(SpeedMeasurePlugin);
  },
  configureWebpack: {
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename]
      }
    },
    externals: {
      vue: 'Vue',
      'vue-router': 'VueRouter',
      'element-ui': 'ELEMENT'
    },
    module: {
      rules: [
        // { parser: { system: false } } // 避免与systemjs冲突
      ]
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        process: 'process/browser'
      }
    },
    plugins: [
      // 添加node.js环境变量，webpack5中node.js polyfills被移除
      // 详情见：https://stackoverflow.com/questions/65018431/webpack-5-uncaught-referenceerror-process-is-not-defined
      new webpack.ProvidePlugin({
        process: 'process/browser'
      }),
      new webpack.EnvironmentPlugin({
        NODE_ENV: 'development', // 默认值为 'development'
      }),
    ],
    // 在这里添加 source map 控制
    // devtool: process.env.NODE_ENV === 'production' ? false : 'source-map'
  },
  css: {
    extract: false
  }
});
