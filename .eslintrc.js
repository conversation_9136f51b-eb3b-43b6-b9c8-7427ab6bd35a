module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', 'plugin:vue/recommended', '@vue/standard', 'prettier'],
  parserOptions: {
    parser: '@babel/eslint-parser'
  },
  rules: {
    'no-alert': 1, // 提示alert
    'no-var': 2, // 禁用var
    'no-constant-condition': 2, // 禁止在条件中使用常量表达式 if(true) if(1)
    'no-empty': 1, // 块语句中的内容不能为空
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-redeclare': 2, // 禁止重复声明变量
    // indent: [2, 2], // 使用缩进量为2
    // semi: [1], // 不使用分号
    quotes: [2, 'single'] // 使用单引号
  },
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
      env: {
        jest: true
      }
    }
  ]
};
