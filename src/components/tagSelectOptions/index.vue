<template>
  <div class="optionContent">
    <div class="left-content" :style="{ width: !tagList?.length ? '80%' : '60%' }">
      <el-tooltip effect="dark" placement="top-start" :gpu-acceleration="false">
        <template #content>
          <div style="max-width: 500px; white-space: pre-wrap">
            {{ labelName }}
          </div>
        </template>
        <div class="labelNameStyle">{{ labelName }}</div>
      </el-tooltip>
    </div>
    <slot name="labelExtra"></slot>
    <div v-show="tagList?.length" class="center-content">
      <el-tag
        class="optionLabel"
        v-for="ite in tagList?.slice(0, showNum)"
        :key="ite?.id"
        size="small"
        >{{ ite?.[tagName] }}</el-tag
      >
      <el-tooltip class="box-item" effect="light" placement="top-start">
        <template #content>
          <el-tag v-for="it in tagList" :key="it?.id" style="margin-left: 8px" size="small">
            <span>{{ it?.[tagName] }}</span>
          </el-tag>
        </template>
        <span v-show="tagList?.length > showNum" style="cursor: pointer">...</span>
      </el-tooltip>
    </div>
    <slot name="extra"></slot>
  </div>
</template>
<script>
export default {
  name: 'TagSelectOptions',
  components: {},
  props: {
    tags: {
      type: Array,
      required: true,
      default: () => []
    },
    showNum: {
      type: Number,
      default: 1
    },
    labelName: {
      type: String,
      required: true
    },
    tagName: {
      type: String,
      required: true
    }
    // extra: {
    //   type: String,
    //   required: true
    // }
  },
  data() {
    return {
      tagList: []
    };
  },
  watch: {
    tags: {
      deep: true,
      handler: function (value) {
        this.tagList = value;
      },
      immediate: true
    }
  },
  async created() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.optionContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  // height: 100%;

  .labelNameStyle {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .optionLabel {
    margin-right: 5px;
  }
}

.left-content {
  display: flex;
  align-items: center;
  margin-right: 20px;
  :deep(.el-tag) {
    vertical-align: top;
  }
}
// .center-content {
//   :deep(.el-tag) {
//     vertical-align: unset;
//   }
// }
</style>
