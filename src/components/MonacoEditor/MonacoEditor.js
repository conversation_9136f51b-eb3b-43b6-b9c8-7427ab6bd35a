import assign from 'nano-assign'

export default {
  name: 'MonacoEditorNew',

  props: {
    original: String,
    value: {
      type: String,
      required: true
    },
    theme: {
      type: String,
      default: 'vs'
    },
    language: String,
    options: Object,
    amdRequire: {
      type: Function
    },
    diffEditor: {
      type: Boolean,
      default: false
    },
    autoScroll: {  // 新增属性，控制是否自动滚动
      type: Boolean,
      default: false
    }
  },

  model: {
    event: 'change'
  },
  data() {
    return {
      readOnly: false,
      isFirstLoad: true,
      prevLength: 0  // 添加长度追踪
    }
  },

  watch: {
    options: {
      deep: true,
      handler(options) {
        console.log(111,options)
        if (this.editor) {
          const editor = this.getModifiedEditor()
          editor.updateOptions(options)
        }
        this.readOnly = options.readOnly
      }
    },

    value(newValue) {
      if (this.editor) {
        const editor = this.getModifiedEditor()
        if (newValue !== editor.getValue()) {
          editor.setValue(newValue)
          // 使用 setTimeout 确保在初始化完成后执行
          setTimeout(() => {
            if (this.isFirstLoad) {
              // 首次加载时滚动到第一行
              editor.revealLine(1, this.monaco.editor.ScrollType.Immediate)
              editor.setPosition({ lineNumber: 1, column: 1 })
              editor.layout()
              this.isFirstLoad = false
              this.prevLength = newValue.length
            } else if (this.autoScroll && this.options.readOnly && newValue.length > this.prevLength) {
              // 非首次加载且内容增加时滚动到底部
              this.scrollToBottom()
              this.prevLength = newValue.length
            }
          }, 100)
        }
      }
    },

    original(newValue) {
      if (this.editor && this.diffEditor) {
        const editor = this.getOriginalEditor()
        if (newValue !== editor.getValue()) {
          editor.setValue(newValue)
          // 新增：内容更新后自动滚动到底部
          if (this.autoScroll&& this.options.readOnly) {
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          }
        }
      }
    },

    language(newVal) {
      if (this.editor) {
        const editor = this.getModifiedEditor()
        this.monaco.editor.setModelLanguage(editor.getModel(), newVal)
      }
    },

    theme(newVal) {
      if (this.editor) {
        this.monaco.editor.setTheme(newVal)
      }
    },
  },

  mounted() {
    if (this.amdRequire) {
      this.amdRequire(['vs/editor/editor.main'], () => {
        this.monaco = window.monaco
        this.$nextTick(() => {
          this.initMonaco(window.monaco)
        })
      })
    } else {
      // ESM format so it can't be resolved by commonjs `require` in eslint
      // eslint-disable-next-line import/no-unresolved
      const monaco = require('monaco-editor')
      this.monaco = monaco
      this.$nextTick(() => {
        this.initMonaco(monaco)
      })
    }
  },

  beforeDestroy() {
    this.editor && this.editor.dispose()
  },

  methods: {
    initMonaco(monaco) {
      this.$emit('editorWillMount', this.monaco)
      // 确保基础配置不会被覆盖
      const baseOptions = {
        scrollBeyondLastLine: false,  // 禁止滚动超过最后一行
        minimap: {
          enabled: false  // 禁用小地图
        },
        automaticLayout: true,  // 自动布局
        overviewRulerBorder: false,  // 禁用概览标尺边框
        scrollbar: {
          vertical: 'visible',
          verticalScrollbarSize: 10,
          alwaysConsumeMouseWheel: false  // 防止过度滚动
        }
      }
      
      const options = assign(
        baseOptions,
        {
          value: this.value,
          theme: this.theme,
          language: this.language
        },
        this.options,
        // 确保关键设置不被覆盖
        {
          scrollBeyondLastLine: false
        }
      )

      if (this.diffEditor) {
        this.editor = monaco.editor.createDiffEditor(this.$el, options)
        const originalModel = monaco.editor.createModel(
          this.original,
          this.language
        )
        const modifiedModel = monaco.editor.createModel(
          this.value,
          this.language
        )
        this.editor.setModel({
          original: originalModel,
          modified: modifiedModel
        })
      } else {
        import('monaco-themes/themes/Monokai.json')
          .then(data => {
            monaco.editor.defineTheme('monokai', data)
          })
          import('monaco-themes/themes/Cobalt.json')
            .then(data => {
              monaco.editor.defineTheme('cobalt', data)
            })
          this.editor = monaco.editor.create(this.$el, options)
      }

      // @event `change`
      const editor = this.getModifiedEditor()

      editor.onDidChangeModelContent(event => {
        const value = editor.getValue()
        if (this.value !== value) {
          this.$emit('change', value, event)
          // 新增：内容变化时自动滚动到底部
          if (this.autoScroll && this.options.readOnly) {
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          }
        }
      })
      this.$emit('editorDidMount', this.editor)
    },

    // 新增方法：滚动到底部
    scrollToBottom() {
      if (!this.editor) return
      
      const editor = this.getModifiedEditor()
      const model = editor.getModel()
      
      if (!model) return
      
      // 获取最后一行
      const lineCount = model.getLineCount()
      const lastLineHeight = editor.getOption(monaco.editor.EditorOption.lineHeight)
      const scrollHeight = editor.getScrollHeight()
      
      // 计算精确的滚动位置
      const scrollTop = Math.max(0, scrollHeight - editor.getLayoutInfo().height)
      
      // 设置滚动位置
      editor.setScrollTop(scrollTop)
      
      // 确保最后一行可见
      editor.revealLine(lineCount)
    },

    /** @deprecated */
    getMonaco() {
      return this.editor
    },

    getEditor() {
      return this.editor
    },

    getModifiedEditor() {
      return this.diffEditor ? this.editor.getModifiedEditor() : this.editor
    },

    getOriginalEditor() {
      return this.diffEditor ? this.editor.getOriginalEditor() : this.editor
    },

    focus() {
      this.editor.focus()
    }
  },

  render(h) {
    return h('div')
  }
}