<template>
  <div class="app-header">
    <div class="AppHeaderLeft">
      <div :class="isHideNavigator || globalNavigatorStatus === 0 ? 'collspan-icon collspan-hidden' : 'collspan-icon'">
        <img
          src="./img/menu.png"
          class="menu"
          @click="handleNavigatorToggle"
        >
      </div>
      <a
        v-if="!getLinKongProjectFlag"
        class="LogoImg"
        href="https://www.ennew.com/"
      >
        <SvgIcon
          name="logoEnn"
          class="logo"
        />
        <!-- <img
          src="@/assets/images/logo.svg"
          alt=""
        > -->
      </a>
      <p
        v-if="!getLinKongProjectFlag"
        class="CompanyName"
      >
        <span />
        <span
          style="cursor:pointer"
          @click="goToIndex"
        >能力生产平台</span>
      </p>
      <p
        v-if="getLinKongProjectFlag"
        class="LinkongName"
      >
        数字临空AI中台
      </p>
      <div class="AppHeaderCenter">
        <div>
          <el-popover
            v-model="visible"
            placement="bottom-start"
            width="200"
            trigger="manual"
            :visible-arrow="false"
            popper-class="user-popover"
          >
            <div class="space-container-popver">
              <div class="space-container">
                <!-- <div class="space-item">ddd</div>
                <div class="space-item">ddd</div> -->
                <div
                  v-for="el in filterSpaceList"
                  :key="el.id"
                  :class="['item', currSpace == el.id ? 'space-item space-item-active' : 'space-item']"
                  :title="el.workspaceName"
                  @click="changeSpace(el.id)"
                >
                  {{ el.workspaceName }}
                </div>
                <div
                  v-if="filterSpaceList.length === 0"
                  class="no-data"
                >
                  <img
                    src="@/assets/images/no-space.png"
                    alt=""
                    class="empty-space"
                  >
                  暂无空间
                </div>
              </div>
            </div>
            <div
              slot="reference"
              :class="visible ? 'space-input space-input-visable' : 'space-input'"
              :style="visible ? 'width:'+spaceWidth+'px' : 'width:auto'"
            >
              <div
                v-if="!visible"
                id="handleWidth"
                @click="showPopoverHandle"
              >
                <span style="padding-left: 8px">{{ workSpaceName || '暂无空间' }}</span>
                <i class="el-icon-caret-bottom" />
              </div>
              <el-input
                v-else
                ref="saveTagInput"
                slot="reference"
                v-model="spaceinput"
                clearable
                :placeholder="workSpaceName || '暂无空间'"
                suffix-icon="el-icon-caret-bottom"
                @input="handleFilterSpaceList"
                @blur="visible = false"
              />
            </div>
          </el-popover>
        </div>
      </div>
      <!-- <span
        class="cur-item"
        @click="toMonitor()"
      >分布式训练资源监控</span> -->
      <!-- 扩展菜单 -->
      <!-- <router-link :class="['extend-nav-module', !menuGroups[1].includes($route.path) ? 'active' : '']" :to="{ path: '/dashboard' }">平台</router-link>
      <router-link :class="['extend-nav-module', menuGroups[1].includes($route.path) ? 'active' : '']" :to="{ path: '/dashboard_v2' }">文档中心</router-link> -->
    </div>

    <div
      v-if="hideHederUser"
      class="AppHeaderRight"
    >
      <el-popover
        placement="bottom"
        :width="240"
        :visible-arrow="false"
        trigger="hover"
        popper-class="user-popover"
      >
        <template slot="reference">
          <div class="user-container">
            <svg class="avatar" part="icon" viewBox="0 0 1024 1024" style="color:#c2cadb;width:24px;height:24px;display:block;">
            <!--?lit$9838483895$--><path d="M512 0C229.504 0 0 229.504 0 512s229.504 512 512 512 512-229.504 512-512S794.496 0 512 0zM366.634667 356.565333c4.565333-73.130667 64-131.669333 137.130666-136.234666a146.432 146.432 0 0 1 154.538667 146.304A146.730667 146.730667 0 0 1 512 512.853333c-84.096 0-150.869333-71.296-145.365333-156.330666z m437.930666 423.338667a24.576 24.576 0 0 1-24.661333 24.661333H245.034667a24.576 24.576 0 0 1-24.704-24.661333v-73.173333c0-96.896 194.773333-146.261333 291.669333-146.261334s291.669333 48.426667 291.669333 146.261334l0.896 73.173333z"></path>
          </svg>
            <!-- <SvgIcon
              class="avatar"
              name="avatar"
            /> -->
          </div>
        </template>
        <div>
          <div class="user-container-popver">
            <div class="user-header">
              <div class="user-icon">
                <SvgIcon
                  class="avatar"
                  name="avatar"
                />
              </div>
              <div class="username">
                {{ AdminName }}
              </div>
            </div>
            <div class="user-opt">
              <div
                class="opt-link"
                @click="SwitchTenant"
              >
                <SvgIcon
                  class="opt-icon"
                  name="qiehuanzuhu"
                />
                <span class="opt-text">切换企业</span>
              </div>
              <div
                class="opt-link"
                @click="SignOut"
              >
                <SvgIcon
                  class="opt-icon"
                  name="tuichudenglu"
                />
                <span class="opt-text">退出登录</span>
              </div>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
    <!-- 新增工作空间 -->
    <!-- <addWorkSpaceVue
      ref="addWorkSpaceVueRef"
      @overClick="refresh"
    /> -->
    <!-- 提示信息: 为管理者 且成员为空 -->
    <Tips
      ref="tipsRef"
      @okHandler="okHandler"
    >
      <div slot="content">
        能力生产平台已于2022.1.1开始收费, 您为【{{ workSpaceName }}】的管理者,
        请您进入成员管理维护团队成员
      </div>
    </Tips>
    <!-- 提示信息: 为普通用户 且未被分配任何空间 -->
    <Tips
      ref="tipsRef2"
      @okHandler="okHandler2"
    >
      <div slot="content">
        您尚未加入团队，请联系团队管理者加入团队后使用
      </div>
    </Tips>
  </div>
</template>
<script type="text/javascript">
// import addWorkSpaceVue from '@/views/workSpace/modules/addWorkSpace.vue'
import Tips from '@/components/Tools/Tips.vue'
import { mapActions, mapGetters } from 'vuex'
import { MENU_GROUPS } from '@/utils/constant'
import Bus from '../bus.js'
// 获取URL参数  //shi坑
function queryKeyVal (name) {
  let reg = new RegExp('(^|&|.?)' + name + '=([^&|#]*)(&|#|$)', 'i')
  let r = window.location.href.match(reg) // 获取url中"?"符后的字符串并正则匹配
  let context = ''
  if (r != null) context = r[2]
  reg = null
  r = null
  return context == null || context == '' || context == 'undefined'
    ? ''
    : context
}
export default {
  components: {
    // addWorkSpaceVue,
    Tips
  },
  data () {
    return {
      menuGroups: MENU_GROUPS,
      visible: false,
      AdminName: '',
      spaceList: [],
      filterSpaceList: [],
      currSpace: '', // 当前选中空间
      showAddModal: false, // 显隐信泽工作空间弹窗
      isSuperAdmin: false, // 是否超管
      currSpaceName: '',
      themeColor: localStorage.getItem('themeColor') || '#264480',
      spaceWidth: 100,
      spaceinput: ''
    }
  },
  computed: {
    ...mapGetters({
      globalNavigatorStatus: 'common/globalNavigatorStatusGetter',
      getLinKongProjectFlag: 'common/getLinKongProjectFlag'
    }),
    hideHederUser () {
      if (this.$route && this.$route.meta && this.$route.meta.hideHederUser) {
        return false
      } else {
        return true
      }
    },
    isHideNavigator () {
      return !!(this.$route.meta && this.$route.meta.hideNavigator)
    },
    // 匹配空间名称
    workSpaceName () {
      return (
        (
          this.spaceList.find(
            (item) => item.id * 1 === this.$store.state.workSpace.currentWorkSpace.id * 1
          ) || {}
        ).workspaceName || '空间名称未匹配'
      )
    }
  },
  watch: {
    hideHederUser: {
      //   immediate:true,
      handler (nv, ov) {
        if (!ov && nv) {
          this.$post(this.baseUrl + '/user/getUserNickName').then((data) => {
            sessionStorage.setItem('LOGIN_Name', data.userNickName)
            sessionStorage.setItem('openId', data.userId) // 提供给监控js使用
            this.AdminName = sessionStorage.getItem('LOGIN_Name')
          })
        }
      }
    },
    '$route.query': {
      deep: true,
      immediate: true,
      handler (val) {
        // this.initInfo()
      }
    }
  },
  async created () {
    this.initInfo()
  },
  mounted () {
    if (this.hideHederUser) {
      this.AdminName = sessionStorage.getItem('LOGIN_Name')
    }
  },
  methods: {
    handleFilterSpaceList (val) {
      console.log(val)
      if (val !== '') {
        const filterList = this.spaceList.filter((item) => item.workspaceName.indexOf(val) > -1)
        console.log(filterList)
        this.filterSpaceList = filterList
      } else {
        this.filterSpaceList = this.spaceList
      }
    },
    async initInfo () {
      // if (this.hideHederUser) {
      //   this.$post(this.baseUrl + '/user/getUserNickName').then((data) => {
      //     sessionStorage.setItem('userId', data.userId)
      //     sessionStorage.setItem('LOGIN_Name', data.userNickName)
      //     sessionStorage.setItem('openId', data.userId) // 提供给监控js使用
      //     this.AdminName = sessionStorage.getItem('LOGIN_Name')
      //     // 用户id
      //     // let queryItcode = this.$route.query.userId //shi坑
      //     // let queryItcode = queryKeyVal('userId')
      //     const queryItcode = data.userName
      //     const storageItcode = sessionStorage.getItem('loadItcode')
      //     let loadItcode = null
      //     if (queryItcode && queryItcode != storageItcode) {
      //       // URL带了参数缓存里没有或者不同
      //       loadItcode = queryItcode
      //       sessionStorage.setItem('loadItcode', queryItcode)
      //     } else {
      //       loadItcode = storageItcode
      //     }
      //     // 租户id
      //     // let queryTenantId = this.$route.query.authTenantId //shi坑
      //     // let queryTenantId = queryKeyVal('authTenantId')
      //     const queryTenantId = data.tenantId
      //     const storageTenantId = sessionStorage.getItem('authTenantId')
      //     let loadTenantId = null
      //     if (queryTenantId && queryTenantId != storageTenantId) {
      //       // URL带了参数缓存里没有或者不同
      //       loadTenantId = queryTenantId
      //       sessionStorage.setItem('authTenantId', queryTenantId)
      //     } else {
      //       loadTenantId = storageTenantId
      //     }
      //     // 有账号可初始化
      //     if (loadItcode) {
      //       zhuge?.load({
      //         // 配置应用的AppKey
      //         superProperty: {
      //           // 全局的事件属性
      //           当前页面名称: document.title,
      //           来源页面名称: document.referrer,
      //           产品名称: '能力生产平台',
      //           'PC-H5': 'PC',
      //           itcode: loadItcode.toLowerCase(),
      //           租户: `${loadTenantId}`
      //         },
      //         duration: true, // 「页面停留时长」访问采集
      //         // 测试，开发环境开启，正式环境需关闭
      //         debug: !(
      //           window.location.href.indexOf('algorithm.ennew.com') !== -1
      //         ),
      //         autoTrack: true, // 启用全埋点采集
      //         singlePage: true // 是否是单页面应用（SPA），启用autoTrack后生效
      //       })
      //       zhuge.identify(loadItcode.toLowerCase())
      //       zhuge.track('打开产品', {
      //         一级分类名称: 'PAAS',
      //         产品名称: '能力生产平台'
      //       })
      //     }
      //     // URL带了参数缓存里没有或者不同
      //     const quryLoginCode = queryKeyVal('loginCode')
      //     if (
      //       quryLoginCode &&
      //       quryLoginCode != sessionStorage.getItem('loginCode')
      //     ) {
      //       sessionStorage.setItem('loginCode', quryLoginCode)
      //       zhuge.track('能力生产平台-登录成功', {
      //         域账号id: loadItcode.toLowerCase()
      //       })
      //     }
      //   })
      // }
      await this.isSuperAdminForCurUser()
      await this.getSpaceList()
    },
    toMonitor () {
      this.$router.push('/resource/monitor')
    },
    SignOut () {
      localStorage.removeItem('currentWorkSpace')
      sessionStorage.clear()
      this.authSdk.logout(window.location.origin + '/#/homePage')
    },
    SwitchTenant () {
      localStorage.removeItem('currentWorkSpace')
      sessionStorage.clear()
      this.authSdk.selectTenant(window.location.origin + '/#/homePage')
    },
    // 显示popover
    showPopoverHandle () {
      this.visible = !this.visible
      console.log('计算输入框宽度', document.getElementById('handleWidth').getBoundingClientRect())
      this.spaceWidth = document.getElementById('handleWidth').getBoundingClientRect().width + 20 || 100
      this.$nextTick(() => {
        this.$refs.saveTagInput.focus();
      })
    },
    // hidePopover 关闭回调
    hidePopover () {
      this.visible = false
    },
    // 判断当前用户是否是超级管理员
    async isSuperAdminForCurUser () {
      const vm = this
      const res = await vm.$axios.post(
        `${vm.baseUrl}/user/isSuperAdminForCurUser`
      )
      if (res && res.data && res.data.status === 200) {
        vm.isSuperAdmin = res.data.data
      }
    },
    // 获取所属空间列表 默认设置第一个(没有切换过情况) 判断空间逻辑展示Tips
    async getSpaceList () {
      const vm = this
      const res = await vm.$axios.post(
        `${vm.baseUrl}/workspace/getWorkspacesByUserInfo`
      )
      if (res && res.data && res.data.status === 200) {
        vm.spaceList = res.data.data
        vm.filterSpaceList = res.data.data
      }
      if (vm.spaceList.length > 0) {
        if (localStorage.getItem('currentWorkSpace')) {
          // 本地存在 且 存在列表中
          vm.currSpace = JSON.parse(
            localStorage.getItem('currentWorkSpace')
          ).id
          vm.currSpaceName = JSON.parse(
            localStorage.getItem('currentWorkSpace')
          ).workspaceName
          const roleType = JSON.parse(
            localStorage.getItem('currentWorkSpace')
          ).roleType
          if (
            this.spaceList.some(
              (el) => el.id === vm.currSpace && el.roleType === roleType
            )
          ) {
            vm.$store.commit(
              'workSpace/setCurrentWorkSpace',
              JSON.parse(localStorage.getItem('currentWorkSpace'))
            )
          } else {
            vm.currSpace = vm.spaceList[0].id
            vm.currSpaceName = vm.spaceList[0].workspaceName
            vm.$store.commit('workSpace/setCurrentWorkSpace', vm.spaceList[0])
            localStorage.setItem(
              'currentWorkSpace',
              JSON.stringify(vm.spaceList[0])
            )
          }
          // 显示tips
          vm.isShowTips()
        } else if (!vm.$store.state.workSpace.currentWorkSpace.id) {
          // 本地和store都未设置过
          vm.currSpace = vm.spaceList[0].id
          vm.currSpaceName = vm.spaceList[0].workspaceName
          vm.$store.commit('workSpace/setCurrentWorkSpace', vm.spaceList[0])
          localStorage.setItem(
            'currentWorkSpace',
            JSON.stringify(vm.spaceList[0])
          )
          // 显示tips
          vm.isShowTips()
        }
        vm.$axios.defaults.headers.workspaceId = vm.currSpace
      } else {
        localStorage.removeItem('currentWorkSpace')
        const showedTips = localStorage.getItem('showedTips') || ''
        const userId = sessionStorage.getItem('userId') || ''
        // 如果是普通用户 && 没有所属空间 && localstorage没有设置过showedTips：base-NoSpace
        // const isUser =
        //   JSON.parse(localStorage.getItem('currentWorkSpace')).roleType == 'GENERAL_USER'
        if (!vm.isSuperAdmin) {
          vm.$nextTick(() => {
            // vm.$refs.tipsRef2.visible = true
            this.$router.push('/workSpace/apply')
          })
          localStorage.setItem(
            'showedTips',
            `${showedTips ? ',' : ''}${userId}-base-NoSpace`
          )
        }
      }
    },
    refresh () {
      this.getSpaceList()
    },
    // 判断提示框是否打开
    isShowTips () {
      const vm = this
      const id = JSON.parse(localStorage.getItem('currentWorkSpace')).id
      const hasM = JSON.parse(localStorage.getItem('currentWorkSpace')).flag
      const isAdmin =
        JSON.parse(localStorage.getItem('currentWorkSpace')).roleType ==
        'WORKSPACE_ADMIN'
      // 如果是管理员 && 当前空间没有成员 && localstorage没有设置过showedTips: admin+空间ID+NoMember
      const showedTips = localStorage.getItem('showedTips') || ''
      const currentTips = `admin-${id}-NoMember`
      if (isAdmin && !hasM && showedTips.indexOf(currentTips) === -1) {
        vm.$nextTick(() => {
          vm.$refs.tipsRef.visible = true
        })
        localStorage.setItem(
          'showedTips',
          `${showedTips}${showedTips ? ',' + currentTips : currentTips}`
        )
      }
    },
    // 切换空间
    changeSpace (val) {

      const vm = this
      let data = vm.spaceList.find(
        (item) => item.id === val
      )
      console.log(val,data,'val')
      if (data.workspaceName && (data.workspaceId || data.id)) {
        data =Object.assign({workspaceId:data.id},data)
        localStorage.setItem('currentWorkSpace', JSON.stringify(data));
        this.$store.commit('workSpace/setCurrentWorkSpace', data);
        const isLinKongProjectFlag = data.workspaceName === '临空智城';
        this.$store.commit('common/setLinKongProjectFlag', isLinKongProjectFlag);
        // TODO   新增一个判断，第一次进入的话需要先进入添加界面，需要一个接口来提供标识
        this.$router.push({
          path: '/planGenerate/first',
          query: {
            ...this.$route.query,
            workspaceId: data.workspaceId || data.id,
            workspaceName: data.workspaceName,
            menuType: 2
          }
        })
        setTimeout(() => {
          const navigation = document
            .querySelector('ennew-navigation')
            .shadowRoot.querySelector('.ennew-navigation');
          navigation.querySelector('.--enn-nav-LOGO').style.display = isLinKongProjectFlag
            ? 'none'
            : 'block';
          navigation.querySelector('.--enn-nav-project-name').textContent = isLinKongProjectFlag
            ? '数字临空AI中台'
            : '能力生产平台';
        }, 1);
        this.$ennewsdk?.setNavMenuActiveKey('');
      }
    },
    // 新增空间
    addSpace () {
      this.visible = false
      this.$refs.addWorkSpaceVueRef.handleOpen()
    },
    goTo () {
      this.visible = false
      const url =
        window.location.protocol +
        '//' +
        window.location.hostname +
        ':' +
        window.location.port +
        '/#/workSpace/apply'
      window.location.href = url
    },
    goToIndex () {
      this.visible = false
      const url =
        window.location.protocol +
        '//' +
        window.location.hostname +
        ':' +
        window.location.port +
        '/#/'
      window.location.href = url
    },
    // 提示回调事件
    okHandler () {
      this.$route.path !== '/workSpace/space' &&
        this.$router.push('/workSpace/space')
    },
    // 提示回调返回总览页面
    okHandler2 () {
      this.$router.push('/workSpace/apply')
    },
    // 工单系统接入
    feedback () {
      window.open(
        'https://feedback-workorder-customer-web.ennew.com?channel=cyz&secretKey=K000000036064tfsidb&userId=' +
          sessionStorage.userId
      )
    },
    ...mapActions({
      updateGlobalNavigatorStatus: 'common/updateGlobalNavigatorStatusAction',
      getLinKongProject: 'common/getLinKongProject'
    }),
    // 切换导航菜单显隐状态
    handleNavigatorToggle () {
      this.updateGlobalNavigatorStatus(this.globalNavigatorStatus ^ 1)
    }
  }
}
</script>
<style type="text/css" scoped>
:deep(.el-popper) {
  margin-top: 6px !important;
}
.space-input {
  background: transparent;
  color: #C2CADB;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  margin-top: 2px;
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  padding-left: 8px;
}
.space-input i {
  padding: 0px 6px;
}
.space-input:hover{
  background: #1C3362;
}
.space-container {
  max-height: 240px;
  overflow: auto;
}
.space-container .space-item {
  color: #323233;
  line-height: 20px;
  padding: 8px;
  max-width: 190px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.space-container .space-item:hover{
  background-color: #F6F7FB;
  border-radius: 4px;
}
.space-container .space-item-active {
  background-color: #EFF3FF;
  border-radius: 4px;
}
.space-container .space-item-active:hover {
  background-color: #E5E9F5 !important;
  border-radius: 4px;
}
.space-input-visable {
  background: #1C3362;
}
:deep(.space-input-visable .el-input__inner){
  border-radius: 2px;
  height: 36px;
  line-height: 36px;
  padding: 0px;
  border: none;
  color: #C2CADB;
  margin-top: -2px;
  width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  background: #1C3362;
  padding-left: 8px;
}
.collspan-icon {
  padding: 6px;
  border-radius: 2px;
  margin-right: 8px;
  background: #192E57;
}
.collspan-hidden {
  background: transparent;
}
.collspan-hidden:hover{
  background: #192E57;
}
.operaor-icon {
  border-radius: 2px;
  margin-right: 8px;
}
.operaor-icon:hover{
  background: #1C3362;
}
.operaor-icon:active{
  background: #192E57;
}
.extend-nav-module {
  margin: 0 8px;
  color: #c2cadb;
}

.extend-nav-module.active {
  color: rgb(255, 208, 75);
}

.app-header {
  /* position: fixed; */
  width: 100%;
  height: 48px;
  background: var(--header-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 4;
}
.AppHeaderLeft {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
.LogoImg {
  /*width: 100px;*/
  /*height: 30px;*/
  cursor: pointer;
}
.LogoImg .logo {
  width: 66px;
  height: 24px;
}
.menu {
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.CompanyName {
  color: #ffffff;
  font-size: 15px;
  display: flex;
  align-items: center;
}
.CompanyName span:nth-child(1) {
  margin: 0 16px;
  margin-top: 2px;
  width: 1px;
  height: 16px;
  opacity: 0.24;
  background-color: #fff;
}
.CompanyName span:last-child {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}
.LinkongName {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}

.CompanyName .ws {
  color: #ffffff;
  margin-left: 10px;
  background-color: #1c3362;
  border-radius: 2px;
  padding: 0 10px;
  height: 36px;
}
.AppHeaderCenter {
  height: 40px;
}
.AppHeaderRight {
  margin-right: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.cur-item {
  color: #c2cadb;
  margin: 0 12px 0 12px;
  font-size: 14px;
  font-family: SYZT, SimHei, SimSun;
  font-weight: 400;
  cursor: pointer;
}

.cur-space {
  color: #c2cadb;
  margin: 0 2px 0 6px;
  font-size: 14px;
  font-family: SYZT, SimHei, SimSun;
  font-weight: 400;
}
.doc-btn {
  color: #c2cadb;
  width: 36px;
  height: 36px;
  display: inline-block;
  line-height: 38px;
  text-align: center;
  border-radius: 2px;
}
.doc-btn:hover{
  background-color: #1C3362;
}
.doc-btn:active{
  background-color: #192E57;
}
.doc-btn i {
  font-size: 18px;
}

.SignOutHover >>> .el-dropdown-link {
  color: #ffffff;
}
.SignOutPart {
  color: #ffffff;
  display: flex;
  align-items: center;
  outline: 0 !important;
}
.SignOutPart span {
  margin: 0 2px 0 6px;
  font-size: 14px;
  font-family: SYZT, SimHei, SimSun;
  font-weight: 400;
}
.space-btn {
  height: 100%;
  display: flex;
  align-items: center;
  outline: 0 !important;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 2px;
  color: #c2cadb;
  line-height: 18px;
}
.space-btn-active {
  background-color: #1c3362;
  position: relative;
}
.space-btn-active::after{
  content: '';
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #EFF3FF;
  border-radius: 0px 0px 2px 2px;
}
.space-btn:active {
  background-color: #192E57;
}
.space-btn:hover {
  background-color: #1C3362;
}
.space-btn .el-icon-s-grid {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
}
.user-container {
  display: flex;
  align-items: center;
  height: 100%;
}
.user-container{
  width: 36px;
  height: 36px;
  border-radius: 2px;
  justify-content: center;
}
.user-container:hover {
  background: #192e57;
}
.user-container .avatar {

  vertical-align: middle;
    fill: currentcolor;
    overflow: hidden;
    display: block;
    color:#c2cadb;
    
}
.user-container .username {
  color: #fff;
  font-size: 14px;
  line-height: 48px;
  margin: 0px 16px 0px 4px;
}
.user-popover {
  min-width: 264px !important;
}
.space-container-popver {
  background: #fff;
  font-size: 14px;
  line-height: 20px;
  color: #969799;
  padding: 4px;
  text-align: left;
  border-radius: 4px;
}
.user-container-popver {
  background: #fff;
  font-size: 14px;
  line-height: 20px;
  color: #969799;
  padding: 0px;
  text-align: left;
  border-radius: 4px;
}
.user-container-popver .user-header {
  padding: 0px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #ebedf0;
}
.user-container-popver .user-header .user-icon {
  display: flex;
  align-items: center;
}
.user-container-popver .user-header .user-icon .avatar {
  width: 56px;
  height: 56px;
}
.user-container-popver .user-header .username {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  line-height: 24px;
}
.user-container-popver .user-header .department {
  font-size: 12px;
  font-weight: 400;
  color: #969799;
  line-height: 20px;
  margin-top: 4px;
}
.user-container-popver .user-opt {
  padding: 4px;
}
.user-container-popver .opt-link {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  padding: 9px 8px;
  color: #646566;
  font-size: 14px;
  line-height: 22px;
  text-align: left;
  border-bottom: 0px solid #EBECF0;
}
.user-container-popver .opt-link:last-child{
  border-bottom: none;
}
.user-container-popver .opt-link:hover {
  background: #f6f7fb;
}
.user-container-popver .opt-text {
  margin-left: 8px;
}
.user-container-popver .exit-icon {
  width: 14px;
  height: 14px;
}
</style>
<style lang="sass">
.user-popover
  padding: 0px !important
  margin-top: 6px !important
.cus-tooltip
  border-radius: 2px
  padding: 0 !important
  .cus-con
    &-head
      display: flex
      justify-content: space-between
      height: 48px
      line-height: 48px
      border-bottom: solid 1px #e5e5e5
      padding: 0 16px
      &-tit
        color: #333333
    &-main
      max-height: 200px
      overflow-y: auto
      border-bottom: solid 1px #e5e5e5
      .item
        padding: 0 26px
        line-height: 36px
        cursor: pointer
        overflow: hidden
        text-overflow: ellipsis
        white-space: nowrap
        &:hover
          background-color: #f7f6f9
        &.active
          background-color: #f7f6f9
          color: #264480
    &-foot
      padding: 0 26px
      line-height: 36px
      display: flex
      justify-content: space-between
      align-items: center
      &.right
        justify-content: flex-end
      &-add
        cursor: pointer
        color: #0f55fa
.no-data
  padding: 20px
  text-align: center
  .empty-space
    width: 80px
    display: block
    margin: 0 auto
</style>
