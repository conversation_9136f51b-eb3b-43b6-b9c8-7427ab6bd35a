<template>
  <div class="app-menu-inner">
    <div
      v-show="
        !isWhitePath &&
        $route.query.workspaceId &&
        !isBigModelPath &&
        !isEdgePath &&
        !isCapacityDesignPath &&
        zhuanjiaFlag
      "
      style="position: fixed;"
    >
      <el-menu 
        style="height: 100vh" 
        v-if="$route.path !== '/abilityCenter/targetList/detailFull' || this.$route.path !== '/abilityCenter/targetList/approvecode'" 
        :default-active="outerMenuPath" 
        class="app-menu-wrapper" 
        :collapse="globalNavigatorStatusGetter"
        @mouseenter.native="updateGlobalNavigatorStatus(0)"
        @mouseleave.native="updateGlobalNavigatorStatus(1)"
      >
        <!-- <el-menu-item index="planGenerateRunTask" @click="RouteJust('/planGenerate/runTask?workspaceId=1&workspaceName=专用空间&FullScreen=false&id=6894&fromMenu=1&status=0', '', '')">
          <SvgIcon name="ganzhi" class="menu-icon" />
          <span slot="title">任务列表</span>
        </el-menu-item>         -->
        <el-menu-item index="planGenerateGuidance" @click="RouteJust('/planGenerate/guidance', '', '')">
          <SvgIcon name="test" class="menu-icon"></SvgIcon>
          <span slot="title">生产指引</span>
        </el-menu-item>       
        <el-menu-item index="SampleAbility" @click="RouteJust('/planGenerate/sampleAbility', '', '')">
          <SvgIcon name="yangban" class="menu-icon" />
          <span slot="title">能力样版</span>
        </el-menu-item>
        <el-menu-item index="planGenerateFirst" @click="RouteJust('/planGenerate/first', '', '')">
          <SvgIcon name="ganzhi" class="menu-icon" />
          <span slot="title">专家生产</span>
        </el-menu-item>
        <el-menu-item index="planGenerateTaskIndex" @click="RouteJust('/planGenerate/taskRd', '', '')">
          <SvgIcon name="shuju" class="menu-icon" />
          <span slot="title">任务规划</span>
        </el-menu-item>
        <el-menu-item  index="planGenerateIndex" @click="RouteJust('/planGenerate/index', '', '')">
          <SvgIcon name="zhuanjiaMenu" class="menu-icon" />
          <span slot="title">研发生产</span>
        </el-menu-item>
        <el-menu-item index="planGenerateValidation" @click="RouteJust('/planGenerate/validation', '', '')">
          <SvgIcon name="toolMenu" class="menu-icon" />
          <span slot="title">训练与验证</span>
        </el-menu-item>
        <el-menu-item index="targetList" @click="RouteJust('/abilityCenter/targetList', '', '')">
          <SvgIcon name="marketMenu" class="menu-icon" />
          <span slot="title">能力仓库</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapState } from 'vuex'

// 需要【成员管理】菜单权限的路由
const paths = [
  '/notebookList',
  '/dashboard',
  '/blockly-project/Algorithm',
  '/workSpace/space',
  '/workSpace/member',
  '/zhuanjia/workSpace/member',
  '/workSpace/bill',
  '/OperationAudit',
  '/workSpace/businessBoard',
  '/AccessControl',
  '/ocrTemplate'
];
// 建议子路由采用以一级路有为根后进行扩展的命名方式
export default {
  data() {
    return {
      isSuperAdmin: false,
      edgeDeviceEnabled: false,
      edgeAppEnabled: false,
      edgeHistoryEnabled: false,
      projectInfo: {},
      showDataMenu: false,
      isAdminNew: false,
      isCollapse: true,
      fromMenu: this.$route.query.fromMenu,
      isPlanGen: false,
      isAgentPower: false,
      testName: ''
    };
  },
  computed: {
    ...mapGetters({
      getLinKongProjectFlag: 'common/getLinKongProjectFlag',
      globalNavigatorStatusGetter: 'common/globalNavigatorStatusGetter',
      getIsNew: 'workSpace/getIsNew'
    }),
    zhuanjiaFlag() {
      if (this.$route.meta.zhunjiaFlag) {
        return true;
      } else {
        return false;
      }
    },
    isLinkong() {
      return this.$route.query.workspaceName == '临空智城';
    },
    platformOuter() {
      if (this.$route.meta.platformFlag) {
        return true;
      } else {
        return false;
      }
    },
    checkIsOuter() {
      if (!this.$route.query.workspaceId || this.$route.meta.platformFlag) {
        return true;
      } else {
        return false;
      }
    },
    isWhitePath() {
      return (
        this.$route.query.algorithmProjectId &&
        this.$route.query.type &&
        this.$route.query.projectName &&
        !(this.$route.query.bigModalFlag == 'true' || this.$route.query.bigModalFlag == true)
      );
    },
    isBigModelPath() {
      // console.log(
      //   'this.$route.query.bigModalFlag',
      //   this.$route.query.bigModalFlag,
      //   this.$route.query.bigModalFlag == true
      // );
      return this.$route.query.bigModalFlag == 'true' || this.$route.query.bigModalFlag == true;
    },
    isEdgePath() {
      // 边端应用
      if (this.$route.path.includes('/edgeInfo/')) {
        return true;
      }
      return false;
    },
    isCapacityDesignPath() {
      // 能力管理
      if (this.$route.path.includes('/ability/')) {
        return true;
      }
      return false;
    },
    outerMenuPath() {
      let name = this.$route.name;
      console.log('this.$route.path', this.$route.path, name,'menu------------');
      // 场景配置页，返回智能能力研发按钮使用
      if (
        this.$route.path === '/planGenerate/wllsDevPlanchat' ||
        this.$route.path === '/planGenerate/wllsExpertPlanchat'
      ) {
        sessionStorage.setItem('wllsChatPath', this.$route.path);
      }
      if (
        this.$route.path === '/workSpace/apply/history' ||
        this.$route.path == '/workSpace/apply'
      ) {
        name = 'workSpaceIndex';
      } else if (
        [
          '/MirrorListOuter',
          '/MirrorCustom',
          '/CreateMirror',
          '/MirrorBuildRecords',
          '/MirrorDetailOuter'
        ].indexOf(this.$route.path) > -1
      ) {
        name = 'MirrorListOuter';
      } else if (
        this.$route.path === '/blockly-project/Algorithm' ||
        this.$route.path === '/blocklyAddBasic'
      ) {
        name = 'projectPython';
      } else if (
        this.$route.path === '/ocrTemplate' ||
        this.$route.path === '/createOcrProject' ||
        this.$route.path === '/ocrModelProject' ||
        this.$route.path === '/ocrTemplateManage' ||
        this.$route.path === '/createOcrClass' ||
        this.$route.path === '/ocrStatistics'
      ) {
        name = 'ocrTemplate';
      } else if (
        this.$route.path === '/ServiceManagement' ||
        this.$route.path === '/CreateApplication' ||
        this.$route.path == '/AdminModify'
      ) {
        name = 'ControlAccess';
      } else if (this.$route.path.indexOf('/service-detail') > -1) {
        name = 'service-list';
      }
      if (this.$route.path === '/index') {
        name = 'LinkongIndex';
      }
      if (['/notebookList', '/createNotebook', '/notebookDetail'].includes(this.$route.path)) {
        name = 'notebookList';
      }
      if (this.$route.path.indexOf('/datasetLast/nlp') > -1) {
        name = 'datasetLastNlp';
      }
      if (this.$route.path.indexOf('/datasetLast/cv') > -1) {
        name = 'datasetLastCv';
      }
      if (['/ocrTemplate'].includes(this.$route.path)) {
        name = 'ocrTemplate';
      }
      if (this.$route.path.includes('/mechanism')) {
        name = name;
      }

      if (this.$route.path.indexOf('/abilityCenter/targetList/detail') > -1) {
        name = 'targetList';
      }
      if (this.$route.path.indexOf('/planGenerate/DuiqiModel') > -1) {
        name = 'DuiqiModel';
      }
      if (this.$route.path.indexOf('/planGenerate/apiDocumentDetails') > -1) {
        name = 'abilityComponent';
      }
      if (this.$route.path.indexOf('/planGenerate/funcList') > -1) {
        name = 'DuiqiFunc';
      }
      if (this.$route.path.indexOf('/dashboard') > -1) {
        name = 'dashboard';
      }
      if (this.$route.path.indexOf('/alignmentRules/index') > -1) {
        name = 'RuleMarket';
      }
      if (this.$route.path.indexOf('/alignmentRules/detail') > -1) {
        name = 'RuleMarket';
      }
      if (this.$route.path.indexOf('/abilityCenter/targetList/versionDetail') > -1) {
        name = 'targetList';
      }
      if (this.$route.path.indexOf('/abilityCenter/test') > -1) {
        name = 'targetList';
      }
      if (this.$route.path.indexOf('/abilityCenter/talk') > -1) {
        name = 'targetList';
      }
      console.log('this.$route.path', this.$route.path, name);
      if (this.$route.path.indexOf('/planGenerate/sampleEdit') > -1) {
        name = 'SampleAbility';
      }
      if (
        this.$route.path.indexOf('/planGenerate') > -1 ||
        this.$route.path.indexOf('/targetList') > -1
      ) {
        this.isAgentPowerForWorkspace();
      }
      if(this.$route.path.indexOf('/planGenerate/taskRd') > -1){
        name = 'planGenerateTaskIndex'
      }
      // 解决刷新页面路由对应不上bug
      // 1 专家生产 2任务规划 3研发生产 4训练与验证
      if(this.$route.path.indexOf('/planGenerate/ConfTaskPlanchat') > -1 ||
         this.$route.path.indexOf('/planGenerate/planchat') > -1 ||
         this.$route.path.indexOf('/planGenerate/partnerchat') > -1 ||
         this.$route.path.indexOf('/planGenerate/sceneConfig') > -1 ||
         this.$route.path.indexOf('/planGenerate/task') > -1 || 
         this.$route.path.indexOf('/planGenerate/edit') > -1 ||
         this.$route.path.indexOf('/planGenerate/runTask') > -1 
        ){
        if (this.$route.query.fromMenu === "3") { 
          name = 'planGenerateIndex';
        }
        if (this.$route.query.fromMenu === "2") {
          name = 'planGenerateTaskIndex';
        }
        if (this.$route.query.fromMenu === "1") {
          name = 'planGenerateFirst';
        }
        if (this.$route.query.fromMenu === "4") {
          name = 'planGenerateValidation';
        }
        if (this.$route.query.fromMenu === "5") { // 进入能力样版
          name = 'SampleAbility';
        }
      }
      console.log(name);
      this.testName = name;
      return name;
    },
    innerMenuPath() {
      let path = this.$route.meta.GuidePath ? this.$route.meta.JumpPath : this.$route.path;
      if (this.$route.path === '/workflow') {
        path = '/workflowIndex';
      }

      if (this.$route.path === '/service-list') {
        path = '/service-list';
      }
      if (
        path.indexOf('/notebookList') > -1 ||
        path === '/createNotebook' ||
        [
          '/project-python',
          '/project-python-model',
          '/MirrorList',
          '/MirrorBuildRecords',
          '/CreateMirror',
          '/BuildDetail',
          '/MirrorCustom'
        ].includes(this.$route.path)
      ) {
        path = '/normalModelProject/information';
      }
      if (this.$route.path === '/public-history') {
        path = '/public-history';
      }
      if (this.$route.path === '/create-or-modify-task-group') {
        if (this.$route.query.taskType * 1 === 1) {
          path = '/task-group-list-forecast';
        } else {
          path = '/sub-task-list';
        }
      }
      if (this.$route.path === '/sub-task-list') {
        if (this.$route.query.taskType * 1 === 1) {
          path = '/task-group-list-forecast';
        } else {
          path = '/sub-task-list';
        }
      }
      if (
        this.$route.path === '/create-or-modify-sub-task' ||
        this.$route.path === '/sub-task-detail'
      ) {
        if (this.$route.query.taskType * 1 === 1) {
          path = '/task-group-list-forecast';
        } else {
          path = '/sub-task-list';
        }
      }
      if (
        this.$route.path.indexOf('/datasetLast') > -1 ||
        this.$route.path.indexOf('textDatasetManage') > -1 ||
        this.$route.path.indexOf('imgDatasetManage') > -1 ||
        path.indexOf('textDatasetManage') > -1 ||
        path.indexOf('imgDatasetManage') > -1
      ) {
        path = '/datasetLast/' + this.$route.params.datasetType;
      }
      if (
        this.$route.path.indexOf('textDatasetManage') > -1 ||
        path.indexOf('textDatasetManage') > -1 ||
        path.indexOf('isDatasetLast') > -1
      ) {
        path = '/datasetLast/nlp';
      }
      if (
        this.$route.path.indexOf('imgDatasetManage') > -1 ||
        path.indexOf('imgDatasetManage') > -1
      ) {
        path = '/datasetLast/cv';
      }
      if (this.$route.path.indexOf('/datasetLast') > -1) {
        path = 'datasetLast';
      }
      if (['/batch-inference', '/batch-inference-details'].includes(this.$route.path)) {
        path = '/batch-inference';
      }
      return path;
    },
    innerBigMenuPath() {
      let path = this.$route.meta.GuidePath ? this.$route.meta.JumpPath : this.$route.path;
      if (this.$route.path === '/service-list' || this.$route.path === '/service-add-basic') {
        path = '/service-list';
      }
      if (
        this.$route.path == '/MirrorList' ||
        [
          '/MirrorList',
          '/MirrorCustom',
          '/CreateMirror',
          '/MirrorBuildRecords',
          '/MirrorDetail'
        ].indexOf(this.$route.path) > -1 ||
        this.$route.path.indexOf('/clusterTrain') > -1 ||
        this.$route.path === '/project-python' ||
        this.$route.path === '/project-python-model' ||
        this.$route.path === '/project-python-model' ||
        this.$route.path === '/BuildDetail' ||
        this.$route.path === '/pipelineConfig'
      ) {
        path = '/modelProject/information';
      }
      if (path === '/textDatasetManage' || path === '/imgDatasetManage') {
        path = 'shujuguanli';
      }
      if (this.$route.path === '/public-history') {
        path = '/public-history';
      }
      if (
        this.$route.path === '/create-or-modify-task-group' ||
        this.$route.path === '/create-or-modify-sub-task' ||
        this.$route.path === '/sub-task-detail'
      ) {
        path = '/sub-task-list';
      }
      if (this.$route.path === '/sub-task-list') {
        if (this.$route.query.taskType * 1 === 1) {
          path = '/task-group-list-forecast';
        } else {
          path = '/sub-task-list';
        }
      }
      if (this.$route.path.indexOf('/datasetLast') > -1) {
        path = 'datasetLast';
      }
      if (['/batch-inference', '/batch-inference-details'].includes(this.$route.path)) {
        path = '/batch-inference';
      }
      return path;
    },
    innerEdgeMenuPath() {
      // let name = this.$route.name;
      // console.log(1111, 'this.$route.path', this.$route.path, name)
      return this.$route.name;
    },
    routerpath() {
      return this.$route.meta.GuidePath ? this.$route.meta.JumpPath : this.$route.path;
    },
    isCommonProject() {
      const list = [1, 2, 3];
      return list.includes(Number(this.$route.query.type));
    },
    isCloud() {
      return Number(this.$route.query.projectType) === 1;
    },
    isEdge() {
      return Number(this.$route.query.projectType) === 2;
    },
    isEdgeAndCloud() {
      return Number(this.$route.query.projectType) === 3 || !this.$route.query.projectType;
    },
    // isAdminNew() {
    //   return this.$store.state.common.isAdminNew;
    // }
    isChangePath() {
      const path = this.$route.path;
      const workspaceId = this.$route.query?.workspaceId;
      return { path, workspaceId };
    }
  },
  watch: {
    '$route.query.projectId'(projectId) {
      if (!projectId) {
        const projectId = this.$route.query.algorithmProjectId;
        if (projectId) {
          this.$store.dispatch('fetchProjectInfo', projectId);
        }
      } else {
        this.$store.dispatch('fetchProjectInfo', projectId);
      }
    },
    '$store.state.project.projectInfo.type'(type) {
      this.showDataMenu = [4, 5].includes(type);
    },
    isChangePath: {
      immediate: true,
      handler(val) {
        if (val.workspaceId && this.$route.path !== '/abilityCenter/targetList/detailFull' && this.$route.path !== '/abilityCenter/targetList/approvecode') {
          this.$post(this.baseUrl + '/user/v2/isAdminForCurUser').then((data) => {
            this.isAdminNew = data;
            this.$store.commit('common/setIsAdminNew', data)
          });
        }
        if (val.workspaceId && paths.includes(val.path) && this.$route.path !== '/abilityCenter/targetList/detailFull' && this.$route.path !== '/abilityCenter/targetList/approvecode') {
          this.isAgentPowerForWorkspace();
        }
      }
    }
  },
  created() {
    console.log('this.$route.path-----',this.$route.path)
    window.addEventListener('message', (event) => {
      localStorage.setItem('iframeRuleVersionId', '');
      if (event.data?.platform === 'ai-rule' && event.data?.versionId) {
        // 能力运行平台-能力设计
        localStorage.setItem('iframeRuleVersionId', event.data.versionId);
        this.handlePostIframePush();
      }
    });
    window.addEventListener('click-sideMenu', (cmd) => {
      console.log('菜单按钮', cmd);
      if (this.isCollapse === !cmd?.detail) {
        this.isCollapse = !this.isCollapse;
        this.$store.commit('common/setMenuCollapse', this.isCollapse);
      } else {
        this.isCollapse = !cmd?.detail;
        this.$store.commit('common/setMenuCollapse', this.isCollapse);
      }
    });
    this.isSuperAdminForCurUser();
    this.isAgentPowerForWorkspace();
    // this.isPlanAuth();
    this.offline();
  },
  mounted() {    
    console.log('process?.env.NODE_ENV 111',process?.env.NODE_ENV);
    if (this.$route.query.projectId || this.$route.query.algorithmProjectId) {
      const projectId = this.$route.query.projectId || this.$route.query.algorithmProjectId;
      this.$store.dispatch('fetchProjectInfo', projectId);
    }
    if (this.$store.state.planGenerate.isIframeHide) {
      document.querySelector('.containerCard').style.margin = '16px 0 0 0';
      document.querySelector('.containerCard').style.boxShadow = 'none';
    }
  },
  methods: {
    ...mapActions({
      updateGlobalNavigatorStatus: 'common/updateGlobalNavigatorStatusAction',
    }),
    getUrl(){
      let URL = ''
      switch (process?.env?.VUE_APP_ENV) {
        case 'production':
          URL = 'https://ioc-workbench-front.ennew.com'
          break;
        case 'dev':
          URL = 'https://ioc-workbench-front.dev.ennew.com'
          break;
        case 'uat':
          URL = 'https://ioc-workbench-front.uat.ennew.com'
          break;
        case 'fat':
          URL = 'https://ioc-workbench-front.fat.ennew.com'
          break;
      }
      return URL
    },    
    handlePostIframePush() {
      this.$router.push({
        path: '/abilityCenter/ability/orchestrationEngine',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      });
    },
    getQueryToken(url) {
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    // 外链跳转
    goHref(href) {
      switch (href) {
        case '1':
          // 认知模型生产
          window.open(`${process.env.VUE_APP_RULE_ENGINE}/#/`);
          break;
        case '3':
          // 智能能力运营
          window.open(
            this.getQueryToken(
              `${process.env.VUE_APP_RULEFLOW_ENGINEER}/operatingPlatform/operatingCenter/operatingCenter/electronicFence`
            )
          );
          break;
        case '4':
          // 自主预测
          window.open(
            `${process.env.VUE_APP_RULEFLOW_ENGINEER}/autonomousPredictionPlatform/abilityDesignCenter/abilityCenter/ability`
          );
          break;
        default:
          break;
      }
    },
    // 判断当前空间是否有权限
    async isAgentPowerForWorkspace() {
      const vm = this;
      const res = await vm.$axios.get(
        `${vm.baseUrl}/platform/conf/getApolloVal?key=agent.product.workspaces`
      );
      if (res && res.data && res.data.status === 200) {
        console.log('权限', res.data.data?.split(','));
        if (res.data.data?.split(',').indexOf(this.$route.query.workspaceId) > -1) {
          this.isAgentPower = true;
        } else {
          this.isAgentPower = false;
          // if (
          //   this.$route.path.indexOf('/planGenerate') > -1 ||
          //   this.$route.path.indexOf('/targetList') > -1
          // ) {
          //   this.$message({
          //     type: 'error',
          //     message: '此空间没有智能生产功能权限!'
          //   });
          //   // this.$router.push('/homePage');
          // }
        }
      }
    },
    // 判断当前用户是否是超级管理员
    async isSuperAdminForCurUser() {
      const vm = this;
      const res = await vm.$axios.post(`${vm.baseUrl}/user/isSuperAdminForCurUser`);
      if (res && res.data && res.data.status === 200) {
        vm.isSuperAdmin = res.data.data;
        this.$store.commit('workSpace/setIsSuperAdmin', res.data.data);
      }
    },
    async isPlanAuth() {
      const vm = this;
      const res = await vm.$axios.post(`${vm.baseUrl}/common/getAssistantMenuAuth`);
      if (res && res.data && res.data.status === 200) {
        console.log('res /common/getAssistantMenuAuth', res.data.data);
        if (
          res.data.data &&
          res.data.data.indexOf(sessionStorage.getItem('loadItcode')?.toLowerCase()) > -1
        ) {
          this.isPlanGen = true;
        } else {
          this.isPlanGen = false;
        }
      }
    },
    async RouteJust(path, openType, id, params) {
      console.log("yzw RouteJust", process?.env.NODE_ENV, process?.env.NODE_ENV === 'production', path, openType, id, params);
      if (openType === 1) {
        // 新页面跳转
        window.open(process.env.VUE_APP_DATA_MARK_URL, '_blank');

        /**
         * 为删除跳转菜单的高亮，使用el-menu本身的高亮切换行为
         * 先强制让菜单跳转无效路由，然后回跳
         */
        await this.$router.push(path);
        await this.$nextTick();
        this.$router.go(-1);
      } else if (openType === 2) {
        window.open(path, '_self');
      } else if (openType === 3) {
        const current = this.$router.history.current.fullPath;
        this.$router.push(current);
        window.open(path, '_blank');
      } else {
        // 系统内跳转
        // else{
        //   fromMenu = this.$route.query.fromMenu || '研发生产';
        // }
        this.$router.push({
          path,
          query:
            path === '/homePage'
              ? {}
              : {
                  workspaceId: this.$route.query.workspaceId,
                  workspaceName: this.$route.query.workspaceName,
                  ...params
                }
        });
      }
    },
    jumpToInModelProject(path, query) {
      const { projectId, bigModalFlag, workspaceId, workspaceName, algorithmProjectId } =
        this.$route.query;
      this.$router.push({
        path,
        query: {
          projectId: projectId || algorithmProjectId,
          algorithmProjectId: projectId,
          taskType: 2,
          bigModalFlag,
          workspaceId,
          workspaceName,
          ...query
        }
      });
    },
    jumpTo(path, query) {
      const { projectId, algorithmProjectId, type, projectName, dataPermissionType, projectType } =
        this.$route.query;
      this.$router.push({
        path,
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName,
          projectId: projectId || algorithmProjectId,
          algorithmProjectId: projectId || algorithmProjectId,
          type,
          projectType,
          projectName,
          dataPermissionType,
          ...query
        }
      });
    },
    toProject() {
      if(this.getIsNew){
        this.$router.push({
        path: '/blockly-project-new/ViewWrite',
        query: {
          workspaceId:
            this.$route.query.workspaceId ||
            this.$store.state.workSpace.currentWorkSpace.workspaceId,
          workspaceName:
            this.$route.query.workspaceName ||
            this.$store.state.workSpace.currentWorkSpace.workspaceName
        }
      });
      this.$store.commit('workSpace/setIsNew', false)
      }else{
        this.$router.push({
        path: '/blockly-project/Algorithm',
        query: {
          workspaceId:
            this.$route.query.workspaceId ||
            this.$store.state.workSpace.currentWorkSpace.workspaceId,
          workspaceName:
            this.$route.query.workspaceName ||
            this.$store.state.workSpace.currentWorkSpace.workspaceName
        }
      });
      }

    },
    handleToEdgeList() {
      this.$router.push({
        path: '/edgeCapability',
        query: {
          workspaceId:
            this.$route.query.workspaceId ||
            this.$store.state.workSpace.currentWorkSpace.workspaceId,
          workspaceName:
            this.$route.query.workspaceName ||
            this.$store.state.workSpace.currentWorkSpace.workspaceName
        }
      });
    },
    handleToAbilityCenter() {
      this.$router.push({
        path: '/abilityCenter/ability',
        query: {
          workspaceId:
            this.$route.query.workspaceId ||
            this.$store.state.workSpace.currentWorkSpace.workspaceId,
          workspaceName:
            this.$route.query.workspaceName ||
            this.$store.state.workSpace.currentWorkSpace.workspaceName
        }
      });
    },
    offline() {
      this.$axios.get(this.baseUrl + '/edge/switch/get', {}).then((res) => {
        if (res.data.status === 200) {
          this.edgeDeviceEnabled = res.data.data.edgeDeviceEnabled;
          this.edgeAppEnabled = res.data.data.edgeAppEnabled;
          this.edgeHistoryEnabled = res.data.data.edgeHistoryEnabled;
          this.$store.commit('common/setOfflineSwitch', res.data.data); // 其他页面的操作权限
        }
      });
    },
    dynamicJump() {
      let path = '';
      const query = this.$route.query;
      const type = this.$store.state.project.projectInfo?.type;
      switch (type) {
        case 4:
          query.fileType = 'fileList';
          path = '/datasetLast/nlp';
          break;
        case 5:
          path = '/datasetLast/cv';
          break;
        default:
          path = '/datasetLast/cv';
          break;
      }
      this.$router.push({
        path,
        query: {
          ...query
        }
      });
    },
    dataHandlerJump() {
      this.$router.push({ path: '/dataset/dataHandle', query: { ...this.$route.query } });
    },
    handleToEdgeDeatail(path, query) {
      this.$router.push({
        path,
        query: {
          ...this.$route.query,
          ...query
        }
      });
    }
  }
};
</script>
<style type="scss" scoped>
.app-menu-wrapper {
  color: #646566;
  line-height: 20px;
  .menu-icon {
    margin-right: 12px;
    color: #969799;
    &.menu-icon-collapse {
      margin-right: 0px !important;
    }

  }
}
.vertical {
      display: none;
    }
.nolayer {
  margin-top: -8px;
}
.app-menu-inner {
  height: calc(100vh - 48px);
  max-height: calc(100vh - 48px);
  overflow-y: auto;
  position: relative;
}
.el-menu-item.is-active {
  position: relative;
  background: #eff3ff;
  color: var(--slide-menu-active-color);
  &::before {
    content: '';
    left: 0;
    position: absolute;
    height: 40px;
    border-left: 4px solid var(--slide-menu-active-color);
  }
  &:hover {
    background: #e5e9f5 !important;
  }
  .menu-icon {
    color: var(--slide-menu-active-color);
  }
  > span {
    font-weight: bold;
  }
}
:deep(.el-submenu.is-active > .el-submenu__title) {
  color: var(--slide-menu-active-color);
  > span {
    color: var(--slide-menu-active-color);
    font-weight: bold;
  }
  .menu-icon {
    color: var(--slide-menu-active-color);
  }
}
:deep(.el-menu-item),
:deep(.el-submenu__title) {
  height: 40px;
  line-height: 38px;
  vertical-align: middle;
  display: flex;
  align-items: center;
  color: #646566;
  margin: 8px 0px;
  /* padding: 0px 18px !important; */
  position: relative;
}
:deep(.el-menu-item:first-child) {
  margin-top: 0px !important;
}
:deep(.el-menu-item:last-child) {
  margin-bottom: 0px !important;
}
:deep(.el-menu-item:hover) {
  background-color: #f6f7fb;
}
:deep(.el-submenu__title:hover) {
  background-color: #f6f7fb;
}
:deep(.el-menu-item *) {
  vertical-align: inherit;
}
:deep(.el-menu .el-submenu:first-child .el-submenu__title) {
  margin-top: 0px !important;
}
:deep(.el-submenu .el-menu-item) {
  min-width: auto !important;
}
/* :deep(.el-submenu .el-menu--inline .el-menu-item) {
  padding-left: 42px !important;
} */
:deep(.el-submenu .el-menu--inline .el-menu-item:first-child) {
  margin-top: 0px !important;
}
:deep(.el-menu:not(.el-menu--collapse)) {
  width: 184px;
}
.el-menu-item {
  height: 40px;
  line-height: 40px;
}
.back {
  padding: 0px 16px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  border-bottom: 1px solid #ebecf0;
  max-width: 183px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  margin-bottom: 8px;
  &:hover {
    background-color: #f6f7fb;
  }
  > span {
    color: #646566;
  }
  i {
    font-size: 14px;
    margin-right: 16px;
    color: #969799;
  }
}
/* :deep(.el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow) {
  transform: rotateZ(-360deg);
}
:deep(.el-submenu__icon-arrow) {
  transform: rotateZ(-90deg);
} */
.menu-divider {
  margin: 0;
  background: #ebecf0;
}
</style>
<style lang="scss" scoped>
.app-menu-inner {
  position: relative;
  
  > div {
    position: fixed;
    left: 0;
    top: 48px;
    bottom: 0;
    background: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s;
    z-index: 2000;
  }

  .el-menu {
    height: 100%;
    border-right: solid 1px #e6e6e6;
    
    &.el-menu--collapse {
      width: 64px;
    }
    
    &:not(.el-menu--collapse) {
      width: 200px;
    }
  }

  .menu-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style>
