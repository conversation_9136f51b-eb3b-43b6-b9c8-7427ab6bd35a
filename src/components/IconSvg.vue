<template>
  <svg
    class="icon-svg"
    aria-hidden="true"
    :style="style"
  >
    <use :xlink:href="icon" />
  </svg>
</template>

<script>
export default {
  name: 'IconSvg',
  props: {
    name: {
      type: String,
      required: true
    },
    // 颜色值
    color: {
      type: String,
      default: '#969799'
    }
  },
  computed: {
    icon () {
      return `#svg-${this.name}`
    },
    style () {
      return { '--color': this.color }
    }
  }
}
</script>
<style lang="less">
.icon-svg {
  height: 14px;
  width: 14px;
  fill: currentColor;
  display: inline-block;
  vertical-align: text-bottom;
  color: var(--color);
  &.size-medium {
    height: 48px;
    width: 48px;
  }
  &.size-large {
    height: 90px;
    width: 90px;
  }
}
</style>
