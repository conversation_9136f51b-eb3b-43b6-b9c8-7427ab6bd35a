<template>
 <div id="resize" class="resize" title="收缩侧边栏" @mousedown="startDrag">
  <div class="el-two-column__icon-top">
   <div class="el-two-column__icon-top-bar"></div>
  </div>
  <div class="el-two-column__trigger-icon">
   <SvgIcon name="dragborder" class="process-icon" />
  </div>
  <div class="el-two-column__icon-bottom">
   <div class="el-two-column__icon-bottom-bar"></div>
  </div>
 </div>
</template>
<script>
export default {
 name: 'twoColumn',
 props: {
  startDrag: {
   type: Function
  },
 },
 data() {
  return {

  };
 },
};
</script>
<style scoped lang="scss">
.resize {
 z-index: 1;
 cursor: row-resize;
 background-color: #f4f5f9;
 padding: 8px 0px;
 height: 10px;
 color: #c3cadd;
 display: flex;
 // flex-direction: column;
 align-items: center;

 &:hover {
  background: #e0e6ff;

  .process-icon {
   color: #3455ad !important;
  }
 }

 .el-two-column__icon-top {
  display: flex;
  justify-content: flex-end;
  width: 50%;
  height: 4px;
  display: flex;

  .el-two-column__icon-top-bar {
   width: 50%;
   height: 4px;
   background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
  }
 }

 .el-two-column__trigger-icon {
  width: 14px;
  height: 25px;
  color: #c3cadd;

  .process-icon {
   height: 25px;
   color: #c3cadd;
   transform: rotate(90deg);
  }
 }

 .el-two-column__icon-bottom {
  width: 50%;
  height: 4px;

  .el-two-column__icon-bottom-bar {
   width: 50%;
   height: 4px;
   background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
  }
 }
}
</style>
