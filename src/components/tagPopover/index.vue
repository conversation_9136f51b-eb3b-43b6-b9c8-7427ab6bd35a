<template>
  <div class="custom-tag-list">
    <el-tag
      v-for="el in showList"
      :key="el.id || el[keyVal]"
      bg-color="#E9F5FC"
      text-color="#45AAD9"
      :class="[el.name === '官方' && 'tag-official']"
    >
      {{ el[text] || el?.name }}
    </el-tag>
    <el-popover
      v-if="showIndex && showList?.length !== list?.length"
      :title="showTitle ? `全部标签 (${list?.length || 0})` : ''"
      placement="top-start"
      width="336px"
      popper-class="tag-popover"
      trigger="hover"
    >
      <template #reference>
        <el-tag bg-color="#E9F5FC" text-color="#0075BE" class="last-tag">
          {{ list?.length - showList?.length }}+
        </el-tag>
      </template>
      <div class="all-tag">
        <el-tag
          v-for="el in list"
          :key="el.id || el[keyVal]"
          bg-color="#E9F5FC"
          text-color="#45AAD9"
          :class="[el.name === '官方' && 'tag-official']"
        >
          {{ el[text] || el.name }}
        </el-tag>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'TagPopover',
  props: {
    parentId: {
      type: String,
      required: true
    },
    list: {
      type: Array,
      required: true,
      default: () => []
    },
    keyVal: {
      type: String,
      required: false
    },
    text: {
      type: String,
      required: false
    },
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showList: [],
      showIndex: 0
    };
  },
  watch: {
    list: {
      immediate: true,
      handler() {
        this.getShowList();
      }
    }
  },
  mounted() {
    this.getShowList();
  },
  methods: {
    getShowList() {
      const el = document.getElementById(this.parentId);
      const width = el?.clientWidth - 32;
      let length = 0;
      try {
        this.list.forEach((item, idx) => {
          length += item?.name?.length * 14 + 24;
          if (length + 45 > width) {
            this.showIndex = idx + 1;
            throw new Error('');
          }
        });
      } catch (e) {
        console.log('');
      }
      if (this.showIndex) {
        this.showList = this.list.slice(0, this.showIndex);
        return;
      }
      this.showList = this.list || [];
    }
  }
};
</script>
<style scoped lang="scss">
.custom-tag-list {
  min-height: 25px;
  /* margin: 0 4px; */
  white-space: nowrap;
  :deep(.el-tag) {
    height: 24px;
    line-height: 22px;
    --el-tag-hover-color: #4068d4;
    --el-tag-text-color: #4068d4;
    background: #eff3ff;
    border: 0;
    margin-right: 8px;
    padding: 0 8px;
    max-width: 150px;
    font-size: 14px;
    .el-tag__content {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.tag-popover {
  .all-tag {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    :deep(.el-tag) {
      padding: 0 8px;
      margin-right: 8px;
      margin-bottom: 8px;
      --el-tag-hover-color: #4068d4;
      --el-tag-text-color: #4068d4;
      background: #eff3ff;
      border: 0;
      max-width: 150px;
      font-size: 14px;
      .el-tag__content {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.last-tag {
  cursor: pointer;
}
</style>
