<template>
  <div class="card-main">
    <el-row :gutter="16" type="flex" class="row-flex">
      <el-col v-for="(item, index) in visibleTableData" :key="index" class="col-flex">
        <div class="gird-card">
          <div class="gird-card-content">
            <div class="card-top">
              <div class="card-img">
                <img v-if="item.ext_info.abilityIcon" :src="item.ext_info.abilityIcon" alt="">
                <img v-else src="@/assets/images/nl_1.png" alt="">
                <div class="img-overlay"></div>
              </div>
              <div class="right">
                <div style="display: flex;">
                  <div class="title-left">{{ item.name }}</div>
                  <Status :text="statusTypeMap[item.is_online]?.text" :bg-color="statusTypeMap[item.is_online]?.bgColor"
                    :dot-color="statusTypeMap[item.is_online]?.dotColor" />
                </div>
                <div class="card-tab">
                  <selectItem :array-items="item.tag?.map((tag) => {
                    return { key: tag.id, label: tag.name };
                  })" :max-length="2"></selectItem>
                </div>
              </div>
            </div>
            <div class="card-detail">
              <el-tooltip effect="dark" :content="item.ext_info?.abilityValue || ''" placement="top">
                <div>{{ item.ext_info?.abilityValue || '' }}</div>
              </el-tooltip>
            </div>
            <div class="creator">所属人: {{ item.owner_nickName }}</div>
          </div>
          <div class="itemFooter">
            <el-button type="text" v-if="item.is_online" @click="handleDetail(item)">
              <SvgIcon name="right-rect" style="margin-right: 2px;" />立即使用
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import selectItem from '@/views/planGenerate/selectItem.vue';
import Status from '@/components/Status/index.vue'
export default {
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      }
    },
    pageParams: {
      type: Object,
      default() {
        return {};
      }
    },
  },
  components: {
    selectItem,
    Status
  },
  data() {
    return {
      columns: 4, // 默认一排显示4个卡片
      tag: [
        { id: '1', label: '能力标签' },
        { id: '2', label: '用气安全' },
        { id: '3', label: '能力标签' },
        { id: '4', label: '用气安全' }
      ],
      statusTypeMap: {
        false: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        true: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
      },
    }
  },
  computed: {
    visibleTableData() {
      // 这里可以根据需要对 tableData 进行处理，例如分页或过滤
      return this.tableData;
    }
  },
  created() {
    console.log(this.tableData, this.$route.query, 111111111111);
  },
  methods: {
    handleDetail(val) {
      if (this.$route.path === '/abilityMarket' && this.tableData.length > 0) {
        this.$router.push({
          path: '/abilityCenter/talk',
          query: {
            workspaceId: val.work_space_id,
            workspaceName: val.workspaceName,
            ...this.pageParams,
            id: val.id,
            mode_selection: val.mode_selection,
          }
        });
      } else {
        this.$router.push({
          path: '/abilityCenter/talk',
          query: {
            ...this.$route.query,
            ...this.pageParams,
            id: val.id,
            mode_selection: val.mode_selection,
          }
        });

      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card-main {
  width: 100%;
}

.itemFooter {
  border-top: 1px solid #c8c9cc;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  max-width: 100%;
  overflow: hidden;

  .el-button:hover {
    // background-color: #4068d4; // 或者您希望的悬停颜色
    // border-color: #4068d4;

    // ::after {
    //   content: ">>";
    // }
  }

}
.row-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.col-flex {
  // flex: 1; /* 确保均匀分布且不会被内容撑大 */
  min-width: 0;
  /* 防止内容撑大 */
  overflow: hidden;
  /* 隐藏超出部分 */
  flex-basis: calc(100% / 4); // 默认一排显示4个卡片
  margin-bottom: 16px;
  box-sizing: border-box;
}

.gird-card:hover {
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.16);
}

.gird-card {
  height: 273.3px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #DCDDE0;
  box-sizing: border-box;

  &-content {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

      .creator {
          font-weight: 400;
          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: left; // 确保靠左对齐
          font-style: normal;
        }

    .card-top{
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        align-items: center;


        .right{
          align-self: flex-start;
          max-width: calc(100% - 76px)
        }
    }


    .card-img {
        position: relative;
        margin-right: 16px;
        overflow: hidden;
        border-radius: 5px;
            /* 或者您希望的圆角值 */
        img {
          height: 60px;
          width: 60px;

          object-fit: cover;
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.3);
          /* 放大图片 */
        }
        // .img-overlay {
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   width: 100%;
        //   height: 100%;
        //   background: rgba(0, 0, 0, 0.5);
        //   opacity: 0;
        //   transition: opacity 0.3s ease;
        // }

        // &:hover .img-overlay {
        //   opacity: 1;
        // }
      }

        .title-left {
          white-space: nowrap;
          /* 防止文本换行 */
          overflow: hidden;
          /* 隐藏超出部分 */
          text-overflow: ellipsis;
          /* 显示省略号 */
          height: 24px;
          margin: 0px 8px 8px 0px;
          line-height: 24px;
          font-weight: 500;
          font-size: 18px;
          color: #1D2129;
          font-family: PingFangSC, PingFang SC;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }

    .card-detail {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 限制为三行 */
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
      font-weight: 400;
      font-size: 14px;
      color: #646566;
      line-height: 20px;
      font-style: normal;
      min-height: 60px;
    }

    .creat-tab {
      display: flex;
      margin: 8px 0px;

      .name {
        margin-right: 12px;
      }

      .tab-item {
        font-weight: 400;
        font-size: 14px;
        color: #969799;
        line-height: 20px;
        text-align: justify;
        font-style: normal;
      }
    }

    .card-tab {
      display: flex;

      .tab-title {}
    }
  }

  .itemFooter {
    border-top: 1px solid #c8c9cc;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 16px;
    max-width: 100%;
    overflow: hidden;
  }
}

// 媒体查询，调整不同屏幕尺寸下的卡片宽度
@media (max-width: 1500px) {
  .col-flex {
    flex-basis: calc(100% / 3); // 一排显示3个卡片
  }
}

@media (max-width: 1280px) {
  .col-flex {
    flex-basis: calc(100% / 3); // 一排显示3个卡片
  }
}

@media (max-width: 1080px) {
  .col-flex {
    flex-basis: calc(100% / 2); // 一排显示2个卡片
  }
}

@media (max-width: 768px) {
  .col-flex {
    flex-basis: 100%; // 一排显示1个卡片
  }
}
</style>
