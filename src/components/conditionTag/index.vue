<template>
  <div class="condition-tag">
   <div v-if="model" class="searchFlex" :style="{ marginTop: marginTop }">
    <div class="searchLeft searchTag">
     <div class="scene" style=" flex: 1; display: flex;">
      <div style="color: #646566;  text-align: right; font-weight: bold; line-height: 28px;">
      <i :class="Icon" style="margin-right: 5px" />{{label}}：
      </div>
      <div class="content-item">
       <div :ref="`tagPicker-${uniqueId}`" class="tag-picker">
        <div :ref="`tagItems-${uniqueId}`" class="tagTtems" style="display: flex;align-items: center;flex-wrap: wrap;">
         <div v-for="item in visibleTags" :key="item.code"
          :class="checkedTypeTags.indexOf(item.code) > -1 ? 'tag-item active' : 'tag-item'"
          @click="selectType(item)">
          {{ item.name }}
         </div>
         <div v-if="isExpanded" @click="toggleExpand" :class="isExpanded ? 'collapse-btn' : 'expand-btn'">
         <span>
          收起
         </span>
         <i style="margin-left: 4px;" class="el-icon-arrow-up"></i>
         </div>
        </div>
        <div v-if="!isExpanded && showRow" :ref="`expand-${uniqueId}`" @click="toggleExpand"
         :class="isExpanded ? 'collapse-btn' : 'expand-btn'">
         <span>
         展开
         </span>
         <i style="margin-left: 4px;" class="el-icon-arrow-down"></i>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div v-else class="searchFlex" :style="{ marginTop: marginTop }">
    <div class="searchLeft searchTag">
     <div class="scene" style=" flex: 1; display: flex;">
      <div style="color: #646566;  text-align: right; font-weight: bold; line-height: 28px;" class="width_gl">
      <i :class="Icon" style="margin-right: 5px" />{{label}}：
      </div>
      <div class="content-item">
       <div :ref="`tagPicker-${uniqueId}`" class="tag-picker">
        <div :ref="`tagItems-${uniqueId}`" class="tagTtems" style="display: flex;align-items: center;flex-wrap: wrap;">
         <div v-for="item in visibleTags" :key="item.code"
          :class="checkedTypeTags.indexOf(item.id) > -1 ? 'tag-item active' : 'tag-item'"
          @click="selectType(item)">
          {{ item.name }}
         </div>
         <div v-if="isExpanded" @click="toggleExpand" :class="isExpanded ? 'collapse-btn' : 'expand-btn'">
         <span>
          收起
         </span>
         <i style="margin-left: 4px;" class="el-icon-arrow-up"></i>
         </div>
        </div>
        <div v-if="!isExpanded && showRow" :ref="`expand-${uniqueId}`" @click="toggleExpand"
         :class="isExpanded ? 'collapse-btn' : 'expand-btn'">
         <span>
         展开
         </span>
         <i style="margin-left: 4px;" class="el-icon-arrow-down"></i>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
</template>
<script>
export default {
  name: 'ConditionTag',
  props: {
    model: {
      type: Boolean,
      default: true,
    },
    Icon: {
      type: String,
      default: 'el-icon-shopping-bag-1'
    },
    label: {
      type: String,
      default: '场景分类'
    },
    uniqueId: {
      type: String,
      default: ''
    },
   senceTypeList: {
    type: Array,
    default: ()=> []
   },
   marginTop: {
    type: String,
    default: '0px'
   },
   checkedTypeTags: {
    type: Array,
    default: () => []
   }
  },
  data() {
  return {
   isExpanded:false,
   tagWidthCache: {}, // 缓存标签宽度
   tagPickerWidth: 0,
   tagPicker: '',
   showRow: false,
   visibleTags: [],
  }
  },
  created() {

  },
  watch: {
  senceTypeList: {
   handler(newVal) {
    if (newVal.length > 0) {
     // this.calculateTotalTagWidth()
    }
   },
   immediate: true
  },
  tagPicker(newval, oldval) {
   if (newval > oldval) {
    this.showRow = false
   }
   if (newVal !== oldVal) {
   }
  }
  },
  beforeDestroy() {
  const tagPicker = this.$refs[`tagPicker-${this.uniqueId}`];
  if (tagPicker) {
   const resizeObserver = new ResizeObserver(() => {
    this.calculateTotalTagWidth();
   });
   resizeObserver.unobserve(tagPicker);
  }
  },
  methods: {
   selectType(code) {
   this.$emit('selecType', code);
   },
   observeResize() {
   const tagPicker = this.$refs[`tagPicker-${this.uniqueId}`];
   if (tagPicker) {
    this.calculateTotalTagWidth();
    const resizeObserver = new ResizeObserver((ee) => {
     this.calculateTotalTagWidth();
    });
    resizeObserver.observe(tagPicker);
   }
   },
   toggleExpand() {
   this.isExpanded = !this.isExpanded;
   this.calculateTotalTagWidth();
   },
   calculateTotalTagWidth() {
   if (this.$refs[`tagItems-${this.uniqueId}`] && this.senceTypeList.length > 0) {
    let visibleTags = [];
    let hiddenTags = [];
    let totalWidth = 0;
    this.itemsPerLine = 0;
    this.tagPickerWidth = this.$refs[`tagPicker-${this.uniqueId}`].offsetWidth;
    const values = Object.values(this.tagWidthCache);
    let sum = 0
    values.forEach((num,index) => {
     const margin = index > 0 ? 8 : 0;
    sum += num + margin;
    });
    this.senceTypeList.forEach((item, index) => {
      this.getTagWidth(item);
    });
    this.showRow = sum > this.tagPickerWidth;
    this.showExpandButton = hiddenTags.length > 0;
    this.$nextTick(() => {
        const expandButtonWidth = this.$refs[`expand-${this.uniqueId}`] ? this.$refs[`expand-${this.uniqueId}`].offsetWidth : 0;
        this.senceTypeList.forEach((item, index) => {
          const tagWidth = this.getTagWidth(item);
          const margin = index > 0 ? 8 : 0;
          if (totalWidth + tagWidth + margin + expandButtonWidth > this.tagPickerWidth) {
           totalWidth += tagWidth + margin;
            hiddenTags.push(item);
            // 即使标签被隐藏，也要更新 totalWidth
          } else {
            totalWidth += tagWidth + margin;
            this.itemsPerLine++;
            visibleTags.push(item);
          }
        });

        // 更新 visibleTags 和 hiddenTags
        if (!this.isExpanded) {
          this.visibleTags = visibleTags;
          this.hiddenTags = hiddenTags;
        } else {
          this.visibleTags = [...visibleTags, ...hiddenTags];
          this.hiddenTags = [];
        }
      });
   }
   },
   getTagWidth(item) {
  //  if (!this.tagWidthCache[item.code]) {
   if (!this.tagWidthCache[item.name]) {
    const tagElement = document.createElement('div');
    tagElement.className = 'tag-item';
    tagElement.innerText = item.name;
    tagElement.style.position = 'absolute';
    tagElement.style.visibility = 'hidden';
    this.$refs[`tagItems-${this.uniqueId}`].appendChild(tagElement);
    const rect = tagElement.getBoundingClientRect();
    this.$refs[`tagItems-${this.uniqueId}`].removeChild(tagElement);
    this.tagWidthCache[item.name] = rect.width;
   }
   return this.tagWidthCache[item.name];
   }
  }
}
</script>
<style lang="scss" scoped>
.condition-tag {
  .searchFlex {
    display: flex;
    // margin-bottom: 10px;
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
 
    .searchLeft {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex: 1;
 
      .lable_filter {
        color: #646566;
        margin-bottom: 8px;
      }
 
      &.searchTag {
        display: flex;
        flex-direction: row;
        align-items: flex-end !important;
        // margin-top: 8px;
      }
 
      .tag-item {
        display: inline-block;
        word-break: keep-all;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 20px;
        padding: 5px 12px;
        margin-right: 8px;
        // margin-bottom: 8px;
        height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
 
        &:last-child {
          margin-right: 0px;
        }
 
        &.active {
          background-color: #eff3ff;
          color: #4068d4;
        }
 
        &:hover {
          color: #4068d4;
        }
      }
      .tag-item:hover {
        background-color: #eff3ff;
    color: #4068d4;
      }
      ::v-deep .tag-item {
        display: inline-block;
        word-break: keep-all;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 20px;
        padding: 5px 12px;
        margin-right: 8px;
        // margin-bottom: 8px;
        height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
 
        &:last-child {
          margin-right: 0px;
        }
 
        &.active {
          background-color: #eff3ff;
          color: #4068d4;
        }
 
        &:hover {
          color: #4068d4;
        }
      }
      ::v-deep .tag-item:hover {
        background-color: #eff3ff;
        color: #4068d4;
      }
      .expand-btn, .collapse-btn {
       cursor: pointer;
       display: inline-block;
      }
 
 .expand-btn {
  color: #4068d4;
  height: 22px;
  line-height: 22px;
  font-weight: 400;
  padding: 0 4px;
 }
 
 .collapse-btn {
  color: #4068d4;
 }
 .flex-wrap {
  display: flex;
  flex-wrap: wrap;
 }
      .searchItem {
        max-width: 300px;
        margin-right: 16px;
        width: 30%;
 
        .searchLabel {
          font-weight: 400;
          color: #646566;
          line-height: 22px;
          font-size: 14px;
          margin-bottom: 8px;
        }
      }
    }
    .centered-search {
       display: flex;
       justify-content: center;
       align-items: center;
    .search-button {
      background-color: #4068d4; /* 按钮背景色 */
      border-color: #4068d4; /* 按钮边框色 */
      color: #fff; /* 按钮文字颜色 */
      font-weight: 500; /* 按钮文字粗细 */
    }
 
  .search-button:hover {
    background-color: #66B1FF; /* 按钮悬停背景色 */
    border-color: #66B1FF; /* 按钮悬停边框色 */
  }
 
  .search-button:active {
    background-color: #3A8EE6; /* 按钮激活背景色 */
    border-color: #3A8EE6; /* 按钮激活边框色 */
  }
 }
  }
  .content-item {
   display: flex;
   flex-wrap: wrap;
   align-items: center;
   flex: 1;
   line-height: 22px;
   min-width: 0;
   position: relative;

   .tag-picker {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
   }
  }
  .expand-btn {
  color: #4068d4;
  height: 22px;
  line-height: 22px;
  font-weight: 400;
  padding: 0 4px;
 }
 
 .collapse-btn {
  color: #4068d4;
 }
}
</style>