<template>
  <div class="custom-status" :style="statusStyle">
    <i class="custom-status__point"></i>
    <span class="custom-status__content">
      <slot></slot>
    </span>
  </div>
</template>
<script>
export default {
  name: 'CustomStatus',
  props: {
    type: {
      type: String,
      default: 'info'
    }
  },
  data() {
    return {
      statusEnmu: {
        success: { '--background': '#ebf6ed', '--pointColor': '#39ab4c' },
        warning: { '--background': '#fdf6e5', '--pointColor': '#ffa900' },
        info: { '--background': '#ebeffa', '--pointColor': '#4068d4' },
        danger: { '--background': '#fcecec', '--pointColor': '#ea4646' },
        neutral: { '--background': '#f2f2f2', '--pointColor': '#7d7e80' }
      }
    };
  },
  computed: {
    statusStyle() {
      return this.statusEnmu[this.type];
    }
  }
};
</script>
<style lang="less" scoped>
.custom-status {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  line-height: initial;
  background: var(--background);
  .custom-status__point {
    height: 8px;
    width: 8px;
    background: var(--pointColor);
  }
  .custom-status__content {
    margin-left: 5px;
    font-size: 14px;
    color: #323233;
  }
}
</style>
