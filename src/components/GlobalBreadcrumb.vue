<template>
  <div v-if="breadcrumbData.length" class="global-breadcrumb">
    <el-breadcrumb separator="/" v-if="isShowBreadcrumb !== 'none'">
      <el-breadcrumb-item v-if="$route.query.workspaceId">
        当前所在空间：{{ workspaceNameFn() }}
      </el-breadcrumb-item>
      <el-breadcrumb-item v-for="item in breadcrumbData" :key="item.label" :to="item.route">
        {{ item.label }}
      </el-breadcrumb-item>
      <template>
        <el-breadcrumb-item v-if="operationsResearchIsShow">
          <div class="headerTitle">
            <div style="display: flex; align-items: center">
              <div class="title">
                {{
                  schemeInfo.name
                    ? schemeInfo.name
                    : $route.query.name
                    ? decodeURIComponent(encodeURIComponent($route.query.name))
                    : ''
                }}（{{ $route.query.id }}）
              </div>
              <div class="sence-tag">
                {{
                  schemeInfo.agent_scene_name
                    ? schemeInfo.agent_scene_name
                    : $route.query.agent_scene_name
                    ? decodeURIComponent(encodeURIComponent($route.query.agent_scene_name))
                    : ''
                }}
              </div>
              <el-button
                v-if="!editIsShow && editFnStatus && breadcrumbData[breadcrumbData.length - 1].label !== '能力样版'"
                type="text"
                style="margin-left: 10px"
                @click="editFn"
                ><i class="el-icon-edit"></i
              ></el-button>
            </div>
          </div>
        </el-breadcrumb-item>
        <el-breadcrumb-item
          v-if="operationsResearchIsShow"
          :class="{
            displayTypebreadcrumb: editIsShow,
            specialScene: specialScene.includes(schemeInfo.agent_scene_code) || ['ConfTaskPlanchat','planchat'].includes(isOldorNew)
          }"
          style="float: right; display: flex; align-items: center"
        >
          <template v-if="editIsShow">
            <div
              v-if="
                [
                  'device_ops_assistant_scene',
                  'artificial_handle_scene',
                  'visit_leader_cognition_scene',
                  'rule_generation_scene',
                  'sop_scene'
                ].indexOf(schemeInfo.agent_scene_code) > -1
              "
              class="displayType"
              @click="changeDisplayType"
            >
              <img
                style="width: 16px; height: 16px"
                src="@/assets/images/planGenerater/model-icon.png"
              />
              <div>{{ displayType === 1 ? '标准模式' : '高级模式' }}</div>
            </div>
          </template>
          <div v-if="!editIsShow && knowledgeBaseInfo.isShow">
            知识库：
            <el-select
              v-model="knowledgeBaseInfo.knowledgeBaseID"
              placeholder="请选择知识目录"
              class="knowledgebase-selector"
              filterable
              @change="handleKnowledgeBaseChange">
              <el-option
                v-for="item in knowledgeBaseInfo.selectionList"
                class="knowledgebase-select-option"
                :key="item.id"
                :label="item.name"
                :value="item.id">
                <div class="option-content">
                  <img src="@/assets/images/dir.png" class="dir-icon" />
                  <span class="option-text">{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <!-- 暂时隐藏按钮 -->
          <!-- <el-button
            size="mini"
            type="primary"
            v-if="$route.query?.ability_id||'' != ''"
            @click="
              () => {
                $router.push({ path: '/abilityCenter/targetList/detail', query: { ...$route.query,'id': $route.query.ability_id} });
              }
            "
          >进入能力仓库</el-button> -->
          <el-button
            :disabled="breadcrumbData[breadcrumbData.length - 1].label === '能力样版'"
            size="mini"
            type="primary"
            icon="el-icon-setting"
            @click="
              () => {
                $router.push({ path: '/planGenerate/sceneConfig', query: { ...$route.query } });
              }
            "
          ></el-button>
        </el-breadcrumb-item>
      </template>
    </el-breadcrumb>

    <div v-if="isWhitePath" class="gongchengTitle">
      {{ projectNameFn() }}
      <!-- 如果你想要使用 $route.query.projectName，可以取消下面这行的注释并删除上一行 -->
      <!-- {{ $route.query.projectName }} -->
    </div>
    <!-- 如果你想要添加 select 组件，可以取消下面这部分的注释 -->
    <!--
    <el-select
      v-if="isOverviewSelectVisible"
      class="overview-type-select"
      v-model="overviewType"
      placeholder="请选择"
      @change="handleChange"
    >
      <el-option label="我的" :value="1"></el-option>
      <el-option label="全部" :value="2"></el-option>
    </el-select>
    -->
  </div>
</template>

<script>
import {
  getKnowledgeList,
  getSceneInstanceEnvs,
  querySchemeDetailById, updateSceneInstanceEnvs
} from '@/api/planGenerateApi.js';
import { mapActions, mapGetters,mapMutations,mapState } from 'vuex';
import Bus from './bus.js';
import axios from 'axios';
import Vue from 'vue';

export default {
  name: 'GlobalBreadcrumb',
  data() {
    return {
      PlanchatVersion: 'v2',
      overviewType: undefined, // 总览统计1 :个人 2: 全部
      schemeInfo: {},
      isShow: false,
      displayType: 1,
      specialScene: ['digital_twin_assistant_scene'],
      // enterName: {
      //   develop: '研发生产',
      //   expert: '专家生产'
      // },
      enterName1: {
        ConfTaskPlanchat: '切换老版',
        planchat: '切换新版'
      },
      isShowBreadcrumb: '',
      localWorkspaceName: '',  // 新增，用于存储本地空间名称
      sceneInstanceEnvs: [],  // 用于存储场景实例envs
      knowledgeBaseInfo: {
        isShow: false,
        knowledgeBaseID: '',
        selectionList: []
      }
    };
  },
  async created() {
    Bus.$on('operations-research', (data) => {
      this.getOperationsData(data);
      console.log('this.schemeInfo.agent_scene_code', this.schemeInfo.agent_scene_code);

    });
    Bus.$on('operations-displayType', (data) => {
      this.displayTypeFn(data);
    });
    if (this.$route.query.workspaceId) {
      this.initWorkspaceName();
    }
  },

  async mounted(){
    console.log("isOldorNew", this.isOldorNew, this.schemeInfo.agent_scene_code);
  },
  beforeDestroy() {
    Bus.$off('operations-research');
    Bus.$off('operations-displayType');
  },
  computed: {
   ...mapState('operations', ['newSchemeInfo','embed']),
    ...mapGetters({
      curOverviewType: 'common/curOverviewTypeGetter',
      currentWorkSpace: 'workSpace/getCurrentWorkSpace',
      currentProject: 'project/getCurrentProject'
    }),
    isOverviewSelectVisible() {
      return ['dashboard', 'home'].includes(this.$route.name);
    },
    isWhitePath() {
      return !!(
        this.$route.query.algorithmProjectId &&
        this.$route.query.type &&
        this.$route.query.projectName
      );
    },
    operationsResearchIsShow() {
      return this.$route.meta.isShow;
    },
    // // 是专家进入还是研发进入
    // isDevpOrExpert() {
    //   return this.$route.query.enterType;
    // },

    isOldorNew(){
    const path = this.$route.path; // 获取当前路径
  const segments = path.split('/'); // 按斜杠分割路径
  return segments.pop(); // 返回最后一个元素
    },
    editFnStatus() {
      return this.$route.meta.editFn;
    },
    editIsShow() {
      return this.$route.meta.editIsShow;
    },
    breadcrumbData() {
      const overwrite = this.$route.meta.breadcrumbOverwrite;
      const data = this.$route.meta.breadcrumbData;
      const generator = this.$route.meta.breadcrumbGenerator;
      this.isShowBreadcrumb = this.$route.meta.display;

      if (overwrite) {
        return [];
      }

      if (data && Array.isArray(data)) {
        return data;
      } else {
        if (typeof generator === 'function') {
          return generator();
        } else {
          return [];
        }
      }
    }
  },

  watch: {
   $route: {
      immediate: true,
      handler(val) {
       console.log("this.$route.path33333333", this.$route);

      }
    },
   newSchemeInfo: {
      immediate: true, // 立即执行一次
      handler(newVal) {
        // 将 newSchemeInfo 的值赋给 schemeInfo
        this.schemeInfo = newVal;
      }
    },
    curOverviewType: {
      handler(val) {
        this.overviewType = val;
      },
      immediate: true
    },
    '$route.query.id': {
      handler(val) {
        if (val && this.$route.path !== '/model-warehouse/detail' && this.$route.path !== '/project-python-model/detail') {
          this.fetchData();
        }
      },
      immediate: true
    },
    '$route.query.workspaceId': {
      immediate: true,
      handler(val) {
        if (val) {
          this.initWorkspaceName();
        }
      }
    }
  },
  methods: {
    ...mapMutations('operations',['getEditVisible']),
    async fetchData() {
      // 获取场景实例envs
      const schemeEnvsRes = await getSceneInstanceEnvs(this.$route.query.id);
      if (schemeEnvsRes.status === 200) {
        this.sceneInstanceEnvs = schemeEnvsRes.data?.global_constant;
      }
      const filteredList = this.sceneInstanceEnvs.filter(item => item.variable_type === 'knowledge');
      if (filteredList != null && filteredList.length === 1) {
        this.knowledgeBaseInfo.isShow = true;
        this.knowledgeBaseInfo.knowledgeBaseID = filteredList[0]?.variable_value;
        await this.getKnowledgeFun()
      } else {
        this.knowledgeBaseInfo.isShow = false;
        this.knowledgeBaseInfo.knowledgeBaseID = '';
        this.knowledgeBaseInfo.selectionList = [];
      }
    },
    async getKnowledgeFun() {
      const res = await getKnowledgeList()
      if (res.status === 200 && res.data.code === '0') {
        this.knowledgeBaseInfo.selectionList = res.data.data
        try {
          this.knowledgeBaseInfo.selectionList = this.tableData.map(item => {
            return Object.assign({
              ...item,
              checked: item.id === this.knowledgeBaseInfo.knowledgeBaseID
            })
          })
        } catch (error) {
          console.error(error);
        }
      }
    },
    async handleKnowledgeBaseChange() {
      const filteredList = this.sceneInstanceEnvs.filter(item => item.variable_type === 'knowledge');
      const oldKnowledgeBaseID = filteredList[0]?.variable_value;
      this.sceneInstanceEnvs.forEach((item) => {
        if (item.variable_type === 'knowledge') {
          item.variable_value = this.knowledgeBaseInfo.knowledgeBaseID
        }
      });
      const envsUpdateResp = await updateSceneInstanceEnvs(this.$route.query.id, {global_constant: this.sceneInstanceEnvs});
      if (envsUpdateResp.status === 200) {
        Vue.prototype.$message.success({
          message: '知识库更新成功！'
        })
      } else {
        Vue.prototype.$message.error({
          message: '知识库更新失败，请刷新页面重试！'
        })
      }
    },
    ...mapActions({
      updateCurOverviewType: 'common/updateCurOverviewTypeAction'
    }),
    displayTypeFn(data) {
      this.displayType = data;
    },
    // 运筹专有场景路由
    operationsResearch(data) {},
    // 获取数据
    getOperationsData(data) {
      this.schemeInfo = data;
    },
    editFn() {
      this.getEditVisible(true)
      Bus.$emit('operations-research-edit', '');
    },
    changeDisplayType() {
      Bus.$emit('operations-research-changeDisplayType', '');
    },
    // changeDevAndExpert() {
    //   if (this.$route.query.enterType === 'expert') {
    //     this.$router.push({
    //       path: '/planGenerate/wllsDevPlanchat',
    //       query: {
    //         ...this.$route.query,
    //         status: this.schemeInfo.status,
    //         id: this.schemeInfo.id,
    //         enterType: 'develop',

    //       }
    //     });
    //   } else {
    //     this.$router.push({
    //       path: '/planGenerate/wllsExpertPlanchat',
    //       query: {
    //         ...this.$route.query,
    //         status: this.schemeInfo.status,
    //         id: this.schemeInfo.id,
    //         enterType: 'expert'
    //       }
    //     });
    //   }
    // },
   changeOldandNew() {
      console.log("请问点击了吗 00");
      if (this.isOldorNew === 'ConfTaskPlanchat') {
        console.log("请问点击了吗 01");
        this.$router.push({
          path: '/planGenerate/planchat',
          query: {
            ...this.$route.query,
            status: this.schemeInfo.status,
            id: this.schemeInfo.id,
          }
        });
      } else {
        console.log("请问点击了吗 02");
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          query: {
            ...this.$route.query,
            status: this.schemeInfo.status,
            id: this.schemeInfo.id,
          }
        });
      }
    },
    async initWorkspaceName() {
      try {
        const res = await this.getSpceInfoDetail();
        if (res?.workspaceName) {
          this.localWorkspaceName = res.workspaceName;
          const newCurrWS = {
            workspaceId: res.id,
            workspaceName: res.workspaceName
          };
          localStorage.setItem('currentWorkSpace', JSON.stringify(newCurrWS));
          this.$store.commit('workSpace/setCurrentWorkSpace', newCurrWS);
        }
      } catch (error) {
        console.error('获取空间信息失败:', error);
      }
    },
    handleChange(val) {
      this.updateCurOverviewType(val);
    },
    projectNameFn() {
      const projectName = this.currentProject && this.currentProject.name;
      return projectName;
    },
    workspaceNameFn() {
      // 如果有本地存储的名称，直接返回
      if (this.localWorkspaceName) {
        return decodeURIComponent(this.localWorkspaceName);
      }

      // 从 vuex store 获取
      if (this.currentWorkSpace?.workspaceName) {
        return decodeURIComponent(this.currentWorkSpace.workspaceName);
      }

      // 从 localStorage 获取
      try {
        const storedWorkspace = localStorage.getItem('currentWorkSpace');
        if (storedWorkspace) {
          const parsed = JSON.parse(storedWorkspace);
          if (parsed.workspaceName) {
            return decodeURIComponent(parsed.workspaceName);
          }
        }
      } catch (e) {
        console.error('解析 localStorage 失败:', e);
      }

      // 从路由参数获取
      if (this.$route.query.workspaceName) {
        return decodeURIComponent(this.$route.query.workspaceName);
      }

      // 如果都没有，但有 workspaceId，触发获取
      if (this.$route.query.workspaceId && !this.localWorkspaceName) {
        this.initWorkspaceName();
        return '加载中...';
      }

      return '未知空间';
    },
    updateField(workspaceName) {
      const queryParams = Object.assign({}, this.$route.query); // 复制原始查询参数对象
      // 修改指定字段的值
      queryParams.workspaceName = workspaceName;
      // 导航到新的URL
      this.$router.push({ path: this.$route.path, query: queryParams });
    },
    // 查询空间详情
    async getSpceInfoDetail() {
      const res = await this.$post(`${this.baseUrl}/workspace/detail`, {
        id: this.$route.query.workspaceId
      });
      console.log('空间信息', res,this.$route.path);
      const currWS = JSON.parse(localStorage.getItem('currentWorkSpace')) || {};
      const newCurrWS = {
        ...currWS,
        workspaceId: res?.id,
        workspaceName: res?.workspaceName
      };
      localStorage.setItem('currentWorkSpace', JSON.stringify(newCurrWS));
      this.updateField(res?.workspaceName);
      // this.$store.commit('workSpace/setCurrentWorkSpace', newCurrWS);
      return res?.workspaceName;
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-breadcrumb) {
  .el-breadcrumb__inner {
    &.is-link {
      cursor: pointer !important;
    }
  }
  .el-breadcrumb__item {
    height: 40px;
  }
}

.knowledgebase-selector {
  width: 200px;
}

.knowledgebase-select-option {
  width: 300px; /* Adjust this value as needed */
  display: flex;
  align-items: center;

  .option-content {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .dir-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .option-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

}

.sence-tag {
  margin-left: 16px;
  padding: 0 8px;
  height: 24px;
  border-radius: 2px;
  max-width: calc(100vw - 380px);
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;

  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;
  line-height: 24px;
  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;
  background: #ebf9ff;
  color: #318db8;
}
.displayType {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 14px;
  color: #4068d4;
  line-height: 22px;
  position: relative;
  cursor: pointer;
  &::after {
    content: '';
    position: absolute;
    right: -8px;
    height: 12px;
    top: 6px;
    width: 1px;
    background: #c8c9cc;
  }
}
.displayTypebreadcrumb {
  padding: 5px 0;
  ::v-deep .el-breadcrumb__inner {
    width: 140px;
    display: flex;
    align-items: center;
  }
}
.specialScene {
  :deep(.el-breadcrumb__inner) {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  :deep(.el-input--suffix){
    display: flex;
    align-items: center
  }
}
</style>
<style lang="sass" scoped>

.global-breadcrumb
  padding: 0 0px
  width: 100%
  background-color: #fff
  .el-breadcrumb
    padding: 0px 20px
    height: 40px
    line-height: 40px
    flex-shrink: 0
    justify-content: space-between
    line-height: 40px
    border-bottom: 1px solid #ebecf0

  .gongchengTitle
    font-size: 18px
    font-weight: bold
    color: #323233
    padding: 14px 20px
  .overview-type-select
    width: 100px
</style>
