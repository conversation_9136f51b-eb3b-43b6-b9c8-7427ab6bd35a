<template lang="pug">
  .swiper(ref="swiper")
    .swiper-wrapper
      .swiper-slide(v-for="item in data" :key="item.label")
        img(:src="item.imgUrl")
        .content
          span {{ item.label }}
          .more-statics(v-if="(Object.prototype.toString.call(item.value) === '[object Object]')")
            div {{`${Object.keys(item.value)[0]}：${Object.values(item.value)[0]} ${nameUnit(Object.keys(item.value)[0])}`}}
            el-popover(placement='bottom', width='200', trigger='hover')
             div {{item.label}}
             .more-statics-content(v-for="[key,value] in Object.entries(item.value)")
              div {{key}}：{{value}}{{nameUnit(key)}}

             i(slot="reference" class="el-icon-more")
          p
            em(v-if="item.valuePrefix") {{ item.valuePrefix }}
            ICountUp(
              v-if="!(Object.prototype.toString.call(item.value) === '[object Object]')"
              :delay="delay"
              :endVal="item.value"
              :options="options"
              @ready="onReady"
            )
            em(v-if="item.valueSuffix") {{ item.valueSuffix }}

    .swiper-pagination
    //- .swiper-button-prev
    //- .swiper-button-next
    //- .swiper-scrollbar
</template>

<script>
import Swiper, { Pagination } from 'swiper'
// { Navigation, Pagination }
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'

import ICountUp from 'vue-countup-v2'

export default {
  name: 'CustomSwiper',

  components: { ICountUp },
  props: {
    data: {
      type: Array,
      default () {
        return []
      }
    }
  },

  data () {
    return {
      swiperInstance: null,
      delay: 1000,
      endVal: 0,
      options: {
        useEasing: true,
        useGrouping: true,
        separator: ',',
        decimal: '.',
        prefix: '',
        suffix: ''
      }
    }
  },

  mounted () {
    this.swiperInstance = new Swiper(this.$refs.swiper, {
      modules: [Pagination],
      slidesPerView: 1,
      spaceBetween: 16,
      breakpoints: {
        320: {
          slidesPerView: 1
        },
        480: {
          slidesPerView: 1
        },
        640: {
          slidesPerView: 2
        },
        768: {
          slidesPerView: 3
        },
        1024: {
          slidesPerView: 4
        },
        1200: {
          slidesPerView: 5
        },
        1280: {
          slidesPerView: 6
        }
      },
      loop: false,
      // modules: [Navigation, Pagination],
      pagination: {
        el: '.swiper-pagination'
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      scrollbar: {
        el: '.swiper-scrollbar'
      }
    })

    window.addEventListener('resize', this.resize)
  },

  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
    this.swiperInstance.destroy()
  },

  methods: {
    nameUnit (val) {
      let unit = 'G'
      if (val.indexOf('gpu') !== -1) {
        unit = '卡'
      } else if (val.indexOf('cpu') !== -1) {
        unit = '核'
      }
      return unit
    },
    resize () {
      this.swiperInstance.update()
    },

    onReady (instance, CountUp) {

    }
  }
}
</script>

<style lang="sass" scoped>
.swiper
  .swiper-slide
    display: flex
    justify-content: center
    align-items: center
    //min-width: 200px
    height: 108px
    background: rgba(64, 104, 212, 0.04)
    border-radius: 4px

    img
      width: 67px
      height: 75px
      margin-right: 16px

    .content
      display: flex
      flex-direction: column
      p
      word-break: break-word
      > span
        font-size: 14px
        font-weight: 400
        color: #646566
      p > span
        font-size: 20px
        font-weight: 500
        color: #323233
      .more-statics
        span
         font-size: x-small
         &:hover
           color: blue

      em
        font-style: normal
</style>
