<template>
  <div class="public-pagePaging">
    <el-pagination
      :class="`pagepage-positon-${position}`"
      prev-text="上一页"
      next-text="下一页"
      background
      layout="total, sizes, prev, pager, next,jumper"
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="pageTotal"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'PublicPagePaging',
  props: {
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageTotal: {
      type: Number,
      default: 0
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 40, 50]
    },
    position: {
      type: String,
      default: () => {}
    }
  },

  data () {
    return {

    }
  },

  mounted () {

  },

  methods: {
    handleSizeChange (val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange (val) {
      this.$emit('handleCurrentChange', val)
    }

  }
}
</script>

<style lang="scss" scoped>
  .public-pagePaging {
    margin: 20px;
    text-align: right;
    ::v-deep {

      .el-pagination .el-pagination__total,
      .el-pagination .el-pagination__sizes {
        float: left;
      }
    }
    .pagepage-positon-absolute{
        overflow: auto;
        position: absolute;
        bottom: 0;
        right: 20px;
        left: 20px;
        background-color: #fff;
        padding: 20px;
        z-index: 99;
    }
  }

</style>
