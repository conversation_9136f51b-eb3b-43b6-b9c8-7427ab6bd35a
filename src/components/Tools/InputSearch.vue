<template>
  <div class="autocomplete">
    <el-autocomplete
      width="100"
      ref="elautocomplete"
      v-model="searchValue"
      :maxlength="maxlen"
      :fetch-suggestions="querySearchAsync"
      :placeholder="placeholder"
      @select="handleSelect"
      :clearable="isClear"
      :show-word-limit="false"
      :value-key="valueKey"
      @focus="focusFn"
      :trigger-on-focus="false"
      @blur="blurFn"
      @change="changeFn"
    >
      <div slot="suffix">
        <span v-if="ifshowWordLimit"
          >{{ searchValue.length }}/{{ maxlen }}</span
        >
        <!-- 上下箭头 -->
        <!-- <i class="el-icon-arrow-up" v-if="searchUp"> </i>
        <i class="el-icon-arrow-down" v-else> </i> -->
      </div>
      <el-button
        v-if="searchBtn"
        @click="search"
        slot="append"
        icon="el-icon-search"
      ></el-button>
    </el-autocomplete>
  </div>
</template>
<script>
export default {
  name: "InputSearch",
  data() {
    return {
      timeout: null,
      searchValue: this.value,
      searchUp: false,
    };
  },
  props: {
    //这个value值是可以获取到父组件的v-model值的，可以实现父子组件的双向绑定
    value: {
      type: String,
      default: "",
    },
    // 是否开启重置
    isClear: {
      type: Boolean,
      default: true,
    },
    // 是否开启搜索按钮
    searchBtn: {
      type: Boolean,
      default: false,
    },
    /* 存放数据的数组， */
    options: {
      type: Array,
      default: () => {},
    },
    // 最大值
    maxlen: {
      type: Number,
      default: 100,
    },
    // 默认文案
    placeholder: {
      type: String,
      default: "请输入内容",
    },
    /*开始是想在父组件来传这个值赋值给子组件来实现父子组件双向绑定，
    但是用value父组件就不需要传这个了*/
    defaultVal: {
      type: String,
      default: "",
    },
    // 是否显示字数限制
    ifshowWordLimit: {
      type: Boolean,
      default: false,
    },
    // 默认只能匹配value得键，父组件传键名动态匹配
    valueKey: {
      type: String,
      default: "value",
    },
  },
  watch: {
    searchValue() {
      this.validateTextarea();
    },
    /*在watch中把value赋值给searchValue来实现父子组件双向绑定*/
    value(val) {
      this.searchValue = val;
    },
  },
  methods: {
    focusFn() {
      this.searchUp = true;
    },
    search() {
      this.$emit("search", this.searchValue);
    },
    querySearchAsync(queryString, cb) {
      // 判断是否查询后台的条件
      // if (this.options.filter(this.createStateFilter(queryString)) && this.options.filter(this.createStateFilter(queryString)).length !== 0) {
      let options = this.options;
      let results = queryString
        ? options.filter(this.createStateFilter(queryString, this.valueKey))
        : options;
      if (results && results.length > 0) {
        this.searchUp = true;
      } else {
        this.searchUp = false;
      }
      // clearTimeout(this.timeout);
      // 延时
      // this.timeout = setTimeout(() => {
      cb(results);
      // }, 1000 * Math.random());
      // 处理清空按钮点击清除重新输入时input没有失去焦点导致输入内容不生效
      this.$refs.elautocomplete.handleFocus();
    },
    // 查询输入字符是否存在
    createStateFilter(queryString, valueKey) {
      return (searchValue) => {
        return (
          searchValue[valueKey]
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) !== -1
        );
      };
    },
    blurFn() {
      this.searchUp = false;
      // console.log("blur", this.searchValue);
    },
    handleSelect(item) {
      // 返回的意见点击选择触发事件
      this.searchValue = item[this.valueKey];
      // console.log('blursdfsd', this.searchValue);
      this.$emit("change", this.searchValue);
    },
    // 传值给父组件，并响应父组件input事件
    validateTextarea() {
      try {
        // console.log('input', this.searchValue);
        this.$emit("input", this.searchValue); // 传值给父组件并响应input事件
      } catch (e) {
        // this.$emit('error', true);
      }
    },
    changeFn(item) {
      console.log("change", item);
      this.searchValue = item;
      this.$emit("change", this.searchValue);
    },
  },
};
</script>
<style lang="scss" scoped>
// 处理close样式
.autocomplete {
  ::v-deep .el-autocomplete {
    .el-input--suffix .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
    .el-input__suffix {
      .el-input__suffix-inner {
        height: 32px;
        line-height: 32px;
        border-color: none;
        .el-icon-circle-close:before {
          position: absolute;
          right: 2px;
          top: -3px;
        }
      }
    }
  }
}
// .autocomplete {
//   width: 100%;
//   .el-autocomplete {
//     width: 100%;
//   }
//   ::v-deep .el-input__suffix {
//     line-height: 40px;
//   }
// }
</style>
