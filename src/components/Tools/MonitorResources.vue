<template>
  <div>
    <div class="exhibition">
      <div class="exhibition-box">
        <div id="main-first" style="width: 100%; height: 281px" />
      </div>
      <div class="exhibition-box">
        <div id="main-second" style="width: 100%; height: 281px" />
      </div>
    </div>
    <div v-show="showGpuFlag" class="exhibition">
      <div class="exhibition-box">
        <div id="main-third" style="width: 100%; height: 281px" />
      </div>
      <div class="exhibition-box">
        <div id="main-fourth" style="width: 100%; height: 281px" />
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  name: 'MonitorResources',

  props: {
    // CPU用例
    cpuUtilization: {
      require: false,
      type: Object,
      default: () => {},
    },
    // cpu内存用量
    memoryUsage: {
      require: false,
      type: Object,
      default: () => {},
    },
    // gpu显存用量
    gpuMemoryUsage: {
      require: false,
      type: Object,
      default: () => {},
    },
    // gpu用量
    gpuUtilization: {
      require: false,
      type: Object,
      default: () => {},
    },
    // 是否展示GPU资源
    showGpuFlag: {
      require: false,
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      myChart1: null,
      myChart2: null,
      myChart3: null,
      myChart4: null,
    };
  },

  watch: {
    cpuUtilization: {
      deep: true,
      handler(newV, oldV) {
        this.draw(newV, 'CPU使用量', 'CPU使用率(%)', '1');
      },
    },
    memoryUsage: {
      deep: true,
      handler(newV, oldV) {
        this.draw(newV, '内存用量/率', '内存(G)', '2');
      },
    },
    gpuMemoryUsage: {
      deep: true,
      handler(newV, oldV) {
        this.draw(newV, 'GPU显存用量/率', 'GPU显存(G)', '4');
      },
    },
    gpuUtilization: {
      deep: true,
      handler(newV, oldV) {
        this.draw(newV, 'GPU占用率', 'GPU占用率(%)', '3');
      },
    },
    showGpuFlag: {
      deep: true,
      handler(newV, oldV) {
        if (!oldV && newV) {
          const _this = this;
          this.myChart4 = this.$echarts.init(
            document.getElementById('main-fourth')
          );
          this.myChart3 = this.$echarts.init(
            document.getElementById('main-third'),
            null,
            { renderer: 'svg' }
          );
          setTimeout(function () {
            _this.myChart3?.resize();
            _this.myChart4?.resize();
          }, 200);
        }
      },
    },
  },

  mounted() {
    this.myChart1 && this.myChart1.dispose();
    this.myChart2 && this.myChart2.dispose();
    this.myChart2 = this.$echarts.init(
      document.getElementById('main-second'),
      null,
      { renderer: 'svg' }
    );
    this.myChart1 = this.$echarts.init(
      document.getElementById('main-first'),
      null,
      { renderer: 'svg' }
    );
    if (this.showGpuFlag) {
      this.myChart4 && this.myChart4.dispose();
      this.myChart3 && this.myChart3.dispose();
      this.myChart4 = this.$echarts.init(
        document.getElementById('main-fourth')
      );
      this.myChart3 = this.$echarts.init(document.getElementById('main-third'));
    }

    this.drawLine();
  },

  methods: {
    downTimeDraw(val) {
      this.$emit('downTimeDraw', val);
    },
    upTimeDraw(val) {
      this.$emit('upTimeDraw', val);
    },
    // beforeDestroy () {
    //   window.removeEventListener('resize', () => { this.myChart1 })

    //   this.myChart1 = null
    //   this.myChart2 = null
    // },

    draw(newV, text, yText, type) {
      const limit = [];
      const dataY3 = [];

      const valY = newV.usage.map((el) => {
        if (el.value || el.value === 0) {
          limit.push({
            value: newV.limit,
          });
          let rate = ((Number(el.value) / Number(newV.limit)) * 100).toFixed(2);
          if (Number.isInteger((Number(el.value) / Number(newV.limit)) * 100)) {
            rate = (Number(el.value) / Number(newV.limit)) * 100;
          }
          if (newV.limit === 0) rate = 0;
          dataY3.push(rate);
        } else {
          limit.push({
            value: null,
          });
          dataY3.push(null);
        }
        return {
          value: el.value,
        };
      });

      const valX = newV.usage.map((el) => {
        return {
          value: el.time,
        };
      });

      let yMax = newV.limit ? newV.limit : 0;
      if (newV.usage) {
        newV.usage.forEach(function (item) {
          if (item.value > yMax) {
            yMax = item.value;
          }
        });
      } else {
        yMax = 1;
      }
      const maxVal = Math.max(...dataY3);
      let yRightMax = maxVal;
      if (maxVal < 50) {
        yRightMax = maxVal * 2;
      } else if (maxVal < 80) {
        yRightMax = maxVal + 20;
      }

      this.drawLine(
        valX,
        valY,
        limit,
        null,
        text,
        yText,
        yMax,
        type,
        dataY3,
        yRightMax
      );
    },
    drawLine(
      dataX,
      dataY1,
      dataY2,
      name,
      text,
      yText,
      yMax,
      type,
      dataY3,
      yRightMax
    ) {
      if (!dataX) {
        return;
      }
      const interval =
        parseInt(dataX.length / 6) > 1 ? parseInt(dataX.length / 6) : null;
      try {
        window.addEventListener('resize', () => {
          this.myChart1?.resize();
          this.myChart2?.resize();
          if (this.myChart3) {
            this.myChart3.resize();
          }
          if (this.myChart4) {
            this.myChart4.resize();
          }
        });
        const optionByCallTime = {
          title: {
            text: text,
          },
          xAxis: {
            showMinLabel: true,
            showMaxLabel: true,
            type: 'category',
            name: '时间',
            data: dataX,
            nameLocation: 'end',
            nameGap: 30,

            nameTextStyle: {
              fontSize: 12,
            },
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              interval: interval,
              rotate: 45,
            },
          },
          dataZoom: [
            {
              type: 'slider',
              filterMode: 'none',
              show: true,
              handleSize: 8,
              showDetail: false,
              height: 12,
              bottom: 10,
            },
          ],

          tooltip: {
            trigger: 'axis',
          },
          yAxis: [
            {
              type: 'value',
              name: yText,
              min: 0,
              max: yMax,
              axisLine: {
                show: false,
              },
              axisLabel: {
                show: true,
                interval: 'auto',
              },
            },
          ],
          series: [
            {
              data: dataY2,
              name: '申请内存',
              type: 'line',
              showSymbol: false,
              areaStyle: {},
            },
            {
              data: dataY1,
              name: '当前用量',
              type: 'line',
              showSymbol: false,
              areaStyle: {
                // color: 'black'
                // focus: 'series',
                // blurScope: 'coordinateSystem'
              },
            },
            {
              data: dataY3,
              name: '使用率',
              type: 'line',
              showSymbol: false,
              lineStyle: {
                type: 'dashed',
              },
              // areaStyle: {

              // },
            },
          ],
          legend: [
            {
              show: true,

              itemWidth: 10,
              itemHeight: 10,
            },
            // {
            //   show: true,
            //   data: ['上限'],
            //   itemWidth: 10,
            //   itemHeight: 10
            // }
          ],
        };

        optionByCallTime.tooltip.formatter = (val) => {
          val[0].name = this.$moment(+(val[0].name + '000')).format(
            'YYYY-MM-DD HH:mm:ss'
          );
          let output = val[0].name;
          val.forEach((el, index) => {
            if (el.seriesName === '使用率' && el.value) el.value += '%';
            if (el.seriesName === '当前用量' && type === '3' && el.value) {
              el.value += '%';
            }

            output +=
              '<br>' +
              el.marker +
              el.seriesName +
              '：' +
              (el.value || el.value === 0 ? el.value : '');
          });

          return output;
        };
        // optionByCallTime.dataZoom
        optionByCallTime.xAxis.axisLabel.formatter = (val) => {
          return moment(+(val + '000')).format('HH:mm');
        };
        if (type !== '3' && type !== '1') {
          const rightY = {
            type: 'value',
            name: '百分比',
            min: 0,
            max: 100,
            axisLine: {
              show: false,
            },

            axisLabel: {
              show: true,
              interval: 'auto',
            },
          };

          optionByCallTime.yAxis.push(rightY);
          optionByCallTime.yAxis[1].axisLabel.formatter = '{value}';
          optionByCallTime.series[2].yAxisIndex = 1;
        }

        if (type === '1') {
          optionByCallTime.yAxis[0].max = 100;
          delete optionByCallTime.series[0];
          delete optionByCallTime.series[1];
          optionByCallTime.series[2].areaStyle = {};

          optionByCallTime.legend[0].data = ['使用率'];

          this.myChart1.setOption(optionByCallTime);
        } else if (type === '2') {
          optionByCallTime.yAxis[1].name = '内存使用量(%)';
          optionByCallTime.series[1].name = '当前使用内存';
          // optionByCallTime.legend[0].data = ['当前使用内存', '申请内存', '使用率']
          this.myChart2.setOption(optionByCallTime, true);
        } else if (type === '3' && this.myChart3) {
          optionByCallTime.yAxis[0].max = 100;
          delete optionByCallTime.series[0];
          delete optionByCallTime.series[1];
          optionByCallTime.series[2].areaStyle = {};
          optionByCallTime.legend[0].data = ['使用率'];
          this.myChart3.setOption(optionByCallTime, true);
        } else if (type === '4' && this.myChart4) {
          optionByCallTime.yAxis[1].name = 'GPU显存使用量(%)';
          optionByCallTime.series[1].name = '当前使用显存';
          optionByCallTime.series[0].name = '申请显存';
          optionByCallTime.legend[0].data = [
            '申请显存',
            '当前使用显存',
            '使用率',
          ];
          this.myChart4.setOption(optionByCallTime, true);
        }
      } catch (error) {
        throw new Error(error, 'drawline');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.exhibition {
  display: flex;
  .exhibition-box {
    border: 1px solid #eaebf0;
    padding: 10px;
    margin: 5px 10px;
    width: 50%;
    position: relative;
    .exhibition-arrow-left {
      cursor: pointer;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translate(-18%, -50%);
      z-index: 999;
    }
    .exhibition-arrow-right {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(-10%, -50%);
      z-index: 999;
    }
  }
}
</style>
