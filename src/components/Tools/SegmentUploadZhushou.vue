<template>
  <div style="display:flex;align-items: center;width: 100%">
    <div style="flex:1;max-width:100%">
      <el-upload
        ref="uploadFile"
        class="upload-demo"
        style="display: flex;"
        :auto-upload="true"
        :multiple="multiple"
        :accept="accept"
        :limit="numLimit"
        :list-type="listType"
        :file-list="fileList"
        :show-file-list="showFileList"
        :data="uploadParam"
        :action="uploadUrl"
        :on-change="modelChangeHandle"
        :on-error="modelUploadError"
        :before-upload="modelBeforeUploadHandle"
        :http-request="fnUploadRequest"
        :before-remove="beforeRemove"
      >
        <el-button
          slot="trigger"
          size="small"
          :loading="inProcess"
        >
          <i class="el-icon-upload el-icon--right" />选取文件
        </el-button>
  
        <!-- <el-form :model="data" /> -->
        <!-- <el-progress
          v-show="inProcess"
          style="width: 80%;margin-top: 8px;"
          :text-inside="true"
          :stroke-width="16"
          :percentage="progressPercent"
        /> -->
      </el-upload>
    </div>
    <div
      v-if="uploadTip"
      style="line-height: 20px;color:#969799;font-size: 12px;word-break: keep-all;"
    >
      {{ uploadTip }}
    </div>
  </div>
  </template>
  <script type="text/javascript">
  import innerAxios from 'axios'
  import ObsClient from 'esdk-obs-browserjs/src/obs'
  const CancelToken = innerAxios.CancelToken
  
  let source = CancelToken.source()
  let blob
  const progressLoad = 0
  export default {
    components: {
      /**
       * @description: 自定义组件注册。
       */
    },
    props: {
      listType: {
        type: String,
        default: 'text'
      },
      showFileList: {
        type: Boolean,
        default: true
      },
      /**
       * @description: 可以是数组或对象，用于接收来自父组件的数据。
       */
      multiple: {
        type: Boolean,
        default: true
      },
      limit: {
        type: Number,
        default: 1024
      },
      numLimit: {
        type: Number
      },
      accept: {
        type: String,
        default: ''
      },
      // 由于模型文件存在无文件类型的状态
      // 所以该字段用在before-upload时机用来校验
      accept_v2: {
        type: String,
        default: ''
      },
      uploadTip: {
        type: String,
        default: '支持任意文件'
      },
      defaultFileList: {
        type: Array,
        default: () => {
          return []
        }
      },
      cover: {
        type: Boolean,
        default: false
      },
      shardLimit: {
        type: Number,
        default: 10
      },
      indexRef: {
        type: Number,
        default: 0
      }
    },
    data () {
      /**
       * @description: 当前页面的数据对象，Vue 将会递归将 data 的 property 转换为 getter/setter，从而让 data 的 property 能够响应数据变化。
       * @param void
       * @return {Object}
       */
      return {
        // progressPercent: 0,
        fileCount: 1,
        uploadUrl: '',
        uploadParam: {},
        uploadParamsInfos: new Map(),
        fileList: [],
        inProcess: false,
        count: 0,
        obsClient: null,
        parts: [],
        partSize: 200 * 1024 * 1024, //切片大小为500M
        lastPartSize: 0 //最后一片大小
      }
    },
    computed: {
      /**
       * @description: 计算属性将被混入到当前Vue实例中。所有 getter 和 setter 的 this 上下文自动地绑定为 Vue 实例。
       */
    },
    watch: {
      /**
       * @description: 监听一个对象，键是需要观察的表达式，值是对应回调函数。值也可以是方法名，或者包含选项的对象。
       */
      defaultFileList: {
        deep: true,
        immediate: true,
        handler (nv) {
          if (nv.length) {
            this.fileList = nv.map(itm => itm)
          }
        }
      }
    },
    created () {
      /**
       * @description: 在实例创建完成后被立即调用。
       * @param void
       * @return void
       */
    },
    mounted () {
      /**
       * @description: 实例被挂载后调用。
       * @param void
       * @return void
       */
    },
    methods: {
      
  
      /**
       * @description: methods 将被混入到 Vue 实例中。可以直接通过 VM 实例访问这些方法，或者在指令表达式中使用。方法中的 this 自动绑定为 Vue 实例。
       */
      // 更新上传参数
      setUploadParamInfo (signData, file) {
        this.uploadParamsInfos.set(file.uid, new Object())
        this.uploadParamsInfos.get(file.uid).fileInfo = {
          name: file.name,
          size: file.size,
          uid: file.uid
        }
        this.uploadParamsInfos.get(file.uid).uploadParam = {
            objectKey: signData.objectKey,
            uploadId: signData.uploadId // 任务号
        }
      },
      clearFiles () {
        this.uploadParamsInfos = new Map()
        this.fileList = []
        this.$refs.uploadFile.clearFiles()
      },
      // 上传初始化获取初始化信息
      getFileSign (file, resolve, reject) {
        const _this = this
        console.log('getFileSign ==== ')
        try {
            const fileType = this.$fileUtil.getFileSuffix(file.name)
          this.$axios.post(`/obsfs/commonFile/multipartTask/init/${fileType}`, {}).then((res) => {
            if (res.status === 200 && res.data && res.data.status === 200) {
              // 根据签名服务的返回值拼接文件上传url
              _this.setUploadParamInfo(res.data.data, file)
              resolve(true)
            } else {
              reject(false)
            }
          }).catch((err) => {
            console.log(err)
            reject(false)
          })
        } catch (e) {
          this.$message.error('获取签名出错！')
          reject(false)
        }
      },
      // 保存文件信息
      async saveFileInfo (file, _self, fileKey) {
        const fileName = file.name
        const fileSize = file.size / 1024
        const fileType = this.$fileUtil.getFileSuffix(file.name)
        this.$axios.post('/file/add', {
          fileKey: fileKey,
          fileName: fileName,
          fileSize: fileSize,
          fileType: fileType,
          open: 0,
          storagePlatform: 'Obs'
        }).then((res) => {
          if (res.data.status === 200) {
            const fileId = res.data.data.fileId
            const filePath = res.data.data.path
  
            _self.uploadParamsInfos.get(file.uid).fileInfo.fileId = fileId
  
            const uploadFiles = []
  
            _self.uploadParamsInfos.forEach(function (item, key) {
              uploadFiles.push(item.fileInfo)
            })
  
            console.log('UploaderTool onFilesChange ====')
            this.$emit('onFilesStatus', this.inProcess, this.indexRef)
            _self.$emit('onFilesChange', {
              name: file.name,
              size: file.size,
              fileId: fileId,
              uid: file.uid,
              path: filePath
            }, uploadFiles, this.indexRef)
          }
        }).catch((e) => {
  
        })
      },
      // 合并段
      async completeUpload (file, _self) {
        const params = _self.uploadParamsInfos.get(file.uid).uploadParam
        _self.$axios.post('/obsfs/commonFile/multipartTask/merge', params).then((res) => {
          if (res.data.status === 200) {
            _self.saveFileInfo(file,_self, res.data.data.objectKey)
          }
        }).catch((e) => {
  
        })
      },
      handleRemove (file, fileList) {
        this.fileList = []
      },
  
      // #region el-upload 事件方法
  
      // 自定义上传方法
      async fnUploadRequest (option) {
        const _this = this
        try {
          console.log('fnUploadRequest =====', this.fileCount)
          const file = option.file
          const current = this.uploadParamsInfos.get(file.uid)
          var arr = Array.from({length:this.fileCount}, (v,k) => k);
          let parts = Array.from({length:_this.fileCount}, (v,k) => k);
          this.inProcess = true
          this.$emit('onFilesStatus', this.inProcess, this.indexRef)
          if (arr.length > 0) {
            _this.parts = []
            let pros = []
            arr.forEach(async (item) => {
              const cutfile = file.slice(item * _this.partSize, (item + 1) * _this.partSize)
              const init1 = _this.getFile(cutfile, current, item+1, _this)
              pros.push(init1)
            })
            Promise.all(pros).then(() => {
              console.log('全部片段上传完成===', _this.parts);
              _this.parts.forEach((item) => {
                  parts[item.PartNumber-1] = item
              })
              // 在这里写下执行完所有Promise后需要执行的代码
              console.log('开始合并complete multipart upload \n');
              _this.completeUpload(file, _this)
              _this.inProcess = false
            }).catch((error) => {
              console.log('Promise rejected:', error);
            });
        }
        } catch (error) {
          this.inProcess = false
          console.error(error)
          option.onError('上传失败')
          this.$error(error.message)
        }
      },
      modelBeforeUploadHandle (file) {
        const _this = this
        let isLtType = false
        const lastPartSize = file.size % this.partSize;
        // 段数量
        this.fileCount = Math.ceil(file.size / this.partSize);
        this.lastPartSize = lastPartSize
        console.log('切分段数',this.fileCount) // 分几个文件
        if (this.accept_v2) {
          this.accept_v2.split(',').forEach(function (item) {
            if (file.name.endsWith(item)) {
              isLtType = true
            }
            if (file.name.indexOf('.') == -1 && item == '无类型') {
              isLtType = true
            }
          })
  
          if (!isLtType) {
            this.$message.error('只能上传"' + this.accept_v2 + '"的文件')
          }
        } else {
          isLtType = true
        }
        const isLt2M = file.size / 1024 / 1024 < this.limit
        if (!isLt2M) {
          this.$message.error('大小不能超过' + this.foramtBytes(this.limit))
        }
        console.log(isLtType && isLt2M)
        if (isLtType && isLt2M) {
          return new Promise(
            function (resolve, reject) {
              // 一段耗时的异步操作
              _this.getFileSign(file, resolve, reject)
            }).then(
            (res) => { console.log(res) },
            (err) => {
              console.log(err)
              return false
            }
          )
        } else {
          return false
        }
      },
  
      // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
      modelChangeHandle (file, fileList) {
        if (this.cover) {
          if (fileList.length > 1) {
            fileList.splice(0, 1);
            this.fileList = fileList
          }
        }
        this.$emit('on-change', file, fileList)
      },
      foramtBytes (size) {
        const G = 1024 // or 1024
        if (size < G) { return size + 'M' }
        if (size >= G) { return (size / G).toFixed(0) + 'G' }
      },
      // 文件上传失败时的钩子
      modelUploadError (err, file, fileList) {
        this.uploadParamsInfos = new Map()
        this.$emit('on-error', file, fileList)
        this.$message.warning('上传失败')
      },
  
      beforeRemove (file, fileList) {
        source.cancel('取消')
        source = CancelToken.source()
        this.inProcess = false
        const current = this.uploadParamsInfos.get(file.uid)
        if (current) {
          const fileId = current.fileInfo.fileId
          this.uploadParamsInfos.delete(file.uid)
          this.$emit('on-remove', file, fileList, this.indexRef)
          const uploadFiles = []
          this.count = 0
          this.uploadParamsInfos.forEach(function (item, key) {
            uploadFiles.push(item.fileInfo)
          })
          this.$emit('onFilesStatus', this.inProcess, this.indexRef)
          console.log('UploaderTool beforeRemove onFilesChange ====')
          this.$emit('onFilesChange', {
            name: file.name,
            size: file.size,
            uid: file.uid,
            fileId: fileId
          }, uploadFiles)
        }
      },
      // 保存文件信息
      async togeterAllFile (file) {
        const params = this.uploadParamsInfos.get(file.uid).uploadParam
        this.$axios.post('/obsfs/completeMultipartUpload', {
          "bucketName": "桶名",
          "objectKey": "key",
          "uploadId": params.uploadId,
          "partEtag": [
              {
                  "partNumber": "上传后返回的分段号",
                  "eTag": "分段ETag值"
              }
          ]
        }).then((res) => {
          if (res.data.status === 200) {
            _self.uploadParamsInfos.get(file.uid).fileInfo.objectKey = res.data.data.objectKey
            _self.saveFileInfo(file,_self)
          }
        }).catch((e) => {
  
        })
      },
      blobToArrayBuffer(blob) {
        let reader = new FileReader();
        reader.onload = function() {
            return this.result;
        }
        reader.readAsArrayBuffer(blob);
    },

      async getFile (file, current, partNumber, _self) {
        return new Promise((resolve, reject) => {
          const params = current.uploadParam
          // 获取上传临时url
          _self.$axios.post('/obsfs/commonFile/multipartTask/segmentUpload', {
            objectKey: params.objectKey,
            partNumber: partNumber,
            uploadId: params.uploadId
          }).then((res) => {
            if (res.status === 200 && res.data && res.data.status === 200) {
              const reader = new FileReader()
              reader.readAsArrayBuffer(file)
              reader.onload = (e) => {
                const uint8Array = new Uint8Array(reader.result); 
                innerAxios.put(res.data.data, uint8Array, {
                    headers: {
                      'Content-Type': '' // 这里一定要设置为空
                    }
                }).then(() => {
                    resolve(true)
                })
              } 
          }
          }).catch((err) => {
              console.log(err)
              reject(false)
          })
        })
      }
      // #endregion
    }
  }
  </script>
  <style lang="scss" scoped>
  ::v-deep .is-success .el-upload-list__item-status-label{
    display:none;
  }
  ::v-deep .is-success.el-upload-list__item:hover{
    .el-upload-list__item-status-label{
      display: none;
    }
  }
  </style>
  