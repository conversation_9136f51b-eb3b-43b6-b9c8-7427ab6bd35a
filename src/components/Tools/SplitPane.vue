<template>
  <div
    ref="splitPane"
    class="split-pane"
    :class="direction"
    :style="{ flexDirection: direction }"
  >
    <div
      class="pane pane-one"
      :style="lengthType + ':' + paneLengthValue"
    >
      <slot name="one" />
    </div>

    <div
      class="pane-trigger"
      :style="lengthType + ':' + triggerLengthValue"
      @mousedown="handleMouseDown"
    >
      <div class="el-two-column__icon-top">
        <div class="el-two-column__icon-top-bar" />
      </div>
      <i class="el-icon el-two-column__trigger-icon"><svg
        t="1646619990346"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="3814"
        width="200"
        height="200"
      >
        <path
          d="M543.84 640.032h96v96h-96v-96z m-160 0h96v96h-96v-96z m0.576-176.608h96v96h-96v-96z m160 0h96v96h-96v-96z m-160.576-175.36h96v96h-96v-96z m160 0h96v96h-96v-96zM96.864 511.648L268.8 304.448v415.104zM928 511.648l-171.936-207.2v415.104z"
          fill="currentColor"
          p-id="3815"
        /></svg></i>
      <div class="el-two-column__icon-bottom">
        <div class="el-two-column__icon-bottom-bar" />
      </div>
    </div>

    <div class="pane pane-two">
      <slot name="two" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    direction: {
      type: String,
      default: 'row'
    },

    min: {
      type: Number,
      default: 10
    },

    max: {
      type: Number,
      default: 90
    },

    paneLengthPercent: {
      type: Number,
      default: 50
    },

    triggerLength: {
      type: Number,
      default: 10
    }
  },
  data () {
    return {
      triggerLeftOffset: 0 // 鼠标距滑动器左(顶)侧偏移量
    }
  },
  computed: {
    lengthType () {
      return this.direction === 'row' ? 'width' : 'height'
    },

    paneLengthValue () {
      return `calc(${this.paneLengthPercent}% - ${this.triggerLength / 2 + 'px'})`
    },

    triggerLengthValue () {
      return this.triggerLength + 'px'
    }
  },

  methods: {
    // 按下滑动器
    handleMouseDown (e) {
      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)

      if (this.direction === 'row') {
        this.triggerLeftOffset = e.pageX - e.srcElement.getBoundingClientRect().left
      } else {
        this.triggerLeftOffset = e.pageY - e.srcElement.getBoundingClientRect().top
      }
    },

    // 按下滑动器后移动鼠标
    handleMouseMove (e) {
      const clientRect = this.$refs.splitPane.getBoundingClientRect()
      let paneLengthPercent = 0

      if (this.direction === 'row') {
        const offset =
          e.pageX - clientRect.left - this.triggerLeftOffset + this.triggerLength / 2
        paneLengthPercent = (offset / clientRect.width) * 100
      } else {
        const offset =
          e.pageY - clientRect.top - this.triggerLeftOffset + this.triggerLength / 2
        paneLengthPercent = (offset / clientRect.height) * 100
      }

      if (paneLengthPercent < this.min) {
        paneLengthPercent = this.min
      }
      if (paneLengthPercent > this.max) {
        paneLengthPercent = this.max
      }

      this.$emit('update:paneLengthPercent', paneLengthPercent)
    },

    // 松开滑动器
    handleMouseUp () {
      document.removeEventListener('mousemove', this.handleMouseMove)
    }
  }
}
</script>

<style scoped lang="scss">
.split-pane {
  height: 100%;
  display: flex;
  &.row {
    .pane {
      height: auto;
      min-width:100px;
    }
    .pane-trigger {
      background-color: #eff3ff;
      width: 10px;
      color: #c3cadd;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: auto;
      margin-top:2%;
      cursor: col-resize;
      &:hover {
        background: #e0e6ff;
        .el-two-column__trigger-icon {
          color: #3455ad;
        }
      }
      .el-two-column__icon-top {
        height: 50%;
        width: 4px;
        display: flex;
        flex-direction: column-reverse;
        .el-two-column__icon-top-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
        }
      }
      .el-icon {
        --color: inherit;
        height: 1em;
        width: 1em;
        line-height: 1em;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        position: relative;
        fill: currentColor;
        color: var(--color);
        font-size: inherit;
      }
      .el-two-column__trigger-icon {
        width: 25px;
        height: 25px;
      }

      .el-two-column__icon-bottom {
        height: 50%;
        width: 4px;
        .el-two-column__icon-bottom-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
        }
      }
    }
  }
  &.column {
    .pane {
      width: 100%;
    }
    .pane-trigger {
      width: 100%;
      cursor: row-resize;
    }
  }
  .pane-one {

  }
  .pane-trigger {
    user-select: none;

  }
  .pane-two {
    flex: 1;

  }
}
</style>
