<template>
  <el-dialog :title="modalTitle" :visible.sync="dialogVisible" width="60%"
    :before-close="handleClose" custom-class="dialog" @open="handleOpenDialog">
    <!-- 主题内容 -->
    <div class="main">
      <div class="space-info">
        <span>工作空间：{{ currentWorkSpace.workspaceName }}</span>
        <span>管理者：{{ currentWorkSpace.managerName }}</span>
      </div>
      <el-row class="team">
        <el-col :span="9" :offset="2">
          <div class="list">
            <div class="list-head">
              待选成员
            </div>
            <div class="list-con">
              <div class="list-con-search">
                <el-input v-model="cs.leftSearchText" placeholder="请输入内容">
                  <i slot="suffix" class="el-input__icon el-icon-search" />
                </el-input>
              </div>
              <div class="list-con-option">
                <el-checkbox v-show="cs.leftSearchList.length > 0" v-model="leftAllCheckStatus"
                  @change="allCheckLeftControl">
                  全选
                </el-checkbox>
                <el-checkbox-group v-if="!loading" v-model="cs.checkedLeftSearchList"
                  @change="handleLeftSearchListChange">
                  <el-checkbox v-for="left in cs.leftSearchList" :key="left.allowedUserId"
                    :disabled="left.allowedUserId===userId" :label="left.allowedUserId">
                    {{ `${left.allowedUsername}`
                    }}<span style="color: #c6cacd">{{ ` (${left.allowedLoginName})` }}</span>
                  </el-checkbox>
                </el-checkbox-group>
                <span v-else>加载中···</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="2">
          <div class="center-btn">
            <div class="btns">
              <i class="btn el-icon-arrow-right" @click="leftToRight" />
              <i class="btn el-icon-arrow-left" @click="rightToLeft" />
            </div>
          </div>
        </el-col>
        <el-col :span="9">
          <div class="list">
            <div class="list-head">
              已选成员
            </div>
            <div class="list-con">
              <div class="list-con-search">
                <el-input v-model="cs.rightSearchText" placeholder="请输入内容"
                  @change="rightSearchHandler">
                  <i slot="suffix" class="el-input__icon el-icon-search" />
                </el-input>
              </div>
              <div class="list-con-option">
                <el-checkbox v-show="cs.rightSearchList.length > 0" v-model="rightAllCheckStatus"
                  @change="allCheckRightControl">
                  全选
                </el-checkbox>
                <el-checkbox-group v-model="cs.checkedRightSearchList"
                  @change="handleRightSearchListChange">
                  <el-checkbox v-for="right in filterRightSearchList" :key="right.allowedUserId"
                    :label="right.allowedUserId">
                    {{ `${right.allowedUsername}`
                    }}<span
                      style="color: #c6cacd">{{ ` ${right.allowedLoginName!==undefined?'('+right.allowedLoginName+')':''}` }}</span>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitModal">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { debounce } from '@/utils/util.js';
export default {
  props: {
    modalTitle: {
      type: String,
      default: '成员配置',
      required: false,
    },
    currAlgorithmProjectId: {
      type: [String, Number],
      default: '',
    },
    shareUsers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      userId: sessionStorage.getItem('userId'),
      currentWorkSpace: this.$store.state.workSpace.currentWorkSpace,
      selectList: [],
      loading: false,
      submitLoading: false,
      // 自定义穿梭框
      cs: {
        leftSearchText: '',
        leftSearchList: [],
        checkedLeftSearchList: [],
        rightSearchText: '',
        rightSearchList: [],
        checkedRightSearchList: [],
      },
    };
  },
  computed: {
    filterRightSearchList() {
      const txt = this.cs.rightSearchText;
      return this.cs.rightSearchList.filter(
        (item) =>
          item.allowedUsername.indexOf(txt) > -1 ||
          item.allowedLoginName?.toUpperCase().indexOf(txt.toUpperCase()) > -1
      );
    },
    leftAllCheckStatus: {
      get() {
        return (
          this.cs.leftSearchList.length === this.cs.checkedLeftSearchList.length
        );
      },
      set() {},
    },
    rightAllCheckStatus: {
      get() {
        return (
          this.filterRightSearchList.length ===
            this.cs.checkedRightSearchList.length &&
          this.cs.checkedRightSearchList.length !== 0
        );
      },
      set() {},
    },
  },
  watch: {
    'cs.leftSearchText': debounce(function (n, o) {
      console.log('w: ', n, ';o:', o);
      if (n === '') {
        this.cs.leftSearchList = [];
        this.cs.checkedLeftSearchList = [];
      } else {
        if (!this.loading && n) {
          this.loading = true;
          this.getUserList(n);
        }
      }
    }, 1e3),
    dialogVisible: function (n, o) {
      if (!n) {
        const data = {
          leftSearchText: '',
          leftSearchList: [],
          checkedLeftSearchList: [],
          rightSearchText: '',
          rightSearchList: [],
          checkedRightSearchList: [],
        };
        Object.assign(this.cs, data);
      }
    },
  },
  methods: {
    // 全选框
    allCheckLeftControl(val) {
      this.cs.checkedLeftSearchList = val
        ? this.cs.leftSearchList.map((el) => {
            if (el.allowedUserId !== this.userId) {
              return el.allowedUserId;
            }
          })
        : [];
    },
    handleOpenDialog() {
      this.cs.rightSearchList = this.shareUsers;
    },
    allCheckRightControl(val) {
      this.cs.checkedRightSearchList = val
        ? this.filterRightSearchList.map((el) => {
            return el.allowedUserId;
          })
        : [];
    },
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
    },
    // 开启弹窗
    handleOpen() {
      this.dialogVisible = true;
    },
    // 搜索
    // leftSearchHandler(val) {
    //   console.log(111, val)
    //   if (!this.loading && val) {
    //     this.loading = true
    //     this.getUserList(val)
    //   }
    // },
    rightSearchHandler(val) {
      console.log(val);
    },
    // 组选中切换
    handleLeftSearchListChange(arr) {
      console.log(arr);

      this.cs.checkedLeftSearchList = arr;
    },
    handleRightSearchListChange(arr) {
      console.log(arr);

      this.cs.checkedRightSearchList = arr;
    },
    // 转移
    leftToRight() {
      const arr = [];
      this.cs.leftSearchList.map((el) => {
        if (this.cs.checkedLeftSearchList.indexOf(el.allowedUserId) > -1) {
          if (
            !this.cs.rightSearchList.some(
              (item) => item.allowedUserId === el.allowedUserId
            )
          ) {
            arr.push(el);
          }
        }
      });
      this.cs.rightSearchList = [...this.cs.rightSearchList, ...arr];
    },
    // 右边到左边直接删除即可
    rightToLeft() {
      const arr = [];
      this.cs.rightSearchList.map((el) => {
        if (this.cs.checkedRightSearchList.indexOf(el.allowedUserId) === -1) {
          arr.push(el);
        }
      });

      this.cs.checkedRightSearchList = [];
      this.cs.rightSearchList = arr;
    },
    // 获取用户列表
    async getUserList(name) {
      const vm = this;
      vm.cs.checkedLeftSearchList = [];
      const { data } = await vm.$axios.post(
        `${vm.baseUrl}/algResource/permission/allowToPublishUserList`,
        {
          algorithmProjectId:
            this.$route.query.algorithmProjectId || this.currAlgorithmProjectId,
          // projectResourceBizId: '如果是算法服务，则传入算法服务id => serviceId',
          // resourceOwnerId: '算法服务的创建人, 可以从算法服务详情中获取',
          username: name,
        }
      );
      vm.loading = false;
      if (data.status === 200) {
        // vm.cs.leftSearchList = data.data
        const arr = [];
        data.data.forEach((el) => {
          // console.log(el,vm.cs.leftSearchText,'vm.cs.leftSearchText')
          if (
            el.allowedUsername.indexOf(vm.cs.leftSearchText) >= 0 ||
            el.allowedLoginName
              .toLowerCase()
              .indexOf(vm.cs.leftSearchText.toLowerCase()) >= 0
          ) {
            arr.push(el);
          }
        });
        vm.cs.leftSearchList = arr;

        console.log(sessionStorage.getItem('userId'), 'id');
      }
    },
    // 提交
    async submitModal() {
      // if (this.cs.rightSearchList.length === 0) {
      //   this.$message.warning('未选择分享的成员名单，请选择后提交。')
      //   return
      // }

      this.$emit('shareMemberList', this.cs.rightSearchList);

      this.handleClose();
    },
  },
};
</script>
  <style lang="sass">
.main
  .space-info
    background-color: #f5f7fa
    padding: 10px
    border-radius: 2px
    display: flex
    justify-content: space-between

    .team
      padding: 20px 0
      .list
        height: 380px
        border: solid 1px #e1e2e4
        box-sizing: border-box
        &-head
          padding-left: 12px
          line-height: 40px
          border-bottom: solid 1px #e1e2e4
          font-weight: bold

          &-con
            padding: 12px

            &-option
              margin-top: 10px
              height: 274px
              overflow-y: auto
              overflow-x: hidden
              padding: 0 12px
              .el-checkbox
                display: block
                padding: 2px 0
                margin-right: 0
                .el-checkbox__label
                  overflow: hidden
                  white-space: nowrap
                  text-overflow: ellipsis
                  width: 100%
                  vertical-align: middle

                &:hover
                  background-color: #f5f7fa

        .center-btn
          width: 100%
          height: 380px
          display: flex
          justify-content: center
          align-items: center
          .btns
            width: 26px
            height: 120px
            .btn
              padding: 10px 6px
              color: #0f55fa
              background-color: #f5f7fa
              cursor: pointer
              &:first-child
                margin-bottom: 10px

              &:hover
                color: #f5f7fa
                background-color: #0f55fa

    .dialog
      .el-dialog__title
        font-size: 16px
      .el-dialog__body
        padding: 0 20px 10px
</style>
