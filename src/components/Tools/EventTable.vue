<template>
  <div style="position:relative">
    <div class="TableBox">
      <el-table
        :header-cell-style="{
          background: '#F0F4FF',
          'font-size': '14px',
          color: '#333333',
          height: '46px',
          padding: '0',
        }"
        height="400"
        :data="tableList"
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'descending' }"
      >
        <el-table-column
          show-overflow-tooltip
          prop="eventId"
          label="事件id"
          min-width="200"
        />
        <el-table-column
          align="center"
          min-width="80"
          prop="eventType"
          label="事件类型"
        />
        <el-table-column
          min-width="100"
          prop="reason"
          label="事件原因"
        />
        <el-table-column
          min-width="200"
          prop="message"
          label="事件信息"
        />
        <el-table-column
          min-width="100"
          prop="occurTime"
          label="时间"
        />
      </el-table>
    </div>
    <div class="PagePaging">
      <el-pagination
        prev-text="上一页"
        next-text="下一页"
        background
        layout="total, sizes, prev, pager, next,jumper"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventTable',

  props: {
    tableList: {
      type: Array,
      default: () => {}
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    }
  },

  data () {
    return {

    }
  },
  mounted () {

  },

  methods: {
    handleSizeChange (val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange (val) {
      this.$emit('handleCurrentChange', val)
    }

  }
}
</script>

<style lang="scss" scoped>
.TableBox {
    padding: 20px;
    padding-bottom:80px;
    overflow-y: auto;
    .el-button--small {
      font-size: 14px;
    }
    .limit-table {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .table-text {
      width: 80%;
    }
    .table-btn {
      color: #148ae4;
      cursor: pointer;
    }
  }

  .PagePaging {
    margin: 20px;
    text-align: right;
    ::v-deep {
      .el-pagination{
    position: absolute;
    bottom: 0;
    right: 40px;
    left: 20px;
    background-color: #fff;
    z-index: 99;
      }
      .el-pagination .el-pagination__total,
      .el-pagination .el-pagination__sizes {
        float: left;
      }
    }
  }

</style>
