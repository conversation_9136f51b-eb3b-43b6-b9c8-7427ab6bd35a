<template>
  <el-select
    v-model="teamId"
    class="image-select"
    :placeholder="placeholder"
    @change="getVal"
  >
    <el-option
      v-for="item in teams"
      :key="item.id"
      :label="item.teamName"
      :value="item.id"
    />
  </el-select>
</template>
<script>
export default {
  name: 'TeamSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择团队'
    },
    selectedId: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      teams: {},
      teamId: null
    }
  },
  watch: {
    /// 、阿达
    selectedId: {
      immediate: true,
      handler (val) {
        this.teamId = val
      }
    }
  },
  created () {
    this.initTeam()
    // addjajdakjdak
  },
  methods: {
    initTeam () {
      this.$post('/common/getTeam', {})
        .then(data => {
          this.teams = data
        })
    },
    getVal () {
      const _this = this
      let name = ''
      this.teams.forEach(function (item, index) {
        if (item.id == _this.teamId) {
          name = item.teamName
        }
      })
      this.$emit('func', this.teamId, name)
    }
  }
}
</script>
