<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="40%"
    center
    :before-close="cancelHandler"
  >
    <slot name="content"></slot>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancelHandler">取 消</el-button>
      <el-button type="primary" @click="okHandler">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      title: '温馨提示',
      visible: false,
    }
  },
  created() {},
  methods: {
    // 调用父传来的确定函数 然后关闭自己
    okHandler() {
      this.$emit('okHandler')
      this.visible = false
    },
    cancelHandler() {
      this.$emit('cancelHandler')
      this.visible = false
    },
  },
}
</script>
<style lang="sass" scoped></style>
