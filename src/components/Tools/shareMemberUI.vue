<template>
    <div>
        <el-dialog custom-class="last-dialog" top="calc(45% - 400px)" title="选择分享人" :visible.sync="dialogVisible"
            :before-close="handleClose"
            @open="handleOpenDialog"
        >
            <div class="flex-box">
                <div class="flex-item" style="border-right: 1px solid #EBECF0; padding-right: 20px;">
                    <div style="width: 100%px">
                        <el-input placeholder="搜索" clearable v-model="searchForm.searchKey" class="search-input">
                            <el-button slot="append" icon="el-icon-search" @click="getUserList"></el-button>
                        </el-input>
                    </div>
                    <div class="search-result">
                        <el-checkbox-group v-model="checkResult">
                            <div>
                                <el-checkbox @change="(val) => handleCheckChange(val,user.id)"  v-for="user in userList" :key="user.id" :label="user.id">
                                    {{user.nickname}}<span style="color: #969799">({{user.loginName}})</span>
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                    <div><el-checkbox style="margin-top: 16px;" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox></div>
                </div>
                <div class="flex-item" style="padding-left: 20px">
                    <div style="line-height: 30px;">已选择({{checkResultList.length}})</div>
                    <div class="select-result">
                        <div class="custom-tag" v-for="user in checkResultList" :key="user.id">
                            <span>{{user.nickname}}</span>
                            <i class="el-icon-close" @click="removeUser(user.id)"></i>
                        </div>
                    </div>
                    <div class="search-footer">
                        <el-button type="primary" @click="onConfirm">确定</el-button>
                        <el-button type="info" @click="handleClose">关闭</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { debounce } from '@/utils/util.js'
export default {
    name: 'modelDialog',
    data() {
        return {
            dialogVisible: false,
            loading: false,
            userList: [],
            checkAll: false,
            isIndeterminate: false,
            checkResult: [],
            checkResultList: [],
            searchForm: {
                searchKey: ""
            }
        }
    },
    props: {
        shareUsers: {
            type: Array,
            default: () => []
        }
    },
    watch: {
    'searchForm.searchKey': debounce(function (n, o) {
      if (n === '') {
        this.userList = []
        this.checkResult = []
      } else {
        if (!this.loading && n) {
          this.loading = true
          this.getUserList(n)
        }
      }
    }, 1e3),
    dialogVisible: function (n, o) {
      if (!n) {
        this.searchForm.searchKey = ''
        this.checkAll = false
        this.isIndeterminate = false
        this.checkResult = []
        this.userList = []
      }
    }
  },
    methods: {
        onConfirm() {
            this.$emit('shareMemberList', this.checkResultList)
            this.handleClose()
        },
        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false
        },
        // 开启弹窗
        handleOpen() {
            this.dialogVisible = true
        },
        handleOpenDialog () {
            this.checkResultList = this.shareUsers
            this.userList = this.shareUsers
            this.checkResult = this.checkResultList.map((user)=>user.id)
        },
        // 全选
        handleCheckAllChange (val) {
            console.log('权限', val)
            if (val) {
                const allList = []
                const allids = []
                this.userList.forEach((user) => {
                    allids.push(user.id)
                    allList.push(user)
                })
                this.checkResult.push(...allids);
                this.checkResultList.push(...allList);
                this.isIndeterminate = true
            } else {
                const allids = this.userList.map((user) => user.id)
                const newids = []
                const newList = []
                this.checkResult.forEach((id) => {
                    if (allids.indexOf(id) < 0) {
                        newids.push(id)
                    }
                })
                this.checkResult = newids
                this.checkResultList.forEach((user) => {
                    if (allids.indexOf(user.id) < 0) {
                        newList.push(user)
                    }
                })
                this.checkResultList = newList
                this.isIndeterminate = false
            }
           
        },
        // 删除用户
        removeUser(id) {
            const temp = []
            this.checkResultList.forEach((user) => {
                if (user.id != id) {
                    temp.push(user)
                }
            })
            this.checkResultList = temp;
            this.checkResult = this.checkResultList.map((user)=>user.id)
        },
        handleCheckChange (val, id) {
            console.log(val, id)
            if (val) {// 如果是选中
                const checklist = []
                const filter = this.userList.filter((user)=>user.id==id)
                checklist.push.apply(checklist,filter);
                this.checkResultList.push(...checklist);
            } else {
                const checklist = []
                this.checkResultList.forEach((user)=>{
                    if (user.id != id) {
                        checklist.push(user)
                    }
                })
                this.checkResultList = checklist
            }
            this.checkResult = this.checkResultList.map((user)=>user.id)
        },
        // 获取用户列表
        async getUserList () {
            const vm = this
            vm.userList = []
            const { data } = await vm.$axios.post(`${vm.baseUrl}/workspace/member/getAllMemberByUserName`, {
                userName: vm.searchForm.searchKey
            })
            vm.loading = false
            if (data.status === 200) {
                vm.userList = data.data
            }
        },
        handleSizeChange(val) {
            this.tableData.page = 1
            this.tableData.pageSize = val
            this.queryTableData()
        },
        handleCurrentChange(val) {
            this.tableData.page = val
            this.queryTableData()
        },
    },
}
</script>
<style lang="scss" scoped>
.flex-box {
    display: flex;
    flex-direction: row;
    height: 100%;

    .flex-item {
        flex: 1;
    }
}
.custom-tag {
    display: inline-block;
    background: #F2F3F5;
    border-radius: 2px;
    border: 1px solid rgba(200,201,204,0.4);
    line-height: 20px;
    color: #323233;
    padding: 0px 8px;
    margin-right: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    &:hover {
        background-color: #dcdfe6;
    }
    &:last-child {
        margin-right: 0px;
    }
    i {
        font-size: 12px;
        padding-left: 12px;
    }
}
.search-result {
    height: 240px;
    max-height: 240px;
    overflow-y: auto;
    padding: 16px 0px;
}
.select-result {
    height: 240px;
    max-height: 240px;
    overflow-y: auto;
    padding: 14px 0px;
}

::v-deep .search-result{
    .el-checkbox {
        display: block;
        width: 100%;
        margin-bottom: 8px;
        &:hover {
            background-color: #F2F3F5;
        }
    }
}
::v-deep .el-checkbox__inner {
    width: 16px;
    height: 16px;
}
.search-footer {
    margin-top: 16px;
    text-align: right;
}
::v-deep .el-button {
    line-height: 20px;;
}

::v-deep .search-input {
    .el-input__inner {
        height: 30px;
        line-height: 30px;
        border-color: #C8C9CC !important;
        border-right: none;
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
    }

    .el-input-group__append {
        border-color: #C8C9CC !important;
        background-color: transparent;
        padding: 0px 12px;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
    }
}</style>
  