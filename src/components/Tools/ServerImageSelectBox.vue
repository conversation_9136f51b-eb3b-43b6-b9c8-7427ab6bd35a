<template>
  <el-row style="width: 100%">
    <el-col :span="span">
      <el-select
        v-model="innerBaseImageId"
        :class="{ imageSelect: isExpand }"
        filterable
        :placeholder="placeholder"
        :disabled="disFlag"
        @change="sendFooVal"
      >
        <el-option
          v-for="item in innerFilterImageServerList"
          :key="item.value"
          :label="`${item.label}${item.alias ? '(' + item.alias + ')' : ''}`"
          :value="item.value"
        />
      </el-select>
    </el-col>
    <el-link v-if="isShowAddBtn" style="margin-left: 10px" type="primary" @click="goADD">
      新增
    </el-link>
  </el-row>
</template>

<script>
export default {
  name: 'ServerImageSelectBox',
  props: {
    /**
     * 下拉框宽度
     */
    span: {
      type: Number,
      default: 4
    },
    /**
     * 下拉框提示语
     */
    placeholder: {
      type: String,
      default: '请选择'
    },
    /**
     * 是否显示新增按钮
     */
    isShowAddBtn: {
      type: Boolean,
      default: false
    },
    /**
     * 下拉框是否扩充百分百
     */
    isExpand: {
      type: Boolean,
      default: false
    },
    /**
     * 下拉框选中的镜像id
     */
    selectId: {
      type: [Number, String],
      default: 0
    },
    isGetBaseImage: {
      type: Number,
      default: 0
    },
    isSelectJupyterlabImage: {
      type: Number,
      default: 0
    },
    /**
     * 镜像框架过滤
     * */
    framework: {
      type: String,
      default: null
    },
    disFlag: {
      type: Boolean,
      default: false
    },
    osArch: {
      type: Number,
      default: 0
    },
    modelName: {
      type: String,
      default: ''
    },
    requestPath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      innerFramework: null,
      innerBaseImageId: null,
      innerImageServerList: [],
      innerFilterImageServerList: []
    };
  },
  watch: {
    selectId: {
      immediate: true,
      handler: function (val, oldVal) {
        if (val !== 0) {
          this.innerBaseImageId = val;
        } else {
          this.innerBaseImageId = undefined;
        }
      }
    },
    async framework(val, oldVal) {
      this.innerFramework = val;
      this.imageListFilter();
    },
    osArch(val, oldVal) {
      this.initImageList(null);
    },
    isGetBaseImage(val, oldVal) {
      this.initImageList(null);
    },
    isSelectJupyterlabImage: {
      immediate: true,
      handler: function (val, oldVal) {
        this.initImageList(null);
      }
    }
  },
  created() {
    // this.initImageList(null);
    if (this.modelName === 'YOLO') {
      if (process.env.VUE_APP_ENV === 'production') {
        this.innerBaseImageId = 1116;
      } else {
        this.innerBaseImageId = 1024;
      }
      this.$emit('func', this.innerBaseImageId);
    }
  },
  methods: {
    goADD() {
      this.$router.push({ path: 'keyword/CreateMirror' });
    },
    initImageList(keyword) {
      this.innerFilterImageServerList = [];
      this.innerImageServerList = [];
      const path = this.requestPath || '/aiConfig/queryRuntimeList';
      let params = {
        imageName: keyword,
        isGetBaseImage: this.isGetBaseImage,
        mirrorType: this.mirror_type,
        isSelectJupyterlabImage: this.isSelectJupyterlabImage,
        osArch: this.osArch
      };
      if (this.requestPath) {
        params = { ...params, page: 1, pageSize: 200, type: 1 };
      }
      this.$post(path, params).then((res) => {
        // if (this.modelName === 'YOLO') {
        //   // const imageName = 'enn-py3.7.9_pytorch190_gpu'
        //   // console.log('data', data)
        //   // const item = data.filter((item) => {
        //   //   console.log('item', item)
        //   //   return item.name.indexOf(imageName) > 0
        //   // })[0]
        //   this.innerBaseImageId = 406
        // }
        const data = res?.list || res;
        this.innerImageServerList = data.map((item) => {
          return {
            value: item.id,
            label: this.requestPath ? `${item.name}(${item.version})` : item.name,
            framework: item.framework,
            alias: item.alias
          };
        });
        this.innerFilterImageServerList = this.innerImageServerList;
        this.imageListFilter();
      });
    },
    sendFooVal() {
      const _this = this;
      let name = '';
      let imageItem = null;
      this.innerImageServerList.forEach(function (item, index) {
        if (item.value === _this.innerBaseImageId) {
          name = item.label;
          imageItem = item;
        }
      });
      this.$emit('func', this.innerBaseImageId, name, imageItem);
    },
    imageListFilter() {
      if (this.innerFramework) {
        let cleanDefaultSelect = true;
        this.innerFilterImageServerList = this.innerImageServerList.filter((i) => {
          return (
            this.innerFramework === i.framework || i.framework.indexOf(this.innerFramework) > -1
          );
        });
        for (let i = 0; i < this.innerFilterImageServerList.length; i++) {
          const _imageInfo = this.innerFilterImageServerList[i];
          if (this.innerBaseImageId === _imageInfo.value) {
            cleanDefaultSelect = false;
            break;
          }
        }
        if (cleanDefaultSelect && this.innerFilterImageServerList.length > 0) {
          this.innerBaseImageId = '';
          this.$emit('func', this.innerBaseImageId, '');
        }
      } else {
        this.innerFilterImageServerList = this.innerImageServerList;
      }
    }
  }
};
</script>

<style lang="scss">
.imageSelect {
  width: 100% !important;
}
</style>
