<template>
  <el-upload
    ref="uploadFile"
    class="upload-demo"
    style=""
    :auto-upload="true"
    :multiple="multiple"
    :accept="accept"
    :limit="numLimit"
    :file-list="fileList"
    :data="data"
    :action="uploadUrl"
    :on-success="modelUploadSuccess"
    :on-change="modelChangeHandle"
    :on-error="modelUploadError"
    :before-upload="modelBeforeUploadHandle"
    :http-request="fnUploadRequest"
    :before-remove="beforeRemove"
  >
    <el-button
      slot="trigger"
      :loading="inProcess"
    >
      <i class="el-icon-upload el-icon--right" />上传文件
    </el-button>
    <el-tooltip
      v-if="uploadTip"
      style="padding-left: 20px"
      class="item"
      effect="dark"
      :content="uploadTip"
      placement="right-end"
    >
      <i class="el-icon-question note" />
    </el-tooltip>
    <el-form :model="data" />
    <el-progress
      v-show="inProcess"
      style="width: 80%;margin-top: 8px;"
      :text-inside="true"
      :stroke-width="16"
      :percentage="progressPercent"
    />
  </el-upload>
</template>
<script type="text/javascript">
import innerAxios from 'axios'
// import ObsClient from 'esdk-obs-browserjs/src/obs'
const CancelToken = innerAxios.CancelToken

let source = CancelToken.source()

export default {
  components: {
    /**
     * @description: 自定义组件注册。
     */
  },
  props: {
    /**
     * @description: 可以是数组或对象，用于接收来自父组件的数据。
    */
    signUrl: {
      required: false,
      type: String,
      default: '/obsfs/generateSign'
    },
    open: {
      required: false,
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 1024
    },
    cover: {
      type: Boolean,
      default: false
    },
    numLimit: {
      type: Number
    },
    accept: {
      type: String,
      default: ''
    },
    // 由于模型文件存在无文件类型的状态
    // 所以该字段用在before-upload时机用来校验
    accept_v2: {
      type: String,
      default: ''
    },
    uploadTip: {
      type: String,
      default: '支持任意文件'
    },
    defaultFileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    data: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    /**
     * @description: 当前页面的数据对象，Vue 将会递归将 data 的 property 转换为 getter/setter，从而让 data 的 property 能够响应数据变化。
     * @param void
     * @return {Object}
     */
    return {
      progressPercent: 0,
      uploadUrl: '',
      uploadParam: {},
      fileInfos: new Map(),
      fileList: [],
      inProcess: false,
      fileTest: ''
    }
  },
  computed: {
    /**
     * @description: 计算属性将被混入到当前Vue实例中。所有 getter 和 setter 的 this 上下文自动地绑定为 Vue 实例。
     */
  },
  watch: {
    /**
     * @description: 监听一个对象，键是需要观察的表达式，值是对应回调函数。值也可以是方法名，或者包含选项的对象。
     */
    defaultFileList: {
      deep: true,
      immediate: true,
      handler (nv) {
        if (nv.length) {
          this.fileList = nv.map(itm => itm)
        }
      }
    }
  },
  created () {
    /**
     * @description: 在实例创建完成后被立即调用。
     * @param void
     * @return void
     */
  },
  mounted () {
    /**
     * @description: 实例被挂载后调用。
     * @param void
     * @return void
     */
  },
  methods: {
    /**
     * @description: methods 将被混入到 Vue 实例中。可以直接通过 VM 实例访问这些方法，或者在指令表达式中使用。方法中的 this 自动绑定为 Vue 实例。
     */
    // 更新上传url和参数
    setUploadParamInfo (signData, file) {
      console.log('setUploadParamInfo ==== ')
      // const accessKeyId = signData.accessKeyId
      // const key = signData.key
      const obsUrl = signData.obsUrl
      // const policy = signData.policy
      // const signature = signData.signature
      // const urlArr = ossUrl.split('//')
      this.fileInfos.set(file.uid, new Object())
      this.fileInfos.get(file.uid).uploadParam = {
        key: signData.key,
        accessKeyId: signData.accessKeyId,
        signature: signData.signature,
        policy: signData.policy
      }
      this.fileInfos.get(file.uid).fileInfo = {
        name: file.name,
        size: file.size,
        uid: file.uid
      }
      this.fileInfos.get(
        file.uid
      ).uploadUrl = obsUrl

      console.log('setUploadParamInfo uid add. uid: ' + file.uid)
    },
    clearFiles () {
      this.fileInfos = new Map()
      this.fileList = []
      this.$refs.uploadFile.clearFiles()
    },
    // 手动提交上传
    async manualUpload () {
      // this.$refs.modelUpload.submit()
    },
    getFileSign (file, resolve, reject) {
      const _this = this
      console.log('getFileSign ==== ', file)
      this.fileTest = file
      try {
        const params = {
          // fileType: this.$fileUtil.getFileSuffix(file.name),
          fileName: file.name
        }
        if (!this.open) {
          params.open = false
        }
        this.$axios.post(this.baseUrl + this.signUrl, params).then((res) => {
          if (res.status === 200 && res.data && res.data.status === 200) {
            // 根据签名服务的返回值拼接文件上传url
            _this.setUploadParamInfo(res.data.data, file)
            resolve(true)
          } else {
            reject(false)
          }
        }).catch((err) => {
          console.log(err)
          reject(false)
        })
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
        reject(false)
      }
    },
    // 保存文件信息
    async saveFileInfo (file, _self) {
      console.log(file)
      const params = this.fileInfos.get(file.uid).uploadParam
      const fileKey = params.key
      this.$ossUtil.saveFileInfoOBS(file, fileKey).then((res) => {
        if (res.data.status === 200) {
          const fileId = res.data.data.fileId

          _self.fileInfos.get(file.uid).fileInfo.fileId = fileId

          const uploadFiles = new Array()

          _self.fileInfos.forEach(function (item, key) {
            uploadFiles.push(item.fileInfo)
          })

          console.log('UploaderTool onFilesChange ====')
          _self.$emit('onFilesChange', {
            name: file.name,
            size: file.size,
            fileId: fileId,
            uid: file.uid
          }, uploadFiles)
        }
      })
    },
    // 下载文件
    downloadModel () {
    //      let fileId = this.uploadFiles[0]
    //      this.$ossUtil.downloadModel(fileId)
    },
    // #endregion

    // #region el-upload 事件方法

    // 自定义上传方法
    fnUploadRequest (option) {
      const _this = this
      this.inProcess = true
      try {
        console.log('fnUploadRequest =====')

        const file = option.file

        console.log('fnUploadRequest uid: ' + file.uid)
        console.log('fnUploadRequest file: ' + this.fileInfos.get(file.uid))

        const url = this.fileInfos.get(file.uid).uploadUrl
        const params = this.fileInfos.get(file.uid).uploadParam
        const formData = new FormData()

        console.log(url)
        console.log(params)

        Object.keys(params).forEach(function (key) {
          formData.append(key, params[key])
        })
        formData.append('file', file)
        this.progressPercent = 0
        const config = {
          cancelToken: source.token,
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          timeout: 1000 * 60 * 60,
          onUploadProgress: progressEvent => {
            // progressEvent.loaded:已上传文件大小
            // progressEvent.total:被上传文件的总大小
            this.progressPercent = parseInt((progressEvent.loaded / progressEvent.total * 100) * 100) / 100
          }
        }
        innerAxios.post(url, formData, config).then((res) => {
          this.inProcess = false
          _this.saveFileInfo(file, _this)
        })
      } catch (error) {
        this.inProcess = false
        console.error(error)
        option.onError('上传失败')
        this.$error(error.message)
      }
    },
    modelBeforeUploadHandle (file) {
      const _this = this
      let isLtType = false

      if (this.accept_v2) {
        this.accept_v2.split(',').forEach(function (item) {
          if (file.name.endsWith(item)) {
            isLtType = true
          }
          if (file.name.indexOf('.') == -1 && item == '无类型') {
            isLtType = true
          }
        })

        if (!isLtType) {
          this.$message.error('只能上传"' + this.accept_v2 + '"的文件')
        }
      } else {
        isLtType = true
      }

      const isLt2M = file.size / 1024 / 1024 < this.limit
      if (!isLt2M) {
        this.$message.error('大小不能超过' + this.limit + 'MB!')
      }

      console.log(isLtType && isLt2M)

      if (isLtType && isLt2M) {
        return new Promise(
          function (resolve, reject) {
            // 一段耗时的异步操作
            _this.getFileSign(file, resolve, reject)
          }).then(
          (res) => { console.log(res) },
          (err) => {
            console.log(err)
            return false
          }
        )
      } else {
        return false
      }
    },

    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
    modelChangeHandle (file, fileList) {
      if (this.cover) {
        if (fileList.length > 1) {
          fileList.splice(0, 1);
          this.fileList = fileList
        }
      }
      this.$emit('on-change', file, fileList)
    },

    // 文件上传失败时的钩子
    modelUploadError (err, file, fileList) {
      this.fileInfos = new Map()
      this.$emit('on-error', file, fileList)
      this.$message.warning('上传失败')
    },

    // 文件上传成功时的钩子
    modelUploadSuccess (response, file) {
      this.showProcess = false
      this.processLength = 0

      //      this.uploadStatus = file.status
      //      let _self = this
      //      if (this.uploadStatus === 'success') {
      //        this.saveFileInfo(file, _self)
      //      } else {
      //        this.$message.warning(`文件上传状态为:${this.uploadStatus}`)
      //      }
    },
    beforeRemove (file, fileList) {
      source.cancel('取消')
      source = CancelToken.source()
      this.inProcess = false
      console.log(file)
      console.log(fileList)

      if (this.fileInfos.get(file.uid)) {
        const fileId = this.fileInfos.get(file.uid).fileInfo.fileId

        this.fileInfos.delete(file.uid)
        this.$emit('on-remove', file, fileList)

        const uploadFiles = new Array()

        this.fileInfos.forEach(function (item, key) {
          uploadFiles.push(item.fileInfo)
        })

        console.log('UploaderTool beforeRemove onFilesChange ====')
        this.$emit('onFilesChange', {
          name: file.name,
          size: file.size,
          uid: file.uid,
          fileId: fileId
        }, uploadFiles)
      }
      this.$emit('on-remove', {})
    }
    // #endregion
  }
}
</script>
<style lang="sass" scoped>
label
  margin-top: 0
</style>
