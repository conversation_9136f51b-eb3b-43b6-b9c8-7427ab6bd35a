<template>
  <div class="flex-end-center">
    <div class="recently flex">
      <li
        v-for="item,index in recently"
        :key="index"
        class="recently-item"

        @click="handleTimeRange(item,index)"
      >
        <span :class="index === currentIndex?'active':''">{{ item.label }}</span>
      </li>
      <el-popover
        v-model="visible"
        placement="top"
        title="起止时间"
        trigger="manual"
        popper-class="time-date-pop"
      >
        <div class="content">
          <el-date-picker
            v-model="value1"
            :editable="false"
            popper-class="resource-control-picker"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            :clearable="false"
          />
          <div class="pop-btn">
            <el-button
              type="primary"
              @click="applyTimeChange()"
            >
              应用
            </el-button>
            <el-button

              @click="closePop()"
            >
              取消
            </el-button>
          </div>
        </div>

        <i
          slot="reference"
          class="el-icon-date"
          :class="currentIndex === 1000?'active':'' "
          style="fontsize:16px"
          @click="visible = !visible"
        />
      </el-popover>
    </div>

    <div>
      <el-tooltip
        style="padding-right:10px"
        effect="dark"
        content=""
        placement="top"
      >
        <div slot="content">
          刷新
        </div>
        <div @click="logRefresh()">
          <img
            src="@/assets/images/service/refresh.png"
            alt=""
            width="20"
            height="20"
          >
        </div>
      </el-tooltip>
    </div>
    <div>
      <el-select
        v-model="timeRefresh"
        style="width:90px"
        @change="btnRefresh"
      >
        <el-option
          v-for="item in timeRefreshList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-tooltip
      style="padding-left: 10px"
      class="item"
      effect="dark"
      content=""
      placement="right-start"
    >
      <div slot="content">
        可选择自动刷新间隔
      </div>

      <a><img
        width="20"
        height="20"
        src="@/assets/images/question-circle.png"
      ></a>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'ResourceControl',
  props: {
    value2: {
      type: Array,
      default: () => {}
    }
  },
  data () {
    return {
      time: 60,
      currentIndex: 3,
      visible: false,
      timeRefreshList: [
        { value: 0, label: '关闭' },
        { value: 15000, label: '每15秒' },
        { value: 30000, label: '每30秒' },
        { value: 60000, label: '每1分钟' },
        { value: 300000, label: '每5分钟' },
        { value: 900000, label: '每15分钟' },
        { value: 1800000, label: '每30分钟' }
      ],
      recently: [{ label: '5分钟', value: 5 }, { label: '15分钟', value: 15 }, { label: '30分钟', value: 30 }, { label: '1小时', value: 60 }, { label: '6小时', value: 360 }, { label: '12小时', value: 720 }],
      timeRefresh: '',
      timer: null,
      value1: [],
      choiceDate: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          // 把选择的第一个日期赋值给一个变量。
          this.choiceDate = minDate.getTime()
          // 如何你选择了两个日期了，就把那个变量置空
          if (maxDate) this.choiceDate = ''
        },
        disabledDate: (time) => {
          // 如何选择了一个日期
          if (this.choiceDate) {
            // 7天的时间戳
            const one = 24 * 3600 * 1000
            // 当前日期 - one = 7天之前
            const minTime = this.choiceDate - one
            // 当前日期 + one = 7天之后
            const maxTime = this.choiceDate + one
            return time.getTime() < minTime || time.getTime() > maxTime
          }
        }
      }
    }
  },
  watch: {
    value2: {
      deep: true,
      handler (newV, oldV) {
        this.value1 = newV
      }

    }
  },

  mounted () {
    this.timeRefresh = this.timeRefreshList[0].value
  },

  methods: {
    logRefresh () {
      if (this.currentIndex === -1 || this.currentIndex === 1000) {
        this.$emit('logRefresh')
        this.$nextTick(() => {
          this.currentIndex = 3
        })

        return
      }
      this.recently.forEach((el, index) => {
        if (index === this.currentIndex) {
          this.$emit('logRefresh', el.value)
        }
      })
    },
    closePop () {
      this.visible = !this.visible
    },
    btnRefresh () {
      clearInterval(this.timer)
      this.time = 60
      if (this.currentIndex !== -1 && this.currentIndex !== 1000) {
        this.recently.forEach((el, index) => {
          if (index === this.currentIndex) {
            this.time = el.value
          }
        })
      } else {
        this.currentIndex = 3
      }
      if (this.timeRefresh === 0) return
      console.log(this.timeRefresh)
      this.timer = setInterval(() => {
        if (this.currentIndex === 1000) {
          this.currentIndex = 3
        }
        this.initialDraw(this.time, true)
      }, this.timeRefresh)
    },
    initialDraw (val, status) {
      this.$emit('initialDraw', val, status)
    },
    handleTimeRange (item, index) {
      try {
        this.currentIndex = index
        this.time = item.value

        this.initialDraw(item.value)
      } catch (error) {
        throw new Error(error)
      }
    },
    async  applyTimeChange () {
      await this.$emit('applyTimeChange', this.value1)
      this.currentIndex = 1000
      this.visible = !this.visible
    }

  }
}
</script>

<style lang="scss" scoped>
.recently {
  background: #f2f2f2;
  padding: 5px 10px;
  border-radius: 10px;
  margin: 0 15px;

  .active {
    background: var(--color-primary);
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.35);
    color: #FFFF;
    border-radius: 6px;
    padding: 5px;
  }

  .recently-item {
    padding:0 10px;

    &:hover {
      color: var(--color-primary);
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.resource-control-picker {
  .el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
    display: none;
  }

  .el-date-range-picker__editors-wrap {
    .el-date-range-picker__time-picker-wrap:first-child {
      .el-date-range-picker__editor {
        > .el-input__inner {
          background: #f5f7fa;
          pointer-events: none;
        }
      }
    }
  }

}
.time-date-pop{
 .content{
   .pop-btn{

     margin:10px 0;
   }
 }
}
</style>
