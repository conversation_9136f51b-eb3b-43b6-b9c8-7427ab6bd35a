<template>
  <div class="message-item" :class="[{ 'flex-row-reverse': inversion }]">
    <div class="avatar" :class="[inversion ? 'ml-2' : 'mr-2']">
      <AvatarComponent :image="inversion" />
    </div>
    <div class="content" :class="[inversion ? 'items-end' : 'items-start']">
      <div style="display: flex" :class="[!inversion ? 'items-center' : 'flex-col']">
        <div v-if="inversion" class="name">
          {{ nickName }}
        </div>
        <div class="name" :class="[inversion ? 'tr' : 'tl']">
          {{ item?.createdDate }}
        </div>
      </div>
      <div class="text-wrap">
        <TextComponent
          ref="textRef"
          :inversion="inversion"
          :error="!!item.error"
          :text="item.content"
          :loading="!!item.loading"
          :as-raw-text="asRawText"
        />
        <div v-if="!inversion && item.showMore" class="more" @click="handleMore">更多咨询</div>
      </div>
      <div v-if="item.fileUrl?.length" class="source">
        <div>来源：</div>
        <div class="source-wrap">
          <div v-for="(el, index) in item.fileUrl" :key="index" class="source-item">
            <a v-if="el?.match(/.+\/(.+\.\w{3,4})$/)?.[1]" :href="el">
              {{ decodeURIComponent(el?.match(/.+\/(.+\.\w{3,4})$/)?.[1] || '') }}
            </a>
            <span v-else>{{ el }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AvatarComponent from './avatar.vue';
import TextComponent from './text.vue';

export default {
  name: 'ChatPage',
  components: { AvatarComponent, TextComponent },
  props: {
    item: {
      type: Object,
      default() {
        return {};
      }
    },
    version: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      inversion: this.item?.role === 'user',
      rateSubmit: this.item?.score >= 0,
      asRawText: this.inversion,
      feedBackInfo: { type: 0, value: '' },
      nickName: JSON.parse(sessionStorage.getItem('USER_INFO'))?.nickName || ''
    };
  },
  computed: {
    rate() {
      return this.item?.score || 0;
    }
  },
  methods: {
    handleMore() {
      this.$emit('handleMore');
    }
  }
};
</script>

<style lang="less" scoped>
.message-item {
  width: 100%;
  display: flex;
  padding: 1rem 1.5rem;
  overflow: hidden;
  .avatar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
  }
  .ml-2 {
    margin-left: 0.5rem;
  }
  .mr-2 {
    margin-right: 0.5rem;
  }
  .content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-size: 0.875rem;
    line-height: 1.25rem;
    .text-wrap {
      width: 100%;
      display: flex;
      align-items: flex-end;
      .more {
        font-size: 12px;
        margin-left: 8px;
        color: #2d8cf0;
        cursor: pointer;
      }
    }
  }
  .name {
    flex: 1;
    text-align: right;
    font-size: 0.75rem;
    line-height: 1.1rem;
    color: rgb(180 187 196);
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-center {
    align-items: center;
  }
  .flex-col {
    flex-direction: column;
  }
  .tr {
    text-align: right;
  }
  .tl {
    text-align: left;
  }
}

.flex-row-reverse {
  flex-direction: row-reverse;
}
.source {
  display: flex;
  margin-top: 0.75rem;
  &-wrap {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  &-item {
    margin-bottom: 4px;
    margin-right: 14px;
    > span {
      color: #3b82f6;
    }
  }
}
</style>
