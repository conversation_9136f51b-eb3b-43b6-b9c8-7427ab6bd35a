<template>
  <el-avatar v-if="image" :src="avatar" />
  <el-image v-else style="width: 5em; height: 5em" :src="require('@/assets/images/ennew.png')" />
</template>
<script>

export default {
  name: 'AvatarPage',
  components: {},
  props: {
    image: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    avatar() {
      return JSON.parse(sessionStorage.getItem('USER_INFO'))?.headImageUrl;
    }
  }
};
</script>
