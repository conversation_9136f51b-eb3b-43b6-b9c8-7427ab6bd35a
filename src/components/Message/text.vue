<template>
  <div :class="[inversion ? 'user' : 'assistant']" class="text-content">
    <div ref="textRef" class="leading-relaxed break-words">
      <template v-if="!inversion">
        <div class="markdown-body" :class="{ 'text-red-500': error }" v-html="textValue" />
        <img v-if="isShowQRcode" :src="require('@/assets/images/tips.png')" class="tips-img" />
        <span v-if="!textValue && loading" class="wait" />
      </template>
      <div v-else style="white-space: pre-wrap" v-text="textValue" />
    </div>
  </div>
</template>
<script>
import MarkdownIt from 'markdown-it';
import mdKatex from '@traptitech/markdown-it-katex';
import mila from 'markdown-it-link-attributes';
import hljs from 'highlight.js';

export default {
  name: 'TextPage',
  components: {},
  props: {
    inversion: {
      type: Boolean,
      default: false
    },
    error: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    asRawText: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mdi: null
    };
  },
  computed: {
    textValue() {
      const value = this.text ?? '';
      if (!this.inversion) return this.mdi?.render(value);
      return value;
    },
    isShowQRcode() {
      return ['在线支持|扫码入群'].includes(this.text);
    }
  },
  created() {
    this.mdi = new MarkdownIt({
      linkify: true,
      highlight(code, language) {
        const validLang = !!(language && hljs.getLanguage(language));
        if (validLang) {
          const lang = language ?? '';
          return this.highlightBlock(hljs.highlight(code, { language: lang }).value, lang);
        }
        return this.highlightBlock(hljs.highlightAuto(code).value, '');
      }
    });

    this.mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } });
    this.mdi.use(mdKatex, {
      blockClass: 'katexmath-block rounded-md p-[10px]',
      errorColor: ' #cc0000'
    });
  },
  methods: {
    highlightBlock(str, lang) {
      return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
    }
  }
};
</script>
<style lang="less" scoped>
.text-content {
  flex: 1;
  min-width: 20px;
  padding: 8px 12px;
  margin-top: 8px;
  color: #000;
  border-radius: 4px;
}
.leading-relaxed {
  line-height: 1.625;
}
.break-words {
  overflow-wrap: break-word;
}
.user {
  background: #d2f9d1;
}
.assistant {
  background: #f4f6f8;
}
.markdown-body {
  background: #f4f6f8;
  color: #24292f;
}
.text-red-500 {
  color: rgb(239 68 68);
}
.wait {
  display: block;
  height: 15px;
  width: 4px;
  background-color: currentColor;
  animation: blink 1.2s infinite steps(1, start);
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.tips-img {
  height: 120px;
  width: 120px;
  margin-top: 8px;
  border: 0;
}
</style>
