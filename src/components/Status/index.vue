<template>
  <!-- 状态灯组件 -->
  <div class="status" :style="statusStyle">
    <i v-if="isStatus" class="dotClass"></i>
    <div>{{ text }}</div>
  </div>
</template>
<script>
export default {
  name: 'StatusComponent',
  props: {
    isStatus: {
      type: Boolean,
      default: true
    },
    dotColor: {
      // 灯颜色值
      type: String,
      default: ''
    },
    bgColor: {
      // tag背景颜色值
      type: String,
      default: ''
    },
    text: {
      // 文案
      type: String,
      default: ''
    },
  },
  data() {
    return {
      statusColorStyle: {}
    }
  },
  computed: {
    statusStyle () {
      return { '--background': this.bgColor, '--dotColor': this.dotColor }
    }
  },
}
</script>
<style lang="scss" scoped>
.status {
  height: 24px;
  line-height: 24px;
  border-radius: 2px;
  word-break: keep-all;
  // margin-left: 20px;
  // margin-right: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  background: var(--background);

  .dotClass {
    border-radius: 2px;  
    display: block;
    width: 7px;
    height: 7px;
    background: var(--dotColor);
  }

  >div {
    // margin-left: 5px;
  }
  i {
   margin-right: 5px;
  }
}
</style>
