<template>
 <div class="header-container">
  <div class="header-container__bar-container" :style="barContainerStyle">
   <div class="header-action-bar">
    <div class="header-action-bar__container">
     <div class="header-action-bar__left-container">
      <div>
       <p class="title title__level-4">
        <span class="tooltip__trigger">{{ title }}</span>
       </p>
      </div>
     </div>
     <div v-if="title" class="header-action-bar__right-container">
      <el-button type="primary">主要按钮</el-button>
      <el-button type="primary" plain>按钮</el-button>
     </div>
    </div>
   </div>
  </div>
  <div class="header-container__container">
   <div class="header-container__container-main header-container__container-border">
    <div class="header-container--currency">
     <div>
      <slot name="container"></slot>
     </div>
    </div>
   </div>
  </div>
  <div class="header-container__container-footer">
   <slot name="footer"></slot>
  </div>
 </div>
</template>

<script>
import { title } from 'process';

export default {
 name: 'heardContainer',
 props: {
  title: {
   type: String,
   default: '头部操作栏'
  }
 },
 data() {
  return {

  }
 },
 computed: {
    barContainerStyle() {
      return {
        '--bar-container-height': this.title ? '54px' : '0px'
      };
    }
  }

}
</script>
<style lang="scss" scoped>
.header-container {
 background: #ffffff;
 transition: all .3 cubic-bezier(0.645, 0.045, 0.355, 1);
 cursor: default;

 &__bar-container {
  height: var(--bar-container-height);
  position: relative;
  width: 100%;

  .header-action-bar {
   padding: 0 16px;
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   background: #ffffff;

   &__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--bar-container-height);

    .header-action-bar__left-container {
     flex-shrink: 0;
     display: flex;
     align-items: center;
     margin-right: 40px;
    }
   }
  }
 }

 &__container {
  padding: 0 4px;

  &-main {
   position: relative;
   height: 100%;
  }

  &-border {
   padding-bottom: 16px;
   margin-bottom: 10px;
   border-bottom: 1px solid #f2f3f5
  }

  .header-container--currency {
   transition: all .1s ease;
   overflow: hidden;
  }
 }
 &__container-footer {
  padding: 0 20px;
 }
}
</style>