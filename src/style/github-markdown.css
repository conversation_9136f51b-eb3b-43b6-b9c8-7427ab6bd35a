.markdown-body {
    /* box-sizing: border-box; */
    max-width: 100%;
    margin: 0 auto;
    background-color: transparent !important;
    font-size: 14px !important;
    max-height: 100%;
    overflow: auto;
  }
  @media (prefers-color-scheme: dark){
    .markdown-body {
      color: #24292f !important;
    }
  }
  .markdown-body pre {
    padding: 0px !important;
    padding: 16px !important;
  }
  .markdown-body pre:has(.language-python) {
    background-color: #1c1b1b !important;
    padding: 16px !important;
  }
  .markdown-body pre:has(.language-json) {
    background-color: #1c1b1b !important;
    padding: 16px !important;
  }
  .markdown-body pre:has(.markdown), .markdown-body pre:has(.language-markdown) {
    padding: 16px !important;
    background-color: transparent !important;
    font-size: 14px !important;
    color: #24292f !important;
  }
  .markdown-body pre:has(.markdown) code, .markdown-body pre:has(.language-markdown) code{
    background-color: transparent !important;
    font-size: 14px !important;
    color: #24292f !important;
  }
  .markdown-body pre:has(.markdown) code .hljs-bullet, .markdown-body pre:has(.markdown) code .hljs-code,.markdown-body pre:has(.markdown) code .hljs-section {
    color: #24292f !important;
  }
  .markdown-body pre:has(.language-markdown) code .hljs-bullet, .markdown-body pre:has(.language-markdown) code code .hljs-code,.markdown-body pre:has(.language-markdown) code .hljs-section,.markdown-body pre:has(.language-markdown) code .hljs-string,.markdown-body pre:has(.language-markdown) code .hljs-code {
    color: #24292f !important;
  }
  
  /* .markdown-body ul {
    list-style-type: disc;
    list-style: disc;
  } */
  .markdown-body ul > li {
    list-style-type: disc;
    list-style: disc;
  }
 .markdown-body ul > li > ul > li {
    list-style-type: circle;
    list-style: circle;
  }
  .markdown-body ul > li > ul > li > ul > li {
    list-style-type: square;
    list-style: square;
  }
  .markdown-body ul > li > ul > li > ul > li > ul > li{
    list-style-type: disc;
    list-style: disc;
  }
    ​
  @media (max-width: 767px) {
    .markdown-body {
      padding: 15px;
    }
  }
  @media (prefers-color-scheme: dark){
    .markdown-body {
      color: #24292f !important;
    }
  }