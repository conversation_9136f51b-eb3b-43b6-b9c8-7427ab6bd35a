/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-weight: normal;
    vertical-align: baseline;
}

img,
input {
    vertical-align: middle;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul,
li {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* custom */
a {
    color: #7e8c8d;
    text-decoration: none;
    -webkit-backface-visibility: hidden;
}

/**
 * scrollbar
 */
/* ::-webkit-scrollbar {
    width: 4px;
    height: 74px;
    background: transparent;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #C2CADB;
    background-clip: padding-box;
    border: 1px solid transparent;
    box-sizing: border-box;
    border-radius: 3px;
} */
::-webkit-scrollbar {
    width: 4px;     
    height: 4px;
}
  /*正常情况下滑块的样式*/
::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
}
*:hover ::-webkit-scrollbar-thumb {
    background-color: #C2CADB;

}
    /*正常时候的主干部分*/
/* ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  } */
  /*鼠标悬浮在滚动条上的主干部分*/
/* ::-webkit-scrollbar-track:hover {
    -webkit-box-shadow: inset 0 0 4px #BEC8E1;
    background-color: #C2CADB;
  } */

html,
body {
    width: 100%;
    height: 100%;
    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", "微软雅黑", sans-serif;
}

body {
    line-height: 1;
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    position: relative;
}

html {
    overflow-y: auto !important;
    font-size: 14px;
}

/*清除浮动*/
.clearfix:before,
.clearfix:after {
    content: " ";
    display: inline-block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clearfix {
    *zoom: 1;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

/*隐藏*/
.dn {
    display: none;
}
.flex{
  display: flex;
}
.flex-end-center{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.text-ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.v-md-editor .scrollbar__view {
    height: 100%;
    min-height: 100% !important;
    /* overflow-y: auto; */
}
.v-md-editor .v-md-editor-preview {
    height: 100%;
    min-height: 100% !important;
    /* overflow-y: auto; */
}
.customMd .vuepress-markdown-body pre.v-md-mermaid {
    height: auto !important;
    min-height: auto !important;
    overflow-y: auto;
}
.vuepress-markdown-body pre.v-md-mermaid {
    height: 100%;
    min-height: 100% !important;
    overflow-y: auto;
}
.v-md-pre-wrapper- {
    background: transparent !important;
}
.v-md-pre-wrapper- code {
    background: transparent !important;
    color: #2c3e50 !important;
}
.v-md-editor, .vuepress-markdown-body{
    background: transparent !important;
}
/* .v-md-editor__right-area {
    width: auto !important;
} */
.vuepress-markdown-body {
    color: #1f2328 !important; 
    word-break:break-word
}
.vuepress-markdown-body:not(.custom) {
    padding: 0px 0  !important;
    min-height: 100%;
    height: 100%;
}
.v-md-pre-wrapper-markdown {
    background: transparent !important;
    color: #2c3e50 !important;
}
.v-md-prism-markdown code {
    color: #2c3e50 !important;
}
.vuepress-markdown-body ol > li {
    list-style-type: decimal;
    list-style: decimal;
}
.vuepress-markdown-body ul > li {
    list-style-type: disc;
    list-style: disc;
}
.vuepress-markdown-body ul > li > ul > li {
    list-style-type: circle;
    list-style: circle;
}
.vuepress-markdown-body ul > li > ul > li > ul > li {
    list-style-type: square;
    list-style: square;
}
.vuepress-markdown-body ul > li > ul > li > ul > li > ul > li{
    list-style-type: disc;
    list-style: disc;
}



.vditor {
    border: none !important;
}
.vditor-toolbar {
    border-bottom: none !important;
}
.vditor-ir .vditor-reset {
    padding: 0px !important;
    background: #fff !important;
}
.vditor .vditor-content {
    background: #fff !important;
}
.vditor ul > li {
    list-style-type: disc;
    list-style: disc;
  }
 .vditor ul > li > ul > li {
    list-style-type: circle;
    list-style: circle;
  }
  .vditor ul > li > ul > li > ul > li {
    list-style-type: square;
    list-style: square;
  }
  .vditor ul > li > ul > li > ul > li > ul > li{
    list-style-type: disc;
    list-style: disc;
  }
.el-loading-mask {
    z-index: 1000 !important;
}
