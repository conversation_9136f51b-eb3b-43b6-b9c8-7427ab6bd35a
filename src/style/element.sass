
.tips-popver
  border: 1px solid #303133 !important
.tips-popver
  .popper__arrow
    border-left-color: #303133 !important
  .popper__arrow::after
    border-left-color: #fff !important
.el-tooltip__popper.is-dark
    max-width: 40% !important 
    font-size: 14px
/* 分页器 */
.el-pager
  margin-right: 4px
  color: #323233

.el-pager li
  min-width: 30px
  height: 30px
  line-height: 30px
  font-size: 14px
  padding: 0
  border-radius: 2px
  margin-right: 4px
  font-weight: 400
  color: #323233

.el-pager li.active
  color: #406BD4
  background-color: #EBECF0

.el-pagination:not(.new-paper) button
  width: 88px
  margin-right: 8px
  border: 1px solid #dddddd
  border-radius: 15px

.el-pagination button:hover
  color: var(--color-primary)
.el-pagination button:disabled
  color: rgba(50, 50, 51, 0.4)

.new-paper .el-pager li.btn-quicknext, .el-pager li.btn-quickprev
  color: #323233
  font-size: 12px
.new-paper.el-pagination .btn-prev, .el-pagination .btn-next
  color: #323232
  margin-right: 0px !important
  padding-right: 0px !important
  padding-left: 0px !important
.new-paper.el-pagination .btn-prev
  margin-right: 8px

.el-pagination__editor
  width: 48px

.new-paper.el-pagination .el-select .el-input
  width: 96px
.el-pagination .el-select .el-input__inner
  height: 30px
  line-height: 30px

.new-paper .el-pagination__sizes
  margin: 0px
.new-paper.el-pagination .el-select .el-input .el-input__inner
  background: #F2F3F5
  color: #4068D4
  border: none
  padding-right: 0px
.new-paper .el-pagination__sizes .el-input .el-input__inner
  padding-left: 12px
  text-align: left
.new-paper .el-select .el-input .el-select__caret
  color: #4068D4

.new-paper .el-pagination__jump
  margin-left: 10px
.el-pagination button,
.el-pagination span:not([class*='suffix'])
  height: 30px
  line-height: 28px
  color: #A6A6A6

.el-radio .el-radio__inner
  box-shadow: none !important

.el-message-box:not(.dbclick-dialog)
  .el-message-box__header
    border-bottom: 1px solid #ccc

  .el-message-box__status
    display: none

  .el-message-box__content
    padding: 10px 0px

  .el-message-box__status + .el-message-box__message
    padding-left: 30px
    padding-right: 30px

  .el-message-box__message
    color: #666666
    font-size: 14px

  .el-message-box__btns
    button
      width: 88px
      border-radius: 2px

.el-cascader .el-input .el-icon-arrow-down:before
  content: ''

.el-cascader .el-input .el-icon-arrow-down
  background: url('../assets/images/map.png') center center no-repeat
  background-size: 14px

.el-message
  font-size: 14px
  top: 140px !important

//download
.el-icon-download
  background: require('@/assets/images/icon/download.svg')
  font-size: 16px

.el-table__fixed-right::before, .el-table__fixed::before
  background-color: transparent

/**
 * 避免360极速浏览器暗黑主题下markdown文本变黑
 */
@media (prefers-color-scheme: dark)
  .markdown-body
    color-scheme: light
    --color-prettylights-syntax-comment: #6e7781
    --color-prettylights-syntax-constant: #0550ae
    --color-prettylights-syntax-entity: #8250df
    --color-prettylights-syntax-storage-modifier-import: #24292f
    --color-prettylights-syntax-entity-tag: #116329
    --color-prettylights-syntax-keyword: #cf222e
    --color-prettylights-syntax-string: #0a3069
    --color-prettylights-syntax-variable: #953800
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa
    --color-prettylights-syntax-invalid-illegal-bg: #82071e
    --color-prettylights-syntax-carriage-return-text: #f6f8fa
    --color-prettylights-syntax-carriage-return-bg: #cf222e
    --color-prettylights-syntax-string-regexp: #116329
    --color-prettylights-syntax-markup-list: #3b2300
    --color-prettylights-syntax-markup-heading: #0550ae
    --color-prettylights-syntax-markup-italic: #24292f
    --color-prettylights-syntax-markup-bold: #24292f
    --color-prettylights-syntax-markup-deleted-text: #82071e
    --color-prettylights-syntax-markup-deleted-bg: #FFEBE9
    --color-prettylights-syntax-markup-inserted-text: #116329
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1
    --color-prettylights-syntax-markup-changed-text: #953800
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5
    --color-prettylights-syntax-markup-ignored-text: #eaeef2
    --color-prettylights-syntax-markup-ignored-bg: #0550ae
    --color-prettylights-syntax-meta-diff-range: #8250df
    --color-prettylights-syntax-brackethighlighter-angle: #57606a
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f
    --color-prettylights-syntax-constant-other-reference-link: #0a3069
    --color-fg-default: #323232
    --color-fg-muted: #57606a
    --color-fg-subtle: #6e7781
    --color-canvas-default: #ffffff
    --color-canvas-subtle: #f6f8fa
    --color-border-default: #d0d7de
    --color-border-muted: hsla(210,18%,87%,1)
    --color-neutral-muted: rgba(175,184,193,0.2)
    --color-accent-fg: #0969da
    --color-accent-emphasis: #0969da
    --color-attention-subtle: #fff8c5
    --color-danger-fg: #cf222e
  

/**
 * 自定义$message内容
 */
.custom-msg-wrapper
  ul
    margin: 8px 16px 0

    li
      margin: 8px 0

/**
 * 自定义tag
 */
.custom-tag-wrapper
  .custom-tag-content
    p
      font-size: 12px
