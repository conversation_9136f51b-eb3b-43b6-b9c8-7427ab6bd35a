import utils from './utils'

// 必填
const required = (rule, value, callback) => {
  if (Array.isArray(value)) { // 如果是数组
    if (value.length > 0) {
      callback()
    } else {
      callback(new Error('必填项目'))
    }
    return false
  }
  if (value !== '' && value !== null && value !== undefined) {
    callback()
  } else {
    callback(new Error('必填项目'))
  }
}

// 空数组
const requiredArr = (rule, value, callback) => {
  if (value !== '' && value !== null && value !== undefined && value.length !== 0 && (value.length && value[0] !== '' && value[1] !== '')) {
    callback()
  } else {
    callback(new Error('必填项目'))
  }
}

// 省市区必填
const requiredArea = (rule, value, callback) => {
  if (value.provincesId !== null && value.cityId !== null && value.areaId !== null && value.provincesId !== '' && value.cityId !== '' && value.areaId !== '') {
    callback()
  } else {
    callback(new Error('必填项目'))
  }
}

// 正整数【非必填】
const intTest = (rule, value, callback) => {
  let reg = /^[0-9]*[1-9][0-9]*$/
  if (reg.test(value) || value === '' || value === null || value === undefined) {
    callback()
  } else {
    callback(new Error('必须为正整数！'))
  }
}
// 中文、字母、数字、下划线、中划线
const isCommonName = (rule, value, callback) => {
  let reg = /^[a-zA-Z\u4e00-\u9fa5]+[a-zA-Z0-9_\-\u4e00-\u9fa5]+[a-zA-Z0-9\u4e00-\u9fa5]+$/
  if (!reg.test(value)) {
    callback(new Error('名称只能输入中文、字母、数字、下划线、中划线，中文或者字母开头，中文、字母、数字结尾'))
  } else {
    callback()
  }
}

// 正整数【非必填】
const intNumber01 = (rule, value, callback) => {
  let reg = /^([0]|[1-9][0-9]*)$/
  if (reg.test(value) || value === '' || value === null || value === undefined) {
    callback()
  } else {
    callback(new Error('必须为整数！'))
  }
}
// 整数【非必填】
const integerNumber = (rule, value, callback) => {
  let reg = /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/
  if (reg.test(value) || value === '' || value == 0 || value === null || value === undefined) {
    callback()
  } else {
    callback(new Error('必须为整数！'))
  }
}
// 正整数【必填】
const intNumber = (rule, value, callback) => {
  let reg = /^[0-9]*[1-9][0-9]*$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('必须为正整数！'))
  }
}

// 验证正整数+0
const checkIsPositiveInteger = (rule, value, callback) => {
  const reg = /^(0|[1-9][0-9]*)$/
  if (value === '' || value === null || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入大于等于0的整数'))
  }
}

// 验证正整数
const checkIsPositiveInteger2 = (rule, value, callback) => {
  const reg = /^([1-9][0-9]*)$/
  if (value === '' || value === null || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正整数'))
  }
}

// 数字和英文逗号
const intCommaValidate = (rule, value, callback) => {
  let reg = /^[0-9|,]*$/
  if (reg.test(value) || value === '') {
    callback()
  } else {
    callback(new Error('必须为数字,英文逗号！'))
  }
}

// 手机号码格式校验
const phoneTest = (rule, value, callback) => {
  // let reg = /^1[35847]\d{9}$/
  // let reg = /^[1]\d{10}$|^([5|6|9])\d{7}$|^[9]\d{8}$|^[6]\d{7}$/
  if (utils.phoneTest(value)) {
    callback()
  } else {
    callback(new Error('手机号码格式不正确'))
  }
}

// 富文本编辑器校验
const isEditorNull = (rule, value, callback) => {
  let reg = /^<p><br><\/p>$/g
  if (!reg.test(value) && value !== '') {
    callback()
  } else {
    callback(new Error('内容不能为空'))
  }
}
// 两位小数
const isPrice2 = (rule, value, callback) => {
  let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请填写正确的数字(最多两位小数)'))
  }
}
// 两位小数
const isPrice3 = (rule, value, callback) => {
  let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
  if (reg.test(value) && value != '' && value > 0) {
    callback()
  } else {
    callback(new Error('请填写正确的数字(最多两位小数且大于0)'))
  }
}
// 最多8位整数、2位小数
const price8 = (rule, value, callback) => {
  let reg = /^[0-9]{1,8}([.][0-9]{1,2})?$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请输入正确的数字(最多8位整数、2位小数)'))
  }
}
// 最多8位整数
const number8 = (rule, value, callback) => {
  let reg = /^[0-9]{1,8}$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请输入整数(最多8位)'))
  }
}
// 必须为18位数字或者大写英文字母
const isUnifiedCreditCode = (rule, value, callback) => {
  let reg = /^([A-Z0-9]){18}$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('必须为18位数字或者大写英文字母'))
  }
}
// 请输入正确的百分比比例(0~100)且为
const isPercent = (rule, value, callback) => {
  let reg = /^[1-9]*[1-9][0-9]*$|^[0]$/
  if (reg.test(value) || value === '' || value == 0 || value < 0 || value > 100) {
    callback()
  } else {
    callback(new Error('请输入正确百分比例（0~100）且为整数'))
  }
}
// 请输入正确的百分比比例(0~100)且为
const isPercent1 = (rule, value, callback) => {
  let reg = /^(?:0|[1-9][0-9]?|100)$/
  if (reg.test(value) || value === '') {
    callback()
  } else {
    callback(new Error('请输入正确百分比例（0~100）且为整数'))
  }
}
// 请输入正确的数字(最多一位小数且已.5结尾)isNumberdot5
const isNumberdot5 = (rule, value, callback) => {
  let reg = /^[1-9]+\d*(.[5]{1})?$|(^0\.[5]{1}$)/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请输入正确的数字(最多一位小数且已.5结尾)'))
  }
}
const isNumber3 = (rule, value, callback) => {
  let reg = /^(([1-9]+\d*)|([0-9]+\d*\.\d{1,3}))$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请输入正确的数字(最多三位小数)'))
  }
}
// 说明：1≤标签数≤5，2≤标签字数≤5
const max5tags = (rule, value, callback) => {
  let tagList = value.replace(/，/ig, ',').split(',')
  if (tagList.length == 0 || tagList.length > 3) {
    callback(new Error('说明：1≤标签数≤5'))
    return false
  }
  let words = true
  tagList.forEach((el, i) => {
    if (el.length > 5 || el.length < 2) {
      words = false
    }
  })
  if ((tagList.length > 0 || tagList.length === 0) && words) {
    callback()
  } else {
    callback(new Error('说明：1≤标签数≤5，2≤标签字数≤5'))
  }
}
// 大于等于0的两位小数
const price2 = (rule, value, callback) => {
  let reg = /^(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
  if (reg.test(value) || value === '') {
    callback()
  } else {
    callback(new Error('请填写两位小数的正数'))
  }
}
// 大于等于0的两位小数
const price5 = (rule, value, callback) => {
  let reg = /^(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
  if (value === '' || value === null || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写两位小数的正数'))
  }
}
// 大于0的两位小数[非必填]
const price01 = (rule, value, callback) => {
  let reg = /^(([1-9]+\d*)|([1-9]+\d*\.\d{1,2})|[0].[1-9]|[0].[0-9][1-9])$/
  if (reg.test(value) || value === '' || value === null || value === undefined) {
    callback()
  } else {
    callback(new Error('请填写两位小数的正数'))
  }
}
// 大于0的两位小数
const price02 = (rule, value, callback) => {
  let reg = /^(([1-9]+\d*)|([1-9]+\d*\.\d{1,2})|[0].[1-9]|[0].[0-9][1-9])$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写两位小数的正数'))
  }
}
// 请填写不大于1的小数,小数点后最多4位
const price00 = (rule, value, callback) => {
  let reg = /^(1|([0]+\d*\.\d{1,4}))$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写小于等于1的数值,最多4位小数'))
  }
}
// 小于1000万 两位小数
const price03 = (rule, value, callback) => {
  let reg = /^(([1-9]+\d*)|([1-9]+\d*\.\d{1,2})|[0].[1-9]|[0].[0-9][1-9])$/
  if (value < 1000000) {
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error('请填写两位小数的正数'))
    }
  } else {
    callback(new Error('价格应小于100万'))
  }
}
// 大于等于0的三位小数
const price3 = (rule, value, callback) => {
  // let reg = /^(([1-9]+\d*)|([1-9]+\d*\.\d{1,3})|[0].[0-9]\d*|[0].[0-9][0-9]\d*|[0].[0-9][0-9][0-9])$/
  let reg = /^(([1-9]+\d*)|([0-9]+\d*\.\d{1,3}))$/
  if (reg.test(value) || value === '' || value == 0) {
    callback()
  } else {
    callback(new Error('请填写三位小数的正数'))
  }
}
// 大于等于0的两位小数
const price4 = (rule, value, callback) => {
  if (value.indexOf('。') != -1) {
    callback(new Error('请填写正确的数字格式'))
  } else {
    let reg = /^(([1-9]+\d*)|([1-9]+\d*\.\d{1,2})|[0].[1-9]\d*|[0].[0-9][1-9])$/
    if (reg.test(value) || value === '' || value == 0) {
      callback()
    } else {
      callback(new Error('请填写两位小数的正数'))
    }
  }
}
// 最小1位，最多三位的正整数，不可以0开头
const mainBusinessCode = (rule, value, callback) => {
  let reg = /^[1-9]\d{0,2}$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('最多三位的正整数，不可以0开头'))
  }
}
// 一至三位正整数
const subBusinessCode = (rule, value, callback) => {
  let reg = /^\d{1,3}$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('最多三位正整数'))
  }
}
// 大于0的三位小数
const number3 = (rule, value, callback) => {
  let reg = /^(([1-9]+\d*)|([0-9]+\d*\.\d{1,3}))$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写三位小数的正数'))
  }
}
// 最多100万
const price100 = (rule, value, callback) => {
  if ((parseFloat(value) >= 0 && parseFloat(value) <= 1000000) || value === '') {
    callback()
  } else {
    callback(new Error('请填写两位小数的正数'))
  }
}

// 1-10
const isNumber1 = (rule, value, callback) => {
  let reg = /^[1-9]+(.[0-9]{0,2})?$/
  if (value > 1 && value < 10 && reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入1~10之间的数,最多2位小数'))
  }
}
// 0-100内的两位数
const isTax = (rule, value, callback) => {
  let reg = /^(([0-9]+\d*)|([0-9]+\d*\.\d{1,2}))$/
  if (value >= 0 && value <= 100 && reg.test(value)) {
    callback()
  } else {
    callback(new Error('税点值介于0到100'))
  }
}

const idCardPass = (rule, value, callback) => {
  let reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (value === '') {
    callback(new Error('身份证号不能为空'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else if (reg.test(value)) {
    callback()
  }
}
const mobile = (rule, value, callback) => {
  if (utils.mobile(value)) {
    callback()
  } else {
    callback(new Error('号码格式不正确'))
  }
}
const isEmail = (rule, value, callback) => {
  let reg = /\w@\w*\.\w/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确格式的电子邮件地址'))
  }
}
// number
const number = (rule, value, callback) => {
  let reg = /^[0-9]*$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入整数'))
  }
}
// 大于0的数字
const greaterZero = (rule, value, callback) => {
  let reg = /^([1-9]\d*(\.\d*[1-9])?$)|(0\.\d*[1-9])$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入大于零的数字'))
  }
}

// 非负浮点数 0 整数 小数
const nonnegativeFloat = (rule, value, callback) => {
  let reg = /^\d+(\.\d+)?$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的数字'))
  }
}

const passwordReg = (rule, value, callback) => {
  let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入8位以上密码，需含大小写字母'))
  }
}

const percent99 = (rule, value, callback) => {
  let reg = /^(0|([1-9]\d?))(\.[0-9]{1,2})?$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('范围0-99.99，最多两位小数'))
  }
}

// 价格最多输入8位整数
const maxPrice = (rule, value, callback) => {
  let reg = /^[0-9]{1,8}([.][0-9]{1,2})?$/
  if (reg.test(value) || value === '' || value === null || value === undefined) {
    callback()
  } else {
    callback(new Error('价格最大输入8位'))
  }
}

// 不为null
const notNull = (rule, value, callback) => {
  if (value !== null && value !== undefined) {
    callback()
  } else {
    callback(new Error('必填项目'))
  }
}

// 小于等于365正整数
const number365 = (rule, value, callback) => {
  const reg = /^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$/
  if (value === '' || value === null || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入小于等于365的正整数'))
  }
}
// 必须为20位以内数字或英文字母
const isUnifiedTaxNumber = (rule, value, callback) => {
  let reg = /^([a-zA-Z0-9]){1,20}$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('必须为20位以内数字或英文字母'))
  }
}
// 大于等于0,小于100,最多两位小数
const num0_100_2 = (rule, value, callback) => {
  let reg = /^(([0-9]{1,2})|([1-9]{1,2}\.\d{1,2})|[0]\.[0-9]{1,2})$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写0-100之间的数字，最多两位小数'))
  }
}
// 大于等于0,小于等于1,最多四位小数
const num0_1_4 = (rule, value, callback) => {
  let reg = /^(1|0|(0\.[0-9]{1,4}))$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请填写0-1之间的数字，最多四位小数'))
  }
}

export default {
  required,
  requiredArr,
  requiredArea,
  isCommonName,
  intTest,
  intNumber,
  intNumber01,
  checkIsPositiveInteger,
  checkIsPositiveInteger2,
  phoneTest,
  isEditorNull,
  isPrice2,
  isPrice3,
  isEmail,
  isPercent,
  isPercent1,
  price8,
  number8,
  isUnifiedCreditCode,
  price2,
  price00,
  price01,
  price02,
  price03,
  price3,
  price4,
  price5,
  isNumberdot5,
  isNumber3,
  max5tags,
  price100,
  isNumber1,
  isTax,
  idCardPass,
  mobile,
  number,
  greaterZero,
  nonnegativeFloat,
  number3,
  passwordReg,
  intCommaValidate,
  percent99,
  maxPrice,
  mainBusinessCode,
  subBusinessCode,
  notNull,
  number365,
  isUnifiedTaxNumber,
  integerNumber,
  num0_100_2,
  num0_1_4
}
