
import { isObject } from './validate.js'

const randomNo = (num) => {
  let rand = ''
  for(let i = 0; i < num; i++){
    let r = Math.floor(Math.random() * 10)
    rand += r
  }
  return rand
}

const timestampToTime = (timestamp) => {
  let date = new Date(timestamp)
  let Y = date.getFullYear()
  let M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1)
  let D = date.getDate()
  let h = date.getHours()
  let m = date.getMinutes()
  let s = date.getSeconds()
  let ms = date.getMilliseconds()
  return Y+M+D+h+m+s+ms
}

// 补零
const zerofill = (number) => {
  return number < 10 ? `0${number}` : number
}
// 某个日期（时间戳-毫秒）格式化; formater: 'YY-MM-DD hh:mm:ss'
const dateFormate = (ms, formater) => {
  if (!ms) return ''
  formater = formater || 'YY-MM-DD hh:mm:ss'
  const time = new Date(ms)
  const year = time.getFullYear()
  const month = zerofill(time.getMonth() + 1)
  const day = zerofill(time.getDate())
  const hour = zerofill(time.getHours())
  const minute = zerofill(time.getMinutes())
  const second = zerofill(time.getSeconds())
  return formater.replace('YY', year).replace('MM', month).replace('DD', day).replace('hh', hour).replace('mm', minute).replace('ss', second)
}
// 倒计时（时间戳-毫秒）
const countDown = (ms, formater) => {
  formater = formater || 'hh:mm:ss'
  let [hour, minute, second] = ['00', '00', '00']
  if (ms > 0) {
    const secondnum = parseInt(ms / 1000, 0)
    const day = Math.floor(secondnum / 86400)
    hour = zerofill(Math.floor((secondnum - day * 86400) / 3600))
    minute = zerofill(Math.floor((secondnum - day * 86400 - hour * 3600) / 60))
    second = zerofill(Math.floor(secondnum - day * 86400 - hour * 3600 - minute * 60))
  }
  return formater.replace('hh', hour).replace('mm', minute).replace('ss', second)
}
/**
 * [对queryObj的逆向操作。将query对象变成search字符串]
 * @param  {Object} obj [处理的目标对象]
 * @return {String}     [search字符串]
 */
const objToQuery = (obj) => {
  if (!isObject(obj)) {
    return ''
  }
  let arr = []
  Object.keys(obj).forEach((item, index, array) => {
    arr.push(`${item}=${obj[item]}`)
  })
  return arr.join('&')
}

const NoToChinese = (number) => {
  if (number && number > 0) {
    if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(number)) {
      return '数据非法'
    }
    let unit = '千百拾亿千百拾万千百拾元角分'
    let str = ''
    number += '00'
    let p = number.indexOf('.')
    if (p >= 0) {
      number = number.substring(0, p) + number.substr(p + 1, 2)
    }
    unit = unit.substr(unit.length - number.length)
    for (let i = 0; i < number.length; i++) {
      str += '零壹贰叁肆伍陆柒捌玖'.charAt(number.charAt(i)) + unit.charAt(i)
    }
    return str.replace(/零(千|百|拾|角)/g, '零').replace(/(零)+/g, '零').replace(/零(万|亿|元)/g, '$1').replace(/(亿)万|壹(拾)/g, '$1$2').replace(/^元零?|零分/g, '').replace(/元$/g, '元整')
  }
}

const NumberToChinese = (number) => {
  if (number && number > 0) {
    if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(number)) {
      return '数据非法'
    }
    let unit = '千百拾亿千百拾万千百拾元角分'
    let str = ''
    number += '00'
    let p = number.indexOf('.')
    if (p >= 0) {
      number = number.substring(0, p) + number.substr(p + 1, 2)
    }
    unit = unit.substr(unit.length - number.length)
    for (let i = 0; i < number.length; i++) {
      str += '零一二三四五六七八九'.charAt(number.charAt(i)) + unit.charAt(i)
    }
    return str.replace(/零(千|百|拾|角)/g, '零').replace(/(零)+/g, '零').replace(/零(万|亿|元)/g, '$1').replace(/(亿)万|一(十)/g, '$1$2').replace(/^元零?|零分/g, '').replace(/元$/g, '')
  }
}
// 控制文本长度
const cut = (value, wordwise, max, tail) => {
  if (!value) return ''
  max = parseInt(max, 10)
  if (!max) return value
  if (value.length <= max) return value
  value = value.substr(0, max)
  if (wordwise) {
    let lastspace = value.lastIndexOf(' ')
    if (lastspace != -1) {
      value = value.substr(0, lastspace)
    }
  }
  return value + (tail || ' …')// '...'可以换成其它文字
}
// 负数货币过滤器解决办法
const customCurrency = (amount) => {
  let value
  if (!amount == null || amount == 'null') {
    value = ''
    return ''
  } else {
    value = parseFloat(amount).toFixed(2)
    return `¥ ${value}`
  }
}

// 统一货币过滤器
const currency = (amount, currencySymbol) => {
  // 价格屏蔽世界返回
  if (typeof (amount) == 'string' && amount.indexOf('*') != -1) {
    return amount
  }
  if (!amount || isNaN(Number(amount))) return currencySymbol ? currencySymbol + '0.00' : '0.00'

  let intPart = parseInt(amount)
  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
  let negative0Flag = false
  if (intPart.toString() == 0) { // 处理 0 -0
    negative0Flag = negative0(intPart)
  }
  intPartFormat = negative0Flag ? '-' + intPartFormat : intPartFormat
  // 判断是否是“-0”
  function negative0 (num) {
    return num == 0 && 1 / num < 0
  }
  let floatPart = '.00'
  let value2Array = (amount + '').split('.')

  if (value2Array.length == 2) {
    floatPart = value2Array[1].toString()
    if (floatPart.length == 1) {
      return currencySymbol ? currencySymbol + intPartFormat + '.' + floatPart + '0' : intPartFormat + '.' + floatPart + '0'
    } else if (floatPart.length > 2) {
      let sFloat = (parseFloat('0.' + floatPart).toFixed(2)).split('.')[1]
      return currencySymbol ? currencySymbol + intPartFormat + '.' + sFloat : intPartFormat + '.' + sFloat
    } else {
      return currencySymbol ? currencySymbol + intPartFormat + '.' + floatPart : intPartFormat + '.' + floatPart
    }
  } else {
    return currencySymbol ? currencySymbol + intPartFormat + floatPart : intPartFormat + floatPart
  }
}

// 从时间字符串中返回年月日，如'2016年3月21日 14时30分28秒'返回'2016年3月21日'
const yearMonthDay = (input) => {
  input = input || ''
  input = input.trim()
  return input.match(/(^[^\s]+)\s/) && input.match(/(^[^\s]+)\s/)[1]
}
// 从对象数组中删除重复的对象，id为对象属性名，如果有两个对象的该属性值相等，则认为重复，后一个会被删除
const noRepeat = (input, id) => {
  input = input.filter(function (e, i, arr) {
    let test = arr.slice(0, i).every(function (el) {
      return (el[id] !== e[id])
    })
    if (test) {
      return e
    }
  })
  return input
}

// 大于0的整数
const pInteger = (number) => {
  let posReg = /^[1-9]\d*$/
  return posReg.test(number)
}
// 保留两位小数
const number2 = (number) => {
  if (!number) {
    return 0
  }
  number = number * 1
  return number.toFixed(2)
}
// 保留整数
const number0 = (number) => {
  if (!number) {
    return 0
  }
  return number.toFixed(0)
}
// 精确到百位（不做四舍五入）
const number100 = (number) => {
  if (!number) {
    return 0
  }
  return Math.floor(number / 100) * 100
}

/**
 * [手机号码校验]
 * @param  {[Number/"Number"]} phoneNum [手机号码]
 * @return {[Boolean]}     [符合规则返回ture，否在false]
 */
const phoneTest = (phoneNum) => {
  let reg = /^[1]\d{10}$|^([5|6|9])\d{7}$|^[9]\d{8}$|^[6]\d{7}$/
  return reg.test(phoneNum)
}

/**
 * [校验手机号码和固话]
 */
const mobile = (mobileNum) => {
  let reg = /^[1]\d{10}$|^([5|6|9])\d{7}$|^[9]\d{8}$|^[6]\d{7}$/
  let reg1 = /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/
  return reg.test(mobileNum) || reg1.test(mobileNum)
}

const formatArr = function (key, val, arr) {
  arr.length && arr.map(function (_item) {
    _item.label = _item[key]
    _item.value = _item[val]
    _item.multiple = true
  })
  return arr
}

/**
 * 获取url中参数
 */
const getQueryString = function (name) {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  if (window.location.href.split('?')[1]) {
    let r = window.location.href.split('?')[1].match(reg)
    if (r != null) {
      return unescape(r[2])
    }
  }
  return null
}
const getCurrentYear = () => {
  const data = new Date()
  return data.getFullYear()
}
// 二进制图片转换
const dataURItoBlob = (base64Data) => {
  let byteString
  if (base64Data.split(',')[0].indexOf('base64') >= 0) {
    byteString = atob(base64Data.split(',')[1])
  } else {
    byteString = unescape(base64Data.split(',')[1])
  }
  let mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]
  let ia = new Uint8Array(byteString.length)
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i)
  }
  return new Blob([ia], { type: mimeString })
}
// 将一个对象部分属性复制到另一个对象
const pickProperty = (obj, arr) => {
  let newObj = arr.reduce((iter, val) => {
    if (val in obj) {
      iter[val] = obj[val]
    }
    return iter
  }, {})
  return newObj
}

// 深拷贝
function deepCopy (obj, cache = []) {
  // just return if obj is immutable value
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  // if obj is hit, it is in circular structure
  const hit = find(cache, c => c.original === obj)
  if (hit) {
    return hit.copy
  }

  const copy = Array.isArray(obj) ? [] : {}
  // put the copy into cache at first
  // because we want to refer it in recursive deepCopy
  cache.push({
    original: obj,
    copy
  })

  Object.keys(obj).forEach(key => {
    copy[key] = deepCopy(obj[key], cache)
  })

  return copy
}
export default {
  randomNo,
  timestampToTime,
  dateFormate,
  countDown,
  objToQuery,
  NoToChinese,
  NumberToChinese,
  cut,
  customCurrency,
  currency,
  yearMonthDay,
  noRepeat,
  number2,
  number0,
  number100,
  phoneTest,
  mobile,
  formatArr,
  pInteger,
  getQueryString,
  getCurrentYear,
  dataURItoBlob,
  pickProperty,
  deepCopy
}
