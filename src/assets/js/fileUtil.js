import Vue from 'vue'
import streamSaver from 'streamsaver'

let fileProDialog = {}
export default {
  // 获取文件名不带后缀
  getFileName(fileName){
    let pattern = /\.{1}[a-z]{1,}$/
    if (pattern.exec(fileName) !== null) {
      return (fileName.slice(0, pattern.exec(fileName).index))
    } else {
      return fileName
    }
  },
  // 获取文件后缀，不带.
  getFileSuffix(fileName) {
    if(fileName===null&&fileName===''){
      return ''
    }
    let fileNameArr = fileName.split('.')
    if(fileNameArr.length >1){
      return fileNameArr[fileNameArr.length-1]
    }else{
      return ''
    }
  },
  //获取文件后缀，带.
  getFileSuffixWithSpot(fileName) {
    if(fileName===null&&fileName===''){
      return ''
    }
    let fileNameArr = fileName.split('.')
    if(fileNameArr.length >1){
      return '.'+fileNameArr[fileNameArr.length-1]
    }else{
      return ''
    }
  },
  /**
   * 下载
   * @param  {String} url 目标文件地址
   * @param  {String} filename 想要保存的文件名称
   */
  download(url, filename,proDialog) {
    fileProDialog= proDialog
    fileProDialog.proDialogVisible=true
    getBlob(url).then((blob) => {
      saveAs(blob, filename)
      fileProDialog.proDialogVisible=false
    })
  },

  /**
   * 通过a标签访问地址下载文件
   * @param  {String} url 目标文件地址
   * @param  {String} filename 想要保存的文件名称
   */
  downloadFile (url, filename) {
    if (url !== undefined && url !== null && !url.startsWith('blob') && !url.startsWith('https://dobs-hw0006-xaxiz-ai20')) {
      url = url + '?response-content-type=application/octet-stream'
    }
    const ele = document.createElement('a')
    ele.setAttribute('href', url)
    ele.setAttribute('download', filename) // 用于设置下载文件的文件名
    ele.setAttribute('target', '_blank')
    ele.click()
  },

  handleDownLoad(url, fileName) {
    Vue.prototype.$get(url, null)
      .then(data => {
        const binaryData = []
        binaryData.push(data)
        const blobUrl = window.URL.createObjectURL(new Blob(binaryData))
        this.downloadFile(blobUrl, fileName)
      })
  },

  /**
   * 通过streamSaver流的方式下载oss文件
   * @param url
   * @param fileName
   */
  async down (url, fileName) {
    if (!streamSaver.WritableStream) {
      const pony = await import('web-streams-polyfill/ponyfill')
      streamSaver.WritableStream = pony.WritableStream
    }

    // 修改插件中的默认值改为前端项目地址
    streamSaver.mitm = `${process.env.VUE_APP_WEB_URL}/mitm.html`
    fetch(url).then(res => {
      const fileStream = streamSaver.createWriteStream(fileName)
      const readableStream = res.clone().body
      // more optimized
      if (window.WritableStream && readableStream.pipeTo) {
        return readableStream.pipeTo(fileStream)
          .then(() => console.log('done writing'))
      }
      window.writer = fileStream.getWriter()
      const reader = res.body.getReader()
      const pump = () => reader.read()
        .then(res => res.done
          ? window.writer.close()
          : window.writer.write(res.value).then(pump))
      pump()
    })
  }
}

/**
 * 获取 blob
 * @param  {String} url 目标文件地址
 * @return {Promise}
 */
function getBlob(url) {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()
    xhr.onprogress = onProgress //下载监听
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      }
    }
    xhr.send()
  })
}

/**
 * 保存
 * @param  {Blob} blob
 * @param  {String} filename 想要保存的文件名称
 */
function saveAs(blob, filename) {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    const body = document.querySelector('body')

    link.href = window.URL.createObjectURL(blob)
    link.download = filename

    // fix Firefox
    link.style.display = 'none'
    body.appendChild(link)

    link.click()
    body.removeChild(link)

    window.URL.revokeObjectURL(link.href)
    Vue.prototype.$message.success('文件下载成功')
  }
}

function onProgress(e) {
  if (e.lengthComputable) {
    fileProDialog.percent = Math.round(e.loaded * 100 / e.total)
  }
}
