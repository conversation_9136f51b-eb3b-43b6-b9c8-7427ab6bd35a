import Vue from 'vue'

export default {
  //获得签名
  async getFileSign(file) {
    try {
      let res = await Vue.prototype.$axios.post(
        Vue.prototype.baseUrl + '/file/sign',
        {
          fileType: Vue.prototype.$fileUtil.getFileSuffix(file.name),
        }
      )
      return res
    } catch (e) {
      Vue.prototype.$message.error('获取签名出错！')
    }
  },

  //保存文件信息
  async saveFileInfo(file, fileKey) {
    const fileName = Vue.prototype.$fileUtil.getFileName(file.name)
    const fileSize = file.size / 1024
    const fileType = Vue.prototype.$fileUtil.getFileSuffixWithSpot(
      file.name
    )
    let res = await Vue.prototype.$axios.post(
      Vue.prototype.baseUrl + '/file/add',
      {
        fileKey: fileKey,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
      }
    )
    return res
  },
  //保存文件信息
  async saveFileInfoOBS(file, fileKey) {
    const fileName = Vue.prototype.$fileUtil.getFileName(file.name)
    const fileSize = file.size / 1024
    const fileType = Vue.prototype.$fileUtil.getFileSuffix(
      file.name
    )
    let res = await Vue.prototype.$axios.post(
      Vue.prototype.baseUrl + '/file/add',
      {
        fileKey: fileKey,
        fileName: fileName,
        fileSize: fileSize,
        fileType: '.'+fileType,
        storagePlatform: 'Obs'
      }
    )
    return res
  },
  //下载文件
  downloadModel(fileId) {
    Vue.prototype.$axios
      .post(Vue.prototype.baseUrl + '/file/info', {
        id: fileId,
      })
      .then((res) => {
        if (res.data.status === 200) {
          let downloadUrl = res.data.data.url
          let filename = res.data.data.fileName + res.data.data.fileType
          const loading = Vue.prototype.$loading({
            lock: true,
            background: 'rgba(0, 0, 0, 0.7)',
          })
          Vue.prototype.$fileUtil.download(downloadUrl, filename, loading)
        }
      })
      .catch((err) => {
        Vue.prototype.$message.error(err)
      })
  },
  getParams(data) {
    let uploadParam = {
      key: data.key,
      accessKeyId: data.accessKeyId,
      signature: data.signature,
      policy: data.policy,
    }
    return uploadParam
  },
  getUrl(data) {
    const bucket = data.bucket
    const zone = data.bucketZone
    const ossUrl = data.ossUrl
    const urlArr = ossUrl.split('//')

    let uploadUrl = `${urlArr[0]}//${bucket}.${zone}.${urlArr[1]}`
    return uploadUrl
  },
  async submitAddFile(file) {
    let data = await getFileSign()
    let url = getUrl(data)
    let params = getParams(data)
    let formData = new FormData()
    Object.keys(params).forEach(function (key) {
      formData.append(key, obj[key])
    })
    formData.append('file', file)
    let config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
    Vue.prototype.axios.post(url, formData, config).then((response) => {
      if (response.data.info == 'success') {
        const fileName = Vue.prototype.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = Vue.prototype.$fileUtil.getFileSuffixWithSpot(
          file.raw.name
        )
        const fileKey = params.key
        Vue.prototype.$axios
          .post(Vue.prototype.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
          })
          .then((res) => {
            if (res.data.status === 200) {
              const fileId = res.data.data.fileId
              console.log(fileId)
              Vue.prototype.localFileId = fileId
              Vue.prototype.modelAdd(fileId, fileName + fileType)
            }
          })
        Vue.prototype.$message({
          type: 'success',
          message: '附件上传成功!',
        })
      }
    })
  },
}
