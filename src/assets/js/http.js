function randomString(e) {
  e = e || 32
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const a = t.length;
    let n = ''
  for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}

const install = function (Vue, opts = {}) {
  const err = (errdata, reject, err) => {
    if (err) {
      err(errdata)
    } else {
      // if (Message) Message.error(errdata.data.msg)
      // =>to公用全局处理方法
      // Vue.prototype.$message.closeAll()
      // Vue.prototype.$message.error(errdata.msg);
      reject(errdata)
    }
  }

  const http = (data, errf) => {
    return new Promise(function (resolve, reject) {
      Vue.prototype.$axios(data)
        .then(res => {
          if (!res.data.errcode) {
            resolve(res.data)
          } else {
            err(res.data, reject, errf)
          }
        })
        .then(errs => {
          err(errs, reject, errf)
        })
    })
  }

  const $post = (url, data = {}, errf) => {
    const baseURL = process.env.VUE_APP_API
    return new Promise(function (resolve, reject) {
      Vue.prototype.$axios({
        method: 'post',
        url,
        data,
        baseURL
      })
        .then(res => {
          if (res.data.status === 200) {
            resolve(res.data.data)
          } else if(res.data.status === 500){
            // Vue.prototype.$message.error(res.data.serverErrorMsg)
            reject(res.data.data)
          } else {
            err(res, reject, errf)
          }
        })
        .catch(errData => {
          err(errData, reject, errf)
        })
    })
  }
  const $get = (url, data = {}, errf) => {
    const baseURL = process.env.VUE_APP_API
    data = {loading: false, ...data}
    const loading = data.loading === true ? Vue.prototype.$loading(data.loading) : {
      close() {
      }
    }
    delete data.loading
    return new Promise(function (resolve, reject) {
      Vue.prototype.$axios({
        method: 'get',
        url: baseURL + url,
        params: data
      })
        .then(function (response) {
          loading.close()
          if (response.status == 200) {
            resolve(response.data)
          } else {
            err(response, reject, errf)
          }
        })
        .catch(function (error) {
          loading.close()
          err(error, reject, errf)
        })
    })
  }
  Vue.prototype.$get = $get
  Vue.prototype.$post = $post
}
export default {
  install
}
