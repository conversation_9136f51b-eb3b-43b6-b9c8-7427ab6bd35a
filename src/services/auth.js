import '../public-path'
import EnnewInit from '@enn/ennew-sdk'

// 定义 getEnnAccountInfo 函数
async function getEnnAccountInfo(userInfo) {
  const url = process.env.VUE_APP_AGENT_API.startsWith('/') ?
    window.location.origin + process.env.VUE_APP_AGENT_API + "/api/auth/ennAccountInfo" :
    process.env.VUE_APP_AGENT_API + "/api/auth/ennAccountInfo"

  try {
    const response = await window.Vue.prototype.$axios.get(url, {
      params: {
        user_id: userInfo.userId
      }
    });
    return response.data;
  } catch (err) {
    console.log("请求发生错误", err);
    return null;
  }
}

export class AuthService {
  static instance = null
  static ennewsdk = null
  static authsdk = null
  static userInfo = null

  static async initialize(isShow = false) {
    if (this.instance) {
      return this.instance
    }
    console.log('实例',process.env)
    try {
      this.ennewsdk = await new EnnewInit({
        projectName: '能力生产平台',
        authInfo: {
          appid: process.env.VUE_APP_AUTH_ID,
          baseUrl: process.env.VUE_APP_AUTH_URL,
          env: process.env.VUE_APP_AUTH_ENV,
          accessKey: process.env.VUE_APP_GATEWAY_KEY,
          removeQueryAfterAuth: true,
          withTempAuthCode: true,
          tenantPageName: 'ennew',
          whiteList: [
            'https://rdfa-gateway.ennew.com/algorithm/platform/conf/getApolloVal?key=zhuge.web.key',
            'https://rdfa-gateway.fat.ennew.com/algorithm/platform/conf/getApolloVal?key=zhuge.web.key',
            'https://rdfa-gateway.uat.ennew.com/algorithm/platform/conf/getApolloVal?key=zhuge.web.key',
          ]
        },
        isShow,
        isShowSideMenu: true,
        isClickProjectName: true
      })

      const { authsdk, userInfo } = await this.ennewsdk.init()
      this.authsdk = authsdk
      this.userInfo = userInfo

      await this.setUserInfo(userInfo)

      this.instance = this
      return this
    } catch (error) {
      console.error('Auth initialization failed:', error)
      throw error
    }
  }

  static async setUserInfo(userInfo) {
    const ennAccountInfo = await getEnnAccountInfo(userInfo)

    // 存储用户信息
    sessionStorage.setItem('USER_INFO', JSON.stringify(userInfo))
    if (ennAccountInfo) {
      sessionStorage.setItem('ACCOUNT_INFO', JSON.stringify(ennAccountInfo))
    } else {
      sessionStorage.removeItem('ACCOUNT_INFO')
    }

    // 设置其他用户相关信息
    sessionStorage.setItem('userId', userInfo.userId)
    sessionStorage.setItem('LOGIN_Name', userInfo.nickName || userInfo.nickname)
    sessionStorage.setItem('openId', userInfo.userId)
    sessionStorage.setItem('loadItcode', userInfo.loginName)

    // 初始化埋点
    this.initializeTracking(userInfo)
  }

  static initializeTracking(userInfo) {
    const queryTenantId = userInfo.tenantId
    const storageTenantId = sessionStorage.getItem('authTenantId')
    let loadTenantId = null
    if (queryTenantId && queryTenantId !== storageTenantId) {
      // URL带了参数缓存里没有或者不同
      loadTenantId = queryTenantId
      sessionStorage.setItem('authTenantId', queryTenantId)
    } else {
      loadTenantId = storageTenantId
    }
    if (!userInfo.loginName) return

    let workspaceName = ''
    try {
      workspaceName = JSON.parse(localStorage.getItem('currentWorkSpace'))?.workspaceName
    } catch (error) {
      console.error('Failed to parse workspace:', error)
    }
  }

  static async logout(redirectUrl = window.location.origin + '/#/homePage') {
    localStorage.removeItem('currentWorkSpace')
    sessionStorage.clear()
    return this.authsdk.logout(redirectUrl)
  }

  static async selectTenant(redirectUrl) {
    return this.authsdk.selectTenant(redirectUrl)
  }

  static getAuthSDK() {
    return this.authsdk
  }

  static getUserInfo() {
    return this.userInfo
  }
}

export default AuthService
