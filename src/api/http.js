import axios from 'axios'
import Vue from 'vue'
import { router } from '@/main'

import errorResponse<PERSON>and<PERSON> from './errorResponseHandler'
const store = require('../store').default
// 获取地址栏中的参数
function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  const search = window.location.search.substr(1);
  const r = search.match(reg);
  if (r != null) {
      return decodeURIComponent(r[2]);
  }
  return null;
}

// 使用示例
// const id = getQueryString("id");

let workspaceId = ''
function getWsID () {
  // console.log('ceshi', router?.currentRoute?.query)
  if (store?.state.workSpace.currentWorkSpace.workspaceId) {
    workspaceId = store?.state.workSpace.currentWorkSpace.workspaceId
  } else {
    workspaceId = router?.currentRoute?.query.workspaceId
  }
  if(!workspaceId) {
    try {
      const [hash, query] = window.location.href.split('#')[1].split('?')
      const params = Object.fromEntries(new URLSearchParams(query))
      workspaceId = params.workspaceId
    } catch (error) {
      console.log('error', error)
    }
  }
  // if (localStorage.getItem('currentWorkSpace')) {
  //   const workSpace = JSON.parse(localStorage.getItem('currentWorkSpace'))
  //   workspaceId = workSpace.id
  // }
  return workspaceId
}

// const { flag, id, roleType, workspaceName } = store.state.workSpace.currentWorkSpace
// timeout 临时修改
const service = axios.create({
  baseURL: process.env.VUE_APP_API,
  // timeout: 1000 * 30,
  timeout: 180000, // 设置请求超时时间为3分钟
  headers: {
    'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY,
    workspaceId: getWsID()
  }
})

service.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
// 设置全局的请求次数，请求的间隙
service.defaults.retry = 1
service.defaults.retryDelay = 500
// http request 拦截器
service.interceptors.request.use(
  (config) => {
    config.headers.workspaceId = getWsID()
    // console.log('请求信息', config)
    return config
  },
  (err) => {
    return Promise.reject(err)
  }
)

// 标记提示被踢出的数量
const tickoutTimer = 0
// 禁用的空间 普通用户

// http response 拦截器
service.interceptors.response.use(
  (response) => {
    if (response.data.status === 417) {
      // 说明该用户没有用户角色，需要登出
      const routeName = router.currentRoute.name
      if (routeName !== 'guest') {
        window.location = '/#/guest'
      }
    }
    // 手机端展示的时候去掉message弹窗提示
    if (response.data.status !== 200 && response.data.status !== 0) {
      // console.log(response)
      // if (response.data.status === 50001) {
      //   //gpu资源不足
      //   Vue.prototype.$message.error(response.data.msg)
      //   return
      // } else

      if (response.data.status === 50002) {
        // 表示当前用户已久不在当前工作空间 需提示用户 && 刷新当前页面
        window.location.href = window.location.origin +'/#/homePage'
        // if (location.hash != '#/workSpace/index') {
        //   tickoutTimer++

        //   tickoutTimer === 1 &&
        //     Vue.prototype.$message.warning({
        //       message: '检测到您已经不所属当前工作空间！',
        //       offset: 40,
        //       onClose: () => {
        //         window.location.href = window.location.origin+'/#/workSpace/index'
        //       }
        //     })
        // }
      } else if (response.data.status === 50003) {
        errorResponseHandler.deduplicateErrorResp(response)
      } else if (response.data.status === 50004) {
        // 表示当前管理者所属空间被禁用 不可修改 删除 && 刷新当前页面
        errorResponseHandler.deduplicateErrorResp(response)
      } else if (response.data.status === 4171) {
        /**
         * 没有接口访问权限，后端返回4171
         * 前端跳转到无权限页面
         */

        router.push({
          replace: true,
          name: 'unauthorized'
        })

        // return Promise.reject('无权限路由')
      } else if (response.data.status === 500) {
        Vue.prototype.$message.error(response.data?.msg || response.data?.serverErrorMsg)
      } else {

      }
    }
    return response
  },
  (error) => {
    console.log(error.message)
    console.log(error.code)
    console.log(error)
    if (error.code === 'ECONNABORTED' && error.message === 'Request aborted') {
      Vue.prototype.$message.error({
        message: '请求取消，请刷新页面重试'
      })
    } else {
      if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
        Vue.prototype.$message.error({
          message: '接口调用超时，请稍后重试！---'
        })
      }
    }

    return Promise.reject(error)
  }
)

export default service
