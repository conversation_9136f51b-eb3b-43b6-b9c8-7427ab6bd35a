import { apiPath } from '@/utils/apiPath.js'
import service from '@/axios'

export const baseUrl = apiPath.baseUrl
export const uacUrl = apiPath.uacUrl

// 告警-当月告警
export function currentMonthCount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/currentMonthCount',
    params
  })
}

// 告警-告警表格
export function latestAlert (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/latestAlert',
    params
  })
}
// 告警-告警次数统（近7日）
export function recentDaysCount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/recentDaysCount',
    params
  })
}

// 告警-当月告警-累计
export function totalCount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/totalCount',
    params
  })
}
// 摄像头-接入摄像头合计
export function getAmount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/amount',
    params
  })
}
// 能力-能力调用统计（合计）
export function callAmount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/callAmount',
    params
  })
}
// 能力-当月能力调用
export function currentMonthCallCount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/currentMonthCallCount',
    params
  })
}

// 能力-能力发布次数（每月）
export function publishPerMonth (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/publishPerMonth',
    params
  })
}

// 能力-调用成功率（每月）
export function successRatePerMonth (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/successRatePerMonth',
    params
  })
}

// 能力-能力耗时统计（近7日）
export function timeConsuming (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/timeConsuming',
    params
  })
}

// 能力-生产能力合计
export function totalAmount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/totalAmount',
    params
  })
}
// 能力-当月能力调用-累计
export function totalCallCount (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/totalCallCount',
    params
  })
}

// 资源占用监控
export function monitor (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/monitor',
    params
  })
}
// 资源占用监控
export function health (params) {
  return service({
    method: 'get',
    url: '/linkong/home/<USER>/health',
    params
  })
}
