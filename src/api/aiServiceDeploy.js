import service from '@/axios'
import { apiPath } from '@/utils/apiPath.js'

export const baseUrl = apiPath.baseUrl
export const uacUrl = apiPath.uacUrl

// ------算法服务详情 -----

// 查询指定算法服务实例列表
export function podList (params) {
  return service({
    method: 'post',
    url: '/aiServiceDeploy/podList',
    data: params
  })
}

// 查询指定算法服务实例信息列表
export function podInfoList (params) {
  return service({
    method: 'post',
    url: '/aiServiceDeploy/podInfoList',
    data: params
  })
}

// 根据id查询事件列表
export function eventPageList (params) {
  return service({
    method: 'post',
    url: '/aiServiceDeploy/eventPageList',
    data: params
  })
}

// cpu使用时间线
export function getCpuUsage (params) {
  return service({
    method: 'post',
    url: '/resource/display/getCpuUsage',
    data: params
  })
}

// 内存使用时间线

export function getMemoryUsage (params) {
  return service({
    method: 'post',
    url: '/resource/display/getMemoryUsage',
    data: params
  })
}

// GPU显存用量时间线
export function getGpuMemoryUsage (params) {
  return service({
    method: 'post',
    url: '/resource/display/getGpuMemoryUsage',
    data: params
  })
}
// GPU
export function getGpuUtilizationUsage (params) {
  return service({
    method: 'post',
    url: '/resource/display/getGpuUtilizationUsage',
    data: params
  })
}

// 日志文件列表
export function listLogFile (params) {
  return service({
    method: 'post',
    url: '/aiServiceDeploy/logFile/list',
    data: params
  })
}



export function getAlgoAbiList(params) {
  return service({
    method: 'post',
    url: '/aiService/agentAbility/getPageList',
    data: params
  });
}
// 获取agent能力信息
export function getAgentAbility(params) {
  return service({
    method: 'post',
    url: '/aiService/agentAbility/get',
    data: params
  });
}


export function queryAlgoConfigInfo(params) {
  return service({
    method: 'post',
    url: '/aiConfig/queryConfigInfo',
    data: params
  });
}
// 注册agent能力
export function addAgentAbility(params) {
  return service({
    method: 'post',
    url: '/aiService/agentAbility/add',
    data: params
  });
}


// 下线agent能力
export function offlineAgentAbility(params) {
  return service({
    method: 'post',
    url: '/aiService/agentAbility/offline',
    data: params
  });
}

//查询服务的请求body信息
export function getAiServiceParam(params) {
  return service({
    method: 'post',
    url: '/aiServiceParam/get',
    data: params
  });
}

//查询服务的应用授权列表
export function getAppListByServiceId(params) {
  return service({
    method: 'post',
    url: '/aiApp/queryAppListByServiceId',
    data: params
  });
}

