import service from '@/axios'

// 首页-最近登录空间
export function queryLatestWorkspace(params) {
  return service({
    method: 'get',
    url: '/overview/getLatestWorkspace',
    params
  })
}
// 首页-最近登录工程
export function queryLatestProject(params) {
  return service({
    method: 'get',
    url: '/overview/project/getLatestProject',
    params
  })
}
// 首页-模型工程列表
export function queryBasic(params) {
  return service({
    method: 'post',
    url: '/overview/project/queryBasic',
    data: params
  })
}
// 首页-工作空间列表
export function queryWorkspaceList(params) {
  return service({
    method: 'post',
    url: '/workspace/member/getWorkspaceList',
    data: params
  })
}
export function queryMemberSortedWorkspaceList(params){
  return service({
    method: 'post',
    url: '/workspace/member/getMemberSortedWorkspaceList',
    data: params
  })
}
// 首页-统计
export function queryStatistics(params) {
  return service({
    method: 'get',
    url: '/overview/statistics/resourceUsed',
    params
  })
}
// 模型工程-模型列表
export function queryProjectList(params) {
  return service({
    method: 'post',
    url: '/overview/project/getProjectList',
    data: params
  })
}
export function getIsAdminForCurUser() {
  return service({
    method: 'post',
    url: '/user/isAdminForCurUser',
  })
}
