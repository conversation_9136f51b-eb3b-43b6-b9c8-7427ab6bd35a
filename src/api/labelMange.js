// 标签组管理接口
import service from '@/axios'

// 获取数据集标签组列表
export function getList (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getList',
    data: params
  })
}
// 获取某个标签组信息
export function getOne (params) {
  return service({
    method: 'get',
    url: '/cvDataSetMarkGroup/getOne',
    params
  })
}
// 删除数据集标签组
export function deleteGroup (id) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/delete',
    data: id
  })
}
// 获取下载标签组信息
export function getDownload (params) {
  return service({
    method: 'get',
    url: '/cvDataSetMarkGroup/getDownloadInfo',
    params: params
  })
}
// 启用数据集标签组
export function openStatus (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/status/open',
    data: params
  })
}
// 关闭数据集标签组
export function closeStatus (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/status/close',
    data: params
  })
}
// 追加导入
export function appendImport (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/appendImport',
    data: params
  })
}
// 格式转换
export function markTypeConvert (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/markTypeConvert',
    data: params
  })
}
// 新增数据集标签组
export function addDataSetMarkGroup (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/add',
    data: params
  })
}
// 获取某个标签组下图片列表
export function getOneImagePage (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getOneImagePage',
    data: params
  })
}
// 获取某个标签组下标签统计
export function getOneLabelsStat (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getOneLabelsStat',
    data: params
  })
}
// 根据标签id获取此标签关联的标注图片list
export function getImageListByTagId (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getImageListByTagId',
    data: params
  })
}
// 校验标签组名称重复
export function checkRepeatGroupName (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/checkRepeatGroupName',
    data: params
  })
}
// 获取图片数量
export function countImageType (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/countImageType',
    data: params
  })
}
// 获取指定数据集的相关所有状态值
export function getGroupingTaskStatus (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getGroupingTaskStatus',
    data: params
  })
}

// 获取某个标签组下标签列表(含有零)
export function getLabelsStat (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/getLabelsStat',
    data: params
  })
}
// 新增标签组标签
export function addTag (params) {
  return service({
    method: 'post',
    url: '/cvDataSetMarkGroup/addTag',
    data: params
  })
}

// 保存标注
export function saveMark (params) {
  return service({
    method: 'post',
    url: '/cvDataSetImageMark/saveMark',
    data: params
  })
}

// 获取标签列表
export function getTagList (params) {
  return service({
    method: 'get',
    url: '/tag/library/get',
    params: params
  })
}
// 新增标签接口
export function addTagLibrary (params) {
  return service({
    method: 'post',
    url: '/tag/library/add',
    data: params
  })
}
// 新增标签接口
export function getBusinessList (params) {
  return service({
    method: 'get',
    url: `/tag/library/getByBusinessId/${params.businessId}/${params.businessId2}`,
    params
  })
}
// 单标签绑定状态修改
export function updateOne (params) {
  return service({
    method: 'post',
    url: '/tag/library/rel/singleTag/update',
    data: params
  })
}
