import { router } from '@/main'
import service from '@/axios'
const store = require('../store').default
let workspaceId = ''
function getWsID () {
  // console.log('ceshi', router?.currentRoute?.query)
  if (store?.state.workSpace.currentWorkSpace.workspaceId) {
    workspaceId = store?.state.workSpace.currentWorkSpace.workspaceId
  } else {
    workspaceId = router?.currentRoute?.query.workspaceId
  }
  if(!workspaceId) {
    try {
      const [hash, query] = window.location.href.split('#')[1].split('?')
      const params = Object.fromEntries(new URLSearchParams(query))
      workspaceId = params.workspaceId
    } catch (error) {
      console.log('error', error)
    }
  }
  return workspaceId
}
function getUserInfo () {
  let userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}
  if (Object.keys(userInfo).length) {
    // console.log('--信息---', userInfo);
    return userInfo;
  } else {
    // console.log('----$ennewsdk----', Vue.prototype.$ennewsdk);
    const temp = Vue.prototype.$ennewsdk?.getUser().then(res => {return res;});
    userInfo = temp;
    console.log('信息---', userInfo, 'temp', temp);
    return userInfo;
  }
  
}


// 规则集市列表
export function ruleMarketListApi (params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
    method: 'post',
    url: '/rule/query_page',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || '',
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 规则集市 全部&我的数量统计
export function ruleMarketCountApi(params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
      window.location.href = window.location.origin + '/#/homePage'
    }
    return service({
      method: 'post',
      url: '/rule/query_count',
      baseURL: process.env.VUE_APP_PLAN_API,
      data: {
        header: {
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
        },
        data: params || {}
      }
    })
}
// 发布历史列表记录
export function ruleMarketPublishListApi(params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
      method: 'post',
      url: '/rule/version/list',
      baseURL: process.env.VUE_APP_PLAN_API,
      headers: {
        affinitycode: userInfo.userId || '',
      },
      data: {
        header:{
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
        },
        data:params||{}
      }
    })
}
// 版本上线
export function onLineStatus(params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
      method: 'post',
      url: '/rule/version/online',
      baseURL: process.env.VUE_APP_PLAN_API,
      headers: {
        affinitycode: userInfo.userId || '',
      },
      data: {
        header:{
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
        },
        data:params||{}
      }
    })
  }
  
  // 版本下线
export function offlineStatus(params) {
const userInfo = getUserInfo()
const workspaceId = getWsID();
if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
}
return service({
    method: 'post',
    url: '/rule/version/offline',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
    affinitycode: userInfo.userId || '',
    },
    data: {
    header:{
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
    },
    data:params||{}
    }
})
}
// 根据ID查询规则集市详情
export function ruleMarketDetail(params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
      method: 'post',
      url: '/rule/detail',
      baseURL: process.env.VUE_APP_PLAN_API,
      headers: {
        affinitycode: userInfo.userId || '',
      },
      data: {
        header:{
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
        },
        data: params || {}
      }
    })
}

// 规则集市--查询某个版本的思维树
export function queryComponentById(params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
        window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
        method: 'post',
        url: '/get_component_by_id',
        baseURL: process.env.VUE_APP_PLAN_API,
        headers: {
        affinitycode: userInfo.userId || '',
        },
        data: {
        header:{
            tenant_id: userInfo.tenantId || 'str',
            user_id: userInfo.userId || 'str',
            session_id: '1',
            work_space_id: workspaceId + ''
        },
        data:params||{}
        }
    })
}
  
// 规则集市--查询某个版本的方案明细
export function querySchemeById(params) {
const userInfo = getUserInfo()
const workspaceId = getWsID();
if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
}
return service({
    method: 'post',
    url: '/get_scheme_detail_by_id',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
    affinitycode: userInfo.userId || '',
    },
    data: {
    header:{
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
    },
    data:params||{}
    }
})
}

export function queryUseTagsRuleMarket (params) {
    const userInfo = getUserInfo()
    const workspaceId = getWsID();
    if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
      method: 'get',
      url: `/api/tag/list/with_use?biz_type=rule_market_${workspaceId}&keyword=${params.keyword}`,
      baseURL: process.env.VUE_APP_AGENT_API,
      headers: {
        whiteuservalidate: 'False'
      },
      data: {}
    })
  }
  // VUE_APP_AGENT_API 查询规则集市标签
export function queryTagsRuleMarket (params) {
const userInfo = getUserInfo()
const workspaceId = getWsID();
if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
}
return service({
    method: 'get',
    url: `/api/tag/list?biz_type=rule_market_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
    whiteuservalidate: 'False'
    },
    data: {}
})
}
export function queryUseRuleMarketTagsWithCount (params) {
    const workspaceId = getWsID();
    if (!workspaceId) {
        window.location.href = window.location.origin +'/#/homePage'
    }
    return service({
        method: 'get',
        url:`api/tag/list/with_use_statistic?biz_type=rule_market_${workspaceId}&keyword=${params.keyword}`,
        baseURL: process.env.VUE_APP_AGENT_API,
        headers: {
            whiteuservalidate: 'False'
        },
        data: {}
    })
}

// 规则测试
export function OnRuleTest (params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
      method: 'post',
      url: '/code/rule_execute',
      baseURL: process.env.VUE_APP_PLAN_API,
      headers: {
      affinitycode: userInfo.userId || '',
      },
      data: {
      header:{
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
      },
      data:params||{}
      }
  })
}
// 规则发布
export function RuleMarketAbilityPublish (params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
      window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
      method: 'post',
      url: '/rule/publish',
      baseURL: process.env.VUE_APP_PLAN_API,
      headers: {
      affinitycode: userInfo.userId || '',
      },
      data: {
      header:{
          tenant_id: userInfo.tenantId || 'str',
          user_id: userInfo.userId || 'str',
          session_id: '1',
          work_space_id: workspaceId + ''
      },
      data:params||{}
      }
  })
}
// 绑定标签
export function bindTagMarket (params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/batch_bind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {...params, biz_type: 'rule_market_'+workspaceId}
  })
}

// 更新能力
export function updateRule(params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
    method: 'post',
    url: '/rule/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || '',
    },
    data: {
      header:{
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:params||{}
    }
  })
}

// biz_type: "prompt_template"name: "test"，新增标签
export function addTagMarket (params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/add',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {...params, biz_type: 'rule_market_'+workspaceId}
  })
}
// 查询规则版本的内容
export function queryRuleContent(params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin +'/#/homePage'
  }
  return service({
    method: 'post',
    url: '/rule/content',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || '',
    },
    data: {
      header:{
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:params||{}
    }
  })
}
