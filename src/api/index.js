import service from './http'
import qs from 'qs'
import { apiPath } from '@/utils/apiPath.js'

export const baseUrl = apiPath.baseUrl
export const uacUrl = apiPath.uacUrl
// get方法
export function getData (val) {
  return service({
    method: val.apiMethod,
    url: val.apiUrl,
    params: val.params,
    headers: val.headers
  })
}

// post方法
export function postData (val) {
  return service({
    method: val.apiMethod,
    url: val.apiUrl,
    data: val.params,
    headers: val.headers
  })
}

// ------ 调用日志数据 -----
export function queryConfigInfo (params) {
  return service({
    method: 'post',
    url: 'aiConfig/queryConfigInfo',
    data: params
  })
}

export function queryAppListByServiceId (params) {
  return service({
    method: 'post',
    url: 'aiApp/queryAppListByServiceId',
    data: params
  })
}

export function groupByResCode (params) {
  return service({
    method: 'post',
    url: 'openApi/service/result/groupByResCode',
    data: params
  })
}
export function groupByCallTime (params) {
  return service({
    method: 'post',
    url: 'openApi/service/result/groupByCallTime',
    data: params
  })
}

export function commonLogQuery (params) {
  return service({
    method: 'post',
    url: 'openApi/service/result/commonLogQuery',
    data: params
  })
}

export function getBasic (params) {
  return service({
    method: 'post',
    url: '/service/getBasic',
    data: params
  })
}
export function statisticsMonthServiceCall (params) {
  return service({
    method: 'post',
    url: '/openApi/service/result/statisticsMonthServiceCall',
    data: params
  })
}

export function statisticsDayServiceCall (params) {
  return service({
    method: 'post',
    url: '/openApi/service/result/statisticsDayServiceCall',
    data: params
  })
}
