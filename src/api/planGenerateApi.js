import { router } from '@/main'
/* eslint-disable camelcase */
// import axios from 'axios'
import service from '@/axios'
import axios from 'axios';

const Vue = require('vue')
const store = require('../store').default
let workspaceId = ''
const schemeToken = ''

export function getWsID() {
  // console.log('ceshi', router?.currentRoute?.query)
  if (store?.state.workSpace.currentWorkSpace.workspaceId) {
    workspaceId = store?.state.workSpace.currentWorkSpace.workspaceId
  } else {
    workspaceId = router?.currentRoute?.query.workspaceId
  }
  if (!workspaceId) {
    try {
      const [hash, query] = window.location.href.split('#')[1].split('?')
      const params = Object.fromEntries(new URLSearchParams(query))
      workspaceId = params.workspaceId ?? ''
    } catch (error) {
      console.log('error', error)
    }
  }
  return workspaceId
}

export function getCurrentUserInfo() {
  let userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : {}
  if (Object.keys(userInfo).length) {
    // console.log('--信息---', userInfo);
    return userInfo
  } else {
    // console.log('----$ennewsdk----', Vue.prototype.$ennewsdk);
    const temp = Vue.prototype.$ennewsdk?.getUser().then((res) => {
      return res
    })
    userInfo = temp
    console.log('信息---', userInfo, 'temp', temp)
    return userInfo
  }
}

// 智能伙伴右侧统计数据
export function userIndexCount() {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/user/index_count',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 方案状态列表
export function SchemeStatusList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_status_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 方案类型列表
export function SchemeTypeList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_type_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function SceneList() {
  const userInfo = getCurrentUserInfo()
  console.log('-----userInfo------', userInfo)
  const workspaceId = getWsID()
  return service({
    method: 'post',
    url: '/get_agent_scene_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId,
        user_id: userInfo.userId,
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 任务类型列表
export function TaskTypeList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/task_type_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 场景配置列表
export function SceneTypeList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/getDefaultList',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: params
  })
}

export function getVersionList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: `/api/scene/v2/version_list/${params}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      // data: params || {}
    }
  })
}
// scheme_tasks_modify_basic
export function scheme_tasks_modify_basic(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_tasks_modify_basic',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// scheme_tasks_modify_basic
export function scheme_task_upd(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/task/upd',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: params
  })
}
// 任务类型列表
export function UpdateTaskStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_task_status',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 保存关系
export function saveRel(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/work_bench/save_rel',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 取消收藏知识库关系
export function deleteRel(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/work_bench/delete_rel',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 取消收藏知识库关系
export function schemeListCount(params, headerParams) {
  const userInfo = getCurrentUserInfo()
  console.log('获取用户信息!!!', userInfo)
  const workspaceId = headerParams?.workspaceId || getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_list_count',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 场景列表
export function getSceneList(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/scene/v2/ability_config/dev_assistant_scene',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
  })
}

// 最后一次查到的场景列表
export function getUserLastSceneList(params) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : Vue.prototype.userInfo
    ? Vue.prototype.userInfo
    : {}
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_user_last_creation_scene',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 最后一次查到的场景列表（用户关联）
export function getUserLastSceneListWithBind(params) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : Vue.prototype.userInfo
    ? Vue.prototype.userInfo
    : {}
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_user_last_creation_scene_with_bind',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 获取场景配置
export function getInstanceInfo(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.instanceId}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
  })
}

// 获取角色信息
export function getRoleInfo(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/role/${params.RoleId}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
  })
}
//
// 更新场景配置
export function updateInstanceInfo(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'put',
    url: `/api/scene/v2/instance/${params.instanceId}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params }
  })
}
// 参考提示
export function getRefpromptsList(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/refprompts/list',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params }
  })
}

export function createRefprompts(params) {
  return service({
    method: 'post',
    url: '/api/refprompts/create',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params
  })
}

export function updateRefprompts(params) {
  return service({
    method: 'put',
    url: '/api/refprompts/update',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params
  })
}
export function deleteRefprompts(params) {
  return service({
    method: 'delete',
    url: `/api/refprompts/del/${params}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params
  })
}

export function queryRefpromptUseTags(params) {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list/with_use?biz_type=reference_prompt&keyword=`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 查询各类型模板列表
export function getTemplateList(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/template/available_list',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, workspace_id: workspaceId + '' || '1' }
  })
}

// 角色
export function getRoleList(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/role/getVisibleList',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, workspace_id: workspaceId + '' || '1' }
  })
}

// 方案列表
export function SchemeList(params, headerParams) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : Vue.prototype.userInfo
    ? Vue.prototype.userInfo
    : {}
  const workspaceId = headerParams?.workspaceId || getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 新建方案
export function AddScheme(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_create',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 编辑方案
export function UpdateScheme(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_modify',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 获取方案ext_info
export function GetSchemeExtInfo(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_ext_info',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params
        } || {}
    }
  })
}

// 编辑方案ext_info
export function UpdateSchemeExtInfo(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_ext_info_update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params
        } || {}
    }
  })
}

export function knowledgeQaSave(body) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/knowledge/qa_save',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {
        ...body
      }
    }
  })
}

// 删除方案
export function DeleteScheme(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_delete',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 智能群里列表
export function AgentList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/agent_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 任务分页列表
export function TaskPageList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/final_scheme_tasks_page',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 任务列表非分页
export function TaskPageListNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/task/get',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: params
  })
}
// 编辑任务
export function UpdateTask(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_tasks_modify',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}

// 新增任务
export function CreateTask(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/create_simple_task',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data:
        {
          ...params,
          nickName: userInfo.nickName,
          username: userInfo.username
        } || {}
    }
  })
}
// 新增任务
export function CreateTaskNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/task/add',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: params
  })
}
// 方案明细
export function SchemeDetail(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 获取列表
export function getIocCardList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: 'ability/get_ioc_card_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 更新数据 rel_ioc_card_code
export function setRelIocCardCode(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: 'ability/update_ability_rel_ioc_card_code',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 聊天界面-方案任务
export function SchemeTasks(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_tasks_page',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 生成过程
export function SchemeProcess(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_process_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 对话
export function Conversation(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/conversation',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 对话详情
export function SchemeConversationDetail(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/conversation_detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 上传文件
export function UploadFile(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/files',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 生成方案接口
export function TaskGeneration(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/task_generation',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 删除任务接口
// 对话详情
export function PlanTaskDelete(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/task_delete',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 删除任务接口
// 对话详情
export function PlanTaskDeleteNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/scheme/task/del?task_id=${params.task_id}`,
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    }
  })
}
export function get_parent_task_info(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/scheme/task/get_parent_task_info?scheme_id=${params.scheme_id}`,
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    }
  })
}
// 编辑方案明细
export function PlanTaskEdit(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_scheme_detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//
export function agentSenceList(params) {
  const userInfo = getCurrentUserInfo()
  console.log('-----userInfo------', userInfo)
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_agent_scene_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//
export function queryDictConfig(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_dict_config',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function queryDictConfigNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_manager/get_scheme_exist_scenes',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 查询助手是否被占用
export function queryChatIsUseing(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/check_scheme_using',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// AbilityPublish, AbilityTest
// 能力生成代码部署
export function AbilityPublish(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/deploy',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力生成代码测试
export function AbilityTest(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/test',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 编辑能力生成代码
export function CodeEdit(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_decision_making_content',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 知识保存
export function SchemeSaveKnow(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/save_known_scheme_detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 问候语
export function queryGreeting(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_greetings',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 生成思维树
export function GetDecision(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_decision_making_content',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//
export function getUserInfo(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_user_info',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function getRefPromptList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/refprompts/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  }).then(response => {
    console.log('返回结果:', response); // 输出返回的结果
    return response; // 返回结果
  }).catch(error => {
    console.error('获取指令列表失败:', error);
    throw error; // 重新抛出错误，以便调用者可以处理
  });
}
export function queryQuickReply(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_quick_reply',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 更新状态
export function UpdateSchemeStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_scheme_status',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 检验流程图是否正确
export function CheckContentRight(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/mermaid/check',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 配置查询接口

export function queryAbilityMapping(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_ability_data_mapping',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function updateAbilityMapping(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_ability_data_mapping',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 参数列表查询
export function QueryAbilityData(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_ability_param_mapping',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取所有设备
export function allEquipList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/device/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取单个设备的数据参数
export function queryOneEquipDetail(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/device/attribute_value',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取时序数据
export function querySequenceData(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/device/sequence_data',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 测试-新版本
export function OnCodeTestNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/executev1',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 测试
export function OnCodeTest(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/execute',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 发布
export function CodePublish(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/publish',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 参考已沉淀方案接口
export function getSuggestionList() {
  console.log('process.env.VUE_APP_LLM_API', process.env.VUE_APP_LLM_API)
  // const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/mechanism/suggestion/getSuggestionList/${workspaceId}`,
    baseURL: process.env.VUE_APP_LLM_API,
    headers: {
      'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY_LLM
    }
  })
}

// 查询表列表
export function getTablesList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/db/tables',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 查询表列表
export function getSuanfaList() {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/algorithm/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 根据表名查字段
export function queryFieldsByTable(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/db/columns?tableName=' + params.tableName + '&dbType=' + params.dbType,
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      }
    }
  })
}

// queryDbList
export function queryDbList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/db/query',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//
export function queryUseTags(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list/with_use?biz_type=scheme_manager_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}


export function queryUseTagsWithCount(params,url='api/tag/list/with_use_statistic?biz_type=scheme_manager_') {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `${url}${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}
export function queryUseTagsWithCountNew(params,url='api/tag/list_get_by_un_expert_scene_use?biz_type=scheme_manager_') {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `${url}${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}
export function queryUseTagsWithCountTaskNew(params,url='api/tag/list_get_by_task_scene_use?biz_type=scheme_manager_') {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `${url}${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

export function queryUseMarketTagsWithCount(params,url='api/tag/list/with_use_statistic?biz_type=ability_market_') {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `${url}${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// VUE_APP_AGENT_API
export function queryTags(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list?biz_type=scheme_manager_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// biz_type: "prompt_template"name: "test"
export function addTag(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/add',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, biz_type: 'scheme_manager_' + workspaceId }
  })
}

// tag_ids:[], biz_id:''
export function bindTag(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/batch_bind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, biz_type: 'scheme_manager_' + workspaceId }
  })
}

export function queryUseTagsMarket(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list/with_use?biz_type=ability_market_${workspaceId}&keyword=`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// VUE_APP_AGENT_API
export function queryTagsMarket(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list?biz_type=ability_market_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// biz_type: "prompt_template"name: "test"
export function addTagMarket(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/add',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, biz_type: 'ability_market_' + workspaceId }
  })
}

// tag_ids:[], biz_id:''
export function bindTagMarket(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/tag/batch_bind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, biz_type: 'ability_market_' + workspaceId }
  })
}

// https://aip-agentgpt-service.fat.ennew.com/api/api_doc/get_all?keyword=
export function queryNengliList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/api_doc/get_all?keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// /api/scene/{agent_scene_id}/scheme_optimize_ability
export function queryAbilityList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.scheme_id}/scheme_optimize_ability`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 查询缓存
export function queryCache(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/cache/get',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 增加缓存
export function addCache(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/cache/add',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 删除缓存
export function removeCache(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/cache/remove',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力类型
export function queryAbilityType(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/dict-config/type/resJson/ability_type',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 能力结果展示样式
export function queryDisplayComponents(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/dict-config/type/resJson/display_components',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 能力仓库

export function queryAbilityMarket(params, headerParams) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : Vue.prototype.userInfo
    ? Vue.prototype.userInfo
    : {}
  const workspaceId = headerParams?.workspaceId || getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/query_page',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力仓库 全部&我的数量统计

export function queryAbilityMarketCount(params, headerParams) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = headerParams?.workspaceId || getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/query_count',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 历史版本查询

export function queryAbilityVersion(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/version/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 历史版本查询

export function queryAbilityVersionNew(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/v2/version/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 能力授权查询

export function queryAbilitiesAuthList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/apps/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力授权页面编辑备注

export function editAbilitiesAuthRemake(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/apps/note/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 接口定义
export function queryAbilityApi(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/api',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 用户反馈
export function queryFeedbackList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/feedback/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 版本上线
export function onLineStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/version/online',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 版本下线
export function offlineStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/version/offline',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力发布
export function MarketAbilityPublish(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/publish',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力仓库详情
export function getMarketAppInfo(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 更新能力
export function updateAbility(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 查思维图 代码
export function queryComponentById(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_component_by_id',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 方案明细
export function querySchemeById(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_detail_by_id',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 查询助手详情信息
export function querySchemeDetailById(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_manager',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function queryDuiqiTags(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    // url: `/api/tag/list/with_use?biz_type=api_doc&biz_status=true&keyword=${params.keyword}&rel_biz_type=`,
    url: `/api/tag/list/with_use?biz_type=code_module&biz_status=true&keyword=${params.keyword}&rel_biz_type=`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

export function queryAgentInfoDetail(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.scheme_id}/get_role`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

export function get_preset_question(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: `/api/scene/v2/instance/${params.scheme_id}/get_preset_question`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}
// 获取对接算法列表
export function queryDuiqiApiList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/code_module/page_list',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params
  })
}

export function queryApiMixFuncList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/code_module/list',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, code_status: 1 }
  })
}

// 获取算法key对应的中文描述
export function queryDuiqiApiName(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/dict-config/type/api_doc_type',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 查询猜你想问
export function queryQuestionList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/create_questions',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 根据场景获取模版id
export function queryTempIfFromScene(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.scheme_id}/get_ability/data_source_filter`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 根据场景获取模版id
export function queryTempIfFromScene2(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/${params.agent_scene_id}`,
    url: `/api/scene/v2/instance/${params.scheme_id}/get_ability/data_pre_alignment`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

// 根据模版id启动任务，获取任务列表
export function queryTaskByTemp(params) {
  // console.log('参数', params);
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { template_id, ...rest } = params
  return service({
    method: 'post',
    url: `/api/agent/v2/manual_task/start?template_id=${params.template_id}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {
      name: '',
      goal: '对齐分析（固定输入）',
      custom_input_variables: rest
    }
  })
}

// 执行任务的流接口
export function manualExecute(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/agent/manual_task/execute',
    baseURL: process.env.VUE_APP_AGENT_API,
    responseType: 'stream',
    headers: {
      whiteuservalidate: 'False'
    }
  })
}

// 能力仓库-调用量统计
export function queryAbilityStatistics(params) {
  return service({
    method: 'post',
    url: '/ability/statistics',
    data: params
  })
}

// 能力仓库-获取排序SchemeId
export function querySchemeIdsOrderCallCount(params) {
  return service({
    method: 'post',
    url: '/ability/getSchemeIdsOrderCallCount',
    data: params
  })
}

// 恩牛智伴-调用量统计
export function queryCallCountStatistic(params) {
  return service({
    method: 'post',
    url: '/ability/totalCallCountStatistics',
    data: params
  })
}

// /speaker_selection
export function queryAnsRole(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/speaker_selection',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// /code/params
// 获取测试参数
export function queryCodeParams(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/params',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: { scheme_id: params.scheme_id }
    }
  })
}

// 参数分析
export function apiParamAnalysis(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/api_param_analysis',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        scheme_id: params.scheme_id,
        message: params.message
      }
    }
  })
}

// 能力仓库试一试获取验证结果
export function obtainVerificationContent(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/api_param_analysis',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 查询知识库和空间已绑定关系
export function queryKnowledgeRelation() {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/knowledge/queryKnowledgeRelation',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 查询知识库和空间id列表
export function queryMyKnowledgeBase() {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/knowledge/queryMyKnowledgeBase',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 保存知识库关系
export function saveKnowledgeRelation(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/knowledge/saveKnowledgeRelation',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 发送对话内容
export function startConversation(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/conversation',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// 发送对话内容
export function startConversationWithRole(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/conversation_with_role',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

export function saveSimpleSchemeGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: 'simple_version_scheme_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// 保存方案
export function saveSchemeGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/scheme_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// append_history
// 发送对话内容
export function startAppendHistory(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/append_history',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// decision_tree_generate
// 发送对话内容
export function startDecisionTreeGenerateRequest(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/decision_tree_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// model_param_extraction
// 发送对话内容 建模数据分析
export function startDecisionModelParamRequest(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/model_param_extraction',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

export function structuredModelingInformationReq(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/modeling_info_structure',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

export function mathematicalModelReq(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/math_model_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// clear_history
export function startClearHistory(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/clear_history',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

export function startStopThinking(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/stop_thinking',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// task_generate
export function startTaskGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/task_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// align_data_generate
export function startAlignDataGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/align_data_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// ability_generate
export function startAbilityGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/ability_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// scheme_optimize
export function startSchemeOptimize(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/scheme_optimize',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

export function schemeDetailHistory(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/scheme_detail_history',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// sql_generate
export function startSqlGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/sql_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// 代码分析
export function startCodeAnalysis(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/code_analysis',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 智能能力研发
export function getInitWorkBenchDich(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/work_bench/get_init_work_bench_dict',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取加密信息
export function getBaidu(params) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : {}
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/common/query_baidu',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 智能能力研发
export function getPublishAbilityDich(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/work_bench/get_publish_ability_dict',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力执行
export function getExecuteSync(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: `/api/template/${params.ability_id}/execute_sync`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params || {}
  })
}

// 生成规则
export function startRuleGenerate(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  const { session_id, ...rest } = params
  return service({
    method: 'post',
    url: '/rule_generate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: rest
    }
  })
}

// 规则对齐标签
export function queryRuleUseTags(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list/with_use?biz_type=rule_manager_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}

export function getCodeZipBySchemeIdCode(scheme_id, code_str) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/code/download',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: { code_str: code_str, scheme_id: scheme_id }
    }
  })
}

// /api/scene/v2/getVisibleList
export function getSencetVisibleList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/getVisibleList',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, user_id: userInfo.userId || '' }
  })
}

// /api/scene/v2/getVisibleList
export function getSceneVisibleListWithTop(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/getVisibleListWithTop',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, user_id: userInfo.userId || '' }
  })
}

// /api/scene/v2/listSceneTypeByUserBind
export function listSceneTypeByUserBind() {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/scene/v2/listSceneTypeByUserBind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
  })
}

// /api/scene/v2/getVisibleList
export function sceneListByUserBind(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/listByUserBind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { ...params, user_id: userInfo.userId || '' }
  })
}

export function saveSceneUserBind(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/userBind',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: { scene_ids: params.sceneIds, user_id: userInfo.userId || '' }
  })
}

// 可用部署实例
export function getAbilityEngineServiceList(isSimulation = false) {
  // 根据 isSimulation 的值来决定是否添加 type=simulation 参数
  const params = isSimulation ? { type: 'simulation' } : {};
  return service({
    method: 'get',
    url: '/aiService/getAbilityEngineServiceList',
    baseURL: process.env.VUE_APP_API,
    params
  });
}

// 发布列表分页列表
export function queryAbilityFabuVersion(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/publish_record/list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 更新部署实例/ability/engine_service/update
export function AbilityEngineServiceUpdate(params) {
  const userInfo = getUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/engine_service/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 保存对齐分析结果save_align_type
export function saveAlignType(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/save_align_type',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 查询任务列表
export function getSchemePhaseTasks(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_phase_tasks',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 获取任务结果
export function getTasksRes(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/biz_extend/get_detail',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 更新任务结果
export function updateTasksRes(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/biz_extend/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 从某个步骤开始执行 reexecute_task
export function reexecuteTask(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/reexecute_task',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 根据方案查询设备
export function queryDeviceIds(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/device_ids',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 根据设备id查询测点数据
export function queryPointsByDeviceId(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_iot_device_point_data',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 更新方案和设备信息
export function updateByDeviceId(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_scheme_iot_ref',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 校验方案和设备信息ID
export function checkByDeviceId(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/check_scheme_iot_ref',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 通知后端前端开始触发开始了
export function startTask(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/start_task',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 判断任务是否开始
export function getTaskStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_task_status',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 更新任务状态
export function updateLogTaskStatus(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/log_task',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

export function updateSchemeSort(schemeId, sort, headerParams) {
  const userInfo = sessionStorage.getItem('USER_INFO')
    ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    : Vue.prototype.userInfo
    ? Vue.prototype.userInfo
    : {}
  const workspaceId = headerParams?.workspaceId || getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_scheme_sort',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: {
        scheme_id: schemeId,
        sort: sort
      }
    }
  })
}

// 更新外部表格方案描述
export function updateSchemeDescription(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_desc_update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 知识来源接口
// 获取知识检索能力id
export function getknowledgeRetrievalCapabilityId(params) {
  const ability_type = 'knowledge_base_search'
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.instance_id}/get_ability/${ability_type}`,
    baseURL: process.env.VUE_APP_AGENT_API
  })
}

// 执行知识检索能力
export function getKonwledgeSource(params) {
  return service({
    method: 'post',
    url: `/api/template/${params.agent_template_id}/execute_sync`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params || {}
  })
}


// 关联问题生成接口
export function getAssociationGeneration(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/create_questions',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  })
}

// 能力仓库注册列表接口
export function getAbilityRegistrationList(params) {
  return service({
    method: 'get',
    url: `/api/gpts_ability/${params.gpts_ability_id}/registration_list`,
    baseURL: process.env.VUE_APP_AGENT_API
  })
}

// 通过能力仓库的id获取创建信息
export function getCreateInfoById(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/gpts_ability/${params.gpts_ability_id}/get_registration_info`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + ''
    }
  })
}

// 创建认知能力
export function createCognitiveAbility(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: `/api/gpts_ability/${params.gpts_ability_id}/register`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: params || {}
  })
}

// 注销认知能力
export function deleteCognitiveAbility(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'delete',
    url: `/api/gpts_ability/${params.gpts_ability_id}/delete_registration/${params.gpts_ability_registration_id}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: params || {}
  })
}

// 更新认知能力
export function updateCognitiveAbility(params) {
  return service({
    method: 'get',
    url: `/api/gpts_ability/${params.gpts_ability_id}/registration/${params.gpts_ability_registration_id}/update_status?status=${params.status}`,
    baseURL: process.env.VUE_APP_AGENT_API
  })
}

// 获取租户信息
export function queryRelatedTenants(params) {
  return service({
    method: 'get',
    url: `platform/tenant/listByTenantName?tenantName=${params.tenantName}`,
    baseURL: process.env.UE_APP_BACKEND_URL,
    data: {}
  })
}

// 获取行业列表
export function getIndustryList(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/api/gpts_ability/get_industry_list',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + '',
      whiteuservalidate: 'False'
    }
  })
}

// 查询仿真平台设备列表接口
export function querySimulationPlatformEquipmentList() {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: `/query_simulation_device_list`,
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        work_space_id: workspaceId + ''
      },
      data: {}
    }
  })
}

// 根据仿真平台设备Code查询仿真平台设备详细信息
export function querySimulationPlatformEquipmentInfoByCode(data) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/query_simulation_device_data',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      data
    }
  })
}

// 能力价值
export function abilityValue(data) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/ability_value',
    baseURL: process.env.VUE_APP_AIP_DIFY_URL,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      data
    }
  })
}

// 能力图标接口
export function abilityIconInterface(data) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/generateAbilityIcon',
    baseURL: process.env.VUE_APP_AIP_DIFY_URL,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: data
    }
  })
}

// 可匹配的意图
export function matchableIntent(data) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/matchable_intent',
    baseURL: process.env.VUE_APP_AIP_DIFY_URL,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      data
    }
  })
}

// 关键词标签
export function keywordTag(data) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/keyword_tag',
    timeout: 10000,
    baseURL: process.env.VUE_APP_AIP_DIFY_URL,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      data
    }
  })
}

// 开场白接口
export function openingStatement(data) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/ability_prologue',
    timeout: 10000,
    baseURL: process.env.VUE_APP_AIP_DIFY_URL,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      data
    }
  })
}
// 反馈
export function getFeedback(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/feedback',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: params
  })
}

// qa知识库上传
export function upload_knowledge_qa(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/knowledge_base/upload_knowledge_qa',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
    },
    data: params || {}
  })
}

// file知识库上传
export function upload_knowledge_file(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/knowledge_base/upload_knowledge_file',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: params || {}
  })
}

// 获取obs文件上传签名
export function getObsUploadUrl(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/obs/generate_sign',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: params || {}
  })
}

// 根据方案ID查询任务列表信息和任务阶段执行状态
export function getTaskListBySchemeId(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_phase_tasks_new',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//通用指令接口。
export function generalExecute(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  console.log('打印时间2')
  return service({
    method: 'post',
    url: '/agent/execute',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        instruction_code: params.instruction_code,
        instruction_param: params?.instruction_param,
      }
    }
  });
}

//批量查询执行任务结果1
export function getbatchTasksResult(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_task_result',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      work_space_id: workspaceId + ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        scheme_id: params.scheme_id,
        phase_tasks: params.phase_tasks
      }
    }
  })
}

// 设置分享
export function setShareCode(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/share_code/set',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 验证share_code
export function validateShare(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/share_code/validate',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力参数分析编辑接口
export function CodeAnalysisEdit(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_code_analysis_content',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 能力场景推荐接口
export function recommendedSceneList(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/user/recommended_scene_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 知识库接口
export function getKnowledgeList(params) {
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'get',
    url: '/knowledgeFolder/getKnowledgeList',
    baseURL: process.env.VUE_APP_AI_CHAT,
    headers: {
      'appid': 'ai-chat-backend',
      'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY_LLM
    }
  });
}

export function abilityStatistics(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId && router?.currentRoute.name !== 'abilityMarket' && router?.currentRoute.name !== 'allMate' ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme_manager/ability_statistics',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

export function get_obs_pre_sign_url(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_obs_pre_sign_url',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

//站点信息列表接口
export function get_iot_station_list(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/get_iot_station_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        station_name: params.station_name
      }
    }
  });
}


//群组锅炉单耗折线图接口
export function api_query_running_analysis_data_list(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability_boiler/query_running_analysis_data_list',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        deviceGroupId: params.deviceGroupId,
        startDate: params.startDate,
        endDate: params.endDate
      }
    }
  });
}


//查询锅炉群组信息接口
export function AllGuoLuInfo(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability_boiler/single_ability_execute',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        abilityId: params.abilityId,
        requestBody: {
          device_group_id: params.requestBody.device_group_id,
          detection_time: params.requestBody.detection_time
        }
      }
    }
  });
}




//查询锅炉设备信息接口
export function GuoLuInfo(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability_boiler/single_ability_execute',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        abilityId: params.abilityId,
        requestBody: {
          device_id: params.requestBody.device_id,
          detection_time: params.requestBody.detection_time
        }
      }
    }
  });
}


//踢出原来对话占有人接口
export function chatDisconnect(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/chat_session/disconnect',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        scheme_id: params.scheme_id
      }
    }
  });
}

// 首页能力相关数据统计接口
export function indexAbilityStatistics() {
  return service({
    method: 'get',
    url: '/api/statistics/homepage_statistics',
    baseURL: process.env.VUE_APP_AGENT_API,
  });
}

// 授权连接
export function setApproveCode(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/apps/approvecode/set',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}


// 设置接入app的appovecode
export function byApproveCode(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/ability/apps/getByApproveCode',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取token
export function byToken(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/admin/client/getToken',
    baseURL: 'https://middle-open-platform.fat.ennew.com/',
    params: params
  });
}

// 专家沉淀标签查询
export function expertTagsWithCount(params) {
  const workspaceId = getWsID()
  if (
    !workspaceId &&
    router?.currentRoute.name !== 'abilityMarket' &&
    router?.currentRoute.name !== 'allMate'
  ) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/tag/list_get_by_expert_scene_use?biz_type=scheme_manager_${workspaceId}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: {}
  })
}


//获取所有能力
export function get_instance_abilities_with_result(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/scheme_manager/get_instance_abilities_with_result',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
      },
      data: {
        scheme_id: params.scheme_id,
        scheme_detail_id: params.scheme_detail_id || ''
      }
    }
  });
}
// 更新 /update_scheme_materials
export function update_scheme_materialsFetch(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/update_scheme_materials',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 创建 /update_scheme_materials
export function create_scheme_materialsFetch(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/create_scheme_materials',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}
// 获取 /get_scheme_materialsFetch
export function get_scheme_materialsFetch(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/get_scheme_materials',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
}

// 获取场景实例的envs, 入参：scheme_id
export function getSceneInstanceEnvs(scene_instance_id) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${scene_instance_id}/global_constant`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
  })
}

// 更新场景实例的envs, 入参：scheme_id
export function updateSceneInstanceEnvs(scene_instance_id, params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'put',
    url: `/api/scene/v2/instance/${scene_instance_id}/global_constant`,
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params || {}
  })
}

export function extractGlobalConstantFromAbility(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/api/scene/v2/version/extract_global_constant_from_ability',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    },
    data: params || {}
  })
}



// 判断是否出示方案一键优化按钮
export function shouldShowSchemeOptAPI(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'get',
    url: `/api/scene/v2/instance/${params.scheme_id}/get_ability/simple_version_generate_scheme`,
    baseURL: process.env.VUE_APP_AGENT_API
  });
}

// 查询物料列表
export function materials(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/get_materials',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
// 卡片组件基本信息
export function cardMaterialInfo(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/get_card_material_info',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
// 注册卡片接口
export function registerCard(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/register_card',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
// 卡片详情接口
export function cardInfo(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/get_card_info',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
// 升级卡片接口
export function updateCard(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/update_card',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
// 能力上架接口
export function cardShelveEnnew(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/ability/card/card_shelve_ennew',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
export function agentGetTagList(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'get',
    url: `/api/tag/list?biz_type=${params.biz_type}&keyword=${params.keyword}`,
    baseURL: process.env.VUE_APP_AGENT_API
  });

}

export function agentAddTag(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/api/tag/add',
    baseURL: process.env.VUE_APP_AGENT_API,
    data: params || {}
  });
}


// 采纳代码接口
export function accepCodeApi(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/decision_making_tree/accept_code',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        scheme_id: params.scheme_id,
        message: params.message
      }
    }
  });
}
// 保存记忆接口
export function scheme_memory(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/api/scheme_memory/list',
    baseURL: process.env.VUE_APP_AGENT_API,
    data:  params || {}
  });
}
// 删除记忆接口
export function scheme_memory_del(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'delete',
    url: `/api/scheme_memory/${params}`,
    baseURL: process.env.VUE_APP_AGENT_API,
  });
}
// 创建记忆接口
export function scheme_memory_create(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: `/api/scheme_memory`,
    baseURL: process.env.VUE_APP_AGENT_API,
    data:  params || {}
  });
}
// 更新记忆接口
export function scheme_memory_update(params) {
  const userInfo = getCurrentUserInfo();
  const {id,...rest } =params
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'put',
    url: `/api/scheme_memory/${id}`,
    baseURL: process.env.VUE_APP_AGENT_API,
    data:  rest || {}
  });
}
// 更新状态接口
export function update_usage_status(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: `/api/scheme_memory/update_usage_status`,
    baseURL: process.env.VUE_APP_AGENT_API,
    data:  params || {}
  });
}

// 采纳代码接口
export function ScheManaioc_save_data(params) {
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/scheme_manager/ioc_save_data',
    baseURL: process.env.VUE_APP_PLAN_API,
    // baseURL: 'http://10.20.99.25',
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: {
        businessKey: params.businessKey,
        // scheme_detail: params.scheme_detail,
        data_scheme_detail: params.data_scheme_detail,
        scheme_id: params.scheme_id,
        scheme_url: params.scheme_url
      }
    }
  });
}
export function update_ability_result(params){
  const userInfo = getCurrentUserInfo();
  const workspaceId = getWsID();
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage';
  }
  return service({
    method: 'post',
    url: '/scheme_manager/update_ability_result',
    baseURL: process.env.VUE_APP_PLAN_API,
    // baseURL: 'http://10.20.99.25',
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params
    }
  });
}
export function queryMemberSortedWorkspaceList(params){
  return service({
    method: 'post',
    url: '/workspace/member/getMemberSortedWorkspaceList',
    data: params
  })
}
//能力样版查询
export function schemeTemplateList(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/scheme_template_list',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: '1',
       work_space_id: workspaceId + ''
     },
     data:
       {
         ...params,
         nickName: userInfo.nickName,
         username: userInfo.username
       } || {}
   }
 })
}
//能力样版新建
export function schemeCreateTemplate(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/scheme_create_template',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: '1',
       work_space_id: workspaceId + ''
     },
     data:
       {
         ...params,
         // nickName: userInfo.nickName,
         // username: userInfo.username
       } || {}
   }
 })
}
export function schemeCreateFromTemplate(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/scheme_create_from_template',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: params.session_id || '1',
       work_space_id: workspaceId + ''
     },
     data: params || {}
   }
 })
}
// 编辑方案
export function schemeTemplateModify(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/scheme_template_modify',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: '1',
       work_space_id: workspaceId + ''
     },
     data:
       {
         ...params,
         nickName: userInfo.nickName,
         username: userInfo.username
       } || {}
   }
 })
}
// 类型
export function queryTemplate(params,typeUrl='scheme_template_code_type') {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'get',
   url: `/api/dict-config/type/resJson/${typeUrl}`,
   baseURL: process.env.VUE_APP_AGENT_API,
   headers: {
     whiteuservalidate: 'False'
   },
   data: {}
 })
}
// 文件列表
export function queryListFiles(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/ability/list_files',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: params.session_id || '1',
       work_space_id: workspaceId + ''
     },
     data: params || {}
   }
 })
}
export function queryObjectKey(params) {
 const userInfo = getCurrentUserInfo()
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'post',
   url: '/ability/obs_generateSignByObjectKey',
   baseURL: process.env.VUE_APP_PLAN_API,
   headers: {
     affinitycode: userInfo.userId || ''
   },
   data: {
     header: {
       tenant_id: userInfo.tenantId || 'str',
       user_id: userInfo.userId || 'str',
       session_id: params.session_id || '1',
       work_space_id: workspaceId + ''
     },
     data: params || {}
   }
 })
}
//更新代码
export function updateCom(params) {
  const userInfo = getCurrentUserInfo()
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'post',
    url: '/scheme/biz_extend/update',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      affinitycode: userInfo.userId || ''
    },
    data: {
      header: {
        tenant_id: userInfo.tenantId || 'str',
        user_id: userInfo.userId || 'str',
        session_id: params.session_id || '1',
        work_space_id: workspaceId + ''
      },
      data: params || {}
    }
  })
 }

 //查询产物
export function get_upd_agent_context(params) {
  return service({
    method: 'post',
    url: '/agent_context/get',
    baseURL: process.env.VUE_APP_PLAN_API,
    data:  params || {}
  })
 }
 //更新产物
 export function add_upd_agent_context(params) {
  return service({
    method: 'post',
    url: '/agent_context/add_upd_agent_context',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: params || {}
  })
 }
  //删除文件
  export function abilityDel(params) {
    return service({
      method: 'post',
      url: '/ability/files/del',
      baseURL: process.env.VUE_APP_PLAN_API,
      data: params || {}
    })
   }
//  generate_sign_url
export function generate_sign_url(params) {
  return service({
    method: 'post',
    url: '/obs_util/generate_sign_url',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: params || {}
  })
 }
 export function chain_variable_session_upd(params) {
  return service({
    method: 'post',
    url: '/api/scene/v2/chain_variable_session_upd',
    baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: false
    },
    data:  params || {}
  })
 }
//  clear_this_message
 export function clear_this_message(params) {
  return service({
    method: 'post',
    url: '/clear_this_message',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      whiteuservalidate: false
    },
    data:  params || {}
  })
 }
 // 代码测试
export function queryAbilityConfig(id) {
 const workspaceId = getWsID()
 if (!workspaceId) {
   window.location.href = window.location.origin + '/#/homePage'
 }
 return service({
   method: 'get',
   url: `/api/scene/v2/${id}/get_code_test_ability`,
   baseURL: process.env.VUE_APP_AGENT_API,
    headers: {
      whiteuservalidate: 'False'
    }
 })
}
// 代码生成修改代码接口
export function update_file_content(params) {
  return service({
    method: 'post',
    url: '/obs_util/update_file_content',
    baseURL: process.env.VUE_APP_PLAN_API,
    headers: {
      whiteuservalidate: false
    },
    data:  params || {}
  })
 }
 // 任务tab
 export function queryTaskContent(params) {
  const workspaceId = getWsID()
  if (!workspaceId) {
    window.location.href = window.location.origin + '/#/homePage'
  }
  return service({
    method: 'get',
    url: '/scheme_manager/get_task_content',
    baseURL: process.env.VUE_APP_PLAN_API,
     headers: {
       whiteuservalidate: 'False'
     },
     params
  })
 }
 // 重命名
 export function handleRenameFile(params) {
  return service({
    method: 'post',
    url: '/obs_util/rename_file',
    baseURL: process.env.VUE_APP_PLAN_API,
    data: params || {}
  })
 }
  // 记忆文件新增接口

  export function notes_file_add(params) {
    const workspaceId = getWsID()
    if (!workspaceId) {
      window.location.href = window.location.origin + '/#/homePage'
    }
    return service({
      method: 'post',
      url: '/memory/notes/file/add',
      baseURL: process.env.VUE_APP_PLAN_API,
       headers: {
         whiteuservalidate: 'False'
       },
       data:  params || {}
    })
   }
     // 记忆文件删除接口

  export function notes_delete_file(params) {
    const workspaceId = getWsID()
    if (!workspaceId) {
      window.location.href = window.location.origin + '/#/homePage'
    }
    return service({
      method: 'post',
      url: '/obs_util/delete_file',
      baseURL: process.env.VUE_APP_PLAN_API,
       headers: {
         whiteuservalidate: 'False'
       },
       data:  params || {}
    })
   }

