import { Message } from 'element-ui'

class ErrorResponseHandler {
  constructor (interval = 2000) {
    this.interval = interval
    this.errorHandlerList = {
      _50003: {
        timer: null,
        lastTriggerTime: Date.now()
      },
      _50004: {
        timer: null,
        lastTriggerTime: Date.now()
      }
    }
  }

  deduplicateErrorResp (resp) {
    const currentTime = Date.now()
    if (resp?.data?.status === 50002) {
      window.location.href = window.location.origin +'/#/homePage'
    }

    if (resp?.data?.status === 50003) {
      window.location.href = window.location.origin +'/#/homePage'
    }

    if (resp?.data?.status === 50004) {
      const errorHandler = this.errorHandlerList._50004
      if ((currentTime - errorHandler.lastTriggerTime) >= this.interval) {
        errorHandler.lastTriggerTime = currentTime
        window.location.href = window.location.origin +'/#/homePage'
        Message({
          type: 'error',
          message: resp.data.msg,
          offset: 40,
          onClose: () => {
          }
        })
      }
    }
  }
}

export default new ErrorResponseHandler()
