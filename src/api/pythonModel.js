import service from '@/axios'
import { apiPath } from '@/utils/apiPath.js'

export const baseUrl = apiPath.baseUrl
export const uacUrl = apiPath.uacUrl

// 分页查询模型列表
export function queryPythonModelList (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/queryPythonModelList',
    data: params
  })
}

// 分页查询模型版本列表
export function queryPythonModelVersionList (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/queryPythonModelVersionList',
    data: params
  })
}

// 上传模型接口
export function uploadModel (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/add4Upload',
    data: params
  })
}

// 上传模型版本接口
export function uploadModelVersion (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/version/add4Upload',
    data: params
  })
}

// 校验模型名称是否重复
export function checkModelNameRepeat (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/checkRepeatModelName',
    data: params
  })
}

// 校验模型版本是否重复
export function checkModelVersionRepeat (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/checkRepeatModelVersionName',
    data: params
  })
}
// 设置指定模型版本为最新版
export function setNewestPythonModelVersion (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/setNewestPythonModelVersion',
    data: params
  })
}

// 模糊搜索模型名称
export function queryPythonModelName (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/queryPythonModelName',
    data: params
  })
}

// 更新模型名称
export function updatePythonModelName (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/updatePythonModelName',
    data: params
  })
}

// 更新模型描述
export function updatePythonModelDescription (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/updatePythonModelDescription',
    data: params
  })
}

// 更新模型版本
export function updatePythonModelVersion (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/updatePythonModelVersion',
    data: params
  })
}

// 删除模型版本
export function delPythonModelVersion (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/delPythonModel',
    data: params
  })
}

// 删除模型版本前check
export function delPythonModelVersionCheck (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/delPythonModelCheck',
    data: params
  })
}

// 查询模型版本信息
export function getPythonModelVersionDetailInfo (params) {
  return service({
    method: 'post',
    url: '/pythonProject/model/getPythonModelVersionDetailInfo',
    data: params
  })
}
