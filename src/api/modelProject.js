import service from '@/axios'

// 获取项目信息
export function getProjectInfo(data) {
  return service({
    method: 'post',
    url: '/project/getProjectInfo',
    data
  })
}

// 修改项目信息
export function modifyProjectInfo(data) {
  return service({
    method: 'post',
    url: '/project/modifyProject',
    data
  })
}

// 获取项目流水线
export function getPipelineRecord(data) {
  return service({
    method: 'post',
    url: '/bigmodel/bigModelWorkflowRunRecord/query',
    data
  })
}

// 训练时长预测接口
export function getPredictionTime(data) {
  return service({
    method: 'post',
    url: '/task/task/timePred',
    data
  })
}

// 根据name获取用户
export function getAllUserListByUserName(data) {
  return service({
    method: 'post',
    url: '/user/getAllUserListByUserName',
    data
  })
}
