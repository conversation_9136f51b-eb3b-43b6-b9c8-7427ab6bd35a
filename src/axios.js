/**
 * axios 配置
 */
import axios from 'axios'
import { router } from '@/main'
import errorR<PERSON>ponse<PERSON><PERSON><PERSON> from '@/api/errorResponseHandler'
const store = require('./store').default

// 获取地址栏中的参数
function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  const search = window.location.search.substr(1);
  const r = search.match(reg);
  if (r != null) {
      return decodeURIComponent(r[2]);
  }
  return null;
}

// 使用示例
// const id = getQueryString("id");

let workspaceId = ''
function getWsID () {
  // console.log('ceshi', router?.currentRoute?.query.workspaceId)
  if (store?.state.workSpace.currentWorkSpace.workspaceId) {
    workspaceId = store?.state.workSpace.currentWorkSpace.workspaceId
  } else {
    workspaceId = router?.currentRoute?.query.workspaceId
  }
  // console.log('到这是多少', workspaceId)
  if(!workspaceId) {
    workspaceId = getQueryString('workspaceId')
  }
  // if (localStorage.getItem('currentWorkSpace')) {
  //   const workSpace = JSON.parse(localStorage.getItem('currentWorkSpace'))
  //   workspaceId = workSpace.id
  // }
  return workspaceId
}

// const { flag, id, roleType, workspaceName } = store.state.workSpace.currentWorkSpace
// timeout 临时修改
const service = axios.create({
  baseURL: process.env.VUE_APP_API,
  timeout: 180000, // 设置请求超时时间为3分钟
  headers: {
    'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY,
    workspaceId: getWsID()
  }
})

service.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
// 设置全局的请求次数，请求的间隙
service.defaults.retry = 1
service.defaults.retryDelay = 500
// http request 拦截器
service.interceptors.request.use(
  (config) => {
    config.headers.workspaceId = getWsID()
    // console.log('请求信息', config)
    return config
  },
  (err) => {
    return Promise.reject(err)
  }
)

// 标记提示被踢出的数量
const tickoutTimer = 0
// 禁用的空间 普通用户

// http response 拦截器
service.interceptors.response.use(
  (response) => {
    if (response.data?.status === 417) {
      // 说明该用户没有用户角色，需要登出
      const routeName = router.currentRoute.name
      if (routeName !== 'guest') {
        window.location = '/#/guest'
      }
    }
    // console.log('location', location)
    // 手机端展示的时候去掉message弹窗提示
    if (response.data?.status !== 200 && response.data?.status !== 0) {
      // console.log(response)
      // if (response.data.status === 50001) {
      //   //gpu资源不足
      //   Vue.prototype.$message.error(response.data.msg)
      //   return
      // } else
      if (response.data?.status === 50002) {
        // 表示当前用户已久不在当前工作空间 需提示用户 && 刷新当前页面
        window.location.href = window.location.origin +'/#/homePage'
        // if (location.hash != '#/workSpace/index') {
        //   tickoutTimer++

        //   tickoutTimer === 1 &&
        //     Vue.prototype.$message.warning({
        //       message: '检测到您已经不所属当前工作空间！',
        //       offset: 40,
        //       onClose: () => {
        //         window.location.href = window.location.origin+'/#/workSpace/index'
        //       }
        //     })
        // }
      } else if (response.data?.status === 50003) {
        // 表示当前用户所属空间被禁用 && 刷新当前页面
        errorResponseHandler.deduplicateErrorResp(response)
      } else if (response.data?.status === 50004) {
        // 表示当前管理者所属空间被禁用 不可修改 删除 && 刷新当前页面
        errorResponseHandler.deduplicateErrorResp(response)
      }
    }
    return response
  },
  (error) => {
    console.log('错误信息', error);
    return Promise.reject(error)
  }
)
export default service
