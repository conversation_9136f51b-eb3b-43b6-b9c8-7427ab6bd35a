import { getList } from "@/api/labelMange"

const state={
    list:[],
}
const getters ={
    getList: (state) => {
        return state.list
    },
}
const mutations={
    setList(state, val) {
        if (Array.isArray(val)) {
            const uniqueMap = new Map();
            val.forEach(item => {
                if (item && item.name) {
                    uniqueMap.set(item.name, item);
                }
            });
            state.list = Array.from(uniqueMap.values());
        } else {
            state.list = val;
        }
    },
}
const actions={
}
export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
  }
  