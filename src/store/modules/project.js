import { Message } from 'element-ui';
import { getProjectInfo } from '@/api/modelProject';

export const UPDATE_PROJECT_INFO = 'UPDATE_PROJECT_INFO';
const state = {
  currentProjectId: null,
  projectInfo: {} // 当前项目信息
};
// 处理获取数据
const getters = {
  getCurrentProject: (state) => {
    return state.projectInfo
  }
};

// 同步操作
const mutations = {
  [UPDATE_PROJECT_INFO](state, payload) {
    console.log('first', state, payload)
    state.projectInfo = payload
    state.currentProjectId = payload.id
  }
};

// 异步操作
const actions = {
  fetchProjectInfo: {
    root: true,
    handler({ commit, state }, payload) {
      const id = payload
      // 如果当前的Id存在
      getProjectInfo({ id }).then((res) => {
        if (res.data.status === 200) {
          const data = res.data.data;
          commit(UPDATE_PROJECT_INFO, { ...data, id });
        }
        if (res.data.status === 50002) {
          Message.error(res.data.msg)
        }
      });
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
