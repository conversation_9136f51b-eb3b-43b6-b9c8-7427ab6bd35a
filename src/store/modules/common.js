import { CURRENT_OVERVIEW_TYPE, GLOBAL_NAVIGATOR_STATUS } from '../mutation-types'
import service from '@/axios'
export default {
  namespaced: true,
  state: {
    curOverviewType:
      parseInt(sessionStorage.getItem('overviewType')) || undefined,
    globalNavigatorStatus:
      localStorage.getItem('globalNavigatorStatus') !== null
        ? parseInt(localStorage.getItem('globalNavigatorStatus'))
        : 1, // 0 - 关闭导航菜单， 1 - 显示导航菜单， 默认值1;考虑拓展，使用map，不用boolean
    invokingLoading: false,
    offlineSwitch: {},
    deployEnvList: [],
    isLinKongProject: false,
    isAdmin: false,
    isAdminNew: false,
    isMenuCollapse: true,
    JujiaoStatus: {
      data:{},
      status: false
    },
    taskJujiaoStatus: {
      status: false,
      data: {}
    },
    JujiaoRole:{
      PTData: {
        currentRoleInfo: '',
        selectAgentRoleNew: {},
        showSelectRoleId:''
      },
      JJData:{
        currentRoleInfo: '',
        selectAgentRoleNew: {},
        showSelectRoleId:''
      }
    }
  },
  getters: {
    getJujiaoRole: (state) => {
      return state.JujiaoRole
    },
    gettaskJujiaoStatus: (state) => {
      return state.taskJujiaoStatus
    },
    getJujiaoStatus: (state) => {
      return state.JujiaoStatus
    },
    curOverviewTypeGetter: (state) => {
      return state.curOverviewType
    },
    globalNavigatorStatusGetter: (state) => {
      return state.globalNavigatorStatus ? true :false
    },
    getDeployEnvList: (state) => {
      return state.deployEnvList
    },
    getLinKongProjectFlag: (state) => {
      return state.isLinKongProject
    },
    getIsAdminGetter: (state) => {
      return state.isAdmin
    },
    getisAdminNewGetter: (state) => {
      return state.isAdminNew
    },
    getMenuCollapseGetter: (state) => {
      return state.isMenuCollapse
    }
  },
  mutations: {
    setJujiaoRole(state, val) {
      state.JujiaoRole = val
    },
    setTaskJujiaoStatus(state, val) {
      state.taskJujiaoStatus = val
    },
    setJujiaoStatus(state, val) {
      state.JujiaoStatus = val
    },
    [CURRENT_OVERVIEW_TYPE](state, payload) {
      state.curOverviewType = payload
      sessionStorage.setItem('overviewType', payload)
    },
    [GLOBAL_NAVIGATOR_STATUS](state, payload) {
      state.globalNavigatorStatus = payload
      localStorage.setItem('globalNavigatorStatus', payload)
    },
    setInvokingLoading(state, val) {
      state.invokingLoading = val
    },
    setOfflineSwitch(state, val) {
      state.offlineSwitch = val
    },
    setDeployEnvList(state, val) {
      state.deployEnvList = val
    },
    setLinKongProjectFlag(state, val) {
      state.isLinKongProject = val
    },
    setIsAdmin(state, val) {
      state.isAdmin = val
    },
    setIsAdminNew(state, val) {
      state.isAdminNew = val
    },
    setMenuCollapse(state, val) {
      console.log('菜单展开折叠',val);
      state.isMenuCollapse = val
    }
  },
  actions: {
    updateCurOverviewTypeAction({ commit, state }, payload) {
      commit(CURRENT_OVERVIEW_TYPE, payload)
    },
    updateGlobalNavigatorStatusAction({ commit, state }, payload) {
      commit(GLOBAL_NAVIGATOR_STATUS, payload)
    },
    getDeployEnvListAction({ commit, state }, payload) {
      // 获取部署环境列表
      service(
        {
          method: 'post',
          url: '/deploy/env/list'
        }
      ).then(res => {
        if (res?.data?.status === 200) {
          commit('setDeployEnvList', res.data?.data)
        }
      })
    },
    getLinKongProject({ commit, state }, payload) {
      // 获取部署环境列表
      service(
        {
          method: 'get',
          url: '/identity/authentication/linkong'
        }
      ).then(res => {
        if (res?.data?.status === 200) {
          commit('setLinKongProjectFlag', res.data?.data)
          if (res.data?.data) { // 如果是临空的需要换标题
            document.querySelector('ennew-navigation').shadowRoot.querySelector('nav').querySelector('.--enn-nav-project-name').textContent = '数字临空AI中台'
          } else {
            document.querySelector('ennew-navigation').shadowRoot.querySelector('nav').querySelector('.--enn-nav-project-name').textContent = '能力生产平台'
          }
        }
      })
    },
  }
}
