export default {
  namespaced: true,
  state: {
   newSchemeInfo: {},
    editVisible: false,
    embed: false,
    codeTestText: '',
    codeTestStatus: 0,
    topHeight: '100%'
  },
  mutations: {
    researchEdit(state, val) {
      state.newSchemeInfo = val
    },
    setEmbed(state, val) {
      state.embed = val
    },
    getEditVisible(state, val) {
      state.editVisible = val
    },
    getCodeTestText(state, val) {
      state.codeTestText += val
    },
    restCodeTestText(state, val) {
      state.codeTestText = val
    },
    setTopHeight(state, val) {
      state.topHeight = val
    },
    setCodeStatus(state, val) {
      state.codeTestStatus = val
    },

  }
}