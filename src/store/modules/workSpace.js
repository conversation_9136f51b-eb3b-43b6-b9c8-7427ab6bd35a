const state = {
  currentWorkSpace: {}, // 当前空间对象
  isSuperAdmin: undefined,
  isNew:false
}
// 处理获取数据
const getters = {
  getCurrentWorkSpace: (state) => {
    return state.currentWorkSpace
  },
  getIsNew: (state) => {
    return state.isNew
  },
  getIsSuperAdmin: (state) => {
    return state.isSuperAdmin
  },
}

// 同步操作
const mutations = {
  // 设置当前空间id
  setCurrentWorkSpace(state, val) {
    state.currentWorkSpace = val
  },
  setIsSuperAdmin(state, val) {
    state.isSuperAdmin = val
  },
  setIsNew(state, val) {
    state.isNew = val
  },
}

// 异步操作
const actions = {}


export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
