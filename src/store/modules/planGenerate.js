const state = {
  currentToken: undefined,
  isIframeHide:false,
  initSengMsg:'',
  // isFullScreen: '',
  isFullScreen: window.localStorage.getItem('isFullScreen') || '',
}
// 处理获取数据
const getters = {
  getInitSengMsg: (state) => {
    return state.initSengMsg
  }
}

// 同步操作
const mutations = {
  setIsFullScreen(state,val) {
    state.isFullScreen = val
  },
  // 设置当前方案生成token
  setCurrentToken(state, val) {
    console.log('setCurrentToken', val)
    state.currentToken = val
  },
  setHideIframe(state, value) {
    state.isIframeHide = value;
  },
  setInitSengMsg(state, value) {
    state.initSengMsg = value;
  },

}

// 异步操作
const actions = {}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
