// 工具函数 - 查找节点
function findNodeByPath(tree, path) {
 for (const node of tree) {
  if (node.objectKey === path) {
   return { node, parent: null, list: tree };
  }

  if (node.children?.length) {
   const result = findNodeInChildren(node, path);
   if (result) return result;
  }
 }
 // return null;
}

// 工具函数 - 在子节点中递归查找
function findNodeInChildren(node, targetPath) {
 for (const child of node.children) {
  if (child.objectKey === targetPath) {
   return {
    node: child,
    parent: node,
    list: node.children
   };
  }
  if (child.children?.length) {
   const found = findNodeInChildren(child, targetPath);
   if (found) return found;
  }
 }
 return null;
}

export default {
 namespaced: true,
 state: {
  treeData: [],
  monacoText: '',
  ability: {
   language: '',
   executeResult: '',
   isLoading: false,
   display_component: '',
   name: ''
  },
  isStop: false,
  isRunning: false,
  taskContent: ''
 },
 mutations: {
  setTaskContent(state, val) {
   state.taskContent = val
  },
  setIsStop(state, val) {
   state.isStop = val
  },
  setIsRunning(state, val) {
   state.isRunning = val
  },
  setTreeData(state, val) {
   state.treeData = val
  },
  setMonacoText(state, val) {
   state.monacoText = val.executeResult
   state.ability = val
  },
  setAbility(state, val) {
   state.ability = val
  },
  REMOVE_TREE_NODE(state, objectKey) {
   let parentToCheck = null; // 用于存储可能需要检查的父节点
   
   const removeNode = (nodes, parentNode = null) => {
     for (let i = 0; i < nodes.length; i++) {
       // 找到目标节点
       if (nodes[i].objectKey === objectKey) {
         // 如果是文件夹且有子节点，不能删除
         if (nodes[i].isDir && nodes[i].children && nodes[i].children.length > 0) {
           console.warn('无法删除非空文件夹:', nodes[i].objectName);
           return false;
         }
         
         // 记录被删除节点的父节点（如果是文件，可能需要检查父节点）
         if (!nodes[i].isDir && parentNode) {
           parentToCheck = parentNode;
         }
         
         // 删除当前节点
         nodes.splice(i, 1);
         
         return true;
       }
       
       // 递归检查子节点
       if (nodes[i].children && nodes[i].children.length > 0) {
         if (removeNode(nodes[i].children, nodes[i])) {
           // 如果子节点被删除，检查当前节点（父节点）是否需要删除
           if (parentToCheck === nodes[i] && nodes[i].children.length === 0) {
             // 从祖父节点中删除这个空父节点
             if (parentNode) {
               const index = parentNode.children.indexOf(nodes[i]);
               if (index !== -1) {
                 parentNode.children.splice(index, 1);
               }
             } else {
               // 如果是根节点的情况
               const index = state.treeData.indexOf(nodes[i]);
               if (index !== -1) {
                 state.treeData.splice(index, 1);
               }
             }
             parentToCheck = null; // 重置检查标记
           }
           return true;
         }
       }
     }
     return false;
   };
   
   removeNode(state.treeData);
   
   // 处理文件删除后父节点可能为空的情况（非递归路径）
   if (parentToCheck && parentToCheck.children.length === 0) {
     // 需要从祖父节点中删除这个空父节点
     const removeParent = (nodes) => {
       for (let i = 0; i < nodes.length; i++) {
         if (nodes[i] === parentToCheck) {
           nodes.splice(i, 1);
           return true;
         }
         if (nodes[i].children && nodes[i].children.length) {
           if (removeParent(nodes[i].children)) return true;
         }
       }
       return false;
     };
     removeParent(state.treeData);
   }
 },
  // REMOVE_TREE_NODE(state, objectKey) {
  //  console.log('fjdpafjdspfjsfsjdfsdstate.treeData',state.treeData)
  //  let parentToCheck = null; // 用于存储可能需要检查的父节点
  //  // 这个函数会直接修改状态树
  //  const removeNode = (nodes, parentNode = null) => {
  //   for (let i = 0; i < nodes.length; i++) {
  //    // 精确匹配要删除的节点
  //    if (nodes[i].objectKey === objectKey) {
  //     // 记录被删除节点的父节点（如果是文件，可能需要检查父节点）
  //     if (!nodes[i].isDir && parentNode) {
  //      parentToCheck = parentNode;
  //    }
  //     nodes.splice(i, 1);  // 关键操作：直接删除数组中的元素
  //     return true;
  //    }
  //    // 递归查找子节点
  //    if (nodes[i].children && removeNode(nodes[i].children)) {
  //     return true;
  //    }
  //   }
  //   return false;
  //  };
  //  removeNode(state.treeData);
  // },
  updateNodeChildren(state, { nodeKey, children, isRootLevel, currentNode = null,isUpload }) {
   console.log('新增----------1111', nodeKey, children, isRootLevel, currentNode)
   if (!isRootLevel) {
    const updateNode = (nodes) => {
     if (!nodes) return false;
     for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.objectKey === nodeKey) {
       // 1. 找出旧数组中匹配的所有节点
       const oldNodes = node.children || [];
       if(!currentNode) {
        return 
       }
       const index = children?.findIndex(item => item.objectKey === currentNode.objectKey && item.objectName === currentNode.objectName)
       const newNode = oldNodes?.find(i => i.objectKey === currentNode.objectKey && i.objectName === currentNode.objectName)
       const oldIndex = oldNodes.findIndex(i =>
        i.objectKey === currentNode.objectKey &&
        i.objectName === currentNode.objectName
       );
      console.log('fjdaspfjdspfjsdpfdsjp',oldIndex,index,)
       // 存在旧节点：先移除
       if (oldIndex !== -1) {
        oldNodes.splice(oldIndex, 1);
       }
       if (index !== -1) {
        oldNodes.splice(index, 0, {
         ...(isUpload ? children[index] : newNode),
         icons: children[index].icons
        })
       }

       // // 2. 创建一个新数组，初始为空
       // const newChildren = [];

       // // 3. 遍历新children数组
       // for (const newItem of children) {
       //  // 在旧节点中查找匹配项
       //  const matchedOldNode = oldNodes.find(old =>
       //   old.objectKey === newItem.objectKey &&
       //   old.objectName === newItem.objectName
       //  );

       //  if (matchedOldNode) {
       //   // 4. 如果找到匹配项，创建更新后的节点
       //   newChildren.push({
       //    ...matchedOldNode,       // 保留旧节点的状态            // 应用更新
       //    icons: newItem.icons     // 特别更新icons属性
       //   });
       //  } else {
       //   // 5. 如果没有匹配项，直接添加新节点
       //   console.log('newItem', newItem)
       //   newChildren.push(newItem);
       //  }
       // }

       // // 6. 更新节点的children
       // node.children = newChildren;
       return true;
      }
      if (node.children && node.children.length) {
       const found = updateNode(node.children);
       if (found) return true;
      }
     }
     return false;
    };
    updateNode(state.treeData);
   } else {
    const index = children?.findIndex(item => item.objectKey === currentNode.objectKey && item.objectName === currentNode.objectName)
    const newNode = state.treeData?.find(i => i.objectKey === currentNode.objectKey && i.objectName === currentNode.objectName)
    const oldIndex = state.treeData.findIndex(i =>
     i.objectKey === currentNode.objectKey &&
     i.objectName === currentNode.objectName
    );
    // 存在旧节点：先移除
    if (oldIndex !== -1) {
     state.treeData.splice(oldIndex, 1);
    }
    // state.treeData = state.treeData?.filter(i => !(i.objectKey === currentNode.objectKey &&  i.objectName === currentNode.objectName)) || []
    if (index !== -1 && newNode) {
     state.treeData.splice(index, 0, {
      ...newNode,
      icons: children[index].icons
     })
    }
   }
  },
  addNode(state, newNode) {
   const { node, parentPath, position } = newNode
   // 获取父节点路径
   // const parentPath = newNode.objectKey.substring(0, newNode.objectKey.lastIndexOf('/'))
   console.log('新增----------222', node, parentPath, position)
   const nodeExists = (nodeList, targetNode) => {
    return nodeList?.some(item =>
     item.objectKey === targetNode.objectKey &&
     item.objectName === targetNode.objectName
    ) || false;
   }
   if (!parentPath) {
    // 添加到根节点
    // state.treeData = state.treeData?.filter(i => !(i.objectKey === node.objectKey &&  i.objectName === node.objectName)) || []
    if (nodeExists(state.treeData, node)) {
     console.log('节点已存在于根节点，跳过新增');
     return;
    }
    state.treeData.splice(position, 0, node)
    // console.log('新增----------111144',state.treeData)
   } else {
    const updateNode = (nodes) => {
     if (!nodes) return false;
     for (let i = 0; i < nodes.length; i++) {
      const currentNode = nodes[i];
      if (currentNode.objectKey === parentPath) {
       if (nodeExists(currentNode.children, node)) {
        console.log('节点已存在于父节点下，跳过新增', currentNode);
        return true;
       }
       currentNode.children = currentNode.children?.filter(i => !(i.objectKey === node.objectKey && i.objectName === node.objectName)) || []
       // if( node.children[position] ) {
       // console.log('新增----------1111',currentNode,state.treeData)
       currentNode.children.splice(position, 0, node)
       // console.log('新增----------111144',state.treeData)
       // }
       console.log('dajfpsdjfpasdfjsad', node)
       return true;
      }
      if (currentNode.children && currentNode.children.length) {
       const found = updateNode(currentNode.children);
       if (found) return true;
      }
     }
     return false;
    };
    updateNode(state.treeData);

   }
   // if(!isRootLevel) {
   //  const updateNode = (nodes) => {
   //   if (!nodes) return false;
   //   for (let i = 0; i < nodes.length; i++) {
   //    const node = nodes[i];
   //    if (node.objectKey === nodeKey) {
   //       // 1. 找出旧数组中匹配的所有节点
   //       const oldNodes = node.children || [];

   //       // 2. 创建一个新数组，初始为空
   //       const newChildren = [];

   //       // 3. 遍历新children数组
   //       for (const newItem of children) {
   //         // 在旧节点中查找匹配项
   //         const matchedOldNode = oldNodes.find(old => 
   //           old.objectKey === newItem.objectKey && 
   //           old.objectName === newItem.objectName
   //         );

   //         if (matchedOldNode) {
   //           // 4. 如果找到匹配项，创建更新后的节点
   //           newChildren.push({
   //             ...matchedOldNode,       // 保留旧节点的状态            // 应用更新
   //             icons: newItem.icons     // 特别更新icons属性
   //           });
   //         } else {
   //           // 5. 如果没有匹配项，直接添加新节点
   //           console.log('newItem',newItem)
   //           newChildren.push(newItem);
   //         }
   //       }

   //       // 6. 更新节点的children
   //       node.children = newChildren;
   //     return true;
   //    }
   //    if (node.children && node.children.length) {
   //     const found = updateNode(node.children);
   //     if (found) return true;
   //    }
   //   }
   //   return false;
   //  };
   //  updateNode(state.treeData);
   // }else {
   //  const index = children?.findIndex(item => item.objectKey === currentNode.objectKey && item.objectName === currentNode.objectName)
   //  const newNode = state.treeData?.find(i => i.objectKey === currentNode.objectKey &&  i.objectName === currentNode.objectName)
   //  state.treeData = state.treeData?.filter(i => !(i.objectKey === currentNode.objectKey &&  i.objectName === currentNode.objectName)) || []
   //  if(index !== -1 && newNode) {
   //   state.treeData.splice(index,0, {
   //    ...newNode,
   //    icons: children[index].icons
   //   })
   //  }
   // }
  },
  // 更新节点
  updateNode(state, { path, data }) {

   const result = findNodeByPath(state.treeData, path);

   if (result && result.node) {
    // 更新节点属性，保留子节点
    Object.assign(result.node, {
     ...data,
     children: result.node.children
    });
   }
  },

 }
}