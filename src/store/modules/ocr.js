import service from '@/axios'
import { getCurrentUserInfo } from '@/api/planGenerateApi'
const state = {
  dataAll: {
    all: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//全部
    waitAi: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//待Ai识别
    comput: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//待人工标注
    done: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//已完成标注
    error: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//标记异常
    module: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    },//未匹配模板
    sberror: {
      data: [],
      page_num: 1,
      page_size: 8,
      total: 0
    }//识别异常
  },
  authorization: '',
  userInfo: {},
  acData: {
    acIndex: '',
    acImageData: '',
    acTabName: '全部',
  },
  acPageData: {
    list: [],
    page_num: 1,
    page_size: 8,
    total: 0
  },
  cache:{
    all: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//全部
    waitAi: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//待Ai识别
    comput: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//待人工标注
    done: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//已完成标注
    error: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//标记异常
    module: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//未匹配模板
    sberror: {
      page_num: 1,
      page_size: 8,
      image_id: ''
    },//识别异常
  },
  ImageInfo: {},
  showBot: true,
  loadingAll:false,
  isEdit: false,
  errorList:[],
  isSingle:false,//单页status
  option:[]//空间数据
}
// 处理获取数据
const getters = {
  getOption: (state) => {
    return state.option
  },
  getIsSingle: (state) => {
    return state.isSingle
  },
  getCache: (state) => {
    return state.cache
  },
  getIsEdit: (state) => {
    return state.isEdit
  },
  getLoadingAll: (state) => {
    return state.loadingAll
  },
  getShowBot: (state) => {
    return state.showBot
  },
  getAcPageData: (state) => {
    return state.acPageData
  },
  getData: (state) => {
    return state.dataAll
  },
  getAuthorization: (state) => {
    return state.authorization
  },
  getUserInfo: (state) => {
    return state.userInfo
  },
  getAcData: (state) => {
    return state.acData
  },
  getFetchImageInfoState: (state) => {
    return state.ImageInfo
  },
  getErrorList: (state) => {
    return state.errorList
  }
}

// 同步操作
const mutations = {
  setOption(state, val) {
    state.option = val
  },
  setIsSingle(state, val) {
    state.isSingle = val
  },
  setErrorList(state, val) {
    state.errorList = val
  },
  setCache(state, val) {
    let obj = {}
    switch (state.acData.acTabName) {
      case '全部':
        obj = Object.assign(obj, state.cache, { all: val})
        break;
      case '识别中':
        obj = Object.assign(obj, state.cache, { waitAi: val})
        break;
      case '待人工标注':
        obj = Object.assign(obj, state.cache, { comput: val})
        break;
      case '已完成标注':
        obj = Object.assign(obj, state.cache, { done: val})
        break;
      case '标记异常':
        obj = Object.assign(obj, state.cache, { error: val})
        break;
      case '未匹配模板':
        obj = Object.assign(obj, state.cache, { module: val})
        break;
      case '识别异常':
        obj = Object.assign(obj, state.cache, { sberror: val})
        break;
    }
    state.cache = obj
  },
  setIsEdit(state, val) {
    state.isEdit = val
  },
  setLoadingAll(state, val) {
    state.loadingAll = val
  },
  setShowBot(state, val) {
    state.showBot = val
  },
  setAcPageData(state, val) {
    state.acPageData = val
  },
  setData(state, val) {
    state.dataAll = val
  },
  setAuthorization(state, val) {
    state.authorization = val
  },
  setUserinfo(state, val) {
    state.userInfo = val
  },
  setAcData(state, val) {
    let obj = {}
    switch (state.acData.acTabName) {
      case '全部':
        obj = Object.assign(obj, {
          acTabName: '全部',
          acImageData: state.dataAll?.all?.data?.length > 0 ? state.dataAll?.all?.data[val.acIndex] : undefined,
        }, { ...val })
        break;
      case '识别中':
        obj = Object.assign(obj, {
          acTabName: '识别中',
          acImageData: state.dataAll?.waitAi?.data?.length > 0 ? state.dataAll.waitAi.data[val.acIndex] : undefined,
        }, val)
        break;
      case '待人工标注':
        obj = Object.assign(obj, {
          acTabName: '待人工标注',
          acImageData: state.dataAll?.comput?.data?.length > 0 ? state.dataAll.comput.data[val.acIndex] : undefined,
        }, val)
        break;
      case '已完成标注':
        obj = Object.assign(obj, {
          acTabName: '已完成标注',
          acImageData: state.dataAll?.done?.data?.length > 0 ? state.dataAll.done.data[val.acIndex] : undefined,
        }, val)
        break;
      case '标记异常':
        obj = Object.assign(obj, {
          acTabName: '标记异常',
          acImageData: state.dataAll?.error?.data?.length > 0 ? state.dataAll.error.data[val.acIndex] : undefined,
        }, val)
        break;
      case '未匹配模板':
        obj = Object.assign(obj, {
          acTabName: '未匹配模板',
          acImageData: state.dataAll?.module?.data?.length > 0 ? state.dataAll.module.data[val.acIndex] : undefined,
        }, val)
        break;
        case '识别异常':
          obj = Object.assign(obj, {
            acTabName: '识别异常',
            acImageData: state.dataAll?.sberror?.data?.length > 0 ? state.dataAll.sberror.data[val.acIndex] : undefined,
          }, val)
          break;        
    }
    state.acData = obj
  },
  setFetchImageInfoState(state, val) {
    state.ImageInfo = val
  }
}

// 异步操作
const actions = {
  async optionFetch({ commit, state }, payload) {
    const res = await service({
      method: 'post',
      url: process?.env?.VUE_APP_ENV=== 'production' ? 'https://open-platform-gateway.ennew.com/agent/scheme/20113' : 'https://open-platform-gateway.ennew.com/agent/scheme/20072',
      data: {
        test_data:{}
      },
      headers: { 'X-GW-Authorization': state.authorization }
    });
    let data = res.data?.result?.result.map(it =>{
      return {
        label: it,
        value: it
      }
    })
    commit('setOption', data)
  },
  async getCurrentUserInfoFun({ commit, state }, payload) {
    const res = await getCurrentUserInfo()
    commit('setUserinfo', res)
  },
  async fetchData({ commit, state }, payload) {
    // 获取部署环境列表
    commit('setLoadingAll', true)
    const res = await service({
      method: 'post',
      url: process?.env?.VUE_APP_ENV=== 'production' ? 'https://open-platform-gateway.ennew.com/agent/scheme/19460' : 'https://open-platform-gateway.ennew.com/agent/scheme/19336',
      data: {
        user_id: '',
        image_status: (state.acData.acTabName == '全部') ? '' : state.acData.acTabName,
        abnormal_state: null,
        ...payload
      },
      headers: { 'X-GW-Authorization': state.authorization }
    });
    switch (state.acData.acTabName) {
      case '全部':
        let { all, ...rest } = state.dataAll
        commit('setData', Object.assign(rest, { all: res?.data?.result?.result }))
        break
      case '识别中':
        let { waitAi, ...rest1 } = state.dataAll
        commit('setData', Object.assign(rest1, { waitAi: res?.data?.result?.result }))
        break
      case '待人工标注':
        let { comput, ...rest2 } = state.dataAll
        commit('setData', Object.assign(rest2, { comput: res?.data?.result?.result }))
        break
      case '已完成标注':
        let { done, ...rest3 } = state.dataAll
        commit('setData', Object.assign(rest3, { done: res?.data?.result?.result }))
        break
      case '标记异常':
        let { error, ...rest4 } = state.dataAll
        commit('setData', Object.assign(rest4, { error: res?.data?.result?.result }))
        break
      case '未匹配模板':
        let { module, ...rest5 } = state.dataAll
        commit('setData', Object.assign(rest5, { module: res?.data?.result?.result }))
        break
        case '识别异常':
          let { sberror, ...rest6 } = state.dataAll
          commit('setData', Object.assign(rest6, { sberror: res?.data?.result?.result }))
          break        
    }
    commit('setAcPageData', {
      list: res?.data?.result?.result?.data,
      page_num: res?.data?.result?.result?.page_num,
      page_size: res?.data?.result?.result?.page_size,
      total: res?.data?.result?.result?.total
    })
    commit('setLoadingAll', false)
  },
  async fetchAuthorization({ commit, state }, payload) {
    const url = 'https://middle-open-platform.ennew.com/admin/client/getToken?appKey=7b9e5201-2922-4506-951f-af43f653c718&appSecret=a0FvV2pRaUR4dzQ1YkpFZFdEZkVtUzBtNG9LN2g3Z3hiSDVUTVBaNmFrMGtIekFlSTlWUXMwNHBTcVNCQ3ZOSg=='
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok ' + response.statusText);
      }
      const data = await response.json();
      commit('setAuthorization', data.data)
    } catch (error) {
      console.error('Error:', error);
    }
  },
  async getFetchData({ commit, state }, payload) {
    commit('setLoadingAll', true)
    let fetchArray = []
    let statusArray = ['', '识别中', '待人工标注', '已完成标注', '标记异常', '识别异常','未匹配模板']
    statusArray.forEach((item) => {
      fetchArray.push(
        service({
          method: 'post',
          url: process?.env?.VUE_APP_ENV=== 'production' ? 'https://open-platform-gateway.ennew.com/agent/scheme/19460':'https://open-platform-gateway.ennew.com/agent/scheme/19336',
          data: {
            // tenant_id: state.userInfo.tenantId || 'str',
            // user_id: state.userInfo.userId || 'str',
            user_id: '',
            page_size: 8,
            page_num: 1,
            image_status: item,
            abnormal_state:  null,
            ...payload
          },
          headers: { 'X-GW-Authorization': state.authorization }
        })
      )
    });
    const res = await Promise.all(fetchArray);
    commit('setData', {
      all: res[0]?.data?.result?.result,//全部
      waitAi: res[1]?.data?.result?.result,//识别中
      comput: res[2]?.data?.result?.result,//待人工标注
      done: res[3]?.data?.result?.result,//已完成标注
      error: res[4]?.data?.result?.result,//标记异常
      sberror: res[5]?.data?.result?.result,//识别异常
      module: res[6]?.data?.result?.result//未匹配模板
    })
    // commit('setCache',)
    commit('setLoadingAll', false)
    switch (state.acData.acTabName) {
      case '全部':
        commit('setAcPageData', {
          list: state.dataAll?.all?.data,
          page_num: state.dataAll?.all?.page_num,
          page_size: state.dataAll?.all?.page_size,
          total: state.dataAll?.all?.total
        })
        break
      case '识别中':
        commit('setAcPageData', {
          list: state.dataAll?.waitAi?.data,
          page_num: state.dataAll?.waitAi?.page_num,
          page_size: state.dataAll?.waitAi?.page_size,
          total: state.dataAll?.waitAi?.total
        })
        break
      case '待人工标注':
        commit('setAcPageData', {
          list: state.dataAll?.comput?.data,
          page_num: state.dataAll?.comput?.page_num,
          page_size: state.dataAll?.comput?.page_size,
          total: state.dataAll?.comput?.total
        })
        break
      case '已完成标注':
        commit('setAcPageData', {
          list: state.dataAll?.done?.data,
          page_num: state.dataAll?.done?.page_num,
          page_size: state.dataAll?.done?.page_size,
          total: state.dataAll?.done?.total
        })
        break
      case '标记异常':
        commit('setAcPageData', {
          list: state.dataAll?.error?.data,
          page_num: state.dataAll?.error?.page_num,
          page_size: state.dataAll?.error?.page_size,
          total: state.dataAll?.error?.total
        })
        break
      case '未匹配模板':
        commit('setAcPageData', {
          list: state.dataAll?.module?.data,
          page_num: state.dataAll?.module?.page_num,
          page_size: state.dataAll?.module?.page_size,
          total: state.dataAll?.module?.total
        })
        break
        case '识别异常':
          commit('setAcPageData', {
            list: state.dataAll?.sberror?.data,
            page_num: state.dataAll?.sberror?.page_num,
            page_size: state.dataAll?.sberror?.page_size,
            total: state.dataAll?.sberror?.total
          })
          break        
    }
  },
  async getFetchImageInfo({ commit, state }, payload) {
    const res = await service({
      method: 'post',
      url:process?.env?.VUE_APP_ENV=== 'production' ? 'https://open-platform-gateway.ennew.com/agent/scheme/19461' : 'https://open-platform-gateway.ennew.com/agent/scheme/19338',
      data: {
        image_id: state.acData?.acImageData?.image_id || '',
        text_id: ''
      },
      headers: { 'X-GW-Authorization': state.authorization }
    })
    // extracted_text
    commit('setFetchImageInfoState', res.data?.result?.result)
  },
  async getErrorListFetch({ commit, state }, payload) {
    const res = await service({
      method: 'post',
      url:process?.env?.VUE_APP_ENV=== 'production' ? 'https://open-platform-gateway.ennew.com/agent/scheme/19517' : 'https://open-platform-gateway.ennew.com/agent/scheme/19470',
      data: {
        test_data: ''
      },
      headers: { 'X-GW-Authorization': state.authorization }
    })
    // extracted_text
    commit('setErrorList', res.data?.result?.result.map(it =>{
      if(it.code == '1'){
        it.tips = '需要恩牛优化'
      }else if(it.code == '2'){
        it.tips = '需要下次上门时清洁或触发周检时采集'
      }else if(it.code == '3'){
        it.tips = '触发下次上门APP采集多角度照片识别结果拼接或周检时采集'
      }else if(it.code == '4'){
        it.tips = '触发周检时采集'
      }else if(it.code == '5'){
        it.tips = '需要联系厂家更换'
      }else if(it.code == '6'){
        it.tips = '触发下次上门APP采集'
      }else if(it.code == '7'){
        it.tips = '改主板，改物联计费方式等，造成型号、表钢号与铭牌不一致的'
      }else if(it.code == '8'){
        it.tips = '不在以上类型的，需填写具体情况'
      }
      return it
    }))
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
