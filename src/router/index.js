import 'nprogress/nprogress.css'

import NProgress from 'nprogress'
const store = require('../store').default

// 和主容器之间约定好，对接微应用
export const appPrefix = '/'

/**
 * 路由拦截
 */
export const configRouter = (router) => {
  // const realBaseRoute = window.__MICRO_APP_BASE_ROUTE__
  router.beforeEach(async (to, from, next) => {
    console.log(to,'to.meta')
    if (to.path == '/homePage' || to.path == '/') {
      return next('/planGenerate/first?workspaceId=1&workspaceName=专用空间&menuType=2');
    }
    console.log('这里');
    if(to.query?.workspaceId) {
      localStorage.setItem('currentWorkSpace', JSON.stringify(to.query));
    }
    NProgress.start()
    // document.title = to.meta.title ? `${to.meta.title}` : '能力生产平台'
    if (typeof to.meta.title === 'function') {
      document.title = to.meta.title(to);
    } else if (to.meta.title) {
      document.title = to.meta.title;
    } else {
      document.title = '能力生产平台'; // 设置默认标题
    }
    // if (window.__MICRO_APP_ENVIRONMENT__) {
    //   if (typeof window.history.state.current === 'string') {
    //     window.history.state.current = window.history.state.current.replace(new RegExp(realBaseRoute, 'g'), '')
    //   }
    // }
    const noPath = ['/workSpace/index', '/workSpace/apply/history', '/resource/monitor', '/resources/resourcesManage', '/modelEngineering', '/homePage'];
    if (noPath.indexOf(to.path) < 0 && !to.query.workspaceId && from.query.workspaceId) {
      console.log('走这里', from.query.workspaceId);
      to.query.workspaceId = from.query.workspaceId
      to.query.workspaceName = from.query.workspaceName
    }
    next();
  })
  router.afterEach((to, from) => {
    // console.log('to----', to)
    // if (window.__MICRO_APP_ENVIRONMENT__) {
    //   if (typeof window.history.state === 'object') {
    //     window.history.state.current = realBaseRoute + (window.history.state.current || '')
    //   }
    // }
    const fullUrl = window.location.href;
    console.log(fullUrl,'fullUrl')
    // 向父窗口发送路由消息
    if(fullUrl.indexOf('planGenerate') > -1 || fullUrl.indexOf('abilityCenter') > -1 || fullUrl.indexOf('knowledgeBase') > -1){  //knowledgeBase/portal
      window.parent.postMessage(JSON.stringify({
        iframeUrl:fullUrl
      }), '*'); // '*' 表示允许任何来源接收消息
    }
    NProgress.done()
    window.parent.postMessage(JSON.stringify({result:'done'}), process.env.VUE_APP_ENNEW_IFRAME);
  })

  /**
   * 修复vue-router重复跳转到当前路由的报错
   */
  const originalPush = router.push
  router.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
  }
}
