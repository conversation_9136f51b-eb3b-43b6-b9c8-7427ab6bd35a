import { router } from '../main'
const store = require('../store').default
window.addEventListener('message', (e) => {
  // console.log(process.env.VUE_APP_ENNEW_IFRAME,' router-VUE_APP_ENNEW_IFRAME')
  // console.log(e.origin, 'e.router-eOrigin')
  // console.log(e.data, 'e.data')
  if (e.origin === process.env.VUE_APP_ENNEW_IFRAME) {
    // const data = e.data;
    // console.log(data,'外层传进来的数据')
    store.commit('planGenerate/setHideIframe', true)
  }
})

if (window.top !== window.self) {
  console.log('当前页面被嵌入到了iframe中,routerAAAAAA')
  const sign = new URLSearchParams(window.location.href).get('showHead')
  store.commit('planGenerate/setHideIframe', !sign)
  // 在这里你可以决定是否初始化某些插件，或者执行其他逻辑
}

const planGenerateRoutes = [
  {
    path: '/planGenerate/runTask',
    name: 'planGenerateRunTask',
    meta: {
      title: '任务列表',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/runTask',
      breadcrumbGenerator() {
        const route = router.history.current;
        // const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }else if(fromMenu === '5') {
         label= '能力样版'
         urlPath = '/planGenerate/sampleAbility'
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu:fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/ConfTask/ConfTaskPlanchatNew.vue'], resolve)
  },
  {
    path: '/planGenerate/first',
    name: 'planGenerateFirst',
    meta: {
      title: '专家生产',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/first',
      breadcrumbData: [
        {
          label: '专家生产'
        }
      ]
    },
    component: (resolve) => require(['@/views/planGenerate/First.vue'], resolve)
  },
  {
    path: '/planGenerate/guidance',
    name: 'planGenerateGuidance',
    meta: {
      title: '生产指引',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/guidance',
      breadcrumbData: [
        {
          label: '生产指引'
        }
      ]
    },
    component: (resolve) => require(['@/views/planGenerate/guidance.vue'], resolve)
  },
  {
    path: '/planGenerate/taskRd',
    name: 'planGenerateTask',
    meta: {
      title: '任务规划',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/taskRd',
      breadcrumbData: [
        {
          label: '任务规划'
        }
      ]
    },
    component: (resolve) => require(['@/views/planGenerate/indexTask.vue'], resolve)
  },
  {
    path: '/planGenerate/partnerchat',
    name: 'planGenerateFirst',
    meta: {
      title: '专家生产',
      GuidePath: true,
      zhunjiaFlag: true,
      isShow: true,
      JumpPath: '/planGenerate/partnerchat',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '专家生产',
            route: {
              path: '/planGenerate/first',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) =>
      require(['@/views/planGenerate/wlls/IntelligentPartnerProduction.vue'], resolve)
  },
  {
    path: '/planGenerate/partnerCreate',
    name: 'planGenerateFirst',
    meta: {
      title: '专家生产',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/partnerCreate',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '专家生产',
            route: {
              path: '/planGenerate/first',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '新建专家生产'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/PartnerCreateZhushou.vue'], resolve)
  },
  {
    path: '/planGenerate/index',
    name: 'planGenerateIndex',
    meta: {
      title: '研发生产',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/index',
      breadcrumbData: [
        {
          label: '研发生产'
        }
      ]
    },
    component: (resolve) => require(['@/views/planGenerate/index.vue'], resolve)
  },
  {
    path: '/planGenerate/validation',
    name: 'planGenerateValidation',
    meta: {
      title: '训练与验证',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/validation',
      breadcrumbData: [
        {
          label: '训练与验证'
        }
      ]
    },
    component: (resolve) => require(['@/views/planGenerate/validation.vue'], resolve)
  },
  {
    path: '/planGenerate/copypartnerchat',
    name: 'planGenerateIndex',
    meta: {
      title: '研发生产',
      GuidePath: true,
      zhunjiaFlag: true,
      isShow: true,
      JumpPath: '/planGenerate/copypartnerchat',
      breadcrumbGenerator() {
        const route = router.history.current
        const datasetTypeName = route.query.planName
        return [
          {
            label: datasetTypeName || '研发生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ]
      }
    },
    component: (resolve) =>
      require(['@/views/planGenerate/wlls/IntelligentPartnerProduction.vue'], resolve)
  },
  {
    path: '/planGenerate/task',
    name: 'planGenerateIndex',
    meta: {
      title: '任务信息',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/task',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        let fromMenu = route.query.fromMenu
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }
        console.log('route', route);
        return [
          {
            label: datasetTypeName || '任务规划',
            route: {
              path: '/planGenerate/taskRd',
              query: {
                fromMenu:label,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '任务信息'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/task.vue'], resolve)
  },
  {
    path: '/planGenerate/planchat',
    name: 'planGenerateFirst',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      isShow: true,
      JumpPath: '/planGenerate/planchat',
      editIsShow: false,
      editFn: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        // const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }else if(fromMenu === '5') {
         label= '能力样版'
         urlPath = '/planGenerate/sampleAbility'
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu:fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/zhushouChat.vue'], resolve)
  },
  {
    path: '/planGenerate/wllsDevPlanchat',
    name: 'planGenerateIndex',
    meta: {
      title(route){
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/planchat',
      isShow: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/wlls/developChat.vue'], resolve)
  },
  {
    path: '/planGenerate/wllsExpertPlanchat',
    name: 'planGenerateIndex',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      editFn: true,
      isShow: true,
      JumpPath: '/planGenerate/planchat',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/wlls/expertChat.vue'], resolve)
  },
  {
    path: '/planGenerate/hqwPlanchat',
    name: 'planGenerateIndex',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/planchat',
      isShow: true,
      editFn: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/hqw/hqwPlanchat.vue'], resolve)
  },
  {
    path: '/planGenerate/ConfTaskPlanchat',
    name: 'planGenerateFirst',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      isShow: true,
      JumpPath: '/planGenerate/planchat',
      editIsShow: false,
      editFn: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }else if(fromMenu === '5') {
         label= '能力样版'
         urlPath = '/planGenerate/sampleAbility'
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/ConfTask/ConfTaskPlanchat.vue'], resolve)
  },
  // 场景配置
  {
    path: '/planGenerate/sceneConfig',
    name:'planGenerateSceneConfig',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/planchat',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        }
        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                fromMenu,
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '场景配置'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/sceneConfig/index'], resolve)
  },
  // 通用组件
  {
    path: '/planGenerate/DuiqiModel',
    name: 'DuiqiModel',
    meta: {
      title: '通用组件',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  {
    path: '/planGenerate/funcList',
    name: 'funcList',
    meta: {
      title: '函数组件',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  {
    path: '/planGenerate/abilityComponentList',
    name: 'abilityComponentList',
    meta: {
      title: '能力组件',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  {
    path: '/planGenerate/apiDocumentDetails',
    name: 'apiDocumentDetails',
    meta: {
      title: '能力组件',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/abilityComponentList'
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  {
    path: '/planGenerate/edit',
    name: 'planGenerateFirst',
    meta: {
      title(route) {
        const fromMenu = route.query.fromMenu;
        let label = ''
        if(fromMenu === '1'){
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
        }else if(fromMenu === '2'){
          label= '任务规划' 
        }else if(fromMenu === '4'){
          label= '训练与验证' 
        }
        return label
      },
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/index',
      breadcrumbGenerator() {
        const route = router.history.current;
        const fromMenu = route.query.fromMenu;
        let label = ''
        let urlPath = ''
        if(fromMenu === '1'){
          urlPath= '/planGenerate/first' 
          label= '专家生产'
        }else if(fromMenu === '3'){
          label= '研发生产'
          urlPath= '/planGenerate/index' 
        }else if(fromMenu === '2'){
          label= '任务规划'
          urlPath= '/planGenerate/taskRd' 
        }else if(fromMenu === '4'){
          label= '训练与验证'
          urlPath= '/planGenerate/validation' 
        } 

        return [
          {
            label: label,
            route: {
              path: urlPath,
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: route.query.id ? '编辑' : '新建'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/editZhushou.vue'], resolve)
  },
  {
    path: '/planGenerate/create',
    name: 'planGenerateIndex',
    meta: {
      title: '研发生产',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/index',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '研发生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '新建研发生产'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/createZhushou.vue'], resolve)
  },
  {
    path: '/planGenerate/createTask',
    name: 'planGenerateTaskIndex',
    meta: {
      title: '任务规划',
      GuidePath: true,
      zhunjiaFlag: true,
      JumpPath: '/planGenerate/taskRd',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '任务规划',
            route: {
              path: '/planGenerate/taskRd',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '新建任务规划'
          }
        ];
      }
    },
    component: (resolve) => require(['@/views/planGenerate/createTaskZhushou.vue'], resolve)
  },
  {
    path: '/abilityCenter/test',
    name: 'marketTest',
    component: () => import('@/views/planGenerate/marketApp.vue'),
    meta: {
      title: '能力仓库试一试',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '试一试'
          }
        ];
      }
    }
  },
  {
    path: '/AlgoabilityCenter/test',
    name: 'marketTest',
    component: () => import('@/views/planGenerate/components/AlgoTest.vue'),
    meta: {
      title: '能力仓库试一试',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '试一试'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/talk',
    name: 'abilityTalk',
    component: () => import('@/views/planGenerate/abilityTalk/index.vue'),
    meta: {
      title: '能力仓库对话',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库对话'
          }
        ];
      }
    }
  },
  // 生产场景模版
  {
    path: '/planGenerate/sceneModel',
    name: 'sceneModel',
    meta: {
      title: '场景设计',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  // 能力编排
  {
    path: '/planGenerate/abilityModel',
    name: 'abilityModel',
    meta: {
      title: '能力编排',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  // 生产辅助角色
  {
    path: '/planGenerate/abilityRole',
    name: 'abilityRole',
    meta: {
      title: '角色设计',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  // 原子能力设计
  {
    path: '/planGenerate/abilityTool',
    name: 'abilityTool',
    meta: {
      title: '原子能力',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  // 能力调试
  {
    path: '/planGenerate/abilityDebug',
    name: 'abilityDebug',
    meta: {
      title: '能力调试',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/agentIFrame.vue'], resolve)
  },
  // aip-search
  {
    path: '/planGenerate/aipSearch',
    name: 'aipSearch',
    meta: {
      title: '智能搜索',
      GuidePath: false,
      zhunjiaFlag: true
    },
    component: (resolve) => require(['@/views/planGenerate/aipSearch.vue'], resolve)
  },
  {
    path: '/abilityCenter/targetList/detailFull',
    name: 'abilityTalk',
    component: () => import('@/views/planGenerate/abilityTalk/fullIndex.vue'),
    meta: {
      title: '能力仓库对话',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库对话'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/targetList/approvecode',
    name: 'approvecode',
    component: () => import('@/views/planGenerate/abilityTalk/approvecode.vue'),
    meta: {
      title: '能力仓库对话',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库对话'
          }
        ]
      }
    }
  },
  {
   path: '/planGenerate/sampleAbility',
   name: 'SampleAbility',
   meta: {
     title: '能力样版',
     GuidePath: true,
     zhunjiaFlag: true,
     JumpPath: '/planGenerate/sampleAbility',
     breadcrumbData: [
       {
         label: '能力样版'
       }
     ]
   },
   component: () => import('@/views/planGenerate/sampleAbility/index.vue'),
 },
 {
   path: '/planGenerate/sampleEdit',
   name: 'SampleEdit',
   meta: {
     title: '编辑',
     GuidePath: true,
     zhunjiaFlag: true,
     JumpPath: '/planGenerate/sampleAbility',
     breadcrumbGenerator() {
      const route = router.history.current;
      return [
        {
          label: '能力样版',
          route: {
            path: '/planGenerate/sampleAbility',
            query: {
              workspaceId: route.query.workspaceId,
              workspaceName: route.query.workspaceName
            }
          }
        },
        {
          label: '编辑'
        }
      ];
    }
   },
   component: () => import('@/views/planGenerate/sampleAbility/component/sampleEdit.vue'),
 }
];

export default planGenerateRoutes
