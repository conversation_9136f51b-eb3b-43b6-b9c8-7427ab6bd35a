import { router } from '../main';
import targetList from '@/views/planGenerate/targetList.vue';
const iframeRoutes = [
  // {
  //   path: '/osRuleEngine',
  //   name: 'osRuleEngine',
  //   component: () => import('@/views/iframeView/index.vue'),
  //   meta: {
  //     title: '认知生产',
  //     breadcrumbData: [
  //       {
  //         label: '认知生产'
  //       },
  //     ]
  //   }
  // },
  // 能力编排
  {
    path: '/abilityCenter/ability',
    name: 'ability',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '能力管理',
      breadcrumbData: [
        {
          label: '能力编排'
        },
        {
          label: '能力管理'
        }
      ]
    }
  },
  {
    path: '/abilityCenter/dataMapList',
    name: 'dataMapList',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '能力迭代',
      breadcrumbData: [
        {
          label: '能力编排'
        },
        {
          label: '能力迭代'
        }
      ]
    }
  },
  {
    path: '/abilityCenter/targetList',
    name: 'targetList',
    component: () => import('@/views/planGenerate/newTargetList.vue'), // todo 最后改成targetList.vue  / newTargetList.vue
    meta: {
      title: '能力仓库',
      zhunjiaFlag: true,
      breadcrumbData: [
        {
          label: '能力仓库'
        }
      ]
    }
  },
  {
    path: '/abilityCenter/targetList/detail',
    name: 'marketDetail',
    component: () => import('@/views/planGenerate/marketDetail/index.vue'),
    meta: {
      title: '能力仓库详情',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库详情'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/AlgoTargetList/detail',
    name: 'AlgomarketDetail',
    component: () => import('@/views/planGenerate/AlgoMarketDetail/index.vue'),
    meta: {
      title: '能力仓库详情',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库详情'
          }
        ];
      }
    }
  },

  {
    path: '/abilityCenter/targetList/versionDetail',
    name: 'marketVersionDetail',
    component: () => import('@/views/planGenerate/marketDetail/indexVersion.vue'),
    meta: {
      title: '能力仓库详情',
      zhunjiaFlag: true,
      JumpPath: '/abilityCenter/targetList',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力仓库',
            route: {
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力仓库详情',
            route: {
              path: '/abilityCenter/targetList/detail',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName,
                ...route.query
              }
            }
          },
          {
            label: '版本详情'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/ability/orchestrationEngine',
    name: 'orchestrationEngine',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '能力编排引擎',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力编排'
          },
          {
            label: '能力管理',
            route: {
              path: '/abilityCenter/ability',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力编排引擎'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/ability/abilityModel',
    name: 'abilityModel',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '能力组件',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力编排'
          },
          {
            label: '能力管理',
            route: {
              path: '/abilityCenter/ability',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力组件'
          }
        ];
      }
    }
  },
  {
    path: '/abilityCenter/ability/assemblyTest',
    name: 'assemblyTest',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '能力测试',
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '能力编排'
          },
          {
            label: '能力管理',
            route: {
              path: '/abilityCenter/ability',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '能力测试'
          }
        ];
      }
    }
  },
  // 平台运营
  {
    path: '/operatingCenter/electronicFence',
    name: 'electronicFence',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '电子围栏',
      breadcrumbData: [
        {
          label: '平台运营'
        },
        {
          label: '电子围栏'
        }
      ]
    }
  },
  {
    path: '/operatingCenter/taskMonitor',
    name: 'taskMonitor',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '任务监控',
      breadcrumbData: [
        {
          label: '平台运营'
        },
        {
          label: '任务监控'
        }
      ]
    }
  },
  {
    path: '/operatingCenter/businessReports',
    name: 'businessReports',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '分布式作业报表',
      breadcrumbData: [
        {
          label: '平台运营'
        },
        {
          label: '分布式作业报表'
        }
      ]
    }
  },
  {
    path: '/operatingCenter/offlineData',
    name: 'offlineData',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '场站边端数据',
      breadcrumbData: [
        {
          label: '平台运营'
        },
        {
          label: '场站边端数据'
        }
      ]
    }
  },

  {
    path: '/planGenerate/mechanism/model',
    name: 'model',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '智能生产',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '智能生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '任务信息',
            route: {
              path: '/planGenerate/task',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName,
                ...route.query
              }
            }
          },
          {
            label: '能力组装'
          }
        ];
      }
    }
  },
  /*{
    path: '/planGenerate/mechanism/knowledgeClassify',
    name: 'knowledgeBasePortal',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识工程',
      breadcrumbGenerator() {
        const route = router.history.current
        const datasetTypeName = route.query.planName
        return [
          {
            label: datasetTypeName || '知识工程',
            // route: {
            //   path: '/planGenerate/index',
            //   query: {workspaceId: route.query.workspaceId, workspaceName: route.query.workspaceName}
            // }
          },
          {
            label: '知识库'
          }
        ]
      }
    }
  },*/
  {
    path: '/planGenerate/mechanism/designDocument',
    name: 'designDocument',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '文档设计',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '智能生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '任务信息',
            route: {
              path: '/planGenerate/task',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName,
                ...route.query
              }
            }
          },
          {
            label: '文档设计'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/settingsManage/manage',
    name: 'knowledgeBaseSettingsManageManage',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识库管理',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '知识库管理'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/settingsManage/tagManage',
    name: 'knowledgeBaseSettingsManageTagManage',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '标签管理',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '标签管理'
          }
        ];
      }
    }
  },
  {
    path: '/planGenerate/mechanism/knowledgeScene',
    name: 'knowledgeScene',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '故障树维护',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/planGenerate/mechanism/knowledgeScene?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '故障树维护'
          }
        ];
      }
    }
  },
  {
    path: '/planGenerate/mechanism/solutionList',
    name: 'solutionList',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '方案知识管理',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/planGenerate/mechanism/knowledgeScene?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '方案知识管理'
          }
        ];
      }
    }
  },
  {
    path: '/planGenerate/mechanism/knowledgeCollation',
    name: 'knowledgeCollation',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识收集',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '知识工程'
            // route: {
            //   path: '/planGenerate/index',
            //   query: {workspaceId: route.query.workspaceId, workspaceName: route.query.workspaceName}
            // }
          },
          // {
          //   label: '任务信息',
          //   route: {
          //     path: '/planGenerate/task',
          //     query: {workspaceId: route.query.workspaceId, workspaceName: route.query.workspaceName, ...route.query}
          //   }
          // },
          {
            label: '知识收集'
          }
        ];
      }
    }
  },
  {
    path: '/planGenerate/mechanism/devlopmentAlgorithm',
    name: 'devlopmentAlgorithm',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '智能生产',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '智能生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '模型开发'
          }
        ];
      }
    }
  },
  {
    path: '/planGenerate/mechanism/developmentMechanism',
    name: 'developmentMechanism',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '智能生产',
      breadcrumbGenerator() {
        const route = router.history.current;
        const datasetTypeName = route.query.planName;
        return [
          {
            label: datasetTypeName || '智能生产',
            route: {
              path: '/planGenerate/index',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName
              }
            }
          },
          {
            label: '任务信息',
            route: {
              path: '/planGenerate/task',
              query: {
                workspaceId: route.query.workspaceId,
                workspaceName: route.query.workspaceName,
                ...route.query
              }
            }
          },
          {
            label: '规则开发'
          }
        ];
      }
    }
  },
  // 智能能力研发任务管理
  {
    path: '/planGenTask/taskdetail',
    name: 'taskdetail',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '智能生产',
      breadcrumbData: [
        {
          label: '智能生产'
        },
        {
          label: '任务'
        }
      ]
    }
  },
  {
    path: '/multiModel/model',
    name: 'modelMarket',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '模型市场',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '模型广场',
            route: `/multiModel/model?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '模型市场'
          }
        ];
      }
    }
  },
  {
    path: '/multiModel/chat',
    name: 'modelChat',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '大模型广场',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '模型广场',
            route: `/multiModel/model?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '大模型广场'
          }
        ];
      }
    }
  },
  {
    path: '/multiModel/image',
    name: 'modelImage',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '多模态验证',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '模型广场',
            route: `/multiModel/model?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '多模态验证'
          }
        ];
      }
    }
  },
  {
    path: '/playground/chat',
    name: 'playgroundChat',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '助手提示验证',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '模型广场',
            route: `/multiModel/model?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '助手提示验证'
          }
        ];
      }
    }
  },
  {
    path: '/playground/completions',
    name: 'playgroundCompletions',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '完形填空验证',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '模型广场',
            route: `/multiModel/model?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '完形填空验证'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/portal',
    name: 'knowledgeBasePortal',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识库',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '知识库'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/knowledgeSearch',
    name: 'knowledgeBaseKnowledgeSearch',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识检索',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '知识检索'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/privateKnowledge',
    name: 'knowledgeBasePrivateKnowledge',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识问答',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '知识问答'
          }
        ];
      }
    }
  },
  {
    path: '/knowledgeBase/feedback',
    name: 'knowledgeBaseFeedback',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: '知识反馈',
      zhunjiaFlag: true,
      breadcrumbGenerator() {
        const route = router.history.current;
        return [
          {
            label: '知识工程',
            route: `/knowledgeBase/portal?workspaceId=${route.query.workspaceId}&workspaceName=${route.query.workspaceName}`
          },
          {
            label: '知识反馈'
          }
        ];
      }
    }
  },
  {
    path: '/systemManage/apiAuthorization',
    name: 'systemManageApiAuthorization',
    component: () => import('@/views/iframeView/index.vue'),
    meta: {
      title: 'API授权',
      zhunjiaFlag: true,
      breadcrumbData: [
        {
          label: 'API授权'
        }
      ]
    }
  }
];

export default iframeRoutes
