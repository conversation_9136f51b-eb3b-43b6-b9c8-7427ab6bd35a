import './public-path'
import AuthService from '@/services/auth'
import ClientMonitor from 'skywalking-client-js'
let instance = null
let router = null

/**
 * 渲染函数
 * 两种情况：主应用生命周期钩子中运行 / 微应用单独启动时运行
 */
async function render(props = {}) {
  // 加载外部公共库
  console.log('B项目实例',process.env)
  const { loadExternalLib } = require('@/utils/loadExternalLib')
  await loadExternalLib()

  // 加载公共库，以及初始化各种库配置
  require('@/utils/loadCommonLib')
  const Vue = require('vue')
  Vue.config.productionTip = false

  // 处理[AlgorithmWrite]组件内乱用XML标签导致的报错
  Vue.config.ignoredElements = [
    'xml',
    'category',
    'block',
    'mutation'
  ]

  const VueRouter = require('vue-router')
  const store = require('./store').default
  const App = require('./App.vue').default
  const { configRouter } = require('./router')
  const routes = require('./router/routes').default
  require('@/assets/images/svg-icon/index')
  let isShow = true;
  // 检测是否被iframe嵌套
  if (window.top !== window.self) {
    console.log('当前页面被iframe嵌套',window.location);
    const sign = new URLSearchParams(window.location.href).get('showHead')
    isShow = !!sign;
    // 在这里你可以决定是否初始化某些插件，或者执行其他逻辑
  } else {
    console.log('当前页面是直接打开的');
    isShow = false;
  }

  try {
    /**
     * 统一鉴权SDK
     * 将实例挂载至vue原型链中，以便其它组件调用 登出功能 （this.authSdk.logout()） 切换租户功能(this.authSdk.selectTenant())
     */
    const auth = await AuthService.initialize(isShow)
     // 将认证服务挂载到 Vue 实例
    Vue.prototype.authSdk = auth.getAuthSDK()
    Vue.prototype.userInfo = auth.getUserInfo()
    if(process.env.VUE_APP_ENV == 'production'){
      ClientMonitor.register({
        collector: "https://apm-gateway.ennew.com/report/ghvmtyinjf",
        service: 'expert-agent-web|xz|prod',
        serviceVersion: 'v1.0.0',
        pagePath: location.href
        });
    }else {
      let env = ''
      let collector = ''
      if(process.env.VUE_APP_ENV == 'dev'){
        env = 'dev'
        collector= 'gszjscfxql'
      }else if(process.env.VUE_APP_ENV == 'fat'){
        env = 'fat'
        collector= 'fnlgerhrbf'
      }else if(process.env.VUE_APP_ENV == 'uat'){
        env = 'uat'
        collector= 'tgtatmmnrh'
      }
      ClientMonitor.register({
        collector: `https://apm-gateway.dev.ennew.com/report/${collector}`,
        service: `expert-agent-web|xz|${env}`,
        serviceVersion: 'v1.0.0',
        pagePath: location.href
        });
    }    

    router = new VueRouter({
      // 运行在主应用中时，添加路由命名空间 /vue
      // base: window.__POWERED_BY_QIANKUN__ ? appPrefix : '/',
      base: window.__MICRO_APP_BASE_ROUTE__ || '/',
      linkActiveClass: 'open active',
      scrollBehavior: () => ({
        y: 0
      }),
      routes
    })
    configRouter(router)
    // 挂载应用
    instance = new Vue({
      store,
      router,
      render: (h) => h(App)
    }).$mount('#app')
  } catch (e) {
    console.log(e)
  }
}

// 独立运行时，直接挂载应用
if (!window.__POWERED_BY_QIANKUN__) {
  render()
}

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log('VueMicroApp bootstraped')
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  console.log('VueMicroApp mount', props)
  render(props)
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount() {
  console.log('VueMicroApp unmount')
  instance.$destroy()
  instance = null
  router = null
}

export { router }
