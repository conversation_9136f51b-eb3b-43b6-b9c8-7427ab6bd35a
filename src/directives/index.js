import store from '../store'

// 注册一个全局自定义指令 `v-wsPower`
Vue.directive('wsPower', {
  // 当被绑定的元素插入到 DOM 中时……
  inserted: function (el) {
    const { flag, id, roleType, workspaceName } = store.state.workSpace.currentWorkSpace
    console.log('全局状态', store.state.workSpace.currentWorkSpace, roleType, el.style.display)
    if (roleType === 'WORKSPACE_ADMIN' || roleType === 'SUPER_ADMIN') {
      el.style.display = 'initial'
    } else if (roleType === 'GENERAL_USER') {
      el.style.display = 'none'
    }
  },
  update: function (el) {
    const { flag, id, roleType, workspaceName } = store.state.workSpace.currentWorkSpace
    // console.log(roleType, el.style.display)
    console.log('全局状态3', store.state.workSpace.currentWorkSpace, roleType, el.style.display)
    if (roleType === 'WORKSPACE_ADMIN' || roleType === 'SUPER_ADMIN') {
      el.style.display = 'initial'
    } else if (roleType === 'GENERAL_USER') {
      el.style.display = 'none'
    }
  },
  componentUpdated: function (el) {
    const { flag, id, roleType, workspaceName } = store.state.workSpace.currentWorkSpace
    // console.log(roleType, el.style.display)
    console.log('全局状态2', store.state.workSpace.currentWorkSpace, roleType, el.style.display)
    if (roleType === 'WORKSPACE_ADMIN' || roleType === 'SUPER_ADMIN') {
      el.style.display = 'initial'
    } else if (roleType === 'GENERAL_USER') {
      el.style.display = 'none'
    }
  },
})
