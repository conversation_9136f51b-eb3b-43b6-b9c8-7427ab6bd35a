import Hljs from 'highlight.js'
import python from 'highlight.js/lib/languages/python';
// import 'highlight.js/styles/github.css'
import 'highlight.js/styles/stackoverflow-dark.css'

Hljs.configure({ ignoreUnescapedHTML: true })
Hljs.registerLanguage('python', python);
Hljs.registerLanguage('xml', require('highlight.js/lib/languages/xml'));
Hljs.registerLanguage('bash', require('highlight.js/lib/languages/bash'));
Hljs.registerLanguage('c', require('highlight.js/lib/languages/c'));
Hljs.registerLanguage('cpp', require('highlight.js/lib/languages/cpp'));
Hljs.registerLanguage('csharp', require('highlight.js/lib/languages/csharp'));
Hljs.registerLanguage('css', require('highlight.js/lib/languages/css'));
Hljs.registerLanguage('markdown', require('highlight.js/lib/languages/markdown'));
Hljs.registerLanguage('diff', require('highlight.js/lib/languages/diff'));
Hljs.registerLanguage('ruby', require('highlight.js/lib/languages/ruby'));
Hljs.registerLanguage('go', require('highlight.js/lib/languages/go'));
Hljs.registerLanguage('ini', require('highlight.js/lib/languages/ini'));
Hljs.registerLanguage('java', require('highlight.js/lib/languages/java'));
Hljs.registerLanguage('javascript', require('highlight.js/lib/languages/javascript'));
Hljs.registerLanguage('json', require('highlight.js/lib/languages/json'));
Hljs.registerLanguage('kotlin', require('highlight.js/lib/languages/kotlin'));
Hljs.registerLanguage('less', require('highlight.js/lib/languages/less'));
Hljs.registerLanguage('lua', require('highlight.js/lib/languages/lua'));
Hljs.registerLanguage('makefile', require('highlight.js/lib/languages/makefile'));
Hljs.registerLanguage('perl', require('highlight.js/lib/languages/perl'));
Hljs.registerLanguage('objectivec', require('highlight.js/lib/languages/objectivec'));
Hljs.registerLanguage('php', require('highlight.js/lib/languages/php'));
Hljs.registerLanguage('php-template', require('highlight.js/lib/languages/php-template'));
Hljs.registerLanguage('plaintext', require('highlight.js/lib/languages/plaintext'));
Hljs.registerLanguage('python', require('highlight.js/lib/languages/python'));
Hljs.registerLanguage('python-repl', require('highlight.js/lib/languages/python-repl'));
Hljs.registerLanguage('r', require('highlight.js/lib/languages/r'));
Hljs.registerLanguage('rust', require('highlight.js/lib/languages/rust'));
Hljs.registerLanguage('scss', require('highlight.js/lib/languages/scss'));
Hljs.registerLanguage('shell', require('highlight.js/lib/languages/shell'));
Hljs.registerLanguage('sql', require('highlight.js/lib/languages/sql'));
Hljs.registerLanguage('swift', require('highlight.js/lib/languages/swift'));
Hljs.registerLanguage('yaml', require('highlight.js/lib/languages/yaml'));
Hljs.registerLanguage('typescript', require('highlight.js/lib/languages/typescript'));
Hljs.registerLanguage('vbnet', require('highlight.js/lib/languages/vbnet'));
Hljs.highlightAll();

const HighlightPack = {}
// 自定义插件
HighlightPack.install = function (Vue) {
// 自定义指令 v-highlight
  Vue.directive('highlight', {
    // 被绑定元素插入父节点时调用
    inserted: function (el) {
      const blocks = el.querySelectorAll('pre code')
      for (let i = 0; i < blocks.length; i++) {
        Hljs.highlightBlock(blocks[i])
      }
    },
    // 指令所在组件的 VNode 及其子 VNode 全部更新后调用
    componentUpdated: function (el) {
      setTimeout(() => {
        const blocks = el.querySelectorAll('pre code')
        for (let i = 0; i < blocks.length; i++) {
          Hljs.highlightBlock(blocks[i])
        }
      })
    }
  })
}

export default HighlightPack
