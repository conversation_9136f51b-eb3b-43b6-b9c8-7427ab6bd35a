import Vue from 'vue'

const dataUrl = `var dataUrl = '${process.env.VUE_APP_BACKEND_URL}';\n`

const getLastData = 'var getLastData = function (param1, param2, param3, param4) {\n' +
  '  var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_new_data?type=\' + param1 + \'&point=\' + param2 + \'&start=\' + param3 + \'&end=\' + param4);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    if (JSON.parse(request.response).data[0]) {\n' +
  '      finalData.push(JSON.parse(request.response).data[0])\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '    if (JSON.parse(request.response).data[1]) {\n' +
  '      finalData.push(JSON.parse(request.response).data[1])\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '  }\n' +
  '  return finalData.length === 0 ? JSON.stringify(finalData) : finalData;\n' +
  '}\n'

const getLastData1 = 'var getLastData1 = function (param1, param2, param3, param4, param5) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_new_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&start=\' + finalParam4 + \'&end=\' + finalParam5;\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_new_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&start=\' + finalParam4 + \'&end=\' + finalParam5;\n' +
  '    }\n' +
  '    var url = encodeURI(finalParamStr);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'

const getFirstData = 'var getFirstData = function (param1, param2, param3, param4) {\n' +
  '  var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_first_data?type=\' + param1 + \'&point=\' + param2 + \'&start=\' + param3 + \'&end=\' + param4);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    if (JSON.parse(request.response).data[0]) {\n' +
  '      finalData.push(JSON.parse(request.response).data[0])\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '    if (JSON.parse(request.response).data[1]) {\n' +
  '      finalData.push(JSON.parse(request.response).data[1])\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '  }\n' +
  '  return finalData.length === 0 ? JSON.stringify(finalData) : finalData;\n' +
  '}\n'

const getFirstData1 = 'var getFirstData1 = function (param1, param2, param3, param4, param5) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_first_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&start=\' + finalParam4 + \'&end=\' + finalParam5;\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_first_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&start=\' + finalParam4 + \'&end=\' + finalParam5;\n' +
  '    }\n' +
  '    var url = encodeURI(finalParamStr);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'

const getParticularData = 'var getParticularData = function (param1, param2, param3, param4, param5, param6, param7) {\n' +
  '  var url = dataUrl + \'/internal/rule/algorithm/function/get_data?type=\' + param1 + \'&point=\' + param2 + \'&start=\' + param3 + \'&end=\' + param4 + \'&logic=\' + encodeURIComponent(param5) + \'&timeType=\' + param6 + \'&valueType=\' + param7;\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    finalData = JSON.parse(request.response).data\n' +
  '  }\n' +
  '  return finalData.length === 0 ? JSON.stringify(finalData) : finalData;\n' +
  '}\n'

const getParticularData1 = 'var getParticularData1 = function (param1, param2, param3, param4, param5, param6, param7) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParam6 = param6 && param6.type ? param6.data : param6;\n' +
  '    var finalParam7 = param7 && param7.type ? param7.data : param7;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&valueType=\' + finalParam4 + \'&logic=\' + encodeURIComponent(finalParam5) + \'&start=\' + finalParam6 + \'&end=\' + finalParam7;\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_data?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&valueType=\' + finalParam4 + \'&logic=\' + encodeURIComponent(finalParam5) + \'&start=\' + finalParam6 + \'&end=\' + finalParam7;\n' +
  '    }\n' +
  '    var url = finalParamStr;\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'

const getElePrice = 'var getElePrice = function (param1, param2, param3) {\n' +
  '  var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_ele_price?type=\' + param1 + \'&user=\' + param2 + \'&t=\' + param3);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    if (JSON.parse(request.response).data.price) {\n' +
  '      finalData.push(JSON.parse(request.response).data.price)\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '    if (JSON.parse(request.response).data.type) {\n' +
  '      finalData.push(JSON.parse(request.response).data.type)\n' +
  '    } else {\n' +
  '      finalData.push(undefined)\n' +
  '    }\n' +
  '  }\n' +
  '  return finalData.length === 0 ? JSON.stringify(finalData) : finalData;\n' +
  '};\n'

const getCimAttrValue = 'var getCimAttrValue = function (param1) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1\n' +
  '    var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_cim_attr_value?attrPoint=\' + finalParam1);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = null;\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return finalData;\n' +
  '  }\n'

const getCimDeviceTag = 'var getCimDeviceTag = function (param1) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1\n' +
  '    var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_device_tags?path=\' + finalParam1);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = null;\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'

const getTagIsExist = 'var getTagIsExist = function (param1, param2) {\n' +
  '    var finalParam1 = [];\n' +
  '    if (param1 && param1.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param1.properties).length; i++) {\n' +
  '        if (param1.properties[Object.keys(param1.properties)[i]].data) {\n' +
  '          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param1) {\n' +
  '        if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '          finalParam1 = JSON.parse(param1);\n' +
  '        } else {\n' +
  '          finalParam1 = param1;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam1 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = param2;\n' +
  '    if (param2 && param2.data) {\n' +
  '      finalParam2 = param2.data;\n' +
  '    }\n' +
  '    var finalBool = false;\n' +
  '    if (finalParam1 && finalParam1.length > 0) {\n' +
  '      for (var i = 0; i < finalParam1.length; i++) {\n' +
  '        if (finalParam1[i][1] === finalParam2) {\n' +
  '          finalBool = true;\n' +
  '          break;\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '    return finalBool;\n' +
  '  }\n'

const getCimDeviceType = 'var getCimDeviceType = function (param1) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1\n' +
  '    var finalType = \'\';\n' +
  '    if (finalParam1.substring(0, 2) === \'2|\' && finalParam1.split(\'|\')[3] && finalParam1.split(\'|\')[3].indexOf(\'_\') !== -1) {\n' +
  '      finalType = finalParam1.split(\'|\')[3].split(\'_\')[0];\n' +
  '    }\n' +
  '    if (finalParam1.substring(0, 2) !== \'2|\' && finalParam1.split(\'|\')[2]) {\n' +
  '      finalType = finalParam1.split(\'|\')[2];\n' +
  '    }\n' +
  '    return finalType;\n' +
  '  }\n'


const getCimSystemGroupName =
  `var getCimSystemGroupName = function(param1) {
      var finalParam1 = param1 && param1.data ? param1.data : param1
      var res = finalParam1.split("|")[3];
      return JSON.stringify(res);
   }
  `


// param1: PVS|CA24VS01|ACLN|ACLN_ACLNACLNXBCK01 (获取设备组下的所有设备) 业务域|能源系统|设备组类型|设备名称
const getCimSystemGroup =
  `var getCimSystemGroup = function(param1) {
      var finalParam1 = param1 && param1.data ? param1.data : param1
      var stationId = finalParam1.split("|")[1];
      var groupName = finalParam1.split("|")[3];
      var url = encodeURI(dataUrl + "/internal/rule/algorithm/function/device/group/info/groupName/"+stationId + "/" + groupName);
      var request = new XMLHttpRequest();
      var resList = [];
      request.open('get', url, false);
      request.send(null);
      if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
        resList = JSON.parse(request.response).data;
      }
      return resList;
   }
   `

const ensureStrPrefix =
  `var ensureStrPrefix = function(param1, param2) {
      var finalParam1 = param1 && param1.data ? param1.data : param1
      var finalParam2 = param2 && param2.data ? param2.data : param2
      return finalParam1.startsWith(finalParam2);
   }
  `

const ensureStrSuffix =
  `var ensureStrSuffix = function(param1, param2) {
      var finalParam1 = param1 && param1.data ? param1.data : param1
      var finalParam2 = param2 && param2.data ? param2.data : param2
      return finalParam1.endsWith(finalParam2);
   }
  `


const concatenateCim =
  `var concatenateCim = function(param1, param2) {
      var finalParam1 = param1 && param1.data ? param1.data : param1
      var finalParam2 = param2 && param2.data ? param2.data : param2
      return finalParam1+"|"+finalParam2;
   }
  `


const getDataByLogicAndTime1 = 'var getDataByLogicAndTime1 = function (param1, param2, param3, param4, param5, param6) {\n' +
  '  var url = dataUrl + \'/internal/rule/algorithm/function/get_total_time_interval?point=\' + param1 + \'&type=\' + param2 + \'&start=\' + param3 + \'&direction=\' + param4 + \'&totalTime=\' + param5 + \'&logic=\' + encodeURIComponent(param6);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    finalData = JSON.parse(request.response).data\n' +
  '  }\n' +
  '  return JSON.stringify(finalData);\n' +
  '}\n'
const getDataByLogicAndTime11 = 'var getDataByLogicAndTime11 = function (param1, param2, param3, param4, param5, param6, param7) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParam6 = param6 && param6.type ? param6.data : param6;\n' +
  '    var finalParam7 = param7 && param7.type ? param7.data : param7;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_total_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&direction=\' + finalParam6 + \'&totalTime=\' + finalParam7;\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_total_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&direction=\' + finalParam6 + \'&totalTime=\' + finalParam7;\n' +
  '    }\n' +
  '    var url = finalParamStr;\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'
const getDataByLogicAndTime2 = 'var getDataByLogicAndTime2 = function (param1, param2, param3, param4, param5) {\n' +
  '  var url = dataUrl + \'/internal/rule/algorithm/function/get_range_time_interval?point=\' + param1 + \'&type=\' + param2 + \'&start=\' + param3 + \'&end=\' + param4 + \'&logic=\' + encodeURIComponent(param5);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    finalData = JSON.parse(request.response).data\n' +
  '  }\n' +
  '  return JSON.stringify(finalData);\n' +
  '}\n'
const getDataByLogicAndTime21 = 'var getDataByLogicAndTime21 = function (param1, param2, param3, param4, param5, param6) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParam6 = param6 && param6.type ? param6.data : param6;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_range_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&end=\' + finalParam6;\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_range_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&end=\' + finalParam6;\n' +
  '    }\n' +
  '    var url = finalParamStr;\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'
const getDataByLogicAndTime3 = 'var getDataByLogicAndTime3 = function (param1, param2, param3, param4, param5, param6) {\n' +
  '  var url = dataUrl + \'/internal/rule/algorithm/function/get_continued_time_interval?point=\' + param1 + \'&type=\' + param2 + \'&start=\' + param3 + \'&end=\' + param4 + \'&timeLogic=\' + encodeURIComponent(param5) + \'&logic=\' + encodeURIComponent(param6);\n' +
  '  var request = new XMLHttpRequest();\n' +
  '  var finalData = [];\n' +
  '  request.open(\'get\', url, false);\n' +
  '  request.send(null);\n' +
  '  if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '    finalData = JSON.parse(request.response).data\n' +
  '  }\n' +
  '  return JSON.stringify(finalData);\n' +
  '}\n'
const getDataByLogicAndTime31 = 'var getDataByLogicAndTime31 = function (param1, param2, param3, param4, param5, param6, param7) {\n' +
  '    var finalParam1 = param1 && param1.type ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.type ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.type ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.type ? param4.data : param4;\n' +
  '    var finalParam5 = param5 && param5.type ? param5.data : param5;\n' +
  '    var finalParam6 = param6 && param6.type ? param6.data : param6;\n' +
  '    var finalParam7 = param7 && param7.type ? param7.data : param7;\n' +
  '    var finalParamStr = \'\';\n' +
  '    if (finalParam3) {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_continued_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&timeType=\' + finalParam3 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&end=\' + finalParam6 + \'&timeLogic=\' + encodeURIComponent(finalParam7);\n' +
  '    } else {\n' +
  '      finalParamStr = dataUrl + \'/internal/rule/algorithm/function/get_continued_time_interval?type=\' + finalParam1 + \'&point=\' + finalParam2 + \'&logic=\' + encodeURIComponent(finalParam4) + \'&start=\' + finalParam5 + \'&end=\' + finalParam6 + \'&timeLogic=\' + encodeURIComponent(finalParam7);\n' +
  '    }\n' +
  '    var url = finalParamStr;\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = [];\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'
const getDataByFixedTime = 'var getDataByFixedTime = function (param1, param2, param3, param4) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && (param2.data || typeof param2.data === \'number\') ? param2.data : param2;\n' +
  '    var finalParam4 = param4 && param4.data ? param4.data : param4;\n' +
  '    var finalParam3 = [];\n' +
  '    if (param3.properties) {\n' +
  '      for (var key in param3.properties) {\n' +
  '        finalParam3.push([param3.properties[key].properties[0].data, param3.properties[key].properties[1].data])\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (typeof param3 === \'string\' && typeof JSON.parse(param3) === \'object\') {\n' +
  '        finalParam3 = JSON.parse(param3);\n' +
  '      } else {\n' +
  '        finalParam3 = param3;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalData = [];\n' +
  '    var url = encodeURI(dataUrl + \'/internal/rule/algorithm/function/get_time_range_data?point=\' + finalParam1 + \'&type=\' + finalParam2 + \'&timeIntervalDtoList=\' + JSON.stringify(finalParam3) + \'&aggLevel=\' + finalParam4);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.status !== 200) {\n' +
  '      return null;\n' +
  '    }\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return JSON.stringify(finalData);\n' +
  '  }\n'
const getEnthalpy = 'var getEnthalpy = function (param1, param2) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
  '    var url = encodeURI(dataUrl + \'/internal/ai/algorithm/function/enthalpy_entropy_calculator?temperature=\' + finalParam1 + \'&pressure=\' + finalParam2);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = null;\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return finalData;\n' +
  '  }\n'
const getRefrigerantT = 'var getRefrigerantT = function (param1, param2) {\n' +
  '    if (!param1 || !param2) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
  '    var url = encodeURI(dataUrl + \'/internal/ai/algorithm/function/refrigeration?type=\' + finalParam1 + \'&p=\' + finalParam2);\n' +
  '    var request = new XMLHttpRequest();\n' +
  '    var finalData = null;\n' +
  '    request.open(\'get\', url, false);\n' +
  '    request.send(null);\n' +
  '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
  '      finalData = JSON.parse(request.response).data;\n' +
  '    }\n' +
  '    return finalData;\n' +
  '  }\n'
// const calculateBall = 'var calculateBall = function (param1, param2, param3, param4) {\n' +
//   '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
//   '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
//   '    var finalParam3 = param3 && param3.data ? param3.data : param3;\n' +
//   '    var finalParam4 = param4 && param4.data ? param4.data : param4;\n' +
//   '    var url = encodeURI(dataUrl + \'/internal/ai/algorithm/function/wet_and_dry_bulb?type=\' + finalParam1 + \'&p1=\' + finalParam2 + \'&p2=\' + finalParam3 + \'&p3=\' + finalParam4);\n' +
//   '    var request = new XMLHttpRequest();\n' +
//   '    var finalData = null;\n' +
//   '    request.open(\'get\', url, false);\n' +
//   '    request.send(null);\n' +
//   '    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {\n' +
//   '      finalData = JSON.parse(request.response).data;\n' +
//   '    }\n' +
//   '    return finalData;\n' +
//   '  }\n';

const calculateBall = `
  var calculateBall = function (param1, param2, param3, param4) {
     var finalParam1 = param1 && param1.data ? param1.data : param1;
     var finalParam2 = param2 && param2.data ? param2.data : param2;
     var finalParam3 = param3 && param3.data ? param3.data : param3;
     var finalParam4 = param4 && param4.data ? param4.data : param4;
     var finalData = null;

     if (finalParam1 === 0) {
      finalData = psychrolib.GetTWetBulbFromTDewPoint(finalParam2, finalParam3, finalParam4);
     } else if (finalParam1 === 1) {
      finalData = psychrolib.GetTWetBulbFromRelHum(finalParam2, finalParam3, finalParam4);
     } else if (finalParam1 === 2) {
      finalData = psychrolib.GetRelHumFromTDewPoint(finalParam2, finalParam3);
     } else if (finalParam1 === 3) {
      finalData = psychrolib.GetRelHumFromTWetBulb(finalParam2, finalParam3, finalParam4);
     } else if (finalParam1 === 4) {
      finalData = psychrolib.GetTDewPointFromRelHum(finalParam2, finalParam3);
     } else if (finalParam1 === 5) {
      finalData = psychrolib.GetTDewPointFromTWetBulb(finalParam2, finalParam3, finalParam4);
     } else {
      console.log("param error!")
     }
     return finalData;
  }
`

export {
  dataUrl,
  getLastData,
  getLastData1,
  getFirstData,
  getFirstData1,
  getParticularData,
  getParticularData1,
  getElePrice,
  getCimAttrValue,
  getCimDeviceTag,
  getTagIsExist,
  getCimDeviceType,
  getCimSystemGroup,
  getCimSystemGroupName,
  ensureStrPrefix,
  ensureStrSuffix,
  concatenateCim,
  getDataByLogicAndTime1,
  getDataByLogicAndTime11,
  getDataByLogicAndTime2,
  getDataByLogicAndTime21,
  getDataByLogicAndTime3,
  getDataByLogicAndTime31,
  getDataByFixedTime,
  getEnthalpy,
  getRefrigerantT,
  calculateBall
}
