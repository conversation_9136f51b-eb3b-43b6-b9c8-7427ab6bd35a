import '@/utils/publicPath'
import Vue from 'vue'
import ElementUI from 'element-ui'
import hljsVuePlugin from '@highlightjs/vue-plugin';
import VueMarkdown from 'vue-markdown'
// import '@/style/theme-reset.scss'
import '@/style/element-variables.scss'
import '@/style/normalize.css'
import '@/style/element.sass'

import highlight from './highlightPack'

import axios from '@/api/http'
import '@/directives'
import '@/components/global'

import http from '@/assets/js/http.js'
import fileUtil from '@/assets/js/fileUtil.js'
import ossUtil from '@/assets/js/ossUtil.js'
import VueClipboard from 'vue-clipboard2'
import Driver from 'driver.js'
import 'driver.js/dist/driver.min.css'
import 'view-design/dist/styles/iview.css'

import * as echarts from 'echarts'
import moment from 'moment'
import { addCollection } from '@iconify/vue2'; // 引入注册方法
import { icons } from '@iconify-json/vscode-icons'; // 引入vscode-icons的本地图标数据

// 注册vscode-icons图标集到Iconify系统
addCollection(icons); 

ElementUI.Dialog.props.closeOnClickModal.default = false

Vue.use(hljsVuePlugin);
Vue.use(highlight)
Vue.use(ElementUI)
Vue.use(http)
Vue.use(VueClipboard)
Vue.component('VueMarkdown', VueMarkdown)

Vue.prototype.$moment = moment
Vue.prototype.$echarts = echarts

Vue.prototype.$fileUtil = fileUtil
Vue.prototype.$axios = axios

Vue.prototype.baseUrl = process.env.VUE_APP_API
Vue.prototype.$ossUtil = ossUtil

Vue.prototype.$driver = new Driver({
  animate: false,
  opacity: 0.7,
  allowClose: false,
  doneBtnText: '完成',
  closeBtnText: '关闭',
  nextBtnText: '下一步',
  prevBtnText: '上一步'
})

/**
 * 接入前端监控平台
 */
// eslint-disable-next-line no-undef,no-unused-vars
// let monitorEnv = MonitorJS.EnnDEV
// if (process.env.VUE_APP_AUTH_ENV === 'NEW_PRO_ONLINE') {
//   // eslint-disable-next-line no-undef
//   monitorEnv = MonitorJS.EnnPROD
// } else if (process.env.VUE_APP_AUTH_ENV === 'NEW_FAT') {
//   // eslint-disable-next-line no-undef
//   monitorEnv = MonitorJS.EnnFAT
// } else if (process.env.VUE_APP_AUTH_ENV === 'NEW_UAT') {
//   // eslint-disable-next-line no-undef
//   monitorEnv = MonitorJS.EnnUAT
// }

// if (process.env.NODE_ENV === 'production') {
//   // eslint-disable-next-line no-undef
//   new MonitorJS().init({
//     pageId: process.env.VUE_APP_APM_ID, // 应用唯一标识
//     tenantId: '1369923265280311297',
//     // eslint-disable-next-line no-undef
//     env: monitorEnv, // 运行环境，MonitorJS.DEV测试环境、MonitorJS.PRE预发环境，MonitorJS.PROD生产环境
//     consoleError: true,
//     vueError: true, // 是否上报Vue错误
//     vue: Vue
//   })
// }
