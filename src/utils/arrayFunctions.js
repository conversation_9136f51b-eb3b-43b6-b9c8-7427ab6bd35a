// 基础依赖函数
// 递归排序函数
const quickSort = 'var quickSort = function (arr, column, type) {\n' +
  '      var finalArr = arr\n' +
  '      var finalType = type\n' +
  '      if (arr && arr.data) {\n' +
  '        finalArr = [].concat(arr.data)\n' +
  '      }\n' +
  '      if (type && type.data) {\n' +
  '        finalType = type.data\n' +
  '      }\n' +
  '      if (finalArr.length <= 1) {\n' +
  '        return finalArr;\n' +
  '      }\n' +
  '      var middle, temp, calMiddle;\n' +
  '      var middleIndex = Math.floor(finalArr.length / 2);\n' +
  '      middle = finalArr.splice(middleIndex, 1);\n' +
  '      calMiddle = middle;\n' +
  '      if (column >= 2 && Array.isArray(middle)) {\n' +
  '        calMiddle = middle[0][column - 1];\n' +
  '      }\n' +
  '      var left = [];\n' +
  '      var right = [];\n' +
  '      for(var i = 0; i < finalArr.length; i++){\n' +
  '        temp = finalArr[i];\n' +
  '        if (column >= 2 && Array.isArray(temp)) {\n' +
  '          temp = temp[column - 1];\n' +
  '        }\n' +
  '        if (finalType === 0) {\n' +
  '          if (temp < calMiddle) {\n' +
  '            left.push(finalArr[i]);\n' +
  '          } else {\n' +
  '            right.push(finalArr[i]);\n' +
  '          }\n' +
  '        } else {\n' +
  '          if (temp < calMiddle) {\n' +
  '            right.push(finalArr[i]);\n' +
  '          } else {\n' +
  '            left.push(finalArr[i]);\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '      return quickSort(left, column, type).concat(middle, quickSort(right, column, type));\n' +
  '    }\n'

// 数组取值
const getDataFromArray = 'var getDataFromArray = function (param1, param2, param3) {\n' +
  '    var finalParam1 = param1\n' +
  '    var finalParam2 = param2\n' +
  '    var finalParam3 = param3\n' +
  '    if (param1 && param1.data) {\n' +
  '      finalParam1 = [].concat(param1.data)\n' +
  '    } else {\n' +
  '      if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '        finalParam1 = JSON.parse(param1)\n' +
  '      } else {\n' +
  '        finalParam1 = param1\n' +
  '      }\n' +
  '    }\n' +
  '    if (param2 && param2.data) {\n' +
  '      finalParam2 = param2.data\n' +
  '    }\n' +
  '    if (param3 && param3.data) {\n' +
  '      finalParam3 = param3.data\n' +
  '    }\n' +
  '    if (!finalParam1 || finalParam1 === \'null\' || finalParam1 === \'[]\') {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var arr = finalParam1;\n' +
  '    if (!Array.isArray(arr)) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var temArr = arr[finalParam2 - 1];\n' +
  '    var finalData = null;\n' +
  '    if (Array.isArray(temArr)) {\n' +
  '      finalData = temArr[finalParam3 - 1];\n' +
  '    } else {\n' +
  '      finalData = temArr;\n' +
  '    }\n' +
  '    return finalData;\n' +
  '  }\n'

// 获取数组指定列的最大值、最小值、均值、总和、长度
const anaArr = 'var anaArr = function (data, column, type) {\n' +
  '    if (!data) {\n' +
  '      return null;\n' +
  '    }\n' +
  '    var finaleData = []\n' +
  '    var finaleColumn = column\n' +
  '    var finaleType = type\n' +
  '    if (data && data.data) {\n' +
  '      finaleData = [].concat(data.data)\n' +
  '    } else if (data.properties) {\n' +
  '      for (let key in data.properties) {\n' +
  '        finaleData.push(data.properties[key])\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (typeof data === \'string\' && typeof JSON.parse(data) === \'object\') {\n' +
  '        finaleData = JSON.parse(data)\n' +
  '      } else {\n' +
  '        finaleData = data\n' +
  '      }\n' +
  '    }\n' +
  '    if (column && column.data) {\n' +
  '      finaleColumn = column.data\n' +
  '    }\n' +
  '    if (type && type.data) {\n' +
  '      finaleType = type.data\n' +
  '    }\n' +
  '    if (!finaleData || finaleData === \'null\' || finaleData === \'[]\') {\n' +
  '      if (finaleType === \'length\') {\n' +
  '        return null;\n' +
  '      } else {\n' +
  '        return undefined;\n' +
  '      }\n' +
  '    }\n' +
  '    var arr = finaleData;\n' +
  '    if (!Array.isArray(arr) || arr.length === 0) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var isOne = false, isTwo = false\n' +
  '    arr.map(function (n) {\n' +
  '      if (n && !Array.isArray(n)) {\n' +
  '        isOne = true;\n' +
  '      }\n' +
  '      if (n && Array.isArray(n)) {\n' +
  '        isTwo = true;\n' +
  '      }\n' +
  '    })\n' +
  '    if ((isOne && isTwo) || (!isOne && !isTwo)) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    if (isOne && finaleColumn !== 1 && finaleColumn !== 0) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    if (isTwo && finaleColumn > arr[0].length) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var finalInt = null, temInt;\n' +
  '    if (finaleType === \'min\') {\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (finalInt) {\n' +
  '          if (typeof temInt === \'number\' && !isNaN(temInt) && finalInt > temInt) {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        } else {\n' +
  '          if (typeof temInt === \'number\' && !isNaN(temInt)) {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        }\n' +
  '      });\n' +
  '    } else if (finaleType === \'max\') {\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (finalInt) {\n' +
  '          if (typeof temInt === \'number\' && !isNaN(temInt) && finalInt < temInt) {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        } else {\n' +
  '          if (typeof temInt === \'number\' && !isNaN(temInt)) {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        }\n' +
  '      });\n' +
  '    } else if (finaleType === \'ave\') {\n' +
  '      var totalNum = 0\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (typeof temInt === \'number\' && !isNaN(temInt)) {\n' +
  '          totalNum += 1;\n' +
  '          if (finalInt) {\n' +
  '            finalInt += temInt;\n' +
  '          } else {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        }\n' +
  '      });\n' +
  '      if (finalInt && totalNum > 0) {\n' +
  '        finalInt = finalInt / totalNum\n' +
  '      }\n' +
  '    } else if (finaleType === \'abs_ave\') {\n' +
  '      var totalNum1 = 0\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (typeof temInt === \'number\' && !isNaN(temInt)) {\n' +
  '          totalNum1 += 1;\n' +
  '          if (finalInt) {\n' +
  '            finalInt += Math.abs(temInt);\n' +
  '          } else {\n' +
  '            finalInt = Math.abs(temInt);\n' +
  '          }\n' +
  '        }\n' +
  '      });\n' +
  '      if (finalInt && totalNum1 > 0) {\n' +
  '        finalInt = finalInt / totalNum1\n' +
  '      }\n' +
  '    } else if (finaleType === \'total\') {\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (typeof temInt === \'number\' && !isNaN(temInt)) {\n' +
  '          if (finalInt) {\n' +
  '            finalInt += temInt;\n' +
  '          } else {\n' +
  '            finalInt = temInt;\n' +
  '          }\n' +
  '        }\n' +
  '      });\n' +
  '    } else if (finaleType === \'length\') {\n' +
  '      var totalLength = 0;\n' +
  '      arr.map(function (n) {\n' +
  '        if (isOne) {\n' +
  '          temInt = n;\n' +
  '        } else {\n' +
  '          temInt = n[finaleColumn - 1];\n' +
  '        }\n' +
  '        if (temInt !== null && temInt !== \'null\' && temInt !== undefined && temInt !== \'undefined\') {\n' +
  '          totalLength++;\n' +
  '        }\n' +
  '      });\n' +
  '      finalInt = totalLength;\n' +
  '    } else {\n' +
  '      finalInt = null;\n' +
  '    }\n' +
  '    return finalInt;\n' +
  '  }\n'

// 将数组指定列进行重新排序
const sortArr = 'var sortArr = function (data, column, type) {\n' +
  '    var finaleData = data\n' +
  '    var finaleColumn = column\n' +
  '    if (data && data.data) {\n' +
  '      finaleData = [].concat(data.data)\n' +
  '    } else {\n' +
  '      if (typeof data === \'string\' && typeof JSON.parse(data) === \'object\') {\n' +
  '        finaleData = JSON.parse(data)\n' +
  '      } else {\n' +
  '        finaleData = data\n' +
  '      }\n' +
  '    }\n' +
  '    if (column && column.data) {\n' +
  '      finaleColumn = column.data\n' +
  '    }\n' +
  '    if (!data || data === \'null\' || data === \'[]\') {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var arr = finaleData;\n' +
  '    if (!Array.isArray(arr) || arr.length === 0) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var isOne = false, isTwo = false\n' +
  '    arr.map(function (n) {\n' +
  '      if (n && !Array.isArray(n)) {\n' +
  '        isOne = true;\n' +
  '      }\n' +
  '      if (n && Array.isArray(n)) {\n' +
  '        isTwo = true;\n' +
  '      }\n' +
  '    })\n' +
  '    if ((isOne && isTwo) || (!isOne && !isTwo)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isOne && finaleColumn !== 1) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isTwo && (finaleColumn > arr[0].length || finaleColumn < 1)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    return JSON.stringify(quickSort(arr, finaleColumn, type));\n' +
  '  }\n'

// 将数组按指定列满足条件后组成新数组
const subArr = 'var subArr = function (data, column, logic) {\n' +
  '    var finalData = data\n' +
  '    var finalColumn = column\n' +
  '    var finalLogic = logic\n' +
  '    if (data && data.data) {\n' +
  '      finalData = [].concat(data.data)\n' +
  '    } else {\n' +
  '      if (typeof data === \'string\' && typeof JSON.parse(data) === \'object\') {\n' +
  '        finalData = JSON.parse(data)\n' +
  '      } else {\n' +
  '        finalData = data\n' +
  '      }\n' +
  '    }\n' +
  '    if (column && column.data) {\n' +
  '      finalColumn = column.data\n' +
  '    }\n' +
  '    if (logic && logic.data) {\n' +
  '      finalLogic = logic.data\n' +
  '    }\n' +
  '    if (!finalData || finalData === \'null\' || finalData === \'[]\') {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var arr = finalData;\n' +
  '    if (!Array.isArray(arr) || arr.length === 0) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var isOne = false, isTwo = false\n' +
  '    arr.map(function (n) {\n' +
  '      if (n && !Array.isArray(n)) {\n' +
  '        isOne = true;\n' +
  '      }\n' +
  '      if (n && Array.isArray(n)) {\n' +
  '        isTwo = true;\n' +
  '      }\n' +
  '    })\n' +
  '    if ((isOne && isTwo) || (!isOne && !isTwo)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isOne && finalColumn !== 1) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isTwo && (finalColumn > arr[0].length || finalColumn < 1)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var finalArr = []\n' +
  '    if (isOne) {\n' +
  '      arr.map(function (n) {\n' +
  '        if (eval(finalLogic)) {\n' +
  '          finalArr.push(n)\n' +
  '        }\n' +
  '      })\n' +
  '    } else {\n' +
  '      var x\n' +
  '      arr.map(function (n) {\n' +
  '        x = n[finalColumn - 1]\n' +
  '        if (eval(finalLogic)) {\n' +
  '          finalArr.push(n)\n' +
  '        }\n' +
  '      })\n' +
  '    }\n' +
  '    return finalArr.length > 0 ? finalArr : JSON.stringify([]);\n' +
  '  }\n'

// 数组按指定列满足条件后的连续区间
const regionArr = 'var regionArr = function (data, column, logic) {\n' +
  '    var finalData = data\n' +
  '    var finalColumn = column\n' +
  '    var finalLogic = logic\n' +
  '    if (data && data.data) {\n' +
  '      finalData = [].concat(data.data)\n' +
  '    } else {\n' +
  '      if (typeof data === \'string\' && typeof JSON.parse(data) === \'object\') {\n' +
  '        finalData = JSON.parse(data)\n' +
  '      } else {\n' +
  '        finalData = data\n' +
  '      }\n' +
  '    }\n' +
  '    if (column && column.data) {\n' +
  '      finalColumn = column.data\n' +
  '    }\n' +
  '    if (logic && logic.data) {\n' +
  '      finalLogic = logic.data\n' +
  '    }\n' +
  '    if (!finalData || finalData === \'null\' || finalData === \'[]\') {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var arr = finalData;\n' +
  '    if (!Array.isArray(arr) || arr.length === 0) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var isOne = false, isTwo = false\n' +
  '    arr.map(function (n) {\n' +
  '      if (n && !Array.isArray(n)) {\n' +
  '        isOne = true;\n' +
  '      }\n' +
  '      if (n && Array.isArray(n)) {\n' +
  '        isTwo = true;\n' +
  '      }\n' +
  '    })\n' +
  '    if ((isOne && isTwo) || (!isOne && !isTwo)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isOne && finalColumn !== 1) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (isTwo && (finalColumn > arr[0].length || finalColumn < 1)) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var finalArr = []\n' +
  '    var temArr = []\n' +
  '    var startIndex = null\n' +
  '    var endIndex = null\n' +
  '    var x\n' +
  '    arr.map(function (n, i) {\n' +
  '      x = n\n' +
  '      if (isTwo) {\n' +
  '        x = n[finalColumn - 1]\n' +
  '      }\n' +
  '      if (eval(finalLogic)) {\n' +
  '        temArr.push(i + 1)\n' +
  '      }\n' +
  '    })\n' +
  '    if (temArr.length > 0) {\n' +
  '      temArr.map(function (n, i1) {\n' +
  '        startIndex = n\n' +
  '        for (var i2 = i1 + 1; i2 < temArr.length; i2++) {\n' +
  '          if (n + (i2 - i1) === temArr[i2]) {\n' +
  '            endIndex = temArr[i2]\n' +
  '          } else {\n' +
  '            break\n' +
  '          }\n' +
  '        }\n' +
  '        if (endIndex && endIndex > startIndex) {\n' +
  '          finalArr.push([startIndex, endIndex, endIndex - startIndex + 1])\n' +
  '        }\n' +
  '      })\n' +
  '    }\n' +
  '    return finalArr.length > 0 ? finalArr : JSON.stringify([]);\n' +
  '  }\n'

// 时间对齐处理
const addDataByTime = 'var addDataByTime = function (param1, param2, param3, param4) {\n' +
  '    var finalParam1 = param1;\n' +
  '    var finalParam2 = param2;\n' +
  '    var finalParam3 = param3;\n' +
  '    var finalParam4 = param4;\n' +
  '    if (param1 && param1.data) {\n' +
  '      finalParam1 = [].concat(param1.data)\n' +
  '    } else {\n' +
  '      if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '        finalParam1 = JSON.parse(param1);\n' +
  '      } else {\n' +
  '        finalParam1 = param1;\n' +
  '      }\n' +
  '    }\n' +
  '    if (param2 && param2.data) {\n' +
  '      finalParam2 = param2.data;\n' +
  '    }\n' +
  '    if (param3 && param3.data) {\n' +
  '      finalParam3 = param3.data;\n' +
  '    }\n' +
  '    if (param4 && param4.data) {\n' +
  '      finalParam4 = param4.data;\n' +
  '    }\n' +
  '    if (!finalParam1 || finalParam1 === \'null\' || finalParam1 === \'[]\') {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    if (!Array.isArray(finalParam1) || finalParam1.length === 0) {\n' +
  '      return JSON.stringify([]);\n' +
  '    }\n' +
  '    var finalArr = [];\n' +
  '    var temArr = [];\n' +
  '    var gap = null;\n' +
  '    if (finalParam4 === \'day\') {\n' +
  '      gap = 86400000;\n' +
  '    } else if (finalParam4 === \'hour\') {\n' +
  '      gap = 3600000;\n' +
  '    } else if (finalParam4 === \'min\') {\n' +
  '      gap = 60000;\n' +
  '    }\n' +
  '    for (var temInt = finalParam2; temInt <= finalParam3; temInt += gap) {\n' +
  '      temArr[0] = temInt;\n' +
  '      temArr[1] = null;\n' +
  '      finalArr.push(temArr);\n' +
  '      temArr = [];\n' +
  '    }\n' +
  '    for (let i = 0; i < finalParam1.length; i++) {\n' +
  '      for (let m = 0; m < finalArr.length; m++) {\n' +
  '        if (finalParam1[i][0] < finalArr[0][0]) {\n' +
  '          break;\n' +
  '        }\n' +
  '        if (finalParam1[i][0] >= finalArr[finalArr.length - 1][0]) {\n' +
  '          if (!finalArr[finalArr.length - 1][1]) {\n' +
  '            finalArr[finalArr.length - 1][1] = finalParam1[i][1];\n' +
  '          }\n' +
  '          break;\n' +
  '        }\n' +
  '        if (finalParam1[i][0] >= finalArr[m][0] && finalParam1[i][0] <= finalArr[m + 1][0]) {\n' +
  '          if (!finalArr[m][1]) {\n' +
  '            finalArr[m][1] = finalParam1[i][1];\n' +
  '          }\n' +
  '          break;\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '    return JSON.stringify(finalArr);\n' +
  '  }\n'

export {
  quickSort,
  getDataFromArray,
  anaArr,
  sortArr,
  subArr,
  regionArr,
  addDataByTime
}
