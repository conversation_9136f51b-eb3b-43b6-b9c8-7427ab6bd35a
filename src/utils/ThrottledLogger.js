import throttle from 'lodash/throttle';

// 创建一个全局的节流日志函数
const throttledLog = throttle((...args) => {
  log('throttledLog:', ...args);
}, 2000); // 节流时间为 2 秒

/**
 * 通用节流日志函数
 * @param {string} label 日志前缀
 * @param  {...any} args 其他日志参数
 */
export function handleThrottleLog(...args) {
  throttledLog(...args);
}


// import { handleThrottleLog } from '@/utils/ThrottledLogger.js';
// yzw utils here

/**
 * 尝试将字符串转换为 JSON 对象
 * @param {string} str - 需要转换的字符串
 * @returns {Object|string} - 成功返回 JSON 对象，失败返回原字符串
 */
export function safeStringToJSON(str) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return str; // 转换失败，返回原字符串
  }
}

/**
 * 尝试将 JSON 对象转换为字符串
 * @param {Object} json - 需要转换的 JSON 对象
 * @returns {string|Object} - 成功返回字符串，失败返回原内容
 */
export function safeJSONToString(json) {
  try {
    return JSON.stringify(json);
  } catch (e) {
    return json; // 转换失败，返回原内容
  }
}


/**
 * 判断一个字符串是否为有效的 JSON 格式
 * @param {string} str - 需要判断的字符串
 * @returns {boolean} - 如果是有效 JSON 返回 true，否则返回 false
 */
export function isValidJSON(str) {
  if (typeof str !== 'string') {
    return false; // 非字符串直接返回 false
  }
  try {
    JSON.parse(str);
    return true; // 解析成功，说明是 JSON 格式
  } catch (e) {
    return false; // 解析失败，说明不是 JSON 格式
  }
}



export function log(...Myargs) {
  const cuteIcons = ['🐱', '💖', '✨', '🌸'];
  const randomIcon = cuteIcons[Math.floor(Math.random() * cuteIcons.length)];
  const prefix = `${randomIcon}`;
  console.log(prefix, ...Myargs);
}
// import { log } from '@/utils/ThrottledLogger.js';

