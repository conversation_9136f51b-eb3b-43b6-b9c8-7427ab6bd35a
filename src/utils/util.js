import { v4 as uuidv4 } from 'uuid'

// echarts字体自适应
export const echartsFont = size => {
  let htmlFont
  if (screen.width > 750) {
    htmlFont = 75
  } else {
    htmlFont = screen.width / 10
  }
  return size * htmlFont
}
// 展示native的头部
export function showTitleBottom(self) {
  const el = self
  const plateform = sessionStorage.getItem('plateform')
  if (plateform === 'android') {
    window.control.callHandler('bottom_title', 'show')
    el.$emit('changeTabStatus', false)
  } else if (plateform === 'ios') {
    window.webkit.messageHandlers.js_closeHomeFullScreen.postMessage(null)
    el.$emit('changeTabStatus', false)
  }
}
// 隐藏native的头部
export function hideTitleBottom(self) {
  const el = self
  const plateform = sessionStorage.getItem('plateform')
  if (plateform === 'android') {
    window.control.callHandler('bottom_title', 'invisible')
    el.$emit('changeTabStatus', true)
  } else if (plateform === 'ios') {
    window.webkit.messageHandlers.js_openHomeFullScreen.postMessage(null)
    el.$emit('changeTabStatus', true)
  }
}
// 深拷贝
function type(obj) {
  const toString = Object.prototype.toString
  const map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  return map[toString.call(obj)]
}
// 深拷贝
export function cloneDeep(data) {
  const t = type(data)
  let o
  let i
  let ni
  if (t === 'array') {
    o = []
  } else if (t === 'object') {
    o = {}
  } else {
    return data
  }
  if (t === 'array') {
    for (i = 0, ni = data.length; i < ni; i++) {
      o.push(cloneDeep(data[i]))
    }
    return o
  } else if (t === 'object') {
    for (i in data) {
      o[i] = cloneDeep(data[i])
    }
    return o
  }
}
// 跳转native登陆
export function goNativeLogin() {
  const plateform = sessionStorage.getItem('plateform')
  if (plateform === 'android') {
    console.log('android token_invalid ')
    window.control.callHandler('token_invalid', JSON.stringify({}))
  } else if (plateform === 'ios') {
    console.log('ios token_invalid ')
    window.webkit.messageHandlers.token_invalid.postMessage(JSON.stringify({}))
  }
}

// 控制是否有下拉刷新功能
/**
 *
 * @param vm  组件中的this,vueComponent
 * @param bool  是否禁止下拉刷新 true 禁止 false 不禁止
 */
export function ctrolRefresh(vm, bool) {
  vm.$emit('ctrolRefresh', bool)
}
// 时间格式化
/**
 * Parse the time to string
 * @param {(object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}
// 获取url的参数
/**
 * get url querystring parameter
 * @param {string} name
 * @returns {string | null}
 */
export function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  const str = window.location.hash
  const index = str.indexOf('?')
  const result = str.substring(index + 1, str.length)
  const r = result.match(reg)
  if (r != null) {
    return decodeURI(r[2])
  }
  return null
}
// 存储
export const session = {
  set(key, value) {
    sessionStorage.setItem(key, JSON.stringify(value))
  },
  get(key) {
    return JSON.parse(sessionStorage.getItem(key))
  },
  remove(key) {
    sessionStorage.removeItem(key)
  }
}

// 雪花算法
export function Snowflake() {
  const bigInt = require('big-integer')
  const Snowflake = /** @class */ (function () {
    function Snowflake(_workerId, _dataCenterId, _sequence) {
      this.twepoch = 0
      this.workerIdBits = 5
      this.dataCenterIdBits = 5
      this.maxWrokerId = -1 ^ (-1 << this.workerIdBits) // 值为：31
      this.maxDataCenterId = -1 ^ (-1 << this.dataCenterIdBits) // 值为：31
      this.sequenceBits = 12
      this.workerIdShift = this.sequenceBits // 值为：12
      this.dataCenterIdShift = this.sequenceBits + this.workerIdBits // 值为：17
      this.timestampLeftShift = this.sequenceBits + this.workerIdBits + this.dataCenterIdBits // 值为：22
      this.sequenceMask = -1 ^ (-1 << this.sequenceBits) // 值为：4095
      this.lastTimestamp = -1
      // 设置默认值,从环境变量取
      this.workerId = 1
      this.dataCenterId = 1
      this.sequence = 0
      if (this.workerId > this.maxWrokerId || this.workerId < 0) {
        throw new Error('config.worker_id must max than 0 and small than maxWrokerId-[' + this.maxWrokerId + ']')
      }
      if (this.dataCenterId > this.maxDataCenterId || this.dataCenterId < 0) {
        throw new Error('config.data_center_id must max than 0 and small than maxDataCenterId-[' + this.maxDataCenterId + ']')
      }
      this.workerId = _workerId
      this.dataCenterId = _dataCenterId
      this.sequence = _sequence
    }

    Snowflake.prototype.tilNextMillis = function (lastTimestamp) {
      let timestamp = this.timeGen()
      while (timestamp <= lastTimestamp) {
        timestamp = this.timeGen()
      }
      return timestamp
    }
    Snowflake.prototype.timeGen = function () {
      return Date.now()
    }
    Snowflake.prototype.nextId = function () {
      let timestamp = this.timeGen()
      if (timestamp < this.lastTimestamp) {
        throw new Error('Clock moved backwards. Refusing to generate id for ' +
          (this.lastTimestamp - timestamp))
      }
      if (this.lastTimestamp === timestamp) {
        this.sequence = (this.sequence + 1) & this.sequenceMask
        if (this.sequence === 0) {
          timestamp = this.tilNextMillis(this.lastTimestamp)
        }
      } else {
        this.sequence = 0
      }
      this.lastTimestamp = timestamp
      const shiftNum = (this.dataCenterId << this.dataCenterIdShift) |
        (this.workerId << this.workerIdShift) |
        this.sequence // dataCenterId:1,workerId:1,sequence:0  shiftNum:135168
      let nfirst = new bigInt(String(timestamp - this.twepoch), 10)
      nfirst = nfirst.shiftLeft(this.timestampLeftShift)
      const nnextId = nfirst.or(new bigInt(String(shiftNum), 10)).toString(10)
      return nnextId
    }
    return Snowflake
  }())
  const tempSnowflake = new Snowflake(1, 1, 0)
  const tempId = tempSnowflake.nextId()
  return tempId
}

// 生成uuid
export function createUuidAndSnowflake() {
  const uuid = uuidv4().slice(-4)
  // console.log(uuid)
  const snowflake = Snowflake().slice(-4)
  return uuid + '_' + snowflake
}

/**
 * 使用test方法实现模糊查询
 * @param  {Array}  list     原数组
 * @param  {String} keyWord  查询的关键词
 * @return {Array}           查询的结果
 */
export function fuzzyQuery(list, keyWord) {
  const reg = new RegExp(keyWord)
  const arr = []
  list.forEach((value, index) => {
    const compTeam = value.children.filter((val, idx) => {
      return reg.test(val.label)
    })
    const obj = JSON.parse(JSON.stringify(value))
    if (compTeam.length) {
      obj.children = compTeam
      arr.push(obj)
    }
  })
  return arr
}

// 防抖
export function debounce(fn, delay = 300) {
  let timer
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      timer = null
      fn.apply(this, arguments)
    }, delay)
  }
}
// 随机字符串
export function randomString(e) {
  e = e || 32
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const a = t.length
  let n = ''
  for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}

// 数学计算去除防止精度误差问题
export class MathBase {
  // 加法
  add(...list) {
    return list.reduce(MathBase._add)
  }

  // 减法
  subtract(...list) {
    return list.reduce(MathBase._subtract)
  }

  // 乘法
  multiply(...list) {
    return list.reduce(MathBase._multiply)
  }

  // 除法
  divide(...list) {
    return list.reduce(MathBase._divide)
  }

  // 加法
  static _add(n, m) {
    const { F, S, T, l1, l2 } = MathBase.getInteger(n, m)
    return (F[0] * T + F[1] * T / Math.pow(10, l1) + S[0] * T + S[1] * T / Math.pow(10, l2)) / T
  }

  // 减法
  static _subtract(n, m) {
    const { F, S, T, l1, l2 } = MathBase.getInteger(n, m)
    return (F[0] * T + F[1] * T / Math.pow(10, l1) - S[0] * T - S[1] * T / Math.pow(10, l2)) / T
  }

  // 乘法
  static _multiply(n, m) {
    const { F, S, T, l1, l2 } = MathBase.getInteger(n, m)
    return ((F[0] * T + F[1] * T / Math.pow(10, l1)) * (S[0] * T + S[1] * T / Math.pow(10, l2))) / T / T
  }

  // 除法
  static _divide(n, m) {
    const { F, S, T, l1, l2 } = MathBase.getInteger(n, m)
    return ((F[0] * T + F[1] * T / Math.pow(10, l1)) / (S[0] * T + S[1] * T / Math.pow(10, l2)))
  }

  static numToString(tempArray) {
    if (typeof tempArray === 'number') {
      return tempArray.toString()
    }
    return '0'
  }

  static handleNum(n) {
    n = n.toString()
    const temp = n.split('.')
    temp.push(temp[1].length)
    return temp
  }

  static getInteger(n, m) {
    n = typeof n === 'string' ? n : MathBase.numToString(n)
    m = typeof m === 'string' ? m : MathBase.numToString(m)
    const F = n.indexOf('.') !== -1 ? MathBase.handleNum(n) : [n, 0, 0]
    const S = m.indexOf('.') !== -1 ? MathBase.handleNum(m) : [m, 0, 0]
    const l1 = F[2]; const l2 = S[2]
    const L = Math.max(l1, l2)
    const T = Math.pow(10, L)
    return {
      F,
      S,
      T,
      l1,
      l2
    }
  }
}
/* URL的形式的链接下载 */
export const downloadFileInURL = (url, name) => {
  if (/(png|jpg|gif|jpeg|webp)$/.test(name)) {
    const image = new Image();
    // 解决跨域 Canvas 污染问题
    // image.setAttribute('crossOrigin', 'anonymous');
    image.onload = function () {
      const canvas = document.createElement('canvas');
      canvas.width = image.width;
      canvas.height = image.height;
      const context = canvas.getContext('2d');
      context?.drawImage(image, 0, 0, image.width, image.height);
      const url = canvas.toDataURL('image/png'); // 得到图片的base64编码数据
      const a = document.createElement('a'); // 生成一个a元素
      const event = new MouseEvent('click'); // 创建一个单击事件
      a.download = name; // 设置图片名称
      a.href = url; // 将生成的URL设置为a.href属性
      a.dispatchEvent(event); // 触发a的单击事件
    };
    image.src = url;
  } else {
    const oReq = new XMLHttpRequest();
    oReq.open('GET', url, true);
    oReq.responseType = 'blob';
    oReq.onload = function () {
      const file = new Blob([oReq.response], {
        type: 'application/pdf'
      });

      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = window.URL.createObjectURL(new Blob([file]));
      link.setAttribute('download', name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    oReq.send();
  }
};
