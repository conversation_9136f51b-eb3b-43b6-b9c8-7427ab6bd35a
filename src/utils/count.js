/*
 * formatThousandth(s,type)
 * 功能：按千位逗号分割
 * 参数：s，需要格式化的数值.
 * 参数：type，判断格式化后的数值是否需要小数位.
 * 返回：返回格式化后的数值字符串.
 */
export const formatThousandth = (s, type) => {
  const n = type > 0 && type <= 20 ? type : 2;
  s = parseFloat((s + '').replace(/[^\d.-]/g, '')) + '';
  const result = s.toString().indexOf('.'); // 查找是否含有小数点
  if (result !== -1) s = Number(s).toFixed(n) + '';
  const l = s.split('.')[0].split('').reverse();
  const r = s.split('.')[1];
  let t = '';
  for (let i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 === 0 && i + 1 !== l.length ? ',' : '');
  }
  const num = r ? t.split('').reverse().join('') + '.' + r : t.split('').reverse().join('');
  return num;
};

/**
 * 数字相除
 * @param {*} arg1
 * @param {*} arg2
 * @returns
 */
export const accDiv = (arg1, arg2) => {
  const t1 = getDecimalLength(arg1);
  const t2 = getDecimalLength(arg2);
  if (t1 - t2 > 0) {
    return getNum(arg1) / getNum(arg2) / Math.pow(10, t1 - t2);
  } else {
    return (getNum(arg1) / getNum(arg2)) * Math.pow(10, t2 - t1);
  }
};

function getDecimalLength(arg) {
  try {
    return arg.toString().split('.')[1].length;
  } catch (e) {
    return 0;
  }
}

function getNum(arg) {
  return Number(arg.toString().replace('.', ''));
}
