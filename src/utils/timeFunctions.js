// 基础依赖函数
// 将时间参数格式化为标准毫秒级时间戳
const formatToTimestamp = 'var formatToTimestamp = function (str) {\n' +
  '  var timestamp = str.substring(0, 4) + \'/\' + str.substring(4, 6) + \'/\' + str.substring(6, 8) + \' \' + str.substring(8, 10) + \':\' + str.substring(10, 12) + \':\' + str.substring(12, 14);\n' +
  '  timestamp = new Date(timestamp).getTime();\n' +
  '  return timestamp;\n' +
  '}\n'

// 将毫秒级时间戳格式化为时间
const formatToTime = 'var formatToTime = function (timestamp) {\n' +
  '  var date = new Date(timestamp);\n' +
  '  var YY = date.getFullYear().toString();\n' +
  '  var MM = (date.getMonth() + 1 < 10 ? \'0\' + (date.getMonth() + 1) : (date.getMonth() + 1).toString());\n' +
  '  var DD = date.getDate() < 10 ? \'0\' + date.getDate() : date.getDate().toString();\n' +
  '  var hh = date.getHours() < 10 ? \'0\' + date.getHours() : date.getHours().toString();\n' +
  '  var mm = date.getMinutes() < 10 ? \'0\' + date.getMinutes() : date.getMinutes().toString();\n' +
  '  var ss = date.getSeconds() < 10 ? \'0\' + date.getSeconds() : date.getSeconds().toString();\n' +
  '  return YY + MM + DD + hh + mm + ss;\n' +
  '}\n'

// 获取系统当前时间
const getCurrentTime = 'var getCurrentTime = function () {\n' +
  '  var currentTime = new Date();\n' +
  '  return currentTime.getTime();\n' +
  '}\n'

// 获取某时间点所在自然年、月、日、时的起止时间
const getTime = 'var getTime = function (type, time) {\n' +
  '    var finalTime = time && time.data ? time.data : time\n' +
  '    var finalType = type && (type.data || type.data === 0) ? type.data : type\n' +
  '    if (!finalTime || finalTime.toString().length !== 13) {\n' +
  '      return null;\n' +
  '    }\n' +
  '    var start, end;\n' +
  '    if (finalType === 0) {\n' +
  '      start = formatToTimestamp(formatToTime(finalTime).substring(0, 4) + \'0101000000\');\n' +
  '      end = formatToTimestamp(formatToTime(finalTime).substring(0, 4) + \'1231235959\');\n' +
  '    } else if (finalType === 1) {\n' +
  '      start = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + \'01000000\');\n' +
  '      if (formatToTime(finalTime).substring(4, 6) === \'01\' || formatToTime(finalTime).substring(4, 6) === \'03\' || formatToTime(finalTime).substring(4, 6) === \'05\' || formatToTime(finalTime).substring(4, 6) === \'07\' || formatToTime(finalTime).substring(4, 6) === \'08\' || formatToTime(finalTime).substring(4, 6) === \'10\' || formatToTime(finalTime).substring(4, 6) === \'12\') {\n' +
  '        end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + \'31235959\');\n' +
  '      } else if (formatToTime(finalTime).substring(4, 6) === \'02\') {\n' +
  '        if (parseFloat(formatToTime(finalTime).substring(0, 4)) % 4 === 0) {\n' +
  '          end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + \'29235959\');\n' +
  '        } else {\n' +
  '          end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + \'28235959\');\n' +
  '        }\n' +
  '      } else {\n' +
  '        end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + \'30235959\');\n' +
  '      }\n' +
  '    } else if (finalType === 2) {\n' +
  '      start = formatToTimestamp(formatToTime(finalTime).substring(0, 8) + \'000000\');\n' +
  '      end = formatToTimestamp(formatToTime(finalTime).substring(0, 8) + \'235959\');\n' +
  '    } else if (finalType === 3) {\n' +
  '      start = formatToTimestamp(formatToTime(finalTime).substring(0, 10) + \'0000\');\n' +
  '      end = formatToTimestamp(formatToTime(finalTime).substring(0, 10) + \'5959\');\n' +
  '    } else {\n' +
  '      start = null;\n' +
  '      end = null;\n' +
  '    }\n' +
  '    return JSON.stringify([start, end]);\n' +
  '  }\n'

// 获取在当前时间基础上移动指定时长后的时间值
const moveTime = 'var moveTime = function (time, step, distance) {\n' +
  '  if (!time || time.toString().length !== 13) {\n' +
  '    return null;\n' +
  '  }\n' +
  '  var finalTimestamp;\n' +
  '  if (step === \'sec\') {\n' +
  '      finalTimestamp = time + distance * 1000;\n' +
  '  } else if (step === \'min\') {\n' +
  '      finalTimestamp = time + distance * 60 * 1000;\n' +
  '  } else if (step === \'hour\') {\n' +
  '      finalTimestamp = time + distance * 60 * 60 * 1000;\n' +
  '  } else if (step === \'day\') {\n' +
  '      finalTimestamp = time + distance * 24 * 60 * 60 * 1000;\n' +
  '  } else if (step === \'week\') {\n' +
  '      finalTimestamp = time + distance * 7 * 24 * 60 * 60 * 1000;\n' +
  '  } else if (step === \'month\') {\n' +
  '      finalTimestamp = time + distance * 31 * 24 * 60 * 60 * 1000;\n' +
  '  } else if (step === \'year\') {\n' +
  '      var initStr = formatToTime(time);\n' +
  '      var finalStr = (parseFloat(initStr.substring(0, 4)) + distance).toString() + initStr.substring(4, 14);\n' +
  '      finalTimestamp = formatToTimestamp(finalStr);\n' +
  '  } else {' +
  '      finalTimestamp = null;\n' +
  '  }\n' +
  '  return finalTimestamp;\n' +
  '}\n'

// 获取时间点的指定数据
const getTimePart = 'var getTimePart = function (time, type) {\n' +
  '  if (!time || time.toString().length !== 13) {\n' +
  '    return null;\n' +
  '  }\n' +
  '  var finalStr;\n' +
  '  if (type === \'year\') {\n' +
  '      finalStr = formatToTime(time).substring(0, 4);\n' +
  '  } else if (type === \'month\') {\n' +
  '      finalStr = formatToTime(time).substring(4, 6);\n' +
  '  } else if (type === \'day\') {\n' +
  '      finalStr = formatToTime(time).substring(6, 8);\n' +
  '  } else if (type === \'hour\') {\n' +
  '      finalStr = formatToTime(time).substring(8, 10);\n' +
  '  } else {' +
  '      finalStr = null;\n' +
  '  }\n' +
  '  return finalStr;\n' +
  '}\n'

const timeToTimestamp = `var timeToTimestamp = function (time) {
    console.log(time)
    var date = new Date(time);
    console.log(date)
    return date.getTime();
}
`

export {
  formatToTimestamp,
  formatToTime,
  getCurrentTime,
  timeToTimestamp,
  getTime,
  moveTime,
  getTimePart
}
