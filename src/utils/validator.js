// 输入中文英文数字及符号(不能输入空格)
export const requiredInput = /^[^\s]*$/;

// 输入中文英文数字及符号_ 、 .（联系人）
export const personInput = /^[\u4e00-\u9fa5_.、a-zA-Z0-9]+$/;

// 输入数字字母
export const codeFormats = /^[a-zA-Z0-9]*$/;

// 大于等于0 小数点后两位
export const numberFormater = /^\d+(\.\d{1,2})?$/;

// 正整数
export const positiveInteger = /^[0-9]*[1-9][0-9]*$/;

// 正整数和0
export const integerFormats = /(^[0-9]\d*$)/;

// 数字及— (固定电话)
export const fixedPhone = /^\d{3}-\d{7,8}|\d{4}-\d{7,8}$/;

// 支持数字和‘-’
export const numberSeparatorFormats = /^[0-9-]+$/;

// 支持正整数或正浮点数
export const integerDecimalFormats = /^[1-9][0-9]*$|^(?:[1-9][0-9]*\.[0-9]+|0\.(?!0+$)[0-9]+)$/;

// 支持整数最大8位 小数点后两位
export const decimalFormats = /^0(\.[0-9]{1,2})*$|^[1-9]\d{0,7}(\.\d{1,2})*$/;

// 支持整数最大8位 小数点后两位 支持负数
export const decimalMinusFormats = /^-?\d{1,8}([.]\d{1,2})?$/;

// 首位不能输入空格
export const spaceFormats = /^\S.*\S$|(^\S{0,1}\S$)/;

// http/https 是否有效
export const httpFormats = /^(http|https):\/\/[\w.-]+(?:\.[\w-]+)+[\w\-._~:/?#[\]@!$&'*+,;=.]+$/;
// 邮箱
export const emailFormats = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
// 中文
export const chineseFormats = /^[\u4e00-\u9fa5]*$/;
// 数字和‘-’
export const phoneFormats = /^[\d\-,]+$/;

// 统一社会信用代码
export const enterpriseFormats = /^\w{18}$/;

// 身份证号
export const idNoFormats =
  /^([1-9]\d{5}(19|20|21)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx])|[1-9]\d{5}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/;
export const phoneFormat = /^\d{11}$/;
