import Vue from 'vue'


var eventBus = new Vue({});

// 过滤空内容显示--
Vue.filter('IsNull', function (value) {
  if (value || value === 0) return value
  return '- -'
})


// 时间戳转化字符串 - 年月日时分秒
Vue.filter('StringTime', function (value) {
  if (value) {
    let add0 = function (m) {
      return m < 10 ? '0' + m : m
    }
    let time = new Date(value)
    let y = time.getFullYear()
    let m = time.getMonth() + 1
    let d = time.getDate()
    let h = time.getHours()
    let mm = time.getMinutes()
    let s = time.getSeconds()
    return y + '-' + add0(m) + '-' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s)
  } else {
    return '- -'
  }
})


// 时间戳转化字符串 - 年月日
Vue.filter('StringTimeDay', function (value) {
  if (value) {
    let add0 = function (m) {
      return m < 10 ? '0' + m : m
    }
    let time = new Date(value)
    let y = time.getFullYear()
    let m = time.getMonth() + 1
    let d = time.getDate()
    return y + '-' + add0(m) + '-' + add0(d)
  } else {
    return '- -'
  }
})


// 规则算法 - 算法接口 - 调用参数说明 - CIM点名
Vue.filter('CimNameMatching', function (value) {
  if (value === '0') {
    return '字符串'
  } else if (value === '1') {
    return '数字'
  } else if (value === '2') {
    return '数值数组'
  } else if (value === '3') {
    return '字符串数组'
  } else if (value === '4') {
    return '布尔'
  } else if (value === '5') {
    return '时间'
  } else if (value === '6') {
    return 'CIM物联点'
  } else if (value === '7') {
    return 'CIM静态属性点'
  } else if (value === '8') {
    return '自定义指标点'
  } else {
    return '- -'
  }
})
export default eventBus;