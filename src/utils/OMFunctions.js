// 等级评定（变量，常数组 下限值序列，常数组 上限值序列，常数组 等级序列）
const getRankByScore = 'var getRankByScore = function (score, lower, upper, degree) {\n' +
  '    if (lower.length !== upper.length || lower.length !== degree.length) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    var temStr = \'无等级\';\n' +
  '    var temObj = {};\n' +
  '    var temArr = [];\n' +
  '    var arrLength = lower.length;\n' +
  '    for (var i = 0; i < arrLength; i++) {\n' +
  '      temObj.range = [lower[i], upper[i]];\n' +
  '      temObj.label = degree[i];\n' +
  '      temArr.push(temObj);\n' +
  '      temObj = {};\n' +
  '    }\n' +
  '    for (var i = 0; i < temArr.length; i++) {\n' +
  '      if (score >= temArr[i].range[0] && score < temArr[i].range[1]) {\n' +
  '        temStr = temArr[i].label;\n' +
  '        break;\n' +
  '      }\n' +
  '    }\n' +
  '    return temStr;\n' +
  '  }\n'
// 死区设置（变量，int/float 上限死区，int/float 下限死区）
const setDeadZone = 'var setDeadZone = function (score, lower, upper) {\n' +
  '    var finalScore = score && score.data ? score.data : score;\n' +
  '    var finalLower = lower && lower.data ? lower.data : lower;\n' +
  '    var finalUpper = upper && upper.data ? upper.data : upper;\n' +
  '    var temScore = 0;\n' +
  '    if (finalScore >= finalLower && finalScore <= finalUpper) {\n' +
  '      temScore = finalScore;\n' +
  '    } else if (finalScore > finalUpper) {\n' +
  '      temScore = finalUpper\n' +
  '    } else if (finalScore < finalLower) {\n' +
  '      temScore = finalLower\n' +
  '    }\n' +
  '    return temScore;\n' +
  '  }\n'
// 空值判断（变量）
const getIsNull = 'var getIsNull = function (param) {\n' +
  '    if (param === 0) {\n' +
  '      return false;\n' +
  '    }\n' +
  '    if (!param) {\n' +
  '      return true;\n' +
  '    }\n' +
  '    var finalParam = param && (param.data || param.data === \'\') ? param.data : param;\n' +
  '    var temResult = false;\n' +
  '    if (typeof finalParam === \'string\' && typeof JSON.parse(finalParam) === \'object\') {\n' +
  '      finalParam = JSON.parse(finalParam)\n' +
  '    }\n' +
  '    if (Array.isArray(finalParam)) {\n' +
  '      var allIsNull = true;\n' +
  '      for (let i = 0; i < finalParam.length; i++) {\n' +
  '        if (finalParam[i][1] !== null) {\n' +
  '          allIsNull = false;\n' +
  '          break;\n' +
  '        }\n' +
  '      }\n' +
  '      return allIsNull;\n' +
  '    } else {\n' +
  '      if (finalParam === null || (typeof finalParam !== \'number\' && finalParam.length === 0) || finalParam === \'[]\' || finalParam === "\'\'" || finalParam === \'undefined\') {\n' +
  '        temResult = true;\n' +
  '      }\n' +
  '      return temResult;\n' +
  '    }\n' +
  '  }\n'
// 数组列为空判断
const getIsArrayNull = 'var getIsArrayNull = function (param1, param2) {\n' +
  '    if (!param1) {\n' +
  '      return true;\n' +
  '    }\n' +
  '    var finalParam1 = param1 && (param1.data || param1.data === \'\') ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && (param2.data || param2.data === \'\') ? param2.data : param2;\n' +
  '    if (typeof finalParam1 === \'string\' && typeof JSON.parse(finalParam1) === \'object\') {\n' +
  '      finalParam1 = JSON.parse(finalParam1)\n' +
  '    }\n' +
  '    if (Array.isArray(finalParam1) && !!finalParam2) {\n' +
  '      var allIsNull = true;\n' +
  '      for (let i = 0; i < finalParam1.length; i++) {\n' +
  '        if (finalParam1[i][parseInt(finalParam2)] !== null) {\n' +
  '          allIsNull = false;\n' +
  '          break;\n' +
  '        }\n' +
  '      }\n' +
  '      return allIsNull;\n' +
  '    } else {\n' +
  '      var temResult = false;\n' +
  '      if (finalParam1 === null || (typeof finalParam1 !== \'number\' && finalParam1.length === 0) || finalParam1 === \'[]\' || finalParam1 === "\'\'" || finalParam1 === \'undefined\') {\n' +
  '        temResult = true;\n' +
  '      }\n' +
  '      return temResult;\n' +
  '    }\n' +
  '  }\n'
// 数据位数处理
const handleData = 'var handleData = function (param1, param2, param3) {\n' +
  '    if (!param1) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    if (param2 === undefined || param3 === undefined) {\n' +
  '      return undefined;\n' +
  '    }\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && (param2.data || typeof param2.data === \'number\') ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && (param3.data || typeof param3.data === \'number\')  ? param3.data : param3;\n' +
  '    if (finalParam1.toString().split(\'.\')[1] !== undefined) {\n' +
  '      if (finalParam1.toString().split(\'.\')[1].length <= finalParam2) {\n' +
  '        return finalParam1;\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (0 <= finalParam2) {\n' +
  '        return finalParam1;\n' +
  '      }\n' +
  '    }\n' +
  '    var temResult = finalParam1;\n' +
  '    if (finalParam3 === 0 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 0)) {\n' +
  '      temResult = Math.floor(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))\n' +
  '    } else if (finalParam3 === 1 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 1)) {\n' +
  '      temResult = Math.ceil(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))\n' +
  '    } else if (finalParam3 === 2 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 2)) {\n' +
  '      temResult = Math.round(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))\n' +
  '    }\n' +
  '    return temResult;\n' +
  '  }\n'
// 求指数值
const getExponent = 'var getExponent = function (param1, param2) {\n' +
  '    var finalParam1 = param1 && param1.data ? param1.data : param1;\n' +
  '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
  '    return Math.pow(finalParam1, finalParam2);\n' +
  '  }\n'
// 求绝对值
const getAbsolute = 'var getAbsolute = function (param) {\n' +
  '    var finalParam = param && param.data ? param.data : param;\n' +
  '    return Math.abs(finalParam);\n' +
  '  }\n'
// 时段范围缩扩（常数组 时段序列，str 是否允许出范围，str 新起点位置，str 新终点位置）
const handleTime1 = 'var handleTime1 = function (param1, param2, param3, param4) {\n' +
  '    var finalParam1 = [];\n' +
  '    if (param1.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param1.properties).length; i++) {\n' +
  '        if (param1.properties[Object.keys(param1.properties)[i]].data) {\n' +
  '          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '        finalParam1 = JSON.parse(param1);\n' +
  '      } else {\n' +
  '        finalParam1 = param1;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.data ? param3.data : param3;\n' +
  '    var finalParam4 = param4 && param4.data ? param4.data : param4;\n' +
  '    if (finalParam2 !== \'是\' && finalParam2 !== \'否\') {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    if ((finalParam3.indexOf(\'+\') === -1 && finalParam3.indexOf(\'-\') === -1) || (finalParam4.indexOf(\'+\') === -1 && finalParam4.indexOf(\'-\') === -1)) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    if ((finalParam3.indexOf(\'+\') !== -1 && finalParam4.indexOf(\'+\') === -1) || (finalParam3.indexOf(\'-\') !== -1 && finalParam4.indexOf(\'-\') === -1)) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    if (finalParam3.indexOf(\'+\') !== -1 && finalParam4.indexOf(\'+\') !== -1) {\n' +
  '      if (parseFloat(finalParam3.split(\'+\')[1]) > parseFloat(finalParam4.split(\'+\')[1])) {\n' +
  '        return \'null\';\n' +
  '      }\n' +
  '    }\n' +
  '    if (finalParam3.indexOf(\'-\') !== -1 && finalParam4.indexOf(\'-\') !== -1) {\n' +
  '      if (parseFloat(finalParam3.split(\'-\')[1]) < parseFloat(finalParam4.split(\'-\')[1])) {\n' +
  '        return \'null\';\n' +
  '      }\n' +
  '    }\n' +
  '    var temArr = [];\n' +
  '    var num1, num2;\n' +
  '    if (finalParam3.indexOf(\'+\') !== -1) {\n' +
  '      if (finalParam2 === \'否\') {\n' +
  '        for (var i = 0; i < finalParam1.length; i++) {\n' +
  '          num1 = finalParam1[i][0] + parseFloat(finalParam3.split(\'+\')[1]) * 60 * 1000\n' +
  '          num2 = finalParam1[i][0] + parseFloat(finalParam4.split(\'+\')[1]) * 60 * 1000\n' +
  '          if (num1 >= finalParam1[i][1]) {\n' +
  '            temArr.push([finalParam1[i][1], finalParam1[i][1]])\n' +
  '          } else if (num1 < finalParam1[i][1] && num2 >= finalParam1[i][1]) {\n' +
  '            temArr.push([num1, finalParam1[i][1]])\n' +
  '          } else if (num1 < finalParam1[i][1] && num2 < finalParam1[i][1]) {\n' +
  '            temArr.push([num1, num2])\n' +
  '          } else {\n' +
  '            temArr.push([])\n' +
  '          }\n' +
  '        }\n' +
  '      } else {\n' +
  '        for (var i = 0; i < finalParam1.length; i++) {\n' +
  '          num1 = finalParam1[i][0] + parseFloat(finalParam3.split(\'+\')[1]) * 60 * 1000\n' +
  '          num2 = finalParam1[i][0] + parseFloat(finalParam4.split(\'+\')[1]) * 60 * 1000\n' +
  '          temArr.push([num1, num2])\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (finalParam2 === \'否\') {\n' +
  '        for (var i = 0; i < finalParam1.length; i++) {\n' +
  '          num1 = finalParam1[i][1] - parseFloat(finalParam3.split(\'-\')[1]) * 60 * 1000\n' +
  '          num2 = finalParam1[i][1] - parseFloat(finalParam4.split(\'-\')[1]) * 60 * 1000\n' +
  '          if (num2 <= finalParam1[i][0]) {\n' +
  '            temArr.push([finalParam1[i][0], finalParam1[i][0]])\n' +
  '          } else if (num2 > finalParam1[i][0] && num1 <= finalParam1[i][0]) {\n' +
  '            temArr.push([finalParam1[i][0], num2])\n' +
  '          } else if (num1 > finalParam1[i][0] && num2 > finalParam1[i][0]) {\n' +
  '            temArr.push([num1, num2])\n' +
  '          } else {\n' +
  '            temArr.push([])\n' +
  '          }\n' +
  '        }\n' +
  '      } else {\n' +
  '        for (var i = 0; i < finalParam1.length; i++) {\n' +
  '          num1 = finalParam1[i][1] - parseFloat(finalParam3.split(\'-\')[1]) * 60 * 1000\n' +
  '          num2 = finalParam1[i][1] - parseFloat(finalParam4.split(\'-\')[1]) * 60 * 1000\n' +
  '          temArr.push([num1, num2])\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '    return JSON.stringify(temArr);\n' +
  '  }\n'
// 时间点扩展（常数组 时间点序列，str 前扩展量，str 后扩展量）
const handleTime2 = 'var handleTime2 = function (param1, param2, param3) {\n' +
  '    var finalParam1 = [];\n' +
  '    if (param1.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param1.properties).length; i++) {\n' +
  '        if (param1.properties[Object.keys(param1.properties)[i]].data) {\n' +
  '          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '        finalParam1 = JSON.parse(param1);\n' +
  '      } else {\n' +
  '        finalParam1 = param1;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = param2 && param2.data ? param2.data : param2;\n' +
  '    var finalParam3 = param3 && param3.data ? param3.data : param3;\n' +
  '    if ((finalParam2.indexOf(\'-\') === -1 && finalParam2.indexOf(\'+\') === -1) || (finalParam3.indexOf(\'-\') === -1 && finalParam3.indexOf(\'+\') === -1)) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    var temArr = [];\n' +
  '    var num1, num2;\n' +
  '    if (finalParam1 instanceof Array) {\n' +
  '      for (var i = 0; i < finalParam1.length; i++) {\n' +
  '        if (typeof parseFloat(finalParam1[i]) === \'number\') {\n' +
  '          num1 = parseFloat(finalParam1[i]) + parseFloat(finalParam2) * 60 * 1000\n' +
  '          num2 = parseFloat(finalParam1[i]) + parseFloat(finalParam3) * 60 * 1000\n' +
  '          temArr.push([num1, num2])\n' +
  '        } else {\n' +
  '          temArr.push([])\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (typeof parseFloat(finalParam1) === \'number\') {\n' +
  '        num1 = parseFloat(finalParam1) + parseFloat(finalParam2) * 60 * 1000\n' +
  '        num2 = parseFloat(finalParam1) + parseFloat(finalParam3) * 60 * 1000\n' +
  '        temArr.push([num1, num2])\n' +
  '      } else {\n' +
  '        temArr.push([])\n' +
  '      }\n' +
  '    }\n' +
  '    return JSON.stringify(temArr);\n' +
  '  }\n'
// 时段求交集（常数组 时段序列1，常数组 时段序列2，常数组 时段序列3，常数组 时段序列4）
const handleTime3 = 'var handleTime3 = function (param1, param2, param3, param4) {\n' +
  '    var finalParam1 = [];\n' +
  '    if (param1 && param1.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param1.properties).length; i++) {\n' +
  '        if (param1.properties[Object.keys(param1.properties)[i]].data) {\n' +
  '          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param1) {\n' +
  '        if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '          finalParam1 = JSON.parse(param1);\n' +
  '        } else {\n' +
  '          finalParam1 = param1;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam1 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = [];\n' +
  '    if (param2 && param2.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param2.properties).length; i++) {\n' +
  '        if (param2.properties[Object.keys(param2.properties)[i]].data) {\n' +
  '          finalParam2.push(param2.properties[Object.keys(param2.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param2) {\n' +
  '        if (typeof param2 === \'string\' && typeof JSON.parse(param2) === \'object\') {\n' +
  '          finalParam2 = JSON.parse(param2);\n' +
  '        } else {\n' +
  '          finalParam2 = param2;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam2 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam3 = [];\n' +
  '    if (param3 && param3.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param3.properties).length; i++) {\n' +
  '        if (param3.properties[Object.keys(param3.properties)[i]].data) {\n' +
  '          finalParam3.push(param3.properties[Object.keys(param3.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param3) {\n' +
  '        if (typeof param3 === \'string\' && typeof JSON.parse(param3) === \'object\') {\n' +
  '          finalParam3 = JSON.parse(param3);\n' +
  '        } else {\n' +
  '          finalParam3 = param3;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam3 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam4 = [];\n' +
  '    if (param4 && param4.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param4.properties).length; i++) {\n' +
  '        if (param4.properties[Object.keys(param4.properties)[i]].data) {\n' +
  '          finalParam4.push(param4.properties[Object.keys(param4.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param4) {\n' +
  '        if (typeof param4 === \'string\' && typeof JSON.parse(param4) === \'object\') {\n' +
  '          finalParam4 = JSON.parse(param4);\n' +
  '        } else {\n' +
  '          finalParam4 = param4;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam4 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    if (!finalParam1 || !finalParam2) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    var temArr1 = [];\n' +
  '    var temArr2 = [];\n' +
  '    var temArr3 = [];\n' +
  '    var finalArr = [];\n' +
  '    var isExist = false;\n' +
  '    for (var i = 0; i < finalParam1.length; i++) {\n' +
  '      for (var m = 0; m < finalParam2.length; m++) {\n' +
  '        isExist = false;\n' +
  '        if (finalParam2[m][0] === finalParam1[i][0] && finalParam2[m][1] === finalParam1[i][1]) {\n' +
  '          for (let n = 0; n < temArr1.length; n++) {\n' +
  '            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '              isExist = true;\n' +
  '              break\n' +
  '            }\n' +
  '          }\n' +
  '          if (!isExist) {\n' +
  '            temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '          }\n' +
  '        } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1])) {\n' +
  '          for (let n = 0; n < temArr1.length; n++) {\n' +
  '            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '              isExist = true;\n' +
  '              break\n' +
  '            }\n' +
  '          }\n' +
  '          if (!isExist) {\n' +
  '            temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '          }\n' +
  '        } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1])) {\n' +
  '          for (let n = 0; n < temArr1.length; n++) {\n' +
  '            if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '              isExist = true;\n' +
  '              break\n' +
  '            }\n' +
  '          }\n' +
  '          if (!isExist) {\n' +
  '            temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '          }\n' +
  '        } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1] && finalParam2[m][0] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1] && finalParam2[m][0] < finalParam1[i][1])) {\n' +
  '          for (let n = 0; n < temArr1.length; n++) {\n' +
  '            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '              isExist = true;\n' +
  '              break\n' +
  '            }\n' +
  '          }\n' +
  '          if (!isExist) {\n' +
  '            temArr1.push([finalParam2[m][0], finalParam1[i][1], (finalParam1[i][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '          }\n' +
  '        } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1] && finalParam2[m][1] > finalParam1[i][0]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1] && finalParam2[m][1] > finalParam1[i][0])) {\n' +
  '          for (let n = 0; n < temArr1.length; n++) {\n' +
  '            if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '              isExist = true;\n' +
  '              break\n' +
  '            }\n' +
  '          }\n' +
  '          if (!isExist) {\n' +
  '            temArr1.push([finalParam1[i][0], finalParam2[m][1], (finalParam2[m][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '    finalArr = temArr1;\n' +
  '    if (temArr1.length > 0 && finalParam3) {\n' +
  '      for (var i = 0; i < temArr1.length; i++) {\n' +
  '        for (var m = 0; m < finalParam3.length; m++) {\n' +
  '          isExist = false;\n' +
  '          if (finalParam3[m][0] === temArr1[i][0] && finalParam3[m][1] === temArr1[i][1]) {\n' +
  '            for (let n = 0; n < temArr2.length; n++) {\n' +
  '              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1])) {\n' +
  '            for (let n = 0; n < temArr2.length; n++) {\n' +
  '              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1])) {\n' +
  '            for (let n = 0; n < temArr2.length; n++) {\n' +
  '              if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1] && finalParam3[m][0] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1] && finalParam3[m][0] < temArr1[i][1])) {\n' +
  '            for (let n = 0; n < temArr2.length; n++) {\n' +
  '              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr2.push([finalParam3[m][0], temArr1[i][1], (temArr1[i][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1] && finalParam3[m][1] > temArr1[i][0]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1] && finalParam3[m][1] > temArr1[i][0])) {\n' +
  '            for (let n = 0; n < temArr2.length; n++) {\n' +
  '              if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr2.push([temArr1[i][0], finalParam3[m][1], (finalParam3[m][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '      finalArr = temArr2;\n' +
  '    }\n' +
  '    if (temArr2.length > 0 && finalParam4) {\n' +
  '      for (var i = 0; i < temArr2.length; i++) {\n' +
  '        for (var m = 0; m < finalParam4.length; m++) {\n' +
  '          isExist = false;\n' +
  '          if (finalParam4[m][0] === temArr2[i][0] && finalParam4[m][1] === temArr2[i][1]) {\n' +
  '            for (let n = 0; n < temArr3.length; n++) {\n' +
  '              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1])) {\n' +
  '            for (let n = 0; n < temArr3.length; n++) {\n' +
  '              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1])) {\n' +
  '            for (let n = 0; n < temArr3.length; n++) {\n' +
  '              if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1] && finalParam4[m][0] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1] && finalParam4[m][0] < temArr2[i][1])) {\n' +
  '            for (let n = 0; n < temArr3.length; n++) {\n' +
  '              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr3.push([finalParam4[m][0], temArr2[i][1], (temArr2[i][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1] && finalParam4[m][1] > temArr2[i][0]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1] && finalParam4[m][1] > temArr2[i][0])) {\n' +
  '            for (let n = 0; n < temArr3.length; n++) {\n' +
  '              if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr3.push([temArr2[i][0], finalParam4[m][1], (finalParam4[m][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '      finalArr = temArr3;\n' +
  '    }\n' +
  '    return JSON.stringify(finalArr);\n' +
  '  }\n'
// 时段求并集（常数组 时段序列1，常数组 时段序列2，常数组 时段序列3，常数组 时段序列4）
const handleTime4 = 'var handleTime4 = function (param1, param2, param3, param4) {\n' +
  '    var finalParam1 = [];\n' +
  '    if (param1 && param1.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param1.properties).length; i++) {\n' +
  '        if (param1.properties[Object.keys(param1.properties)[i]].data) {\n' +
  '          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param1) {\n' +
  '        if (typeof param1 === \'string\' && typeof JSON.parse(param1) === \'object\') {\n' +
  '          finalParam1 = JSON.parse(param1);\n' +
  '        } else {\n' +
  '          finalParam1 = param1;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam1 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = [];\n' +
  '    if (param2 && param2.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param2.properties).length; i++) {\n' +
  '        if (param2.properties[Object.keys(param2.properties)[i]].data) {\n' +
  '          finalParam2.push(param2.properties[Object.keys(param2.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param2) {\n' +
  '        if (typeof param2 === \'string\' && typeof JSON.parse(param2) === \'object\') {\n' +
  '          finalParam2 = JSON.parse(param2);\n' +
  '        } else {\n' +
  '          finalParam2 = param2;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam2 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam3 = [];\n' +
  '    if (param3 && param3.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param3.properties).length; i++) {\n' +
  '        if (param3.properties[Object.keys(param3.properties)[i]].data) {\n' +
  '          finalParam3.push(param3.properties[Object.keys(param3.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param3) {\n' +
  '        if (typeof param3 === \'string\' && typeof JSON.parse(param3) === \'object\') {\n' +
  '          finalParam3 = JSON.parse(param3);\n' +
  '        } else {\n' +
  '          finalParam3 = param3;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam3 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam4 = [];\n' +
  '    if (param4 && param4.properties) {\n' +
  '      for (var i = 0; i < Object.keys(param4.properties).length; i++) {\n' +
  '        if (param4.properties[Object.keys(param4.properties)[i]].data) {\n' +
  '          finalParam4.push(param4.properties[Object.keys(param4.properties)[i]].data);\n' +
  '        }\n' +
  '      }\n' +
  '    } else {\n' +
  '      if (param4) {\n' +
  '        if (typeof param4 === \'string\' && typeof JSON.parse(param4) === \'object\') {\n' +
  '          finalParam4 = JSON.parse(param4);\n' +
  '        } else {\n' +
  '          finalParam4 = param4;\n' +
  '        }\n' +
  '      } else {\n' +
  '        finalParam4 = null;\n' +
  '      }\n' +
  '    }\n' +
  '    if (finalParam1 && !finalParam2) {\n' +
  '      return finalParam1;\n' +
  '    } else if (!finalParam1 && finalParam2) {\n' +
  '      return finalParam2;\n' +
  '    } else if (!finalParam1 && !finalParam2) {\n' +
  '      return null;\n' +
  '    }\n' +
  '    var temArr1 = [];\n' +
  '    var temArr2 = [];\n' +
  '    var temArr3 = [];\n' +
  '    var finalArr = [];\n' +
  '    var isExist = false;\n' +
  '    if (finalParam1.length > 0 && finalParam2.length > 0) {\n' +
  '      for (var i = 0; i < finalParam1.length; i++) {\n' +
  '        for (var m = 0; m < finalParam2.length; m++) {\n' +
  '          isExist = false;\n' +
  '          if (finalParam2[m][0] === finalParam1[i][0] && finalParam2[m][1] === finalParam1[i][1]) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1])) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1])) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1] && finalParam2[m][0] <= finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1] && finalParam2[m][0] <= finalParam1[i][1])) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam1[i][0], finalParam2[m][1], (finalParam2[m][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1] && finalParam2[m][1] >= finalParam1[i][0]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1] && finalParam2[m][1] >= finalParam1[i][0])) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam2[m][0], finalParam1[i][1], (finalParam1[i][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          } else if (finalParam2[m][1] < finalParam1[i][0] || finalParam2[m][0] > finalParam1[i][1]) {\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '            isExist = false;\n' +
  '            for (let n = 0; n < temArr1.length; n++) {\n' +
  '              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {\n' +
  '                isExist = true;\n' +
  '                break\n' +
  '              }\n' +
  '            }\n' +
  '            if (!isExist) {\n' +
  '              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)]);\n' +
  '            }\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '    } else if (finalParam1.length > 0 && finalParam2.length === 0) {\n' +
  '      temArr1 = finalParam1;\n' +
  '    } else if (finalParam1.length === 0 && finalParam2.length > 0) {\n' +
  '      temArr1 = finalParam2;\n' +
  '    }\n' +
  '    finalArr = temArr1;\n' +
  '    if (finalParam3) {\n' +
  '      if (temArr1.length > 0 && finalParam3.length > 0) {\n' +
  '        for (var i = 0; i < temArr1.length; i++) {\n' +
  '          for (var m = 0; m < finalParam3.length; m++) {\n' +
  '            isExist = false;\n' +
  '            if (finalParam3[m][0] === temArr1[i][0] && finalParam3[m][1] === temArr1[i][1]) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1])) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1])) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1] && finalParam3[m][0] <= temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1] && finalParam3[m][0] <= temArr1[i][1])) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([temArr1[i][0], finalParam3[m][1], (finalParam3[m][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1] && finalParam3[m][1] >= temArr1[i][0]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1] && finalParam3[m][1] >= temArr1[i][0])) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([finalParam3[m][0], temArr1[i][1], (temArr1[i][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if (finalParam3[m][1] < temArr1[i][0] || finalParam3[m][0] > temArr1[i][1]) {\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '              isExist = false;\n' +
  '              for (let n = 0; n < temArr2.length; n++) {\n' +
  '                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            }\n' +
  '          }\n' +
  '        }\n' +
  '      } else if (temArr1.length > 0 && finalParam3.length === 0) {\n' +
  '        temArr2 = temArr1;\n' +
  '      } else if (temArr1.length === 0 && finalParam3.length > 0) {\n' +
  '        temArr2 = finalParam3;\n' +
  '      }\n' +
  '      finalArr = temArr2;\n' +
  '    }\n' +
  '    if (finalParam4) {\n' +
  '      if (temArr2.length > 0 && finalParam4.length > 0) {\n' +
  '        for (var i = 0; i < temArr2.length; i++) {\n' +
  '          for (var m = 0; m < finalParam4.length; m++) {\n' +
  '            isExist = false;\n' +
  '            if (finalParam4[m][0] === temArr2[i][0] && finalParam4[m][1] === temArr2[i][1]) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1])) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1])) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1] && finalParam4[m][0] <= temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1] && finalParam4[m][0] <= temArr2[i][1])) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([temArr2[i][0], finalParam4[m][1], (finalParam4[m][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1] && finalParam4[m][1] >= temArr2[i][0]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1] && finalParam4[m][1] >= temArr2[i][0])) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([finalParam4[m][0], temArr2[i][1], (temArr2[i][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            } else if (finalParam4[m][1] < temArr2[i][0] || finalParam4[m][0] > temArr2[i][1]) {\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '              isExist = false;\n' +
  '              for (let n = 0; n < temArr3.length; n++) {\n' +
  '                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {\n' +
  '                  isExist = true;\n' +
  '                  break\n' +
  '                }\n' +
  '              }\n' +
  '              if (!isExist) {\n' +
  '                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)]);\n' +
  '              }\n' +
  '            }\n' +
  '          }\n' +
  '        }\n' +
  '      } else if (temArr2.length > 0 && finalParam4.length === 0) {\n' +
  '        temArr3 = temArr2;\n' +
  '      } else if (temArr2.length === 0 && finalParam4.length > 0) {\n' +
  '        temArr3 = finalParam4;\n' +
  '      }\n' +
  '      finalArr = temArr3;\n' +
  '    }\n' +
  '    return JSON.stringify(finalArr);\n' +
  '  }\n'
const jointString = 'var jointString = function (param1, param2, param3, param4) {\n' +
  '    function formatToTime (timestamp) {\n' +
  '      var finalTimestamp = timestamp && timestamp.data ? timestamp.data : timestamp\n' +
  '      var date = new Date(finalTimestamp);\n' +
  '      var YY = date.getFullYear().toString();\n' +
  '      var MM = (date.getMonth() + 1 < 10 ? \'0\' + (date.getMonth() + 1) : (date.getMonth() + 1).toString());\n' +
  '      var DD = date.getDate() < 10 ? \'0\' + date.getDate() : date.getDate().toString();\n' +
  '      var hh = date.getHours() < 10 ? \'0\' + date.getHours() : date.getHours().toString();\n' +
  '      var mm = date.getMinutes() < 10 ? \'0\' + date.getMinutes() : date.getMinutes().toString();\n' +
  '      var ss = date.getSeconds() < 10 ? \'0\' + date.getSeconds() : date.getSeconds().toString();\n' +
  '      return YY + \'年\' + MM + \'月\' + DD + \'日\' + \' \' + hh + \'时\' + mm + \'分\' + ss + \'秒\';\n' +
  '    }\n' +
  '    var finalParam1 = null;\n' +
  '    if (param1 || param1 === 0) {\n' +
  '      if (param1.properties) {\n' +
  '        finalParam1 = param1.properties;\n' +
  '      } else {\n' +
  '        finalParam1 = param1;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam2 = null;\n' +
  '    if (param2 || param2 === 0) {\n' +
  '      if (param2.properties) {\n' +
  '        finalParam2 = param2.properties;\n' +
  '      } else {\n' +
  '        finalParam2 = param2;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam3 = null;\n' +
  '    if (param3 || param3 === 0) {\n' +
  '      if (param3.properties) {\n' +
  '        finalParam3 = param3.properties;\n' +
  '      } else {\n' +
  '        finalParam3 = param3;\n' +
  '      }\n' +
  '    }\n' +
  '    var finalParam4 = null;\n' +
  '    if (param4 || param4 === 0) {\n' +
  '      if (param4.properties) {\n' +
  '        finalParam4 = param4.properties;\n' +
  '      } else {\n' +
  '        finalParam4 = param4;\n' +
  '      }\n' +
  '    }\n' +
  '    if ((!finalParam1 && finalParam1 !== 0) || (!finalParam2 && finalParam2 !== 0)) {\n' +
  '      return \'null\';\n' +
  '    }\n' +
  '    var finalString = \'\';\n' +
  '    if (finalParam1 && finalParam1.data) {\n' +
  '      finalParam1 = finalParam1.data\n' +
  '    }\n' +
  '    if (typeof finalParam1 === \'number\' && finalParam1.toString().length === 13) {\n' +
  '      finalParam1 = formatToTime(finalParam1);\n' +
  '    } else if (typeof finalParam1 === \'number\' && finalParam1.toString().length !== 13) {\n' +
  '      finalParam1 = finalParam1.toString();\n' +
  '    } else if (typeof finalParam1 === \'boolean\') {\n' +
  '      finalParam1 = finalParam1 ? \'真\' : \'假\';\n' +
  '    }\n' +
  '    if (finalParam2 && finalParam2.data) {\n' +
  '      finalParam2 = finalParam2.data\n' +
  '    }\n' +
  '    if (typeof finalParam2 === \'number\' && finalParam2.toString().length === 13) {\n' +
  '      finalParam2 = formatToTime(finalParam2);\n' +
  '    } else if (typeof finalParam2 === \'number\' && finalParam2.toString().length !== 13) {\n' +
  '      finalParam2 = finalParam2.toString();\n' +
  '    } else if (typeof finalParam2 === \'boolean\') {\n' +
  '      finalParam2 = finalParam2 ? \'真\' : \'假\';\n' +
  '    }\n' +
  '    finalString = finalParam1 + finalParam2;\n' +
  '    if (!!finalParam3) {\n' +
  '      if (finalParam3 && finalParam3.data) {\n' +
  '        finalParam3 = finalParam3.data\n' +
  '      }\n' +
  '      if (typeof finalParam3 === \'number\' && finalParam3.toString().length === 13) {\n' +
  '        finalParam3 = formatToTime(finalParam3);\n' +
  '      } else if (typeof finalParam3 === \'number\' && finalParam3.toString().length !== 13) {\n' +
  '        finalParam3 = finalParam3.toString();\n' +
  '      } else if (typeof finalParam3 === \'boolean\') {\n' +
  '        finalParam3 = finalParam3 ? \'真\' : \'假\';\n' +
  '      }\n' +
  '      finalString += finalParam3\n' +
  '    }\n' +
  '    if (!!finalParam4) {\n' +
  '      if (finalParam4 && finalParam4.data) {\n' +
  '        finalParam4 = finalParam4.data\n' +
  '      }\n' +
  '      if (typeof finalParam4 === \'number\' && finalParam4.toString().length === 13) {\n' +
  '        finalParam4 = formatToTime(finalParam4);\n' +
  '      } else if (typeof finalParam4 === \'number\' && finalParam4.toString().length !== 13) {\n' +
  '        finalParam4 = finalParam4.toString();\n' +
  '      } else if (typeof finalParam4 === \'boolean\') {\n' +
  '        finalParam4 = finalParam4 ? \'真\' : \'假\';\n' +
  '      }\n' +
  '      finalString += finalParam4\n' +
  '    }\n' +
  '    return finalString;\n' +
  '  }\n'
export {
  getRankByScore,
  setDeadZone,
  getIsNull,
  getIsArrayNull,
  handleData,
  getExponent,
  getAbsolute,
  handleTime1,
  handleTime2,
  handleTime3,
  handleTime4,
  jointString
}
