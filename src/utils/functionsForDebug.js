const DataUrl = process.env.VUE_APP_BACKEND_URL

const functionsForDebug = {
  getDataFromArray: function (param1, param2, param3) {
    let finalParam1 = param1
    let finalParam2 = param2
    let finalParam3 = param3
    if (param1 && param1.data) {
      finalParam1 = [].concat(param1.data)
    } else {
      if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
        finalParam1 = JSON.parse(param1)
      } else {
        finalParam1 = param1
      }
    }
    if (param2 && param2.data) {
      finalParam2 = param2.data
    }
    if (param3 && param3.data) {
      finalParam3 = param3.data
    }
    if (!finalParam1 || finalParam1 === 'null' || finalParam1 === '[]') {
      return undefined
    }
    let arr = finalParam1
    if (!Array.isArray(arr)) {
      return undefined
    }
    let temArr = arr[finalParam2 - 1]
    let finalData = null
    if (Array.isArray(temArr)) {
      finalData = temArr[finalParam3 - 1]
    } else {
      finalData = temArr
    }
    return finalData
  },
  anaArr: function (data, column, type) {
    if (!data) {
      return null
    }
    let finaleData = []
    let finaleColumn = column
    let finaleType = type
    if (data && data.data) {
      finaleData = [].concat(data.data)
    } else if (data.properties) {
      for (let key in data.properties) {
        finaleData.push(data.properties[key])
      }
    } else {
      if (typeof data === 'string' && typeof JSON.parse(data) === 'object') {
        finaleData = JSON.parse(data)
      } else {
        finaleData = data
      }
    }
    if (column && column.data) {
      finaleColumn = column.data
    }
    if (type && type.data) {
      finaleType = type.data
    }
    if (!finaleData || finaleData === 'null' || finaleData === '[]') {
      if (finaleType === 'length') {
        return null
      } else {
        return undefined
      }
    }
    let arr = finaleData
    if (!Array.isArray(arr) || arr.length === 0) {
      return undefined
    }
    let isOne = false, isTwo = false
    arr.map(function (n) {
      if (n && !Array.isArray(n)) {
        isOne = true
      }
      if (n && Array.isArray(n)) {
        isTwo = true
      }
    })
    if ((isOne && isTwo) || (!isOne && !isTwo)) {
      return undefined
    }
    if (isOne && finaleColumn !== 1 && finaleColumn !== 0) {
      return undefined
    }
    if (isTwo && finaleColumn > arr[0].length) {
      return undefined
    }
    let finalInt = null, temInt
    if (finaleType === 'min') {
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (finalInt) {
          if (typeof temInt === 'number' && !isNaN(temInt) && finalInt > temInt) {
            finalInt = temInt
          }
        } else {
          if (typeof temInt === 'number' && !isNaN(temInt)) {
            finalInt = temInt
          }
        }
      })
    } else if (finaleType === 'max') {
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (finalInt) {
          if (typeof temInt === 'number' && !isNaN(temInt) && finalInt < temInt) {
            finalInt = temInt
          }
        } else {
          if (typeof temInt === 'number' && !isNaN(temInt)) {
            finalInt = temInt
          }
        }
      })
    } else if (finaleType === 'ave') {
      let totalNum = 0
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (typeof temInt === 'number' && !isNaN(temInt)) {
          totalNum += 1
          if (finalInt) {
            finalInt += temInt
          } else {
            finalInt = temInt
          }
        }
      })
      if (finalInt && totalNum > 0) {
        finalInt = finalInt / totalNum
      }
    } else if (finaleType === 'abs_ave') {
      let totalNum1 = 0
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (typeof temInt === 'number' && !isNaN(temInt)) {
          totalNum1 += 1
          if (finalInt) {
            finalInt += Math.abs(temInt)
          } else {
            finalInt = Math.abs(temInt)
          }
        }
      })
      if (finalInt && totalNum1 > 0) {
        finalInt = finalInt / totalNum1
      }
    } else if (finaleType === 'total') {
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (typeof temInt === 'number' && !isNaN(temInt)) {
          if (finalInt) {
            finalInt += temInt
          } else {
            finalInt = temInt
          }
        }
      })
    } else if (finaleType === 'length') {
      let totalLength = 0
      arr.map(function (n) {
        if (isOne) {
          temInt = n
        } else {
          temInt = n[finaleColumn - 1]
        }
        if (temInt !== null && temInt !== 'null' && temInt !== undefined && temInt !== 'undefined') {
          totalLength++
        }
      })
      finalInt = totalLength
    } else {
      finalInt = null
    }
    return finalInt
  },
  sortArr: function (data, column, type) {
    function quickSort (arr, column, type) {
      let finalArr = arr
      let finalType = type
      if (arr && arr.data) {
        finalArr = [].concat(arr.data)
      }
      if (type && type.data) {
        finalType = type.data
      }
      if (finalArr.length <= 1) {
        return finalArr
      }
      let middle, temp, calMiddle
      let middleIndex = Math.floor(finalArr.length / 2)
      middle = finalArr.splice(middleIndex, 1)
      calMiddle = middle
      if (column >= 2 && Array.isArray(middle)) {
        calMiddle = middle[0][column - 1]
      }
      let left = []
      let right = []
      for(let i = 0; i < finalArr.length; i++){
        temp = finalArr[i]
        if (column >= 2 && Array.isArray(temp)) {
          temp = temp[column - 1]
        }
        if (finalType === 0) {
          if (temp < calMiddle) {
            left.push(finalArr[i])
          } else {
            right.push(finalArr[i])
          }
        } else {
          if (temp < calMiddle) {
            right.push(finalArr[i])
          } else {
            left.push(finalArr[i])
          }
        }
      }
      return quickSort(left, column, type).concat(middle, quickSort(right, column, type))
    }
    let finaleData = data
    let finaleColumn = column
    if (data && data.data) {
      finaleData = [].concat(data.data)
    } else {
      if (typeof data === 'string' && typeof JSON.parse(data) === 'object') {
        finaleData = JSON.parse(data)
      } else {
        finaleData = data
      }
    }
    if (column && column.data) {
      finaleColumn = column.data
    }
    if (!data || data === 'null' || data === '[]') {
      return JSON.stringify([])
    }
    let arr = finaleData
    if (!Array.isArray(arr) || arr.length === 0) {
      return JSON.stringify([])
    }
    let isOne = false, isTwo = false
    arr.map(function (n) {
      if (n && !Array.isArray(n)) {
        isOne = true
      }
      if (n && Array.isArray(n)) {
        isTwo = true
      }
    })
    if ((isOne && isTwo) || (!isOne && !isTwo)) {
      return JSON.stringify([])
    }
    if (isOne && finaleColumn !== 1) {
      return JSON.stringify([])
    }
    if (isTwo && (finaleColumn > arr[0].length || finaleColumn < 1)) {
      return JSON.stringify([])
    }
    return JSON.stringify(quickSort(arr, finaleColumn, type))
  },
  subArr: function (data, column, logic) {
    let finalData = data
    let finalColumn = column
    let finalLogic = logic
    if (data && data.data) {
      finalData = [].concat(data.data)
    } else {
      if (typeof data === 'string' && typeof JSON.parse(data) === 'object') {
        finalData = JSON.parse(data)
      } else {
        finalData = data
      }
    }
    if (column && column.data) {
      finalColumn = column.data
    }
    if (logic && logic.data) {
      finalLogic = logic.data
    }
    if (!finalData || finalData === 'null' || finalData === '[]') {
      return JSON.stringify([])
    }
    let arr = finalData
    if (!Array.isArray(arr) || arr.length === 0) {
      return JSON.stringify([])
    }
    let isOne = false, isTwo = false
    arr.map(function (n) {
      if (n && !Array.isArray(n)) {
        isOne = true
      }
      if (n && Array.isArray(n)) {
        isTwo = true
      }
    })
    if ((isOne && isTwo) || (!isOne && !isTwo)) {
      return JSON.stringify([])
    }
    if (isOne && finalColumn !== 1) {
      return JSON.stringify([])
    }
    if (isTwo && (finalColumn > arr[0].length || finalColumn < 1)) {
      return JSON.stringify([])
    }
    let finalArr = []
    if (isOne) {
      arr.map(function (n) {
        if (eval(finalLogic)) {
          finalArr.push(n)
        }
      })
    } else {
      let x
      arr.map(function (n) {
        x = n[finalColumn - 1]
        if (eval(finalLogic)) {
          finalArr.push(n)
        }
      })
    }
    return finalArr.length > 0 ? finalArr : JSON.stringify([])
  },
  regionArr: function (data, column, logic) {
    let finalData = data
    let finalColumn = column
    let finalLogic = logic
    if (data && data.data) {
      finalData = [].concat(data.data)
    } else {
      if (typeof data === 'string' && typeof JSON.parse(data) === 'object') {
        finalData = JSON.parse(data)
      } else {
        finalData = data
      }
    }
    if (column && column.data) {
      finalColumn = column.data
    }
    if (logic && logic.data) {
      finalLogic = logic.data
    }
    if (!finalData || finalData === 'null' || finalData === '[]') {
      return JSON.stringify([])
    }
    let arr = finalData
    if (!Array.isArray(arr) || arr.length === 0) {
      return JSON.stringify([])
    }
    let isOne = false, isTwo = false
    arr.map(function (n) {
      if (n && !Array.isArray(n)) {
        isOne = true
      }
      if (n && Array.isArray(n)) {
        isTwo = true
      }
    })
    if ((isOne && isTwo) || (!isOne && !isTwo)) {
      return JSON.stringify([])
    }
    if (isOne && finalColumn !== 1) {
      return JSON.stringify([])
    }
    if (isTwo && (finalColumn > arr[0].length || finalColumn < 1)) {
      return JSON.stringify([])
    }
    let finalArr = []
    let temArr = []
    let startIndex = null
    let endIndex = null
    let x
    arr.map(function (n, i) {
      x = n
      if (isTwo) {
        x = n[finalColumn - 1]
      }
      if (eval(finalLogic)) {
        temArr.push(i + 1)
      }
    })
    if (temArr.length > 0) {
      temArr.map(function (n, i1) {
        startIndex = n
        for (let i2 = i1 + 1; i2 < temArr.length; i2++) {
          if (n + (i2 - i1) === temArr[i2]) {
            endIndex = temArr[i2]
          } else {
            break
          }
        }
        if (endIndex && endIndex > startIndex) {
          finalArr.push([startIndex, endIndex, endIndex - startIndex + 1])
        }
      })
    }
    return finalArr.length > 0 ? finalArr : JSON.stringify([])
  },
  getLastData: function (param1, param2, param3, param4) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_new_data?type=' + finalParam1 + '&point=' + finalParam2 + '&start=' + finalParam3 + '&end=' + finalParam4)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      if (JSON.parse(request.response).data[0]) {
        finalData.push(JSON.parse(request.response).data[0])
      } else {
        finalData.push(undefined)
      }
      if (JSON.parse(request.response).data[1]) {
        finalData.push(JSON.parse(request.response).data[1])
      } else {
        finalData.push(undefined)
      }
    }
    return finalData.length === 0 ? JSON.stringify(finalData) : finalData
  },
  getLastData1: function (param1, param2, param3, param4, param5) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_new_data?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&start=' + finalParam4 + '&end=' + finalParam5
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_new_data?type=' + finalParam1 + '&point=' + finalParam2 + '&start=' + finalParam4 + '&end=' + finalParam5
    }
    let url = encodeURI(finalParamStr)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getFirstData: function (param1, param2, param3, param4) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_first_data?type=' + finalParam1 + '&point=' + finalParam2 + '&start=' + finalParam3 + '&end=' + finalParam4)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      if (JSON.parse(request.response).data[0]) {
        finalData.push(JSON.parse(request.response).data[0])
      } else {
        finalData.push(undefined)
      }
      if (JSON.parse(request.response).data[1]) {
        finalData.push(JSON.parse(request.response).data[1])
      } else {
        finalData.push(undefined)
      }
    }
    return finalData.length === 0 ? JSON.stringify(finalData) : finalData
  },
  getFirstData1: function (param1, param2, param3, param4, param5) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_first_data?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&start=' + finalParam4 + '&end=' + finalParam5
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_first_data?type=' + finalParam1 + '&point=' + finalParam2 + '&start=' + finalParam4 + '&end=' + finalParam5
    }
    let url = encodeURI(finalParamStr)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getParticularData: function (param1, param2, param3, param4, param5, param6, param7) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalParam5 = param5 && param5.data ? param5.data : param5
    let finalParam6 = param6 && param6.data ? param6.data : param6
    let finalParam7 = param7 && param7.data ? param7.data : param7
    let url = DataUrl + '/internal/rule/algorithm/function/get_data?type=' + finalParam1 + '&point=' + finalParam2 + '&start=' + finalParam3 + '&end=' + finalParam4 + '&logic=' + encodeURIComponent(finalParam5) + '&timeType=' + finalParam6 + '&valueType=' + finalParam7
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return finalData.length === 0 ? JSON.stringify(finalData) : finalData
  },
  getParticularData1: function (param1, param2, param3, param4, param5, param6, param7) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParam6 = param6 && param6.type ? param6.data : param6
    let finalParam7 = param7 && param7.type ? param7.data : param7
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_data?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&valueType=' + finalParam4 + '&logic=' + encodeURIComponent(finalParam5) + '&start=' + finalParam6 + '&end=' + finalParam7
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_data?type=' + finalParam1 + '&point=' + finalParam2 + '&valueType=' + finalParam4 + '&logic=' + encodeURIComponent(finalParam5) + '&start=' + finalParam6 + '&end=' + finalParam7
    }
    let url = finalParamStr
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getElePrice: function (param1, param2, param3) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_ele_price?type=' + finalParam1 + '&user=' + finalParam2 + '&t=' + finalParam3)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      if (JSON.parse(request.response).data.price) {
        finalData.push(JSON.parse(request.response).data.price)
      } else {
        finalData.push(undefined)
      }
      if (JSON.parse(request.response).data.type) {
        finalData.push(JSON.parse(request.response).data.type)
      } else {
        finalData.push(undefined)
      }
    }
    return finalData.length === 0 ? JSON.stringify(finalData) : finalData
  },
  getCimAttrValue: function (param1) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_cim_attr_value?attrPoint=' + finalParam1)
    let request = new XMLHttpRequest()
    let finalData = null
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return finalData
  },
  getCimDeviceTag: function (param1) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_device_tags?path=' + finalParam1)
    let request = new XMLHttpRequest()
    let finalData = null
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getTagIsExist: function (param1, param2) {
    let finalParam1 = []
    if (param1 && param1.properties) {
      for (var i = 0; i < Object.keys(param1.properties).length; i++) {
        if (param1.properties[Object.keys(param1.properties)[i]].data) {
          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data)
        }
      }
    } else {
      if (param1) {
        if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
          finalParam1 = JSON.parse(param1)
        } else {
          finalParam1 = param1
        }
      } else {
        finalParam1 = null
      }
    }
    let finalParam2 = param2
    if (param2 && param2.data) {
      finalParam2 = param2.data
    }
    let finalBool = false
    if (finalParam1 && finalParam1.length > 0) {
      for (var i = 0; i < finalParam1.length; i++) {
        if (finalParam1[i][1] === finalParam2) {
          finalBool = true
          break
        }
      }
    }
    return finalBool
  },
  getCimDeviceType: function (param1) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalType = ''
    if (finalParam1.substring(0, 2) === '2|' && finalParam1.split('|')[3] && finalParam1.split('|')[3].indexOf('_') !== -1) {
      finalType = finalParam1.split('|')[3].split('_')[0]
    }
    if (finalParam1.substring(0, 2) !== '2|' && finalParam1.split('|')[2]) {
      finalType = finalParam1.split('|')[2]
    }
    return finalType
  },

  getCimSystemGroup: function (param1) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let stationId = finalParam1.split('|')[1]
    let groupName = finalParam1.split('|')[3]
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/device/group/info/groupName/'+stationId + '/' + groupName)
    let request = new XMLHttpRequest()
    let resList = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      resList = JSON.parse(request.response).data
    }
    return resList
  },

  getCimSystemGroupName: function (param1) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let res = finalParam1.split('|')[3]
    return JSON.stringify(res)
  },

  ensureStrPrefix: function (param1, param2) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    return finalParam1.startsWith(finalParam2)
  },

  ensureStrSuffix: function (param1, param2) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    return finalParam1.endsWith(finalParam2)
  },

  concatenateCim: function (param1, param2) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    return finalParam1+'|'+finalParam2
  },

  getDataByLogicAndTime1: function (param1, param2, param3, param4, param5, param6) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalParam5 = param5 && param5.data ? param5.data : param5
    let finalParam6 = param6 && param6.data ? param6.data : param6
    let url = DataUrl + '/internal/rule/algorithm/function/get_total_time_interval?point=' + finalParam1 + '&type=' + finalParam2 + '&start=' + finalParam3 + '&direction=' + finalParam4 + '&totalTime=' + finalParam5 + '&logic=' + encodeURIComponent(finalParam6)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByLogicAndTime11: function (param1, param2, param3, param4, param5, param6, param7) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParam6 = param6 && param6.type ? param6.data : param6
    let finalParam7 = param7 && param7.type ? param7.data : param7
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_total_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&direction=' + finalParam6 + '&totalTime=' + finalParam7
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_total_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&direction=' + finalParam6 + '&totalTime=' + finalParam7
    }
    let url = finalParamStr
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByLogicAndTime2: function (param1, param2, param3, param4, param5) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalParam5 = param5 && param5.data ? param5.data : param5
    let url = DataUrl + '/internal/rule/algorithm/function/get_range_time_interval?point=' + finalParam1 + '&type=' + finalParam2 + '&start=' + finalParam3 + '&end=' + finalParam4 + '&logic=' + encodeURIComponent(finalParam5)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByLogicAndTime21: function (param1, param2, param3, param4, param5, param6) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParam6 = param6 && param6.type ? param6.data : param6
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_range_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&end=' + finalParam6
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_range_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&end=' + finalParam6
    }
    let url = finalParamStr
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByLogicAndTime3: function (param1, param2, param3, param4, param5, param6) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalParam5 = param5 && param5.data ? param5.data : param5
    let finalParam6 = param6 && param6.data ? param6.data : param6
    let url = DataUrl + '/internal/rule/algorithm/function/get_continued_time_interval?point=' + finalParam1 + '&type=' + finalParam2 + '&start=' + finalParam3 + '&end=' + finalParam4 + '&timeLogic=' + encodeURIComponent(finalParam5) + '&logic=' + encodeURIComponent(finalParam6)
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByLogicAndTime31: function (param1, param2, param3, param4, param5, param6, param7) {
    let finalParam1 = param1 && param1.type ? param1.data : param1
    let finalParam2 = param2 && param2.type ? param2.data : param2
    let finalParam3 = param3 && param3.type ? param3.data : param3
    let finalParam4 = param4 && param4.type ? param4.data : param4
    let finalParam5 = param5 && param5.type ? param5.data : param5
    let finalParam6 = param6 && param6.type ? param6.data : param6
    let finalParam7 = param7 && param7.type ? param7.data : param7
    let finalParamStr = ''
    if (finalParam3) {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_continued_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&timeType=' + finalParam3 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&end=' + finalParam6 + '&timeLogic=' + encodeURIComponent(finalParam7)
    } else {
      finalParamStr = DataUrl + '/internal/rule/algorithm/function/get_continued_time_interval?type=' + finalParam1 + '&point=' + finalParam2 + '&logic=' + encodeURIComponent(finalParam4) + '&start=' + finalParam5 + '&end=' + finalParam6 + '&timeLogic=' + encodeURIComponent(finalParam7)
    }
    let url = finalParamStr
    let request = new XMLHttpRequest()
    let finalData = []
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getDataByFixedTime: function (param1, param2, param3, param4) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && (param2.data || typeof param2.data === 'number') ? param2.data : param2
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalParam3 = []
    if (param3.properties) {
      for (let key in param3.properties) {
        finalParam3.push([param3.properties[key].properties[0].data, param3.properties[key].properties[1].data])
      }
    } else {
      if (typeof param3 === 'string' && typeof JSON.parse(param3) === 'object') {
        finalParam3 = JSON.parse(param3)
      } else {
        finalParam3 = param3
      }
    }
    let finalData = []
    let url = encodeURI(DataUrl + '/internal/rule/algorithm/function/get_time_range_data?point=' + finalParam1 + '&type=' + finalParam2 + '&timeIntervalDtoList=' + JSON.stringify(finalParam3) + '&aggLevel=' + finalParam4)
    let request = new XMLHttpRequest()
    request.open('get', url, false)
    request.send(null)
    if (request.status !== 200) {
      return null
    }
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return JSON.stringify(finalData)
  },
  getEnthalpy: function (param1, param2) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let url = encodeURI(DataUrl + '/internal/ai/algorithm/function/enthalpy_entropy_calculator?temperature=' + finalParam1 + '&pressure=' + finalParam2)
    let request = new XMLHttpRequest()
    let finalData = null
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return finalData
  },
  getRefrigerantT: function (param1, param2) {
    if (!param1 || !param2) {
      return undefined
    }
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let url = encodeURI(DataUrl + '/internal/ai/algorithm/function/refrigeration?type=' + finalParam1 + '&p=' + finalParam2)
    let request = new XMLHttpRequest()
    let finalData = null
    request.open('get', url, false)
    request.send(null)
    if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
      finalData = JSON.parse(request.response).data
    }
    return finalData
  },
  // calculateBall: function (param1, param2, param3, param4) {
  //   var finalParam1 = param1 && param1.data ? param1.data : param1;
  //   var finalParam2 = param2 && param2.data ? param2.data : param2;
  //   var finalParam3 = param3 && param3.data ? param3.data : param3;
  //   var finalParam4 = param4 && param4.data ? param4.data : param4;
  //   var url = encodeURI(this.DataUrl + '/internal/ai/algorithm/function/wet_and_dry_bulb?type=' + finalParam1 + '&p1=' + finalParam2 + '&p2=' + finalParam3 + '&p3=' + finalParam4);
  //   var request = new XMLHttpRequest();
  //   var finalData = null;
  //   request.open('get', url, false);
  //   request.send(null);
  //   if (request.response && JSON.parse(request.response) && JSON.parse(request.response).data) {
  //     finalData = JSON.parse(request.response).data;
  //   }
  //   return finalData;
  // },
  calculateBall: function (param1, param2, param3, param4) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    let finalData = null
    if (finalParam1 === 0) {
      finalData = psychrolib.GetTWetBulbFromTDewPoint(finalParam2, finalParam3, finalParam4)
    } else if (finalParam1 === 1) {
      finalData = psychrolib.GetTWetBulbFromRelHum(finalParam2, finalParam3, finalParam4)
    } else if (finalParam1 === 2) {
      finalData = psychrolib.GetRelHumFromTDewPoint(finalParam2, finalParam3, finalParam4)
    } else if (finalParam1 === 3) {
      finalData = psychrolib.GetRelHumFromTWetBulb(finalParam2, finalParam3, finalParam4)
    } else if (finalParam1 === 4) {
      finalData = psychrolib.GetTDewPointFromRelHum(finalParam2, finalParam3)
    } else if (finalParam1 === 5) {
      finalData = psychrolib.GetTDewPointFromTWetBulb(finalParam2, finalParam3, finalParam4)
    } else {
      console.log('param error!')
    }
    return finalData
  },
  jointString: function (param1, param2, param3, param4) {
    function formatToTime (timestamp) {
      let finalTimestamp = timestamp && timestamp.data ? timestamp.data : timestamp
      let date = new Date(finalTimestamp)
      let YY = date.getFullYear().toString()
      let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1).toString())
      let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate().toString()
      let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString()
      let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString()
      let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString()
      return YY + '年' + MM + '月' + DD + '日' + ' ' + hh + '时' + mm + '分' + ss + '秒'
    }
    let finalParam1 = null
    if (param1 || param1 === 0) {
      if (param1.properties) {
        finalParam1 = param1.properties
      } else {
        finalParam1 = param1
      }
    }
    let finalParam2 = null
    if (param2 || param2 === 0) {
      if (param2.properties) {
        finalParam2 = param2.properties
      } else {
        finalParam2 = param2
      }
    }
    let finalParam3 = null
    if (param3 || param3 === 0) {
      if (param3.properties) {
        finalParam3 = param3.properties
      } else {
        finalParam3 = param3
      }
    }
    let finalParam4 = null
    if (param4 || param4 === 0) {
      if (param4.properties) {
        finalParam4 = param4.properties
      } else {
        finalParam4 = param4
      }
    }
    if ((!finalParam1 && finalParam1 !== 0) || (!finalParam2 && finalParam2 !== 0)) {
      return 'null'
    }
    let finalString = ''
    if (finalParam1 && finalParam1.data) {
      finalParam1 = finalParam1.data
    }
    if (typeof finalParam1 === 'number' && finalParam1.toString().length === 13) {
      finalParam1 = formatToTime(finalParam1)
    } else if (typeof finalParam1 === 'number' && finalParam1.toString().length !== 13) {
      finalParam1 = finalParam1.toString()
    } else if (typeof finalParam1 === 'boolean') {
      finalParam1 = finalParam1 ? '真' : '假'
    }
    if (finalParam2 && finalParam2.data) {
      finalParam2 = finalParam2.data
    }
    if (typeof finalParam2 === 'number' && finalParam2.toString().length === 13) {
      finalParam2 = formatToTime(finalParam2)
    } else if (typeof finalParam2 === 'number' && finalParam2.toString().length !== 13) {
      finalParam2 = finalParam2.toString()
    } else if (typeof finalParam2 === 'boolean') {
      finalParam2 = finalParam2 ? '真' : '假'
    }
    finalString = finalParam1 + finalParam2
    if (!!finalParam3) {
      if (finalParam3 && finalParam3.data) {
        finalParam3 = finalParam3.data
      }
      if (typeof finalParam3 === 'number' && finalParam3.toString().length === 13) {
        finalParam3 = formatToTime(finalParam3)
      } else if (typeof finalParam3 === 'number' && finalParam3.toString().length !== 13) {
        finalParam3 = finalParam3.toString()
      } else if (typeof finalParam3 === 'boolean') {
        finalParam3 = finalParam3 ? '真' : '假'
      }
      finalString += finalParam3
    }
    if (!!finalParam4) {
      if (finalParam4 && finalParam4.data) {
        finalParam4 = finalParam4.data
      }
      if (typeof finalParam4 === 'number' && finalParam4.toString().length === 13) {
        finalParam4 = formatToTime(finalParam4)
      } else if (typeof finalParam4 === 'number' && finalParam4.toString().length !== 13) {
        finalParam4 = finalParam4.toString()
      } else if (typeof finalParam4 === 'boolean') {
        finalParam4 = finalParam4 ? '真' : '假'
      }
      finalString += finalParam4
    }
    return finalString
  },
  getCurrentTime: function () {
    let currentTime = new Date()
    return currentTime.getTime()
  },
  timeToTimestamp: function(time){
    console.log(time)
    let date = new Date(time)
    console.log(date)
    return date.getTime()
  },
  getTime: function (type, time) {
    function formatToTime (timestamp) {
      let finalTimestamp = timestamp && timestamp.data ? timestamp.data : timestamp
      let date = new Date(finalTimestamp)
      let YY = date.getFullYear().toString()
      let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1).toString())
      let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate().toString()
      let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString()
      let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString()
      let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString()
      return YY + MM + DD + hh + mm + ss
    }
    function formatToTimestamp (str) {
      let finalStr = str && str.data ? str.data : str
      let timestamp = finalStr.substring(0, 4) + '/' + finalStr.substring(4, 6) + '/' + finalStr.substring(6, 8) + ' ' + finalStr.substring(8, 10) + ':' + finalStr.substring(10, 12) + ':' + finalStr.substring(12, 14)
      timestamp = new Date(timestamp).getTime()
      return timestamp
    }
    let finalTime = time && time.data ? time.data : time
    let finalType = type && (type.data || type.data === 0) ? type.data : type
    if (!finalTime || finalTime.toString().length !== 13) {
      return null
    }
    let start, end
    if (finalType === 0) {
      start = formatToTimestamp(formatToTime(finalTime).substring(0, 4) + '0101000000')
      end = formatToTimestamp(formatToTime(finalTime).substring(0, 4) + '1231235959')
    } else if (finalType === 1) {
      start = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + '01000000')
      if (formatToTime(finalTime).substring(4, 6) === '01' || formatToTime(finalTime).substring(4, 6) === '03' || formatToTime(finalTime).substring(4, 6) === '05' || formatToTime(finalTime).substring(4, 6) === '07' || formatToTime(finalTime).substring(4, 6) === '08' || formatToTime(finalTime).substring(4, 6) === '10' || formatToTime(finalTime).substring(4, 6) === '12') {
        end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + '31235959')
      } else if (formatToTime(finalTime).substring(4, 6) === '02') {
        if (parseFloat(formatToTime(finalTime).substring(0, 4)) % 4 === 0) {
          end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + '29235959')
        } else {
          end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + '28235959')
        }
      } else {
        end = formatToTimestamp(formatToTime(finalTime).substring(0, 6) + '30235959')
      }
    } else if (finalType === 2) {
      start = formatToTimestamp(formatToTime(finalTime).substring(0, 8) + '000000')
      end = formatToTimestamp(formatToTime(finalTime).substring(0, 8) + '235959')
    } else if (finalType === 3) {
      start = formatToTimestamp(formatToTime(finalTime).substring(0, 10) + '0000')
      end = formatToTimestamp(formatToTime(finalTime).substring(0, 10) + '5959')
    } else {
      start = null
      end = null
    }
    return JSON.stringify([start, end])
  },
  moveTime: function (time, step, distance) {
    function formatToTime (timestamp) {
      let finalTimestamp = timestamp && timestamp.data ? timestamp.data : timestamp
      let date = new Date(finalTimestamp)
      let YY = date.getFullYear().toString()
      let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1).toString())
      let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate().toString()
      let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString()
      let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString()
      let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString()
      return YY + MM + DD + hh + mm + ss
    }
    function formatToTimestamp (str) {
      let finalStr = str && str.data ? str.data : str
      let timestamp = finalStr.substring(0, 4) + '/' + finalStr.substring(4, 6) + '/' + finalStr.substring(6, 8) + ' ' + finalStr.substring(8, 10) + ':' + finalStr.substring(10, 12) + ':' + finalStr.substring(12, 14)
      timestamp = new Date(timestamp).getTime()
      return timestamp
    }
    let finalTime = time && time.data ? time.data : time
    let finalStep = step && step.data ? step.data : step
    let finalDistance = distance && distance.data ? distance.data : distance
    if (!finalTime || finalTime.toString().length !== 13) {
      return null
    }
    let finalTimestamp
    if (finalStep === 'sec') {
      finalTimestamp = finalTime + finalDistance * 1000
    } else if (finalStep === 'min') {
      finalTimestamp = finalTime + finalDistance * 60 * 1000
    } else if (finalStep === 'hour') {
      finalTimestamp = finalTime + finalDistance * 60 * 60 * 1000
    } else if (finalStep === 'day') {
      finalTimestamp = finalTime + finalDistance * 24 * 60 * 60 * 1000
    } else if (finalStep === 'week') {
      finalTimestamp = finalTime + finalDistance * 7 * 24 * 60 * 60 * 1000
    } else if (finalStep === 'month') {
      finalTimestamp = finalTime + finalDistance * 31 * 24 * 60 * 60 * 1000
    } else if (finalStep === 'year') {
      let initStr = formatToTime(finalTime)
      let finalStr = (parseFloat(initStr.substring(0, 4)) + finalDistance).toString() + initStr.substring(4, 14)
      finalTimestamp = formatToTimestamp(finalStr)
    } else {
      finalTimestamp = null
    }
    return finalTimestamp
  },
  getTimePart: function (time, type) {
    function formatToTime (timestamp) {
      let finalTimestamp = timestamp && timestamp.data ? timestamp.data : timestamp
      let date = new Date(finalTimestamp)
      let YY = date.getFullYear().toString()
      let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1).toString())
      let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate().toString()
      let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours().toString()
      let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes().toString()
      let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds().toString()
      return YY + MM + DD + hh + mm + ss
    }
    let finalTime = time && time.data ? time.data : time
    let finalType = type && type.data ? type.data : type
    if (!finalTime || finalTime.toString().length !== 13) {
      return null
    }
    let finalStr
    if (finalType === 'year') {
      finalStr = formatToTime(finalTime).substring(0, 4)
    } else if (finalType === 'month') {
      finalStr = formatToTime(finalTime).substring(4, 6)
    } else if (finalType === 'day') {
      finalStr = formatToTime(finalTime).substring(6, 8)
    } else if (finalType === 'hour') {
      finalStr = formatToTime(finalTime).substring(8, 10)
    } else {
      finalStr = null
    }
    return finalStr
  },
  getRankByScore: function (score, lower, upper, degree) {
    let finalScore = score && score.data ? score.data : score
    let finalLower = []
    let finalUpper = []
    let finalDegree = []
    for (var i = 0; i < Object.keys(lower.properties).length; i++) {
      finalLower.push(lower.properties[Object.keys(lower.properties)[i]].data)
    }
    for (var i = 0; i < Object.keys(upper.properties).length; i++) {
      finalUpper.push(upper.properties[Object.keys(upper.properties)[i]].data)
    }
    for (var i = 0; i < Object.keys(degree.properties).length; i++) {
      finalDegree.push(degree.properties[Object.keys(degree.properties)[i]].data)
    }
    if (finalLower.length !== finalUpper.length || finalLower.length !== finalDegree.length) {
      return 'null'
    }
    let temStr = '无等级'
    let temObj = {}
    let temArr = []
    let arrLength = finalLower.length
    for (var i = 0; i < arrLength; i++) {
      temObj.range = [finalLower[i], finalUpper[i]]
      temObj.label = finalDegree[i]
      temArr.push(temObj)
      temObj = {}
    }
    for (var i = 0; i < temArr.length; i++) {
      if (finalScore >= temArr[i].range[0] && finalScore < temArr[i].range[1]) {
        temStr = temArr[i].label
        break
      }
    }
    return temStr
  },
  setDeadZone: function (score, lower, upper) {
    let finalScore = score && score.data ? score.data : score
    let finalLower = lower && lower.data ? lower.data : lower
    let finalUpper = upper && upper.data ? upper.data : upper
    let temScore = 0
    if (finalScore >= finalLower && finalScore <= finalUpper) {
      temScore = finalScore
    } else if (finalScore > finalUpper) {
      temScore = finalUpper
    } else if (finalScore < finalLower) {
      temScore = finalLower
    }
    return temScore
  },
  getIsNull: function (param) {
    if (param === 0) {
      return false
    }
    if (!param) {
      return true
    }
    let finalParam = param && (param.data || param.data === '') ? param.data : param
    let temResult = false
    if (typeof finalParam === 'string' && typeof JSON.parse(finalParam) === 'object') {
      finalParam = JSON.parse(finalParam)
    }
    if (Array.isArray(finalParam)) {
      let allIsNull = true
      for (let i = 0; i < finalParam.length; i++) {
        if (finalParam[i][1] !== null) {
          allIsNull = false
          break
        }
      }
      return allIsNull
    } else {
      if (finalParam === null || (typeof finalParam !== 'number' && finalParam.length === 0) || finalParam === '[]' || finalParam === '\'\'' || finalParam === 'undefined') {
        temResult = true
      }
      return temResult
    }
  },
  getIsArrayNull: function (param1, param2) {
    if (!param1) {
      return true
    }
    let finalParam1 = param1 && (param1.data || param1.data === '') ? param1.data : param1
    let finalParam2 = param2 && (param2.data || param2.data === '') ? param2.data : param2
    if (typeof finalParam1 === 'string' && typeof JSON.parse(finalParam1) === 'object') {
      finalParam1 = JSON.parse(finalParam1)
    }
    if (Array.isArray(finalParam1) && !!finalParam2) {
      let allIsNull = true
      for (let i = 0; i < finalParam1.length; i++) {
        if (finalParam1[i][parseInt(finalParam2)] !== null) {
          allIsNull = false
          break
        }
      }
      return allIsNull
    } else {
      let temResult = false
      if (finalParam1 === null || (typeof finalParam1 !== 'number' && finalParam1.length === 0) || finalParam1 === '[]' || finalParam1 === '\'\'' || finalParam1 === 'undefined') {
        temResult = true
      }
      return temResult
    }
  },
  handleData: function (param1, param2, param3) {
    if (!param1) {
      return undefined
    }
    if (param2 === undefined || param3 === undefined) {
      return undefined
    }
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && (param2.data || typeof param2.data === 'number') ? param2.data : param2
    let finalParam3 = param3 && (param3.data || typeof param3.data === 'number') ? param3.data : param3
    if (finalParam1.toString().split('.')[1] !== undefined) {
      if (finalParam1.toString().split('.')[1].length <= finalParam2) {
        return finalParam1
      }
    } else {
      if (0 <= finalParam2) {
        return finalParam1
      }
    }
    let temResult = finalParam1
    if (finalParam3 === 0 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 0)) {
      temResult = Math.floor(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))
    } else if (finalParam3 === 1 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 1)) {
      temResult = Math.ceil(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))
    } else if (finalParam3 === 2 || (parseFloat(finalParam3) && parseFloat(finalParam3) === 2)) {
      temResult = Math.round(parseFloat(finalParam1) * Math.pow(10, parseFloat(finalParam2))) / Math.pow(10, parseFloat(finalParam2))
    }
    return temResult
  },
  getExponent: function (param1, param2) {
    let finalParam1 = param1 && param1.data ? param1.data : param1
    let finalParam2 = param2 && param2.data ? param2.data : param2
    return Math.pow(finalParam1, finalParam2)
  },
  getAbsolute: function (param) {
    let finalParam = param && param.data ? param.data : param
    return Math.abs(finalParam)
  },
  handleTime1: function (param1, param2, param3, param4) {
    let finalParam1 = []
    if (param1.properties) {
      for (var i = 0; i < Object.keys(param1.properties).length; i++) {
        if (param1.properties[Object.keys(param1.properties)[i]].data) {
          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data)
        }
      }
    } else {
      if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
        finalParam1 = JSON.parse(param1)
      } else {
        finalParam1 = param1
      }
    }
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    let finalParam4 = param4 && param4.data ? param4.data : param4
    if (finalParam2 !== '是' && finalParam2 !== '否') {
      return 'null'
    }
    if ((finalParam3.indexOf('+') === -1 && finalParam3.indexOf('-') === -1) || (finalParam4.indexOf('+') === -1 && finalParam4.indexOf('-') === -1)) {
      return 'null'
    }
    if ((finalParam3.indexOf('+') !== -1 && finalParam4.indexOf('+') === -1) || (finalParam3.indexOf('-') !== -1 && finalParam4.indexOf('-') === -1)) {
      return 'null'
    }
    if (finalParam3.indexOf('+') !== -1 && finalParam4.indexOf('+') !== -1) {
      if (parseFloat(finalParam3.split('+')[1]) > parseFloat(finalParam4.split('+')[1])) {
        return 'null'
      }
    }
    if (finalParam3.indexOf('-') !== -1 && finalParam4.indexOf('-') !== -1) {
      if (parseFloat(finalParam3.split('-')[1]) < parseFloat(finalParam4.split('-')[1])) {
        return 'null'
      }
    }
    let temArr = []
    let num1, num2
    if (finalParam3.indexOf('+') !== -1) {
      if (finalParam2 === '否') {
        for (var i = 0; i < finalParam1.length; i++) {
          num1 = finalParam1[i][0] + parseFloat(finalParam3.split('+')[1]) * 60 * 1000
          num2 = finalParam1[i][0] + parseFloat(finalParam4.split('+')[1]) * 60 * 1000
          if (num1 >= finalParam1[i][1]) {
            temArr.push([finalParam1[i][1], finalParam1[i][1]])
          } else if (num1 < finalParam1[i][1] && num2 >= finalParam1[i][1]) {
            temArr.push([num1, finalParam1[i][1]])
          } else if (num1 < finalParam1[i][1] && num2 < finalParam1[i][1]) {
            temArr.push([num1, num2])
          } else {
            temArr.push([])
          }
        }
      } else {
        for (var i = 0; i < finalParam1.length; i++) {
          num1 = finalParam1[i][0] + parseFloat(finalParam3.split('+')[1]) * 60 * 1000
          num2 = finalParam1[i][0] + parseFloat(finalParam4.split('+')[1]) * 60 * 1000
          temArr.push([num1, num2])
        }
      }
    } else {
      if (finalParam2 === '否') {
        for (var i = 0; i < finalParam1.length; i++) {
          num1 = finalParam1[i][1] - parseFloat(finalParam3.split('-')[1]) * 60 * 1000
          num2 = finalParam1[i][1] - parseFloat(finalParam4.split('-')[1]) * 60 * 1000
          if (num2 <= finalParam1[i][0]) {
            temArr.push([finalParam1[i][0], finalParam1[i][0]])
          } else if (num2 > finalParam1[i][0] && num1 <= finalParam1[i][0]) {
            temArr.push([finalParam1[i][0], num2])
          } else if (num1 > finalParam1[i][0] && num2 > finalParam1[i][0]) {
            temArr.push([num1, num2])
          } else {
            temArr.push([])
          }
        }
      } else {
        for (var i = 0; i < finalParam1.length; i++) {
          num1 = finalParam1[i][1] - parseFloat(finalParam3.split('-')[1]) * 60 * 1000
          num2 = finalParam1[i][1] - parseFloat(finalParam4.split('-')[1]) * 60 * 1000
          temArr.push([num1, num2])
        }
      }
    }
    return JSON.stringify(temArr)
  },
  handleTime2: function (param1, param2, param3) {
    let finalParam1 = []
    if (param1.properties) {
      for (var i = 0; i < Object.keys(param1.properties).length; i++) {
        if (param1.properties[Object.keys(param1.properties)[i]].data) {
          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data)
        }
      }
    } else {
      if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
        finalParam1 = JSON.parse(param1)
      } else {
        finalParam1 = param1
      }
    }
    let finalParam2 = param2 && param2.data ? param2.data : param2
    let finalParam3 = param3 && param3.data ? param3.data : param3
    if ((finalParam2.indexOf('-') === -1 && finalParam2.indexOf('+') === -1) || (finalParam3.indexOf('-') === -1 && finalParam3.indexOf('+') === -1)) {
      return 'null'
    }
    let temArr = []
    let num1, num2
    if (finalParam1 instanceof Array) {
      for (var i = 0; i < finalParam1.length; i++) {
        if (typeof parseFloat(finalParam1[i]) === 'number') {
          num1 = parseFloat(finalParam1[i]) + parseFloat(finalParam2) * 60 * 1000
          num2 = parseFloat(finalParam1[i]) + parseFloat(finalParam3) * 60 * 1000
          temArr.push([num1, num2])
        } else {
          temArr.push([])
        }
      }
    } else {
      if (typeof parseFloat(finalParam1) === 'number') {
        num1 = parseFloat(finalParam1) + parseFloat(finalParam2) * 60 * 1000
        num2 = parseFloat(finalParam1) + parseFloat(finalParam3) * 60 * 1000
        temArr.push([num1, num2])
      } else {
        temArr.push([])
      }
    }
    return JSON.stringify(temArr)
  },
  handleTime3: function (param1, param2, param3, param4) {
    let finalParam1 = []
    if (param1 && param1.properties) {
      for (var i = 0; i < Object.keys(param1.properties).length; i++) {
        if (param1.properties[Object.keys(param1.properties)[i]].data) {
          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data)
        }
      }
    } else {
      if (param1) {
        if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
          finalParam1 = JSON.parse(param1)
        } else {
          finalParam1 = param1
        }
      } else {
        finalParam1 = null
      }
    }
    let finalParam2 = []
    if (param2 && param2.properties) {
      for (var i = 0; i < Object.keys(param2.properties).length; i++) {
        if (param2.properties[Object.keys(param2.properties)[i]].data) {
          finalParam2.push(param2.properties[Object.keys(param2.properties)[i]].data)
        }
      }
    } else {
      if (param2) {
        if (typeof param2 === 'string' && typeof JSON.parse(param2) === 'object') {
          finalParam2 = JSON.parse(param2)
        } else {
          finalParam2 = param2
        }
      } else {
        finalParam2 = null
      }
    }
    let finalParam3 = []
    if (param3 && param3.properties) {
      for (var i = 0; i < Object.keys(param3.properties).length; i++) {
        if (param3.properties[Object.keys(param3.properties)[i]].data) {
          finalParam3.push(param3.properties[Object.keys(param3.properties)[i]].data)
        }
      }
    } else {
      if (param3) {
        if (typeof param3 === 'string' && typeof JSON.parse(param3) === 'object') {
          finalParam3 = JSON.parse(param3)
        } else {
          finalParam3 = param3
        }
      } else {
        finalParam3 = null
      }
    }
    let finalParam4 = []
    if (param4 && param4.properties) {
      for (var i = 0; i < Object.keys(param4.properties).length; i++) {
        if (param4.properties[Object.keys(param4.properties)[i]].data) {
          finalParam4.push(param4.properties[Object.keys(param4.properties)[i]].data)
        }
      }
    } else {
      if (param4) {
        if (typeof param4 === 'string' && typeof JSON.parse(param4) === 'object') {
          finalParam4 = JSON.parse(param4)
        } else {
          finalParam4 = param4
        }
      } else {
        finalParam4 = null
      }
    }
    if (!finalParam1 || !finalParam2) {
      return 'null'
    }
    let temArr1 = []
    let temArr2 = []
    let temArr3 = []
    let finalArr = []
    let isExist = false
    for (var i = 0; i < finalParam1.length; i++) {
      for (var m = 0; m < finalParam2.length; m++) {
        isExist = false
        if (finalParam2[m][0] === finalParam1[i][0] && finalParam2[m][1] === finalParam1[i][1]) {
          for (let n = 0; n < temArr1.length; n++) {
            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {
              isExist = true
              break
            }
          }
          if (!isExist) {
            temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)])
          }
        } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1])) {
          for (let n = 0; n < temArr1.length; n++) {
            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {
              isExist = true
              break
            }
          }
          if (!isExist) {
            temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)])
          }
        } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1])) {
          for (let n = 0; n < temArr1.length; n++) {
            if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {
              isExist = true
              break
            }
          }
          if (!isExist) {
            temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)])
          }
        } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1] && finalParam2[m][0] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1] && finalParam2[m][0] < finalParam1[i][1])) {
          for (let n = 0; n < temArr1.length; n++) {
            if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam1[i][1]) {
              isExist = true
              break
            }
          }
          if (!isExist) {
            temArr1.push([finalParam2[m][0], finalParam1[i][1], (finalParam1[i][1] - finalParam2[m][0]) / (1000 * 60)])
          }
        } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1] && finalParam2[m][1] > finalParam1[i][0]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1] && finalParam2[m][1] > finalParam1[i][0])) {
          for (let n = 0; n < temArr1.length; n++) {
            if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam2[m][1]) {
              isExist = true
              break
            }
          }
          if (!isExist) {
            temArr1.push([finalParam1[i][0], finalParam2[m][1], (finalParam2[m][1] - finalParam1[i][0]) / (1000 * 60)])
          }
        }
      }
    }
    finalArr = temArr1
    if (temArr1.length > 0 && finalParam3) {
      for (var i = 0; i < temArr1.length; i++) {
        for (var m = 0; m < finalParam3.length; m++) {
          isExist = false
          if (finalParam3[m][0] === temArr1[i][0] && finalParam3[m][1] === temArr1[i][1]) {
            for (let n = 0; n < temArr2.length; n++) {
              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1])) {
            for (let n = 0; n < temArr2.length; n++) {
              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1])) {
            for (let n = 0; n < temArr2.length; n++) {
              if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)])
            }
          } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1] && finalParam3[m][0] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1] && finalParam3[m][0] < temArr1[i][1])) {
            for (let n = 0; n < temArr2.length; n++) {
              if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === temArr1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr2.push([finalParam3[m][0], temArr1[i][1], (temArr1[i][1] - finalParam3[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1] && finalParam3[m][1] > temArr1[i][0]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1] && finalParam3[m][1] > temArr1[i][0])) {
            for (let n = 0; n < temArr2.length; n++) {
              if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === finalParam3[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr2.push([temArr1[i][0], finalParam3[m][1], (finalParam3[m][1] - temArr1[i][0]) / (1000 * 60)])
            }
          }
        }
      }
      finalArr = temArr2
    }
    if (temArr2.length > 0 && finalParam4) {
      for (var i = 0; i < temArr2.length; i++) {
        for (var m = 0; m < finalParam4.length; m++) {
          isExist = false
          if (finalParam4[m][0] === temArr2[i][0] && finalParam4[m][1] === temArr2[i][1]) {
            for (let n = 0; n < temArr3.length; n++) {
              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1])) {
            for (let n = 0; n < temArr3.length; n++) {
              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1])) {
            for (let n = 0; n < temArr3.length; n++) {
              if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)])
            }
          } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1] && finalParam4[m][0] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1] && finalParam4[m][0] < temArr2[i][1])) {
            for (let n = 0; n < temArr3.length; n++) {
              if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === temArr2[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr3.push([finalParam4[m][0], temArr2[i][1], (temArr2[i][1] - finalParam4[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1] && finalParam4[m][1] > temArr2[i][0]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1] && finalParam4[m][1] > temArr2[i][0])) {
            for (let n = 0; n < temArr3.length; n++) {
              if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === finalParam4[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr3.push([temArr2[i][0], finalParam4[m][1], (finalParam4[m][1] - temArr2[i][0]) / (1000 * 60)])
            }
          }
        }
      }
      finalArr = temArr3
    }
    return JSON.stringify(finalArr)
  },
  handleTime4: function (param1, param2, param3, param4) {
    let finalParam1 = []
    if (param1 && param1.properties) {
      for (var i = 0; i < Object.keys(param1.properties).length; i++) {
        if (param1.properties[Object.keys(param1.properties)[i]].data) {
          finalParam1.push(param1.properties[Object.keys(param1.properties)[i]].data)
        }
      }
    } else {
      if (param1) {
        if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
          finalParam1 = JSON.parse(param1)
        } else {
          finalParam1 = param1
        }
      } else {
        finalParam1 = null
      }
    }
    let finalParam2 = []
    if (param2 && param2.properties) {
      for (var i = 0; i < Object.keys(param2.properties).length; i++) {
        if (param2.properties[Object.keys(param2.properties)[i]].data) {
          finalParam2.push(param2.properties[Object.keys(param2.properties)[i]].data)
        }
      }
    } else {
      if (param2) {
        if (typeof param2 === 'string' && typeof JSON.parse(param2) === 'object') {
          finalParam2 = JSON.parse(param2)
        } else {
          finalParam2 = param2
        }
      } else {
        finalParam2 = null
      }
    }
    let finalParam3 = []
    if (param3 && param3.properties) {
      for (var i = 0; i < Object.keys(param3.properties).length; i++) {
        if (param3.properties[Object.keys(param3.properties)[i]].data) {
          finalParam3.push(param3.properties[Object.keys(param3.properties)[i]].data)
        }
      }
    } else {
      if (param3) {
        if (typeof param3 === 'string' && typeof JSON.parse(param3) === 'object') {
          finalParam3 = JSON.parse(param3)
        } else {
          finalParam3 = param3
        }
      } else {
        finalParam3 = null
      }
    }
    let finalParam4 = []
    if (param4 && param4.properties) {
      for (var i = 0; i < Object.keys(param4.properties).length; i++) {
        if (param4.properties[Object.keys(param4.properties)[i]].data) {
          finalParam4.push(param4.properties[Object.keys(param4.properties)[i]].data)
        }
      }
    } else {
      if (param4) {
        if (typeof param4 === 'string' && typeof JSON.parse(param4) === 'object') {
          finalParam4 = JSON.parse(param4)
        } else {
          finalParam4 = param4
        }
      } else {
        finalParam4 = null
      }
    }
    if (finalParam1 && !finalParam2) {
      return finalParam1
    } else if (!finalParam1 && finalParam2) {
      return finalParam2
    } else if (!finalParam1 && !finalParam2) {
      return null
    }
    let temArr1 = []
    let temArr2 = []
    let temArr3 = []
    let finalArr = []
    let isExist = false
    if (finalParam1.length > 0 && finalParam2.length > 0) {
      for (var i = 0; i < finalParam1.length; i++) {
        for (var m = 0; m < finalParam2.length; m++) {
          isExist = false
          if (finalParam2[m][0] === finalParam1[i][0] && finalParam2[m][1] === finalParam1[i][1]) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)])
            }
          } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1])) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)])
            }
          } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1])) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)])
            }
          } else if ((finalParam2[m][0] >= finalParam1[i][0] && finalParam2[m][1] > finalParam1[i][1] && finalParam2[m][0] <= finalParam1[i][1]) || (finalParam2[m][0] > finalParam1[i][0] && finalParam2[m][1] >= finalParam1[i][1] && finalParam2[m][0] <= finalParam1[i][1])) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam2[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam1[i][0], finalParam2[m][1], (finalParam2[m][1] - finalParam1[i][0]) / (1000 * 60)])
            }
          } else if ((finalParam2[m][0] <= finalParam1[i][0] && finalParam2[m][1] < finalParam1[i][1] && finalParam2[m][1] >= finalParam1[i][0]) || (finalParam2[m][0] < finalParam1[i][0] && finalParam2[m][1] <= finalParam1[i][1] && finalParam2[m][1] >= finalParam1[i][0])) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam2[m][0], finalParam1[i][1], (finalParam1[i][1] - finalParam2[m][0]) / (1000 * 60)])
            }
          } else if (finalParam2[m][1] < finalParam1[i][0] || finalParam2[m][0] > finalParam1[i][1]) {
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam2[m][0] && temArr1[n][1] === finalParam2[m][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam2[m][0], finalParam2[m][1], (finalParam2[m][1] - finalParam2[m][0]) / (1000 * 60)])
            }
            isExist = false
            for (let n = 0; n < temArr1.length; n++) {
              if (temArr1[n][0] === finalParam1[i][0] && temArr1[n][1] === finalParam1[i][1]) {
                isExist = true
                break
              }
            }
            if (!isExist) {
              temArr1.push([finalParam1[i][0], finalParam1[i][1], (finalParam1[i][1] - finalParam1[i][0]) / (1000 * 60)])
            }
          }
        }
      }
    } else if (finalParam1.length > 0 && finalParam2.length === 0) {
      temArr1 = finalParam1
    } else if (finalParam1.length === 0 && finalParam2.length > 0) {
      temArr1 = finalParam2
    }
    finalArr = temArr1
    if (finalParam3) {
      if (temArr1.length > 0 && finalParam3.length > 0) {
        for (var i = 0; i < temArr1.length; i++) {
          for (var m = 0; m < finalParam3.length; m++) {
            isExist = false
            if (finalParam3[m][0] === temArr1[i][0] && finalParam3[m][1] === temArr1[i][1]) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1])) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1])) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)])
              }
            } else if ((finalParam3[m][0] >= temArr1[i][0] && finalParam3[m][1] > temArr1[i][1] && finalParam3[m][0] <= temArr1[i][1]) || (finalParam3[m][0] > temArr1[i][0] && finalParam3[m][1] >= temArr1[i][1] && finalParam3[m][0] <= temArr1[i][1])) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === finalParam3[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([temArr1[i][0], finalParam3[m][1], (finalParam3[m][1] - temArr1[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam3[m][0] <= temArr1[i][0] && finalParam3[m][1] < temArr1[i][1] && finalParam3[m][1] >= temArr1[i][0]) || (finalParam3[m][0] < temArr1[i][0] && finalParam3[m][1] <= temArr1[i][1] && finalParam3[m][1] >= temArr1[i][0])) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === temArr1[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([finalParam3[m][0], temArr1[i][1], (temArr1[i][1] - finalParam3[m][0]) / (1000 * 60)])
              }
            } else if (finalParam3[m][1] < temArr1[i][0] || finalParam3[m][0] > temArr1[i][1]) {
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === finalParam3[m][0] && temArr2[n][1] === finalParam3[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([finalParam3[m][0], finalParam3[m][1], (finalParam3[m][1] - finalParam3[m][0]) / (1000 * 60)])
              }
              isExist = false
              for (let n = 0; n < temArr2.length; n++) {
                if (temArr2[n][0] === temArr1[i][0] && temArr2[n][1] === temArr1[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr2.push([temArr1[i][0], temArr1[i][1], (temArr1[i][1] - temArr1[i][0]) / (1000 * 60)])
              }
            }
          }
        }
      } else if (temArr1.length > 0 && finalParam3.length === 0) {
        temArr2 = temArr1
      } else if (temArr1.length === 0 && finalParam3.length > 0) {
        temArr2 = finalParam3
      }
      finalArr = temArr2
    }
    if (finalParam4) {
      if (temArr2.length > 0 && finalParam4.length > 0) {
        for (var i = 0; i < temArr2.length; i++) {
          for (var m = 0; m < finalParam4.length; m++) {
            isExist = false
            if (finalParam4[m][0] === temArr2[i][0] && finalParam4[m][1] === temArr2[i][1]) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1])) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1])) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)])
              }
            } else if ((finalParam4[m][0] >= temArr2[i][0] && finalParam4[m][1] > temArr2[i][1] && finalParam4[m][0] <= temArr2[i][1]) || (finalParam4[m][0] > temArr2[i][0] && finalParam4[m][1] >= temArr2[i][1] && finalParam4[m][0] <= temArr2[i][1])) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === finalParam4[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([temArr2[i][0], finalParam4[m][1], (finalParam4[m][1] - temArr2[i][0]) / (1000 * 60)])
              }
            } else if ((finalParam4[m][0] <= temArr2[i][0] && finalParam4[m][1] < temArr2[i][1] && finalParam4[m][1] >= temArr2[i][0]) || (finalParam4[m][0] < temArr2[i][0] && finalParam4[m][1] <= temArr2[i][1] && finalParam4[m][1] >= temArr2[i][0])) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === temArr2[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([finalParam4[m][0], temArr2[i][1], (temArr2[i][1] - finalParam4[m][0]) / (1000 * 60)])
              }
            } else if (finalParam4[m][1] < temArr2[i][0] || finalParam4[m][0] > temArr2[i][1]) {
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === finalParam4[m][0] && temArr3[n][1] === finalParam4[m][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([finalParam4[m][0], finalParam4[m][1], (finalParam4[m][1] - finalParam4[m][0]) / (1000 * 60)])
              }
              isExist = false
              for (let n = 0; n < temArr3.length; n++) {
                if (temArr3[n][0] === temArr2[i][0] && temArr3[n][1] === temArr2[i][1]) {
                  isExist = true
                  break
                }
              }
              if (!isExist) {
                temArr3.push([temArr2[i][0], temArr2[i][1], (temArr2[i][1] - temArr2[i][0]) / (1000 * 60)])
              }
            }
          }
        }
      } else if (temArr2.length > 0 && finalParam4.length === 0) {
        temArr3 = temArr2
      } else if (temArr2.length === 0 && finalParam4.length > 0) {
        temArr3 = finalParam4
      }
      finalArr = temArr3
    }
    return JSON.stringify(finalArr)
  },
  addDataByTime: function (param1, param2, param3, param4) {
    let finalParam1 = param1
    let finalParam2 = param2
    let finalParam3 = param3
    let finalParam4 = param4
    if (param1 && param1.data) {
      finalParam1 = [].concat(param1.data)
    } else {
      if (typeof param1 === 'string' && typeof JSON.parse(param1) === 'object') {
        finalParam1 = JSON.parse(param1)
      } else {
        finalParam1 = param1
      }
    }
    if (param2 && param2.data) {
      finalParam2 = param2.data
    }
    if (param3 && param3.data) {
      finalParam3 = param3.data
    }
    if (param4 && param4.data) {
      finalParam4 = param4.data
    }
    if (!finalParam1 || finalParam1 === 'null' || finalParam1 === '[]') {
      return JSON.stringify([])
    }
    if (!Array.isArray(finalParam1) || finalParam1.length === 0) {
      return JSON.stringify([])
    }
    let finalArr = []
    let temArr = []
    let gap = null
    if (finalParam4 === 'day') {
      gap = 86400000
    } else if (finalParam4 === 'hour') {
      gap = 3600000
    } else if (finalParam4 === 'min') {
      gap = 60000
    }
    for (let temInt = finalParam2; temInt <= finalParam3; temInt += gap) {
      temArr[0] = temInt
      temArr[1] = null
      finalArr.push(temArr)
      temArr = []
    }
    for (let i = 0; i < finalParam1.length; i++) {
      for (let m = 0; m < finalArr.length; m++) {
        if (finalParam1[i][0] < finalArr[0][0]) {
          break
        }
        if (finalParam1[i][0] >= finalArr[finalArr.length - 1][0]) {
          if (!finalArr[finalArr.length - 1][1]) {
            finalArr[finalArr.length - 1][1] = finalParam1[i][1]
          }
          break
        }
        if (finalParam1[i][0] >= finalArr[m][0] && finalParam1[i][0] <= finalArr[m + 1][0]) {
          if (!finalArr[m][1]) {
            finalArr[m][1] = finalParam1[i][1]
          }
          break
        }
      }
    }
    return JSON.stringify(finalArr)
  }
}

export {
  functionsForDebug
}
