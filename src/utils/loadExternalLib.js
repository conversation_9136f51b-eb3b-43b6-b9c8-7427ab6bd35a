import 'systemjs/dist/system'
import 'systemjs/dist/extras/amd'
import 'systemjs/dist/extras/named-register'

export async function loadExternalLib () {
  if (!window.Vue) {
    await window.System.import('vue').then(res => {
      window.Vue = res.default
    })
  }
  if (!window.VueRouter) {
    await window.System.import('vue-router').then(res => {
      window.VueRouter = res.default
    })
  }
  window.Vue.use(window.VueRouter)
  if (!window.ELEMENT) {
    await window.System.import('element-ui').then(res => {
      window.ELEMENT = res.default
    })
  }
}
