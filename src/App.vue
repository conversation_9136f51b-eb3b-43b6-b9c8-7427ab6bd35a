<template lang="pug">
#app(:class='{ iframeApp: isHideIframe,screeFull:$store.state.planGenerate.isFullScreen }')
  #dashboard-mask.dashboard-mask
  <!-- 修改处：根据路由参数 authCode 决定是否显示 GlobalHeader -->
  GlobalHeader(v-if="!isHideIframe")
  // 修改处：根据 isHideIframe 动态设置 .app-main 的高度
  .app-main(
    v-if="$route.path!=='/'",
    :style="{ height: isHideIframe ? '100%' : 'calc(100% - 48px)' }"
  )
    .app-menu(:class='{ collapsed: isHideNavigator }')
      GlobalNavigatorNew
    .app-main-wrapper(
      :class='{ collapsed: isHideNavigator }',
      :style="{ marginLeft: !isHideIframe ? '64px' : '',width: !isHideIframe ? 'calc(100% - 64px)' : '100%' }"
    )
      GlobalBreadcrumb(v-if="($route.path != '/planGenerate/runTask' || $route.query.backStatus) && !getJujiaoStatus.status")
      router-view.router-view
</template>

<script>
import GlobalHeader from './components/GlobalHeader'
import GlobalNavigatorNew from './components/GlobalNavigatorNew'
import GlobalBreadcrumb from './components/GlobalBreadcrumb'
import { mapGetters, mapActions, mapState } from 'vuex'

export default {
  name: 'App',
  components: {
    GlobalBreadcrumb,
    GlobalNavigatorNew,
    GlobalHeader
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      spaceId: '',
      isRouterAlive: true,
      timeout: null
    }
  },
  computed: {
    ...mapGetters({
      globalNavigatorStatus: 'common/globalNavigatorStatusGetter',
      isMenuCollapse: 'common/getMenuCollapseGetter',
      getJujiaoStatus: 'common/getJujiaoStatus',
      getIsSuperAdmin: 'workSpace/getIsSuperAdmin'
    }),
    isHideNavigator() {
      return (
        !!(this.$route.meta && this.$route.meta.hideNavigator) ||
        !!this.$store.state.planGenerate.isIframeHide
      )
    },
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },

    isShowRouterView() {
      return (
        this.spaceId ||
        ['/guest', '/workSpace/apply', '/workSpace/apply/history'].includes(this.$route.path)
      )
    }
  },
  watch: {
    '$route.query.workspaceName': {
      immediate: true,
      handler() {
        const workspaceName = this.$route.query.workspaceName
        if (workspaceName) {
          console.log('空间更新时埋点全局属性更新--',this.$route)
          const userInfo = sessionStorage.getItem('USER_INFO')
            ? JSON.parse(sessionStorage.getItem('USER_INFO'))
            : Vue.prototype.userInfo
            ? Vue.prototype.userInfo
            : {}
          const queryTenantId = userInfo?.tenantId
          const storageTenantId = sessionStorage.getItem('authTenantId')
          let loadTenantId = null
          if (queryTenantId && queryTenantId !== storageTenantId) {
            // URL带了参数缓存里没有或者不同
            loadTenantId = queryTenantId
          } else {
            loadTenantId = storageTenantId
          }
          const queryItcode = userInfo.userName
          const storageItcode = sessionStorage.getItem('loadItcode')
          let loadItcode = null
          if (queryItcode && queryItcode !== storageItcode) {
            // URL带了参数缓存里没有或者不同
            loadItcode = queryItcode
          } else {
            loadItcode = storageItcode
          }
        }
        // this.updateLinkKongNav()
        this.updateLinkKongNav()
        // console.log("this.$route.path == '/abilityCenter/talkFull'",this.$route.path == '/abilityCenter/talkFull')
        // this.$route.path == '/abilityCenter/talkFull' ? '' : this.updateLinkKongNav();
      }
    }
  },
  mounted() {
    this.queryIsAdmin()
    this.getDeployEnvListAction()
    // this.initNavMenu()
    console.log(this.$route.path, 11111)
    // this.$store.state.planGenerate.isFullScreen ? '' : this.initNavMenu();
  },
  methods: {
    ...mapActions({
      updateGlobalNavigatorStatus: 'common/updateGlobalNavigatorStatusAction',
      getDeployEnvListAction: 'common/getDeployEnvListAction',
      getLinKongProject: 'common/getLinKongProject'
    }),
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
    // 切换导航菜单显隐状态
    handleNavigatorToggle() {
      this.updateGlobalNavigatorStatus(this.globalNavigatorStatus ^ 1)
    },
    async initNavMenu() {
      const data = await this.$post(this.baseUrl + '/user/isSuperAdminForCurUser')
      this.$store.commit('workSpace/setIsSuperAdmin', data)
      if (this.$route.path !== '/abilityCenter/targetList/detailFull' && this.$route.path !== '/abilityCenter/targetList/approvecode') {
        await this.$ennewsdk?.setNavMenu([
          {
            icon: '',
            label: '首页',
            key: 'home-page',
            href: ''
          },
          {
                icon: '',
                label: '模型仓库',
                key: 'modelWarehouse',
                href: ''
            },
            {
                icon: '',
                label: '产品文档',
                key: 'product-document',
                href: 'https://www.ennew.com/document.html#/documentDetail?documentCode=P181915'
            },
          {
            icon: '',
            label: '平台管理',
            key: 'platform-manage',
            href: '',
            disabled: !data
          },
          {
            icon: '',
            label: '项目管理',
            key: 'project-manage',
            href: '',
            disabled: !data
          }
        ])
      }

      const menuActiveKey = ['/', '/homePage'].includes(this.$route.path)
        ? 'home-page'
        : this.$route.path === '/projectManage'
        ? 'project-manage'
        : this.$route.path === '/model-warehouse'
        ? 'modelWarehouse'
        : 'platform-manage'
      const platformMenu = [
        '/workSpace/index',
        '/modelEngineering',
        '/clusterTrain',
        '/MirrorListOuter',
        '/resources/resourcesManage',
        '/resource/monitor',
        '/service-list',
        // '/model-warehouse',
        '/model-warehouse/detail',
        '/tagManagement/index',
        '/allMate',
        '/abilityMarket',
        '/CacheQuery'
      ]
      if (
        menuActiveKey !== 'platform-manage' ||
        (data && platformMenu.includes(this.$route.path))
      ) {
        await this.$ennewsdk?.setNavMenuActiveKey(menuActiveKey)
      }

      // 监听一级菜单点击事件
      window.addEventListener('click-nav-menu', (data) => {
        console.log('点击了',this.$route.path,data?.detail?.key)
        let path = ''
        if (data?.detail?.key === 'home-page') {
          path = '/homePage'
        }
        if (data?.detail?.key === 'platform-manage') {
          path = '/workSpace/index'
        }
        if (data?.detail?.key === 'project-manage') {
          path = '/projectManage'
        }
        if (data?.detail?.key === 'product-document') {
          window.open(data?.detail?.href,"_blank")
            // window.location.href = data?.detail?.href;
          }
        if (data?.detail?.key === 'modelWarehouse') {
            path = '/model-warehouse'
          }
        this.$router.push(path)
        this.updateLinkKongNav()
      })
      // 如果配置了点击项目名称事件(isClickProjectName=true)，可用监听实现业务逻辑
      window.addEventListener('click-project-name', () => {
        if (this.$route.path !== '/homePage') {
          this.$router.replace('/homePage')
        }
      })
    },
    updateLinkKongNav() {
      const workspaceName = this.$route.query.workspaceName
      const isLinKongProjectFlag = workspaceName && workspaceName === '临空智城'
      setTimeout(() => {
        const navigation = document
          .querySelector('ennew-navigation')
          .shadowRoot.querySelector('.ennew-navigation')
        navigation.querySelector('.--enn-nav-project-name').textContent = isLinKongProjectFlag
          ? '数字临空AI中台'
          : '能力生产平台'
        if (this.$route.path === '/abilityCenter/targetList/detailFull' || this.$route.path === '/abilityCenter/targetList/approvecode') {
          // 禁用点击事件
          navigation.querySelector('.--enn-nav-project-name').style.pointerEvents = 'none'
        }

        navigation.querySelector('.--enn-nav-LOGO').style.display = isLinKongProjectFlag
          ? 'none'
          : 'block'
        // if(this.$store.state.planGenerate.isIframeHide){
        //   navigation.style.height='0px'
        //   navigation.style.visibility='hidden'
        // }
      }, 250)
    },
    async queryIsAdmin() {
      const data = await this.$post(this.baseUrl + '/user/isAdminForCurUser')
      this.$store.commit('common/setIsAdmin', data)
    }
  }
}
</script>

<style lang="sass">
.iframeApp
  height: calc(100vh - 48px)
.screeFull
  height: calc(100vh - 48px)
.menu-colspan
  position: absolute
  left: 173px
  top: 400px
  width: 20px
  height: 40px
  background: #cdd7f2
  border-radius: 2px
  border: 1px solid #F2F3F5
  display: flex
  align-items: center
  justify-content: center
  z-index: 88
  cursor: pointer
  transition: left 0.4s ease-in-out
  &:hover
    background: #f6f7fb
  i
    color:#4068D4
    font-size: 14px
.menu-colhid
  transition: left 0.4s ease-in-out
  left: 0px !important
.collapsed
  display: none
*
  margin: 0
  padding: 0
  list-style: none
  font-family: SYZT, SimHei, SimSun, serif, PingFangSC-Regular, PingFang SC
  font-size: 14px

html,
body
  // height: auto
  padding: 0 !important
  margin: 0 !important
  position: relative

#app
  background: #f4f5f9

.el-menu
  border-right: none
.dashboard-mask
  position: absolute
  left: 45%
  top: 45%
  width: 1px
  height: 1px
  padding: 0
  margin: 0

.app-main
  padding-top: 0px
  display: flex
  position: relative
  overflow: hidden
  
  .app-main-wrapper
    flex: 1
    display: flex
    flex-direction: column
    overflow: auto
    min-height: calc(100vh - 48px)
    transition: all 0.3s ease-in-out
    position: relative

    &:not(.collapsed)
      margin-left: 200px
      width: calc(100% - 200px)

.app-menu
  position: fixed
  left: 0
  top: 48px
  bottom: 0
  background: #fff
  box-shadow: 2px 0px 8px 0px rgba(0,0,0,0.08)
  z-index: 2000
  transition: width 0.3s ease-in-out
  width: 64px
  
  &:not(.collapsed)

  .el-menu
    height: 100%
    border-right: solid 1px #e6e6e6

  .el-menu-item
    display: flex
    align-items: center
    padding: 0 20px !important
    white-space: nowrap
    overflow: hidden
    
    .menu-icon
      flex-shrink: 0
      margin-right: 10px

    span
      white-space: nowrap
      overflow: hidden
      text-overflow: ellipsis
.router-view
  flex: auto

  > .container
    min-height: calc(100vh - 137px)
    overflow-y: auto
    width: calc(100% - 40px)
    margin: 20px auto auto

#app .el-pagination.is-background .el-pager li
  box-sizing: border-box
  line-height: 26px
  min-width: 30px
  height: 30px
  border-radius: 15px
  border: 1px solid #dddddd

#app .el-pagination.is-background .el-pager li:not(.active):not(.disabled)
  border: solid 1px #d2d3d5
  background-color: transparent
  color: #656469

#app .el-pagination.is-background .btn-prev,
#app .el-pagination.is-background .btn-next
  border-radius: 18px
  border: solid 1px #d2d3d5
  background-color: transparent

#app .el-pagination .el-pagination__total
  line-height: 34px

#app .el-pagination .el-select .el-input .el-input__inner,
#app .el-pagination__editor.el-input .el-input__inner
  border-radius: 2px
</style>
<style lang="scss" scoped>
#app{
  height:100%;
}
</style>

