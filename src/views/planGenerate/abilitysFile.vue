<template>
  <div style="width: 100%;overflow: auto;">
    <div class="file-item" v-for="(item, index) in mappedFileData" :key="index">
      <div class="flex_body" @click="handleItemClick(item)">
        <div class="flex_body">
          <!-- 文件夹/文件图标 -->
          <img v-if="item?.ext_info" :src="item?.ext_info?.abilityIcon" alt width="32" height="32" />  
          <!-- <i v-else :class="['el-icon', item.isDir ? 'el-icon-folder' : 'el-icon-document']"></i> -->
          <div v-else>
            <!-- <img v-if="item.isDir" src="@/assets/images/planGenerater/ic_wjj.png" alt="">
            <img v-else src="@/assets/images/planGenerater/ic_wj.png" alt=""> -->
            <i v-if="item.isDir" :class="['el-icon', item.expanded ? 'el-icon-folder-opened' : 'el-icon-folder']" style="margin-right: 8px; color: #8c939d;"></i>
            <i v-else class="el-icon-document" style="margin-right: 8px; color: #8c939d;"></i>
          </div>
          <div  @click="showFileFun(item)" class="file-scene">{{ item.name }}</div> 
          <el-tag size="mini" v-if="item?.ext_info" >{{ item.agent_scene_name }}</el-tag>
        </div>
        <!-- 文件夹展开箭头 -->
        <div style="margin: 0px 10px" v-if="item?.ext_info">
            <i class="el-icon-delete cur" @click.stop="delnlFun(index)"></i>
        </div>
        <i v-if="item.isDir" :class="['el-icon', item.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
      </div>
      <!-- 递归展示子文件夹内容 -->
      <div v-if="item.isDir && item.expanded" class="sub-files">
        <abilitys-file :fileData="item.children || []" />
      </div>
    </div>
    <el-dialog
        :visible.sync="show"
        :title="title"
        width="80%"
        height="70vh"
        @closed="show = false"
        append-to-body
      >
        <div style="height: 65vh;overflow: auto;">
            <highlightjs  autodetect :code="codeStr" />
        </div>
        <template #footer>
          <div>
            <el-button @click="show = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
  </div>
</template>

<script>
import { queryListFiles,queryObjectKey } from '@/api/planGenerateApi.js'

export default {
  name: 'AbilitysFile',
  props: {
    fileData: {
      type: Array,
      default: () => [],
      required: true
    }
  },
  data() {
    return {
      show:false,
      title:'',
      codeStr: '',
      mappedFileData: []
    }
  },
  watch: {
    fileData: {
      handler(newVal) {
        this.mappedFileData = newVal.map(item => ({
          isDir: item.isDir != undefined ? item.isDir : true,
          name: item.name || '',
          agent_scene_name: item.agent_scene_name ||'',
          objectKey: item.objectKey || '',
          objectSize: 0,
          objectType: '',
          ext_info: item.ext_info || null,
          description: item.description || '',
          id: item.id,
          scheme_id:item.scheme_id
        }))
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
      // 获取文件扩展名
    getFileExtension(filename) {
      const parts = filename.split('.');
      return parts.pop() || '';
    },
    async  showFileFun(item){
        if(!item.isDir){
          if (this.getFileExtension(item.name) == 'pkl' || this.getFileExtension(item.name) == 'png' || this.getFileExtension(item.name) == 'jpg' || this.getFileExtension(item.name) == 'jpeg' || this.getFileExtension(item.name) == 'gif') {
              this.$message({
                type: 'warning',
                message: `当前文件不能预览`
              });
             return false     
          }
            const res  = await queryObjectKey({
                'objectKey': item.objectKey,
            })
            if(res.data.code == 200){
                this.title = item.name
                fetch(res.data.result)
                .then(response => response.text())
                .then(data => {
                // data就是文件的内容
                this.codeStr = data
                this.show = true
                })
                .catch(error => { console.log('error', error) });
            }
        }
    },
    delnlFun(index){
        this.$emit('delnlFun',index)
    },
    async handleItemClick(item) {
        console.log(item,'item')
      if (!item.isDir) return
      
      // 切换展开状态
      this.$set(item, 'expanded', !item.expanded)
      
      // 如果已经有子文件数据，直接显示/隐藏
      if (item.children) return
      
      try {
        const res = await queryListFiles({
          scheme_id: Number(item.scheme_id),
          prefix: item?.objectKey
        })
        
        if (res.data.code === 200 && res.data.result) {
          // 设置子文件数据，子层数据不需要特殊处理
          this.$set(item, 'children', res.data.result.map(it => ({
            ...it,
            scheme_id: item.scheme_id,
            name: it.objectName
          })))
        }
      } catch (error) {
        console.error('获取子文件失败:', error)
        this.$message.error('获取子文件失败')
      }
    }
  }
}
</script>

<style scoped>
.file-item {
  width: 100%;
  color: #616161;
}

.flex_body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 26px;
  width: 100%;
  border-radius: 2px;
  border-bottom: 1px solid #EBECF0;
  padding: 0 10px;
  cursor: pointer;
}

.flex_body:hover {
  background-color: #f5f7fa;
}

.file-name {
  margin-left: 8px;
  width: 100%;
  text-align: right;
}
.file-scene{
  width: 100%;
  text-align: left;
  margin-left: 8px;
  cursor: pointer;
}
.sub-files {
  padding-left: 20px;
}

/* 图标样式 */
.el-icon-folder {
  color: #909399;
}

.el-icon-document {
  color: #909399;
}
</style>
