<template>
 <div class="first">
   <div class="top">
     <!-- <div class="header">专家生产</div> -->
   </div>
   <div class="bottom">
     <div class="left-content">
       <div class="ai-container">
         <PartnerZhushouChat></PartnerZhushouChat>
       </div>
     </div>
   </div>
 </div>
</template>

<script>
import PartnerZhushouChat from './guide/partnerZhushouChat.vue';
const userInfo = sessionStorage.getItem('USER_INFO')
 ? JSON.parse(sessionStorage.getItem('USER_INFO'))
 : {};
let title = '';
if (sessionStorage.getItem('ACCOUNT_INFO')) {
 const account = JSON.parse(sessionStorage.getItem('ACCOUNT_INFO'));
 const titleParts = account.postName.split('/');
 title = titleParts[titleParts.length - 2];
} else {
 title = '';
}
export default {
 name: 'First',
 components: { PartnerZhushouChat },
 data() {
   return {
     workspaceId: 1,
     title: '安全专家',
   };
 },
 mounted() {
   this.workspaceId = Number(this.$route.query.workspaceId);
   // 请求目的：为了记录用户进入空间的日志数据落库
   this.$get('/overview/statistics/resourceUsed/v2')
 },
 methods: {
 }
};
</script>

<style lang="scss" scoped>
.first {
//  max-height: calc(100vh - 90px);
 overflow: hidden;
 span {
   font-family: PingFangSC, PingFang SC;
   font-weight: normal;
   font-size: 16px;
   color: #323233;
 }

 .top {
   background-color: #fff;
   padding: 0px 20px 0px;
   position: relative;

   .header {
     font-weight: bold;
     color: #323233;
     line-height: 26px;
     font-size: 18px;
     padding: 14px 0px;
   }
 }

 .bottom {
   overflow: auto;
   box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
   border-radius: 4px;
   background-color: transparent;
   display: flex;
   height: calc(100%);
   max-height: calc(100%);

   .left-content {
     background-color: #fff;
     width: 100%;
     min-width: 50px;
     height: 100%;
     overflow-y: auto;
     border-radius: 4px;

   }

 }
}
</style>