<template>
  <div class="flow-container">
    <div
      :class="index === 0 ? 'flow-wrap-first' : 'flow-wrap'"
      v-for="(item, index) in flowData"
      :key="index"
    >
      <div class="flow-item-line" v-if="index !== 0 ">
        <el-divider v-if="item.status !== 0"></el-divider>
        <div v-else class="dash-line"></div>
      </div>
      <div class="flow-item-wrap">
        <img v-if="item.status === 1" src="@/assets/images/planGenerater/task-success.png">
        <img v-else-if="item.status === 3" src="@/assets/images/planGenerater/task-running.png">
        <img v-else-if="item.status === 2" src="@/assets/images/planGenerater/task-error.png">
        <img v-else src="@/assets/images/planGenerater/task-info.png">
        <div style="margin-left: 8px">{{item.title}}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
     // flowData的status 0:待执行，1:执行成功，2:执行失败，3:执行中
    flowData: {
      type: Array,
      default() {
        return [];
      }
    },
    maxLength: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      list: []
    };
  },
  // watch: {
  //   flowData: {
  //     handler(val) {
        
  //     },
  //     immediate: true
  //   }
  // }
};
</script>
<style lang="scss" scoped>
.flow-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-start;
  // justify-content: space-between;
  .flow-wrap {
    // flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .flow-wrap-first {
    width: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .flow-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .flow-item-line {
    // flex: 1;
    width: 16px;
    margin: 0px 4px;
    .dash-line {
      width: 16px;
      border-top: 1px dashed #C8C9CC;
    }
    // padding: 0px 12px 10px;
    .el-divider--horizontal {
      margin: 0px;
    }
  }
  .flow-item-wrap {
    width: auto;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    img {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
