<template>
  <div class="task-card">
    <div class="task-title">
      <img src="@/assets/images/planGenerater/deploy-color.png"/>
      <div class="task-title-text">代码部署</div>
    </div>
    <div class="task-card-content" v-loading="loading" element-loading-text="代码部署中..." element-loading-spinner="el-icon-loading">
      <div v-if="status !== 0">
        <el-result v-if="deployStatus && !testFlag" icon="success" subTitle="代码部署成功"/>
        <el-result v-else-if="deployStatus && testFlag" icon="error" subTitle="代码部署失败"/>
        <el-result v-else icon="warning" subTitle="代码未部署"/>
      </div>
      <div v-else>
        <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
          <img src="@/assets/images/planGenerater/empty.png" style="width: 180px;height: auto"/>
          <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">暂无</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { GetDecision, AbilityPublish } from '@/api/planGenerateApi.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  // components: { Status, MyEditor },
  props: {
    updateDevelopFlag: {
      type: [Number, String],
      default: 1
    },
    status:  {
      type: [String, Number],
      default: ''
    },
  },
  watch: {
    status: {
      handler(val) {
        console.log('部署val', val);
        if (Number(val) === 3) {
          this.loading = true;
        } 
        if (Number(val) === 1) {
          this.deployStatus = true;
          this.testFlag = false;
          this.bushu();
        } 
        if (Number(val) === 2) {
          this.testFlag = true;
          console.log('部署失败，部署val', val, this.testFlag);
          this.testFlag = true;
          this.deployStatus = true;
        } 
        if (Number(val) === 0) {
          this.loading = false;
        } 
      },
      immediate: true
    },
    updateDevelopFlag: {
      handler(val) {
        console.log('部署更新val', val)
        if (val !== 1) {
          this.bushu();
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      loading: false,
      deployStatus: false, // 部署状态
      testFlag: false, // 是否部署失败
      testFlag: false,
    };
  },
  methods: {
    async handleInit() {
      this.loading = true;
      console.log('初始化');
      await GetDecision({scheme_id: this.$route.query.id,scheme_status: 'decision_ability'}).then(res => {
        this.loading = false;
        // this.codeData = res.data.result?.decision_making_content || '';
        if(res.data.result?.ext_info?.deploy_status && res.data.result?.ext_info?.deploy_status === 'deployed') {
          this.deployStatus = true;
          this.$emit('handleOk', true);
        } else {
          this.deployStatus = false;
        }
      }).finally(() => {
        this.loading = false;
      })
      
    },
    async bushu() {
      this.loading = true;
      await GetDecision({scheme_id: this.$route.query.id,scheme_status: 'decision_ability'}).then(async codeRes => {
        const codeData = codeRes.data.result?.decision_making_content || '';
        if(codeRes.data.result?.ext_info?.deploy_status && codeRes.data.result?.ext_info?.deploy_status === 'deployed') {
          this.deployStatus = true;
          this.testFlag = false;
          this.loading = false;
          this.$emit('handleOk', true);
        } else {
          await AbilityPublish({
            code_str: codeData,
            scheme_id: this.$route.query.id
          }).then(res => {
            if(res?.data?.code !== 200){
              // this.$message.error(res?.data?.msg || '部署失败');
              this.loading = false;
              this.testFlag = true;
              this.$emit('handleOk', false);
              return;
            } else {
              this.loading = false;
              this.deployStatus = true;
              this.testFlag = false;
              this.$emit('handleOk', true);
            }
          }).catch(() => {
            this.loading = false;
            this.testFlag = true;
            this.$emit('handleOk', false);
          }).finally(() => {
            this.loading = false;
          });
        }
      })
      // this.$message.success('能力代码生成部署成功');
    },
  }
};
</script>
<style lang="scss" scoped>
.task-card {
    min-height: 300px;
    border: 1px solid #DCDDE0;
    border-radius: 4px;
    margin-bottom: 16px;
    .task-title {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #DCDDE0;
        img {
          width: 14px;
          height: auto;
        }
        .task-title-text {
          font-weight: 500;
          font-size: 14px;
          color: #323233;
          line-height: 22px;
          margin-left: 8px;
        }
    }
    .task-card-content {
      padding: 16px 20px;
      text-align: center;
      .deploy-text {
        color: #323233;
        font-size: 14px;
        line-height: 22px;
      }
    }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
