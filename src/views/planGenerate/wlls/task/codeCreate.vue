<template>
  <div class="task-card">
    <div class="task-title">
      <img src="@/assets/images/planGenerater/code-color.png"/>
      <div class="task-title-text">代码生成</div>
    </div>
    <div class="task-card-content" v-loading="loading" element-loading-text="代码生成中..." element-loading-spinner="el-icon-loading">
      <MonacoEditor
        id="codeCreated"
        class="editor"
        :value="showCodeData"
        language="python"
        :scroll-beyond-last-line="true"
        :original="codeDataOri"
        theme="vs-dark"
        v-if="status !== 0"
        :diff-editor="false"
        :options="options"
        @editorDidMount="{}"
        @change="{}"
      />
      <div v-else>
        <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
          <img src="@/assets/images/planGenerater/empty.png" style="width: 180px;height: auto"/>
          <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">暂无</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import MonacoEditor from '@/components/MonacoEditor'
// import Status from '@/components/Status/index.vue';
import { GetDecision } from '@/api/planGenerateApi.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  components: { MonacoEditor },
  props: {
    codeDataStatus: {
      type: [String, Number],
      default: ''
    },
    codeData: {
      type: String,
      default: ''
    },
    status:  {
      type: [String, Number],
      default: ''
    },
    processStatus: {
      type: [String, Number],
      default: ''
    },
  },
  watch: {
    codeDataStatus: {
      handler(val) {
        if (Number(val) === 2 || Number(val) === 3) {
          this.handleInit();
        } else {
          this.showCodeData = this.codeData
          this.codeDataOri =this.codeData
          const temp = document.getElementById('codeCreated');
          if (temp) {
            temp.scrollIntoView({ block: 'end', inline: 'nearest' });
            temp.scrollTop = temp.scrollHeight + 200;
          }
        }
      },
      immediate: true
    },
    codeData: {
      handler(val) {
        this.showCodeData = val
        this.codeDataOri = val
        // const temp = document.getElementById('codeCreated');
        // if (temp) {
        //   temp.scrollIntoView({ block: 'end', inline: 'nearest' });
        //   temp.scrollTop = temp.scrollHeight + 200;
        // }
        const target = document.getElementById('codeCreated')
        console.log('target.scrollTop', target, target.scrollHeight);
        if (target) {
          target.scrollTop = target.scrollHeight - 40;
          console.log('target2.scrollTop', target);
          //target.scrollTop = target.scrollHeight - target.clientHeight + 30;
        }
      },
      immediate: true
    },
    status: {
      handler(val) {
        // 状态是成功时获取代码
        if (Number(val) === 1) {
          this.handleInit();
        } 
      },
      immediate: true
    },
    processStatus: {
      handler(val) {
        console.log('生成日志梳妆台', val);
        // 代码生成过程日志状态是成功时获取代码
        if (Number(val) === 2 || Number(val) === 3) {
          this.handleInit();
        }
        if (Number(val) === 1) {
          this.loading = true;
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      loading: false,
      options: {
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      },
      showCodeData: '',
      codeDataOri: '',
    };
  },
  methods: {
    async handleInit() {
      this.loading = true;
      console.log('初始化');
      await GetDecision({scheme_id: this.$route.query.id,scheme_status: 'decision_ability'}).then(res => {
        this.loading = false;
        this.showCodeData = res.data.result?.decision_making_content || '';
        this.codeDataOri = res.data.result?.decision_making_content || '';
      })
      
    }
  }
};
</script>
<style lang="scss" scoped>
.task-card {
    min-height: 300px;
    border: 1px solid #DCDDE0;
    border-radius: 4px;
    margin-bottom: 16px;
    .task-title {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #DCDDE0;
        img {
          width: 14px;
          height: auto;
        }
        .task-title-text {
          font-weight: 500;
          font-size: 14px;
          color: #323233;
          line-height: 22px;
          margin-left: 8px;
        }
    }
    .task-card-content {
      padding: 16px 20px;
      height: 100%;
      min-height: 250px;
      .editor {
        min-height: 250px;
        height: 100%;
        width: 100%;
        margin-left: 1px;
      }
    }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
