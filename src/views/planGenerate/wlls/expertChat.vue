<template>
  <div
    id="chatContainer"
    :class="
      $store.state.planGenerate.isIframeHide ? 'chatContainer chatContainerFrame' : 'chatContainer'
    "
  >
    <div class="headerBox">
      <!-- <div class="headerTitle">
        <div style="display: flex; align-items: center">
          <div class="title">
            {{
              schemeInfo.name
                ? schemeInfo.name
                : $route.query.name
                ? decodeURIComponent(encodeURIComponent($route.query.name))
                : ''
            }}（{{ $route.query.id }}）
          </div>
          <div class="sence-tag">
            {{
              schemeInfo.agent_scene_name
                ? schemeInfo.agent_scene_name
                : $route.query.agent_scene_name
                ? decodeURIComponent(encodeURIComponent($route.query.agent_scene_name))
                : ''
            }}
          </div>
          <el-button type="text" style="margin-left: 10px" @click="editFn"
            ><i class="el-icon-edit"></i
          ></el-button>
        </div>
        <div style="display: flex; align-items: center" class="right">
          <div class="model-expert">专家生产模式</div>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-setting"
            @click="
              () => $router.push({ path: '/planGenerate/sceneConfig', query: { ...$route.query } })
            "
          ></el-button>
          <el-button
            class="button-last"
            type="info"
            @click="
              () =>
                $router.push({
                  path: '/planGenerate/index',
                  query: {
                    ...$route.query,
                    name: null,
                    id: null,
                    agent_id: null,
                    type: null,
                    create_time: null,
                    desc: null
                  }
                })
            "
            >返回智能能力研发</el-button
          >
        </div>
      </div> -->
      <div
        v-if="schemeInfo.agent_scene_code === 'digital_twin_assistant_scene'"
        class="headerStep"
        :style="{ width: '100%' }"
      >
        <el-steps :active="activeStep" finish-status="success" simple class="myStep">
          <el-step title="方案明细">
            <img
              slot="icon"
              :src="
                activeStep !== 0
                  ? require('@/assets/images/icon/1_icon.png')
                  : require('@/assets/images/icon/1_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
          <el-step title="智能能力测试与迭代">
            <img
              slot="icon"
              :src="
                activeStep !== 1
                  ? require('@/assets/images/icon/2_icon.png')
                  : require('@/assets/images/icon/2_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
        </el-steps>
        <div :class="progressData === 0 ? ' myProgress progressEmpty' : 'myProgress'">
          <el-progress
            :text-inside="true"
            :stroke-width="12"
            :percentage="progressData"
          ></el-progress>
        </div>
      </div>
    </div>
    <div v-if="miniFlag" class="stepBox">
      <div class="stepWrap">
        <div style="font-weight: bold; margin-right: 8px">任务进行中：</div>
        <div style="width: 70%"><stepFlow :flow-data="flowData" /></div>
      </div>
      <el-tooltip class="item" effect="dark" content="最大化" placement="top">
        <div @click="showTaskModal">
          <img class="full" src="@/assets/images/planGenerater/full.png" />
        </div>
      </el-tooltip>
    </div>
    <planChat
      v-if="activeStep * 1 === 0"
      ref="planChatRef"
      :kickLoading = "kickLoading"
      :isOccupied = "isOccupied"
      :handleUpdateKickedLoading="handleUpdateKickedLoading"
      :startPolling="startPolling"
      :flow-data="flowData"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :session-id="sessionId"
      :agent-error="agentError"
      :plan-data-val="planData"
      :plan-process-val="optionDataProcess"
      :plan-status="planStatus"
      :tree-status="treeStatus"
      :tree-data-val="treeData"
      :tree-process-val="treeDataProcess"
      :is-expert="isExpert"
      :mini-flag="miniFlag"
      :change-run-task-status="runTaskStatus"
      :system-messages="systemMessages"
      :emergentScene="emergentScene"
      @updateIsShow = "handleUpdateIsShow"
      @updateSendMsg="handleSendMsg"
      @updateStep="handleUpdateStep"
      @handleReStart="handleReStart"
      @updateGenerate="handleGenerate"
      @showTaskModal="showTaskModal"
      @handleUpdateScheme="handleUpdateScheme"
      @handleUpdateTreeData="handleUpdateTreeData"
      @updateDeviceId="updateDeviceId"
      @initFlowData="handleInitFlowData"
    ></planChat>
    <abilityTest
      v-if="activeStep * 1 === 1"
      :flow-data="flowData"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :tree-data-val="treeData"
      :tree-process-val="treeDataProcess"
      :tree-status="treeStatus"
      :mini-flag="miniFlag"
      :publish-ability="publishAbility"
      @showTaskModal="showTaskModal"
      @updateStep="handleUpdateStep"
      @updateGenerate="handleGenerate"
      @handleUpdateTreeData="handleUpdateTreeData"
    ></abilityTest>
    <RunTask
      v-if="taskModalVisable"
      :is-visible="taskModalVisable"
      :session-id="sessionId"
      :flow-data="flowData"
      :run-stream-list="runStreamList"
      :all-success="allSuccess"
      :template-id="templateId"
      :data-align-data-status="dataAlignDataStatus"
      :data-align-data="dataAlignData"
      :process-status="processStatus"
      :code-data="codeData"
      :code-process-data="codeProcessData"
      :code-data-status="codeDataStatus"
      :update-develop-flag="updateDevelopFlag"
      :dev-person="schemeInfo.developer"
      :code-analysis-data="codeAnalysisData"
      :code-analysis-process-data="codeAnalysisProcessData"
      :code-analysis-data-status="codeAnalysisDataStatus"
      :tree-data-val="treeData"
      :tree-process-val="treeDataProcess"
      :tree-status="treeStatus"
      :align-process-data="alignProcessData"
      @updateTaskModal="updateTaskModal"
      @updateTaskMiniModal="updateTaskMiniModal"
      @handleReStart="handleReStart"
      @stopAbilityGen="stopAbilityGen"
      @updateFlowData="updateFlowData"
      @alignDataAns="queryStart"
      @reAlignData="reAlignData"
      @updateCodeAnalysis="handleUpdateCodeAnalysis"
    ></RunTask>
    <editModal
      :is-visible="editModalVisable"
      :flow-data="flowData"
      @updateEditModal="updateEditModal"
    ></editModal>
    <DataAlignPreDialog
      :is-visible="alignPreVisable"
      :all-success="allSuccess2"
      :run-stream-list="runStreamList2"
      @close="handelCofirmAlignPre"
    ></DataAlignPreDialog>
  </div>
</template>
<script>
import {
  chatDisconnect,
  queryChatIsUseing,
  saveSimpleSchemeGenerate,
  startDecisionTreeGenerateRequest,
  startStopThinking,
  startAlignDataGenerate,
  startAbilityGenerate,
  startConversation,
  querySchemeDetailById,
  queryTaskByTemp,
  saveAlignType,
  queryTempIfFromScene,
  queryTempIfFromScene2,
  queryAbilityMapping,
  queryDuiqiTags,
  getSchemePhaseTasks,
  queryDeviceIds,
  queryPointsByDeviceId,
  GetDecision,
  startTask,
  checkByDeviceId,
  reexecuteTask,
  updateLogTaskStatus,
  startCodeAnalysis
} from '@/api/planGenerateApi.js'
import planChat from './planChat.vue'
import abilityTest from './abilityTest.vue'
import RunTask from './runTaskNew.vue'
import stepFlow from './stepFlow.vue'
import editModal from './editModal.vue'
import DataAlignPreDialog from './dataAlignPreModal.vue'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import axios from 'axios'
import Bus from '../../../components/bus'
const cancelToken = axios.CancelToken
let source = cancelToken.source()

// 这里有一些内容的对应函数我还没写好，比如方案优化process和SQL的process, 而且这个函数应该是没有的，我得弄一下。
const messageRoleToHandlersMap = {
  decision_tree_generate: 'handleProcessTreeDetail', // 思维树生成
  decision_tree_gen_process: 'handleProcessTreeData', // 思维树生成过程
  scheme_generate: 'handleTaskDetail', // 方案明细的流式消息
  scheme_gen_process: 'handleProcessSchemeData', // 方案明细的过程流式消息
  gen_process: 'handleProcessDetail', // 聊天的生成过程的流式消息
  ability_generate: 'handleAbilityDetail', // 能力生成
  ability_gen_process: 'handleCodeProcesData', // 能力生成过程
  sql_generate: 'handleSQLData', // SQL 流消息
  sql_gen_process: '',
  align_data_gen_process: 'handleAlignProcessData', // 数据对齐过程流，不过通用监听那个不从这里返回了。
  scheme_optimize: 'handleOptimizeData', // 方案优化
  scheme_optimize_gen_process: 'handleOptimizeProcessData', // 方案优化过程
  code_analysis: 'handleCodeAnalysisData', // 代码分析
  code_analysis_gen_process: 'handleCodeAnalysisProcessData', // 代码分析过程
  rule_generate: 'handleRuleDetail', // 规则生成
  rule_generate_gen_process: 'handleProcessRuleData', // 规则生成过程
  model_param_extraction: 'modelData', //模型参数识别
  model_param_extraction_process: 'modelData', //模型参数识别过程
  modeling_info_structure: 'modelData', // 结构化建模信息
  modeling_info_structure_process: 'modelData', // 结构化建模信息过程
  math_model_generate: 'modelData', //生成数学模型
  math_model_generate_process: 'modelData' //生成数学模型过程
}

export default {
  name: 'ExpertChat',
  components: {
    planChat,
    abilityTest,
    RunTask,
    editModal,
    stepFlow,
    DataAlignPreDialog
  },
  data() {
    return {
      kickLoading: false,
      isOccupied: false,
      isExpert: true,
      eventSource: null,
      planData: '', // 方案明细内容
      schemeInfo: {},
      activeStep: 0,
      hasChatingName: '',
      systemMessages: '',
      optionDataProcess: '',
      treeData: '', // 思维树内容
      treeDataProcess: '', // 生成过程
      treeStatus: -1,
      planStatus: -1,
      agentError: false,
      // flowData的status 0:待执行，1:执行成功，2:执行失败，3:执行中
      flowData: [
        {
          icon: 'fangan',
          title: '思维树生成',
          status: 0,
          runCode: 'decision_tree',
          process: ''
        },
        {
          icon: 'fangan',
          title: '数据对齐分析',
          status: 0,
          runCode: 'align_analysis',
          process: ''
        },
        {
          icon: 'fangan',
          title: '数据对齐',
          status: 0,
          runCode: 'align_data_generate',
          process: ''
        },
        {
          icon: 'fangan',
          title: '代码生成',
          status: 0,
          runCode: 'decision_making_generate',
          process: ''
        },
        {
          icon: 'fangan',
          title: '代码部署',
          status: 0,
          runCode: 'code_deploy',
          process: ''
        },
        {
          icon: 'fangan',
          title: '代码测试',
          status: 0,
          runCode: 'code_test',
          process: ''
        },
        {
          icon: 'fangan',
          title: '能力参数分析',
          status: 0,
          runCode: 'code_analysis',
          process: ''
        }
      ],
      taskModalVisable: false,
      dataAlignData: [], // 数据对齐内容
      dataAlignDataStatus: '', // 数据对齐状态
      codeData: '', // 代码生成内容
      codeDataStatus: '', // 代码生成状态
      codeProcessData: '', // 代码生成过程内容
      processStatus: '', // 代码生成日志状态
      // 数据对齐分析字段--开始
      allSuccess: false, // 数据对齐分析状态
      runStreamList: [], // 数据对齐分析内容
      tablesList: [],
      templateId: '',
      tagList: [],
      allTagList: [],
      allRunTasks: [], // 所有需要执行的任务
      runId: '', // 执行需要的id,
      // 数据对齐分析字段--结束
      formData: {
        cangku: false,
        chooseData: [],
        tag_ids: [],
        api: false,
        manual_input: false
      },
      // 数据对齐分析字段--结束
      updateDevelopFlag: 1,
      editModalVisable: false,
      progressData: 0, // 当前进度值
      miniFlag: false, // 任务执行最小化标识
      sessionId: '',
      alignPreVisable: false, // 数据对齐预处理窗口
      deviceId: '', // 绑定的设备ID
      preTemplateId: '', // 数据对齐前过程模版ID,
      pointsData: [], // 设备测点数据

      // 数据对齐分析前字段--开始
      allSuccess2: false, // 数据对齐分析状态
      runStreamList2: [], // 数据对齐分析内容
      allRunTasks2: [], // 所有需要执行的任务
      runId2: '', // 执行需要的id,
      // 数据对齐分析前字段--结束
      runTaskStatus: 0,
      codeAnalysisData: '', // 代码分析
      codeAnalysisDataStatus: '', // 代码分析过程状态
      codeAnalysisProcessData: '', // 代码分析过程
      alignProcessData: '', // 数据对齐过程数据
      alignProcessDataStatus: '', // 数据对齐过程状态
      emergentScene: ''
    }
  },
  computed: {
    ...mapGetters({
      initSengMsg: 'planGenerate/getInitSengMsg'
    })
  },
  watch: {
    flowData: {
      handler(newVal, oldVal) {
        if (newVal) {
          const maxCount = 7
          const count = newVal.filter((item) => item.status === 1).length
          console.log(count, 'count')
          const percentage = Math.ceil((count / maxCount) * 100)
          console.log(percentage, 'percentage')
          this.progressData = percentage
        }
      },
      immediate: true,
      deep: true
    }
  },
  async mounted() {
    Bus.$on('operations-research-edit', (data) => {
      this.editFn()
    })
    await this.schemeDetailById()
    await this.queryTemplate()
    await this.querySchcemTask()
  },
  beforeDestroy() {
    Bus.$off('operations-research-edit')
    source.cancel()
    source = cancelToken.source()
    this.eventSource && this.eventSource.close()
  },
  methods: {
    handleUpdateIsShow(val) {
      this.isend = val
    },
    handleUpdateKickedLoading(val) {
      this.kickLoading = val
    },
    alertKicked() {
      this.$alert('对话已被他人占用', '提示', {
        confirmButtonText: '确定',
        customClass: 'my-message-box1',
        showClose: false,
        callback: action => {
          // this.$message({
          //   type: 'info',
          //   message: `action: ${action}`
          // });
          if (action === 'confirm') {
            let fromMenu = this.$route.query.fromMenu
            if(fromMenu === '1'){
              // urlPath= '/planGenerate/first' 
              // label= '专家生产'
              this.$router.push({
                path: '/planGenerate/first',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '3'){
              // label= '研发生产'
              // urlPath= '/planGenerate/index' 
              this.$router.push({
                path: '/planGenerate/index',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '2'){
              // label= '任务规划'
              // urlPath= '/planGenerate/taskRd' 
              this.$router.push({
                path: '/planGenerate/taskRd',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '4'){
              // label= '训练与验证'
              // urlPath= '/planGenerate/validation' 
              this.$router.push({
                path: '/planGenerate/validation',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }
          }
        }
      });
    },
    async startPolling() {
      const poll = async () => {
        try {
          const res = await queryChatIsUseing({ scheme_id: this.$route.query.id });
          if (res && res.data.result.is_using === false) {
            console.log('Polling stopped as the condition met.');
            await this.getChatIsUseing()
            this.kickLoading = false
            return; // 停止轮询
          } else {
            await chatDisconnect({ session_id: this.sessionId, scheme_id: this.$route.query.id })
          }
        } catch (error) {
          console.error('Error during polling:', error);
          // 根据需求决定是否停止轮询或继续
        }
        // 在操作完成后，等待2秒再进行下一次轮询
        setTimeout(poll, 4000);
      };

      // 启动第一次轮询
      poll();
    },
    async handleUpdateHasChatingName(val) {
      // this.hasChatingName = val
      await this.getChatIsUseing()
    },
    async handleSSEMessagebyRole(message) {
      for (const role of Object.keys(messageRoleToHandlersMap)) {
        // 使用 for...of 循环
        if (role === message.role) {
          const handlerName = messageRoleToHandlersMap[role]
          if (this[handlerName]) {
            await this[handlerName](message) // 直接 await，支持异步
          } else {
            console.error(`No handler found for this role: ${role}`)
          }
        }
      }
    },
    async handleInit() {
      console.log('获取思维树、数据对齐内容')
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_tree'
      }).then((res) => {
        this.treeData = res.data.result?.decision_making_content || ''
        this.treeDataProcess = res.data.result?.sub_content || ''
        if (res.data.result?.decision_making_content) {
          this.flowData[0].status === 1
          this.flowData[0].process === res.data.result?.sub_content
        }
      })
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_ability'
      }).then((res) => {
        const codeData = res.data.result?.decision_making_content || ''
        if (
          res.data.result?.ext_info?.deploy_status &&
          res.data.result?.ext_info?.deploy_status === 'deployed'
        ) {
          if (this.flowData[2].status === 1) {
            this.flowData[4].status = 1
            this.flowData[5].status = 1
          }
          if (
            res.data.result.ext_info.code_analysis_status ||
            res.data.result.ext_info.code_params
          ) {
            console.log('1已经有参数了')
            this.flowData[6].status = 1
            this.flowData[6].process = res.data.result.ext_info.sub_content || ''
          } else {
            console.log('1没有参数，触发流信息')
            this.flowData[6].status = 0
            this.flowData[6].process = ''
          }
        }
        if (codeData) {
          if (this.flowData[2].status === 1) {
            // console.log('代码生成结果处理', res.data.result?.sub_content);
            this.flowData[3].status = 1
            this.flowData[3].process = res.data.result?.sub_content
            // console.log('代码生成结果处理---', this.flowData[3]);
          }
        } else {
          this.flowData[3].status = 0
          this.flowData[4].status = 0
          this.flowData[5].status = 0
          this.flowData[6].status = 0
        }
      })
    },
    handleInitFlowData() {
      this.flowData.forEach((item) => {
        item.status = 0
      })
    },
    saveSchemeGenerateFn(val) {
      saveSimpleSchemeGenerate({ session_id: this.sessionId, messages: val })
    },
    editFn() {
      this.editModalVisable = true
    },
    // 显示任务执行窗口
    showTaskModal() {
      this.taskModalVisable = true
    },
    updateEditModal() {
      this.schemeDetailById()
      this.editModalVisable = false
    },
    // 关闭任务执行窗口，同时将最小化窗口也关闭显示
    updateTaskModal(val) {
      this.miniFlag = false
      this.taskModalVisable = false
      if (val === 'finish') {
        this.$refs.planChatRef && this.$refs.planChatRef.changeViews(1)
        // this.activeStep = 1
      }
    },
    // 显示最小化任务执行进度
    updateTaskMiniModal() {
      this.miniFlag = true
      this.taskModalVisable = false
    },
    handleSendMsg(val) {
      console.log(val, '发送的信息')
      this.saveSchemeGenerateFn(val)
    },
    handleGenerate() {
      console.log('触发重新生成思维树')
      this.treeStatus = -1
      startDecisionTreeGenerateRequest({ session_id: this.sessionId })
    },
    regenerateTree() {
      this.treeData = ''
      this.treeProcess = ''
      this.taskLoading = true
      this.handleGenerate()
    },
    // 2. 下一步跳转逻辑：
    // 2.1. 任务执行未触发时: 触发按任务执行，打开‘执行任务’弹窗。
    // 2.2. 当所有任务都执行完成且成功时，跳转至能力测试页面。
    // 2.3. 任务未全部执行成功且未全部完成时，打开‘执行任务’弹窗。
    handleUpdateStep(v) {
      this.treeData = ''
      this.changeViews(v)
    },
    changeViews(val) {
      // 向外层页面传递消息
      console.log('当前在第几步', val)
      this.codeAnalysisDataStatus = '0'
      window.parent.postMessage(JSON.stringify({ stepIndex: val }), '*')
      this.activeStep = val
    },
    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.schemeInfo = res.data.result || { ...this.$route.query }
            console.log('详情', this.schemeInfo)
            this.schemeInfo.agent_scene_code = 'digital_twin_assistant_scene'
            this.emergentScene = res.data.result?.agent_scene_id
            this.eventSource && this.eventSource.close()
            this.eventSource = null
            Bus.$emit('operations-research', this.schemeInfo)
            this.totalWidth = document.getElementById('chatContainer').getBoundingClientRect().width
            this.leftWidth = document.getElementById('left-content').getBoundingClientRect().width
            this.rightWidth = document.getElementById('right-content').getBoundingClientRect().width
            console.log('宽度', this.rightWidth)
            this.taskStatus = this.$route.query.status
            await this.getChatIsUseing()
            console.log('操作系统', window.navigator.userAgent)
          }
        })
        .catch((_err) => {
          // this.$message({
          //   type: 'error',
          //   message: _err.data?.msg || '接口异常!'
          // });
          console.log(_err.data?.msg || '接口异常!')
        })
    },
    // 查询当前是否正在跟其他用户对话
    async getChatIsUseing() {
      queryChatIsUseing({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('判断是否可用', res.data)
          if (res.data.result.is_using) {
            this.hasChatingName = res.data.result?.using_user?.nickName
          } else {
            this.hasChatingName = ''
            this.sessionId = res.data.result.session_id || '1'
            const url = process.env.VUE_APP_PLAN_API.startsWith('/')
              ? window.location.origin + process.env.VUE_APP_PLAN_API + '/stream'
              : process.env.VUE_APP_PLAN_API + '/stream'
            console.log('url', url)
            const userInfo = sessionStorage.getItem('USER_INFO')
              ? JSON.parse(sessionStorage.getItem('USER_INFO'))
              : {}

            this.eventSource = new EventSource(
              url +
                '?scheme_id=' +
                this.$route.query.id +
                '&user_id=' +
                (userInfo.userId || 'str') +
                '&work_space_id=' +
                (this.$route.query.workspaceId + '' || '1') +
                '&tenant_id=' +
                (userInfo.tenantId || 'str') +
                '&session_id=' +
                (res.data.result.session_id || '1')
            )

            this.eventSource.addEventListener('open', async() => {
              console.log('连接已建立')
              if (this.initSengMsg) {
                console.log('initSengMsg', this.initSengMsg)
                this.planStatus = 0
                // 初始化执行知识来源
               await this.$refs.planChatRef.getKonwledgeSourceFn(window.customeDescription);
                this.saveSchemeGenerateFn(this.initSengMsg)
                this.$store.commit('planGenerate/setInitSengMsg', '')
              }
              this.agentError = false
            })

            this.eventSource.addEventListener('system', async (event) => {
              console.log('event.data 001', event.data)
              if (event.data === 'occupied') {
                this.eventSource.close()
                this.isOccupied = true
                this.alertKicked()
                // await queryChatIsUseing({ scheme_id: this.$route.query.id }).\
                // this.getChatIsUseing()
              }
            })

            this.eventSource.addEventListener('messages', (event) => {
              console.log('messages收到消息：', event.data)
              this.handleMessage(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('stream_message', (event) => {
              console.log('stream_message收到消息：', event.data)
            })
            this.eventSource.addEventListener('system_message', (event) => {
              // console.log('system_message', JSON.parse(event.data));
              this.handleSystemMessage(JSON.parse(event.data))
            })

            this.eventSource.addEventListener('scheme_stream_message', (event) => {
              console.log('scheme_stream_message', event.data)
              this.handleProcessPlanDetail(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('process_stream_message', (event) => {
              console.log('process_stream_message', event.data)
              this.systemMessages = 'process_stream_message'
              this.handleProcessDetail(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('decision_tree_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received decision_tree_stream_message:', message)
              if (message.action !== 'end') {
                this.treeStatus = 0
              }
              this.handleProcessTreeDetail(message)
            })
            // 代码生成 BI 能力代码
            this.eventSource.addEventListener('ability_generate_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received ability_generate_stream_message:', message)
              // bi报表loading状态
              if (message.action !== 'end') {
                this.codeDataStatus = 1
              }
              this.handleAbilityDetail(message)
            })
            // BI报表、能力代码生成，生成过程流式过程
            this.eventSource.addEventListener('ability_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('代码生成过程日志:', message)
              this.handleCodeProcesData(message)
              // process loading状态
              if (message.action !== 'end') {
                this.processStatus = 1
              } else {
                this.processStatus = 2
              }
            })

            this.eventSource.addEventListener('decision_tree_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received decision_tree_process_stream_message:', message)
              this.handleProcessTreeData(message)
            })
            this.eventSource.addEventListener('scheme_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received scheme_process_stream_message:', message)
              this.handleProcessSchemeData(message) // TODO
            })
            // 数据对齐分析过程日志
            this.eventSource.addEventListener('align_data_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received align_data_process_stream_message:', message)
              this.handleAlignProcessData(message) // TODO
            })
            this.eventSource.addEventListener('error', () => {
              console.log('连接出错')
              this.agentError = true
              // this.treeStatus = 3;
              // this.abilityDataProcessStatus = 3;
              // 回到第一步
              this.stepIndex = 0
              this.systemMessages = ''
              this.changeViews(0)
            })
            this.eventSource.addEventListener('disconnect', () => {
              console.log('连接断开')
              this.agentError = true
              this.systemMessages = ''
            })
            // 代码分析流
            this.eventSource.addEventListener('code_analysis_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('代码分析-Received code_analysis_stream_message:', message)
              this.handleCodeAnalysisData(message)
            })
            // 代码分析过程流
            this.eventSource.addEventListener('code_analysis_process_stream_message', (event) => {
              // console.log('代码分析过程流', event.data);
              const message = JSON.parse(event.data)
              console.log('Received code_analysis_process_stream_message:', message)
              if (message.active === 'start') {
                this.codeAnalysisDataStatus = '1'
              }
              this.handleCodeAnalysisProcessData(message)
            })

            this.eventSource.addEventListener('common_stream_message', (event) => {
              console.log('common_stream_message 0', JSON.parse(event.data))
              this.handleSSEMessagebyRole(JSON.parse(event.data))
              // this.handleSystemMessage(JSON.parse(event.data))
            })
          }
        } else {
          this.hasChatingName = ''
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleProcessSchemeData(message) {
      if (message.action === 'start') {
        this.optionDataProcess = message.data
      } else {
        this.optionDataProcess = this.optionDataProcess + message.data
      }
      if (message.action === 'end') {
        console.log('方案生成过程的数据======', this.optionDataProcess)
      }
    },
    handleSystemMessage(message) {
      console.log('系统消息', message)
      this.systemMessages = message.data
      // 弹窗不开 不执行下一步
      if (!this.taskModalVisable) {
        return
      }
      if (message.data === 'scheme generating error') {
        this.taskStatusText = 'scheme generating error'
        this.treeStatus = 2
      }
      // 清空聊天记录成功
      if (message.data === 'clear history completed' || message.data === 'clear history error') {
        this.clearChat(message.data === 'clear history completed')
      }

      if (message.data === 'process running') {
        console.log('----正在会话中')
      }
      if (
        message.data === 'process completed' ||
        message.data === 'process error' ||
        message.data === 'scheme generating error'
      ) {
        console.log('结束----')
        this.queryTask()
      }
      if (message.data === '403 error') {
        this.refresh()
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1].content
        this.qaList = []
        startConversation({
          messages: lastMessage.parts,
          agent_role_id: this.agentAvatorInfo.id,
          session_id: this.sessionId
        })
      }
      if (message.data === 'process completed') {
        this.taskStatus = 1
        this.queryTask()
      }
      if (message.data === 'process waiting') {
        this.scrollToBottom()
      }
      if (message.data === 'scheme generating completed') {
        this.treeStatus = 2
      }
      if (message.data === 'decision tree generating completed') {
        this.treeStatus = 2
        this.flowData[0].status = 1
        this.flowData[1].status = 3
        this.flowData[2].status = 0
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        this.queryStart() // 触发数据对齐分析
      }
      if (message.data === 'decision tree generating') {
        this.treeStatus = 1
        this.flowData[0].status = 3
        this.flowData[1].status = 0
        this.flowData[2].status = 0
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        console.log('this.treeStatus思维树', this.treeStatus)
      }
      if (message.data === 'decision tree generating error') {
        this.treeStatus = 3
        this.flowData[0].status = 2
        this.flowData[1].status = 0
        this.flowData[2].status = 0
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
      }
      if (message.data === 'ability generating completed') {
        this.codeDataStatus = 2
        console.log('ability generating completed BI-CODE', message.data)
        this.flowData[3].status = 1
        this.flowData[4].status = 3
        // 触发自动部署
        this.updateDevelopFlag = this.updateDevelopFlag + 1
      }
      if (message.data === 'ability generating') {
        this.codeDataStatus = 1
        this.flowData[3].status = 3
        // {
        //   icon: 'fangan',
        //   title: '思维树生成',
        //   status: 1,
        //   runCode: 'decision_tree'
        // },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐分析',
        //     status: 1,
        //     runCode: 'align_analysis'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐',
        //     status: 1,
        //     runCode: 'align_data_generate'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码生成',
        //     status: 3,
        //     runCode: 'decision_making_generate'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码部署',
        //     status: 0,
        //     runCode: 'code_deploy'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码测试',
        //     status: 0,
        //     runCode: 'code_test'
        //   },
        //   {
        //   icon: 'fangan',
        //   title: '能力参数分析',
        //   status: 0,
        //   runCode: 'code_analysis'
        // },
        // ];
      }
      if (message.data === 'ability generating error') {
        this.codeDataStatus = 3
        this.flowData[3].status = 2
        console.log('ability generating error BI-CODE', message.data)
        // {
        //   icon: 'fangan',
        //   title: '思维树生成',
        //   status: 1,
        //   runCode: 'decision_tree'
        // },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐分析',
        //     status: 1,
        //     runCode: 'align_analysis'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐',
        //     status: 1,
        //     runCode: 'align_data_generate'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码生成',
        //     status: 2,
        //     runCode: 'decision_making_generate'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码部署',
        //     status: 0,
        //     runCode: 'code_deploy'
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码测试',
        //     status: 0,
        //     runCode: 'code_test'
        //   },
        //   {
        //   icon: 'fangan',
        //   title: '能力参数分析',
        //   status: 0,
        //   runCode: 'code_analysis'
        // },
        // ];
      }
      // 数据对齐完成
      if (message.data === 'align_data generating completed') {
        this.dataAlignDataStatus = 2
        this.flowData[2].status = 1
        console.log('align_data generating completed数据对齐完成 ======', message.data)
        // 数据对齐完成执行代码生成
        startAbilityGenerate({ ability_type: 'decision_ability', session_id: this.sessionId })
      }
      if (message.data === 'align_data generating') {
        this.dataAlignDataStatus = 1
        this.flowData[2].status = 3
      }
      if (message.data === 'align_data generating error') {
        this.dataAlignDataStatus = 3
        this.flowData[2].status = 2
        this.queryAlignData()
      }
      if (message.data === 'scheme optimize completed') {
        this.planStatus = 2
        console.log('scheme optimize completed方案优化完成 ======', message.data)
      }
      if (message.data === 'scheme optimize generating') {
        this.planStatus = 1
      }
      if (message.data === 'scheme optimize generating error') {
        this.planStatus = 3
      }
      if (message.data === 'code_analysis generating error') {
        console.log('code_analysis generating error')
        this.codeAnalysisDataStatus = '3'
        this.flowData[6].status = 2
      }
      if (message.data === 'code_analysis generating completed') {
        console.log('代码分析完成')
        this.codeAnalysisDataStatus = '2'
        this.flowData[6].status = 1
      }
      if (message.data === 'code_analysis generating') {
        console.log('代码分析中。。。')
        this.codeAnalysisDataStatus = '1'
        this.flowData[6].status = 3
      }
    },
    handleProcessTreeData(message) {
      if (message.action === 'start') {
        this.treeDataProcess = message.data
        this.treeDataProcessNodes = [{ node_id: '1', data: message.data || '' }]
      } else {
        // 如果有node_id代表我分组任务流程过程
        if (message.node_id) {
          const filtersData = this.treeDataProcessNodes.filter(
            (item) => item.node_id === message.node_id
          )
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === message.node_id) {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            this.treeDataProcessNodes = temp
          } else {
            this.treeDataProcessNodes.push({ node_id: message.node_id, data: message.data })
          }
          // console.log('分组数据', this.treeDataProcessNodes);

          const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
          // console.log('拼接后的', conData);
          this.treeDataProcess = conData
        } else {
          const filtersData = this.treeDataProcessNodes.filter((item) => item.node_id === '1')
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === '1') {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            this.treeDataProcessNodes = temp
            const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
            // console.log('拼接后的', conData);
            // console.log('分组数据', this.treeDataProcessNodes);
            this.treeDataProcess = conData
          } else {
            this.treeDataProcess = this.treeDataProcess + message.data
          }
        }
      }
      if (message.action === 'end') {
        console.log('思维树生成过程的数据======', this.treeDataProcess)
      }
    },
    handleProcessPlanDetail(message) {
      this.planStatus = 1
      if (message.action === 'start') {
        this.planData = message.data
      } else {
        this.planData = this.planData + message.data
      }
      if (message.action === 'end') {
        this.planStatus = 2
      }
    },
    handleProcessTreeDetail(message) {
      if (message.action === 'start') {
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      if (message.action === 'end') {
        this.treeStatus = 2
        console.log('思维树生成完成，不该更新taskloading吗？', this.treeStatus);

      }
    },
    handleAbilityDetail(message) {
      if (message.action === 'start') {
        this.codeData = message.data
      } else {
        this.codeData = this.codeData + message.data
      }
    },
    updateDeviceId(val) {
      this.deviceId = val
      try {
        if (val) {
          // 根据方案查询设备id
          queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.tablesList = res.data?.result || []
              console.log('设备仓库列表', this.tablesList)
              if (this.tablesList.length === 0) {
                this.tablesList = []
              } else {
                queryPointsByDeviceId({
                  device_id: res.data?.result[0].device_id,
                  scheme_id: this.$route.query.id
                }).then((pres) => {
                  this.pointsData = pres.data.result || []
                })
              }
            } else {
              this.tablesList = []
            }
          })
        }
        // 查询此设备的测点数据
      } catch (error) {}
    },
    // 获取模版ID，用于数据对齐分析
    async queryTemplate() {
      queryTempIfFromScene({ scheme_id: this.$route.query.id }).then((res) => {
        console.log('获取模版ID接口-', res.data, this.updateStart)
        this.templateId = res.data?.id || ''
      })
      queryTempIfFromScene2({ scheme_id: this.$route.query.id }).then((res) => {
        console.log('获取模版ID接口-', res.data, this.updateStart)
        this.preTemplateId = res.data?.id || ''
      })
      try {
        checkByDeviceId({ scheme_id: this.$route.query.id }).then((gres) => {
          if (gres.status === 200 && gres.data.code === 200) {
            // 根据方案查询设备id
            queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                this.tablesList = res.data?.result || []
                console.log('设备仓库列表', this.tablesList)
                if (this.tablesList.length === 0) {
                  this.tablesList = []
                } else {
                  queryPointsByDeviceId({
                    device_id: res.data?.result[0].device_id,
                    scheme_id: this.$route.query.id
                  }).then((pres) => {
                    this.pointsData = pres.data.result || []
                  })
                }
              } else {
                this.tablesList = []
              }
            })
          }
        })
      } catch (error) {}

      queryDuiqiTags({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data
          this.allTagList = res.data
          console.log('api列表', res.data)
        } else {
          this.tagList = []
          this.allTagList = []
        }
      })
    },
    // 方案明细变更后传递到外层处理的
    async handleUpdateScheme(text) {
      this.planData = text
    },
    async handleUpdateTreeData(text) {
      console.log('接收最新的思维树内容', text)
      this.treeData = text
    },
    // 更新任务列表数据,主要用在代码部署、代码测试更新状态
    updateFlowData(ndata) {
      console.log('此处子更新了父的flowData', ndata)
      this.flowData = ndata
    },
    // 终止流任务
    stopAbilityGen() {
      this.miniFlag = false
      this.taskModalVisable = false
      startStopThinking({ session_id: this.sessionId })
      console.log('---source', source)
      source.cancel()
      source = cancelToken.source()
      this.eventSource && this.eventSource.close()
      if (this.flowData[0].status === 3) {
        this.flowData[0].status = 2
        this.flowData[1].status = 0
        this.flowData[2].status = 0
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
      }
      // 终止的是数据分析
      if (this.flowData[1].status === 3) {
        this.flowData[1].status = 2
        this.flowData[2].status = 0
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        const temp3 = []
        console.log('根据设备id查询测点数据', this.tablesList)
        this.tablesList
          .filter((item) => this.formData.chooseData.includes(item.device_name))
          .map(async (item) => {
            temp3.push(item?.model_type ? item.device_id : '')
          })
        saveAlignType({
          align_type: {
            table: temp3,
            status: 2,
            tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
            manual_input: this.formData.manual_input,
            ansData: JSON.stringify(this.runStreamList)
          },
          scheme_id: this.$route.query.id
        })
        console.log('停止数据分析', this.flowData)
      }
      // 终止的是数据对齐
      if (this.flowData[2].status === 3) {
        this.flowData[2].status = 2
        this.flowData[3].status = 0
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        console.log('停止数据对齐', this.flowData)
      }
      // 终止的是代码生成
      if (this.flowData[3].status === 3) {
        this.flowData[3].status = 2
        this.flowData[4].status = 0
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        console.log('停止代码生成', this.flowData)
      }
      // 终止的是代码部署
      if (this.flowData[4].status === 3) {
        this.flowData[4].status = 2
        this.flowData[5].status = 0
        this.flowData[6].status = 0
        console.log('停止代码部署', this.flowData)
      }
      if (this.flowData[5].status === 3) {
        this.flowData[5].status = 2
        this.flowData[6].status = 0
        console.log('停止代码测试', this.flowData)
      }
      // 终止的是代码分析
      if (this.flowData[6].status === 3) {
        this.flowData[6].status = 2
        console.log('停止代码生成', this.flowData)
      }
      this.processStatus = 0
      setTimeout(() => {
        this.getChatIsUseing()
        this.querySchcemTask()
      }, 1000)
    },
    reAlignData() {
      const temp2 = []
      const temp3 = []
      console.log('根据设备id查询测点数据', this.tablesList)
      this.tablesList
        .filter((item) => this.formData.chooseData.includes(item.device_name))
        .map(async (item) => {
          const promise1 = queryPointsByDeviceId({
            device_id: item.device_id,
            scheme_id: this.$route.query.id
          })
          temp3.push(item?.model_type ? item.device_id : '')
          temp2.push(promise1)
        })
      // cosnole.log('resultF', resultF);
      const table = [...temp3]
      let rtemp = []
      Promise.all(temp2).then((results) => {
        console.log('获取测试数据方法results==', results, table)
        results.forEach((resList, rindex) => {
          if (resList) {
            const newarr = rtemp.concat(resList.data?.result || [])
            rtemp = newarr
          }
        })
        this.$nextTick(() => {
          console.log('rtemp', rtemp)
          console.log('table---', table, this.formData.tag_ids)
          // saveAlignType 保存对齐分析结果
          saveAlignType({
            align_type: {
              table: table,
              status: 1,
              tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
              manual_input: this.formData.manual_input,
              ansData: JSON.stringify(this.runStreamList)
            },
            scheme_id: this.$route.query.id
          })
          // 自动执行数据对齐
          startAlignDataGenerate({
            // table: JSON.stringify(temp2),
            // tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
            // manual_input: this.formData.manual_input,
            session_id: this.sessionId,
            table_schema: rtemp.length ? JSON.stringify(rtemp) : '',
            align_type: {
              table: table,
              status: 1,
              tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
              manual_input: this.formData.manual_input,
              ansData: JSON.stringify(this.runStreamList)
            }
          })
        })
      })
    },
    // 从数据对齐分析开始执行
    handleReStart(val, deviceid) {
      // 通知后端这是第一次执行, 打开任务执行分窗口
      if (val === 'startrun') {
        const filter = this.flowData.filter((item) => item.status)
        if (filter.length === 0) {
          // 显示数据预处理窗口结果
          // this.alignPreVisable = true;
          // this.queryDataPreStart();
          this.handelCofirmAlignPre(true)
        } else {
          console.log('任务正在执行中')
        }
      } else {
        this.flowData = [
          {
            icon: 'fangan',
            title: '思维树生成',
            status: 1,
            runCode: 'decision_tree'
          },
          {
            icon: 'fangan',
            title: '数据对齐分析',
            status: 3,
            runCode: 'align_analysis'
          },
          {
            icon: 'fangan',
            title: '数据对齐',
            status: 0,
            runCode: 'align_data_generate'
          },
          {
            icon: 'fangan',
            title: '代码生成',
            status: 0,
            runCode: 'decision_making_generate'
          },
          {
            icon: 'fangan',
            title: '代码部署',
            status: 0,
            runCode: 'code_deploy'
          },
          {
            icon: 'fangan',
            title: '代码测试',
            status: 0,
            runCode: 'code_test'
          },
          {
            icon: 'fangan',
            title: '能力参数分析',
            status: 0,
            runCode: 'code_analysis'
          }
        ]
        // this.regenerateTree();
        this.queryStart()
      }
    },
    // 触发自动对齐分析
    async queryStart() {
      // 根据方案查询设备id
      console.log('设备仓库列表1111111111111111', this.tablesList)
      await queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.tablesList = res.data?.result || []
          console.log('设备仓库列表', this.tablesList)
          if (this.tablesList.length === 0) {
            this.tablesList = []
          } else {
            queryPointsByDeviceId({
              device_id: res.data?.result[0].device_id,
              scheme_id: this.$route.query.id
            }).then((pres) => {
              this.pointsData = pres.data.result || []
            })
          }
        } else {
          this.tablesList = []
        }
      })

      this.loading = true
      this.runStreamList = []
      await queryTaskByTemp({
        template_id: this.templateId,
        scheme_detail: this.planData, // 方案明细
        mind_tree: this.treeData, // 思维树内容
        device_info: JSON.stringify(this.tablesList[0])
      }).then(async (res) => {
        console.log('需执行的任务列表', res.data)
        this.allRunTasks = res.data.new_tasks || []
        this.runId = res.data.run_id || ''
        res.data.new_tasks.forEach(async (item, index) => {
          this.runStreamList.push({
            name: item.name,
            content: '',
            status: 'start',
            type: 1,
            ...item
          })
        })
      })
      const temp = []
      if (this.runStreamList.length) {
        this.allSuccess = false
        await this.runExcute(this.runStreamList[0].id, this.runStreamList[0].order, 0)
      }
      console.log('promise1', temp)
    },
    async runExcute(id, order, index) {
      const url = process.env.VUE_APP_AGENT_API.startsWith('/')
        ? window.location.origin +
          process.env.VUE_APP_AGENT_API +
          '/api/agent/v2/manual_task/execute'
        : process.env.VUE_APP_AGENT_API + '/api/agent/v2/manual_task/execute'
      console.log('url', url)
      await this.$axios
        .post(
          url,
          {
            template_id: this.templateId,
            run_id: this.runId,
            agent_id: this.runId,
            task_id: id,
            order: order
          },
          {
            responseType: 'stream',
            baseURL: process.env.VUE_APP_AGENT_API,
            headers: {
              whiteuservalidate: 'False'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target
              const { responseText } = xhr
              this.$nextTick(() => {
                // console.log('----流----',this.runStreamList[index])
                this.runStreamList[index].status = 'running'
                this.runStreamList[index].content = responseText
                const temp = document.getElementById('taskBox')
                if (temp) {
                  temp.scrollIntoView({ block: 'end', inline: 'nearest' })
                  temp.scrollTop = temp.scrollHeight + 200
                }
              })
            },
            onError: function (error) {
              // 处理流错误
              console.error(error)
              this.runStreamList[index].status = 'error'
              this.runStreamList[index].content = ''
              this.flowData[1].status = 2
            }
          }
        )
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流', response)
          this.runStreamList[index].status = 'success'
          this.$nextTick(() => {
            const temp = document.getElementById('taskBox')
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' })
              temp.scrollTop = temp.scrollHeight + 700
            }
          })
          if (this.runStreamList[index].name === '数据仓库对齐') {
            if (this.runStreamList[index].content) {
              const valTemp = eval(this.runStreamList[index].content)
              if (valTemp && valTemp.length) {
                this.formData.cangku = true
                const filters = this.tablesList
                  .filter((item) => valTemp.includes(item.device_id))
                  .map((item) => item.device_name)
                this.formData.chooseData = filters
                console.log('库表的选择', valTemp, filters)
                this.runStreamList[index].content = JSON.stringify(filters)
                // this.$set(this.formData, 'chooseData', filters || []);
              } else {
                this.formData.cangku = false
                this.formData.chooseData = []
              }
            } else {
              this.formData.cangku = false
              this.formData.chooseData = []
            }
          }
          if (this.runStreamList[index].name === '算法API对齐') {
            if (this.runStreamList[index].content) {
              let valTemp = []
              try {
                valTemp = JSON.parse(this.runStreamList[index].content)
              } catch (error) {
                valTemp = [this.runStreamList[index].content]
              }
              console.log('valTemp---', valTemp)
              console.log('this.allTagList', this.allTagList)
              if (valTemp && valTemp.length) {
                const filters = []
                if (Array.isArray(valTemp)) {
                  valTemp?.forEach((teItem) => {
                    const ffilter = this.allTagList.filter((tag) => tag.name === teItem)
                    if (ffilter && ffilter.length) {
                      filters.push(ffilter[0].id)
                    }
                  })
                } else {
                  const ffilter = this.allTagList.filter((tag) => tag.name === valTemp)
                  if (ffilter && ffilter.length) {
                    filters.push(ffilter[0].id)
                  }
                }

                this.formData.api = true

                this.formData.tag_ids = [...filters] || []
                this.$set(this.formData, 'tag_ids', filters || '')
                console.log('少选出的', filters, this.formData.tag_ids)
              } else {
                this.formData.api = false
                this.formData.tag_ids = []
              }
            } else {
              this.formData.api = false
              this.formData.tag_ids = []
            }
          }
          if (index + 1 < this.runStreamList.length) {
            this.$nextTick(async () => {
              await this.runExcute(
                this.runStreamList[index + 1].id,
                this.runStreamList[index + 1].order,
                index + 1
              )
            })
          } else {
            this.allSuccess = true
            this.loading = false
            console.log('最后的结果', this.runStreamList)
            this.formData.manual_input = true
            this.flowData[1].status = 1
            // this.formData.chooseData = ['1255800335015542784']; // 测试数据，正式清去掉
            this.$nextTick(() => {
              const temp = document.getElementById('taskBox')
              if (temp) {
                temp.scrollIntoView({ block: 'end', inline: 'nearest' })
                temp.scrollTop = temp.scrollHeight + 700
              }
              this.reAlignData()
            })
          }
        })
        .catch(async (err) => {
          console.log('这里收到错误了', err)
          this.allSuccess = true
          this.loading = false
          this.runStreamList[index].status = 'error'
          this.runStreamList[index].content = ''
          console.log('收到错误的结果', this.runStreamList)
          this.$nextTick(async () => {
            const temp = document.getElementById('taskBox')
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' })
              temp.scrollTop = temp.scrollHeight + 700
            }
            const temp3 = []
            console.log('根据设备id查询测点数据', this.tablesList)
            this.tablesList
              .filter((item) => this.formData.chooseData.includes(item.device_name))
              .map(async (item) => {
                temp3.push(item?.model_type ? item.device_id : '')
              })
            // saveAlignType 保存对齐分析结果
            await saveAlignType({
              scheme_id: this.$route.query.id,
              align_type: {
                table: temp3,
                status: 2,
                tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
                manual_input: this.formData.manual_input,
                ansData: JSON.stringify(this.runStreamList)
              }
            })
            // await updateLogTaskStatus({
            //   scheme_id: this.$route.query.id,
            //   task_name: 'align_analysis',
            //   task_status: 2
            // });
            this.formData.manual_input = true
            this.flowData[1].status = 2
            this.flowData[2].status = 0
            this.flowData[3].status = 0
            this.flowData[4].status = 0
            this.flowData[5].status = 0
          })
        })
    },
    async queryAlignData() {
      await queryAbilityMapping({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code * 1 === 200) {
          console.log('数据对齐结果', res)
          const configData = res.data.result?.config || {}
          const status = res.data.result.ability_status
          if (status === 'init') {
            console.log('数据对齐生成中')
            if (res.data.result?.align_type?.ansData) {
              // this.flowData[0].status = 1;
              try {
                this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData)
                console.log('对齐飞兮结果', this.runStreamList)
                if (this.runStreamList.filter((item) => item.status === 'error').length) {
                  this.flowData[1].status = 2
                } else {
                  this.flowData[1].status = 1
                }
              } catch (error) {
                this.runStreamList = []
                this.flowData[1].status = 0
              }
            } else {
              this.flowData[1].status = 0
            }
            this.flowData[2].status = 0
            this.flowData[3].status = 0
            this.flowData[4].status = 0
            this.flowData[5].status = 0
          } else if (status !== 'generating') {
            // if (res.data.result?.align_type?.ansData) {
            //   this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData) || []
            // }
            if (res.data.result?.align_type?.manual_input) {
              this.formData.manual_input = true
            } else {
              this.formData.manual_input = false
            }
            const filters = this.tablesList
              .filter((item) => res.data.result?.align_type?.table.includes(item.device_id))
              .map((item) => item.device_name)
            this.formData.chooseData = filters
            const tagtemp = []
            // 循环判断标签是否有被删除，被删除的就不显示了
            res.data.result?.align_type?.tag_ids?.forEach((titem) => {
              const filter = this.allTagList.filter((item) => item.id === titem)
              if (filter.length) {
                tagtemp.push(filter[0].id)
              }
            })
            this.formData.tag_ids = tagtemp
            try {
              this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData)
              console.log('对齐飞兮结果', this.runStreamList)
              if (this.runStreamList.filter((item) => item.status === 'error').length) {
                this.flowData[1].status = 2
              } else {
                this.flowData[1].status = 1
              }
            } catch (error) {
              this.runStreamList = []
              this.flowData[1].status = 0
            }
            // try {
            //   this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData);
            //   console.log('对齐飞兮结果', this.runStreamList);
            // } catch (error) {
            //   this.runStreamList = [];
            // }
            const dataThs = configData?.header || {}
            const dataThDatas = Object.keys(dataThs).map((item) => {
              return { name: dataThs[item], field: item }
            })
            if (dataThDatas.length) {
              const temp =
                configData?.data?.map((item) => {
                  return {
                    ...item
                  }
                }) || []
              console.log('对齐数据结果', temp)
              this.dataAlignData = temp
              this.dataAlignDataStatus = 2
              console.log('对齐飞兮结果333', res.data.result?.align_type)
              if (this.flowData[1].status !== 2) {
                this.flowData[1].status = 1
                this.flowData[2].status = 1
                this.flowData[2].process = res.data.result?.sub_content
              } else {
                this.flowData[1].status = 2
                // this.flowData[2].status = 0;
                this.flowData[2].process = res.data.result?.sub_content
              }
            } else {
              this.dataAlignData = []
              this.dataAlignDataStatus = 0
              this.flowData[2].process = res.data.result?.sub_content
            }
          }
        }
      })
    },
    async querySchcemTask() {
      await getSchemePhaseTasks({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          const mapData = {}
          res.data.result.forEach((item) => {
            mapData[item.task_name] = item.task_status
          })
          this.flowData[1].status = mapData[this.flowData[1].runCode] || 0
          this.flowData[2].status = mapData[this.flowData[2].runCode] || 0
          this.flowData[3].status = mapData[this.flowData[3].runCode] || 0
          this.flowData[4].status = mapData[this.flowData[4].runCode] || 0
          this.flowData[5].status = mapData[this.flowData[5].runCode] || 0
          this.flowData[6].status = mapData[this.flowData[6].runCode] || 0
          if (this.flowData[6].status === 1) {
            this.flowData[0].status = 1
          }

          if (mapData.align_data_generate === 1 || mapData.align_data_generate === 2) {
            this.flowData[1].status = 1
            await this.queryAlignData()
          }
          if (mapData.align_analysis === 1 && mapData.align_data_generate === 1) {
            await GetDecision({
              scheme_id: this.$route.query.id,
              scheme_status: 'decision_tree'
            }).then((res) => {
              this.treeData = res.data.result?.decision_making_content || ''
              this.treeDataProcess = res.data.result?.sub_content || ''
              // const treeData = res.data.result?.decision_making_content || '';

              if (res.data.result?.decision_making_content) {
                this.flowData[0].status = 1
                this.flowData[0].process = res.data.result?.sub_content
              }
            })
          } else {
            this.flowData[3].status = 0
            this.flowData[4].status = 0
            this.flowData[5].status = 0
            this.flowData[6].status = 0
          }
          if (mapData.decision_tree === 2) {
            this.flowData[1].status = 0
            this.flowData[2].status = 0
            this.flowData[3].status = 0
            this.flowData[4].status = 0
            this.flowData[5].status = 0
            this.flowData[6].status = 0
          }
          if (mapData.decision_tree === 0) {
            this.flowData[1].status = 0
            this.flowData[2].status = 0
            this.flowData[3].status = 0
            this.flowData[4].status = 0
            this.flowData[5].status = 0
            this.flowData[6].status = 0
          }

          if (
            mapData.decision_tree === 1 &&
            mapData.align_analysis === 1 &&
            mapData.code_analysis === 1
          ) {
            await GetDecision({
              scheme_id: this.$route.query.id,
              scheme_status: 'decision_ability'
            }).then((res) => {
              const codeData = res.data.result?.decision_making_content || ''
              if (
                res.data.result?.ext_info?.deploy_status &&
                res.data.result?.ext_info?.deploy_status === 'deployed'
              ) {
                if (this.flowData[2].status === 1) {
                  this.flowData[4].status = 1
                  this.flowData[5].status = 1
                }
                if (
                  res.data.result.ext_info.code_analysis_status ||
                  res.data.result.ext_info.code_params
                ) {
                  console.log('1已经有参数了')
                  this.flowData[6].status = 1
                  this.flowData[6].process = res.data.result.ext_info.sub_content || ''
                } else {
                  console.log('1没有参数，触发流信息')
                  this.flowData[6].status = 0
                  this.flowData[6].process = ''
                }
              }
              if (codeData) {
                if (this.flowData[2].status === 1) {
                  // console.log('代码生成结果处理', res.data.result?.sub_content);
                  this.flowData[3].status = 1
                  this.flowData[3].process = res.data.result?.sub_content
                  // console.log('代码生成结果处理---', this.flowData[3]);
                }
              } else {
                this.flowData[3].status = 0
                this.flowData[4].status = 0
                this.flowData[5].status = 0
                this.flowData[6].status = 0
              }
            })
          }
          if (this.flowData[0].status === 1) {
            console.log('---获取思维树、数据对齐内容')
            await GetDecision({
              scheme_id: this.$route.query.id,
              scheme_status: 'decision_tree'
            }).then((res) => {
              this.treeData = res.data.result?.decision_making_content || ''
              this.treeDataProcess = res.data.result?.sub_content || ''
              if (res.data.result?.decision_making_content) {
                this.flowData[0].status === 1
                this.flowData[0].process === res.data.result?.sub_content
              }
            })
          }
          console.log('通过接口同步的this.flowData', this.flowData, mapData)
        }
      })
    },
    // 关闭数据预处理窗口
    handelCofirmAlignPre(val) {
      if (val) {
        this.flowData[0].status = 1
        this.flowData[1].status = 3
        startTask({ scheme_id: this.$route.query.id })
        this.taskModalVisable = true
        this.runTaskStatus = 1
        // 变为触发生成思维树 this.queryStart();
        // this.regenerateTree();
        this.queryStart()
      }
      this.alignPreVisable = false
    },

    // 对齐分析前，数据确认窗口数据获取
    async queryDataPreStart() {
      this.loading = true
      this.runStreamList2 = []
      await queryTaskByTemp({
        template_id: this.preTemplateId,
        scheme_detail: this.planData, // 方案明细
        mind_tree: this.treeData, // 思维树内容
        metrics_info: JSON.stringify(this.pointsData)
      }).then(async (res) => {
        console.log('数据确认需执行的任务列表', res.data)
        this.allRunTasks2 = res.data.new_tasks || []
        this.runId2 = res.data.run_id || ''
        res.data.new_tasks.forEach(async (item, index) => {
          this.runStreamList2.push({
            name: item.name,
            content: '',
            status: 'start',
            type: 1,
            ...item
          })
        })
      })
      const temp = []
      if (this.runStreamList2.length) {
        this.allSuccess = false
        await this.runPreExcute(this.runStreamList2[0].id, this.runStreamList2[0].order, 0)
      }
      console.log('promise1', temp)
    },
    async runPreExcute(id, order, index) {
      const url = process.env.VUE_APP_AGENT_API.startsWith('/')
        ? window.location.origin +
          process.env.VUE_APP_AGENT_API +
          '/api/agent/v2/manual_task/execute'
        : process.env.VUE_APP_AGENT_API + '/api/agent/v2/manual_task/execute'
      console.log('url', url)
      await this.$axios
        .post(
          url,
          {
            template_id: this.preTemplateId,
            run_id: this.runId2,
            agent_id: this.runId2,
            task_id: id,
            order: order
          },
          {
            responseType: 'stream',
            baseURL: process.env.VUE_APP_AGENT_API,
            headers: {
              whiteuservalidate: 'False'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target
              const { responseText } = xhr
              this.$nextTick(() => {
                // console.log('----流----',this.runStreamList[index])
                this.runStreamList2[index].status = 'running'
                this.runStreamList2[index].content = responseText
              })
            },
            onError: function (error) {
              // 处理流错误
              console.error(error)
              this.runStreamList2[index].status = 'error'
              this.runStreamList2[index].content = ''
            }
          }
        )
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流', response)
          this.runStreamList2[index].status = 'success'
          if (index + 1 < this.runStreamList2.length) {
            this.$nextTick(async () => {
              await this.runPreExcute(
                this.runStreamList2[index + 1].id,
                this.runStreamList2[index + 1].order,
                index + 1
              )
            })
          } else {
            this.allSuccess2 = true
            this.loading = false
            console.log('最后的结果', this.runStreamList2)
            // 显示数据预处理窗口结果
            this.alignPreVisable = true
          }
        })
        .catch((err) => {
          console.log('这里收到错误了', err)
          this.allSuccess2 = true
          this.loading = false
          this.runStreamList2[index].status = 'error'
          this.runStreamList2[index].content = ''
          console.log('最后的结果', this.runStreamList2)
        })
    },
    // 代码生成过程数据
    handleCodeProcesData(message) {
      if (message.action === 'start') {
        this.codeProcessData = message.data
      } else {
        this.codeProcessData = this.codeProcessData + message.data
      }
      if (message.action === 'end') {
        console.log('代码生成生成过程的数据======', this.codeProcessData)
      }
    },
    // 数据对齐过程数据
    handleAlignProcessData(message) {
      if (message.action === 'start') {
        this.alignProcessData = message.data
        this.alignProcessDataStatus = '1'
      } else {
        this.alignProcessData = this.alignProcessData + message.data
        this.alignProcessDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('数据对齐生成过程的数据======', this.alignProcessData)
        this.alignProcessDataStatus = '2'
      }
    },
    // 代码分析
    handleUpdateCodeAnalysis() {
      console.log('触发代码分析')
      startCodeAnalysis({ session_id: this.sessionId })
    },
    // 代码分析过程消息处理
    handleCodeAnalysisProcessData(message) {
      if (message.action === 'start') {
        this.codeAnalysisProcessData = message.data
        this.codeAnalysisProcessDataStatus = '1'
      } else {
        this.codeAnalysisProcessData = this.codeAnalysisProcessData + message.data
        this.codeAnalysisProcessDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======', this.codeAnalysisProcessData)
        this.codeAnalysisProcessDataStatus = '2'
      }
    },
    // 代码分析消息处理
    handleCodeAnalysisData(message) {
      if (message.action === 'start') {
        this.codeAnalysisData = message.data
        this.codeAnalysisDataStatus = '1'
      } else {
        this.codeAnalysisData = this.codeAnalysisData + message.data
        this.codeAnalysisDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======', this.codeAnalysisData)
        this.codeAnalysisDataStatus = '2'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.chatContainer {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  position: relative;
  flex-direction: column;
  .headerBox {
    background-color: #fff;
    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;
      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }
      .sence-tag {
        margin-left: 16px;
        padding: 0 8px;
        height: 24px;
        border-radius: 2px;
        max-width: calc(100vw - 380px);
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
        background: #ebf9ff;
        color: #318db8;
      }
      .right {
        .model-expert {
          margin-right: 10px;
          font-size: 14px;
          color: #4068d4;
          font-weight: bold;
        }
      }
    }
    .headerStep {
      position: relative;
      .myStep {
        background: #fff;
        padding: 13px 20%;
        :deep(.el-step__arrow) {
          margin: 0 16px;
          &::before {
            // content: '';
            // position: static;
            // height: 1px;
            // width: 100%;
            // background: #c8c9cc;
            // transform: none;
            // display: block;
            display: none;
          }
          &::after {
            display: none;
          }
        }
        :deep(.is-process) {
          color: #4068d4;
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: none;
            }
          }
        }
        :deep(.is-success) {
          color: #000;
          border-color: #4068d4;
          .el-icon-check {
            color: #4068d4;
          }
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: 1px solid #4068d4;
            }
          }
        }
        .empty-space {
          width: 100%;
          height: 100%;
        }
      }
      .myProgress {
        padding: 13px 20%;
        width: 100%;
        position: absolute;
        z-index: 999;
        top: 0;
        :deep(.el-progress) {
          margin: 0 194px 0 110px;
        }
        :deep(.el-progress-bar__inner) {
          background: linear-gradient(149deg, #b1c6ff 0%, #2056ea 100%);
        }
        :deep(.el-progress-bar__innerText) {
          margin-top: -5px;
        }
      }
      .progressEmpty {
        :deep(.el-progress-bar__innerText) {
          color: #406bd4;
        }
      }
    }
  }
  &.chatContainerFrame {
    height: 100%;
  }
}
.stepBox {
  background: #ebeffa;
  border-radius: 2px;
  border: 1px solid #4068d4;
  padding: 8px 20px;
  margin: 16px 16px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #323233;
  .stepWrap {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .item {
    display: flex;
    align-items: center;
  }
  .full {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  display: flex;
  align-items: center;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
</style>
