<template>
    <div :class="$store.state.planGenerate.isIframeHide ? 'containerBox2 containerBox2IFrame' : 'containerBox2'" style="width: 100%;">
      <div id="left-content" :style="{ width: leftWidth,maxWidth: leftWidth, marginRight: !rightFullFlag ? '0px' : '16px', userSelect: isDragging ? 'none' : 'auto', transition: isDragging ? 'none' : 'width 0.2s', position: thinkFullFlag ? '' : 'relative'}" :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'">
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">思维树</div>
            <div class="rightTitleOpt">
              <el-tooltip class="item" effect="dark" :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'" placement="top">
                <el-button type="info" size="mini" @click="changeShowType"><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png"/><img v-else src="@/assets/images/planGenerater/markdown.png"/></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="rightFullFlag ? '退出全屏' : '全屏'" placement="top">
                <el-button :type="rightFullFlag ? 'primary': 'info'" size="mini" @click="changeShowFull"><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/></el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="optScroll">
            <div class="optContentBox">
              <div v-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
                <MyEditor id="MyEditor5" ref='MyEditor5' :md-content="treeData"></MyEditor>
                <!-- <pre v-if="(treeData && treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
                <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
              </div>
              <div v-else>
                <pre>{{treeData}}</pre>
              </div>
            </div>
          </div>
        </div>
        <div class="optFooter">
          <el-button class="button-last" :disabled="hasChatingName !== ''" type="primary" @click="(treeStatusLast === 1 || treeStatusLast === 0) ? stopGenerate() : regenerate()">{{treeStatusLast === 1 || treeStatusLast === 0  ?'终止生成':(codeData !== '' ? '重新生成' : '生成')}}</el-button>
        </div>
      </div>
      <div v-if="planDetailShow && !rightFullFlag" id="resize" class="resize" title="收缩侧边栏" @mousedown="startDrag">
        <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
        <div class="el-two-column__trigger-icon"><SvgIcon name="dragborder" class="process-icon" /></div>
        <div class="el-two-column__icon-bottom"><div class="el-two-column__icon-bottom-bar"></div></div>
      </div>
      <div id="right-content" :style="{ width: rightWidth, marginRight: '16px', transition: isDragging ? 'none' : 'width 0.2s', userSelect: isDragging ? 'none' : 'auto'}" :class="!planDetailShow ? 'chatRight chatRightFull': 'chatRight'">
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">能力代码生成</div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
                <el-button type="info" :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName !== ''" size="mini" @click="() => {hisCode = codeData; isEdit = true; options.readOnly = false;}"><img src="@/assets/images/planGenerater/bianji.png"/></el-button>
              </el-tooltip>
              <!-- <el-button v-if="!isEdit" type="info" :disabled="treeStatusLast === 1 || treeStatusLast === 0" @click="() => {hisCode = codeData; isEdit = true}">编辑</el-button> -->
              <template v-if="isEdit">
                <el-tooltip class="item" effect="dark" content="保存" placement="top">
                  <el-button type="info" size="mini"  :disabled="treeStatusLast === 1 || treeStatusLast === 0 ||codeData ===''" @click="handleCodeSave"><img src="@/assets/images/planGenerater/baocun.png"/></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top">
                  <el-button type="info" size="mini"  @click="handleCodeSaveClose"><img src="@/assets/images/planGenerater/quxiao.png"/></el-button>
                </el-tooltip>
                <!-- <el-button type="primary" :disabled="treeStatusLast === 1 || treeStatusLast === 0 ||codeData ===''" @click="handleCodeSave">保存</el-button> -->
                <!-- <el-button type="info" @click="handleCodeSaveClose">取消</el-button> -->
              </template>
              <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
                <el-button type="info" @click="showSikao">生成过程</el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="复制" placement="top">
                <el-button type="info" @click="copyText">复制</el-button>
              </el-tooltip> -->
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
                <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                    <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/>
                  </el-button>
              </el-tooltip>
              <el-dropdown style="margin-left: 10px;" @command="handleCommand">
                <el-button type="info" :disabled="hasChatingName !== ''" size="mini"><img src="@/assets/images/planGenerater/more.png"/></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :disabled="isCacheDisabled ||treeStatusLast === 1 || treeStatusLast === 0" :command="isCache?'removeCache':'addCache'">{{ isCache?'取消缓存':'缓存' }}</el-dropdown-item>
                  <el-dropdown-item command="sikao">代码生成日志</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                  <el-dropdown-item command="down">代码下载</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div v-loading="taskLoading" class="optScroll" element-loading-text="能力代码生成中..." element-loading-spinner="el-icon-loading">
            <!-- <template v-if="isEdit">
              <div class="optContentBox">
                <el-input id="detail-content" v-model="codeData" type="textarea" :autosize="{ minRows: 10}" placeholder="请输入"/>
              </div>
            </template> -->
            <template>
              <!-- <div v-if="treeStatusLast === 1 || treeStatusLast === 0" style="width: 100%;">
                <div>{{codeData}}</div>
              </div> -->
              <div v-if="treeStatus === 3" class="optContentBox">
                <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                  <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto"/>
                  <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">能力代码生成失败，请<el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''" @click="regenerate()">重试</el-link></div>
                </div>
                <!-- <el-alert :closable="false" type="error">
                  <span slot="title">能力生成失败<el-link :underline="false" @click="regenerate()">重试</el-link></span>
                </el-alert> -->
              </div>
              <div v-else>
                <div id="optContentBox" class="optContentBox">
                  <MonacoEditor
                    id="optContentBoxMd"
                    class="editor"
                    :value="codeData"
                    language="python"
                    :scroll-beyond-last-line="true"
                    :original="codeDataOri"
                    theme="vs-dark"
                    :diff-editor="false"
                    :options="options"
                    @editorDidMount="editorMounted"
                    @change="onContentChange"
                  />
                  <!-- <vue-markdown v-highlight id="optContentBoxMd" :source="codeData" class="markdown-body"></vue-markdown> -->
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="optFooter">
          <el-button :loading="deployLoading" :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName!=='' || deployLoading || ploading" :type="deployStatus ? 'success' : 'primary'" @click="bushuFun">部署</el-button>
          <el-button type="primary" :disabled="treeStatusLast === 1 || treeStatusLast === 0 || !deployStatus || hasChatingName!=='' || ploading || deployLoading" @click="testFun">代码测试</el-button>
          <el-button class="button-last" :disabled="treeStatusLast === 1 || treeStatusLast === 0 || ploading || deployLoading" type="primary" @click="changeViews(1)">上一步</el-button>
           <!-- TODO :disabled="treeStatusLast === 1 || treeStatusLast === 0 ||codeData ==='' || ploading || deployLoading" -->
          <el-button class="button-last" :disabled="treeStatusLast === 1 || treeStatusLast === 0 ||codeData ==='' || ploading || deployLoading"  type="primary" @click="changeViews(3, 'next')">下一步</el-button>
        </div>
      </div>
      <treeProcess ref="chatScrollBox" :is-visible="processVisable" :tree-process-val="codeProcess" :title-val="'代码生成日志'" @close="closeSikaoRizhi"/>
      <nengliTestReult :is-visible="testVisable" :tree-process-val="testResult" @close="() => {testVisable = false;testReq='';testResult=''}"/>
      <codeAnalysis ref="chatScrollBox2" :is-visible="codeAnalysisProcessStatus" :tree-process-val="codeAnalysisProcess" :title-val="'代码分析日志'" @close="handleNext"/>
    </div>
  </template>

  <script>
  import { mapGetters } from 'vuex';
  import {
    CodeEdit,
    GetDecision,
    AbilityPublish,
    AbilityTest,
    addCache,
    removeCache,
    queryCache,
    getCodeZipBySchemeIdCode,
    querySchemeDetailById
  } from '@/api/planGenerateApi.js'
  import { Transformer } from 'markmap-lib'
  import { Markmap } from 'markmap-view'
  import treeProcess from '../treeProcess.vue';
  import nengliTestReult from '../nengliTest.vue';
  import panzoom from 'panzoom';
  import MonacoEditor from '@/components/MonacoEditor'
  import MyEditor from '../mdEditorPreview.vue';
  import codeAnalysis from '../codeAnalysis.vue';
  import JSZip from 'jszip';
  import { saveAs } from 'file-saver';
  export default {
    components: {
      treeProcess,
      nengliTestReult,
      MonacoEditor ,
      MyEditor,
      codeAnalysis
    },
    props: {
      agentSenceCode: {
        type: String,
        default: '',
      },
      treeDataVal: {
        type: String,
        default: '',
      },
      treeProcessVal: {
        type: String,
        default: '',
      },
      treeProcessStatus: {
        type: String,
        default: '',
      },
      treeStatus:{
        type: Number,
        default: -1,
      },
      hasChatingName: {
        type: String,
        default: '',
      },
      codeAnalysisData: {
        type: String,
        default: '',
      },
      codeAnalysisDataStatus: {
        type: String|Number,
        default: '',
      }
    },
    data() {
      return {
        testdata:'graph TD\nA([开始]) --> B1{检查电源插座是否有电}\nB1 -->|有电| B2[检查家里主电源开关是否打开]\nB2 -->|打开| C1[检查家庭电路中断器是否跳闸]\nC1 -->|跳闸| E1[复位中断器-然后检查电是否恢复]\nE1 -->|电恢复| END([结束])\nE1 -->|电未恢复| F[联系专业电工进行故障排查和维修]\nC1 -->|没有跳闸| D1[检查电路配线是否受损]\nD1 -->|受损| E2[修复或更换电路线路-然后检查电是否恢复]\nE2 -->|电未恢复| F\nD1 -->|未受损| F\nB2 -->|没有打开| B3[打开电源开关-然后检查电是否恢复]\nB3 -->|电未恢复| F\nB1 -->|没有电| C1',
        isSuperAdmin: false, // 是否超级管理员
        rules: {
          name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
        },
        hisDetail: '',
        processContent: {text: ''},
        processList: [],
        taskList: [],
        ploading: false,
        tableData: {
          list: [], // 表格数据
          page: 1,
          pageSize: 10,
          total: 0
        },
        currentText: '',
        socket: null,
        systemMessages: '',
        taskStatusText: '',
        agentError: false,
        taskStatus: 0,
        isDragging: false,
        leftWidth: '50%',
        rightWidth: '',
        totalWidth: 1000,
        isEdit: false,
        taskGeneratorStatus: '',
        planDetailShow: true,
        rightFullFlag: false,
        thinkFlag: false,
        thinkFullFlag: false,
        taskLoading: false,
        writeFlag: true,
        writeText: '',
        greets: '',
        taskGenType: '',
        sessionId:'',
        treeData:'',
        codeData:'',
        codeDataOri: '',
        codeProcess: '',
        jueceYulanFlag: true,
        processVisable: false, // 思考过程弹窗标志
        hisCode: '', // 编辑能力代码生成代码时的数据
        testVisable: false, // 能力测试窗口
        testReq: '', // 能力测试请求参数
        testResult: '', // 能力测试结果
        treeStatusLast: 0,
        panZoomRef: null,
        redoFlag: false, // 是否重新生成、编辑过
        deployStatus: false, // 是否部署过
        isCache:false,
        isCacheDisabled:true,
        editorMan: null,
        options: {
          readOnly: true,
          lineNumbers: true,
          fontSize: 15,
          mouseStyle: 'default',
          colorDecorators: true,
          foldingStrategy: 'indentation', // 代码可分小段折叠
          automaticLayout: true, // 自适应布局
          overviewRulerBorder: false, // 不要滚动条的边框
          autoClosingBrackets: true,
          renderLineHighlight: 'all',
          wordWrap: 'on',
          scrollBeyondLastLine: true,
          tabSize: 4, // tab 缩进长度
          minimap: {
            enabled: true // 不要小地图
          },
          fontFamily:
            'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
          folding: true
        },
        deployLoading: false, // 部署loading
        ext_info: {},
        codeAnalysisProcessStatus: false,
        codeAnalysisProcess: ''
      };
    },
    computed: {
      ...mapGetters({
        isAdmin: 'common/getIsAdminGetter'
      }),
    },
    watch: {
      treeStatus: {
        handler(val) {
          this.treeStatusLast = Number(val);
          if(val === 2){
            this.taskLoading = false
            this.queryDecision('decision_ability') // 能力代码生成
          }else if(val === 0){
            console.log('0');
            this.taskLoading = false
          }
          else if(val === 3){
            this.taskLoading = false
            this.processVisable = false;
          }

        },
        immediate: true
      },
      treeDataVal: {
        handler(val) {
          this.codeData = val
          this.$nextTick(() => {
            console.log('Mon',  this.editorMan);
            if ( this.editorMan) {
              console.log('this.editorMan.getModel().getLineCount()', this.editorMan.getModel().getLineCount() );
              this.editorMan.revealLineInCenterIfOutsideViewport(this.editorMan.getModel().getLineCount());
            }
          })

          // const target = document.getElementById('optContentBox')
          // const target2 = document.getElementById('optContentBoxMd')
          // console.log('target.scrollTop', target, target.scrollHeight);
          // if (target) {
          //   target2.scrollTop = target.scrollHeight - 40;
          //   console.log('target2.scrollTop', target2.scrollTop, target);
          //   //target.scrollTop = target.scrollHeight - target.clientHeight + 30;
          // }
        },
        immediate: true
      },
      treeProcessVal: {
        handler(val) {
          this.codeProcess = val
          // this.$nextTick(() => {
          //   console.log(this.$refs.chatScrollBox,'aaaaa')
          //   console.log(this.$refs.chatScrollBox.$refs.chatBox.scrollHeight,'bbbbbb')
          //   this.$refs.chatScrollBox.scrollFn()
          //   console.log(this.$refs.chatScrollBox.$refs.chatBox.scrollTop,'cccccc')
          // });
        },
        immediate: true
      },
      treeProcessStatus: {
        handler(val) {
          if (Number(val) === 1) {
            this.processVisable = true;
          } else {
            this.processVisable = false;
          }
        },
        immediate: true
      },
      codeAnalysisData: {
        handler(val) {
          this.codeAnalysisProcess = val
        },
        immediate: true
      },
      codeAnalysisDataStatus: {
        handler(val) {
          console.log('状变化', val);
          if (Number(val) === 1) {
            this.ploading = false;
            this.codeAnalysisProcessStatus = true;
          } else {
            this.codeAnalysisProcessStatus = false;
          }
          if (Number(val) === 2 || Number(val) === 3) {
            this.ploading = false;
            this.handleNext();
          }
        },
        immediate: true
      },
    },
    async created() {

    },
    beforeDestroy() {
    },
    // 生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
      // console.log('宽度', this.rightWidth);
      this.taskStatus = this.$route.query.status;
      const nodeMarkmap = document.getElementById('markmap');
      if(nodeMarkmap) {
        nodeMarkmap.innerHTML = '';
      }
      await this.queryDecision('decision_tree') // 思维树
      await this.queryDecision('decision_ability') // 能力代码生成
    },
    methods: {
      async queryCacheHandle () {
      queryCache({scheme_id: this.$route.query.id,ability_name:'code_generate'}).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          if(res.data.result){
            this.isCache = res.data.result.isCache;
            this.isCacheDisabled = false
          }

        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    async handleCommand(command) {
      this.codeGenerateZhuge(command === 'sikao' ? '思考' : command === 'copy' ? '复制' : '代码下载')
      if (command === 'sikao') {
        this.showSikao();
      } else if (command === 'addCache') {
        addCache({scheme_id: this.$route.query.id, ability_name: 'code_generate'}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '新增成功'
            });
            this.isCache = !this.isCache
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      } else if (command === 'removeCache') {
        removeCache({scheme_id: this.$route.query.id, ability_name: 'code_generate'}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '删除成功'
            });
            this.isCache = !this.isCache
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
      } else if (command === 'down') {
        const res = await getCodeZipBySchemeIdCode(this.$route.query.id, this.codeData)
        if (res?.data?.code === 200) {
          const zip = new JSZip()
          for (const item of res.data.result) {
            zip.file(item.name, item.content)
          }
          zip.generateAsync({ type: 'blob' }).then((blob) => {
            saveAs(blob, this.$route.query.id + '.zip');
          });
        }else{
          this.$message({
            type: 'error',
            message: res.data.msg
          });
        }
      } else {
        this.copyText();
      }
    },
      copyText() {
        // 获取需要复制的文本
        const text = this.codeData;
        navigator.clipboard.writeText(text)
        .then(() => {
          this.$message({
              type: 'success',
              message: '复制成功！'
            });
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          });
        });
      },
      fangda (e) {
        // console.log('开启缩放', e.target.getElementsByTagName('svg'));
        const svgdoms = e.target.getElementsByTagName('svg');
        const arr = [...svgdoms];
        arr.forEach((svgdom) => {
          if (svgdom.id.indexOf('mermaid') > -1) {
            panzoom(svgdom, {
              smoothScroll: false,
              bounds: true,
              // autocenter: true,
              zoomDoubleClickSpeed: 1,
              minZoom: 0.1,
              maxZoom: 20,
            })
          }
        })
      },
      // 显示生成过程
      showSikao () {
        console.log('生成过程显示', this.codeProcess);
        this.processVisable = true;
      },
      closeSikaoRizhi () {
        this.processVisable = false;
      },
      // 部署方法
      async bushuFun () {
        // console.log('部署');
        if (this.codeData !== '') {
          this.deployLoading = true;
          // 调用接口
          const res = await AbilityPublish({
            code_str: this.codeData,
            scheme_id: this.$route.query.id
          });
          if(res?.data?.code !== 200){
            this.$message.error(res?.data?.msg || '部署失败');
            this.deployLoading = false;
            return;
          }
          this.$message.success('能力代码生成部署成功');
          this.redoFlag = false;
          this.deployStatus = true;
          this.deployLoading = false;
          this.codeGenerateZhuge('部署')
        }

      },
      // 测试方法
      async testFun () {
        if (this.codeData !== '') {
          this.testVisable = true
          // 调用接口
          const res = await AbilityTest({
            code_str: this.codeData,
            scheme_id: this.$route.query.id
          });
          if(res?.data?.code !== 200){
            this.$message.error(res?.data?.msg || '测试失败');
          } else {
            this.testResult = res.data.result?.resp || '';
          }
          this.codeGenerateZhuge('代码测试')
        }
      },
      onContentChange (newVal) {
        console.log('代码长度', newVal.length);
        if (newVal.length <= 65000) {
          this.codeData = newVal
        } else {
          this.codeData = newVal.slice(0, 65000)
          this.editorMan.setValue(newVal.slice(0, 65000));
          this.$message.error('代码最多65000个字符！');
        }
      },
      editorMounted (value) {
        console.log('初始化', value);
        this.editorMan = value;
      },
      // 关闭保存
      handleCodeSaveClose () {
        this.codeData = this.hisCode;
        this.isEdit = false;
        this.options.readOnly = true;
        this.codeGenerateZhuge('编辑')
      },
      // 编辑能力代码生成保存
      async handleCodeSave () {
        this.isEdit = false;
        this.options.readOnly = true;
        // console.log('修改后的代码', this.codeData);
        // 调用接口
        const res = await CodeEdit({
          text: this.codeData,
          scheme_status: 'decision_ability',
          scheme_id: this.$route.query.id
        });
        if(res?.data?.code !== 200){
          this.$message.error(res?.data?.msg || '编辑失败');
          return;
        }
        this.$message.success('编辑成功');
        this.redoFlag = true;
        // 编辑成功后重新查询一次最新能力代码生成代码
        await this.queryDecision('decision_ability') // 能力代码生成
      },
      // 预览
      yuLan(){
        this.jueceYulanFlag = !this.jueceYulanFlag
        if (this.treeData && (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1)) {
          console.log('--');
        } else {
          if(this.jueceYulanFlag){
            this.thinkingHandle()
          }
        }
      },
      changeShowType () {
        this.jueceYulanFlag = !this.jueceYulanFlag;
      },
      thinkingHandle(){
        if (this.treeData) {
          if (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1) {
            console.log('--');
          } else {
            const transformer = new Transformer()
            const { root } = transformer.transform(this.treeData)
            this.$nextTick(() => {
              Markmap.create('#markmap',null,root)
            })
          }
        }
      },
      changeViews(val, type){
        this.codeGenerateZhuge(type === 'next' ? '下一步' : '上一步')
        if ((this.redoFlag || !this.deployStatus) && type === 'next') {
          this.$message.warning('请重新部署后再进行下一步！');
        } else {
          if (type) {
            // 只有物联场景\人工场景、通用走代码分析
            if (this.ext_info.code_analysis_status || this.ext_info.code_params) {
              console.log('已经有参数了', this.ext_info);
              this.$emit('updateStep',val)
            } else {
              console.log('没有参数，触发流信息', this.ext_info);
              this.ploading = true;
              this.$emit('updateCodeAnalysis')
            }

          }else {
            console.log('上一步', val);
              this.$emit('updateStep',val)
          }
        }
      },
      handleNext() {
        console.log('下一步');
        this.codeAnalysisProcessStatus = false;
        this.$emit('updateStep',3)
      },
      // 终止生成
      stopGenerate () {
        this.treeStatusLast = 2; // 还原到完成状态
        this.codeProcess = '';
        this.taskLoading = false;
        this.processVisable = false;
        this.$emit('stopAbilityGen')
        this.queryDecision('decision_ability') // 能力代码生成
        this.codeGenerateZhuge('终止生成')
      },
      regenerate(){
        this.codeData = ''
        this.codeProcess = '';
        this.taskLoading = true;
        this.redoFlag = true;
        this.$emit('updateCodeGenerate','decision_ability')
        this.codeGenerateZhuge('重新生成')
      },
      codeGenerateZhuge(btnName){
        querySchemeDetailById({scheme_id: Number(this.$route.query.id)}).then(res => {
            const name = res.data.result.name;
          })
    },
      async queryDecision(status){
        await GetDecision({scheme_id: this.$route.query.id,scheme_status:status}).then((res) => {
          this.treeStatusLast = 2;
          if (res.status === 200 && res.data.code === 200) {
            if(status === 'decision_ability'){
              this.codeData = res.data.result?.decision_making_content || '';
              this.codeDataOri = res.data.result?.decision_making_content || '';
              this.codeProcess = res.data.result?.sub_content || '';
              this.queryCacheHandle()
              if(res.data.result?.ext_info?.deploy_status && res.data.result?.ext_info?.deploy_status === 'deployed') {
                this.deployStatus = true;
              } else {
                this.deployStatus = false;
              }
              this.ext_info = res.data.result?.ext_info || {code_analysis_status: false,code_params:null}
            }else{
              this.treeData = res.data.result?.decision_making_content ||'';
            }
            if(status === 'mind_map' && this.treeData){ // 思维图
              this.thinkingHandle()
            }else if(status === 'mind_map' && !this.treeData){
              const nodeMarkmap = document.getElementById('markmap');
              if(nodeMarkmap) {
                nodeMarkmap.innerHTML = '';
              }
            }

          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      },
      startDrag(event) {
        if (!this.isDragging) {
          this.isDragging = true;
          this.startX = event.clientX;
          // console.log('this.startX', this.startX, this.rightWidth);
          const leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
          this.startWidth = leftWidth;
          document.addEventListener('mousemove', this.onDrag);
          document.addEventListener('mouseup', this.stopDrag);
        }
      },
      onDrag(event) {
        if (this.isDragging) {
          const deltaX = event.clientX - this.startX;
          const widthLeft = this.startWidth + deltaX;
          // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
          this.leftWidth = widthLeft + 'px';
          this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
        }
      },
      stopDrag() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.onDrag);
        document.removeEventListener('mouseup', this.stopDrag);
      },
      getWsID () {
        let workspaceId = '';
        // console.log('ceshi', router?.currentRoute?.query)
        if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
          workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
        } else {
          workspaceId = this.$router?.currentRoute?.query.workspaceId
        }
        if(!workspaceId) {
          try {
            const [hash, query] = window.location.href.split('#')[1].split('?')
            const params = Object.fromEntries(new URLSearchParams(query))
            workspaceId = params.workspaceId
          } catch (error) {
            console.log('error', error)
          }
        }
        return workspaceId
      },

      changeShowRight () {
        this.planDetailShow = !this.planDetailShow
        if (this.planDetailShow) {
          this.rightWidth = '';
          this.leftWidth = '50%'
        } else {
          this.rightWidth = '';
          this.leftWidth = '0px'
        }
      },
      changeShowFull () {
        this.rightFullFlag = !this.rightFullFlag;
        if(this.rightFullFlag) {
          this.leftWidth = '100%'
          this.rightWidth = '0';
        } else {
          this.leftWidth = '50%'
          this.rightWidth = '100%';
        }
      },
      changeThinkFull () {
        this.thinkFullFlag = !this.thinkFullFlag;
      },

    }
  };
  </script>
  <style lang="scss" scoped>
  :deep(.el-loading-spinner) {
    width: 130px !important;
    background: none !important;
  }
  .editor {
    height: 100%;
    width: 100%;
    margin-left: 1px;
  }
  .containerBox2{
    &.containerBox2IFrame {
      height: 100%;
      .containerBox {
        height: calc(100vh - 104px) !important;
        max-height: calc(100vh - 104px) !important;
      }
      .containerCardFull {
        top: -16px !important;
        height: calc(100% - 0px) !important;
        max-height: calc(100% - 0px) !important;
      }
      .fanganyouhua {
        background: #fff;
        top: 0px !important;
        height: calc(100vh - 0px) !important;
        max-height: calc(100vh - 0px) !important;
      }
      .chatRightFull {
        top: -16px !important;
        height: 100vh !important;
        max-height: 100vh !important;
      }
      .optScroll {
        height: calc(100vh - 220px) !important;
        max-height: calc(100vh - 220px) !important;
      }
      .chatRight .optScroll {
        height: calc(100vh - 220px) !important;
        max-height: calc(100vh - 220px) !important;
        .optContentBox {
          height: calc(100vh - 220px) !important;
          max-height: calc(100vh - 220px) !important;
        }
      }
    }
  }
  .chatContainer {
    height: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    &.containerCardFull {
      position: fixed !important;
      top: 32px;
      z-index: 2005;
      height: calc(100% - 50px) !important;
      max-height: calc(100% - 50px) !important;
      width: 100%;
      left: 0px;
      width: 100%;
      margin-left: 0px !important;
      .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }
      .optContentBox {
        width: 100%;
        min-height:calc(100vh - 150px) !important;
      }
    }
    .optContentBox {
      width: 100%;
      min-height:calc(100vh - 330px);
      max-height: 100%;
      display: flex;
    }
    .headerBox{
      background-color: #fff;
      .headerTitle {
        padding: 14px 20px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #EBECF0;
        .title {
          font-weight: bold;
          color: #323233;
          line-height: 26px;
          font-size: 18px;
        }
      }
    }

    .containerBox2 {
      display: flex;
      flex-direction: row;
      height: calc(100%);
      max-height: calc(100%);
      overflow-y: hidden;
      position: relative;
      .showRightFix {
        position: absolute;
        right: 6px;
        top: 24px;
        width: 30px;
        height: 30px;
        background: #4068D4;
        border-radius: 2px;
        text-align: center;
        line-height: 27px;;
        z-index: 2;
        color: #fff;
        cursor: pointer;
        &:hover{
          background: #3455ad;
        }
        &:active{
          background: #264480;
        }
        img {
          width: 12px;
          height: auto;
        }
      }
      .containerCard {
        //height: calc(100% - 18px);
        // max-height: calc(100vh - 210px);
        overflow-y: hidden;
        overflow-x: hidden;
        margin: 16px 16px 0px 0px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        background-color: #fff;
        margin-left: 16px;
        &.containerCardFull {
          position: fixed !important;
          top: 32px;
          z-index: 2005;
          height: calc(100% - 50px) !important;
          max-height: calc(100% - 50px) !important;
          width: 100%;
          left: 0px;
          width: 100%;
          margin-left: 0px !important;
          .optScroll {
            height: calc(100vh - 140px) !important;
            max-height: calc(100vh - 140px) !important;
          }
          .optContentBox {
            width: 100%;
            height:calc(100vh - 180px) !important;
          }
        }
        .optContentBox {
          width: 100%;
          min-height:calc(100vh - 330px);
        }
        .optHeader {
          padding: 0px 20px;
          border-bottom: 1px solid #EBECF0;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTitle {
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &.rightTextBtnGreen {
                background-color: #19b032 !important;
              }
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .optScroll  {
          position: relative;
          height: calc(100vh - 330px);
          max-height: calc(100vh - 330px);
          overflow-y: auto;
          overflow-x: hidden;
          padding: 20px;
          display: flex;
          ::v-deep .el-textarea{
            margin-bottom: 10px;
          }
          .btn{
            position: absolute;
            bottom: 0;
            right: 20px;
          }
        }
        .optContent {
          max-height: calc(100% - 60px);
          overflow-y: hidden;
        }
        .optFooter {
          position: absolute;
          bottom: 0px;
          left: 0px;
          width: 100%;
          background: #FFFFFF;
          box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 12px 20px;
          min-height: 54px;
      }
        .chatHeader {
          font-size: 14px;
          color: #323233;
          line-height: 24px;
          font-weight: bold;
          background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
          background-size: 100% 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          padding: 0px 20px;
          .rightTitle {
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &.rightTextBtnGreen {
                background-color: #19b032 !important;
              }
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .thinkContent  {
          margin-left: 16px;
          width: calc(100% - 32px);
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          max-height: 225px;
          height: 225px;
          overflow-y: auto;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #DCDDE0;
          transition: height 0.1s;
          &.thinkContentFull {
            position: absolute !important;
            left: 0px;
            width: calc(100vw - 214px) !important;
            height: calc(100vh - 150px) !important;
            overflow: hidden;
            z-index: 2;
            max-height: calc(100vh - 150px) !important;
            top: 0px;
          }
          .thinkHeader {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 12px 12px;
            .title {
              color: #323233;
              line-height: 20px;
              display: flex;
              align-items: center;
              img {
                height: 24px;
                width: 24px;
                margin-right: 4px;
              }
            }
            .thinkOpt {
              display: flex;
              .think-btn {
                font-size: 14px;
                margin-left: 4px;
                cursor: pointer;
                width: 24px;
                height: 24px;
                text-align: center;
                line-height: 22px;
                font-weight: bold;
                &.think-btn-blue {
                  background-color: #4068D4 !important;
                  border-radius: 4px;
                  &:hover{
                    background: #3455ad !important;
                  }
                  &:active{
                    background: #264480;
                  }
                }
                &:hover {
                  background-color: #ebecf0;
                  border-radius: 4px;
                }
                img {
                  width: 12px;
                  height: 12px;
                }
              }
            }
          }
          .thinkWrap {
            background: #FFFFFF;
            padding: 0px 12px 12px 36px;
            max-height: calc(100% - 40px);
            overflow-y: auto;
            .thinkItem {
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              justify-content: space-start;
              padding: 8px 12px;
              border-radius: 4px;
              border: 1px solid #DCDDE0;
              margin-top: 12px;
              &:first-child {
                margin-top: 0px;
              }
            }
            .itemContent {
              color: #646566;
              line-height: 22px;
              flex: 1;
              margin-left: 8px;
            }
          }
        }
      }
      .chatRight {
        flex: 1;
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.06);
        border-radius: 4px;
        height: calc(100% - 18px);
        max-height: calc(100% - 18px);
        overflow-y: hidden;
        margin-top: 16px;
        position: relative;
        &.chatRightFull {
          position: fixed !important;
          top: 32px;
          z-index: 2005;
          height: calc(100% - 50px);
          width: 100%;
          left: 0px;
          width: 100%;
          margin-left: 0px !important;
          .optScroll {
            height: calc(100vh - 150px) !important;
            max-height: calc(100vh - 150px) !important;
          }
          .optScroll2 {
            height: calc(100vh - 110px) !important;
            max-height: calc(100vh - 110px) !important;
          }
          .optContentBox {
            height: calc(100vh - 180px) !important;
            max-height: calc(100vh - 180px) !important;
          }
        }
        .optContentBox {
          height: calc(100vh - 340px);
          max-height: calc(100vh - 340px);
          width: 100%;
          position:relative;
          overflow-y: auto;
        }
        .optHeader {
          padding: 0px 20px;
          border-bottom: 1px solid #EBECF0;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTitle {
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &.rightTextBtnGreen {
                background-color: #19b032 !important;
              }
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .optScroll  {
          position: relative;
          height: calc(100vh - 330px);
          max-height: calc(100vh - 330px);
          overflow-y: hidden;
          overflow-x: hidden;
          padding: 20px;
          ::v-deep .el-textarea{
            margin-bottom: 10px;
          }
          .btn{
            position: absolute;
            bottom: 0;
            right: 20px;
          }
        }
        .optContent {
          max-height: calc(100% - 60px);
          overflow-y: hidden;
        }
        .optFooter {
          position: absolute;
          bottom: 0px;
          left: 0px;
          width: 100%;
          background: #FFFFFF;
          box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 12px 20px;
          min-height: 54px;
      }
        }
    }
    .resize {
      cursor: col-resize;
      background-color: #f4f5f9;
      padding: 0px 8px;
      width: 10px;
      color: #c3cadd;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:hover {
        background: #e0e6ff;
        .process-icon {
          color: #3455ad !important;
        }
      }
      .el-two-column__icon-top {
        height: 50%;
        width: 4px;
        display: flex;
        flex-direction: column-reverse;
        .el-two-column__icon-top-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
        }
      }
      .el-two-column__trigger-icon {
        width: 25px;
        height: 25px;
        color: #c3cadd;
        .process-icon {
          width: 25px;
          color: #c3cadd;
        }
      }
      .el-two-column__icon-bottom {
        height: 50%;
        width: 4px;
        .el-two-column__icon-bottom-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
        }
      }
    }
    ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  }
  .descriptionTd {
    max-width: 250px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  ::v-deep .el-button--mini {
      line-height: 0px !important;
      padding: 8px 6px !important;
      img {
        height: 16px;
        margin-top: -2px;
      }
    }
  </style>
