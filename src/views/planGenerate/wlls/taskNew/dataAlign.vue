<template>
  <div
    class="task-card-content"
    id="taskTable"
    v-loading="loading"
    element-loading-text="多模态数据对齐中..."
    element-loading-spinner="el-icon-loading"
  >
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
      class="transition-box"
      v-if="status !== 0 && dataStatus==='finished'"
    >
      <el-table-column
        v-for="(item, index) in dataThDatas"
        :class-name="index === dataThDatas.length - 1 ? 'no-bor' : ''"
        :key="item.field"
        min-width="170"
        :prop="item.field"
        :label="item.name"
      >
        <template #default="scope">
          {{ scope.row[item.field] }}
        </template>
      </el-table-column>
    </el-table>
    <div v-else-if="status ===2|| dataStatus === 'failed'">
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          对齐失败
        </div>
      </div>
    </div>
    <div v-else-if="status === 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { queryAbilityMapping } from '@/api/planGenerateApi.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  // components: { Status, MyEditor },
  props: {
    dataAlignDataStatus: {
      type: [String, Number],
      default: ''
    },
    dataAlignData: {
      type: Array,
      default: []
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    dataAlignDataStatus: {
      handler(val) {
        if (Number(val) === 2 || Number(val) === 3) {
          this.handleInit();
          // this.tableData = this.dataAlignData
          // console.log('渲染', this.dataAlignData);
        } else {
          if (Number(val) !== 0) {
            console.log('数据对齐中', val);
            this.loading = true;
            const temp = document.getElementById('taskTable');
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' });
              temp.scrollTop = temp.scrollHeight + 200;
            }
          } else {
            console.log('还未对齐过');
          }
        }
      },
      immediate: true
    },
    status: {
      handler(val) {
        console.log('align status', val);
        if (Number(val) !== 3) {
          this.loading = false;
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      dataThDatas: [],
      dataStatus: ''
    };
  },
  async mounted() {
    // this.handleInit();
  },
  methods: {
    async handleInit(id) {
      this.loading = true;
      console.log('初始化');
      const res = await queryAbilityMapping({ scheme_id: this.$route.query.id });
      const status = res.data.result.ability_status;
      this.dataStatus = status;
      const configData = res.data.result?.config || {};
      if (status !== 'generating') {
        this.loading = false;
        const dataThs = configData?.header || {};
        this.dataThDatas = Object.keys(dataThs).map((item) => {
          return { name: dataThs[item], field: item };
        });
        this.tableData =
          configData?.data?.map((item) => {
            return {
              ...item
            };
          }) || [];
      } else {
        console.log('生成中,轮询调用此接口进行查询');
        this.loading = false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.task-card-content {
  padding: 16px 20px;
  min-height: 200px;
}
:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}
::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}
::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
