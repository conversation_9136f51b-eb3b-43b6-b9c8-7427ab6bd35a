<template>
    <div class="task-card-content">
      <div class="taskBox" id="taskBox">
        <div class="taskCard">
          <div class="title"><img src="@/assets/images/planGenerater/start.png"/>开始一个新目标：<span class="subTitle">{{taskList.map(item => item.name).join('、')}}</span></div>
        </div>
        <div v-for="(item, index) in taskList" :key="index">
          <div v-if="item.status !=='start'" class="taskCard">
            <div class="title">
              <div style="display: flex;align-items:center;"><img :src="item.status === 'running' ? require('@/assets/images/planGenerater/task-running.png'):(item.status === 'error' ? require('@/assets/images/planGenerater/task-error.png'):require('@/assets/images/planGenerater/task-success.png'))"></div>
              <div class="title">{{item.status === 'running' ? '任务执行中': '完成任务'}}: <span class="subTitle">【{{item.name}}】</span></div>
            </div>
            <div class="desc">{{item.content}}</div>
          </div>
        </div>
        <div v-if="allSuccess">
          <div class="taskCard">
            <div class="title"><span class="subTitle">所有任务完成</span></div>
          </div>
        </div>
      </div>
    </div>
</template>
<script type="text/javascript">;

export default {
  name: 'ExampleComAns',
  props: {
    runStreamList: {
      type: Array,
      default: []
    },
    allSuccess: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    runStreamList: {
      handler(val) {
        console.log('流式变化',val);
        this.taskList = val
        const temp = document.getElementById('taskBox');
        if (temp) {
          temp.scrollIntoView({ block: 'end', inline: 'nearest' });
        }
      },
      immediate: true
    }
  },
  async mounted() {
    console.log('第一次',this.runStreamList);
  },
};
</script>
<style lang="scss" scoped>
    .task-card-content {
      padding: 0px;
    }
    .headerTip {
      display: flex;
      align-items: center;
      .tipIcon {
        width: 48px;
        height: 48px;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .tipDesc {
        flex: 1;
        background: #EFF3FF;
        margin-left: 13px;
        position: relative;
        padding: 8px 16px; 
        font-size: 14px;
        line-height: 20px;
        color: #323233;
        border-radius: 6px;
        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          border-top: 5px solid transparent; /*左边透明*/
          border-bottom: 5px solid transparent; /*右边透明*/
          border-right: 8px solid #EFF3FF; /*底部为黑色线条*/
        }
      }
    }
    .taskCard {
      // border: 1px solid #DCDDE0;
      border-radius: 4px;
      padding: 10px 12px;
      // margin-bottom: 12px;
      .title {
        color: #323233;
        font-weight: bold;
        line-height: 20px;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
          margin-right: 10px;
        }
        .subTitle {
          font-weight: normal !important;
        }
      }
      .desc{
        color: #646566;
        margin-top: 12px;
        line-height: 22px;
      }
    }
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
