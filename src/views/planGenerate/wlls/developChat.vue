<template>
  <div
    id="chatContainer"
    :class="
      $store.state.planGenerate.isIframeHide ? 'chatContainer chatContainerFrame' : 'chatContainer'
    "
  >
    <div class="headerBox">
      <!-- <div class="headerTitle">
        <div style="display: flex; align-items: center">
          <div class="title">
            {{
              schemeInfo.name
                ? schemeInfo.name
                : $route.query.name
                ? decodeURIComponent(encodeURIComponent($route.query.name))
                : ''
            }}（{{ $route.query.id }}）
          </div>
          <div class="sence-tag">
            {{
              schemeInfo.agent_scene_name
                ? schemeInfo.agent_scene_name
                : $route.query.agent_scene_name
                ? decodeURIComponent(encodeURIComponent($route.query.agent_scene_name))
                : ''
            }}
          </div>
          <el-button type="text" style="margin-left:10px;" @click="editFn"><i class ="el-icon-edit"></i></el-button>
        </div>
        <div style="display: flex; align-items: center" class="right">
          <div class="model-develop">研发生产模式</div>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-setting"
            @click="
              () => $router.push({ path: '/planGenerate/sceneConfig', query: { ...$route.query } })
            "
          ></el-button>
          <el-button
            class="button-last"
            type="info"
            @click="
              () =>
                $router.push({
                  path: '/planGenerate/index',
                  query: {
                    ...$route.query,
                    name: null,
                    id: null,
                    agent_id: null,
                    type: null,
                    create_time: null,
                    desc: null
                  }
                })
            "
            >返回智能能力研发</el-button
          >
        </div>
      </div> -->
      <div
        v-if="schemeInfo.agent_scene_code === 'digital_twin_assistant_scene'"
        class="headerStep"
        :style="{ width: '100%' }"
      >
        <el-steps :active="activeStep" finish-status="success" simple class="myStep">
          <el-step title="方案明细">
            <img
              slot="icon"
              :src="
                activeStep !== 0
                  ? require('@/assets/images/icon/1_icon.png')
                  : require('@/assets/images/icon/1_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
          <el-step title="多模态数据对齐">
            <img
              slot="icon"
              :src="
                activeStep !== 1
                  ? require('@/assets/images/icon/2_icon.png')
                  : require('@/assets/images/icon/2_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
          <el-step title="能力代码生成">
            <img
              slot="icon"
              :src="
                activeStep !== 2
                  ? require('@/assets/images/icon/3_icon.png')
                  : require('@/assets/images/icon/3_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
          <el-step title="智能能力测试与迭代">
            <img
              slot="icon"
              :src="
                activeStep !== 3
                  ? require('@/assets/images/icon/4_icon.png')
                  : require('@/assets/images/icon/4_icon_success.png')
              "
              class="empty-space"
            />
          </el-step>
        </el-steps>
      </div>
    </div>
    <!-- <div class="stepBox">
      <div class="stepWrap">
        <div style="font-weight: bold; margin-right: 8px">任务进行中：</div>
        <div style="width: 50%"><stepFlow :flowData="flowData" /></div>
      </div>
      <el-tooltip class="item" effect="dark" content="最大化" placement="top">
        <div @click="showTaskModal">
          <img class="full" src="@/assets/images/planGenerater/full.png" />
        </div>
      </el-tooltip>
    </div> -->

    <planChat
      v-if="activeStep * 1 === 0"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :session-id="sessionId"
      :agent-error="agentError"
      :plan-data-val="planData"
      :plan-process-val="optionDataProcess"
      :plan-status="planStatus"
      :tree-data-val="treeData"
      :tree-process-val="treeDataProcess"
      :tree-status="treeStatus"
      :develop-flag="true"
      :emergentScene="emergentScene"
      @updateStep="handleUpdateStep"
      @updateSendMsg=handleSendMsg
      @updateGenerate="handleGenerate"
      @showTaskModal="showTaskModal"
    ></planChat>
    <zhushouChatDuiqi
      v-if="activeStep * 1 === 1"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :tree-data-val="treeData"
      :tree-process-val="abilityDataProcess"
      :sql-status="sqlStatus"
      :tree-status="treeStatus"
      :sql-data="sqlData"
      @updateStep="handleUpdateStep"
      @updateCodeGenerate="handleDataAlign"
      @updateSQLSteam="handleEmitSQL"
    ></zhushouChatDuiqi>
    <zhushouCode
      v-if="activeStep * 1 === 2"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :tree-data-val="treeData"
      :tree-process-val="abilityDataProcess"
      :tree-process-status="abilityDataProcessStatus"
      :tree-status="treeStatus"
      :code-analysis-data="codeAnalysisData"
      :code-analysis-data-status="codeAnalysisDataStatus"
      @updateStep="handleUpdateStep"
      @updateCodeGenerate="handleCodeGenerate"
      @updateCodeAnalysis="handleUpdateCodeAnalysis"
      @stopAbilityGen="stopAbilityGen"
    ></zhushouCode>
    <abilityTest
      v-if="activeStep * 1 === 3"
      :agent-sence-code="schemeInfo.agent_scene_code"
      :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName"
      :tree-data-val="treeData"
      :tree-process-val="treeDataProcess"
      :tree-status="treeStatus"
      :develop-flag="true"
      @updateStep="handleUpdateStep"
      @updateGenerate="handleGenerate"
    ></abilityTest>
    <editModal :is-visible="editModalVisable" @updateEditModal="updateEditModal"></editModal>
    <!-- <RunTask :is-visible="taskModalVisable" :dev-person="schemeInfo.developer" @updateTaskModal="updateTaskModal"></RunTask> -->
  </div>
</template>
<script>
import {
  queryChatIsUseing,
  startDecisionTreeGenerateRequest,
  startStopThinking,
  startAlignDataGenerate,
  startAbilityGenerate,
  startSqlGenerate,
  startConversation,
  querySchemeDetailById,
  startCodeAnalysis,
  saveSimpleSchemeGenerate,
} from '@/api/planGenerateApi.js';
import planChat from './planChat.vue';
import abilityTest from './abilityTest.vue';
import zhushouChatDuiqi from './zhushouChatDuiqiNew.vue';
import zhushouCode from './zhushouCode.vue';
import dayjs from 'dayjs';
import stepFlow from './stepFlow.vue';
import RunTask from './runTask.vue';
import editModal from './editModal.vue';
import Bus from '../../../components/bus';

export default {
  name: 'DevelopChat',
  components: {
    planChat,
    abilityTest,
    zhushouChatDuiqi,
    zhushouCode,
    editModal,
    stepFlow,
    RunTask
  },
  data() {
    return {
      eventSource: null,
      flowData: [
        {
          status: 'success',
          icon: 'fangan',
          title: '数据对齐分析'
        },
        {
          status: 'success',
          icon: 'fangan',
          title: '数据对齐'
        },
        {
          status: 'running',
          icon: 'fangan',
          title: '代码生成'
        },
        {
          status: 'info',
          icon: 'fangan',
          title: '代码部署'
        },
        {
          status: 'info',
          icon: 'fangan',
          title: '代码测试'
        }
      ],
      taskModalVisable: false,
      editModalVisable:false,
      schemeInfo: {},
      activeStep: 0,
      hasChatingName: '',
      sessionId: '',
      treeData: '', // 思维图、思维树内容
      treeDataProcess: '', // 生成过程
      treeStatus: -1,
      planStatus:-1,
      planData:'',
      agentError: false,
      sqlStatus: -1,
      optionDataProcess:'',
      abilityDataProcess: '', // 生成过程
      abilityDataProcessStatus: '', // 生成过程状态
      sqlData: '', // sql流数据
      codeAnalysisData: '', // 代码分析
      codeAnalysisDataStatus: '', // 代码分析过程状态
      emergentScene:''
    };
  },
  async mounted() {
    await this.schemeDetailById();
    Bus.$on('operations-research-edit', (data) => {
      this.editFn();
    });
  },
  beforeDestroy() {
    Bus.$off('operations-research-edit');
    this.eventSource && this.eventSource.close();
  },
  methods: {
    saveSchemeGenerateFn(val) {
      saveSimpleSchemeGenerate({session_id:this.sessionId,messages:val});
    },
    editFn(){
      this.editModalVisable = true;
    },
    handleSendMsg(val){
      console.log(val,'发送的信息')
      this.saveSchemeGenerateFn(val)
    },
    updateEditModal() {
      this.schemeDetailById()
      this.editModalVisable = false;
    },
    showTaskModal() {
      this.taskModalVisable = true;
    },
    updateTaskModal () {
      this.taskModalVisable = false;
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 100;
    },
    handleMessage(message) {
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      this.historyChat.messages.push({
        author: { role: message.role },
        auto: message.role === 'auto' || message.data === '请您发送反馈信息',
        content: {
          parts: message.data,
          nickName: message.role === 'user_proxy' ? userInfo.nickName : '',
          username: message.role === 'user_proxy' ? userInfo.username : ''
        },
        agent_role_id: this.currentRoleInfo?.id,
        create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
      });
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    handleMessageStream(message) {
      console.log('会话消息', message);
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      if (message.action === 'start') {
        this.systemMessages = 'process stream';
        this.historyChat.messages.push({
          author: { role: message.role },
          content: {
            parts: message.data,
            nickName: message.role === 'user_proxy' ? userInfo.nickName : '智能能力研发',
            username: message.role === 'user_proxy' ? userInfo.username : ''
          },
          agent_role_id: this.currentRoleInfo?.id,
          create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        });
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } else {
        this.systemMessages = 'process stream running';
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1];
        lastMessage.content.parts = lastMessage.content.parts + message.data;
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage;
        // console.log('最后拼接的', lastMessage);
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
      if (message.action === 'end') {
        this.systemMessages = '';
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1];
        lastMessage.sub_content = this.processRunningContent;
        lastMessage.id = dayjs().format('MMDDHHmm');
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage;
        console.log('当前对话结束后将思考过程拼接到消息中', lastMessage);
        this.qaBoxLoading = true;
        this.$nextTick(() => {
          this.scrollToBottom();
          // 查询猜你想问
          this.handelQuestion();
        });
      }
    },
    // 查询当前是否正在跟其他用户对话
    async getChatIsUseing() {
      queryChatIsUseing({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('判断是否可用', res.data);
          if (res.data.result.is_using) {
            this.hasChatingName = res.data.result?.using_user?.nickName;
          } else {
            this.hasChatingName = '';
            this.sessionId = res.data.result.session_id || '1';
            const url = process.env.VUE_APP_PLAN_API.startsWith('/')
              ? window.location.origin + process.env.VUE_APP_PLAN_API + '/stream'
              : process.env.VUE_APP_PLAN_API + '/stream';
            console.log('url', url);
            const userInfo = sessionStorage.getItem('USER_INFO')
              ? JSON.parse(sessionStorage.getItem('USER_INFO'))
              : {};

            this.eventSource = new EventSource(
              url +
                '?scheme_id=' +
                this.$route.query.id +
                '&user_id=' +
                (userInfo.userId || 'str') +
                '&work_space_id=' +
                (this.$route.query.workspaceId + '' || '1') +
                '&tenant_id=' +
                (userInfo.tenantId || 'str') +
                '&session_id=' +
                (res.data.result.session_id || '1')
            );

            this.eventSource.addEventListener('open', () => {
              console.log('连接已建立');
              this.agentError = false;
            });

            this.eventSource.addEventListener('messages', (event) => {
              console.log('messages收到消息：', event.data);
              this.handleMessage(JSON.parse(event.data));
            });

            this.eventSource.addEventListener('system_message', (event) => {
              // console.log('system_message', JSON.parse(event.data));
              this.handleSystemMessage(JSON.parse(event.data));
            });

            this.eventSource.addEventListener('scheme_stream_message', (event) => {
              console.log('scheme_stream_message', event.data);
              this.handleProcessPlanDetail(JSON.parse(event.data));
            });
            this.eventSource.addEventListener('process_stream_message', (event) => {
              console.log('process_stream_message', event.data);
              this.systemMessages = 'process_stream_message';
              this.handleProcessDetail(JSON.parse(event.data));
            });
            this.eventSource.addEventListener('decision_tree_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received decision_tree_stream_message:', message);
              if (message.action !== 'end') {
                this.treeStatus = 0;
              }
              this.handleProcessTreeDetail(message);
            });
            // 代码生成 BI 能力代码
            this.eventSource.addEventListener('ability_generate_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received ability_generate_stream_message:', message);
              // bi报表loading状态
              if (message.action !== 'end') {
                this.treeStatus = 0;
              }
              this.handleAbilityDetail(message);
            });
            // BI报表、能力代码生成，生成过程流式过程
            this.eventSource.addEventListener('ability_process_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received ability_process_stream_message:', message);
              this.handleProcessAbilityData(message);
            });

            this.eventSource.addEventListener('decision_tree_process_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received decision_tree_process_stream_message:', message);
              this.handleProcessTreeData(message);
            });
            this.eventSource.addEventListener('scheme_process_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received scheme_process_stream_message:', message);
              this.handleProcessSchemeData(message); // TODO
            });
            this.eventSource.addEventListener('align_data_process_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('Received align_data_process_stream_message:', message);
              this.handleProcessAbilityData(message);
            });
            // 代码分析流
            this.eventSource.addEventListener('code_analysis_stream_message', (event) => {
              const message = JSON.parse(event.data);
              console.log('代码分析-Received code_analysis_stream_message:', message);
              this.handleCodeAnalysisData(message);
            });
            // 代码分析过程流
            this.eventSource.addEventListener('code_analysis_process_stream_message', (event) => {
              // console.log('代码分析过程流', event.data);
              const message = JSON.parse(event.data);
              console.log('Received code_analysis_process_stream_message:', message);
              if (message.active === 'start') {
                this.codeAnalysisDataStatus = '1';
              }
              // this.handleCodeAnalysisData(message);
            });

            this.eventSource.addEventListener('error', () => {
              console.log('连接出错');
              this.agentError = true;
              // this.treeStatus = 3;
              // this.abilityDataProcessStatus = 3;
              // 回到第一步
              this.stepIndex = 0;
              this.systemMessages = '';
              this.changeViews(0);
            });
            this.eventSource.addEventListener('disconnect', () => {
              console.log('连接断开');
              this.agentError = true;
              this.systemMessages = '';
            });
          }
        } else {
          this.hasChatingName = '';
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    handleProcessSchemeData (message) {
      if (message.action === 'start') {
        this.optionDataProcess = message.data;
      } else {
        this.optionDataProcess = this.optionDataProcess + message.data;
      }
      if (message.action === 'end') {
        console.log('方案生成过程的数据======',this.optionDataProcess);
      }
    },

    handleSystemMessage(message) {
      console.log('系统消息', message);
        this.systemMessages = message.data
        if(message.data === 'scheme generating error') {
          this.taskStatusText = 'scheme generating error'
          this.treeStatus = 2
        }
        // 清空聊天记录成功
        if(message.data === 'clear history completed' || message.data === 'clear history error') {
          this.clearChat(message.data === 'clear history completed')
        }

        if (message.data === 'process running') {
          console.log('----正在会话中');
        }
        if (message.data === 'process completed' || message.data === 'process error' || message.data === 'scheme generating error') {
          console.log('结束----');
          this.queryTask();
        }
        if(message.data === '403 error'){
          this.refresh()
          const lastMessage=  this.historyChat.messages[this.historyChat.messages.length - 1].content
          this.qaList = [];
          startConversation({messages:lastMessage.parts,agent_role_id:this.agentAvatorInfo.id, session_id: this.sessionId});
        }
        if (message.data === 'process completed') {
          this.taskStatus = 1;
          this.queryTask();
        }
        if (message.data === 'process waiting') {
          this.scrollToBottom();
        }
        if (message.data === 'scheme generating completed') {
          this.treeStatus = 2
        }
        if (message.data === 'decision tree generating completed') {
          this.treeStatus = 2
        }
        if (message.data === 'decision tree generating') {
          this.treeStatus = 1
          console.log('this.treeStatus思维树',this.treeStatus)
        }
        if (message.data === 'decision tree generating error') {
          this.treeStatus = 3
        }
        if (message.data === 'ability generating completed') {
          this.treeStatus = 2
          console.log('ability generating completed BI-CODE',message.data)
        }
        if (message.data === 'ability generating') {
          this.treeStatus = 1
        }
        if (message.data === 'ability generating error') {
          this.treeStatus = 3
          console.log('ability generating error BI-CODE',message.data)
        }
        if (message.data === 'append_history completed') {
          console.log('append_history completed', message.data);
          this.queryDetail();
        }
        if (message.data === 'append_history error') {
          console.log('append_history error', '增加会话历史记录失败');
        }
        if (message.data === 'align_data generating completed') {
          this.treeStatus = 2
          console.log('align_data generating completed数据对齐完成 ======',message.data)
        }
        if (message.data === 'align_data generating') {
          this.treeStatus = 1
        }
        if (message.data === 'align_data generating error') {
          this.treeStatus = 3
        }
        if (message.data === 'sql generating completed') {
          this.sqlStatus = 2
          console.log('sql generating completed数据对齐完成 ======',message.data)
        }
        if (message.data === 'sql generating') {
          this.sqlStatus = 1
        }
        if (message.data === 'sql generating error') {
          this.sqlStatus = 3
        }

        if (message.data === 'scheme optimize completed') {
          this.planStatus = 2
          console.log('scheme optimize completed方案优化完成 ======',message.data)
        }
        if (message.data === 'scheme optimize generating') {
          this.planStatus = 1
        }
        if (message.data === 'scheme optimize generating error') {
          this.planStatus = 3
        }
        if(message.data === 'code_analysis generating error') {
          console.log('code_analysis generating error');
          this.codeAnalysisDataStatus = '3';
        }
        if(message.data === 'code_analysis generating completed') {
          console.log('代码分析完成');
          this.codeAnalysisDataStatus = '2';
        }
        if(message.data === 'code_analysis generating') {
          console.log('代码分析中。。。');
        }

        if (message.data === 'rule generating completed') {
          this.treeStatus = 2
          console.log('rule generating completed规则生成完成 ======',message.data)
        }
        if (message.data === 'rule generating') {
          this.treeStatus = 1
        }
        if (message.data === 'rule generating error') {
          this.treeStatus = 3
        }
    },
    handleProcessTreeData (message) {
      if (message.action === 'start') {
        this.treeDataProcess = message.data;
        this.treeDataProcessNodes = [{node_id: '1', data: message.data || ''}];
      } else {
        // 如果有node_id代表我分组任务流程过程
        if (message.node_id) {
          const filtersData = this.treeDataProcessNodes.filter(item => item.node_id === message.node_id)
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map(item => {
              if (item.node_id === message.node_id) {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item;
              }
            })
            this.treeDataProcessNodes = temp;
          } else {
            this.treeDataProcessNodes.push({node_id:message.node_id, data:message.data})
          }
          // console.log('分组数据', this.treeDataProcessNodes);

          const conData = this.treeDataProcessNodes.map(item => item.data).join('');
          // console.log('拼接后的', conData);
          this.treeDataProcess = conData;
        } else {
          const filtersData = this.treeDataProcessNodes.filter(item => item.node_id === '1')
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map(item => {
              if (item.node_id === '1') {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item;
              }
            })
            this.treeDataProcessNodes = temp;
            const conData = this.treeDataProcessNodes.map(item => item.data).join('');
            // console.log('拼接后的', conData);
            // console.log('分组数据', this.treeDataProcessNodes);
            this.treeDataProcess = conData;
          } else {
            this.treeDataProcess = this.treeDataProcess + message.data;
          }
        }
      }
      if (message.action === 'end') {
        console.log('思维树生成过程的数据======',this.treeDataProcess);
      }
    },
    handleProcessPlanDetail(message) {
      this.planStatus = 1;
      if (message.action === 'start') {
        this.planData = message.data;
      } else {
        this.planData = this.planData + message.data;
      }
      if (message.action === 'end') {
        this.planStatus = 2;
      }
    },
    handleProcessTreeDetail (message) {
      if (message.action === 'start') {
        this.treeData = message.data;
      } else {
        this.treeData = this.treeData + message.data;
      }
      if (message.action === 'end') {
        this.treeStatus = 2
      }
    },
    handleProcessAbilityData(message){
      if (message.action === 'start') {
        this.abilityDataProcess = message.data;
        this.abilityDataProcessStatus = '1'
      } else {
        this.abilityDataProcess = this.abilityDataProcess + message.data;
        this.abilityDataProcessStatus = '1'
      }
      if (message.action === 'end') {
        console.log('能力生成过程的数据======',this.abilityDataProcess);
        this.abilityDataProcessStatus = '2'
      }
    },
    handleAbilityDetail (message) {
      if (message.action === 'start') {
        this.treeData = message.data;
      } else {
        this.treeData = this.treeData + message.data;
      }
      // if (message.action === 'end') {
      //   this.treeStatus = 2
      // }
    },
    // 代码分析消息处理
    handleCodeAnalysisData(message) {
        if (message.action === 'start') {
        this.codeAnalysisData = message.data;
        this.codeAnalysisDataStatus = '1'
      } else {
        this.codeAnalysisData = this.codeAnalysisData + message.data;
        this.codeAnalysisDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======',this.codeAnalysisData);
        this.codeAnalysisDataStatus = '2'
      }
    },
    refresh() {
      this.eventSource && this.eventSource.close();
      this.getChatIsUseing();
    },
    // 能力代码终止生成
    stopAbilityGen() {
      startStopThinking({ session_id: this.sessionId });
      this.codeAnalysisDataStatus = '';
      this.abilityDataProcessStatus = '';
      this.refresh();
    },
    // 代码分析
    handleUpdateCodeAnalysis() {
      console.log('触发代码分析');
      startCodeAnalysis({ session_id: this.sessionId });
    },
    handleCodeGenerate(val) {
      startAbilityGenerate({ ability_type: val, session_id: this.sessionId });
    },
    handleEmitSQL() {
      startSqlGenerate({ session_id: this.sessionId });
    },
    handleDataAlign(params) {
      console.log('数据对齐参数', params);
      startAlignDataGenerate({ ...params, session_id: this.sessionId });
    },
    handleGenerate() {
      startDecisionTreeGenerateRequest({session_id: this.sessionId});
    },
    handleUpdateStep(v) {
      this.treeData = '';
      console.log('切换步骤', this.codeAnalysisDataStatus);
      this.codeAnalysisDataStatus = 0;
      this.changeViews(v);
    },
    changeViews(val) {
      // 向外层页面传递消息
      console.log('当前在第几步', val);
      this.codeAnalysisDataStatus = ''
      window.parent.postMessage(JSON.stringify({ stepIndex: val }), '*');
      this.activeStep = val;
    },

    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.schemeInfo = res.data.result || { ...this.$route.query };
            console.log('详情', this.schemeInfo);
            this.schemeInfo.agent_scene_code = 'digital_twin_assistant_scene';
            this.emergentScene = res.data.result?.agent_scene_id
            // TODO 去掉
            this.schemeInfo.developer = {
              id: '1391550307402108929',
              loginName: 't-DINGNINGE',
              nickname: '丁宁'
            }
            this.eventSource && this.eventSource.close();
            this.eventSource = null;
            Bus.$emit('operations-research', this.schemeInfo);
            this.totalWidth = document
              .getElementById('chatContainer')
              .getBoundingClientRect().width;
            this.leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
            this.rightWidth = document
              .getElementById('right-content')
              .getBoundingClientRect().width;
            console.log('宽度', this.rightWidth);
            this.taskStatus = this.$route.query.status;
            document.addEventListener('click', this.hideGuess);
            await this.getChatIsUseing();
            console.log('操作系统', window.navigator.userAgent);
            if (window.navigator.userAgent.indexOf('Mac') > -1) {
              this.navType = 'Mac';
            } else {
              this.navType = 'Windows';
            }
          }
        })
        .catch((_err) => {
          // this.$message({
          //   type: 'error',
          //   message: _err.data?.msg || '接口异常!'
          // });
          console.log(_err.data?.msg || '接口异常!');
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.chatContainer {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  position: relative;
  flex-direction: column;
  .headerBox {
    background-color: #fff;
    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;
      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }
      .sence-tag {
        margin-left: 16px;
        padding: 0 8px;
        height: 24px;
        border-radius: 2px;
        max-width: calc(100vw - 380px);
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
        background: #ebf9ff;
        color: #318db8;
      }
      .right {
        .model-develop {
          margin-right: 10px;
          font-size: 14px;
          color: #4068d4;
          font-weight: bold;
        }
      }
    }
    .headerStep {
      .myStep {
        background: #fff;
        :deep(.el-step__arrow) {
          margin: 0 16px;
          &::before {
            content: '';
            position: static;
            height: 1px;
            width: 100%;
            background: #c8c9cc;
            transform: none;
            display: block;
          }
          &::after {
            display: none;
          }
        }
        :deep(.is-process) {
          color: #4068d4;
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: none;
            }
          }
        }
        :deep(.is-success) {
          color: #000;
          border-color: #4068d4;
          .el-icon-check {
            color: #4068d4;
          }
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: 1px solid #4068d4;
            }
          }
        }
        .empty-space {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  &.chatContainerFrame {
    height: 100%;
  }
}
.stepBox {
  background: #ebeffa;
  border-radius: 2px;
  border: 1px solid #4068d4;
  padding: 8px 20px;
  margin: 16px 16px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #323233;
  .stepWrap {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .item {
    display: flex;
    align-items: center;
  }
  .full {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  display: flex;
  align-items: center;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
</style>
