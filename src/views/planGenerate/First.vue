<template>
  <div class="first">
    <div class="top">
    </div>
    <div class="bottom">
      <div :class="{ 'left-content': true, 'left-content-max': turnFull }">
        <div class="ai-container" v-if="switchValue === 'chat'">
          <PartnerZhushouChat></PartnerZhushouChat>
        </div>
        <div class="scene-container" v-if="switchValue === 'scene'">
          <SceneSelect @show-ability="handlePageChange"></SceneSelect>
        </div>
        <div class="show" v-if="switchValue === 'schema'">
          <AbilityList
            :myAbilityTriggered="myAbilityTriggered"
            :isMyCollect="isMyCollect"
          ></AbilityList>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import homePage from './guide/homePage.vue';
import PartnerZhushouChat from './guide/partnerZhushouChat.vue';
import RoleInfo from './guide/roleInfo.vue';
import SceneSelect from './guide/sceneSelect.vue';
import AbilityList from './guide/abilityList.vue';

const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {};
let title = '';
if (sessionStorage.getItem('ACCOUNT_INFO')) {
  const account = JSON.parse(sessionStorage.getItem('ACCOUNT_INFO'));
  const titleParts = account.postName.split('/');
  title = titleParts[titleParts.length - 2];
} else {
  title = '';
}
export default {
  name: 'First',
  components: { PartnerZhushouChat, SceneSelect, RoleInfo, AbilityList,homePage },
  data() {
    return {
      turnFull: false,
      switchValue: 'schema',
      workspaceId: 1,
      canCompeleted: false,
      searchVal: '',
      showCardList: false,
      activeName: 'first',
      title: '安全专家',
      tableData: {
        myCollect: '0',
        user_id: '',
        tags: [],
        list: [], // 表格数据
        page: 1,
        pageSize: 12,
        total: 0,
        allist: [],
        allTotal: 0
      },
      sceneList: [],
      checkedList: [],
      // checkedList: [],
      circleUrl: userInfo.headImageUrl,
      userDetailInfo: {
        name: userInfo.nickName,
        title: title
      },
      myAbilityTriggered: false,
      isMyCollect: false
    };
  },
  beforeRouteEnter(to, from, next) {
    if (!to.query.FullScreen) {
      next((vm) => {
        // 使用 replace 避免新增历史记录
        vm.$router.replace({
          path: to.path,
          query: { ...to.query, FullScreen: 'false' }
        });
      });
    } else {
      next();
    }
  },
  watch: {
    $route: {
      immediate: true, // 组件加载时触发
      handler(newRoute) {
        this.turnFull = newRoute.query.FullScreen === 'true';
      }
    }
  },
  mounted() {
    this.workspaceId = Number(this.$route.query.workspaceId);
    // 请求目的：为了记录用户进入空间的日志数据落库
    this.$get('/overview/statistics/resourceUsed/v2')
  },
  methods: {
    handleChangeSize() {
      this.turnFull = !this.turnFull
    },
    handlePageChange(val) {
      this.switchValue = 'schema';
    },
    handSwitchTab(target) {
      if (target == 'scene') {
        this.switchValue = 'scene';
      } else if (target == 'chat') {
        this.switchValue = 'chat';
      } else {
        this.switchValue = 'schema';
      }
    },
    filterMyAbilityEvent(flag) {
      this.myAbilityTriggered = flag;
    },
    filterMyCollect(flag) {
      this.isMyCollect = flag;
    }
    // 查看智能伙伴绑定的tag
  }
};
</script>

<style lang="scss" scoped>
.first {
  // max-height: calc(100vh - 90px);
  overflow: hidden;
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: normal;
    font-size: 16px;
    color: #323233;
  }
  .icon-content {
        cursor: pointer;
        display: flex;
        align-items: center;
        i {
          font-size: 20px;
        }
      }
      .icon-content-card {
        right: 0;
      }
  .top {
    background-color: #fff;
    padding: 0px 20px 0px;
    position: relative;

    .header {
      font-weight: bold;
      color: #323233;
      line-height: 26px;
      font-size: 18px;
      padding: 14px 0px;
    }
  }

  .bottom {
    overflow: hidden;
    margin: 16px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: transparent;
    // display: flex;
    height: calc(100% - 32px);
    // max-height: calc(100%);

    .left-content {
      // background-color: #fff;
      width: 100%;
      min-width: 250px;
      height: 100%;
      overflow-y: auto;
      border-radius: 4px;

      .scene-container {
        width: 100%;
      }
      .show {
        height: 100%;
      }
    }
  }
}
</style>
