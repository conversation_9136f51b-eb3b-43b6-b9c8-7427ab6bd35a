<template>
  <div style=" height: 100%;width:100%" v-loading="!mdContentText && dataAlignStatus === '0' && elBox === 'math_model'"
    element-loading-text="生成中..." element-loading-spinner="el-icon-loading">
    <div style="height: 100%" class='parent previewNew'>
      <!-- <v-md-editor
            :id="id + '' || 'editor555'"
            v-model="mdContentText"
            :text="mdContentText"
            mode="preview"
            right-toolbar="preview"
            height="100%"
            @change="handleChange"
            :key="editorKey"
            ref="editor"
          >
       </v-md-editor> -->
      <div v-if="mdContentText.hasThink">
        <!-- 点击切换折叠状态的按钮 -->
        <span class="toggle-span" @click="toggleThinkCollapse">
          {{ isThinkCollapsed ? '[展开思考]' : '[折叠思考]' }}
        </span>
        <!-- 渲染 <think> 标签的内容 -->
        <div v-if="!isThinkCollapsed">
          <v-md-editor ref="editor555" :id="'think-' + id" v-model="mdContentText.thinkContent" :text="mdContentText.thinkContent" mode="preview" right-toolbar="preview"
            height="100%" :key="editorKey" />
        </div>
      </div>
      <!-- 渲染 restContent -->
      <v-md-editor ref="editor555" :id="'rest-' + id" v-model="mdContentText.restContent" :text="mdContentText.restContent" mode="preview" right-toolbar="preview"
        height="100%" :key="editorKey" />
    </div>
    <div v-show="errorFlag" style=" height: 100%">
      <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
          ">
        <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
        <div style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              margin-top: 16px;
            ">
          转换失败，请修正后重试
        </div>
      </div>
    </div>
    <div v-if="showImageModal" class="image-modal" @click="closeImageModal">
      <img :src="modalImageUrl" class="modal-image" />
    </div>

  </div>
</template>

<script>
import MarkdownIt from 'markdown-it';
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn'
import createKaTeXPlugin from '@kangc/v-md-editor/lib/plugins/katex/cdn';
import createLineNumbertPlugin from '@kangc/v-md-editor/lib/plugins/line-number/index'
import createCopyCodePlugin from '@kangc/v-md-editor/lib/plugins/copy-code/index'
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-python'
import hljs from 'highlight.js'
import 'katex/dist/katex.min.css'
import throttle from 'lodash/throttle';
import { handleThrottleLog, log } from '@/utils/ThrottledLogger.js';
import { v4 as uuidv4 } from 'uuid'
console.log('window.fenceruletype before init basic', window.fenceruletype);

// -1代表，既不是代码模式，也不是基本模式，代表变量还未用过。  1代表基本模式，2代表代码模式
window.fenceruletype = -1
// VMdEditor.use(createMermaidPlugin());
VMdEditor.use(createKaTeXPlugin());
VMdEditor.use(vuepressTheme, {
  Prism,
  extend(md) {
    console.log('ddd', md)
  }
})
// VMdEditor.use(createLineNumbertPlugin());

export default {
  components: {
    VMdEditor,
  },
  props: {
    heightSta: {
      type: Boolean,
      default() {
        return false
      }
    },
    mdContent: {
      type: String,
      default() {
        return ''
      }
    },
    id: {
      type: String,
      default() {
        return uuidv4()
      }
    },
    chatMessage: {
      type: Object,
      default() {
        return {}
      }
    },
    showButton:{
      type: Boolean,
      default() {
        return false
      }
    },
    ModalType: {
      type: Boolean,
      default() {
        return false
      }
    },
    fromModalnotCode:{
      type: Boolean,
      default() {
        return false
      }
    },
    autoScroll: {  // 新增属性，控制是否自动滚动
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isThinkCollapsed: false,
      editorKey: uuidv4(),
      mdContentText: '',
      errorFlag: false,
      showImageModal: false,
      modalImageUrl: '',
      showSplitScreenModal: false, // 控制弹窗显示
      splitScreenContent: '', // 分屏显示的代码内容
      chatMessages: [ // 聊天内容
        { text: '你好，请问这段代码有什么问题？', time: '10:00 AM' },
        { text: '这段代码的逻辑需要优化。', time: '10:05 AM' },
        { text: '具体是哪里需要优化呢？', time: '10:10 AM' },
      ],
      isPreviewMode: false, // 当前是否为预览模式
      item: {
        htmlContent: '<p>这是一个示例HTML内容</p>', // 示例HTML内容
      },
      mymd: null,
    }
  },
  watch: {
    ModalType: {
      handler(val) {
        // this.editorKey = uuidv4()
        console.log('window.fenceruletype 123456 fromModalnotCode', this.fromModalnotCode, this.ModalType,  window.fenceruletype);
        this.initializeCustomCodeButtonPlugin()
      },
      immediate: true
    },
    mdContent: {
      handler(val) {
        this.mdContentText = this.updateProcessedText(val)
        // console.log('val11111mdContentText', val);
        // 正则表达式：匹配 ```html, ```python 等代码块
        if(this.autoScroll){
          this.$nextTick(()=> {
            if (
              this.$refs.editor555 &&
              this.$refs.editor555.$el &&
              this.$refs.editor555.$el.querySelector('.vuepress-markdown-body')
            ) {
                // 如果点击展示详情，则不需要滚动
                this.$refs.editor555.$el
                  .querySelector('.vuepress-markdown-body')
                  .scrollIntoView({ block: 'end', behavior: 'smooth' })
              }
          })
        }
        this.errorFlag = false
        window.mermaid.parseError = (err) => {
          console.log('转换错误---', err, String(err).indexOf('Parse error'))
          if (err.str && String(err.str).indexOf('Parse error') > -1) {
            this.errorFlag = true
          } else {
            this.errorFlag = false
          }
        }
      },
      immediate: true
    },
    chatMessage: {
        handler(val, oldval) {
        if (!val || !val.chat_message) {
          return
        }
        const chatMessageType = val.chat_message_type || 'text'
        if (chatMessageType === 'text') {
          // this.mdContentText = val?.chat_message.content
          this.debounceTimer66 = setTimeout(() => {
            clearTimeout(this.debounceTimer66)
            this.mdContentText = this.updateProcessedText(val?.chat_message.content)
            if (this.ModalType && oldval !== undefined) {
              // this.initializeCustomCodeButtonPlugin()
              // this.handleRender();
              console.log('val old val', val, oldval);

              this.handleCodeBlocks(val?.chat_message.content);
            }
            console.log('nextTick执行几次1');
          }, 50); // 400 毫秒可以根据实际情况调整
        } else if (chatMessageType === 'img_text') {
          this.mdContentText.restContent = `<p>${val?.chat_message.content}</p>
          <img src="${val?.chat_message.image_path}" width="200px" height="100px" class="clickable-image" />`
        }
        this.$nextTick(() => {
          clearTimeout(this.debounceTimer)
          // 设置新的计时器
          this.debounceTimer = setTimeout(() => {
            this.addImageClickEvent()
            this.handleRender();
            this.addEventForButtons()
            console.log('nextTick执行几次1');

          }, 500); // 400 毫秒可以根据实际情况调整
          if (this.heightSta) {
            this.scrollToBottom()
          }
        })
        this.errorFlag = false
        window.mermaid.parseError = (err) => {
          console.log('转换错误---', err, String(err).indexOf('Parse error'))
          if (err.str && String(err.str).indexOf('Parse error') > -1) {
            this.errorFlag = true
          } else {
            this.errorFlag = false
          }
        }
      },
      immediate: true,
      deep: true
    },
  },
  created() {
    if (
      this.mdContent.indexOf('mermaid') > -1 ||
      this.mdContent.indexOf('flow') > -1 ||
      this.mdContent.indexOf('graph') > -1 ||
      this.mdContent.indexOf('flowchart') > -1
    ) {
      VMdEditor.use(createMermaidPlugin())
    }
    this.initializeCustomCodeButtonPlugin()
  },
  mounted(){
  },
  methods: {
    addEventForButtons() {
      console.log('addEventForButtons');
      this.$nextTick(() => {
        const markdownBodies = this.$el.querySelectorAll('.vuepress-markdown-body');
        markdownBodies.forEach((markdownBody) => {
          const YeahzButtons = markdownBody.querySelectorAll('.custom-code-buttonYeahz');
          YeahzButtons.forEach((YeahzButton) => {
            if (YeahzButton) {
              // const editorElement = YeahzButton.closest('.v-md-editor-preview');

              // if (!editorElement || !editorElement.__vue__) {
              //   console.error('没有找到对应的 Vue 实例');
              //   return;
              // }

              // const vueInstance = editorElement.__vue__;
              // console.log('Vue 实例:', vueInstance.text);
              // const mdContent = vueInstance.text
              YeahzButton.addEventListener('click', (event) => {
                if (event.target && event.target.classList.contains('custom-code-buttonYeahz')) {
                  const ObjectStrContent = event.target.dataset.info; // 获取绑定的 codeContent
                  function decodeBase64(encodedStr) {
                    // 使用 decodeURIComponent 代替 unescape
                    return decodeURIComponent(atob(encodedStr)); // 解码 Base64 为原始文本
                  }
                  const parseContent = JSON.parse(ObjectStrContent);
                  console.log('codeContent click nmsl', parseContent);
                  this.$emit('open-modal', { ...parseContent, 'codeContent': decodeBase64(parseContent.codeContent), clickShow:true });
                  // // this.handleCodeClick(codeContent);editorOptions
                  // this.openSplitScreen(codeContent);
                  // this.openSplitScreen(mdContent);
                }
              });
            }
          })
        })
      })
    },
    toggleThinkCollapse() {
      this.isThinkCollapsed = !this.isThinkCollapsed;
    },
    // updateProcessedText(newText) {
    //   let textWithPlaceholders = newText;

    //   // 统一处理，逐步替换 <think> 标签内的内容为 Markdown 引用格式
    //   // 这里会查找所有的 <think> 标签，处理其中的内容
    //   textWithPlaceholders = textWithPlaceholders.replace(/<think>(.*?)<\/think>/gs, (match, p1) => {
    //     // 将每一行都加上 Markdown 引用符号 '>'
    //     let lines = p1.split('\n').map(line => `> ${line}`).join('\n');
    //     return lines;  // 返回带有正确引用格式的多行文本
    //   });

    //   // 处理未闭合的 <think> 标签部分
    //   if ((textWithPlaceholders.match(/<think>/g) || []).length > (textWithPlaceholders.match(/<\/think>/g) || []).length) {
    //     // 如果有未闭合的 <think> 标签，显示为占位符，后续会逐步填充引用内容
    //     textWithPlaceholders = textWithPlaceholders.replace(/<think>(.*?)$/gs, (match, p1) => {
    //       // 将未闭合的 <think> 标签内的内容转换为 Markdown 引用格式
    //       let lines = p1.split('\n').map(line => `> ${line}`).join('\n');
    //       return lines;
    //     });
    //   }

    //   // 更新最终渲染的文本
    //   return textWithPlaceholders;
    // },
    updateProcessedText(newText) {
      let textWithPlaceholders = newText;

      // 检查是否有 <think> 标签
      const hasThinkTag = /<think>/i.test(textWithPlaceholders);
      if (!hasThinkTag) {
        return {
          hasThink: false,
          restContent: textWithPlaceholders
        };
      }

      // 处理已闭合的 <think> 标签
      let thinkContent = '';
      let restContent = textWithPlaceholders;

      if (textWithPlaceholders.includes('</think>')) {
        textWithPlaceholders = textWithPlaceholders.replace(/<think>(.*?)<\/think>/gs, (match, p1) => {
          thinkContent = p1.split('\n').map(line => `> ${line}`).join('\n');
          return ''; // 将 <think> 标签内容替换为空，后续手动处理
        });
        restContent = textWithPlaceholders;
      } else {
        // 处理未闭合的 <think> 标签
        if ((textWithPlaceholders.match(/<think>/g) || []).length > (textWithPlaceholders.match(/<\/think>/g) || []).length) {
          textWithPlaceholders = textWithPlaceholders.replace(/<think>(.*?)$/gs, (match, p1) => {
            thinkContent = p1.split('\n').map(line => `> ${line}`).join('\n');
            return '';
          });
          restContent = textWithPlaceholders;
        }
      }

      return {
        hasThink: true,
        thinkContent: thinkContent, // <think> 标签内的内容
        restContent: restContent // <think> 标签外的内容
      };
    },
    thinkPlugin(md) {
      const originalRender = md.renderer.rules.text || function (tokens, idx) {
        return tokens[idx].content;
      };

      md.renderer.rules.text = function (tokens, idx) {
        const tokenContent = tokens[idx].content;
        if (tokenContent.includes('<think>')) {
          // 找到 <think> 标签，转换为 markdown 引用格式
          const thinkContent = tokenContent.replace(/<think>(.*?)<\/think>/g, '> $1\n');
          return thinkContent;
        }
        return originalRender(tokens, idx);
      };
    },
    initializeCustomCodeButtonPlugin() {

      window.ModalType = this.ModalType

      if(!window.ModalType) {
        console.log('window.ModalType 123 4', window.ModalType);
        let mymd = MarkdownIt();
        // 使用插件
        VMdEditor.use(vuepressTheme, {
          Prism,
          extend(md) {
            console.log('basic window.ModalType1', window.ModalType, window.fenceruletype);
            md.renderer.rules.fence = mymd.renderer.rules.fence;
            window.fenceruletype = 1
            console.log('basic window.ModalType2', window.ModalType, window.fenceruletype);
            // md.renderer = mymd.renderer;
            console.log('执行到这了吗？');

            // think 标签处理
            // md.inline.ruler.before('emphasis', 'think', (state, silent) => {

            //   const startPos = state.pos;
            //   const start = state.src.indexOf('<think>', startPos);
            //   const end = state.src.indexOf('</think>', startPos);
            //   console.log('执行到这了吗？ md.inline.ruler.before', start, end, state);

            //   if (start === -1 || end === -1) return false; // 没有找到 think 标签
            //   // 解析并转为引用格式
            //   const thinkContent = state.src.slice(start + 7, end);
            //   const markdownFormatted = `> ${thinkContent}\n`;

            //   // 替换为 markdown 引用
            //   state.push({
            //     type: 'inline',
            //     tag: 'blockquote',
            //     content: markdownFormatted,
            //   });

            //   state.pos = end + 8; // 更新当前解析位置
            //   return true;
            // });
          }
        });
      }else{
        window.fenceruletype = 2
        VMdEditor.use(vuepressTheme, {
          Prism,
          extend(md) {
            console.log('code mode', window.ModalType, window.fenceruletype);
            const originalFenceRule = md.renderer.rules.fence;
            function encodeBase64(str) {
              // 使用 encodeURIComponent 代替 unescape
              return btoa(encodeURIComponent(str)); // 将文本编码为 Base64 格式
            }
            if (window.ModalType) {
              md.renderer.rules.fence = function (tokens, idx) {
                console.log('gggggg tokens', tokens, idx, window.ModalType, window.fenceruletype);
                const token = tokens[idx];
                let codeContent = token.content;

                const buttonHTML = `
            <button class="custom-code-buttonYeahz" data-info='${JSON.stringify({
              codeContent: encodeBase64(codeContent),
                  codetype: token.info || ''
                },null,2)}'>
              查看代码
            </button>
          `;
                return buttonHTML;
              };
            } else {
              console.log('this time originalFenceRule');
              return originalFenceRule
            }

            // md.renderer = mymd.renderer;
          }
        });

        this.$nextTick(() => {
          this.addEventForButtons()
        })
      }
    },

    handleCodeClick(codeContent) {
      // 在此处理按钮点击事件，展示代码或其他逻辑
      console.log('查看代码:', codeContent);
      alert(codeContent);  // 仅为示例
    },
    handleChange(text, html) {
    },
    handleCodeBlocks: throttle(function (val) {
      console.log('wtf 123456?', val);

      // 定义开始标记和结束标记
      const startDelimiter = /(```|~~~)(\w+)?/; // 开始标记
      const endDelimiter = /(```|~~~)(\s*)/; // 结束标记

      let isInsideCodeBlock = false;  // 用于标记当前是否在代码块内
      let currentCode = '';          // 存储当前提取的代码内容
      let currentLanguage = '';      // 存储当前代码块的语言类型

      let lines = val.split('\n');  // 按行分割输入的mdContent文本

      for (let line of lines) {
        console.log('当前行内容line', line, isInsideCodeBlock,  startDelimiter.test(line), endDelimiter.test(line));
        line = line.trim();  // 去掉行首和行尾的空格和换行符
        // 检查是否遇到代码块的开始标记
        if (!isInsideCodeBlock && startDelimiter.test(line)) {
          // 如果是开始标记，标记为进入代码块
          isInsideCodeBlock = true;

          // 提取代码类型（如html, python等）
          const match = startDelimiter.exec(line);
          currentLanguage = match && match[2] ? match[2] : '';  // 如果有代码类型则使用，没有则为空
          continue;  // 跳过当前行继续处理
        }

        // 检查是否遇到代码块的结束标记
        if (isInsideCodeBlock && endDelimiter.test(line)) {
          console.log('从来没进来吗哥？');
          // 如果是结束标记，将当前代码块内容保存
          // 重置状态
          isInsideCodeBlock = false;
          currentCode = '';  // 清空当前代码内容
          currentLanguage = '';  // 清空当前语言类型
          this.$emit('modal-code-generate',
            {
              generateing: false
            });
          continue;  // 跳过当前行继续处理
        }

        // 如果在代码块内，继续收集代码内容
        if (isInsideCodeBlock) {
          currentCode += line + '\n';  // 追加代码内容
          console.log('currentCode123121321231', currentCode);

          this.$emit('modal-code-generate',
          { codetype: currentLanguage,
            content: currentCode,
            generateing: true
          });
        }
      }

    }, 200),
    openSplitScreen(codeContent) {
      console.log('this.mdContentText 1111', codeContent);
      this.handleCodeBlocks(codeContent)
      this.$emit('open-modal');
      // this.splitScreenContent = codeContent; // 设置代码内容
      // this.item.htmlContent = this.splitScreenContent;
      // this.showSplitScreenModal = true; // 显示弹窗
    },
  // 关闭分屏弹窗
    closeSplitScreenModal() {
      this.showSplitScreenModal = false;
      this.splitScreenContent = '';
    },
    toggleMode(isPreview) {
      this.isPreviewMode = isPreview;
    },
    scrollToBottom() {
      // 使用this.$refs访问DOM元素
      const container = this.$refs.scrollContainer
      // 滚动到底部
      container.scrollTop = container.scrollHeight
    },
    showImage(imageUrl) {
      this.modalImageUrl = imageUrl;
      this.showImageModal = true;
    },
    closeImageModal() {
      this.showImageModal = false;
      this.modalImageUrl = '';
    },
    addImageClickEvent() {
      const images = this.$el.querySelectorAll('.clickable-image')
      images.forEach(image => {
        image.addEventListener('click', () => {
          this.showImage(image.src)
        })
      })
    },
    handleRender () {
      console.log('handleRender called'); // 调试日志
      this.$nextTick(() => {
        if (!this.showButton) return
        // const preElements = this.$el.querySelectorAll('pre');

        const markdownBodies = this.$el.querySelectorAll('.vuepress-markdown-body');
        markdownBodies.forEach((markdownBody) => {
          const preElements = markdownBody.querySelectorAll('pre'); // 在每个 markdownBody 内查找 pre 元素
          // const YeahzButton = markdownBody.querySelector('.custom-code-buttonYeahz');
          // if (YeahzButton){
          //   YeahzButton.addEventListener('click', (event) => {
          //     console.log('Expand button clicked'); // 调试日志
          //     if (event.target && event.target.classList.contains('custom-code-buttonYeahz')) {
          //       const codeContent = event.target.dataset.code; // 获取绑定的 codeContent
          //       // this.handleCodeClick(codeContent);
          //       this.openSplitScreen(codeContent);
          //     }
          //   });
          // }
          // const CodeCElements = markdownBody.querySelectorAll('.v-md-pre-wrapper');
          // console.log('CodeCElements elements found in markdownBody:', CodeCElements.length);

          // console.log('Pre elements found in markdownBody:', preElements.length);

          preElements.forEach((preElement) => {

            // 检查 <pre> 标签是否包含 v-md-prism-html 类
            const isHtmlBlock = preElement.classList.contains('v-md-prism-html');
            if (!isHtmlBlock) {
              return; // 如果不是 HTML，跳过
            } else {
              console.log('preElement111222333', preElement);
            }

            // 检查是否已经添加了工具栏
            if (!preElement.parentElement.querySelector('.code-toolbar')) {
              // 创建工具栏
              const toolbar = document.createElement('div');
              toolbar.className = 'code-toolbar';

              // 创建“展开”按钮
              const expandButton = document.createElement('button');
              expandButton.className = 'toolbar-button';
              expandButton.innerText = '展开';
              expandButton.addEventListener('click', () => {
                console.log('Expand button clicked'); // 调试日志
                preElement.classList.remove('Mycollapsed');
                expandButton.style.display = 'none';
                collapseButton.style.display = 'inline-block';
              });

              // 创建“收缩”按钮
              const collapseButton = document.createElement('button');
              collapseButton.className = 'toolbar-button';
              collapseButton.innerText = '收缩';
              collapseButton.style.display = 'none'; // 初始隐藏
              collapseButton.addEventListener('click', () => {
                console.log('Collapse button clicked'); // 调试日志
                preElement.classList.add('Mycollapsed');
                collapseButton.style.display = 'none';
                expandButton.style.display = 'inline-block';
              });

              // 创建“进入分屏”按钮
              const splitScreenButton = document.createElement('button');
              splitScreenButton.className = 'toolbar-button';
              splitScreenButton.innerText = '进入分屏';
              splitScreenButton.addEventListener('click', () => {
                // console.log('Split screen button clicked'); // 调试日志
                // const codeElement = preElement.querySelector('code');
                // const codeContent = codeElement ? codeElement.textContent : '提取有误';
                // // const mdContent = markdownBody? markdownBody.textContent : 'md提取有误';
                // // const codeContent = codeElement ? codeElement.innerHTML : '提取有误';
                // console.log('mdContent11111111122222', codeContent);
                // this.openSplitScreen(codeContent); // 调用分屏方法
                const editorElement = preElement.closest('.v-md-editor-preview');

                if (!editorElement || !editorElement.__vue__) {
                  console.error('没有找到对应的 Vue 实例');
                  return;
                }

                // 获取 Vue 实例
                const vueInstance = editorElement.__vue__;
                console.log('Vue 实例 123:', vueInstance.text);
                const mdContent = vueInstance.text
                this.openSplitScreen(mdContent);
                // console.log('Markdown content:', mdContentText);
              });

              // 默认展开代码块
              // preElement.classList.add('expanded');
              // preElement.style.maxHeight = 'none';  // 默认展开时没有最大高度限制
              expandButton.style.display = 'none'; // 初始隐藏“展开”按钮
              collapseButton.style.display = 'inline-block'; // 初始显示“收缩”按钮

              // 将按钮添加到工具栏
              toolbar.appendChild(expandButton);
              toolbar.appendChild(collapseButton);
              toolbar.appendChild(splitScreenButton); // 添加“进入分屏”按钮

              // 将工具栏添加到 pre 元素的外面
              const container = document.createElement('div');
              container.className = 'code-block-container';
              preElement.parentElement.insertBefore(container, preElement);
              container.appendChild(toolbar);
              container.appendChild(preElement);
            }
          });
        })
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.toggle-span {
  cursor: pointer;
  color: #007bff;
  font-size: 14px;
  margin-bottom: 10px;
  display: inline-block;
}

.toggle-span:hover {
  text-decoration: underline;
}
.scroll-container {
  height: 100%;
  overflow-y: auto; /* 允许垂直滚动 */
}
.maxHeight {
  max-height: 300px; /* 滚动容器的高度 */
}
:deep(.v-md-mermaid) {
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    height: 100%;
  }
}
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-image {
  max-width: 90%;
  max-height: 90%;
}
::v-deep .code-block-container {
  position: relative;
  /* 确保按钮相对于容器定位 */
  margin: 16px 0;
  /* 容器外边距 */
  width: 100%;
  /* 使用百分比宽度，使其在屏幕上自适应 */
  max-width: 900px;
  /* 固定代码块宽度 */
  // overflow: hidden;
  // /* 防止溢出的内容 */
  border-radius: 8px;
  /* 增加圆角使容器更加柔和 */
  background-color: #2a2a2a;
  /* 给容器背景加上暗黑色 */
  padding-top: 20px;
  /* 给容器添加顶部内边距，确保工具栏不遮挡代码 */
}

::v-deep .code-toolbar {
  position: absolute;
  /* 固定按钮位置 */
  top: 8px;
  /* 调整按钮距离容器顶部的距离 */
  left: 8px;
  /* 调整按钮距离容器右侧的距离 */
  z-index: 10;
  /* 确保按钮在顶部 */
  display: flex;
  /* 使用 flexbox 布局按钮 */
  gap: 10px;
  /* 按钮之间的间隔 */
  padding: 6px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.6);
  /* 背景色稍微透明 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  /* 增加阴影效果 */
}

::v-deep .toolbar-button {
  padding: 4px 8px;
  /* 增加内边距 */
  background-color: #3e3e3e;
  /* 按钮的背景颜色 */
  color: #fff;
  /* 按钮文字颜色 */
  border: 1px solid #555;
  /* 边框颜色与背景区分 */
  border-radius: 4px;
  /* 按钮圆角 */
  cursor: pointer;
  font-size: 12px;
  /* 调整字体大小 */
  transition: background-color 0.3s ease, transform 0.2s ease;
  /* 背景颜色变化和缩放效果 */

  /* 鼠标悬停效果 */
  &:hover {
    background-color: #4e4e4e;
    /* 鼠标悬停时改变背景颜色 */
    transform: scale(1.05);
    /* 鼠标悬停时增加按钮大小 */
  }

  /* 按钮点击效果 */
  &:active {
    background-color: #6a6a6a;
    /* 点击时背景色 */
    transform: scale(0.98);
    /* 点击时略微缩小按钮 */
  }
}

::v-deep pre {
  overflow: auto;
  /* 支持滚动条 */
  width: 100%;
  /* 确保宽度足够 */
  white-space: pre;
  /* 防止代码换行 */
  border-radius: 4px;
  /* 代码块圆角 */
  padding: 10px;
  /* 代码块内边距 */
  margin: 0;
  /* 清除默认外边距 */
  background-color: #1e1e1e;
  /* 代码块背景颜色 */
  color: #f8f8f2;
  /* 代码文字颜色 */
  font-family: 'Courier New', Courier, monospace;
  /* 使用等宽字体 */
  font-size: 14px;
  /* 调整字体大小 */
  line-height: 1.5;
  /* 增加代码行间距 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 代码块的阴影效果 */

  &.Mycollapsed {
    max-height: 300px !important;
    /* 折叠时限制最大高度 */
  }
}
/* 自定义滚动条样式 */
::v-deep pre::-webkit-scrollbar {
  width: 0px; /* 滚动条宽度 */
  height: 1px; /* 滚动条高度 */
}

::v-deep pre::-webkit-scrollbar-thumb {
  background-color: #666; /* 滚动条滑块颜色 */
  border-radius: 4px; /* 滑块圆角 */
}

::v-deep pre::-webkit-scrollbar-track {
  background-color: #f0f0f0; /* 滚动条轨道颜色 */
}


::v-deep {
  .custom-code-buttonYeahz {
    margin-left: 5px;
    padding: 8px 16px; // 按钮内边距
    background-color: #3e3e3e;
    color: white; // 文字颜色
    border: 1px solid #555;
    border-radius: 4px; // 圆角
    cursor: pointer; // 鼠标指针
    font-size: 14px; // 字体大小
    transition: background-color 0.3s ease, transform 0.2s ease; // 背景色变化和缩放效果

    &:hover {
      background-color: #4e4e4e;
      transform: scale(1.05); // 鼠标悬停时按钮稍微放大
    }

    &:active {
      background-color: #6a6a6a;
      transform: scale(0.98); // 点击时略微缩小按钮
    }

    &:focus {
      outline: none; // 去除焦点时的外框
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.6); // 焦点时的蓝色光环
    }
  }
}
:deep(.vuepress-markdown-body){
  overflow-y: hidden;
}
:deep(.vuepress-markdown-body p) {
    word-wrap: break-word;
    white-space: normal;
    width:fit-content;
    overflow-x: auto;
  }
 :deep(.katex-display) {
  padding:0 20px 10px 0;
  width: fit-content;
  overflow-y: hidden;
}
.parent * {
  width: inherit; /* 继承直接父元素的宽度 */
  box-sizing: border-box; /* 确保padding和border在元素的width内计算 */

  /* 处理溢出内容 */
  word-wrap: break-word; /* 允许长单词或URL地址换行到下一行 */
  overflow-wrap: break-word; /* 替代word-wrap，适用于更广泛的浏览器支持 */
  white-space: normal; /* 允许空白符按照普通文本处理，允许自动换行 */
}
:deep(.parent) {
  width: 100%; /* 继承直接父元素的宽度 */
  box-sizing: border-box; /* 确保padding和border在元素的width内计算 */

  /* 处理溢出内容 */
  word-wrap: break-word; /* 允许长单词或URL地址换行到下一行 */
  overflow-wrap: break-word; /* 替代word-wrap，适用于更广泛的浏览器支持 */
  white-space: normal; /* 允许空白符按照普通文本处理，允许自动换行 */
}
:deep(.v-md-editor__main){
  flex: none;
}
:deep(.previewNew .v-md-editor__right-area){
  flex: none;
  width: inherit;
}
</style>
<style>
.v-md-prism-html code{
    white-space: break-spaces;
}
.v-md-prism- code{
    white-space: break-spaces;
}
.vuepress-markdown-body ul > li {
  /* display: block !important; */
  white-space: normal !important;
  word-wrap: break-word;
}
.vuepress-markdown-body >p{
  display: block !important;
  white-space: normal !important;
  word-wrap: break-word;
}
</style>
<style lang="scss">
.previewNew{
  .v-md-editor--preview{
    height: 100%;
  }
  .v-md-editor__right-area{
    height: 100%;
  }
  .v-md-editor__main{
    height: 100%;
  }
}

</style>
