<template>
  <div class='select-container'>
    <div v-if="tagData.length >0" :class=" drapData.length > 0 ? 'tagslist' : 'tagslist'">
       <el-tooltip
        effect="dark"
        v-for="(item, index) in tagData"
        :content="item.label || ''"
        placement="top"
        >
         <el-tag size="small"  @click="handleCommand(item.key)" :class="item.label === '官方' ? 'tag-official' : ''" :style="{maxWidth: Number(100/tagData.length).toFixed(2) + '%'}" :key="index" :index="index" >{{ item.label | truncateString}}</el-tag>
        </el-tooltip>
      <!-- <el-tag size="small" v-for="(item, index) in tagData" @click="handleCommand(item.key)" :class="item.label === '官方' ? 'tag-official' : ''" :title="item.label" :style="{maxWidth: Number(100/tagData.length).toFixed(2) + '%'}" :key="index" :index="index" >{{item.label}}</el-tag> -->
      <div v-if="drapData.length >0" style="position: relative;" :class="drapData.length > 0 ? 'tagslistLast' : ''">
        <el-dropdown @click="handleCommand">
          <el-button type="info" size="mini">{{ drapData.length }}+</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in drapData" :command="item.key" :key="index" :index="index" >{{item.label}}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    
  </div>
</template>

<script>

export default {
  props: {
    arrayItems: {
      type: Array,
      default () {
        return []
      }
    },
    maxLength:{
      type: Number,
      default: 2
    },
  },
  data() {
    return {
      tagData:[],
      drapData:[]
    };
  },
  watch: {
    'arrayItems': {
      handler(val) {
        if (val && val.length>0) {
          this.tagData=val.slice(0,this.maxLength)
          this.drapData = val.slice(this.maxLength)
        }else{
          this.tagData = []
          this.drapData = []
        }
      },
      immediate: true,
    },
  },
    filters: {
    truncateString (val) {
      let length = 0;
      let result = '';
      for (let i = 0; i < val.length; i++) {
        const char = val[i];
          if (/[\u4e00-\u9fa5]/.test(char)) {
              // 汉字占一个字符
              length += 1;
          } else {
              // 非汉字半个字符
              length += 0.5;
          }
          if (length <= 5) {
              result += char;
          } else {
              result += '...';
              break;
            }
      }
      return result
      // return fmoney(val)
    }
  },
  methods: {
    handleCommand(command) {
      console.log('查询', command);
      this.$emit('clickFun',command)
    }
  }
};
</script>
<style lang="scss" scoped>
.select-container{
  display: flex;
  .tagslist {
    width: 100%;
    display: flex;
  }
  .el-tag{
    margin-right: 5px;
    border: none;
    border-radius: 2px;
    background: #EFF3FF;
    line-height: 24px;
    // max-width: calc(48%);
    white-space: nowrap;
    overflow: hidden;
    // text-overflow: ellipsis;
    &:hover {
      background: #E5E9F5;
    }
  }
  .tag-official {
    background: linear-gradient(to right, #def3ff, #dadbff) !important;
    color: #323233 !important;
  }
}
:deep(.el-button--mini) {
  padding: 2px !important;
  line-height: 12px !important;
  font-size: 12px !important;
  height: 22px !important;
  background: #EFF3FF !important;
  color: #4068D4;
}
</style>

