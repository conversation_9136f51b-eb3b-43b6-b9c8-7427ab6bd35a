<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide
        ? 'chatContainerTest chatContainerTestFrame'
        : 'chatContainerTest'
    "
  >
    <div
      :class="
        $store.state.planGenerate.isIframeHide
          ? 'containerBox2 containerBox2IFrame'
          : 'containerBox2'
      "
      style="width: 100%"
    >
      <div
        id="left-content"
        :style="{
          width: leftWidth,
          maxWidth: leftWidth,
          marginRight: !rightFullFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: 'relative'
        }"
        :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'"
      >
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">思维树</div>
            <div class="rightTitleOpt">
              <el-tooltip
                class="item"
                effect="dark"
                :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'"
                placement="top"
              >
                <el-button type="info" size="mini" @click="changeShowType"
                  ><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/markdown.png"
                /></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="rightFullFlag ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="rightFullFlag ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowFull"
                  ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                /></el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="optScroll">
            <div class="optContentBox">
              <div v-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
                <MyEditor id="MyEditorLast" ref="MyEditorLast" :md-content="treeData"></MyEditor>
              </div>
              <div v-else>
                <pre>{{ treeData }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="planDetailShow && !rightFullFlag"
        id="resize"
        class="resize"
        title="收缩侧边栏"
        @mousedown="startDrag"
      >
        <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
        <div class="el-two-column__trigger-icon">
          <SvgIcon name="dragborder" class="process-icon" />
        </div>
        <div class="el-two-column__icon-bottom">
          <div class="el-two-column__icon-bottom-bar"></div>
        </div>
      </div>
      <div
        id="right-content"
        :style="{
          width: rightWidth,
          marginRight: '16px',
          transition: isDragging ? 'none' : 'width 0.2s',
          userSelect: isDragging ? 'none' : 'auto'
        }"
        :class="!planDetailShow ? 'chatRight chatRightFull' : 'chatRight'"
      >
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">智能能力测试与迭代</div>
            <div class="rightTitleOpt">
              <el-tooltip
                v-if="!rightFullFlag"
                class="item"
                effect="dark"
                :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="!planDetailShow ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowRight"
                >
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                  />
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-loading="taskLoading"
            class="optScroll"
            element-loading-text="测试中..."
            element-loading-spinner="el-icon-loading"
          >
            <div class="searchFilter" :style="{ justifyContent: 'flex-end' }">
              <el-button
                class="button-last"
                :style="{ marginLeft: '8px', marginBottom: '16px' }"
                type="primary"
                @click="onTest"
                >测试</el-button
              >
            </div>
            <div>{{ ruleContent }}</div>
            <div class="test-result">
              <div>
                测试结果：<span v-if="tried" class="test-result-warn"
                  >如果对测试结果不满意，可返回到之前的步骤重新调整</span
                >
              </div>
              <JsonViewer
                v-if="tried && !streamFlag"
                class="json-content"
                :value="jsonData"
                :expand-depth="5"
              ></JsonViewer>
              <div v-if="streamFlag" style="color: #323233; width: 100%; max-height: 400px">
                {{ jsonData }}
              </div>
              <!-- <MyEditor v-if="streamFlag" id="jsonData" ref="jsonData" :md-content="jsonData" style="width: 100%;max-height: 500px"></MyEditor> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="optFooter">
      <el-button
        class="button-last"
        :disabled="treeStatusLast === 1 || treeStatusLast === 0"
        type="primary"
        @click="changeViews(activeStep - 1)"
        >上一步</el-button
      >
      <el-button
        class="button-last"
        :disabled="
          treeStatusLast === 1 ||
          treeStatusLast === 0 ||
          loading ||
          taskLoading ||
          lastLoading ||
          hasChatingName !== ''
        "
        type="primary"
        @click="handlePublish"
        >发布</el-button
      >
    </div>
    <!-- 发布窗口 -->
    <el-dialog
      custom-class="last-dialog"
      :visible.sync="dialogVisible2"
      title="能力信息"
      :show-close="!lastLoading"
    >
      <el-form ref="lastFormRef" :model="lastFormModal" label-width="120px">
        <el-form-item label="名称">
          <el-input
            v-model="lastFormModal.name"
            disabled
            maxlength="150"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item
          v-for="(domain, index) in lastFormModal.domains"
          :key="domain.key"
          :label="domain.label"
          :prop="'domains.' + index + '.value'"
          :rules="{
            required: true,
            message: `${domain.label}不能为空`,
            trigger: 'blur'
          }"
        >
          <el-input v-if="domain.type === 'input'" v-model="domain.value" />
          <el-cascader
            v-if="domain.type === 'ability'"
            :ref="'domainsSelect' + index"
            v-model="domain.value"
            :options="domain.domainsOptions"
            style="width: 100%"
          />
        </el-form-item>

        <!-- <el-form-item label="是否是物联">
          <el-select v-model="lastFormModal.iot_type" placeholder="请选择">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="描述">
          <el-input
            v-model="lastFormModal.description"
            type="textarea"
            maxlength="250"
            placeholder="请输入内容"
            resize="none"
          />
        </el-form-item>
      </el-form>
      <div style="margin-top: 20px; display: flex; justify-content: end">
        <el-button type="primary" :loading="lastLoading" @click="onPublish">确认</el-button>
        <el-button :disabled="lastLoading" @click="cancel2">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetDecision,
  SchemeSaveKnow,
  CodePublish,
  UpdateSchemeStatus,
  querySchemeDetailById,
  getPublishAbilityDich,
  getExecuteSync
} from '@/api/planGenerateApi.js'
import { OnRuleTest, RuleMarketAbilityPublish } from '@/api/ruleMarketApi.js'
import panzoom from 'panzoom'
import dayjs from 'dayjs'
import service from '@/axios'
import JsonViewer from 'vue-json-viewer'
import MyEditor from './mdEditorPreview.vue'
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue'
const chatXKey = {
  dev: '3mAatDrr5DBBTgzcgrbLyjpdw2mwwALs',
  fat: 'uXnSpC7JDP6mNC6SFyAqNG1r45apCJPd',
  production: 'dZl9xUDmcJlRc9eLTm68P7R8qNWRzKM1'
}

export default {
  components: {
    JsonViewer,
    MyEditor,
    shardUploaderTool
  },
  props: {
    activeStep: {
      type: Number,
      required: true
    },
    agentSenceCode: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    sqlStatus: {
      type: Number,
      default: -1
    },
    sqlData: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    sessionId: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      loading: false,
      loading2: false,
      saveLoading: false,
      tableData: [],
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      planDetailShow: true,
      rightFullFlag: false,
      taskLoading: false,
      jueceYulanFlag: true,
      treeData: '',
      ruleContent: '',
      searchData: {
        time: '',
        equipId: '',
        equipName: '',
        equipList: [],
        globalList: []
      },
      pageNo: 1,
      pageSize: 10,
      dialogVisible2: false, // 发布到集市标志
      lastFormModal: {
        name: this.$route.query.name,
        description: '',
        // iot_type: '0',
        domains: []
      },
      lastLoading: false,
      dialogVisible: false, // 时序数据显示
      dpsData: {
        list: [],
        pageNo: 1,
        pageSize: 10
      }, // 时序数据
      tried: false,
      jsonData: {},
      fixFiled: {},
      gridData: [],
      dataThs: [],
      dataThsDatas: [],
      filesList: [],
      newTableData: [],
      schemeInfo: {}
    }
  },
  computed: {},
  watch: {
    treeStatus: {
      handler(val) {
        this.treeStatusLast = val
        if (val === 2) {
          this.taskLoading = false
          // this.handleAbilityMapping()
        } else if (val === 0) {
          this.taskLoading = false
        } else if (val === 3) {
          this.taskLoading = false
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.codeData = val
      },
      immediate: true
    }
  },
  async created() {},
  beforeDestroy() {},
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    this.schemeDetailById()
    this.taskStatus = this.$route.query.status
    await this.queryDecision('decision_tree') // 思维树
    await this.queryDecision('rule') // 思维树
  },
  methods: {
    queryEqu() {
      this.loading = true
      this.lastFormModal.domains = []
      getPublishAbilityDich({ scene_id: this.schemeInfo.agent_scene })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data?.result || []
            if (result.length > 0) {
              for (const item of result) {
                const domainItem = {
                  label: item.field_name,
                  key: item.field_code,
                  value: '',
                  type: item.field_val.type || '',
                  domainsOptions: []
                }
                if (item.field_val.type === 'ability') {
                  const res = await getExecuteSync({
                    scene_instance_id: this.$route.query.id,
                    ability_id: item.field_val.ability_id,
                    name: item.field_val.ability_id,
                    goal: ''
                  })
                  if (res.data && res.data.length > 0) {
                    const resultList = res.data
                    if (resultList && resultList.length > 0) {
                      resultList.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                          item.children.forEach((it) => {
                            if (it.children && it.children.length === 0) {
                              delete it.children
                            }
                          })
                        }
                      })
                      domainItem.domainsOptions = resultList
                    }
                  }
                  this.lastFormModal.domains.push(domainItem)
                } else {
                  this.lastFormModal.domains.push(domainItem)
                }
              }
            }
          }
          this.dialogVisible2 = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlePublish() {
      this.queryEqu()
    },
    // 测试
    onTest() {
      this.abilityTestZhuGe('测试')
      this.taskLoading = true
      OnRuleTest({
        scheme_id: this.$route.query.id,
        rule_content: this.ruleContent // 规则内容
      })
        .then(async (res) => {
          this.taskLoading = false
          if (res.status === 200 && res.data.code === 200) {
            this.tried = true
            this.jsonData = res.data.result?.result || {}
            console.log('测试结果', this.jsonData)
            await UpdateSchemeStatus({
              scheme_id: this.$route.query.id,
              scheme_status: 'ability_generate_and_debugging'
            })
            this.tried = true
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.taskLoading = false
        })
    },
    abilityTestZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
      })
    },
    // 查询思维树
    queryDecision(type) {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: type || 'decision_tree'
      }
      this.loading = true
      GetDecision(params)
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            if (type === 'decision_tree') {
              this.treeData = res.data.result?.decision_making_content || ''
              this.treeDataProcess = res.data.result?.sub_content || ''
            } else {
              this.ruleContent = res.data.result?.decision_making_content || ''
            }
            this.loading = false
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('详情', res.data.result)
            this.schemeInfo = res.data.result || { ...this.$route.query }
            this.lastFormModal.name = res.data.result?.name
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
    },
    // 确定发布
    onPublish() {
      console.log('发布到集市')
      this.abilityTestZhuGe('发布')
      this.$refs.lastFormRef.validate(async (validate) => {
        if (validate) {
          this.lastLoading = true
          const outputFields = this.lastFormModal.domains.map((field) => {
            const obj = {}
            obj.field_name = field.label
            obj.field_code = field.key
            if (field.type === 'input') {
              obj.field_val = { value: field.value, label: field.value }
            } else {
              if (field.value && field.value.length === 1) {
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(field.value[0])
                )
                obj.field_val = {
                  value: selectedOptions.value,
                  label: selectedOptions.label,
                  parentValue: selectedOptions.parentValue
                }
              } else if (field.value && field.value.length === 2) {
                const selectedValues = field.value
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(selectedValues[0])
                )
                const selectedSubOptions = selectedOptions?.children?.find(
                  (child) => child.value === selectedValues[1]
                )
                obj.field_val = {
                  value: selectedSubOptions.value,
                  label: selectedSubOptions.label,
                  parentValue: selectedSubOptions.parentValue
                }
              }
            }
            return obj
          })
          console.log(outputFields)

          const all = [
            SchemeSaveKnow({
              scheme_id: this.$route.query.id,
              op_type: 'publish',
              iot_type: Number(this.lastFormModal.iot_type),
              scheme_detail_name: this.schemeInfo.name || this.schemeInfo.scheme_detail_name || ''
            }),
            RuleMarketAbilityPublish({
              scheme_id: Number(this.$route.query.id),
              rule_content: this.ruleContent,
              description: this.lastFormModal.description,
              ext_data_info: outputFields
            })
          ]
          try {
            await Promise.all(all)
            const results1 = await CodePublish({ scheme_id: this.$route.query.id })
            if (results1.data.result) {
              this.lastLoading = false
              this.$message({
                type: 'success',
                message: '发布成功',
                duration: 1500,
                onClose: () => {
                  this.lastLoading = false
                }
              })
              // 向外层页面传递消息
              this.lastLoading = false
              console.log('向上发布消息', { success: true, result: results1.data.result })
              window.parent.postMessage(
                JSON.stringify({ success: true, result: results1?.data?.result }),
                '*'
              )

              this.$router.push({
                path: '/alignmentRules/index',
                query: {
                  workspaceId: this.$route.query.workspaceId,
                  workspaceName: this.$route.query.workspaceName
                }
              })
            } else {
              this.$message.error(results1.data?.message || '发布失败')
              this.lastLoading = false
            }
          } catch (e) {
            this.$message.error(e.message || e)
            this.lastLoading = false
          }
        }
      })
    },
    // 关闭发布窗口
    cancel2() {
      this.dialogVisible2 = false
      this.lastFormModal.description = ''
      this.lastFormModal.name = this.schemeInfo.name
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
    },

    changeViews(val, type) {
      this.$emit('updateStep', val)
      this.abilityTestZhuGe('上一步')
    },
    // 两栏布局拖拽
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = '50%'
        this.rightWidth = '100%'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.upload-demo) {
  flex-direction: column;
}
:deep(.el-upload) {
  text-align: left !important;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 40px;
}
:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
}
:deep(.el-button:not(.el-button--text), .el-button.el-button--primary) {
  line-height: 20px !important;
}
:deep(.el-date-editor--datetime .el-input__prefix) {
  left: calc(100% - 30px);
}
:deep(.el-input__icon) {
  line-height: 30px;
}
:deep(.el-date-editor--datetime .el-input__suffix) {
  right: 25px;
}
:deep(.el-input--prefix .el-input__inner) {
  padding-left: 10px;
}
:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}
.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }
    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }
    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }
    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }
    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }
}
.page {
  height: 30px;
  justify-content: flex-end;
  display: flex;
  margin-top: 10px;
  ::v-deep .el-pagination:not(.new-paper) button {
    width: 30px;
  }
}
.el-pagination {
  height: 20%;
}
.test-result {
  // border-top: 1px solid #EBECF0;
  margin-top: 16px;
  padding-top: 16px;
  margin-bottom: 36px;
  .test-result-warn {
    margin-top: 2px;
    font-size: 12px;
    color: red;
  }
}
.searchFilter {
  display: flex;
  flex-direction: row;
  align-items: center;
  .demonstration {
    color: #646566;
    word-break: keep-all;
    margin-right: 4px;
  }
}
.chatContainerTest {
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 164px);
  max-height: calc(100vh - 164px);
  position: relative;
  &.chatContainerTestFrame {
    height: calc(100vh - 104px);
    max-height: calc(100vh - 104px);
  }
  .optFooter {
    z-index: 3;
    height: 54px;
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 20px;
    min-height: 54px;
  }

  .containerBox2 {
    display: flex;
    flex-direction: row;
    height: calc(100% - 54px);
    max-height: calc(100% - 54px);
    overflow-y: hidden;
    position: relative;
    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;
      &:hover {
        background: #3455ad;
      }
      &:active {
        background: #264480;
      }
      img {
        width: 12px;
        height: auto;
      }
    }
    .containerCard {
      overflow-y: hidden;
      overflow-x: hidden;
      margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      background-color: #fff;
      margin-left: 16px;
      height: calc(100% - 16px);
      max-height: calc(100% - 16px);
      &.containerCardFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        max-height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        background: #fff;
        .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }
        .optContentBox {
          width: 100%;
          min-height: calc(100vh - 150px) !important;
        }
      }
      .optContentBox {
        width: 100%;
        min-height: calc(100vh - 330px);
        display: flex;
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 330px);
        max-height: calc(100vh - 330px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .optContent {
        height: 100%;
        // max-height: calc(100% - 60px);
        overflow-y: hidden;
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
      .chatHeader {
        font-size: 14px;
        color: #323233;
        line-height: 24px;
        font-weight: bold;
        background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
    }
    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      height: calc(100% - 16px);
      max-height: calc(100% - 16px);
      overflow-y: hidden;
      margin-top: 16px;
      position: relative;
      &.chatRightFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 102px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        .optScroll {
          height: calc(100vh - 140px) !important;
          max-height: calc(100vh - 140px) !important;
        }
        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }
        .optContentBox {
          height: calc(100vh - 180px) !important;
        }
      }
      .optContentBox {
        height: calc(100vh - 340px);
        width: 100%;
        position: relative;
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 320px);
        max-height: calc(100vh - 320px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
        .table-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          .duiqi-box {
            height: 50%;
            overflow: hidden;
            padding-bottom: 30px;
            .title {
              margin-bottom: 16px;
            }
            .transition-box {
              table {
                height: 100%;
              }
            }
            .page {
              height: 30px;
              justify-content: flex-end;
              display: flex;
              margin-top: 10px;
              ::v-deep .el-pagination:not(.new-paper) button {
                width: 30px;
              }
            }
            .el-pagination {
              height: 20%;
            }
          }
        }
      }
      .optContent {
        max-height: calc(100% - 10px);
        overflow-y: hidden;
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }
  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:hover {
      background: #e0e6ff;
      .process-icon {
        color: #3455ad !important;
      }
    }
    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;
      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }
    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;
      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }
    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;
      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }
  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    line-height: 20px !important;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button.el-button--primary {
    line-height: 20px;
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    line-height: 20px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
}
.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}
::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
<style lang="scss">
.mytest {
  font-size: 14px;
  .el-button--text {
    display: none !important;
  }
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
</style>
