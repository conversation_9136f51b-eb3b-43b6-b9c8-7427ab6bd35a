<template>
  <div :class="$store.state.planGenerate.isIframeHide ? 'containerBox2 containerBox2IFrame' : 'containerBox2'"
       style="width: 100%;">
    <div id="left-content"
         :style="{ width: leftWidth,maxWidth: leftWidth, marginRight: !rightFullFlag ? '0px' : '16px', userSelect: isDragging ? 'none' : 'auto', transition: isDragging ? 'none' : 'width 0.2s', position: thinkFullFlag ? '' : 'relative'}"
         :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'">
      <div
        v-if="agentSenceCode ==='device_ops_assistant_scene' || agentSenceCode ==='device_ops_assistant_scene-v1' || agentSenceCode ==='artificial_handle_scene' || agentSenceCode ==='visit_leader_cognition_scene' || agentSenceCode === 'rule_generation_scene' || agentSenceCode === 'intelligent_conversation_scene'"
        class="optContent">
        <div class="optHeader">
          <div class="rightTitle">思维树</div>
          <div class="rightTitleOpt">
            <el-tooltip class="item" effect="dark" :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'"
                        placement="top">
              <el-button type="info" size="mini" @click="changeShowType"><img v-if="jueceYulanFlag"
                                                                              src="@/assets/images/planGenerater/text.png" /><img
                v-else src="@/assets/images/planGenerater/markdown.png" /></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="rightFullFlag ? '退出全屏' : '全屏'" placement="top">
              <el-button :type="rightFullFlag ? 'primary' : 'info'" size="mini" @click="changeShowFull"><img
                v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img v-else
                                                                                          src="@/assets/images/planGenerater/tuichuquanping.png" />
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="optScroll">
          <div class="optContentBox">
            <div v-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
              <MyEditor id="MyEditorDuiqi" ref='MyEditorDuiqi' :md-content="treeData"></MyEditor>
              <!-- <pre v-if="(treeData && treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
              <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
            </div>
            <div v-else>
              <pre>{{ treeData }}</pre>
            </div>
          </div>
        </div>
      </div>
      <div class="optFooter">
        <el-button v-if="agentSenceCode ==='artificial_handle_scene'" class="button-last"
                   :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName !== ''" type="primary"
                   @click="regenerateRengong()">
          {{ treeStatusLast === 1 || treeStatusLast === 0 ? '多模态数据对齐中...' : (codeData !== '' ? '重新多模态数据对齐' : '多模态数据对齐') }}
        </el-button>
        <el-button v-else class="button-last"
                   :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName!== ''" type="primary"
                   @click="regenerate()">
          {{ treeStatusLast === 1 || treeStatusLast === 0 ? '多模态数据对齐中...' : (codeData !== '' ? '重新多模态数据对齐' : '多模态数据对齐') }}
        </el-button>
      </div>
    </div>
    <div v-if="planDetailShow && !rightFullFlag" id="resize" class="resize" title="收缩侧边栏" @mousedown="startDrag">
      <div class="el-two-column__icon-top">
        <div class="el-two-column__icon-top-bar"></div>
      </div>
      <div class="el-two-column__trigger-icon">
        <SvgIcon name="dragborder" class="process-icon" />
      </div>
      <div class="el-two-column__icon-bottom">
        <div class="el-two-column__icon-bottom-bar"></div>
      </div>
    </div>
    <div id="right-content"
         :style="{ width: rightWidth, marginRight: '16px', transition: isDragging ? 'none' : 'width 0.2s', userSelect: isDragging ? 'none' : 'auto'}"
         :class="!planDetailShow ? 'chatRight chatRightFull' : 'chatRight'">
      <div
        v-if="agentSenceCode ==='device_ops_assistant_scene' || agentSenceCode ==='device_ops_assistant_scene-v1' || agentSenceCode ==='visit_leader_cognition_scene' || agentSenceCode === 'rule_generation_scene' || agentSenceCode === 'intelligent_conversation_scene'"
        class="optContent">
        <div class="optHeader">
          <div class="rightTitle">多模态数据对齐</div>
          <div class="rightTitleOpt">
            <!-- <el-button :disabled="treeStatusLast === 1 || treeStatusLast === 0 " type="info" @click="handleDone">保存</el-button> -->
            <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
              <el-button type="info" @click="showSikao">生成过程</el-button>
            </el-tooltip> -->
            <el-tooltip v-if="!rightFullFlag" class="item" effect="dark"
                        :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
              <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
                                                                                               src="@/assets/images/planGenerater/tuichuquanping.png" />
              </el-button>
            </el-tooltip>
            <el-dropdown style="margin-left: 10px;" @command="handleCommand">
              <el-button type="info" :disabled="hasChatingName!==''" size="mini"><img
                src="@/assets/images/planGenerater/more.png" /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="isCacheDisabled ||gridData.length ===0 ||treeStatusLast === 1 || treeStatusLast === 0"
                  :command="isCache?'removeCache':'addCache'">{{ isCache ? '取消缓存' : '缓存' }}
                </el-dropdown-item>
                <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div v-loading="taskLoading" class="optScroll" element-loading-text="多模态数据对齐中..."
             element-loading-spinner="el-icon-loading">
          <template v-if="isEdit">
            <el-input id="detail-content" v-model.trim="codeData" type="textarea" :autosize="{ minRows: 2, maxRows: 18}"
                      placeholder="请输入" />
          </template>
          <template v-else>
            <div v-if="treeStatus === 3" style="width: 100%;height:100%">
              <div
                style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto" />
                <div
                  style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">
                  对齐失败，请
                  <el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''"
                           @click="regenerate()">重试
                  </el-link>
                </div>
              </div>
              <!-- <el-alert :closable="false" type="error">
                <span slot="title">对齐失败<el-link :underline="false" :disabled="hasChatingName !==''" style="vertical-align:baseline" @click="regenerate()">重试</el-link></span>
              </el-alert> -->
            </div>
            <!-- dataStatus === 'generating' || loading -->
            <div v-else-if="dataStatus === 'failed'" style="margin-top: 20px;width:100%;height:100%">
              <div
                style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto" />
                <div
                  style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">
                  数据抽取失败，请
                  <el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''"
                           @click="regenerate()">重试
                  </el-link>
                </div>
              </div>
              <!-- <el-result icon="error" title="数据抽取失败！">
                <el-link :underline="false" :disabled="hasChatingName !==''" @click="regenerate()">重试</el-link>
              </el-result> -->
            </div>
            <div v-else v-loading="dataStatus === 'generating' || loading || changeloading"
                 style="width: 100%;height: 100%;" element-loading-text="加载中..."
                 element-loading-spinner="el-icon-loading" custom-class="el-loading-spinner2" class="table-box">
              <!-- <div class="btns">
                <span :class="['btns-item', showType == 1 ? 'active' : '']" @click="changeChart(1)">
                  库表多模态数据对齐
                </span>
                <span :class="['btns-item', showType == 2 ? 'active' : '']" @click="changeChart(2)">
                  算法公式对齐
                </span>
              </div> -->
              <div ref="tabelRef" class="duiqi-box">
                <!-- <div class="title">库表多模态数据对齐</div> -->
                <el-table v-show="dataStatus !== 'failed' && dataThDatas.length" size="medium"
                          :height="displayData.length ? tableHeight : 40" style="width: 100%;"
                          :header-cell-style="{ background: '#F6F7FB', color: '#323233' }" class="transition-box"
                          :data="displayData">
                  <el-table-column v-for="(item, index) in dataThDatas" :key="item.field" min-width="170"
                                   :prop="item.field" :label="item.name">
                    <template #default="scope">
                      <div v-if="item.field === 'a_param_desc'">
                        <el-input v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''"
                                  placeholder="请输入"></el-input>
                      </div>
                      <div v-else-if="item.field === 'b_type'">
                        <el-select v-model="scope.row[item.field]" :disabled="hasChatingName !==''" placeholder="请选择"
                                   @change="(val) => changeCType(val, scope.$index)">
                          <el-option
                            v-for="item in configList" :key="item.value"
                            :label="item.name"
                            :value="item.value"></el-option>
                          <!-- <el-option label="API" value="API"></el-option>
                          <el-option label="库表" value="库表"></el-option> -->
                        </el-select>
                      </div>
                      <div v-else-if="item.field === 'c_source'">
                        <el-select v-if="scope.row.b_type === '库表'" v-model="scope.row[item.field]"
                                   :disabled="hasChatingName !==''" placeholder="请选择"
                                   @change="(val) => handleFiedsByTable(val, scope.$index)">
                          <el-option
                            v-for="item in tablesList"
                            :key="item.table"
                            :label="item.table"
                            :value="item.table">
                          </el-option>
                        </el-select>
                        <el-select v-else-if="scope.row.b_type === 'API'" v-model="scope.row[item.field]"
                                   :disabled="hasChatingName !==''" placeholder="请选择"
                                   @change="(val) => handleFiedsByAPI(val, scope.$index)">
                          <el-option v-for="apiType in useAPIList" :label="apiType.value"
                                     :value="apiType.name"></el-option>
                        </el-select>
                        <el-select v-else v-model="scope.row[item.field]" :disabled="hasChatingName !==''"
                                   placeholder="请选择">
                          <el-option label="人工输入" value="人工输入"></el-option>
                        </el-select>
                      </div>
                      <div v-else-if="item.field === 'd_mapping_field'">
                        <el-select v-if="scope.row.b_type === '库表'" :key="index + 'field'"
                                   v-model="scope.row[item.field]" :disabled="hasChatingName !==''" filterable
                                   placeholder="请选择" @change="(val) => handleFiledChange(val, scope.$index)">
                          <el-option
                            v-for="(fitem, findex) in scope.row.fieldsList"
                            :key="index + fitem.COLUMN_NAME + findex"
                            :label="fitem.COLUMN_NAME"
                            :value="fitem.COLUMN_NAME">
                            <span style="float: left">{{ fitem.COLUMN_NAME }}</span>
                            <span style="float: right; color: #8492a6; font-size: 12px">{{ fitem.COLUMN_COMMENT
                              }}</span>
                          </el-option>
                        </el-select>

                        <el-select v-else-if="scope.row.b_type === 'API'" v-model="scope.row[item.field]"
                                   :disabled="hasChatingName !==''" filterable placeholder="请选择"
                                   @change="(val) => handleFiledApiChange(val, scope.$index)">
                          <el-option
                            v-for="(fitem, findex) in scope.row.fieldsList"
                            :key="fitem.code_func_name + findex"
                            :label="fitem.code_name"
                            :value="fitem.code_func_name">
                          </el-option>
                        </el-select>
                        <el-input v-else v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''" clearable
                                  placeholder="请输入" />
                      </div>
                      <div v-else>
                        <div v-if="scope.row.b_type === 'API' || scope.row.b_type === '库表'">
                          {{ scope.row[item.field] }}
                        </div>
                        <el-input v-else v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''" clearable
                                  placeholder="请输入" />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="x"
                    fixed="right"
                    label="操作"
                    class-name="no-bor"
                    width="94"
                  >
                    <template slot-scope="scope">
                      <el-tooltip v-if="scope.row.b_type === 'API' && scope.row.d_mapping_field" class="item"
                                  effect="dark" content="接口详情" placement="top">
                        <el-link :underline="false" icon="el-icon-paperclip" style="margin-right: 12px;"
                                 @click="handleLink(scope.row, scope.$index)"></el-link>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="删除" placement="top">
                        <el-link :underline="false" :disabled="hasChatingName !== ''" icon="el-icon-remove-outline"
                                 @click="handleRemove(scope.$index)"></el-link>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="新增" placement="top">
                        <el-link v-if="isLastRow(scope.$index)" :disabled="hasChatingName !== ''" :underline="false"
                                 style="margin-left: 12px;" icon="el-icon-circle-plus-outline"
                                 @click="handleAddRow(displayData.length)"></el-link>
                        <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                      </el-tooltip>
                      <!-- <el-link type="primary" @click="handleRemove(scope.$index)">删除</el-link> -->
                    </template>
                  </el-table-column>
                </el-table>
                <div v-show="dataStatus !== 'failed' && dataThDatas.length && displayData.length === 0"
                     style="text-align: right;padding-right:8px">
                  <el-tooltip class="item" effect="dark" content="新增" placement="top">
                    <el-link :underline="false" :disabled="hasChatingName!==''" style="margin-left: 12px;"
                             icon="el-icon-circle-plus-outline" @click="handleAddRow(displayData.length)"></el-link>
                    <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                  </el-tooltip>
                </div>
                <div v-if="gridData.length" class="page">
                  <el-pagination
                    small
                    :page-size="pageSize"
                    :current-page="pageNo"
                    layout="prev, pager, next"
                    :total="gridData.length"
                    @current-change="handleCurrentChange">
                  </el-pagination>
                </div>
                <div v-if="!dataThDatas.length" class="emptyData">
                  <img src="@/assets/images/planGenerater/empty.png" />
                  <div style="font-size: 14px;color: #323233;">暂无内容</div>
                </div>
              </div>
              <!-- <div style='margin-top:15px;width: 100%;display: flex;flex-direction: column;align-items: center'> -->
              <!-- <el-button v-if="dataThDatas.length" type="primary" :disabled ="saveLoading" @click="handleDone">完成</el-button> -->
              <!-- </div> -->
              <!-- <vue-markdown v-highlight :source="codeData" class="markdown-body"></vue-markdown> -->
            </div>
          </template>
        </div>
      </div>
      <div v-if="agentSenceCode ==='artificial_handle_scene'" class="optContent">
        <div class="optHeader">
          <div class="rightTitle">多模态数据对齐</div>
          <div class="rightTitleOpt">
            <el-tooltip v-if="!rightFullFlag" class="item" effect="dark"
                        :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
              <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
                                                                                               src="@/assets/images/planGenerater/tuichuquanping.png" />
              </el-button>
            </el-tooltip>
            <el-dropdown style="margin-left: 10px;" @command="handleCommand">
              <el-button type="info" :disabled="hasChatingName !== ''" size="mini"><img
                src="@/assets/images/planGenerater/more.png" /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div v-loading="taskLoading" class="optScroll" element-loading-text="多模态数据对齐中..."
             element-loading-spinner="el-icon-loading" cl>
          <template v-if="isEdit">
            <el-input id="detail-content" v-model.trim="codeData" type="textarea" :autosize="{ minRows: 2, maxRows: 18}"
                      placeholder="请输入" />
          </template>
          <template v-else>
            <div v-if="treeStatus === 3" style="width: 100%;height:100%">
              <div
                style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto" />
                <div
                  style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">
                  对齐失败，请
                  <el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''"
                           @click="regenerateRengong()">重试
                  </el-link>
                </div>
              </div>
              <!-- <el-alert :closable="false" type="error">
                <span slot="title">对齐失败<el-link :underline="false" style="vertical-align:baseline" :disabled="hasChatingName !==''" @click="regenerateRengong()">重试</el-link></span>
              </el-alert> -->
            </div>
            <!-- dataStatus === 'generating' || loading -->
            <div v-else-if="dataStatus === 'failed'" style="margin-top: 20px;width:100%;height:100%">
              <div
                style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto" />
                <div
                  style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">
                  数据抽取失败，请
                  <el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''"
                           @click="regenerateRengong()">重试
                  </el-link>
                </div>
              </div>
              <!-- <el-result icon="error" title="数据抽取失败！">
                <el-link :underline="false" :disabled="hasChatingName !==''" @click="regenerateRengong()">重试</el-link>
              </el-result> -->
            </div>
            <div v-else v-loading="dataStatus === 'generating' || loading || changeloading"
                 style="width: 100%;height: 100%;" element-loading-text="加载中..."
                 element-loading-spinner="el-icon-loading" custom-class="el-loading-spinner2" class="table-box">
              <div ref="tabelRef" class="duiqi-box">
                <!-- <div class="title">库表多模态数据对齐</div> -->
                <el-table v-show="dataStatus !== 'failed' && dataThDatas.length" size="medium"
                          :height="displayData.length ? tableHeight : 40" style="width: 100%;"
                          :header-cell-style="{ background: '#F6F7FB', color: '#323233' }" class="transition-box"
                          :data="displayData">
                  <el-table-column v-for="(item, index) in dataThDatas" :key="item.field" min-width="170"
                                   :prop="item.field" :label="item.name">
                    <template #default="scope">
                      <div v-if="item.field === 'd_mapping_field'">
                        <el-input v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''"
                                  placeholder="请输入"></el-input>
                      </div>
                      <div v-else-if="item.field === 'e_mapping_desc'">
                        <el-input v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''"
                                  placeholder="请输入"></el-input>
                      </div>
                      <div v-else>
                        <div v-if="scope.row.isAdd && item.field !== 'b_type' && item.field !== 'c_source'">
                          <el-input v-model.trim="scope.row[item.field]" :disabled="hasChatingName !==''"
                                    placeholder="请输入"></el-input>
                        </div>
                        <div v-else>{{ scope.row[item.field] }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="x"
                    fixed="right"
                    label="操作"
                    class-name="no-bor"
                    width="64"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="dark" content="删除" placement="top">
                        <el-link :underline="false" :disabled="hasChatingName!==''" icon="el-icon-remove-outline"
                                 @click="handleRemove(scope.$index)"></el-link>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="新增" placement="top">
                        <el-link v-if="isLastRow(scope.$index)" :disabled="hasChatingName!==''" :underline="false"
                                 style="margin-left: 12px;" icon="el-icon-circle-plus-outline"
                                 @click="handleAddRow(displayData.length)"></el-link>
                        <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                      </el-tooltip>
                      <!-- <el-link type="primary" @click="handleRemove(scope.$index)">删除</el-link> -->
                    </template>
                  </el-table-column>
                </el-table>
                <div v-show="dataStatus !== 'failed' && dataThDatas.length && displayData.length === 0"
                     style="text-align: right;padding-right:8px">
                  <el-tooltip class="item" effect="dark" content="新增" placement="top">
                    <el-link :underline="false" :disabled="hasChatingName!==''" style="margin-left: 12px;"
                             icon="el-icon-circle-plus-outline" @click="handleAddRow(displayData.length)"></el-link>
                    <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                  </el-tooltip>
                </div>
                <div v-if="!dataThDatas.length" class="emptyData">
                  <img src="@/assets/images/planGenerater/empty.png" />
                  <div style="font-size: 14px;color: #323233;">暂无内容</div>
                </div>
                <div v-if="gridData.length" class="page">
                  <el-pagination
                    small
                    :page-size="pageSize"
                    :current-page="pageNo"
                    layout="prev, pager, next"
                    :total="gridData.length"
                    @current-change="handleCurrentChange">
                  </el-pagination>
                </div>
              </div>
              <!-- <div style='margin-top:15px;width: 100%;display: flex;flex-direction: column;align-items: center'> -->
              <!-- <el-button v-if="dataThDatas.length" type="primary" :disabled ="saveLoading" @click="handleDone">完成</el-button> -->
              <!-- </div> -->
              <!-- <vue-markdown v-highlight :source="codeData" class="markdown-body"></vue-markdown> -->
            </div>
          </template>
        </div>
      </div>
      <div class="optFooter">
        <!-- <el-button :disabled="treeStatusLast === 1 || treeStatusLast === 0 " type="primary" @click="showSqlModal">生成sql</el-button> -->
        <el-button class="button-last" :disabled="treeStatusLast === 1 || treeStatusLast === 0" type="primary"
                   @click="changeViews(1)">上一步
        </el-button>
        <el-button
          v-if="agentSenceCode ==='device_ops_assistant_scene' || agentSenceCode ==='device_ops_assistant_scene-v1' || agentSenceCode ==='artificial_handle_scene' || agentSenceCode ==='visit_leader_cognition_scene' || agentSenceCode === 'rule_generation_scene' || agentSenceCode === 'intelligent_conversation_scene'"
          class="button-last"
          :disabled="treeStatusLast === 1 || treeStatusLast === 0 || gridData.length ===0 || loading" type="primary"
          @click="changeViews(3, 'next')">下一步
        </el-button>
      </div>
    </div>
    <treeProcess :is-visible="processVisable" :tree-process-val="codeProcess" @close="closeSikaoRizhi" />
    <editSql :is-visible="showSqlModalVisable" :tree-status="sqlStatusVal" :sql-data="sqlData" @close="closeSqlEdit"
             @updateSQL="emitSQL" />
    <chooseTable
      v-if="agentSenceCode !=='device_ops_assistant_scene' && agentSenceCode !=='visit_leader_cognition_scene' && agentSenceCode !== 'intelligent_conversation_scene' && agentSenceCode !== 'rule_generation_scene'"
      :is-visible="showChooseTableVisable" :agent-sence-code="agentSenceCode" :edit-data="initChooseData"
      @close="closeChooseTableEdit" />
    <chooseTableNew
      v-if="agentSenceCode ==='device_ops_assistant_scene' || agentSenceCode ==='visit_leader_cognition_scene' || agentSenceCode === 'rule_generation_scene' || agentSenceCode === 'intelligent_conversation_scene'"
      :is-visible="showChooseTableVisable" :agent-sence-code="agentSenceCode" :template-id="templateId"
      :edit-data="initChooseData" :tree-data="treeData" :detail-content="detailContent" @close="closeChooseTableEdit" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  CodeEdit,
  GetDecision,
  AbilityPublish,
  AbilityTest,
  queryAbilityMapping,
  getTablesList,
  queryFieldsByTable,
  updateAbilityMapping,
  SchemeDetail,
  queryNengliList,
  addCache,
  removeCache,
  queryCache,
  queryDuiqiApiName,
  queryTempIfFromScene,
  querySchemeDetailById, queryApiMixFuncList
} from '@/api/planGenerateApi.js';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import treeProcess from './treeProcess.vue';
import nengliTestReult from './nengliTest.vue';
import chooseTable from './chooseTable.vue';
import chooseTableNew from './chooseTableNew.vue';
import editSql from './editSql.vue';
// import mermaid from 'mermaid'
import panzoom from 'panzoom';
import MyEditor from './mdEditorPreview.vue';

export default {
  components: {
    treeProcess,
    editSql,
    chooseTable,
    chooseTableNew,
    MyEditor
  },
  props: {
    agentSenceCode: {
      type: String,
      default: ''
    },
    agentScene: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    sqlStatus: {
      type: Number,
      default: -1
    },
    sqlData: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailContent: '',
      showType: 1,
      loading: false,
      saveLoading: false,
      showSqlModalVisable: false, // 生成sql弹窗
      showChooseTableVisable: false, // 选择数据表定义窗口弹窗
      isSuperAdmin: false, // 是否超级管理员
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      hisDetail: '',
      processContent: { text: '' },
      processList: [],
      taskList: [],
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      currentText: '',
      socket: null,
      systemMessages: '',
      taskStatusText: '',
      agentError: false,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskLoading: false,
      writeFlag: true,
      writeText: '',
      greets: '',
      taskGenType: '',
      sessionId: '',
      treeData: '',
      codeData: '',
      codeProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      hisCode: '', // 编辑能力代码生成代码时的数据
      testVisable: false, // 能力测试窗口
      testReq: '', // 能力测试请求参数
      testResult: '', // 能力测试结果
      treeStatusLast: 0,
      panZoomRef: null,
      gridData: [], // 多模态数据对齐表数据
      dataThs: [],
      dataThDatas: [], // 多模态数据对齐表头数据
      dataStatus: '', // 多模态数据对齐状态
      tablesList: [], // 表数据
      fieldsList: [], // 字段名
      sqlStatusVal: -1,
      tableHeight: '90%', // 表格高度
      samllHeight: '90%', // 表格高度
      changeloading: false,
      redoFlag: false, // 判断是否重新生成过/保存过
      sqlLastData: '',
      suanfaData: [],// 算法公式对齐数据
      biaoDatas: [],
      verifyBiaoDatas: [],// 校验比对
      verifySuanfaData: [],// 校验比对
      suanfaList: [], // 算法列表
      useAPIList: [],
      dataThs2: [],
      dataThDatas2: [], // 多模态数据对齐表头数据
      pageNo: 1,
      pageSize: 10,
      pageNo2: 1,
      pageSize2: 10,
      timer: null,
      isCache: false,
      isCacheDisabled: true,
      initChooseData: { 'table': [], 'tag_ids': [] },
      configList: (this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'intelligent_conversation_scene') ? [
        {
          name: 'API/函数',
          value: 'API'
        }
      ] : [
        {
          name: 'API/函数',
          value: 'API'
        },
        {
          name: '库表',
          value: '库表'
        }
      ],
      apiNameMap: {},
      templateId: ''
    };
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    }),
    displayData() {
      const startIndex = (this.pageNo - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.gridData.slice(startIndex, endIndex);
    },
    displaySuanfaData() {
      const startIndex = (this.pageNo2 - 1) * this.pageSize2;
      const endIndex = startIndex + this.pageSize2;
      return this.suanfaData.slice(startIndex, endIndex);
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        this.treeStatusLast = val;
        if (val === 2) {
          this.taskLoading = false;
          this.handleAbilityMapping();
        } else if (val === 0) {
          this.taskLoading = false;
        } else if (val === 3) {
          this.taskLoading = false;
        }

      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.codeData = val;
      },
      immediate: true
    },
    sqlStatus: {
      handler(val) {
        this.sqlStatusVal = val;
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.codeProcess = val;
      },
      immediate: true
    }
  },
  async created() {

  },
  beforeDestroy() {
    clearTimeout(this.timer);
    this.timer = null;
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    console.log('宽度', this.rightWidth);
    this.taskStatus = this.$route.query.status;
    const nodeMarkmap = document.getElementById('markmap');
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = '';
    }
    if (this.agentSenceCode === 'device_ops_assistant_scene' || this.agentSenceCode === 'device_ops_assistant_scene-v1' || this.agentSenceCode === 'artificial_handle_scene' || this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'rule_generation_scene' || this.agentSenceCode === 'intelligent_conversation_scene') {
      await this.queryDecision('decision_tree'); // 思维树
    }
    console.log('tabelRef', this.$refs.tabelRef, this.$refs.tabelRef.scrollHeight);
    this.tableHeight = this.$refs.tabelRef.scrollHeight - 40;
    this.smallHeigth = this.$refs.tabelRef.scrollHeight - 40;
    await this.apiAllName();
    await this.handleAbilityMapping(); // 多模态数据对齐
    await this.queryTemplate(); // 查询数据对齐分析模版ID
    await this.queryPlanDetail();
  },
  methods: {
    async queryTemplate() {
      queryTempIfFromScene({ scheme_id: this.$route.query.id }).then(res => {
        console.log('获取模版ID接口-', res.data);
        this.templateId = res.data?.id || '';
      });
    },
    async apiAllName() {
      queryDuiqiApiName().then(res => {
        console.log('map', res.data);
        try {
          const apiNames = eval(res.data);
          // console.log('名称映射',apiNames);
          const temp = {};
          apiNames.forEach(item => {
            temp[item.code] = item.name;
          });
          this.apiNameMap = temp;
          console.log('名称映射', this.apiNameMap);
        } catch (error) {

        }
      });
    },
    async queryCacheHandle() {
      queryCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          if (res.data.result) {
            this.isCache = res.data.result.isCache;
            this.isCacheDisabled = false;
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showSikao();
      } else if (command === 'addCache') {
        addCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '新增成功'
            });
            this.isCache = !this.isCache;

          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });

      } else if (command === 'removeCache') {
        removeCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '删除成功'
            });
            this.isCache = !this.isCache;
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      }
    },
    changeChart(type) {
      this.showType = type;
    },
    isLastRow(rowIndex) {
      const lastindex = (this.pageNo - 1) * this.pageSize + rowIndex;
      // console.log(lastindex,'最后一行')
      // const lastRowIndex = (this.pageNo - 2) * this.pageSize + this.displayData.length - 1;
      return lastindex === this.gridData.length - 1;
    },
    isLastRow2(rowIndex) {
      const lastindex = (this.pageNo2 - 1) * this.pageSize2 + rowIndex;
      // const lastRowIndex = (this.pageNo - 2) * this.pageSize + this.displayData.length - 1;
      return lastindex === this.suanfaData.length - 1;
    },
    handleCurrentChange(val) {
      this.pageNo = val;
    },
    handleCurrentChange2(val) {
      this.pageNo2 = val;
    },
    closeChooseTableEdit(res, tableName, tags, manual_input) {
      console.log('关闭数据表定义', res, '---');
      this.showChooseTableVisable = false;
      if (res !== undefined && res !== '') {
        this.codeData = '';
        this.codeProcess = '';
        this.taskLoading = true;
        this.redoFlag = true;
        if (res === 'close') {
          this.$emit('updateCodeGenerate', {
            table_schema: '',
            align_type: { 'table': tableName, 'tag_ids': tags.length ? tags : null, manual_input }
          });
        } else {
          this.$emit('updateCodeGenerate', {
            table_schema: res,
            align_type: { 'table': tableName, 'tag_ids': tags.length ? tags : null, manual_input }
          });
        }
      }
    },
    emitSQL() {
      this.redoFlag = false;
      this.$emit('updateSQLSteam');
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg');
      const arr = [...svgdoms];
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          });
        }
      });
    },
    // 显示生成sql窗口
    showSqlModal() {
      this.sqlStatusVal = -1;
      this.showSqlModalVisable = true;
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示', this.codeProcess);
      this.processVisable = true;
    },
    closeSikaoRizhi() {
      this.processVisable = false;
    },
    // 部署方法
    async bushuFun() {
      console.log('部署');
      if (this.codeData !== '') {
        // 调用接口
        const res = await AbilityPublish({
          code_str: this.codeData,
          scheme_id: this.$route.query.id
        });
        if (res?.data?.code !== 200) {
          this.$message.error(res?.data?.msg || '部署失败');
          return;
        }
        this.$message.success('能力代码生成部署成功');
      }

    },
    // 测试方法
    async testFun() {
      if (this.codeData !== '') {
        this.testVisable = true;
        // 调用接口
        const res = await AbilityTest({
          code_str: this.codeData,
          scheme_id: this.$route.query.id
        });
        if (res?.data?.code !== 200) {
          this.$message.error(res?.data?.msg || '测试失败');
        } else {
          this.testResult = res.data.result?.resp || '';
        }
      }
    },
    // 关闭保存
    handleCodeSaveClose() {
      this.codeData = this.hisCode;
      this.isEdit = false;
    },
    // 编辑能力代码生成保存
    async handleCodeSave() {
      this.isEdit = false;
      // 调用接口
      const res = await CodeEdit({
        text: this.codeData,
        scheme_status: 'decision_ability',
        scheme_id: this.$route.query.id
      });
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败');
        return;
      }
      this.$message.success('编辑成功');
      // 编辑成功后重新查询一次最新能力代码生成代码
      await this.queryDecision('decision_ability'); // 能力代码生成
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag;
      this.$nextTick(() => {
        mermaid.run({
          querySelector: '.language-mermaid'
        });
      });
    },
    thinkingHandle() {
      if (this.treeData) {
        if (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1) {

        } else {
          const transformer = new Transformer();
          const { root } = transformer.transform(this.treeData);
          this.$nextTick(() => {
            Markmap.create('#markmap', null, root);
          });
        }
      }
    },
    validToComparison() {
      // console.log(JSON.stringify(this.gridData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log('verify',JSON.stringify(this.verifyBiaoDatas.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.gridData.map(({fieldsList,isAdd,...rest}) =>rest)) ===  JSON.stringify(this.verifyBiaoDatas.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.suanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log('verify',JSON.stringify(this.verifySuanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.suanfaData.map(({fieldsList,isAdd,...rest}) =>rest)) ===  JSON.stringify(this.verifySuanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      return JSON.stringify(this.gridData.map(({
                                                 fieldsList,
                                                 isAdd,
                                                 ...rest
                                               }) => rest)) === JSON.stringify(this.verifyBiaoDatas.map(({
                                                                                                           fieldsList,
                                                                                                           isAdd,
                                                                                                           ...rest
                                                                                                         }) => rest)) &&
        JSON.stringify(this.suanfaData.map(({
                                              fieldsList,
                                              isAdd,
                                              ...rest
                                            }) => rest)) === JSON.stringify(this.verifySuanfaData.map(({
                                                                                                         fieldsList,
                                                                                                         isAdd,
                                                                                                         ...rest
                                                                                                       }) => rest));
    },

    changeViews(val, type) {
      // if (this.redoFlag && type === 'next') {
      if (type === 'next') {
        this.handleDone();
        // if(!this.validToComparison()){
        //   this.$message.warning('多模态数据对齐数据不一致，请手动保存后再进行下一步！');
        // }else{
        //   this.$emit('updateStep',val)
        // }
      } else {
        this.$emit('updateStep', val);
      }
      this.dataDuiqiZhuGe(type === 'next' ? '下一步' : '上一步');
    },
    regenerate() {
      this.showChooseTableVisable = true;
      // this.codeData = ''
      // this.codeProcess = '';
      // this.taskLoading = true;
      // this.redoFlag = true;
      // this.$emit('updateCodeGenerate','decision_ability')
      this.dataDuiqiZhuGe('多模态数据对齐');
    },
    regenerateRengong() {
      this.codeData = '';
      this.codeProcess = '';
      this.taskLoading = true;
      this.redoFlag = true;
      this.$emit('updateCodeGenerate', { table_schema: '' });
      this.dataDuiqiZhuGe('多模态数据对齐');
    },
    dataDuiqiZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then(res => {
        const name = res.data.result.name;
      });
    },
    async closeSqlEdit() {
      this.showSqlModalVisable = false;
      this.loading = false;
      await queryAbilityMapping({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.sqlLastData = res.data.result?.sql;
            this.loading = false;
          } else {
            this.loading = false;
          }
        })
        .catch((_err) => {
          this.loading = false;
        })
        .finally(() => {
        });
    },
    handleAbilityMapping() {
      this.saveLoading = true;
      this.loading = true;
      queryAbilityMapping({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.sqlLastData = res.data.result?.sql;
            this.codeProcess = res.data.result.sub_content;
            this.dataStatus = res.data.result.ability_status;
            this.initChooseData = res.data.result?.align_type || { 'table': [], 'tag_ids': [], manual_input: false };
            if (res.data.result?.align_type) {
              const temp = [];
              console.log('tables类型', typeof res.data.result?.align_type?.table);
              if (res.data.result?.align_type?.table) {
                if (typeof res.data.result?.align_type?.table === 'string') {
                  temp.push({ name: '库表', value: '库表' });
                } else {
                  if (res.data.result?.align_type?.table.length) {
                    temp.push({ name: '库表', value: '库表' });
                  }
                }
              }
              if (res.data.result?.align_type?.tag_ids && res.data.result?.align_type?.tag_ids?.length) {
                temp.push({ name: 'API/函数', value: 'API' });
              }
              if (res.data.result?.align_type?.manual_input) {
                temp.push({ name: '人工', value: '人工' });
              }
              this.configList = temp;
            } else {
              if (this.agentSenceCode === 'device_ops_assistant_scene') {
                this.configList = [
                  { name: 'API/函数', value: 'API' },
                  { name: '库表', value: '库表' },
                  { name: '人工', value: '人工' }
                ];
              } else {
                this.configList = [
                  { name: 'API/函数', value: 'API' },
                  { name: '人工', value: '人工' }
                ];
              }

            }
            console.log('多模态数据对齐结果', this.initChooseData);
            const status = res.data.result.ability_status;
            const configData = res.data.result?.config || {};
            this.queryCacheHandle();
            if (status !== 'generating') {
              this.saveLoading = false;
              clearTimeout(this.timer);
              this.timer = null;
              console.log(configData, '111');
              this.dataThs = configData?.header || {};
              this.dataThDatas = Object.keys(configData?.header || {}).map(item => {
                return { name: configData?.header[item], field: item };
              });

              // this.dataThs2 = configData.content?.algorithm_map?.header || {};
              // this.dataThDatas2 = Object.keys(configData.content?.algorithm_map?.header||{}).map(item => {return {name: configData.content?.algorithm_map?.header[item], field: item}})
              // console.log('ziduan', this.dataThDatas2);
              const temp = configData?.data?.map(item => {
                return {
                  ...item
                };
              }) || [];
              // const temp2 = configData.content?.algorithm_map?.data || [];
              this.biaoDatas = temp;
              this.verifyBiaoDatas = [...temp];
              // this.suanfaData = temp2;
              // this.verifySuanfaData = [...temp2];
              if (this.agentSenceCode === 'artificial_handle_scene') {
                this.gridData = temp;
                this.loading = false;
              } else {
                const resultF = [];
                temp.forEach(async (item) => {
                  if (item.b_type !== 'API' && item.c_source) {
                    const promise1 = queryFieldsByTable({ tableName: item.c_source, dbType: 'static' });
                    console.log('hhhh', promise1);
                    resultF.push(promise1);
                  } else {
                    if (item.b_type === 'API') {
                      const promise1 = queryApiMixFuncList({
                        page: 1,
                        page_size: 200,
                        code_status: null,
                        code_type: item.c_source,
                        keyword: '',
                        tag_ids: res.data.result?.align_type?.tag_ids || []
                      });
                      console.log('hhhh', promise1);
                      resultF.push(promise1);
                    } else {
                      resultF.push('');
                    }
                  }
                });
                // cosnole.log('resultF', resultF);
                Promise.all(resultF).then((results) => {
                  console.log('results==', results);
                  const rtemp = [];
                  results.forEach((resList, rindex) => {
                    if (resList) {
                      if (resList?.config?.url === '/api/code_module/list') {
                        temp[rindex].fieldsList = resList.data;
                        temp[rindex].isAdd = false;
                        rtemp.push(resList.data || []);
                      } else {
                        temp[rindex].fieldsList = resList.data?.data?.list;
                        temp[rindex].isAdd = false;
                        rtemp.push(resList.data?.data?.list || []);
                      }
                    } else {
                      rtemp.push([]);
                    }
                  });
                  console.log('结果rtemp', rtemp);
                  this.fieldsList = rtemp;
                  console.log('fieldsList结果rtemp', this.fieldsList);
                  this.gridData = temp;
                  console.log('表格数据', this.gridData);
                  this.loading = false;
                  // 对 userData 和 productData 进行数据处理
                });
                this.$nextTick(() => {
                  if (this.agentSenceCode === 'device_ops_assistant_scene' || this.agentSenceCode === 'device_ops_assistant_scene-v1' || this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'intelligent_conversation_scene' || this.agentSenceCode === 'rule_generation_scene') {
                    this.handleAllTables();
                    this.handleAllSuanfa(res.data.result?.align_type?.tag_ids || []);
                  }
                });
              }

              this.dialogTableVisible = true;
            } else {
              clearTimeout(this.timer);
              this.timer = null;
              this.timer = setTimeout(() => {
                this.handleAbilityMapping();
              }, 3000);
            }


          } else {
            this.loading = false;
            this.dataStatus = '';
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .catch((_err) => {
          this.loading = false;
          this.dataStatus = '';
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
        .finally(() => {
        });
    },
    handleDone() {
      console.log(this.gridData, '222');
      const tempGridData = JSON.parse(JSON.stringify(this.gridData));
      // const tempGridData2 = JSON.parse(JSON.stringify(this.suanfaData));
      const tempData = tempGridData.map(item => {
        const tempItem = item;
        delete tempItem.fieldsList;
        delete tempItem.isAdd;
        return tempItem;
      });
      // const tempData2 = tempGridData2.map(item => {
      //   const tempItem = item;
      //   delete tempItem.isAdd;
      //   return tempItem;
      // })
      const fields = tempData.reduce((acc, cur) => {
        Object.keys(cur).forEach((key) => {
          if (!acc.includes(key)) {
            acc.push(key);
          }
        });
        return acc;
      }, []);

      // const fields2 = tempData2.reduce((acc, cur) => {
      //   Object.keys(cur).forEach((key) => {
      //     if (!acc.includes(key)) {
      //       acc.push(key);
      //     }
      //   });
      //   return acc;
      // }, []);

      const isValid = tempData.every((item) => {
        return fields.every((field) => {
          return item[field] !== '';
        });
      });
      // const isValid2 = tempData2.every((item) => {
      //   return fields2.every((field) => {
      //     return item[field] !== '';
      //   });
      // });
      console.log('tempData====', isValid, fields, tempData);
      const showData = this.gridData.map(item => {
        return {
          ...item,
          isAdd: false
        };
      });
      // const showData2 = this.suanfaData.map(item => {
      //   return {
      //     ...item,
      //     isAdd: false,
      //   }
      // })
      if (!isValid) {
        this.$message.warning('请检查字段信息是否填写完整');
      } else {
        updateAbilityMapping({
          scheme_id: this.$route.query.id,
          config: {
            business_type: this.agentSenceCode === 'artificial_handle_scene' ? 'paramData' : 'dataTableAlgMapV1',
            header: this.dataThs,
            data: tempData
          },
          ability_status: 'finished'
        }).then((res) => {
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            // this.$message({
            //   type: 'success',
            //   message: '更新完成!'
            // });
            this.redoFlag = true;
            this.gridData = showData;
            this.verifyBiaoDatas = [...showData];
            // this.suanfaData = showData2;
            // this.verifySuanfaData = [...showData2];
            this.saveLoading = false;
            this.$emit('updateStep', 3);
          } else {
            this.$message({
              type: 'success',
              message: '保存失败!'
            });
            this.saveLoading = false;
          }
        });
      }
    },
    async queryDecision(status) {
      await GetDecision({ scheme_id: this.$route.query.id, scheme_status: status }).then((res) => {
        this.treeStatusLast = 2;
        if (res.status === 200 && res.data.code === 200) {
          if (status === 'decision_ability') {
            this.codeData = res.data.result?.decision_making_content || '';
            this.codeProcess = res.data.result?.sub_content || '';
          } else {
            this.treeData = res.data.result?.decision_making_content || '';
          }
          if (status === 'mind_map' && this.treeData) { // 思维图
            this.thinkingHandle();
          } else if (status === 'mind_map' && !this.treeData) {
            const nodeMarkmap = document.getElementById('markmap');
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = '';
            }
          }

        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    // 数据表
    async handleAllTables() {
      getTablesList({ dbType: 'static' }).then((res) => {
        if (res.status === 200) {
          const temp = res.data.data?.list || [];
          console.log('多模态数据对齐配置的表', this.initChooseData);
          if (this.agentSenceCode === 'device_ops_assistant_scene' || this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'intelligent_conversation_scene') {
            if (this.initChooseData?.table) {
              this.tablesList = temp.filter(item => this.initChooseData?.table.indexOf(item.table) > -1);
            } else {
              this.tablesList = [];
            }
          } else {
            this.tablesList = temp;
          }
          console.log('表数据', this.tablesList);
        } else {
          this.$message({
            type: 'success',
            message: '接口错误!'
          });
        }
      });
    },
    // 数据表
    async handleAllSuanfa(tags) {
      queryApiMixFuncList({
        tag_ids: tags
      }).then((res) => {
        console.log('算法', res.data);
        if (res.data) {
          const abTemp = {};
          const temp = res.data?.map(item => {
            abTemp[item.code_type] = this.apiNameMap[item.code_type] || '';
            return {
              code_name: item.code_name,
              code_type: item.code_type,
              code_func_name: item.code_func_name,
              code_type_name: this.apiNameMap[item.code_type] || ''
            };
          }) || [];
          this.useAPIList = Object.keys(abTemp).map(item => {
            return { name: item, value: abTemp[item] };
          });
          this.suanfaList = temp;
          console.log('算法列表数据', this.suanfaList, this.useAPIList);
        } else {
          this.$message({
            type: 'success',
            message: '接口错误!'
          });
        }
      });
    },
    async changeAlg(name, index) {
      const tempData = this.gridData.map(item => {
        return item;
      });
      const rowIndex = (this.pageNo - 1) * this.pageSize + index;
      const filters = this.suanfaList.filter(item => item.code_func_name === name);
      if (filters.length) {
        // console.log(JSON.parse(filters[0].algorithm_schema));
        tempData[rowIndex].e_mapping_desc = filters[0].public_description;
        // tempData[rowIndex].d_algorithm_url = filters[0].algorithm_url
        this.gridData = tempData;
      }

    },
    // 切换匹配类型，库表还是api
    changeCType(val, index) {
      console.log('匹配切换类型', val, index);
      const tempData = this.gridData.map(item => {
        return item;
      });
      const rowIndex = (this.pageNo - 1) * this.pageSize + index;
      this.fieldsList[rowIndex] = [];
      console.log('fieldsList', this.fieldsList);
      tempData[rowIndex].fieldsList = [];
      tempData[rowIndex].c_source = '';
      tempData[rowIndex].d_mapping_field = '';
      tempData[rowIndex].e_mapping_desc = '';
      this.gridData = tempData;
      console.log('表数据', this.gridData);
    },
    // 表中对应的列
    async handleFiedsByTable(tableName, index) {
      // this.changeloading = true;
      const tempData = this.gridData.map(item => {
        return item;
      });
      const rowIndex = (this.pageNo - 1) * this.pageSize + index;
      await queryFieldsByTable({ tableName, dbType: 'static' }).then((res) => {
        if (res.status === 200 && res.data.success) {
          console.log('列数据', res.data.data);
          const temp = res.data.data?.list || [];
          this.fieldsList[rowIndex] = temp;
          console.log('fieldsList', this.fieldsList);
          tempData[rowIndex].fieldsList = temp;
          tempData[rowIndex].d_mapping_field = temp[0].COLUMN_NAME;
          const filter = this.fieldsList[rowIndex].filter(item => item.COLUMN_NAME === temp[0].COLUMN_NAME);
          tempData[rowIndex].e_mapping_desc = filter[0].COLUMN_COMMENT;
          // tempData[rowIndex].f_field_type = filter[0].COLUMN_TYPE
          this.gridData = tempData;
          this.changeloading = false;
        } else {
          this.changeloading = false;
          this.$message({
            type: 'success',
            message: '接口错误!'
          });
        }
      });

    },
    // api中对应的列
    async handleFiedsByAPI(name, index) {
      // this.changeloading = true;
      const tempData = this.gridData.map(item => {
        return item;
      });
      const rowIndex = (this.pageNo - 1) * this.pageSize + index;
      await queryApiMixFuncList({
        code_type: name,
        tag_ids: this.initChooseData.tag_ids || []
      }).then((res) => {
        if (res.status === 200 && res.data) {
          const temp = res.data || [];
          this.fieldsList[rowIndex] = temp;
          console.log('fieldsList', this.fieldsList);
          tempData[rowIndex].fieldsList = temp;
          // tempData[rowIndex].d_mapping_field = temp[0].code_name
          // const filter =  this.fieldsList[rowIndex].filter(item => item.code_fun_name === temp[0].code_fun_name);
          // tempData[rowIndex].e_mapping_desc = filter[0].public_description
          // tempData[rowIndex].f_field_type = filter[0].COLUMN_TYPE
          if (temp.length) {
            tempData[rowIndex].d_mapping_field = temp[0].code_func_name;
            const filter = this.fieldsList[rowIndex].filter(item => item.code_name === temp[0].code_name);
            tempData[rowIndex].e_mapping_desc = filter[0].public_description;
          } else {
            tempData[rowIndex].d_mapping_field = '';
            tempData[rowIndex].e_mapping_desc = '';
          }
          this.gridData = tempData;
          this.changeloading = false;
        } else {
          this.changeloading = false;
        }
      });

    },
    handleFiledApiChange(val, index) {
      const temp = this.fieldsList[index];
      const filter = temp.filter(item => item.code_func_name === val);
      console.log('更改描述信息', filter, index);
      if (filter.length) {
        const temp = this.gridData.map((item, gindex) => {
          if (gindex === index) {
            return { ...item, e_mapping_desc: filter[0].public_description };
          } else {
            return item;
          }
        });
        console.log('temp--', temp);
        this.gridData = temp;
      }
    },
    // 切换字段关联描述信息
    handleFiledChange(val, index) {
      const temp = this.fieldsList[index];
      const filter = temp.filter(item => item.COLUMN_NAME === val);
      console.log('更改描述信息', filter, index);
      if (filter.length) {
        const temp = this.gridData.map((item, gindex) => {
          if (gindex === index) {
            // f_field_type: filter[0].COLUMN_TYPE
            return { ...item, e_mapping_desc: filter[0].COLUMN_COMMENT };
          } else {
            return item;
          }
        });
        console.log('temp--', temp);
        this.gridData = temp;
      }
    },
    // 查看API详情
    handleLink(row, list) {
      console.log('跳转接口详情', row, list);
      const filter = row.fieldsList.filter(item => item.code_func_name === row.d_mapping_field || item.code_name === row.d_mapping_field);
      if (filter.length) {
        if (filter[0].code_type == 'llm') {
          window.open(`${process.env.VUE_APP_AGENT_URL}/apiDocumentDetails/${filter[0].code_type}?id=${filter[0].sub_id}`, '_blank');
        }else if(filter[0].code_type==='func_doc'){
          window.open(`${process.env.VUE_APP_AGENT_URL}/funDetails?id=${filter[0].sub_id}`, '_blank');
        } else {
          window.open(`${process.env.VUE_APP_AGENT_URL}/apiDocumentDetails?id=${filter[0].sub_id}`, '_blank');
        }
      }
      //
      // https://agent.fat.ennew.com/apiDocumentDetails?id=1e1b802e-c4d9-4f0c-a911-20aed89d3c2c
    },
    // 删除行
    handleRemove(rowIndex) {
      rowIndex = (this.pageNo - 1) * this.pageSize + rowIndex;
      const temp = [...this.gridData];
      temp.splice(rowIndex, 1);
      const res = this.gridData.splice(rowIndex, 1);
      console.log('显示页面条数', (this.pageNo - 1) * this.pageSize, this.pageNo, temp.length);
      const res2 = this.fieldsList.splice(rowIndex, 1);
      console.log('删除行', res, this.gridData, res2, this.fieldsList);
      if ((this.pageNo - 1) * this.pageSize === temp.length) {
        this.pageNo = this.pageNo - 1;
      }
    },
    // 增加行
    handleAddRow(rowIndex) {
      rowIndex = (this.pageNo - 1) * this.pageSize + rowIndex;
      console.log('行信息', rowIndex, (rowIndex) % 10);
      // TODO
      const addData = { isAdd: true };
      if (this.agentSenceCode === 'artificial_handle_scene') {
        Object.keys(this.dataThs).forEach(item => {
          if (item === 'b_type') {
            addData[item] = '人工';
          } else if (item === 'c_source') {
            addData[item] = '人工输入';
          } else {
            addData[item] = '';
          }
        });
        this.displayData.splice(rowIndex, 0, addData);
        this.gridData.splice(rowIndex, 0, addData);
        this.fieldsList.splice(rowIndex, 0, []);
        console.log('增加行', this.gridData);
      } else {
        Object.keys(this.dataThs).forEach(item => {
          addData[item] = '';
        });
        this.fieldsList.splice(rowIndex, 0, []);
        this.displayData.splice(rowIndex, 0, addData);
        this.gridData.splice(rowIndex, 0, addData);
        console.log('增加行', this.gridData, this.fieldsList);
      }
      if (rowIndex !== 0 && (rowIndex) % 10 === 0) {
        this.pageNo = this.pageNo + 1;
      }
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true;
        this.startX = event.clientX;
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
        this.startWidth = leftWidth;
        document.addEventListener('mousemove', this.onDrag);
        document.addEventListener('mouseup', this.stopDrag);
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX;
        const widthLeft = this.startWidth + deltaX;
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px';
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    getWsID() {
      let workspaceId = '';
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId;
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId;
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?');
          const params = Object.fromEntries(new URLSearchParams(query));
          workspaceId = params.workspaceId;
        } catch (error) {
          console.log('error', error);
        }
      }
      return workspaceId;
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10;
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow;
      if (this.planDetailShow) {
        this.rightWidth = '';
        this.leftWidth = '50%';
        this.$nextTick(() => {
          console.log('退出高度---tabelRef', this.samllHeight);
          this.tableHeight = this.samllHeight;
        });
      } else {
        this.rightWidth = '';
        this.leftWidth = '0px';
        this.$nextTick(() => {
          console.log('高度---tabelRef', this.$refs.tabelRef, this.$refs.tabelRef.scrollHeight);
          this.tableHeight = this.$refs.tabelRef.scrollHeight - 40;
        });
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag;
      if (this.rightFullFlag) {
        this.leftWidth = '100%';
        this.rightWidth = '0';
      } else {
        this.leftWidth = '50%';
        this.rightWidth = '100%';
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag;
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result?.text;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}

:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 40px;
}

:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}

.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;

    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }

    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }

    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }

    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }

    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }

    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;

      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }
}

.chatContainer {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;

  .headerBox {
    background-color: #fff;

    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #EBECF0;

      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }
    }
  }

  .containerBox2 {
    display: flex;
    flex-direction: row;
    height: calc(100%);
    max-height: calc(100%);
    overflow-y: hidden;
    position: relative;

    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068D4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;;
      z-index: 2;
      color: #fff;
      cursor: pointer;

      &:hover {
        background: #3455ad;
      }

      &:active {
        background: #264480;
      }

      img {
        width: 12px;
        height: auto;
      }
    }

    .containerCard {
      //height: calc(100% - 18px);
      // max-height: calc(100vh - 210px);
      overflow-y: hidden;
      overflow-x: hidden;
      margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: #fff;
      margin-left: 16px;

      &.containerCardFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        max-height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;

        .optScroll {
          height: calc(100vh - 170px) !important;
          max-height: calc(100vh - 170px) !important;
        }

        .optContentBox {
          width: 100%;
          min-height: calc(100vh - 230px) !important;
        }
      }

      .optContentBox {
        width: 100%;
        min-height: calc(100vh - 330px);
        overflow-y: auto;
        display: flex;
      }

      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #EBECF0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406BD4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068D4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnBlue {
              background-color: #406BD4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .optScroll {
        position: relative;
        height: calc(100vh - 330px);
        max-height: calc(100vh - 330px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }

      }

      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }

      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }

      .chatHeader {
        font-size: 14px;
        color: #323233;
        line-height: 24px;
        font-weight: bold;
        background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406BD4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068D4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnBlue {
              background-color: #406BD4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .thinkContent {
        margin-left: 16px;
        width: calc(100% - 32px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        max-height: 225px;
        height: 225px;
        overflow-y: auto;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #DCDDE0;
        transition: height 0.1s;

        &.thinkContentFull {
          position: absolute !important;
          left: 0px;
          width: calc(100vw - 214px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 2;
          max-height: calc(100vh - 150px) !important;
          top: 0px;
        }

        .thinkHeader {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 12px 12px;

          .title {
            color: #323233;
            line-height: 20px;
            display: flex;
            align-items: center;

            img {
              height: 24px;
              width: 24px;
              margin-right: 4px;
            }
          }

          .thinkOpt {
            display: flex;

            .think-btn {
              font-size: 14px;
              margin-left: 4px;
              cursor: pointer;
              width: 24px;
              height: 24px;
              text-align: center;
              line-height: 22px;
              font-weight: bold;

              &.think-btn-blue {
                background-color: #4068D4 !important;
                border-radius: 4px;

                &:hover {
                  background: #3455ad !important;
                }

                &:active {
                  background: #264480;
                }
              }

              &:hover {
                background-color: #ebecf0;
                border-radius: 4px;
              }

              img {
                width: 12px;
                height: 12px;
              }
            }
          }
        }

        .thinkWrap {
          background: #FFFFFF;
          padding: 0px 12px 12px 36px;
          max-height: calc(100% - 40px);
          overflow-y: auto;

          .thinkItem {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-start;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #DCDDE0;
            margin-top: 12px;

            &:first-child {
              margin-top: 0px;
            }
          }

          .itemContent {
            color: #646566;
            line-height: 22px;
            flex: 1;
            margin-left: 8px;
          }
        }
      }
    }

    .chatRight {
      flex: 1;
      background: #FFFFFF;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      height: calc(100% - 18px);
      max-height: calc(100% - 18px);
      overflow-y: hidden;
      margin-top: 16px;
      position: relative;

      &.chatRightFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;

        .optScroll {
          height: calc(100vh - 140px) !important;
          max-height: calc(100vh - 140px) !important;
        }

        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }

        .optContentBox {
          height: calc(100vh - 180px) !important;
        }
      }

      .optContentBox {
        height: calc(100vh - 330px);
        max-height: 100%;
        width: 100%;
        position: relative;
      }

      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #EBECF0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406BD4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068D4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnBlue {
              background-color: #406BD4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .optScroll {
        position: relative;
        height: calc(100vh - 300px);
        max-height: calc(100vh - 300px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        display: flex;
        flex-direction: column;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }

        .table-box {
          height: 100%;
          display: flex;
          flex-direction: column;

          .duiqi-box {
            height: calc(100% - 0px);
            overflow: hidden;
            // padding-bottom: 30px;
            .emptyData {
              height: 100%;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;

              img {
                width: 50%;
                max-width: 200px;
                height: auto;
              }
            }

            .title {
              margin-bottom: 16px;
              color: #323233;
            }

            .transition-box {
              table {
                height: 100%;
              }
            }

            .page {
              height: 30px;
              justify-content: flex-end;
              display: flex;
              margin-top: 10px;

              ::v-deep .el-pagination:not(.new-paper) button {
                width: 30px;;
              }
            }

            .el-pagination {
              height: 20%;
            }
          }
        }
      }

      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }

      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }

  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      background: #e0e6ff;

      .process-icon {
        color: #3455ad !important;
      }
    }

    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;

      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }

    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;

      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }

    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;

      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }

  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;

    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }

    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }

    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }

  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;

    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }

    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }

    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
}

.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

::v-deep .el-table::before {
  background-color: transparent;
}

::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}

::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}

.btns {
  display: flex;
  margin-bottom: 16px;

  .btns-item {
    display: inline-block;
    position: relative;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #406BD4;
    line-height: 22px;
    background: #f2f3f5;
    text-align: center;
    cursor: pointer;
    padding: 4px 16px;

    &:hover {
      background: #eaeaed;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 1px;
      height: 18px;
      background: #dcdde0;
    }

    &.one-btns {
      border-radius: 2px !important;
    }

    &:first-child {
      border-radius: 2px 0px 0px 2px;

      &::before {
        content: unset;
      }
    }

    &:last-child {
      border-radius: 0px 2px 2px 0px;
    }

    &.active {
      background: #4068d4;
      color: #ffffff;

      .view-icon {
        color: #ffffff;
      }

      &::before {
        content: unset;
      }
    }
  }
}

::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;

  img {
    height: 16px;
    margin-top: -2px;
  }
}
</style>
