<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="container">
    <div class="flex_bot" v-if="dataAll.length > 0">
      <div class="flex_body" v-for="(item, index) in dataAll" :key="index">
        <div class="flex_top">
          <div class="flex_top_one">
            <div class="flex_top_info">
              <div class="font_top">{{ item.name }}</div>
              <Status :status="item.task_status" style="margin-left:12px;" ></Status>
            </div>
            <div class="flex_top_body">
              <div class="flex_top_body_date">提交时间：{{ item.create_time }}</div>
            </div>
          </div>
          <Goal :taskDesc="item.description" :nickName="item.nickName" :username="item.username"></Goal>
          <!-- 能力需求人 -->
          <div class="flex_nl">
            <div class="flex_nl_ren">
            </div>
            <div class="flex_detail">
              <el-button type="primary" @click="add(item)">新增任务</el-button>
              <!-- <el-button type="primary" @click="goRouter(item)">查看详情(老的)</el-button> -->
              <el-button type="primary" @click="goRouter1(item)">查看详情</el-button>
              <el-button @click="showchildFun(index, item,!item?.showchild)">{{ !item.showchild ? '展开子任务' : '收起子任务' }}</el-button>
            </div>
          </div>
        </div>
        <!-- 专业模型 -->
        <div class="flex_bottom" v-show="item.showchild">
          <div v-if="item?.childData?.length > 0" v-for="(items, vt) in item.childData" :key="vt" class="flex_step">
            <Step :index="vt" :last="item.childData.length - 1 == vt ? true : false"></Step>
            <Mude :childData="items" @showchildFun="showchildFun(index,item,true)"></Mude>
          </div>
          <div style="text-align: right; padding: 16px; background: #F6F7FB;" v-if="item?.childData?.length">
              <el-pagination class="new-paper" layout="prev, pager, next, sizes, jumper" :page-sizes="[5, 10]"
                :current-page="item.pageData.page_num" :page-size="item.pageData.page_size" :total="item.pageData.total"
                @size-change="(e)=>{handleSizeChange(e,index,item)}" @current-change="(e)=>{handleCurrentChange(e,index,item)}">
              </el-pagination>
            </div>
          <el-empty description="暂无数据" v-if="!item?.childData?.length" ></el-empty>
        </div>
      </div>
    </div>
    <el-empty description="暂无数据" v-else></el-empty>
    <el-dialog custom-class="last-dialog" title="创建任务" :visible.sync="visible" :before-close="onClose" append-to-body
      width="40%">
      <el-form ref="form" label-position="right" label-width="99px" :model="formData" :rules="rules">
        <el-form-item label="任务名称:" prop="task_name">
          <el-input v-model="formData.task_name" placeholder="请输入" show-word-limit maxlength="30" style="width: 100%" />
        </el-form-item>
        <el-form-item label="任务描述:" prop="task_desc">
          <el-input v-model="formData.task_desc" type="textarea" :rows="2" placeholder="请输入" show-word-limit
            style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createSubmit">确定</el-button>
        <el-button type="info" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
    <!-- <editModal :edit-data="editData" :visible="createVisable" @close="handleCreate" /> -->
  </div>
</template>

<script>
import editModal from './editTask.vue';
import { TaskPageListNew,CreateTaskNew } from '@/api/planGenerateApi.js'
import { debounce } from '@/utils/util.js'
import Status from './components/statusNew.vue'
import Goal from './components/goalNew.vue'
import Mude from './components/mudeNew.vue'
import Step from './components/step.vue'
import service from '@/axios'
export default {
  components: {
    Status,
    Step,
    Goal,
    Mude,
    editModal
  },
  props:{
    dataProps:{
      type: Array
    }
  },
  data() {
    return {
      developTaskStatus: {
        un_validate: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '待验证' },
        todo: { bgColor: '#ebeffa', dotColor: '#4068d4', text: '研发中' },
        online: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已完成' }
      },
      List: [{
        status: '待验证'
      }, {
        status: '研发中'
      }, {
        status: '已完成'
      },
      ],
      dataAll: [],
      keyWord: '',
      createVisable: false,
      editData:{},
      formData: {
        task_name: '',
        task_desc: ''
      },
      scheme_id:'',
      visible:false,
      rules: {
        task_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        task_desc: [
          // 根据需要添加规则
        ],
      },
    }
  },
  methods: {
    onClose() {
      this.formData.task_name = '';
      this.formData.task_desc = '';
      this.visible = false
    },
    createSubmit() {
      this.$refs.form.validate(async (validate) => {
        if (validate) {
          this.loading = true;
          const param = {
            task_name: this.formData.task_name,
            task_desc: this.formData.task_desc || '',
            scheme_id: this.scheme_id
          };
          const request = CreateTaskNew;
          await request(param)
            .then(async (res) => {
              this.loading = false;
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  type: 'success',
                  message: `新建成功`
                });
                this.onClose();
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    handleCreate(){
      this.createVisable= false
    },
    add(item){
      item.task_id = item.id.toString()
      item.id = item.id.toString()
      this.scheme_id = item.id.toString()
      this.editData = item
      this.visible = true
    },
    handleDevelopTaskStatus(row) {
      if (row.agent_scene_code === 'other_assistant_scene') {
        return row.scheme_status === 1 ? 'online' : 'todo'
      } else {
        return row.is_publish
          ? 'online'
          : row.scheme_status === 6 ? 'un_validate' : 'todo'
      }
    },
    async changeStatus(item,status){
      const res = await service({
        method: 'post',
        url: process.env.VUE_APP_AI_CHAT + '/api/knowledgeBase/prepareTask/updateTaskStatus',
        headers: { 'appid': 'ai-chat-backend' },
        data: {
          taskType:1,
          taskId:item.id,
          status:status,
         },
      })
      if (res.data.success) {
        this.getData({
        "keyWord": this.keyWord
      });
      }
    },
    goRouter(row) {
      this.$emit('checkInto',row)
    },
    goRouter1(row) {
      this.$emit('checkInto1',row)
    },
    async showchildFun(index, item,showStatus) {
      if(!showStatus) {
        this.$set(this.dataAll, index, Object.assign({ ...this.dataAll[index] }, {
          showchild: false,
          childData: this.dataAll[index].childData
        }))

        return false
      }
      this.getData({
        scheme_id: item.id.toString()
      },index,showStatus)
    },
    async getData(params,index,showStatus) {
      const res = await TaskPageListNew(params)
      if (res.status == 200 && res.data.code == 200) {
        this.$set(this.dataAll, index, Object.assign({ ...this.dataAll[index] }, {
          showchild: showStatus,
          pageData:{
            total: res.data.result.total,
            page_size: this.dataAll[index]?.pageData?.page_size || 5,
            page_num: this.dataAll[index]?.pageData?.page_num || 1,
          },
          childData: res.data.result.map(item => {
          return {
            ...item,
            statusFlag: item.status !== '未完成'
          }
        }) || []
        }));
      }
    },
    handleSizeChange(e,index,item) {
      this.$set(this.dataAll, index, Object.assign({ ...this.dataAll[index] }, {
          pageData:{
            ...this.dataAll[index]?.pageData,
            page_num: 1,
            page_size: e,
          }
      }));
      this.getData({
        scheme_id: item.id.toString()
      },index,true);
    },
    handleCurrentChange(e,index,item) {
      this.$set(this.dataAll, index, Object.assign({ ...this.dataAll[index] }, {
          pageData:{
            ...this.dataAll[index]?.pageData,
            page_num: e,
          }
      }));
      this.getData({
        scheme_id: item.id.toString()
      },index,true);
    }
  },
  watch:{
    dataProps:{
      handler(valNew,oldNew){
        this.dataAll = valNew.map(it =>{
          return {
            ...it,
            pageData:{
              page_num:1,
              page_size:5,
              total:0
            }
          }
        })
      },
      immediate: true,
      deep:true
    }
  }
}
</script>

<style scoped lang="postcss">
.bg_title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 32px;
  color: #4068D4;
  line-height: 45px;
  text-align: left;
  font-style: normal;
  margin: 46px auto 10px 86px;
}

.bg_font {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #585A61;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  margin-left: 86px;
}

.flex_nl_font {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #646566;
  line-height: 20px;
  text-align: center;
  font-style: normal;

  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #323233;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}

.flex_nl {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  border-top: 1px solid #DCDDE0;
}

.flex_detail {
  flex: 2;
  display: flex;
  justify-content: end;
}

.flex_nl_ren {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 6;
}

.flex_top_body_div {
  width: 30px;
  height: 30px;
  background: #F2F3F5;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 24px;
}

.flex_top_body_icon {
  font-size: 16px;
}

.flex_top_body_date {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

.flex_top_info {
  display: flex;
  align-items: center;
  height: 39px;
}

.flex_top_body {
  display: flex;
  justify-content: center;
  align-items: center;
}

.font_top {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #323233;
  line-height: 27px;
  text-align: left;
  font-style: normal;
  margin-right: 12px;
}

.flex_top_one {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #DCDDE0;
}

.flex_body {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border-radius: 4px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.flex_top {
  display: flex;
  flex-direction: column;
  border: 1px solid #DCDDE0;
}

.flex_bottom {
  display: flex;
  flex-direction: column;
}

.container {
  display: flex;
  flex-direction: column;
  /* padding: 16px 20px; */
}

.flex_bot {
  /* padding: 20px; */
  background-color: #fff;
  /*height: calc(100vh - 380px);*/
  overflow: auto;
  flex: 1;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 54px;
  background-color: #ffffff;
}

.flex_ju {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
}

.flex_left {
  padding-left: 20px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #323233;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.bg {
  background: url('@/assets/images/bg.png');
  background-size: 100% 100%;
  height: 220px;
}

.flex_step {
  display: flex;
  background: #F6F7FB;
}

.ysu {
  background: #EBF9FF;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #318DB8;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.gongre {
  background: #FAE6E6;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #C84949;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
</style>
