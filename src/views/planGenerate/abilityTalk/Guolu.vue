<template>
    <div class="mainBox">
        <div class="infoBox">
            <div class="info-top">群组锅炉运行负荷情况</div>
            <div class="infoBox-item">
                <div>
                    <el-select v-model="station_name" placeholder="请选择" @change="handleStationChange">
                        <el-option v-for="item in stationList" :key="item.id" :label="item.station_name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <div class="item-content" v-loading="activeDateLoading" element-loading-spinner="el-icon-loading">
                        <!-- <span class="infoBox2-item-value">20241008 10:00:00</span> -->
                        <el-date-picker v-model="activeDate" type="datetime" placeholder="请选择"
                            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                            @change="handleActiveDateChange" />
                    </div>
                </div>
            </div>
        </div>
        <div class="infoBox2">
            <div class="infoBox2-item">
                <div class="infoBox2-item-title">当前单耗</div>
                <div class="item-content" v-loading="loading" element-loading-spinner="el-icon-loading">
                    <span class="infoBox2-item-value" :style="{ color: IsLCdata ? '#323232' : '#C8C9CC' }">{{
                        currentData.actual_value
                        }}</span>
                    <span class="infoBox2-item-unit">{{ currentData.actual_unit }}</span>
                </div>
            </div>
            <div class="infoBox2-item">
                <div class="infoBox2-item-title">单耗偏差</div>
                <div class="item-content" v-loading="loading" element-loading-spinner="el-icon-loading">
                    <span class="infoBox2-item-value" :style="{ color: IsLCdata ? '#323232' : '#C8C9CC' }">{{
                        currentData.query_group_boiler_energy_consumption_value
                        }}</span>
                    <span class="infoBox2-item-unit">{{ currentData.query_group_boiler_energy_consumption_unit }}</span>
                </div>
            </div>
            <!-- <div class="infoBox2-item">
                <div class="infoBox2-item-title">当前时间</div>
      
            </div> -->
        </div>
        <div class="infoBox3">
            <div v-show="IsLCdata" id="lineCharts1" style="width: 100%; height: 400px;"></div>
            <div v-if="!IsLCdata" style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
        ">
                <img src="@/assets/images/planGenerater/emptyData.png" style="width: 180px; height: auto" />
                <div style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
        ">
                    暂无数据
                </div>
            </div>
        </div>
        <div>
            <strategy ref="refStrategy" :boilerGroup="boilerGroup" :GuoluGroupInfos="GuoluGroupInfos"
                @changeRecommendation="handleChangeRecommendation" />
        </div>

    </div>
</template>

<script>
import {
    get_iot_station_list,
    api_query_running_analysis_data_list,
    AllGuoLuInfo,
    GuoLuInfo
} from '@/api/planGenerateApi.js'
import strategy from '@/views/planGenerate/abilityTalk/components/strategy.vue';
import dayjs from 'dayjs'

const chartBase = {
    predicted_value: {
        name: '预计单耗',
        color: '#4B7AFF'
    },
    actual_value: {
        name: '实际单耗',
        color: '#46CF9F'
    },
    query_group_boiler_energy_consumption: {
        name: '单耗偏差',
        color: '#FFC147'
    }
}
export default {
    name: 'guolupage',
    components: {
        strategy
    },
    props: {
        executeData: {
            type: Object,
            required: true
        },
    },
    data() {
        return {
            IsLCdata: false,
            GuoluGroupadvise: '',
            GuoluGroupInfos: [],
            loading: true,
            activeDateLoading: false,
            currentData: {
                actual_value: 0,
                actual_unit: '',
                query_group_boiler_energy_consumption_value: 0,
                query_group_boiler_energy_consumption_unit: '',
            },
            boilerGroup: {
                results: [{ verifyResult: true }],
                list: []
            },
            activeDate: '',
            lineCharts1: null,
            stationList: [],
            station_name: '',
            sel_deviceGroupId: '',
            // chartBase: {
            //     predicted_value: {
            //         name: '预计单耗',
            //         color: '#4B7AFF'
            //     },
            //     actual_value: {
            //         name: '实际单耗',
            //         color: '#46CF9F'
            //     },
            //     query_group_boiler_energy_consumption: {
            //         name: '单耗偏差',
            //         color: '#FFC147'
            //     }
            // },
            lineCharts1Ooption: {
                title: {
                    text: '单耗（天然气m²/ 蒸汽t）',
                    top: '52px',
                    left: 'left',
                    textStyle: {
                        fontSize: 12,
                        fontWeight: 400,
                        color: 'rgba(100, 101, 102, 1)'
                    },
                    // subtext: '单耗（天然气m²/蒸汽t）'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    // right: '1%',
                    icon: 'rect',
                    itemWidth: 8,
                    itemHeight: 8,
                    top: '16px',  // 距离顶部的百分比，或者可以用像素值
                    left: 'left',  // 距离左侧的百分比，或者可以用像素值
                    data: ['预计单耗', '实际单耗', '单耗偏差']
                },
                grid: {
                    top: '82px',
                    // bottom: '0',
                    left: 'left',
                    // right: '0.5%',
                    containLabel: true
                },
                // toolbox: {
                //     feature: {
                //         saveAsImage: {}
                //     }
                // },
                xAxis: {
                    // axisLabel: { interval: false },
                    // boundaryGap: false,
                    data: []
                    // data: ['00:00', '00:30', '01:00', '01:30', '02:00']
                },
                yAxis: {
                    min: null
                },
                series: [{
                    name: "预计单耗",
                    showSymbol: false,
                    data: [],
                    type: 'line',
                    // stack: 'x',
                    itemStyle: { color: chartBase['predicted_value'].color },
                }, {
                    name: "实际单耗",
                    showSymbol: false,
                    data: [],
                    type: 'line',
                    // stack: 'x',
                    itemStyle: { color: chartBase['actual_value'].color },
                }, {
                    name: "单耗偏差",
                    showSymbol: false,
                    data: [],
                    type: 'line',
                    // stack: 'x',
                    itemStyle: { color: chartBase['query_group_boiler_energy_consumption'].color },
                }]
            }
        }
    },
    watch: {
        // executeData: {
        //     deep: true,
        //     immediate: true,
        //     handler(newData) {
        //         if (newData && Object.keys(newData).length > 0) {
        //             console.log("executeData 更新了:", newData);
        //             this.useExecuteData();
        //         }
        //     }
        // }
    },
    async created() {
        await this.query_get_iot_station_list()
        await this.get_api_query_running_analysis_data_list()
    },
    async mounted() {
        this.$nextTick(() => {
            this.initChart()
            this.lineCharts1.on('click', async (params) => {
                if (this.$refs.refStrategy) {
                    this.$refs.refStrategy.loading = true
                }
                this.loading = true;
                console.log(params);
                this.activeDate = params.name;
                // currentActive.value = -1;
                // showRight2.value = false;
                await new Promise((resolve) => {
                    this.$emit('generalExeApi', {
                        device_group_id: this.sel_deviceGroupId,
                        detection_time: this.activeDate,
                        resolve  // 把 resolve 传递给父组件
                    });
                });
                await this.useExecuteData();

                // this.groupStatus({
                //     abilityId: '9891',
                //     requestBody: {
                //         device_group_id: this.sel_deviceGroupId,
                //         detection_time: params.name,
                //     }
                // });
            });
        })

        window.addEventListener('resize', this.chartsResize);
    },
    beforeDestroy() {
        // 组件销毁时移除监听
        window.removeEventListener('resize', this.chartsResize);
    },
    methods: {
        async handleActiveDateChange() {
            const endDate = this.activeDate;
            const startDate = dayjs(endDate).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
            await this.get_api_query_running_analysis_data_list(startDate, endDate);
      
        },
        async handleChangeRecommendation() {
            if (this.$refs.refStrategy) {
                this.$refs.refStrategy.loading = true
            }
            await new Promise((resolve) => {
                this.$emit('generalExeApi', {
                    device_group_id: this.sel_deviceGroupId,
                    detection_time: this.activeDate,
                    resolve  // 把 resolve 传递给父组件
                });
            });
            await this.useExecuteData();

            // this.groupStatus({
            //     // 9096
            //     abilityId: '9891',
            //     requestBody: {
            //         device_group_id: this.sel_deviceGroupId,
            //         detection_time: this.activeDate,
            //     }
            // });
        },
        singleStatus(params) {
            const boiler = this.boilerGroup.list[this.boilerGroup.list.length - 1];  // 获取数组最后一个元素
            boiler.loading = true;
            boiler.verifyResult = true;

            return GuoLuInfo(params).then(res => {
                if (res.status === 200 && res.data.code === 200) {
                    const response = res.data.result;
                    boiler.measurePoints = response.measurePoints;
                    // 更新 boiler 中的 measurePoints 数组
                    // this.$set(boiler, 'measurePoints', response.measurePoints);

                    boiler.results = response.results;
                    console.log("hahah boiler.results", boiler.results);

                    for (const result of response.results) {
                        if (!result.verifyResult) {
                            boiler.verifyResult = result.verifyResult;
                        }

                        for (const mp of response.measurePoints) {
                            if (mp.code === result.measurePoint) {
                                mp.verifyMessage = result.verifyMessage;
                                mp.verifyResult = result.verifyResult;
                                console.log("mp hahaha", mp);
                            } else {
                                mp.verifyResult = true;
                            }

                            if (mp.code === 'calculate_boiler_efficiency') {
                                boiler.calculate_boiler_efficiency = mp.value;
                            }
                        }
                    }
                    this.boilerGroup = { ...this.boilerGroup }
                } else {
                    this.$message.error(res.message || `接口数据错误: name: ${boiler?.name} , device_id : ${boiler?.device_id}`);
                }
            }).finally(() => {
                boiler.loading = false;
            });
        },

        async useExecuteData() {
            console.log("data yzw00000", this.executeData);
            try {
                // 直接使用 props 传入的 this.executeData 数据
                const data = this.executeData.resp;
                console.log("data yzw00001", data);

                if (data && data.result && data.result.measurePoints && data.result.measurePoints.length) {
                    // 重置数据
                    this.boilerGroup.list = [];
                    this.GuoluGroupInfos = [];

                    for (const mp of data.result.measurePoints) {
                        if (mp.code === 'actual_value') {
                            this.currentData.actual_value = +(mp.value).toFixed(2);
                            this.currentData.actual_unit = mp.unit;
                            console.log("mp.unit", mp.unit);
                            
                        }
                        if (mp.code === 'query_group_boiler_energy_consumption') {
                            this.currentData.query_group_boiler_energy_consumption_value = +(mp.value).toFixed(2);
                            this.currentData.query_group_boiler_energy_consumption_unit = mp.unit;
                        }

                        if (mp.device_id) {
                            this.boilerGroup.list.push(mp);
                            this.singleStatus({
                                abilityId: '9096',
                                requestBody: {
                                    device_id: mp.device_id,
                                    detection_time: this.activeDate,
                                }
                            });
                        } else {
                            this.GuoluGroupInfos.push(mp);
                        }
                    }
                    this.boilerGroup.list.sort((a, b) => a.name[0] - b.name[0]);
                    this.boilerGroup.results = data.result.results;
                    console.log("this.boilerGroup 00", this.boilerGroup);
                    console.log("this.boilerGroup.list ", this.boilerGroup.list);

                }
            } finally {
                this.loading = false;
                if (this.$refs.refStrategy) {
                    this.$refs.refStrategy.loading = false
                }
            }
        },
        chartsResize() {
            this.lineCharts1.resize({ animation: { duration: 0 } });
        },
        setCharts(allData) {
            const xAxisData = [];
            for (const data of allData) {
                xAxisData.push(data[0]);
                for (const [key, series] of Object.entries(data[1])) {
                    const currentSeries = this.lineCharts1Ooption.series.find(s => s.name === chartBase[key]?.name);
                    if (currentSeries) {
                        currentSeries.data.push(series);
                    }
                }
            }
            this.lineCharts1Ooption.xAxis.data = xAxisData;
            // console.log(lineCharts1Ooption)
            this.lineCharts1.setOption(this.lineCharts1Ooption);
        },
        // 处理 select 变化时，更新 sel_deviceGroupId
        handleStationChange(selectedId) {
            const selectedStation = this.stationList.find(item => item.id === selectedId);
            if (selectedStation) {
                this.sel_deviceGroupId = selectedStation.device_group_id;
            }
        },
        async get_api_query_running_analysis_data_list(startDate, endDate) {
            console.log(`Start Date: ${startDate}`);
            console.log(`End Date: ${endDate}`);

            this.activeDateLoading = true;  // 开始加载
            this.loading = true;

            startDate = startDate || dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
            endDate = endDate || dayjs().format('YYYY-MM-DD HH:mm:ss');
            console.log(`End 2 Date: ${endDate}`);
            this.activeDate = endDate;

            try {
                const res = await api_query_running_analysis_data_list({
                    deviceGroupId: this.sel_deviceGroupId,
                    startDate: startDate,
                    endDate: endDate
                    // startDate: "2024-10-01 10:23:14",
                    // endDate: "2024-10-02 10:23:14"
                })
                console.log("get_api_query_running_analysis_data_list 00", res.data.result);
                const allData = Object.entries(JSON.parse(res.data.result));
                console.log(Object.entries(JSON.parse(res.data.result)));
                if (allData.length) {
                    if (this.$refs.refStrategy) {
                        this.$refs.refStrategy.loading = true
                    }
                    this.IsLCdata = true;
                    console.log("有数据吗？allData", this.sel_deviceGroupId, this.activeDate);

                    this.$nextTick(() => {
                        this.setCharts(allData);
                        this.chartsResize()
                    });
                    this.activeDate = allData.at(-1)[0];
                    await new Promise((resolve) => {
                        this.$emit('generalExeApi', {
                            device_group_id: this.sel_deviceGroupId,
                            detection_time: this.activeDate,
                            resolve  // 把 resolve 传递给父组件
                        });
                    });
                    await this.useExecuteData();
                    // this.groupStatus({
                    //     // 9096
                    //     abilityId: '9891',
                    //     requestBody: {
                    //         device_group_id: this.sel_deviceGroupId,
                    //         detection_time: this.activeDate,
                    //     }
                    // });
                } else {
                    this.IsLCdata = false;
                    this.currentData = {
                        actual_value: 0,
                        actual_unit: '',
                        query_group_boiler_energy_consumption_value: 0,
                        query_group_boiler_energy_consumption_unit: '',
                    }
                    this.boilerGroup = {
                        results: [{ verifyResult: true }],
                        list: []
                    }
                    console.log("this.boilerGroup， 怎么回事阿？");
                    
                }
            }
            catch (error) {
                console.error(error)
            } finally {
                this.loading = false;
                this.activeDateLoading = false;  // 无论成功或失败，结束加载

            }
        },
        async query_get_iot_station_list() {
            console.log("啊 0");
            try {
                const res = await get_iot_station_list({ station_name: this.station_name }); // 等待 get_iot_station_list 返回的 Promise
                if (res.status === 200 && res.data.code === 200) {
                    console.log('query_get_iot_station_list 00', res);
                    this.stationList = res.data.result;
                    if (this.stationList.length > 0) {
                        // 默认选中第一项
                        this.station_name = this.stationList[0].station_name;
                        this.sel_deviceGroupId = this.stationList[0].device_group_id;
                    }
                }
            } catch (err) {
                console.log("啊 1", err); // 捕获并处理错误
            }
        },


        initChart() {
            this.lineCharts1 = this.$echarts.init(document.getElementById('lineCharts1'))
            this.lineCharts1.setOption(this.lineCharts1Ooption)
        },
    }
}
</script>
<style lang="scss" scoped>
.mainBox {
    background-color: #fff;
    margin: 16px;
    padding: 0 20px 20px 20px;
    height: 100%;
    overflow-y: auto;

    .infoBox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 15px 0;

        .info-top {
            height: 24px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #323233;
            line-height: 24px;
            text-align: left;
            font-style: normal;
        }

        .infoBox-item {
            display: flex;

            :deep(.el-select) {
                width: 220px;
            }

            >div:first-child {
                margin-right: 12px;
            }

            .item-content {
                :deep(.el-loading-spinner) {
                    // width: 20px !important;
                    // height: 20px !important;
                    top: calc(50% + 10px);
                    background: none !important;
                }
            }
        }
    }

    .infoBox2 {
        display: flex;
        justify-content: start;
        gap: 68px;
        margin-bottom: 16px;

        :deep(.el-loading-spinner) {
            // width: 20px !important;
            // height: 20px !important;
            top: calc(50% + 10px);
            background: none !important;
        }

        // border-bottom: 1px solid #EBECF0;
        .infoBox2-item {

            .infoBox2-item-title {
                height: 22px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #646566;
                line-height: 22px;
                text-align: left;
                font-style: normal;
            }

            .item-content {
                display: flex;
                align-items: center;
                height: 32px;

                :deep(.el-input__inner) {
                    font-weight: 500;
                }

                :deep(.el-date-editor.el-input) {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 15px;
                    color: #323232;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;
                }

                .infoBox2-item-value {
                    height: 28px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 24px;
                    color: #323232;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;

                    margin-right: 4px;
                }

                .infoBox2-item-unit {
                    color: #323232;
                    font-size: 14px;
                }
            }


        }
    }

    .infoBox3 {
        border-top: 1px solid #EBECF0;
        padding: 16px 0;
        min-height: 430px;
        height: 430px;

    }

}
</style>
