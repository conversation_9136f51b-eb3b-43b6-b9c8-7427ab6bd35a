<template>
  <div v-loading="isloading" class="main  ">
    <div v-if="openScreen" class="contain">
      <div class="contain-ce">
        <div class="talk" v-if="isShow">
          <el-row v-if="!this.$route.query.mode_selection">
            <el-col class="col">
              <div class="talk-right" :class="fileList.length > 0 ? 'talk-right-pr' : ''">
                <div class="box-top">
                  <div class="avatar">
                    <img v-if="marketResult.scheme_ext_info?.abilityIcon"
                      :src="marketResult.scheme_ext_info?.abilityIcon" alt />
                    <img v-else src="@/assets/images/image/nl_1.png" alt />
                    <div class="name">{{ marketResult.ability_name }}</div>
                  </div>
                  <div class="build-talk">
                    <div class="talk"></div>
                    <div class="history"></div>
                  </div>
                </div>
                <div ref="scrollContainer" class="chart-content">
                  <div v-for="(el, index) in messaeList" :key="index" :class="el.role === 'user' ? 'boxrg' : 'boxlf'">
                    <div class="chart-box">
                      <div class="chart-main">
                        <div class="text">
                          <img v-if="el.role === 'user' && el.image_path" class="boxrg-filesrc" :src="el.image_path"
                            alt />
                          <div v-if="el.loading" ref="loading" class="qa-loading-spinner3"></div>
                          <div v-if="el.content && !el.loading" class="text-title">
                            <MyEditor id="MyEditorDuiqi" ref="MyEditorDuiqi" :md-content="el.content"></MyEditor>
                          </div>
                          <el-empty v-if="false" :image="require('@/assets/images/planGenerater/emptyParams.png')"
                            description="暂无参数或参数存在多个，无法使用会话模式，请使用其他模式"></el-empty>
                          <div
                            v-if="abilityValueList.length > 0 && el.role !== 'user' && index == messaeList.length - 1 && flowFinish"
                            class="toolp-label">
                            <el-button v-for="(item, index) in abilityValueList" :key="index" type="primary" plain
                              class="tab-item" @click="handleButtonClick(item)">{{ item }}</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="chart-bottom">
                  <div class="chart-icon">
                    <div class="icon-item">
                      <el-tooltip effect="dark" content="语音输入">
                        <div class="phoneStatus">
                          <div v-if="false">
                            <img src="@/assets/images/planGenerater/shuohua.png" />
                          </div>
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="icon-item uploadfile">
                      <el-upload ref="modelUpload" class="upload-demo" :file-list="fileList" :action="uploadUrl"
                        :show-file-list="true" :list-type="listType" :data="uploadParam" :limit="1"
                        accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG" :before-upload="modelBeforeUploadHandle"
                        :on-change="modelChangeHandle" :on-remove="handleRemove" :on-success="modelUploadSuccess">
                        <el-tooltip effect="dark" content="支持图片上传，.jpg,.jpeg,.png,.JPG,.JPEG,.PNG文件">
                          <img src="@/assets/images/planGenerater/upload-icon.png" />
                        </el-tooltip>
                      </el-upload>
                    </div>
                    <div></div>
                  </div>
                  <div class="custom-input">
                    <el-input ref="myInput" class="input-area" autofocus :disabled="!flowFinish"
                      :class="fileList.length > 0 ? 'custom-img' : 'custom-ipt'" type="textarea"
                      :autosize="{ minRows: 2, maxRows: 5 }" v-model="messagefile"
                      @keydown.enter.native.prevent="handleEnter($event)" placeholder="输入你的问题或指令"
                      :style="{ height: '50px' }"></el-input>
                    <div class="suffix-icon">
                      <i @click="onTest()" class="el-icon-position"></i>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="task_model" v-if="isability">
          <div class="header">
            <img v-if="marketResult.scheme_ext_info?.abilityIcon" :src="marketResult.scheme_ext_info?.abilityIcon"
              alt />
            <img v-else src="@/assets/images/image/nl_1.png" alt />
            <div class="title">{{ marketResult.ability_name }}</div>
          </div>
          <div class="main">
            <div class="req_container">
              <div class="title">输入信息</div>
              <div class="param">
                <div v-for="(item, index) in tableData" :key="index">
                  <div class="label">{{ index + 1 }}、{{ item.param_desc }}</div>
                  <div style="flex: 1">
                    <el-select v-if="item.param_name === 'deviceId'" v-model="item.param_value" placeholder="请选择"
                      filterable remote :remote-method="filterEquip" :loading="loading2" style="width: 100%"
                      @change="(val) => changeEquip(val, index)">
                      <el-option v-for="fitem in equipList" :key="fitem.deviceId + ''" :label="fitem.deviceName"
                        :value="fitem.deviceId + ''"></el-option>
                    </el-select>
                    <div v-else-if="item.data_type === 'File'">
                      <div class="fileBox">
                        <shardUploaderTool :accept="''" upload-tip="小于20M" :limit="20" :multiple="false" :cover="true"
                          :index-ref="index" :shard-limit="1" @onFilesChange="uploadScriptCallback"
                          @onFilesStatus="uploadShardStatusCb" @on-remove="removeFile" />
                      </div>
                      <div v-if="previewFlag" class="image">
                        <img v-if="curImageUrl" :src="curImageUrl" />
                        <span v-else>暂不支持预览</span>
                      </div>
                    </div>
                    <el-select v-else-if="item.data_type === 'bool' || item.data_type === 'Boolean'"
                      v-model="item.param_value" placeholder="请选择">
                      <el-option key="1" value="true">true</el-option>
                      <el-option key="2" value="false">false</el-option>
                    </el-select>
                    <el-date-picker v-else-if="item.data_type === 'dateTime'" v-model="item.param_value"
                      :picker-options="pickerOptions1" style="width: 100%" type="datetime" popper-class="mytest"
                      placeholder="选择日期时间"></el-date-picker>
                    <el-input v-else v-model="item.param_value" />
                  </div>
                </div>
              </div>
              <div class="foot">
                <el-button class="check-button" type="primary" @click="handleTest"
                  :disabled="completeLoading">开始检测</el-button>
              </div>
            </div>
            <div class="result">
              <div class="title">
                识别结果
                <span v-if="Object.keys(jsonData).length && jsonData.success" class="reTitle">
                  <i style="color:green;" class="el-icon-success"></i>
                  {{ jsonData.message }}
                </span>
                <span v-if="Object.keys(jsonData).length && !jsonData.success" class="reTitle">
                  <i style="color:red;" class="el-icon-error"></i>
                  {{ jsonData.message }}
                </span>
              </div>
              <div class="content">
                <div v-if="testloading" class="qa-loading-spinner3"></div>
                <div v-if="Object.keys(jsonData).length" class="qsResult" :loading="testloading">
                  <div class="reContent">
                    <MyEditorPreview id="MyJsonEditorResult" ref="MyJsonEditorResult" :md-content="jsonData.result">
                    </MyEditorPreview>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isempty" class="empty-img">
      <el-empty :image="require('@/assets/images/planGenerater/empty.png')" description="暂无"></el-empty>
    </div>
  </div>
</template>
<script>
import MyEditor from '../mdEditor.vue';
import { createUuidAndSnowflake } from '@/utils/util.js';
import { getMarketAppInfo, queryAbilityApi, byApproveCode, byToken } from '@/api/planGenerateApi.js';
import axios from 'axios';
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue';
import dayjs from 'dayjs';
import MyEditorPreview from '@/views/planGenerate/mdEditorPreview.vue';
const cancelToken = axios.CancelToken;
let source = cancelToken.source();
export default {
  name: 'abilityTalk',
  components: {
    MyEditorPreview,
    shardUploaderTool,
    MyEditor
  },
  data() {
    return {
      isloading: false,
      reqUrl: '',
      ennunifiedcsrftoken: '',
      agentSceneCode: 'execute',
      openScreen: false,
      flowFinish: true,
      isShow: false,
      isempty: false,
      isability: false,
      uuid: '',
      image_path: '',
      onShow: false,
      userPicture: [],
      listType: 'picture',
      previewFlag: false,
      curImageUrl: '',
      toMessage: { content: '', image_key: '', image_path: '' },
      isFilterFlag: true,
      completeLoading: false,
      testloading: false,
      resultTableData: [],
      messaeListChat: [],
      uploadUrl: '',
      uploadParam: {},
      messaeList: [],
      dialogMessageList: [],
      marketResult: {},
      abilityValueList: [],
      messagefile: '',
      isDragging: false,
      fileList: [],
      tableData: [],
      filesList: [],
      jsonData: {}
    };
  },
  beforeCreate() {
    this.$store.commit('planGenerate/setHideIframe', true);
  },
  created() {
    this.getbyApproveCode({
      approveCode: this.$route.query?.approveCode || ''
    })
    this.uuid = createUuidAndSnowflake();
  },
  mounted() {
  },
  watch: {
    '$route.query.approveCode': {
      handler(val) {
        window.localStorage.setItem('ShareCode', val)
        this.getbyApproveCode({
          approveCode: val || ''
        });
      }
    },
    messaeList() {
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    }
  },
  methods: {
    getbyApproveCode(params = {}) {
      this.isloading = true
      byApproveCode(params).then(res => {
        if (res.data.code === 200 && res.status === 200) {
          if (res.data.result) {
            this.ennunifiedcsrftoken = res.data.result?.token || ''
            this.openScreen = true;
            this.isempty = false
            if (res.data.result.abilityid) {
              this.getMarketAppInfo({
                ability_id: +res.data.result?.abilityid || ''
              });
              this.getCodeParams({ ability_id: Number(res.data.result?.abilityid || '') });
            }
          } else {
            this.openScreen = false
            this.isempty = true
          }
          this.isloading = false
        }else {
          this.openScreen = false
          this.isloading = false
          this.isempty = true
          this.$message.error(res.data.msg)
        }
      }).catch(err => {
        this.openScreen = false
        this.isloading = false
        this.isempty = true
        this.$message.error('内部异常')
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        // 使用this.$refs访问DOM元素
        const container = this.$refs?.scrollContainer;
        // 滚动到底部
        if (container && container.scrollTop) {
          container.scrollTop = container?.scrollHeight;
        }
      })
    },
    handleButtonClick(item) {
      this.messaeList.push({
        content: item,
        role: 'user',
        loading: false,
        image_path: '',
        onShow: this.onShow
      });
      this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest);
      this.handleTest1(JSON.stringify(this.messaeListChat));
    },
    handleEnter(event) {
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault();
          this.$nextTick(() => {
            if (
              (this.messagefile && this.fileList.length > 0 && this.flowFinish) ||
              (this.messagefile && this.flowFinish)
            ) {
              this.onTest();
            }
          });
        }
      }
    },
    uploadScriptCallback(fileData, fileList, index) {
      const fileType = this.$fileUtil.getFileSuffix(fileData.name);
      if (fileData.path) {
        this.previewFlag = true;
      } else {
        this.previewFlag = false;
      }
      if (
        fileType.toLowerCase() === 'jpg' ||
        fileType.toLowerCase() === 'png' ||
        fileType.toLowerCase() === 'jpeg'
      ) {
        this.curImageUrl = fileData.path;
      } else {
        this.curImageUrl = '';
      }
      this.filesList[index] = fileData.path;
    },
    uploadShardStatusCb(status) { },
    removeFile(fileId, filelist, index) {
      this.filesList[index] = '';
    },
    async handleTest1(datas) {
      let dialContect = [];
      if (this.fileList.length > 0 && this.messagefile) {
        dialContect.push({
          type: 'text',
          text: this.messagefile
        });
        dialContect.push({
          type: 'image_url',
          image_url: {
            url: this.toMessage.image_path
          }
        });
        this.image_path = dialContect[1].image_url?.url;
      } else {
        dialContect.push({
          type: 'text',
          text: this.messagefile
        });
        this.image_path = '';
      }
      this.onShow = dialContect.some((item) => item.type === 'image_url');
      this.completeLoading = true;
      this.flowFinish = false;
      this.messagefile = '';
      this.fileList = [];
      this.onShow = false;
      this.messaeList.push({
        role: 'assistant',
        content: '',
        loading: true,
        image_path: '',
        onShow: false
      });
      const lastWithMessage = this.messaeList[this.messaeList.length - 1];
      let url = '';
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      let params = {};
      url = this.reqUrl;
      params = {
        params: {
          session_id: this.uuid,
          ability_id: 'chatbot',
          userMessage: {
            role: 'user',
            content: dialContect
          }
        },
      };
      this.$axios
        .post(
          url,
          {
            ...params
          },
          {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              'X-GW-Authorization': this.ennunifiedcsrftoken,
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/json'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target;
              const { responseText } = xhr;
              let newStr;
              newStr = responseText.replace(/[\r\n]/g, '');
              let splitres = JSON.parse(JSON.stringify(responseText.split('data: '))).filter(item => {
                if (item && item !== '[DONE]') { return true }
              })
              // 去掉空行并将字符串对象转换为 JSON 对象
              const jsonObjects = splitres.map(str => {
                // 去掉空行
                const cleanedStr = str.replace(/\n/g, '');
                if (cleanedStr !== '[DONE]') {
                  return JSON.parse(cleanedStr);
                }
              });
              // 提取 result 字段并拼接
              const concatenatedResult = jsonObjects.map(obj => obj?.result).join('');
              this.$nextTick(() => {
                lastWithMessage.loading = false;
                this.$nextTick(() => {
                });
                lastWithMessage.content = concatenatedResult;
              });
            },
            onError: function (error) {
              // 处理流错误
              this.flowFinish = true;
              lastWithMessage.loading = false
              this.$nextTick(() => {
                this.scrollToBottom()
              });
            }
          }
        )
        .then(async (res) => {
          // 关闭数据流
          this.flowFinish = true;
          lastWithMessage.loading = false
          this.$nextTick(() => {
            this.scrollToBottom()
          });
          this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest);
          this.completeLoading = false;
        })
        .catch(() => {
          this.flowFinish = true;
          this.completeLoading = false;
          lastWithMessage.loading = false
          this.$nextTick(() => {
            this.scrollToBottom()
          });
        });
    },
    onTest() {
      // 将消息发送到服务器
      this.messaeList.push({
        content: this.messagefile,
        role: 'user',
        loading: false,
        image_path: this.fileList.length ? this.toMessage.image_path : '',
        onShow: this.onShow
      });
      this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest);
      this.handleTest1(JSON.stringify(this.messaeListChat));
      this.userPicture = JSON.parse(JSON.stringify(this.fileList));
    },
    async getFileSign(file) {
      try {
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        });
        if (res.data.status === 200) {
          this.toMessage.image_key = res.data.data.key;
          this.uploadUrl = res.data.data.obsUrl;
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          };
        }
      } catch (e) {
        console.log(e);
        this.$message.error('获取签名出错！');
      }
    },
    async modelBeforeUploadHandle(file) {
      const isLt2M = file.size / 1024 / 1024 / 1024 < 1;
      if (!isLt2M) {
        this.$message.error('大小不能超过1GB!');
        return false;
      }
      await this.getFileSign(file);

      return isLt2M;
    },
    modelChangeHandle(file, fileList) {
      this.fileList = fileList;
      this.upLoadFileFlag = true;
      if (fileList.length > 0) {
        this.onShow = true;
      } else {
        this.onShow = false;
      }
    },
    modelUploadSuccess(response, file) {
      this.showProcess = false;
      this.processLength = 0;

      this.uploadStatus = file.status;
      if (this.uploadStatus === 'success') {
        this.$message.success(`模型文件上传状态为:${this.uploadStatus}`);
        const fileName = this.$fileUtil.getFileName(file.raw.name);
        const fileSize = file.raw.size / 1024;
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name);
        const fileKey = this.uploadParam.key;

        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.toMessage.image_path = res.data.data.path;
              const fileId = res.data.data.fileId;
            }
          });
      } else {
        this.$message.warning(`模型上传状态为:${this.uploadStatus}`);
      }

      this.modelDialogVisible = false;
    },
    getMarketAppInfo(params) {
      getMarketAppInfo(params).then((res) => {
        if (res?.data?.code === 200) {
          this.marketResult = res.data.result;
          if (this.marketResult.ext_info?.mode_selection == 'dialogue') {
            this.isShow = true;
          } else {
            this.isability = true;
          }
          this.abilityValueList = this.marketResult.scheme_ext_info.abilityIntents.split('\n');
          this.messaeList.push({
            content: this.marketResult?.scheme_ext_info?.opening_statement,
            role: 'assistant'
          });
          this.flowFinish = true;
        }
      });
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX;
        const widthLeft = this.startWidth + deltaX;
        this.leftWidth = widthLeft + 'px';
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    getCodeParams(params) {
      queryAbilityApi(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.newTableData = res?.data?.result?.req_body || [];
          this.reqUrl = res?.data?.result?.req_url || [];
        } else {
          this.newTableData = [];
        }
        const files = [];
        const temp = [];
        if (this.agent_scene_code === 'device_ops_assistant_scene') {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name === 'detectionTime') {
              item.param_value = dayjs()
                .subtract(2, 'day')
                .startOf('day')
                .format('YYYY-MM-DD HH:mm:ss');
              temp.push({ ...item });
            } else {
              temp.push({ ...item, param_value: '' });
            }
            files.push('');
          });
        } else {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name !== 'detectionTime' && item.param_name !== 'deviceId') {
              temp.push({ ...item, param_value: '' });
            }
            files.push('');
          });
        }

        this.filesList = files;
        this.tableData = temp;
      });
    },
    async handleTest() {
      this.jsonData = {};
      this.testloading = true;
      this.completeLoading = true;
      let url = '';
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      let params = {};
      const datas = {};
      let deviceId = '';
      let detectionTime = '';
      this.tableData.forEach((item, index) => {
        if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
          if (item.data_type !== 'File') {
            if (item.data_type == 'Boolean' || item.data_type == 'bool') {
              const temp = item.param_value === 'true';
              datas[item.param_name] = temp;
            } else if (item.data_type == 'Array' || item.data_type == 'Object') {
              try {
                const temp = JSON.parse(item.param_value);
                datas[item.param_name] = temp;
              } catch (error) {
                const temp = item.param_value;
                datas[item.param_name] = temp;
              }
            } else if (item.data_type == 'String' || item.data_type == 'string') {
              datas[item.param_name] = item.param_value;
            } else if (item.data_type == 'Number' || item.data_type === 'decimal') {
              const temp = Number(item.param_value);
              if (isNaN(temp)) {
                datas[item.param_name] = item.param_value;
              } else {
                datas[item.param_name] = temp;
              }
            } else {
              if (item.param_value !== '') {
                datas[item.param_name] = item.param_value;
              } else {
                console.log('不传');
              }
            }
          } else {
            datas[item.param_name] = this.filesList[index];
          }
        } else {
          if (item.param_name === 'deviceId') {
            deviceId = item.param_value;
          }
          if (item.param_name === 'detectionTime') {
            detectionTime = item.param_value;
          }
        }
      });
      if (this.agent_scene_code === 'device_ops_assistant_scene') {
        this.agentSceneCode = 'executev1'
        url = this.reqUrl

        params = {
          scheme_id: this.marketResult.scheme_id,
          deviceId: deviceId,
          detectionTime: dayjs(detectionTime).format('YYYY-MM-DD HH:mm:ss'),
          params: datas,
          oper_type: 'ability'
        };
      } else {
        this.agentSceneCode = 'execute'
        url = this.reqUrl
        params = {
          params: datas,
        };
      }
      this.$axios
        .post(
          url,
          {
            ...params
          },
          {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              'X-GW-Authorization': this.ennunifiedcsrftoken,
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/json'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              this.testloading = false;
              const xhr = event.target;
              const { responseText } = xhr;
              let newStr = responseText.replace(/[\r\n]/g, '');
              let splitres = JSON.parse(JSON.stringify(responseText.split('data: '))).filter(item => {
                if (item && item !== '[DONE]') { return true }
              })
              
              // 去掉空行并将字符串对象转换为 JSON 对象
              const jsonObjects = splitres.map(str => {
                // 去掉空行
                const cleanedStr = str.replace(/\n/g, '');
                if (cleanedStr !== '[DONE]') {
                  return JSON.parse(cleanedStr);
                }
              });
              // 提取 result 字段并拼接
              const concatenatedResult = jsonObjects.map(obj => obj?.result).join('');
              // 输出结果
              this.$nextTick(() => {
                this.jsonData = {
                  message: '执行成功',
                  result: concatenatedResult,
                  success: true
                };
              });
            },
            onError: function (error) {
              // 处理流错误
              console.error(error);
              this.testloading = false;
            }
          }
        )
        .then(async (res) => {
          // 关闭数据流
          console.log('数据流', res);
          this.testloading = false;
          this.completeLoading = false;
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result) {
              if(Object.prototype.toString.call(res.data.result) === '[object Object]') {
                this.jsonData = {
                  message: '执行成功',
                  result: JSON.stringify(res.data.result,null,2),
                  success: true
                };

              }else {
                this.jsonData = res.data.result || {
                  message: '执行成功',
                  result: '',
                  success: true
                };
              }
            } else {
              this.jsonData = {
                message: res.data.msg || '执行失败',
                result: '',
                success: false
              };
            }
          } else {
            if (res.status === 200) {
              this.$nextTick(() => {
                const newStr = res.data.replace(/[\r\n]/g, '');
                let splitres = JSON.parse(JSON.stringify(res.data.split('data: '))).filter(item => {
                  if (item && item !== '[DONE]') { return true }
                })
                const jsonObjects = splitres.map(str => {
                  // 去掉空行
                  const cleanedStr = str.replace(/\n/g, '');
                  if (cleanedStr !== '[DONE]') {
                    return JSON.parse(cleanedStr);
                  }
                  // 解析 JSON
                });
                // 提取 result 字段并拼接
                const concatenatedResult = jsonObjects.map(obj => obj?.result).join('');
                this.jsonData = {
                  message: '执行成功',
                  result: concatenatedResult,
                  success: true
                };
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          }
        })
        .catch((err) => {
          // this.$message.error('内部异常')
          this.testloading = false;
          this.completeLoading = false;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.empty-img {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contain {
  height: 200px;
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  justify-content: space-between;
  padding-left: 20px;
  box-shadow: 0px 1px 0px 0px #ebecf0;

  &-ce {
    flex: 1;
  }
}

::v-deep .el-button+.el-button {
  margin-left: 0px;
  display: block;
}

::v-deep button.el-button.tab-item.el-button--primary.is-plain {
  border-radius: 6px;
}

::v-deep .el-input__inner {
  border-radius: 6px;
}

::v-deep .el-input__suffix {
  display: flex;
  align-items: center;
}

.el-row {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.talk {
  overflow-y: hidden;
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;

  &-left {
    height: calc(100vh - 88px);
    background: #ffffff;
    border: 1px solid transparent;
  }

  .talk-end {
    height: calc(100vh - 88px);
    background: #ffffff;
  }

  &-right-pr {
    padding-bottom: 70px !important;
  }

  &-right {
    display: flex;
    flex-direction: column;
    background: #f3f6ff;
    height: calc(100vh - 88px);
    /* 设置为可视区域高度 */
    overflow: hidden;
    padding: 28px 30px 36px 30px;

    .custom-img {
      ::v-deep textarea.el-textarea__inner {
        height: 90px !important;
        padding-top: 56px !important;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .avatar {
        display: flex;
        align-items: center;
        justify-content: space-between;

        img {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 8px;
        }

        .name {
          font-weight: 500;
          font-size: 16px;
          color: #323233;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .build-talk {
        display: flex;
        align-items: center;
      }
    }

    .chart-bottom {
      position: relative;
      display: flex;
      align-items: flex-end;

      ::v-deep textarea.el-textarea__inner {
        border-radius: 6px;
      }

      ::v-deep ul.el-upload-list.el-upload-list--text {
        width: 100%;
        position: absolute;
        left: 0px;
        z-index: 1;
      }

      ::v-deep ul.el-upload-list.el-upload-list--picture li {
        width: 24px;
        transform: scale(0.5);
        position: absolute;
        margin-top: 0px;
        left: 22px;
        top: -16px;
        z-index: 1;
        object-fit: cover;
        border-radius: 4px;
      }

      ::v-deep textarea.el-textarea__inner {
        resize: none;
      }

      .custom-input {
        width: 100%;
        position: relative;
        display: inline-block;

        .suffix-icon {
          position: absolute;
          right: 10px;
          top: 50%;
          cursor: pointer;
        }
      }

      .chart-icon {
        display: flex;
        justify-content: column;
        align-items: flex-start;
        /* 添加这一行，使 chart-icon 靠左 */
        margin: 0 auto;

        /* 添加这一行 */
        .icon-item {
          cursor: pointer;

          .upload-demo {
            margin-right: 6px;
          }

          img {
            width: 24px;
            height: 24px;
          }
        }
      }

      .custom-input {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        margin: 0 auto;

        /* 添加这一行 */
        .input-area {
          height: 50px;
          min-height: 50px;
        }
      }
    }

    .chart-content {
      flex: 1;
      /* 填充剩余空间 */
      overflow-y: auto;
      background: #f3f6ff;
      justify-content: flex-start;
      align-items: flex-start;
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;

      /* Firefox */
      &::-webkit-scrollbar {
        display: none;
      }

      .boxrg {
        flex: 1;

        ::v-deep .editor-show {
          overflow: initial !important;
        }

        .boxrg-filesrc {
          width: 208px;
          height: 123px;
        }

        .chart-main {
          display: flex;
          width: 100%;
          justify-content: flex-end;
        }
      }

      .boxlf {
        flex: 1;
        width: 100%;

        .chart-main {
          display: flex;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .chart-box {
        .chart-main {
          .text {
            max-width: calc(100% - 80px);
            flex: 0 0 auto;
            background: #ffffff;
            padding: 8px;
            text-align: left;
            margin-bottom: 8px;

            &-title {
              font-weight: 400;
              font-size: 14px;
              color: #323233;
              line-height: 20px;
              text-align: left;
              font-style: normal;
            }
          }

          .toolp-label {
            background-color: transparent;

            .tab-item {
              margin-bottom: 8px;
            }

            .tab-item:last-of-type {
              margin-bottom: 0px;
            }
          }
        }
      }
    }
  }
}

.fileBox {
  :deep(.el-upload-list__item:first-child) {
    margin-top: 3px;
  }
}

.image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  margin-top: 10px;
  color: rgb(150, 151, 153);

  img {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: auto;
  }
}

.ability-info {
  background-color: white;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88px);

  .ability-basic {
    padding: 10px;
    flex-basis: 30%;
    background-color: inherit;

    .title {
      display: block;
      font-weight: bold;
      font-size: 18px;
    }

    .ability-value {
      display: block;
      font-size: 14px;
    }
  }

  .ability-user-voice {
    background-color: inherit;
    flex-grow: 1;
    padding: 10px;

    .title {
      margin-left: 3px;
      font-size: 14px;
      font-weight: bold;
      border-left: 2px solid #000;
      padding-left: 5px;
      margin-bottom: 5px;
    }
  }
}

.task_model {
  background-color: white;
  border-radius: 5px;
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  min-width: 600px;
  height: calc(100vh - 88px);

  .header {
    display: flex;
    justify-content: flex-start;

    img {
      width: 48px;
      height: 48px;
      border-radius: 24px;
    }

    .title {
      font-weight: bold;
      font-size: 16px;
      margin: auto 5px;
    }
  }

  .main {
    margin-top: 15px;
    flex-grow: 1;
    display: flex;
    gap: 10px;
    overflow: auto;

    .req_container {
      border: 1px solid #dcdde0;
      border-radius: 5px;
      height: 98%;
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        margin-bottom: 20px; // 增加下边距
        border-bottom: 2px solid #ccc; // 添加下划线
      }

      .param {
        margin-top: 10px;
        overflow-y: scroll;

        div {
          margin-bottom: 5px;
        }

        .label {
          font-size: 14px;
          font-weight: bold;
        }
      }

      .foot {
        flex-grow: 1;
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;

        .check-button {
          font-weight: 500;

          /* 按钮文字粗细 */
          &:hover {
            background-color: #66b1ff;
            /* 按钮悬停背景色 */
            border-color: #66b1ff;
            /* 按钮悬停边框色 */
          }

          &:active {
            background-color: #3a8ee6;
            /* 按钮激活背景色 */
            border-color: #3a8ee6;
            /* 按钮激活边框色 */
          }
        }
      }
    }

    .result {
      background-color: #fff;
      border: 1px solid #dcdde0;
      border-radius: 5px;
      height: 98%;
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        margin-bottom: 20px; // 增加下边距
        border-bottom: 2px solid #ccc; // 添加下划线
      }

      .content {
        word-wrap: break-word;
        margin-top: 10px;
        overflow-y: scroll;
      }
    }
  }
}

.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

:deep(.el-avatar) {
  background-color: #409eff;
  font-size: 16px;
  font-weight: bold;
}
</style>
