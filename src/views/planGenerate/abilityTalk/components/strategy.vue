<template>
    <div class="strategy" v-loading="loading" element-loading-spinner="el-icon-loading">
        <div class="strategy-heard">
            <div class="title">锅炉运行策略</div>
        </div>
        <div class="strategy-contain">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane v-for="(item) in strategyList" :key="item.device_id" :label="item.name"
                    :name="item.device_id">
                    <div v-show="boilerGroup && boilerGroup.list.length" class="group">
                        <div class="group-lf">
                            <div class="lf-title">
                                <div class="heard">
                                    <div class="line"></div>
                                    <div class="heard-contain">
                                        运行参数现状
                                    </div>
                                </div>
                            </div>
                            <div class="table">
                                <div class="items">
                                    <div>
                                        <div class="item" v-for="(it, index) in paramsList" :key="index">
                                            <div class="row">{{ it.name }}</div>
                                            <div class="column">{{
                                                it.value.toFixed(2) }}{{
                                                it.unit }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="group-rg" v-if="item.device_id !== 'first'">
                            <div class="lf-title">
                                <div class="heard">
                                    <div class="line"></div>
                                    <div class="heard-contain">
                                        运行负荷
                                    </div>
                                </div>
                            </div>
                            <div class="pie">
                                <div :ref="'recombination' + item.device_id" class="recombination"
                                    style="width: 260px; height:260px;"></div>
                            </div>
                        </div>
                    </div>
                    <div v-if="!boilerGroup || (boilerGroup.list && boilerGroup.list.length === 0)" style="
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        height: 340px;
                        width: 100%;
        ">
                        <img src="@/assets/images/planGenerater/emptyData.png" style="width: 180px; height: auto" />
                        <div style="
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        margin-top: 16px;
        ">
                            暂无数据
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="action-recommendation" v-show="boilerGroup && boilerGroup.list.length">
            <div>
                <span class="ui-span"></span>
                <span class="title-span">处置建议</span>
            </div>
            <div class="content" v-html="recommendationText"></div>
            <div class="change-btn" @click="handleChange">
                <el-button>更换</el-button>
            </div>
        </div>
    </div>
</template>
<script>
// import * as echarts from 'echarts'
export default {
    name: 'strategy',
    props: {
        boilerGroup: {
            type: Object,
            required: true
        },
        GuoluGroupInfos: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            recommendationText: '暂无数据',
            currentverifyResult: true,
            charts: {}, // 初始化 charts 为一个空对象
            loading: false,
            activeName: 'first',
            strategyList: [
                {
                    name: '群组锅炉',
                    device_id: 'first'
                },
                // {
                //     label: '1#锅炉',
                //     name: 'second'
                // },
                // {
                //     label: '2#锅炉',
                //     name: 'third'
                // },
            ],
            paramsList: [
            ],
            pie: {
                title: {
                    text: '',
                    left: '50%',
                    top: '45%',
                    textAlign: 'center',
                    textStyle: {
                        fontFamily: 'PingFangSC, PingFang SC',
                        fontWeight: 500,
                        fontSize: 24,
                        lineHeight: 28,
                        color: '#323232',
                        align: 'center'
                    },
                },
                series: [
                    {
                        type: 'pie',
                        // center: ['50%', '50%'],
                        labelLine: {
                            show: false
                        },
                        label: {
                            show: false
                        },
                        color: ['#EBECF0', '#EA4646', '#4068D4'],
                        // bottom: 80,
                        // radius:'100%'
                        radius: ['55%', '80%'],
                        data: [
                            { value: 15.5, name: 'Unused Load' },
                            { value: 84.5, name: 'Used Load' },
                        ],
                        // 基本的动画配置
                        // animation: true,
                        // animationDuration: 1000,  // 动画持续时间
                        // animationEasing: 'cubicOut'  // 动画缓动效果
                    }
                ]
            },
        }
    },
    watch: {
        boilerGroup: {
            //   immediate:true,
            handler(val) {
                this.strategyList = [
                    {
                        name: '群组锅炉',
                        device_id: 'first'
                    },
                ]
                console.log("loading true 00", val, !this.boilerGroup || (this.boilerGroup.list && this.boilerGroup.list.length === 0));
                if (!val || (val.list && val.list.length === 0)){
                    this.activeName = 'first'
                    console.log("active name 00", this.activeName);
                }
                // 确保 val.list 存在，且最后一个元素存在并具有 measurePoints 属性
                if (val && Array.isArray(val.list) && val.list.length > 0) {
                    console.log("n 说的哪个length 00", val.list.length);

                    const lastItem = val.list.at(-1);
                    if (lastItem.measurePoints && lastItem.measurePoints.length > 0) {
                        this.strategyList = this.strategyList.concat(this.boilerGroup.list);
                        console.log("this.boilerGroup 111100", this.boilerGroup);
                        this.generateRecommendationText(this.boilerGroup.results)
                        if (this.activeName === 'first') {
                            this.paramsList = this.GuoluGroupInfos
                        } else {
                            const finditem = this.boilerGroup.list.find(item => item.device_id === this.activeName)
                            if (finditem) {
                                console.log("finditem 00", finditem);
                                this.currentverifyResult = this.verifyOverload(finditem)
                                this.paramsList = finditem.measurePoints
                                console.log("this.paramsList xy2", this.paramsList);
                            }

                        }
                        this.$nextTick(() => {
                            this.initCharts();
                            console.log("你怎么提前结束loading了 00?");

                        });
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        activeName: {
            handler(val) {
                if (val === 'first') {
                    console.log("this.GuoluGroupInfos 00", this.GuoluGroupInfos);
                    this.paramsList = this.GuoluGroupInfos
                } else {
                    console.log("this.boilerGroup list x111", this.boilerGroup.list);
                    const finditem = this.boilerGroup.list.find(item => item.device_id === val)
                    if (finditem) {
                        console.log("finditem 00", finditem);
                        this.currentverifyResult = this.verifyOverload(finditem)
                        this.paramsList = finditem.measurePoints
                        console.log(" finditem.measurePoints 00", finditem.measurePoints);
                    }
                    this.$nextTick(() => {
                        this.initCharts();
                    });
                }
            },
            // immediate: true,
        },
    },
    async created() {
        console.log("this.boilerGroup11111 00", this.boilerGroup.list);
    },

    beforeDestroy() {
        // 组件销毁时移除监听
        window.removeEventListener('resize', this.chartsResize);
    },
    methods: {
        handleChange() {
            this.$emit('changeRecommendation')
        },
        generateRecommendationText(results) {
            // 检查数组长度，决定是否显示编号
            if (results.length === 1) {
                // 只有一条内容，不加编号
                this.recommendationText = `<p>${results[0].verifyMessage}</p>`;
            } else {
                // 多条内容，加编号
                this.recommendationText = results
                    .map((item, index) => {
                        // 使用模板字符串生成 HTML
                        return `<p>${index + 1}. ${item.verifyMessage}</p>`;
                    })
                    .join('');  // 合并所有生成的字符串
            }
        },
        verifyOverload(finditem) {
            for (const i of finditem.results) {
              if (i.measurePoint === 'LoadSet') {
                    return finditem.verifyResult
                } else {
                    return true
                }
            }
        },
        // 在 data 里添加一个方法，获取指定 code 的数据
        getLoadValue() {
            console.log("this.paramsList xy1", this.paramsList);

          const loadParam = this.paramsList.find(param => param.code === 'LoadSet');
            return loadParam ? `${loadParam.value.toFixed(2)}${loadParam.unit}` : 'N/A';
        },

        updatePieData(loadPercentage) {
            if (this.activeName === 'first') return
            console.log("currentverifyResult 00", this.currentverifyResult, this.currentverifyResult === false);
            this.pie.series[0].color = this.currentverifyResult === false ? ['#EBECF0', '#EA4646'] : ['#EBECF0', '#4068D4'],
                console.log("运行负荷 001", loadPercentage);
            const loadValue = parseFloat(loadPercentage); // 将百分比文本转换为数值
            this.pie.title.text = loadPercentage;
            this.pie.series[0].data = [
                { value: 100 - loadValue, name: 'Unused Load' },
                { value: loadValue, name: 'Used Load' },
            ];

            // 通过 activeName 获取当前活动的图表
            const chartElement = this.$refs[`recombination${this.activeName}`];

            // const chart = this.charts[`recombination${this.activeName}`];
            const chart = this.$echarts.init(chartElement[0])
            if (chart) {
                chart.setOption(this.pie, true);
                console.log("重新画了吗 01", chart.id, this.$refs[`recombination${this.activeName}`][0]);

                // chart.resize()
                this.$nextTick(() => {
                    console.log("重新画了吗 02", chart.id);
                    // chart.resize()
                });
            } else {
                console.warn(`No chart instance found for device_id: ${this.activeName}`);
            }
        },

        initCharts() {
            this.strategyList.forEach((item) => {
                if (item.device_id !== 'first') {

                    const chartElement = this.$refs[`recombination${item.device_id}`];

                    // 先检查 this.charts 是否已经存在该设备的 chart 实例
                    if (!this.charts[`recombination${item.device_id}`]) {

                        if (chartElement && chartElement[0]) {
                            console.log("初始化图表 001");

                            const chart = this.$echarts.init(chartElement[0]);
                            chart.setOption(this.pie);

                            // 使用 Vue 的 $set 方法来动态地为 charts 添加新属性
                            this.$set(this.charts, `recombination${item.device_id}`, chart);

                            // 添加 resize 监听事件
                            window.addEventListener('resize', () => {
                                chart.resize();
                            });
                        } else {
                            console.warn(`Element not found for device_id: ${item.device_id}`);
                        }
                    } else {
                        console.warn(`Chart for device_id: ${item.device_id} already initialized.`);
                        // const existingChart = this.$echarts.getInstanceByDom(chartElement[0]);
                        // if (existingChart) {
                        //     // 如果实例存在，销毁该实例
                        //     console.log("存在就销毁");
                        //     this.$echarts.dispose(existingChart);
                        // }
                        const loadValue = this.getLoadValue();
                        // 更新饼图数据
                        this.updatePieData(loadValue);
                    }
                }
            });
        },


        handleClick() {
            console.log("this.activeName 00", this.activeName);
        }
    }
}
</script>
<style lang="scss" scoped>
.strategy {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    min-height: 583px;
    background: #ffffff;

    :deep(.el-loading-spinner) {
        // width: 20px !important;
        // height: 20px !important;
        background: none !important;
    }

    &-heard {
        height: 54px;
        padding: 16px 0px;

        .title {
            height: 24px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #323233;
            line-height: 24px;
            text-align: left;
            font-style: normal;
        }
    }

    &-contain {
        :deep(.el-tabs__nav-wrap):after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0.5px;
            background-color: #ebecf0;
            z-index: 1;
        }

        :deep(.el-tab-pane) {
            min-height: 340px;
        }



        .group {
            display: flex;

            //    padding: 0 20px;

            .lf-title {
                height: 48px;
                display: flex;
                align-items: center;

                .heard {
                    display: flex;
                    align-items: center;

                    .line {
                        width: 2px;
                        margin: 0px 8px 0px 0px;
                        height: 12px;
                        background: #323233;
                        border-radius: 2px;
                    }

                    .heard-contain {
                        height: 22px;
                        font-weight: 500;
                        font-size: 14px;
                        color: #323233;
                        line-height: 22px;
                    }
                }
            }

            &-lf {
                flex: 1;

                // .table {
                // display: flex;
                .items {
                    display: flex;

                    .item {
                        display: flex;
                        border: 1px solid #EBECF0;

                        .row {
                            height: 40px;
                            width: 180px;
                            color: #323233;
                            padding: 9px 16px;
                            background: #F6F7FB;
                            border-right: 1px solid #EBECF0;
                        }

                        .column {
                            height: 40px;
                            width: 164px;
                            padding: 9px 16px;
                            color: #323233;
                            // height: 22px;
                            // line-height: 22px;
                        }

                        .exceed {
                            color: #EA4646;
                        }
                    }
                }

                // }
            }

            &-rg {
                flex: 1;

                .pie {
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .recombination {
                        // width: 260px !important;
                        // height: 260px !important;
                    }
                }
            }
        }
    }

    ::v-deep .el-tabs__nav-scroll {
        padding: 0 0px;
    }

    ::v-deep .el-tabs__header {
        margin-bottom: 8px;
    }

    .action-recommendation {
        display: flex;
        flex-direction: column;
        margin-top: 36px;
        gap: 16px;

        .ui-span {
            display: inline-block;
            width: 2px;
            height: 12px;
            background: #323233;
            border-radius: 2px;
            margin-right: 8px;
        }

        .title-span {
            height: 22px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #323233;
            line-height: 22px;
            text-align: left;
            font-style: normal;
        }

        .content {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #646566;
            line-height: 22px;
            text-align: justify;
            font-style: normal;
        }

        .change-btn {
            width: 60px;
            height: 30px;
            background: #F2F3F5;
            border-radius: 2px;
            align-self: flex-end;
        }
    }
}
</style>
