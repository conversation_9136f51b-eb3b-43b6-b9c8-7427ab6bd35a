<template>
  <div class="business-validation">
    <div class="ask">
      <span></span>
      <el-input
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 5 }"
        placeholder="请输入内容"
        v-model="textareaVal"
      >
      </el-input>
      <div class="ask-buttons">
        <el-button
          class="button-last"
          type="primary"
          :loading="resultLaoding"
          :disabled="!textareaVal"
          @click="handleTest"
          >{{ resultLaoding ? '测试中...' : '测试' }}</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import MyEditorPreview from '../../planGenerate/mdEditorPreview.vue'
import { obtainVerificationContent, OnCodeTest, queryAbilityApi } from '@/api/planGenerateApi.js'

export default {
  name: 'BusinessValidation',
  components: { MyEditorPreview },
  data() {
    return {
      textareaVal: '',
      jsonData: {},
      resultLaoding: false,
      roughQuery: {},
      detailQuery: []
    }
  },
  props: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 测试结果展示处理
    resultDisplayProcessing() {
      const result = this.jsonData?.resp?.result
      if (typeof result === 'undefined' || result == null || result === '') {
        return this.jsonData?.resp?.message
      } else {
        return this.jsonData?.resp?.message + '\n' + result
      }
    },
    // 获取请求参数
    handleObtainVerificationContent() {
      this.resultLaoding = true
      this.jsonData = {}
      let params = {
        message: this.textareaVal,
        ability_id: Number(this.$route.query.id)
      }
      obtainVerificationContent(params)
        .then((res) => {
          if (res.data.code === 200 || res.status === 200) {
            this.roughQuery = res.data.result.param_body
            const isEmpty = Object.entries(res.data.result.param_body).length === 0 // 判断是否为空
            if (!isEmpty) {
              this.$emit('e-autocodeTableData', res.data.result.param_body)
            } else {
              this.$emit('e-autocodeTableData', 'error')
            }
            this.handleOnCodeTest(res.data.result.param_body)
            this.handleQueryAbilityApi()
          }
          this.resultLaoding = false
        })
        .catch((err) => {
          this.$emit('e-autocodeTableData', 'error')
          this.$message.error(err)
          this.resultLaoding = false
        })
    },
    async handleOnCodeTest(data) {
      let params = {
        scheme_id: Number(this.$route.query.scheme_id),
        params: data,
        oper_type: 'ability'
      }
      let results = await OnCodeTest(params)
      if (results.data.code === 200 || results.status === 200) {
        this.jsonData = results.data.result
      }
    },
    // 将对象json序列化，失败时
    objToJsonStr(obj, dataType) {
      let jsonStr = ''
      if (['Object', 'Array', 'object', 'array'].includes(dataType)) {
        try {
          jsonStr = JSON.stringify(obj)
        } catch (error) {
          console.error('param_value json序列化失败，直接toString(): ' + error.message)
          jsonStr = obj.toString()
        }
      } else {
        jsonStr = obj
      }
      return jsonStr
    },
    // 根据请求参数匹配请求具体数据
    handleQueryAbilityApi() {
      queryAbilityApi({ ability_id: Number(this.$route.query.id) }).then((res) => {
        if (res.data.code === 200 || res.status === 200) {
          // 先过滤掉不匹配的请求参数，然后再遍历添加新值param_value
          this.detailQuery = res.data.result.req_body
            .filter((item) => this.roughQuery.hasOwnProperty(item.param_name))
            .map((item) => ({
              ...item,
              param_value: this.objToJsonStr(this.roughQuery[item.param_name], item.data_type)
            }))
        }
      })
    },
    // 根据粗略的请求参数匹配出新的详情请求参数
    handleRoughQueryToDetailQuery() {},
    handleTest() {
      this.handleObtainVerificationContent()
    },
    handleReset() {
      this.textareaVal = ''
      this.jsonData = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.business-validation {
  height: 100%;
  width: 100%;
  span {
    color: #333333;
    font-weight: 650;
    font-size: 13px;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  }
  .ask {
    padding: 20px;
    background: #ffffff;
    .el-textarea {
      margin: 10px 0 5px;
      :deep(.el-textarea__inner) {
        border-radius: 13px;
      }
    }
    .ask-buttons {
      display: flex;
      justify-content: flex-end;
    }
  }
  .answer {
    background: #ffffff;
    margin-top: 20px;
    padding: 20px;
    .answer-title {
      display: flex;
      justify-content: space-between;
    }
    .answer-content {
    }
    .answer-error {
      span {
        font-size: 14px;
        font-weight: 400;
        font-family: none;
      }
    }
  }
}
.el-popover__reference {
  font-size: 14px; /* 按钮字体大小 */
  background-color: #fff; /* 按钮背景色 */
  border: 1px solid #dcdfe6; /* 按钮边框 */
  color: #606266; /* 按钮文字颜色 */
  padding: 5px 10px; /* 按钮内边距 */
  border-radius: 4px; /* 按钮圆角 */
  cursor: pointer; /* 鼠标样式 */
}

.el-popover__reference:hover {
  background-color: #f5f7fa; /* 鼠标悬停时背景色 */
}

.el-table__body-wrapper {
  max-height: 300px; /* 表格最大高度 */
  overflow-y: auto; /* 表格内容超出高度时滚动 */
}

.el-table__row {
  height: 40px; /* 表格行高 */
}

.el-table__header th {
  background-color: #f5f7fa; /* 表头背景色 */
  font-weight: bold; /* 表头字体加粗 */
  color: #303133; /* 表头文字颜色 */
}

.el-table__body td {
  font-size: 14px; /* 单元格字体大小 */
  color: #606266; /* 单元格文字颜色 */
  border-bottom: 1px solid #dcdfe6; /* 单元格底部边框 */
}

.el-table__body td:first-child {
  font-weight: bold; /* 第一列字体加粗 */
  width: 50px; /* 第一列宽度 */
}

.el-table__body td:nth-child(2) {
  width: 170px; /* 第二列宽度 */
}

.el-table__body td:nth-child(3) {
  width: 90px; /* 第三列宽度 */
}

.el-table__body td:last-child {
  width: 50px; /* 最后一列宽度 */
}
</style>
