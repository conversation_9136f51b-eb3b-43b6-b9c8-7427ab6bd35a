<template>
  <div>
    <div class="contain">
      <div class="contain-ce">
        <div class="talk" v-if="isShow">
          <el-row v-if="!this.$route.query.mode_selection">
            <el-col class="col">
              <div class="talk-right" :style="{
         height: !isHideIframe ? 'calc(100vh - 88px)':'calc(100vh - 40px)'
        }" :class="fileList.length > 0 ? 'talk-right-pr' : ''">
                <div class="box-top">
                  <div class="avatar">
                    <img v-if="marketResult.scheme_ext_info?.abilityIcon"
                      :src="marketResult.scheme_ext_info?.abilityIcon" alt="" />
                    <img v-else src="@/assets/images/image/nl_1.png" alt="" />
                    <div class="name">{{ marketResult.ability_name }}</div>
                  </div>
                  <div class="build-talk">
                    <div class="talk"></div>
                    <div class="history"></div>
                  </div>
                </div>
                <div ref="scrollContainer" class="chart-content">
                  <div v-for="(el, index) in messaeList" :key="index" :class="el.role === 'user' ? 'boxrg' : 'boxlf'">
                    <div class="chart-box">
                      <div class="chart-main">
                        <div class="text">
                          <img v-if="el.role === 'user' && el.image_path" class="boxrg-filesrc" :src="el.image_path"
                            alt="" />
                          <div v-if="el.loading" ref="loading" class="qa-loading-spinner3"></div>
                          <div v-if="el.content && !el.loading" class="text-title">
                            <MyEditor id="MyEditorDuiqi" ref="MyEditorDuiqi" :md-content="el.content"></MyEditor>
                          </div>
                          <el-empty v-if="false" :image="require('@/assets/images/planGenerater/emptyParams.png')"
                            description="暂无参数或参数存在多个，无法使用会话模式，请使用其他模式"></el-empty>
                          <div v-if="
                            abilityValueList.length > 0 &&
                            el.role !== 'user' &&
                            index == messaeList.length - 1 &&
                            flowFinish
                          " class="toolp-label">
                            <el-button v-for="(item, index) in abilityValueList" :key="index" type="primary" plain
                              class="tab-item" @click="handleButtonClick(item)">{{ item }}</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="chart-bottom">
                  <div class="chart-icon">
                    <div class="icon-item">
                      <el-tooltip effect="dark" content="语音输入">
                        <div class="phoneStatus">
                          <div v-if="false">
                            <img src="@/assets/images/planGenerater/shuohua.png" />
                          </div>
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="icon-item uploadfile">
                      <el-upload ref="modelUpload" class="upload-demo" :file-list="fileList" :action="uploadUrl"
                        :show-file-list="true" :list-type="listType" :data="uploadParam" :limit="1"
                        accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG" :before-upload="modelBeforeUploadHandle"
                        :on-change="modelChangeHandle" :on-remove="handleRemove" :on-success="modelUploadSuccess">
                        <el-tooltip effect="dark" content="支持图片上传，.jpg,.jpeg,.png,.JPG,.JPEG,.PNG文件">
                          <img src="@/assets/images/planGenerater/upload-icon.png" />
                        </el-tooltip>
                      </el-upload>
                    </div>
                    <div></div>
                  </div>
                  <div class="custom-input">
                    <el-input ref="myInput" class="input-area" autofocus :disabled="!flowFinish"
                      :class="fileList.length > 0 ? 'custom-img' : 'custom-ipt'" type="textarea"
                      :autosize="{ minRows: 2, maxRows: 5 }" v-model="messagefile"
                      @keydown.enter.native.prevent="handleEnter($event)" placeholder="输入你的问题或指令"
                      :style="{ height: '50px' }">
                    </el-input>
                    <div class="suffix-icon">
                      <i @click="onTest()" class="el-icon-position"></i>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="task_model" :style="{
         height: !isHideIframe ? 'calc(100vh - 88px)':'calc(100vh - 40px)'
        }" v-if="isability">
          <div class="header">
            <img v-if="marketResult.scheme_ext_info?.abilityIcon" :src="marketResult.scheme_ext_info?.abilityIcon"
              alt="" />
            <img v-else src="@/assets/images/image/nl_1.png" alt="" />
            <div class="title">{{ marketResult.ability_name }}</div>
          </div>
          <div class="main">
            <div class="req_container">
              <div class="title" v-if="this.schemeInfo.agent_scene_code === 'operational_optimization_scene'">
                <div class="title_title">智能识别</div>
                <MyBusinessValidation @e-autocodeTableData="handleAutocodeTableData"></MyBusinessValidation>
              </div>
              <div class="s_box">
                <div class="param">
                  <div class="param_title">输入信息</div>
                  <div v-for="(item, index) in tableData" :key="index">
                    <div class="label">
                      {{ index + 1 }}、{{ item.param_name }}
                      <el-tooltip class="item" effect="dark" placement="top-start">
                        <div slot="content">{{ item.param_desc }}</div>
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </div>
                    <div style="flex: 1">
                      <el-select v-if="item.param_name === 'deviceId'" v-model="item.param_value" placeholder="请选择"
                        filterable remote :remote-method="filterEquip" :loading="loading2" style="width: 100%"
                        @change="(val) => changeEquip(val, index)">
                        <el-option v-for="fitem in equipList" :key="fitem.deviceId + ''" :label="fitem.deviceName"
                          :value="fitem.deviceId + ''"></el-option>
                      </el-select>
                      <div v-else-if="item.data_type === 'File'">
                        <div class="fileBox">
                          <shardUploaderTool :accept="''" upload-tip="小于20M" :limit="20" :multiple="false" :cover="true"
                            :index-ref="index" :shard-limit="1" @onFilesChange="uploadScriptCallback"
                            @onFilesStatus="uploadShardStatusCb" @on-remove="removeFile" />
                        </div>
                        <div v-if="previewFlag" class="image">
                          <img v-if="curImageUrl" :src="curImageUrl" />
                          <span v-else>暂不支持预览</span>
                        </div>
                      </div>
                      <el-select v-else-if="item.data_type === 'bool' || item.data_type === 'Boolean'"
                        v-model="item.param_value" placeholder="请选择">
                        <el-option key="1" value="true">true</el-option>
                        <el-option key="2" value="false">false</el-option>
                      </el-select>
                      <el-date-picker v-else-if="item.data_type === 'dateTime'" v-model="item.param_value"
                        :picker-options="pickerOptions1" style="width: 100%" type="datetime" popper-class="mytest"
                        placeholder="选择日期时间">
                      </el-date-picker>
                      <el-input v-else v-model="item.param_value" />
                    </div>
                  </div>
                </div>
                <div class="foot">
                  <el-button class="check-button" type="primary" @click="handleTest()"
                    :disabled="completeLoading">开始</el-button>
                </div>
              </div>
            </div>
            <div class="result">
              <div class="title">
                输出结果
                <span v-if="Object.keys(jsonData).length && jsonData.success" class="reTitle">
                  <i style="color: green" class="el-icon-success"></i>{{ jsonData.message }}
                </span>
                <span v-if="Object.keys(jsonData).length && !jsonData.success" class="reTitle">
                  <i style="color: red" class="el-icon-error"></i>{{ jsonData.message }}
                </span>
              </div>
              <div class="content">
                <div v-if="testloading" class="qa-loading-spinner3"></div>
                <div v-if="Object.keys(jsonData).length" class="qsResult" :loading="testloading">
                  <div class="reContent">
                    <MyEditorPreview id="MyJsonEditorResult" ref="MyJsonEditorResult" :md-content="jsonData.result">
                    </MyEditorPreview>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="guolu" :style="{
         height: !isHideIframe ? 'calc(100vh - 120px)':'calc(100vh - 72px)'
        }" v-if="isGuoluShow">
          <Guolu :executeData=executeData @generalExeApi="handleGeneralExeApi" />
        </div>
      </div>
      <div class="contain-rg"> </div>
      <el-dialog :visible.sync="visible" width="40%" close-on-click-modal :before-close="handleClose">
        <div class="dialog-cn">
          <div class="main-lf">
            <div class="main-lf-it">公开分享</div>
          </div>
          <div class="main-lf-rg">
            <div class="switch">
              <div class="is-switch">
                <div class="text">开启</div>
                <el-switch v-model="shareCode" @change="toggleSwitch" active-color="#13ce66">
                </el-switch>
              </div>
            </div>
            <div v-if="shareCode" class="copy-input">
              <el-input style="width: 80%; margin-right: 10px" id="textToCopy" v-model="copyLink"
                placeholder=""></el-input>
              <el-button type="primary" @click="toCopy">复制</el-button>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { v4 as uuidv4 } from 'uuid';
import MyEditor from '../mdEditor.vue'
import { createUuidAndSnowflake } from '@/utils/util.js';
import {
  getMarketAppInfo,
  queryFeedbackList,
  queryAbilityApi,
  getCurrentUserInfo,
  getFeedback,
  setShareCode,
  querySchemeDetailById
} from '@/api/planGenerateApi.js'
import axios from 'axios'
import TagPopover from '@/components/tagPopover/index.vue'
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue'
import dayjs from 'dayjs'
import MyEditorPreview from '@/views/planGenerate/mdEditorPreview.vue'
import selectItem from '@/views/planGenerate/selectItem.vue'
import MyBusinessValidation from './MyBusinessValidation.vue'
import Guolu from './Guolu.vue'
const cancelToken = axios.CancelToken
let source = cancelToken.source()
export default {
  name: 'abilityTalk',
  components: {
    MyEditorPreview,
    shardUploaderTool,
    TagPopover,
    selectItem,
    MyEditor,
    MyBusinessValidation,
    Guolu,
  },
  data() {
    return {
      executeData: {},
      schemeId: -1,
      schemeInfo: {},
      abilityId: '',
      rolePermiss: false,
      screeUuid: '',
      screeFull: '',
      shareCode: true,
      copyLink: '' /*  */,
      visible: false,
      pageParams: {
        pageNum: 1,
        pageSize: 12,
        name: '',
        scene_type: '',
        total: 0
      },
      flowFinish: true,
      isShow: false,
      isability: false,
      isGuoluShow: false,
      uuid: '',
      image_path: '',
      onShow: false,
      userPicture: [],
      listType: 'picture',
      previewFlag: false,
      curImageUrl: '',
      toMessage: { content: '', image_key: '', image_path: '' },
      remarkList: [],
      comment: '',
      tag: [],
      isFilterFlag: true,
      completeLoading: false,
      testloading: false,
      curKey: 'input_dict',
      resultTableData: [],
      messaeListChat: [],
      uploadUrl: '',
      uploadParam: {},
      messaeList: [],
      dialogMessageList: [],
      marketResult: {},
      abilityValueList: [],
      messagefile: '',
      isDragging: false,
      fileList: [],
      tableData: [],
      filesList: [],
      jsonData: {}
    }
  },
  async created() {
    // this.$store.commit('planGenerate/setHideIframe', false);
    window.localStorage.removeItem('ShareCode')
    this.uuid = createUuidAndSnowflake();
    await this.getMarketAppInfo()
    await this.getBindList();
    await this.getCodeParams()
    //     const myTempTable = `
    // | Name  | Age | City      |
    // |-------|-----|-----------|
    // | Alice | 25  | New York  |
    // | Bob   | 30  | London    |
    // | Carol | 28  | Paris     |
    // `
    //     this.messaeList.push({
    //       content: myTempTable,
    //       role: 'assistant',
    //       loading: false,
    //       image_path: '',
    //       onShow: this.onShow
    //     })
  },
  computed: {
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },
  },
  watch: {
    messaeList: {
      handler(newVal) {
        console.log('messaeList 00', newVal)
        console.trace('Trace the call stack here', newVal)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    }
  },
  methods: {
    async handleGeneralExeApi({ device_group_id, detection_time, resolve }) {
      console.log('handleGeneralExeApi 异步操作完成 1111111111111111111');

      // 父组件中的异步操作
      await this.handleTest({ device_group_id, detection_time });
      // 在异步操作完成后，调用 resolve 回调通知子组件
      resolve();
      
    },
    handleAutocodeTableData(val) {
      if (val === 'error') {
        this.$message.warning('未提取到参数');
        return;
      }
      this.tableData.forEach((item) => {
        console.log('item 00', item);
        if (val.hasOwnProperty(item.param_name)) {
          if (val[item.param_name] instanceof Array || val[item.param_name] instanceof Object) {
            item.param_value = JSON.stringify(val[item.param_name]);
          } else {
            item.param_value = val[item.param_name];
          }
        }
        this.$message.success('提取参数完成');
      });
    },
    toUnicode(str) {
      return str
        .split('')
        .map(function (char) {
          let unicode = char.charCodeAt(0).toString(16) // 获取字符的 Unicode 编码，并转换为16进制
          return '\\u' + ('0000' + unicode).slice(-4) // 格式化为 \uXXXX 的格式
        })
        .join('')
    },
    fromUnicode(str) {
      return str.replace(/\\u[0-9a-fA-F]{4}/g, function (match) {
        return String.fromCharCode(parseInt(match.replace('\\u', ''), 16))
      })
    },
    handleClose(done) {
      done()
    },
    openDialog() {
      this.visible = true;
      let hrefPath = window.location.href?.split('?');
      hrefPath.pop();
      this.screeFull =
        hrefPath.join('?') + 'Full' + '?' + `workspaceId=${+this.$route.query?.workspaceId || ''}`;
      if (window.localStorage.getItem('ShareCode')) {
        this.copyLink =
          this.screeFull + `&shareCode=${window.localStorage.getItem('ShareCode') || ''}`;
      } else if (window.localStorage.getItem('ShareCode') === '') {
        this.shareCode = false;
        window.localStorage.removeItem('ShareCode');
      } else {
        console.log(
          window.localStorage.getItem('ShareCode') === '',
          window.localStorage.getItem('ShareCode') === undefined,
          1111111111
        );
        this.copyLink = this.screeFull + `&shareCode=${this.marketResult?.share_code || ''}`;
      }
    },
    // 获取URL参数
    getQueryParams(url) {
      const queryParams = {}
      const queryString = url.split('?')[1]
      if (queryString) {
        const pairs = queryString.split('&')
        for (const pair of pairs) {
          const [key, value] = pair.split('=')
          queryParams[key] = decodeURIComponent(value || '')
        }
      }
      return queryParams
    },
    //复制
    async toCopy() {
      // 获取输入框元素
      const textField = document.getElementById('textToCopy')
      if (this.shareCode) {
        // 复制文本到剪贴板
        try {
          await navigator.clipboard.writeText(this.copyLink)
          this.visible = false;
        } catch (err) {
          console.error('复制失败: ', err)
        }
      } else {
        textField.addEventListener('copy', function (e) {
          e.preventDefault()
        })
      }
    },
    toggleSwitch() {
      this.screeUuid = uuidv4()
      this.getShareCode({
        ability_id: +this.$route.query?.id || '',
        share_code: this.shareCode ? this.screeUuid : ''
      });
    },
    getShareCode(params) {
      setShareCode(params).then((res) => {
        if (res.data.code === 200 && res.status === 200) {
          this.copyLink = this.screeFull + `&shareCode=${params.share_code || ''}`
        }
      });
    },
    // 分页
    handleCurrentChange(val) {
      this.pageParams.pageNum = val
      this.getBindList(val);
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.log(this.messaeList, this.fileList);
    },
    scrollToBottom() {
      this.$nextTick(() => {
        // 使用this.$refs访问DOM元素
        const container = this.$refs.scrollContainer
        // 滚动到底部
        container.scrollTop = container.scrollHeight
      });
    },
    // 反馈列表
    async getBindList(val) {
      await queryFeedbackList({
        ability_id: +this.$route.query?.id || '',
        page: this.pageParams.pageNum || 1,
        page_size: this.pageParams.pageSize || 999
      })
        .then((res) => {
          if (res?.data?.code === 200) {
            this.comment = '';
            this.remarkList = res.data?.result?.data;
            this.pageParams.total = res.data?.result?.total
            // this.remarkList = this.remarkList.slice(0,this.pageParams.pageSize)
          } else {
          }
        })
        .catch(() => { });
    },
    handleButtonClick(item) {
      console.log('hello message from where 00', item)
      this.messaeList.push({
        content: item,
        role: 'user',
        loading: false,
        image_path: '',
        onShow: this.onShow
      })
      this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest)
      this.handleTest1(JSON.stringify(this.messaeListChat))
    },
    handleEnter(event) {
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault()
          this.$nextTick(() => {
            if (
              (this.messagefile && this.fileList.length > 0 && this.flowFinish) ||
              (this.messagefile && this.flowFinish)
            ) {
              this.onTest()
            }
            if (this.comment) {
              this.onRemark();
            }
          })
        }
      }
      // this.onTest();
    },
    uploadScriptCallback(fileData, fileList, index) {
      console.log('文件上传成功过', fileData, index)
      const fileType = this.$fileUtil.getFileSuffix(fileData.name)
      if (fileData.path) {
        this.previewFlag = true
      } else {
        this.previewFlag = false
      }
      if (
        fileType.toLowerCase() === 'jpg' ||
        fileType.toLowerCase() === 'png' ||
        fileType.toLowerCase() === 'jpeg'
      ) {
        this.curImageUrl = fileData.path
      } else {
        this.curImageUrl = ''
      }
      this.filesList[index] = fileData.path
      console.log(this.filesList)
    },
    uploadShardStatusCb(status) { },
    removeFile(fileId, filelist, index) {
      console.log('删除的行', index);
      this.filesList[index] = '';
    },
    async onRemark() {
      if (this.comment === '') return
      const userId = getCurrentUserInfo().userId;
      await getFeedback({
        feedback_content: this.comment,
        ability_version_id: this.marketResult?.current_version_id,
        workspace_id: +this.$route.query?.workspaceId || '',
        ability_id: +this.$route.query?.id || '',
        user_id: userId
      })
        .then((res) => {
          this.comment = '';
        })
        .catch((err) => {
          this.comment = '';
        })
        .finally(() => {
          this.comment = '';
          this.getBindList();
        });
    },
    async handleTest1(datas) {
      let dialContect = [];
      if (this.fileList.length > 0 && this.messagefile) {
        dialContect.push({
          type: 'text',
          text: this.messagefile
        });
        dialContect.push({
          type: 'image_url',
          image_url: {
            url: this.toMessage.image_path
          }
        });
        this.image_path = dialContect[1].image_url?.url;
      } else {
        dialContect.push({
          type: 'text',
          text: this.messagefile
        });
        this.image_path = '';
      }
      this.onShow = dialContect.some((item) => item.type === 'image_url');
      this.completeLoading = true
      this.flowFinish = false;
      this.messagefile = ''
      this.fileList = [];
      this.onShow = false;
      console.log('hello message from where 01')
      this.messaeList.push({
        role: 'assistant',
        content: '',
        loading: true,
        image_path: '',
        onShow: false
      });
      const lastWithMessage = this.messaeList[this.messaeList.length - 1];
      let url = '';
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      let params = {}
      url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute'
        : process.env.VUE_APP_PLAN_API + '/code/execute';
      params = {
        scheme_id: this.marketResult.scheme_id,
        params: {
          session_id: this.uuid,
          ability_id: 'chatbot',
          userMessage: {
            role: 'user',
            content: dialContect
          }
        },
        oper_type: 'online_ability'
        // 'oper_type': 'ability'
      };
      this.$axios
        .post(
          url,
          {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: params || {}
          },
          {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/octet-stream'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target
              const { responseText } = xhr
              let newStr;
              // if(responseText?.includes('code')){
              //   // 处理返回体数据
              //   try {
              //     console.log(JSON.parse(responseText).result?.resp?.result,'没进来吗')
              //     const val = JSON.parse(responseText).result?.resp?.result
              //     try {
              //       const resultData=  JSON.parse(val)
              //       newStr=resultData.result
              //     } catch (error) {
              //       newStr= val
              //     }
              //   } catch (error) {
              //     const val =  responseText?.result?.resp?.result
              //     const resultData=  val
              //     // console.log('流式数据---',val,responseText?.result?.resp?.result)
              //   //  newStr = responseText
              //     newStr=resultData.result
              //   }

              // }else{

              newStr = responseText.replace(/\n{2,}/g, '__NEWLINE__');

              // 将单个换行符替换为空格
              newStr = newStr.replace(/\n/g, '');

              // 将特殊标记替换回单个换行符
              newStr = newStr.replace(/__NEWLINE__/g, '\n');

              this.$nextTick(() => {
                lastWithMessage.loading = false;
                this.$nextTick(() => {
                  // this.$refs.loading.style.display = 'none';
                });
                console.log('----流----', newStr);
                lastWithMessage.content = newStr;
              });
            },
            onError: function (error) {
              // 处理流错误
              this.flowFinish = true;
              lastWithMessage.loading = false;
              this.$nextTick(() => {
               this.scrollToBottom();
                // this.$refs.loading.style.display = 'none';
              });
            }
          }
        )
        .then(async (res) => {
          // 关闭数据流
          this.flowFinish = true;
          lastWithMessage.loading = false;
          this.$nextTick(() => {
           this.scrollToBottom();
            console.log('this.$refs.loading 01', this.$refs.loading)
            if (this.$refs.loading && this.$refs.loading.style) {
              // this.$refs.loading.style.display = 'none'
            }
          })
          this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest)
          this.completeLoading = false
        })

        .catch(() => {
          console.log('catch');
          this.flowFinish = true;
          this.completeLoading = false
          lastWithMessage.loading = false;
          this.$nextTick(() => {
           this.scrollToBottom();
            console.log('this.$refs.loading 02', this.$refs.loading)
            if (this.$refs.loading && this.$refs.loading.style) {
              // this.$refs.loading.style.display = 'none'
            }
          });
        })
    },
    onTest() {
      console.log(this.fileList, '回车在这里', this.messaeList, this.onShow, this.toMessage);
      // 将消息发送到服务器
      console.log('hello message from where 02', this.messagefile)
      this.messaeList.push({
        content: this.messagefile,
        role: 'user',
        loading: false,
        image_path: this.fileList.length ? this.toMessage.image_path : '',
        onShow: this.onShow
      })
      this.messaeListChat = this.messaeList.map(({ loading, ...rest }) => rest)
      this.handleTest1(JSON.stringify(this.messaeListChat));
      this.userPicture = JSON.parse(JSON.stringify(this.fileList));
    },
    async getFileSign(file) {
      try {
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        })
        if (res.data.status === 200) {
          this.toMessage.image_key = res.data.data.key
          // 根据签名服务的返回值拼接文件上传url
          // const bucket = res.data.data.bucket
          // const zone = res.data.data.bucketZone
          // const ossUrl = res.data.data.ossUrl
          // const urlArr = ossUrl.split('//')
          // this.uploadUrl = `${urlArr[0]}//${bucket}.${zone}.${urlArr[1]}`
          this.uploadUrl = res.data.data.obsUrl
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    async modelBeforeUploadHandle(file) {
      const isLt2M = file.size / 1024 / 1024 / 1024 < 1
      if (!isLt2M) {
        this.$message.error('大小不能超过1GB!')
        return false
      }
      await this.getFileSign(file)

      return isLt2M
    },
    modelHandleProgress(event, file, fileList) {
      this.showProcess = true
      this.processLength = Math.floor(event.percent)
    },
    modelChangeHandle(file, fileList) {
      this.fileList = fileList
      this.upLoadFileFlag = true
      if (fileList.length > 0) {
        this.onShow = true;
      } else {
        this.onShow = false;
      }
      // this.$refs.myInput.focus();

      // if (this.handleBeforeUpload) {
      //   this.handleBeforeUpload(file)
      //   this.fileList=[file]
      //   this.upLoadFileFlag = true
      // }
    },
    modelUploadSuccess(response, file) {
      this.showProcess = false
      this.processLength = 0

      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        this.$message.success(`模型文件上传状态为:${this.uploadStatus}`)
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key

        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.toMessage.image_path = res.data.data.path
              // this.image_path = res.data.data.path
              const fileId = res.data.data.fileId
              // if (this.addOrUpdateFlag) {
              //   this.modelAdd(fileId, fileName + fileType);
              // } else {
              //   this.modelUpdate(fileId, fileName + fileType);
              // }
            }
          })
      } else {
        this.$message.warning(`模型上传状态为:${this.uploadStatus}`)
      }

      this.modelDialogVisible = false
    },
    getMarketAppInfo() {
      getMarketAppInfo({
        ability_id: +this.$route.query?.id || ''
      }).then((res) => {
        if (res?.data?.code === 200) {
          console.log('详情==========', res.data.result);
          this.marketResult = res.data.result;
          this.shareCode = this.marketResult?.share_code ? true : false;
          this.schemeId = this.marketResult?.scheme_id;
          querySchemeDetailById({ scheme_id: Number(this.schemeId) })
            .then((res) => {
              console.log('res 00', res, this.schemeId);
              if (res.status === 200 && res.data.code === 200) {
                this.schemeInfo = res.data.result;
                console.log('agent_scene_code', this.schemeInfo);
                console.log('agent_scene_code', this.schemeInfo.agent_scene_code);
              }
            })
            .catch((_err) => {
              console.error(_err);
            });
          console.log('this.schemeId', this.schemeId);
          this.rolePermiss =
            getCurrentUserInfo().userId === this.marketResult.user_id ? true : false;
          console.log('权限控制分享---', this.rolePermiss, getCurrentUserInfo().userId);
          if (this.marketResult.ext_info?.mode_selection == 'dialogue') {
            this.isShow = true;
          } else if (this.marketResult.ext_info?.mode_selection == 'boiler') {
            this.isGuoluShow = true
          } else {
            this.isability = true;
          }
          this.abilityValueList =
            this.marketResult?.scheme_ext_info?.abilityIntents?.split('\n') || []

          //           const myTempTable = `
          // | Name  | Age | City      |
          // |-------|-----|-----------|
          // | Alice | 25  | New York  |
          // | Bob   | 30  | London    |
          // | Carol | 28  | Paris     |
          // `
          this.messaeList.push({
            content: this.marketResult?.scheme_ext_info?.opening_statement,
            role: 'assistant'
          });
          this.flowFinish = true;
        }
      })
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    getCodeParams() {
      queryAbilityApi({ ability_id: Number(this.$route.query.id) }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('测试参数', res.data.result)
          this.newTableData = res?.data?.result?.req_body || []
        } else {
          this.newTableData = []
        }
        const files = []
        const temp = []
        if (this.schemeInfo.agent_scene_code === 'device_ops_assistant_scene') {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name === 'detectionTime') {
              item.param_value = dayjs()
                .subtract(2, 'day')
                .startOf('day')
                .format('YYYY-MM-DD HH:mm:ss')
              temp.push({ ...item })
            } else {
              temp.push({ ...item, param_value: '' })
            }
            files.push('')
          })
        } else {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name !== 'detectionTime' && item.param_name !== 'deviceId') {
              temp.push({ ...item, param_value: '' })
            }
            files.push('')
          })
        }

        this.filesList = files;
        this.tableData = temp;
        console.log('this.tableData 00', this.tableData);
      });
    },
    async handleTest(val) {
      // console.log('----流----111111111111111')
      this.jsonData = {};
      this.testloading = true
      this.completeLoading = true
      let url = ''
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {}
      let params = {}
      const datas = {}
      let deviceId = ''
      let detectionTime = ''
      this.tableData.forEach((item, index) => {
        if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
          if (item.data_type !== 'File') {
            if (item.data_type == 'Boolean' || item.data_type == 'bool') {
              const temp = item.param_value === 'true'
              datas[item.param_name] = temp
            } else if (item.data_type == 'Array' || item.data_type == 'Object') {
              try {
                const temp = JSON.parse(item.param_value)
                datas[item.param_name] = temp
              } catch (error) {
                const temp = item.param_value
                datas[item.param_name] = temp
              }
            } else if (item.data_type == 'String' || item.data_type == 'string') {
              datas[item.param_name] = item.param_value
            } else if (item.data_type == 'Number' || item.data_type === 'decimal') {
              const temp = Number(item.param_value)
              if (isNaN(temp)) {
                datas[item.param_name] = item.param_value
              } else {
                datas[item.param_name] = temp
              }
            } else {
              if (item.param_value !== '') {
                datas[item.param_name] = item.param_value
              } else {
                console.log('不传')
                // datas[item.name] = item.value
              }
            }
          } else {
            datas[item.param_name] = this.filesList[index]
          }
        } else {
          if (item.param_name === 'deviceId') {
            deviceId = item.param_value
          }
          if (item.param_name === 'detectionTime') {
            detectionTime = item.param_value
          }
        }
      })
      if (this.schemeInfo.agent_scene_code === 'device_ops_assistant_scene') {
        url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/executev1'
          : process.env.VUE_APP_PLAN_API + '/code/executev1'

        params = {
          scheme_id: this.marketResult.scheme_id,
          deviceId: deviceId,
          detectionTime: dayjs(detectionTime).format('YYYY-MM-DD HH:mm:ss'),
          params: val || datas,
          oper_type: 'ability'
        }
      } else {
        url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute'
          : process.env.VUE_APP_PLAN_API + '/code/execute'
        params = {
          scheme_id: this.marketResult.scheme_id,
          params: val || datas,
          oper_type: 'online_ability'
          // oper_type: 'ability'
        }
      }
      return this.$axios
        .post(
          url,
          {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: params || {}
          },
          {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/octet-stream'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              this.testloading = false
              const xhr = event.target
              // console.log('----流----', xhr)
              const { responseText } = xhr
              // const newStr = responseText.replace(/[\r\n]/g, '')
              const newStr = responseText.replace(/[\r\n]/g, '')
              console.log('----流----', newStr)
              this.$nextTick(() => {
                this.jsonData = {
                  message: '执行成功',
                  result: newStr,
                  success: true
                }
              })
            },
            onError: function (error) {
              // 处理流错误
              console.error(error)
              this.testloading = false
            }
          }
        )
        .then(async (res) => {
          // 关闭数据流
          console.log('数据流', res)
          this.testloading = false
          this.completeLoading = false
          if (res.status === 200 && res.data.code === 200) {
            console.log('测试结果', res.data.result)
            this.executeData = res.data.result
            if (res.data.result) {
              this.jsonData = res.data.result?.resp || {
                message: '执行成功',
                result: '',
                success: true
              }
            } else {
              this.jsonData = {
                message: res.data.msg || '执行失败',
                result: '',
                success: false
              }
            }
          } else {
            if (res.status === 200) {
              this.$nextTick(() => {
                const newStr = res.data.replace(/[\r\n]/g, '')
                // console.log('----流----', res.data, newStr)
                this.jsonData = {
                  message: '执行成功',
                  result: newStr,
                  success: true
                }
              })
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          }
        })
        .catch(() => {
          this.testloading = false
          this.completeLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.contain {
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  justify-content: space-between;
  padding-left: 20px;
  box-shadow: 0px 1px 0px 0px #ebecf0;
  background-color: #f3f6ff;
  overflow: auto;

  ::v-deep .el-dialog__header {
    padding: 0px;
    padding-bottom: 0px;
  }

  .contain-ce {
    background-color: #f3f6ff;
  }

  .dialog-cn {
    display: flex;

    .main-lf {
      padding-right: 16px;
      border-right: 1px solid #dcdde0;
    }

    .main-lf-rg {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;

      .switch {
        margin-bottom: 24px;

        .is-switch {
          display: flex;
        }
      }

      margin-left: 30px;

      .copy-input {
        display: flex;
      }
    }
  }

  &-lf {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 88px);
    width: 354px;
    flex-shrink: 0;
    /* 防止左侧被挤压 */
    border-radius: 4px;
    margin-right: 10px;
    background: #f6f7fb;

    .info {
      position: relative;
      border: 1px solid #dcdde0;
      border-radius: 5px;
      overflow: hidden;

      .info-main {
        // height: 216px;
        padding: 16px 24px;
        background: #ffffff;

        .info-top {
          margin-bottom: 8px;
          font-weight: 500;
          font-size: 18px;
          color: #323233;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }

        .info-text {
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          margin-bottom: 12px;
          font-weight: 400;
          font-size: 14px;
          color: #646566;
          line-height: 22px;
          text-align: justify;
          font-style: normal;
        }

        .info-tag {
          margin-bottom: 8px;
        }

        .bottom {
          display: flex;
          justify-content: space-between;
          font-weight: 400;
          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: justify;
          font-style: normal;

          .bottom-lf {
            .creater {
              margin-bottom: 2px;
            }

            .create-time {}
          }

          .bottom-rg {
            display: flex;
            align-items: flex-end;

            i {
              height: 20px;
              line-height: 20px;
              margin-right: 4px;
            }

            .starnum {
              margin-right: 8px;
            }
          }
        }
      }

      .searchIcon {
        position: absolute;
        left: 50%;
        bottom: -10px;
        transform: translateX(-50%);

        .searchIconTaggle {
          width: 40px;
          height: 20px;
          background: #d9e6f8;
          border-radius: 2px;
          position: relative;
          border: 1px solid #f2f3f5;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #4068d4;
          cursor: pointer;

          &:hover {
            background: #a1bbef;
          }
        }
      }
    }

    .info-dowm {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: calc(100vh - 318px);
      overflow: hidden;
      margin-top: 14px;
      background: #ffffff;
      padding: 16px 14px;
      border: 1px solid #dcdde0;
      border-radius: 5px;
      overflow: hidden;

      .heard {
        font-weight: 400;
        margin-bottom: 14px;
        font-size: 14px;
        color: #646566;
        line-height: 20px;
        text-align: justify;
        font-style: normal;

        .heard-titl {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          height: 20px;
          font-weight: 500;
          font-size: 14px;
          color: #323233;
          line-height: 20px;
          font-style: normal;

          .line {
            width: 2px;
            height: 12px;
            background: #323233;
            margin-left: 0px;
          }
        }
      }

      .search {
        margin-bottom: 12px;
      }

      .info-card:last-of-type {
        margin-bottom: 0px;
      }

      .info-card-container {
        flex: 1;
        overflow-y: auto;
        //  max-height: 100px; /* 你可以根据需要调整这个值 */
      }

      .info-card {
        .box-card {
          display: flex;
          padding: 16px 14px;
          margin-bottom: 8px;
          height: 136px;
          border-radius: 4px;
          border: 1px solid #dcdde0;

          .box-center {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;

            .box-bm {
              display: flex;
              align-items: center;

              .info-avatar {
                margin-right: 4px;
              }
            }

            .card-cotent {
              // 省略号
              display: -webkit-box;
              overflow: hidden;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              word-break: break-all; /* 强制换行 */
              font-weight: 400;
              font-size: 14px;
              color: #646566;
              line-height: 20px;
              text-align: justify;
              font-style: normal;
            }

            .info-avatar {
              img {
                width: 32px;
                height: 32px;
                border-radius: 50%;
              }
            }
          }
        }
      }
    }
  }

  &-ce {
    flex: 1;
  }

  &-rg {
    background: #ffffff;
  }
}

::v-deep .el-button+.el-button {
  margin-left: 0px;
  display: block;
}

::v-deep button.el-button.tab-item.el-button--primary.is-plain {
  border-radius: 6px;
}

::v-deep .el-input__inner {
  border-radius: 6px;
}

::v-deep .el-input__suffix {
  display: flex;
  align-items: center;
}

.el-row {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.talk {
  overflow-y: hidden;
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;

  &-left {
    height: calc(100vh - 88px);
    background: #ffffff;
    border: 1px solid transparent;
  }

  .talk-end {
    height: calc(100vh - 88px);
    background: #ffffff;
  }

  &-right-pr {
    padding-bottom: 70px !important;
  }

  &-right {
    display: flex;
    flex-direction: column;
    background: #f3f6ff;
    // height: calc(100vh - 88px);
    /* 设置为可视区域高度 */
    overflow: hidden;
    padding: 28px 30px 36px 30px;

    // padding-bottom: 100px;
    .custom-img {
      ::v-deep textarea.el-textarea__inner {
        height: 90px !important;
        padding-top: 56px !important;
      }
    }

    .custom-ipt {
      ::v-deep textarea.el-textarea__inner {
        // height: 90px !important;
        // padding-top: 56px !important;
      }
    }

    ::v-deep img.el-tooltip {
      // position: absolute;
      // left: -28px;
      // bottom: -55px;
    }

    ::v-deep .qa-loading-spinner3 {
      width: 42px;
      height: 36px;
      background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
      background-size: 100% 100%;
      position: relative;
      border-radius: 6px;
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .avatar {
        display: flex;
        align-items: center;
        justify-content: space-between;

        img {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 8px;
        }

        .name {
          font-weight: 500;
          font-size: 16px;
          color: #323233;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .build-talk {
        display: flex;
        align-items: center;

        .talk {}

        .history {}
      }
    }

    .chart-bottom {
      position: relative;
      display: flex;
      // flex-direction: column;
      align-items: flex-end;

      ::v-deep textarea.el-textarea__inner {
        border-radius: 6px;
      }

      ::v-deep ul.el-upload-list.el-upload-list--text {
        width: 100%;
        position: absolute;
        // bottom: 0px;
        left: 0px;
        z-index: 1;
      }

      ::v-deep ul.el-upload-list.el-upload-list--picture li {
        width: 24px;
        transform: scale(0.5);
        position: absolute;
        margin-top: 0px;
        left: 22px;
        top: -16px;
        z-index: 1;
        object-fit: cover;
        // height: 45px;
        border-radius: 4px;
      }

      ::v-deep textarea.el-textarea__inner {
        resize: none;
      }

      ::v-deep .suffix-icon i.el-icon-position {
        // position: absolute;
        // bottom: -74px;
        // left: -18px;
      }

      .custom-input {
        width: 100%;
        position: relative;
        display: inline-block;

        .suffix-icon {
          position: absolute;
          right: 10px;
          top: 50%;
          // transform: translateY(-50%);
          cursor: pointer;
        }
      }

      .chart-icon {
        display: flex;
        justify-content: column;
        align-items: flex-start;
        /* 添加这一行，使 chart-icon 靠左 */
        // width: 100%;
        margin: 0 auto;

        /* 添加这一行 */
        .icon-item.uploadfile {
          //  width: 0px;
        }

        .icon-item {
          cursor: pointer;

          .upload-demo {
            margin-right: 6px;
          }

          img {
            width: 24px;
            height: 24px;
          }
        }
      }

      .custom-input {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        margin: 0 auto;

        /* 添加这一行 */
        .input-area {
          height: 50px;
          min-height: 50px;
        }
      }
    }

    .chart-content {
      flex: 1;
      /* 填充剩余空间 */
      overflow-y: auto;
      background: #f3f6ff;
      // display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;

      /* Firefox */
      &::-webkit-scrollbar {
        display: none;
      }

      .boxrg {
        flex: 1;

        ::v-deep .editor-show {
          overflow: initial !important;
        }

        .boxrg-filesrc {
          width: 208px;
          height: 123px;
        }

        .chart-main {
          display: flex;
          width: 100%;
          justify-content: flex-end;
        }
      }

      .boxlf {
        flex: 1;
        width: 100%;

        .chart-main {
          display: flex;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .chart-box {

        // min-width: 200px;
        // max-width: 450px;
        .chart-main {
          .text {
            max-width: calc(100% - 80px);
            flex: 0 0 auto;
            background: #ffffff;
            padding: 8px;
            text-align: left;
            margin-bottom: 8px;

            &-title {
              font-weight: 400;
              font-size: 14px;
              color: #323233;
              line-height: 20px;
              text-align: left;
              font-style: normal;
            }
          }

          .toolp-label {
            background-color: transparent;

            .tab-item {
              margin-bottom: 8px;
            }

            .tab-item:last-of-type {
              margin-bottom: 0px;
            }
          }
        }
      }
    }
  }
}


.guolu {
  box-sizing: border-box;
  // height: calc(100vh - 120px);
}

.fileBox {
  :deep(.el-upload-list__item:first-child) {
    margin-top: 3px;
  }
}

.image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  margin-top: 10px;
  color: rgb(150, 151, 153);

  img {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: auto;
  }
}

.ability-info {
  background-color: white;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88px);

  .ability-basic {
    padding: 10px;
    flex-basis: 30%;
    background-color: inherit;

    .title {
      display: block;
      font-weight: bold;
      font-size: 18px;
    }

    .ability-value {
      display: block;
      font-size: 14px;
    }
  }

  .ability-user-voice {
    background-color: inherit;
    flex-grow: 1;
    padding: 10px;

    .title {
      margin-left: 3px;
      font-size: 14px;
      font-weight: bold;
      border-left: 2px solid #000;
      padding-left: 5px;
      margin-bottom: 5px;
    }
  }
}

.task_model {
  // width: 60%;
  // width: 50%;
  background-color: #f3f6ff;
  border-radius: 5px;
  // margin: 10px auto;
  padding: 16px 0px 0px 0px;
  display: flex;
  flex-direction: column;
  // box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  min-width: 600px;
  // height: calc(100vh - 88px);

  .header {
    display: flex;
    justify-content: flex-start;

    img {
      width: 48px;
      height: 48px;
      border-radius: 24px;
    }

    .title {
      font-weight: bold;
      font-size: 16px;
      margin: auto 5px;
    }
  }

  .main {
    margin-top: 15px;
    flex-grow: 1;
    display: flex;
    gap: 10px;
    overflow: auto;
    background-color: #f3f6ff;

    .req_container {
      background-color: #f3f6ff;
      border-radius: 5px;
      overflow: hidden;
      height: 100%;
      width: 50%;
      display: flex;
      flex-direction: column;

      .title {
        background-color: #fff;
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        border: 1px solid #dcdde0;
        border-radius: 5px;
        margin-bottom: 10px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

        .title_title {
          font-weight: bold;
          font-size: 16px; // 增大字体大小
          color: #333; // 使用深色以突出标题
          padding: 10px; // 增加内边距
          margin-bottom: 20px; // 增加下边距
          border-bottom: 2px solid #ccc; // 添加下划线
        }
      }

      .s_box {
        padding: 10px;
        background-color: #fff;
        border: 1px solid #dcdde0;
        border-radius: 5px;
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        overflow-y: scroll;

        .param {
          flex-grow: 1;
          overflow-y: scroll;

          .param_title {
            font-weight: bold;
            font-size: 16px; // 增大字体大小
            color: #333; // 使用深色以突出标题
            padding: 10px; // 增加内边距
            margin-bottom: 20px; // 增加下边距
            border-bottom: 2px solid #ccc; // 添加下划线
          }

          div {
            margin-bottom: 5px;
          }

          .label {
            font-size: 14px;
            font-weight: bold;
          }
        }

        .foot {
          background-color: #fff;
          display: flex;
          justify-content: flex-end;
          align-items: flex-end;

          .check-button {
            font-weight: 500;

            /* 按钮文字粗细 */
            &:hover {
              background-color: #66b1ff;
              /* 按钮悬停背景色 */
              border-color: #66b1ff;
              /* 按钮悬停边框色 */
            }

            &:active {
              background-color: #3a8ee6;
              /* 按钮激活背景色 */
              border-color: #3a8ee6;
              /* 按钮激活边框色 */
            }
          }
        }
      }
    }

    .result {
      background-color: #fff;
      border: 1px solid #dcdde0;
      border-radius: 5px;
      height: 100%;
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        margin-bottom: 20px; // 增加下边距
        border-bottom: 2px solid #ccc; // 添加下划线
      }

      .content {
        word-wrap: break-word;
        margin-top: 10px;
        overflow-y: scroll;
      }
    }
  }
}

.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

:deep(.el-avatar) {
  background-color: #409eff;
  font-size: 16px;
  font-weight: bold;
}

.disabled {
  opacity: 0.4;
  background-color: #f2f3f5 !important;
  color: #4068d4;
  cursor: not-allowed !important;
}
</style>
