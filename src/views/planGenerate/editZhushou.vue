<template>
  <div class="editZhushou">
    <div class="headerBox">
      <div class="headerTitle">
        {{ $route.query.id ? '编辑' : '新建' }}
      </div>
      <el-button
        class="button-last"
        type="info"
        @click="() => $router.push({ path: '/planGenerate/index', query: { ...$route.query } })"
        >返回</el-button
      >
    </div>
    <div v-loading="loading" class="editBox">
      <el-form
        ref="form"
        label-position="right"
        label-width="99px"
        :model="formData"
        :rules="rules"
      >
        <el-form-item label="名称:" prop="name">
          <el-input
            v-model.trim="formData.name"
            placeholder="请输入"
            show-word-limit
            maxlength="50"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="使用场景:" prop="agent_scene">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="display: flex; flex: 1">
              <el-select
                v-model="formData.agent_scene_code"
                filterable
                placeholder="请选择使用场景"
                style="padding: 0 5px"
                :disabled="$route.query.id ? true : false"
                @change="sceneChange"
              >
                <el-option
                  v-for="item in dictList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                ref="sceneSelect"
                v-model="formData.agent_scene"
                :disabled="$route.query.id ? true : false"
                clearable
                filterable
                placeholder="请选择使用场景"
                style="width: 100%"
                @change="agentSceneChange"
              >
                <el-option
                  v-for="item in senceOptions"
                  :key="item.versionId"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-for="(domain, index) in formData.domains"
          :key="domain.key"
          :label="domain.label"
          :prop="'domains.' + index + '.value'"
          :rules="{
            required: true,
            message: `${domain.label}不能为空`,
            trigger: 'blur'
          }"
        >
          <el-input v-if="domain.type === 'input'" v-model.trim="domain.value" />
          <el-cascader
            v-if="domain.type === 'ability'"
            :ref="'domainsSelect' + index"
            v-model="domain.value"
            :options="domain.domainsOptions"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="贡献专家:" prop="ownerId">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select
                ref="onwerSelect"
                v-model="formData.ownerId"
                style="width: 100%"
                multiple
                filterable
                remote
                placeholder="请选择用户"
                :remote-method="searchUser"
                clearable
                @change="changeList"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="`${item.nickname}${item.loginName ? '(' + item.loginName + ')' : ''}`"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="标签:" prop="tags">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select
                ref="tagsSelect"
                v-model="formData.tag_ids"
                style="width: 100%"
                multiple
                filterable
                remote
                allow-create
                placeholder="请选择标签"
                :remote-method="searchTags"
                clearable
                @change="changeTags"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="描述:" prop="description">
          <el-input
            v-model.trim="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            maxlength="255"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="可见范围:" prop="visibility">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="public">公开</el-radio>
            <el-radio label="private">私有</el-radio>
            <el-radio label="share">分享</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="formData.visibility === 'share'">
          <div class="flex">
            <div>
              <i
                class="el-icon-circle-plus-outline"
                style="color: blue; cursor: pointer"
                @click="shareFrame()"
              />
              已分享:
            </div>
            <div v-if="shareUsers.length > 0" style="width: 90%">
              <span v-for="(item, index) in shareUsers" :key="item.id">
                {{ item.nickname }}<span v-if="index !== shareUsers.length - 1">，</span>
              </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="showDisplayType" label="展示模式:" prop="display_type">
          <el-radio-group v-model="formData.display_type">
            <el-radio label="2">标准模式</el-radio>
            <el-radio label="1">高级模式</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button
        type="primary"
        :loading="loading"
        :disabled="loading || echoLoading"
        @click="createSubmit"
        >确定</el-button
      >
    </div>
    <shareMember
      ref="shareMemberRef"
      :share-users="shareUsers"
      :modal-title="modalTitle"
      @shareMemberList="shareMemberList"
    />
  </div>
</template>
<script>
import {
  AddScheme,
  UpdateScheme,
  SceneList,
  queryDictConfig,
  agentSenceList,
  queryTags,
  addTag,
  bindTag,
  queryUseTags,
  querySchemeDetailById,
  getInitWorkBenchDich,
  getExecuteSync,
  getSencetVisibleList
} from '@/api/planGenerateApi.js'
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : Vue.prototype.userInfo
  ? Vue.prototype.userInfo
  : {}
export default {
  name: 'ModelDialog',
  data() {
    const checkShare = (rule, value, callback) => {
      if (value.trim() === '') {
        return callback(new Error('可见范围不能为空'))
      } else if (value === 'share' && this.shareUsers.length === 0) {
        return callback(new Error('分享人员不能为空'))
      } else {
        callback()
      }
    }
    return {
      showDisplayType: false, // 是否展示模式
      formData: {
        name: '',
        description: '',
        agent_scene_code: '',
        agent_scene: '',
        agent_id: '',
        ownerId: [],
        share_userids: [], // 分享的用户
        contributors: [],
        tag_ids: [],
        scene_version_id: '',
        scheme_detail_name: '',
        domains: [],
        visibility: 'public',
        display_type: '1'
      },
      userList: [],
      agentList: [{ id: '001', description: 'ddd' }],
      tagList: [],
      allTagList: [],
      sceneList: [],
      senceOptions: [],
      dictList: [],
      allTags: [],
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // scheme_detail_name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        agent_scene: [{ required: true, message: '请选择使用场景', trigger: 'blur' }],
        visibility: [
          { required: true, message: '请选择可见范围', trigger: 'blur' },
          { validator: checkShare, trigger: 'blur' }
        ],
        description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
        display_type: [{ required: true, message: '请选择展示模式', trigger: 'blur' }]
      },
      loading: false,
      echoLoading: false,
      schemeId: '',
      editData: {},
      shareUsers: [],
      modalTitle: '请选择分享用户',
      shareToolTip: '',
      disabledSceneTypeCodeList: ['digital_twin_assistant_scene', 'operational_optimization_scene']
    }
  },
  async mounted() {
    this.loading = true
    if (this.$route.query.id) {
      await this.schemeDetailById()
    } else {
      await this.queryDict()
    }
    await this.queryDataInfo()
    await this.queryAllTag()
    //  await this.searchTags2('');
    this.loading = false
  },
  methods: {
    shareFrame() {
      this.$refs.shareMemberRef.handleOpen()
    },
    shareMemberList(Arr) {
      this.shareUsers = Arr || []
      this.formData.share_userids = Arr.map((el) => {
        return el.id
      })
      this.shareToolTip = Arr.map((el) => {
        return el.nickname
      })
    },
    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('详情', res.data.result)
            this.editData = res.data.result || {}
            console.log(this.editData, '详情2')
            const datas = res.data.result || {}
            this.formData.agent_scene_code = datas.agent_scene_code
            this.formData.name = datas.name
            this.formData.visibility = datas.visibility || 'public'
            this.formData.scheme_detail_name = datas.scheme_detail_name
            this.formData.agent_id = datas.agent_id || ''
            this.formData.ext_data_info = datas.ext_data_info
            this.formData.display_type = datas.display_type + '' || '1'
            await this.querySence2(datas.agent_scene_code)
            await this.queryDict()
            // this.formData.agent_scene_code = [this.editData.agent_scene_code, this.editData.agent_scene];
            this.formData.description = datas.description
            this.formData.contributors = datas.contributors
            // this.formData.tag_ids = this.editData.tag?.map(item => item.id);
            const ownerIds = datas.contributors?.map((item) => item.id)
            this.shareUsers =
              datas.share_infos?.map((el) => {
                return {
                  id: el.user_id,
                  nickname: el.nick_name
                }
              }) || []
            this.shareToolTip = this.shareUsers.map((el) => {
              return el.nickname
            })
            // this.formData.agent_scene = this.editData.agent_scene
            if (datas.contributors?.length > 0) {
              this.$nextTick(() => {
                datas.contributors.forEach((ele) => {
                  this.$refs.onwerSelect?.cachedOptions.push({
                    currentLabel: ele.nickname + '(' + ele.loginName + ')', // 	当前绑定的数据的label
                    currentValue: ele.id, // 当前绑定数据的value
                    label: ele.nickname + '(' + ele.loginName + ')', // 当前绑定的数据的label
                    value: ele.id // 当前绑定数据的value
                  })
                })
                this.$set(this.formData, 'ownerId', ownerIds)
              })
            }
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
    },
    queryDict() {
      queryDictConfig({ business_type: 'scene_type' }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          const temp = res.data.result?.config.map((item) => {
            return {
              value: item.code,
              label: item.name,
              is_display: item.is_display
            }
          })
          this.dictList = temp
          console.log('zhiagent_scene_code', this.formData.agent_scene_code)
          if (this.formData.agent_scene_code) {
            const filters = temp.filter((item) => item.value === this.formData.agent_scene_code)
            if (filters.length && Number(filters[0].is_display) === 1) {
              this.showDisplayType = true
            } else {
              this.showDisplayType = false
            }
            // 物联孪生场景的展示模式需要去除
            if (this.disabledSceneTypeCodeList.includes(this.formData.agent_scene_code)) {
              this.showDisplayType = false
            }
          } else {
            this.showDisplayType = false
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    querySence2(val, type) {
      agentSenceList({ name: '', scene_type: val }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          const result = res.data.result
          if (result && result.length > 0) {
            this.senceOptions = result.map((item) => {
              return {
                value: item.id,
                label: item.name,
                versionId: item.scene_version_id
              }
            })
            const filter = result.filter((item) => item.id === this.editData.agent_scene_id)
            console.log(filter, '000', this.editData)
            if (filter.length) {
              this.formData.agent_scene = this.editData.agent_scene_id
              this.formData.scene_version_id = this.editData.scene_version_id
              this.queryEqu(this.formData.scene_version_id)
            } else {
              this.formData.agent_scene = ''
            }
          } else {
            this.senceOptions = []
            this.formData.agent_scene = ''
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    querySence(val, type) {
      if (!this.$route.query.id) {
        getSencetVisibleList({
          keyword: '',
          user_id: userInfo.userId,
          scene_type: val,
          workspace_id: this.$route.query.workspaceId
        }).then((res) => {
          const result = res.data || []
          if (result && result.length > 0) {
            this.senceOptions = result
              .filter(
                (item) =>
                  item.scene_visibility !== 'unpublish' && item.scene_visibility !== 'invisiable'
              )
              .map((item) => {
                return {
                  value: item.scene_version_id,
                  label: item.name,
                  versionId: item.scene_version_id,
                  scene_visibility: item.scene_visibility
                }
              })
            if (type === 'change') {
              this.formData.agent_scene = this.senceOptions[0].value
              this.formData.scene_version_id = this.senceOptions[0].versionId
              this.queryEqu(this.formData.scene_version_id)
            }
          } else {
            this.senceOptions = []
            this.formData.agent_scene = ''
            this.formData.domains = []
          }
        })
      } else {
        agentSenceList({ name: '', scene_type: val }).then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data.result
            if (result && result.length > 0) {
              this.senceOptions = result.map((item) => {
                return {
                  value: item.scene_version_id,
                  label: item.name,
                  versionId: item.scene_version_id,
                  scene_visibility: item.scene_visibility
                }
              })
              if (type === 'change') {
                this.formData.agent_scene = this.senceOptions[0].value
                this.formData.scene_version_id = this.senceOptions[0].versionId
                this.queryEqu(this.formData.scene_version_id)
              }
            } else {
              this.senceOptions = []
              this.formData.agent_scene = ''
              this.formData.domains = []
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
      }
    },
    filterEmptyChildren(arr) {
      return arr
        .map((obj) => {
          if (obj.children && obj.children.length > 0) {
            obj = Object.assign({}, obj, {
              children: this.filterEmptyChildren(obj.children)
            })
          }
          return obj
        })
        .filter((obj) => {
          return obj.children && obj.children.length > 0
        })
    },
    queryEqu(val) {
      getInitWorkBenchDich({ scene_version_id: val })
        .then(async (res) => {
          console.log('------')
          this.echoLoading = true
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data?.result || []
            this.formData.domains = []
            if (result.length > 0) {
              for (const item of result) {
                const domainItem = {
                  label: item.field_name,
                  key: item.field_code,
                  value: '',
                  type: item.field_val.type || '',
                  domainsOptions: []
                }
                if (item.field_val.type === 'ability') {
                  const res = await getExecuteSync({
                    ability_id: item.field_val.ability_id,
                    name: item.field_val.ability_id,
                    goal: ''
                  })
                  if (res.data && res.data.length > 0) {
                    const resultList = res.data
                    if (resultList && resultList.length > 0) {
                      resultList.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                          item.children.forEach((it) => {
                            if (it.children && it.children.length === 0) {
                              delete it.children
                            }
                          })
                        }
                      })
                      domainItem.domainsOptions = resultList
                    }
                    this.formData.domains.push(domainItem)
                  }
                } else {
                  this.formData.domains.push(domainItem)
                }
              }
              if (this.$route.query.id) {
                if (this.formData.domains.length > 0) {
                  this.formData.domains.forEach((item, index) => {
                    if (item.type === 'ability') {
                      if (this.formData.ext_data_info[index]?.field_val?.parentValue) {
                        // 二层级
                        item.value = [
                          this.formData.ext_data_info[index]?.field_val?.parentValue,
                          this.formData.ext_data_info[index]?.field_val?.value
                        ]
                      } else {
                        // 一层级
                        item.value = [this.formData.ext_data_info[index]?.field_val?.value]
                      }
                    } else {
                      item.value = this.formData.ext_data_info[index]?.field_val?.value
                    }
                  })
                }
              }
            }
          }
          this.echoLoading = false
        })
        .finally(() => {
          this.echoLoading = false
        })
    },
    agentSceneChange(val) {
      console.log('002', val)
      const obj = this.senceOptions.find((obj) => obj.versionId === val)
      // console.log('obj', val, obj, this.senceOptions);
      this.formData.agent_scene = obj.versionId
      this.formData.scene_version_id = obj.versionId
      this.queryEqu(obj.versionId)
    },
    sceneChange(senceid) {
      console.log('001')
      const filters = this.dictList.filter((item) => item.value === senceid)
      console.log('filters', filters)
      if (filters.length && Number(filters[0].is_display) === 1) {
        this.showDisplayType = true
      } else {
        this.showDisplayType = false
      }
      this.querySence(senceid, 'change')
    },
    changeList(val) {
      this.$nextTick(() => {
        this.formData.contributors = this.$refs.onwerSelect.selected.map((item) => {
          return {
            id: item.currentValue,
            nickname: item.currentLabel.slice(0, item.currentLabel.indexOf('(')),
            loginName: item.currentLabel.match(/\((.+)\)/)[1]
          }
        })
      })
    },
    async changeTags(val) {
      console.log('改变', val)
      const temp = []
      val.forEach(async (tagid) => {
        const tagTotal = [...this.allTags, ...this.allTagList]
        const filters = tagTotal.filter((item) => item.id === tagid)
        console.log(filters, '666')
        if (filters.length === 0) {
          console.log('标签长度', tagid.length)
          if (tagid.length && tagid.length <= 15) {
            await addTag({
              name: tagid
            }).then(async (res) => {
              if (res.data) {
                console.log('添加成功', res.data)
                temp.push(res.data)
                await queryTags({
                  keyword: ''
                }).then((res) => {
                  if (res.data) {
                    const mergedArr = [...res.data, ...this.allTags]
                    const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(
                      JSON.parse
                    )
                    this.tagList = uniqueArr
                    this.allTagList = uniqueArr
                    this.$refs.tagsSelect.selected[
                      this.$refs.tagsSelect.selected.length - 1
                    ].currentLabel = tagid
                    this.formData.tag_ids = temp
                    console.log('this.formData.tag_ids', this.formData.tag_ids)
                    this.$nextTick(() => {
                      this.$refs.tagsSelect.selected[
                        this.$refs.tagsSelect.selected.length - 1
                      ].currentLabel = tagid
                    })
                  } else {
                    this.tagList = []
                  }
                })
              }
            })
          } else {
            this.$message({
              type: 'warning',
              message: '标签最长为15个字符，请修改!'
            })
          }
        } else {
          const mergedArr = [...this.allTags, ...this.allTagList]
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse)
          this.tagList = uniqueArr
          this.allTagList = uniqueArr
          const filters = tagTotal.filter((item) => item.id === tagid)
          if (filters.length) {
            // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            temp.push(tagid)
            this.formData.tag_ids = temp
            console.log('this.formData.tag_ids', this.formData.tag_ids)
            this.$nextTick(() => {
              // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            })
          } else {
            temp.push(tagid)
            this.formData.tag_ids = temp
            console.log('this.formData.tag_ids', this.formData.tag_ids)
          }
        }
      })
    },
    searchUser(userName) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data
      })
    },
    searchTags(keyword) {
      queryTags({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data
          if (keyword === '') {
            this.allTagList = res.data
          }
        } else {
          this.tagList = []
        }
      })
    },
    queryAllTag() {
      queryUseTags({ keyword: '' })
        .then((res) => {
          if (res.data) {
            console.log(res.data)
            this.allTags = res.data
            this.$nextTick(async () => {
              await this.searchTags2()
            })
          }
        })
        .finally(() => {})
    },
    searchTags2() {
      queryTags({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          const mergedArr = [...res.data, ...this.allTags]
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse)
          this.allTagList = uniqueArr
          this.tagList = uniqueArr
          const temp = []
          console.log('回显', this.editData.tags, this.tagList)
          this.editData.tags?.forEach((titem) => {
            const filter = uniqueArr.filter((item) => item.id === titem.id)
            if (filter.length) {
              temp.push(titem.id)
            }
          })
          this.formData.tag_ids = temp
        } else {
          this.allTagList = [...this.allTags]
          this.tagList = [...this.allTags]
        }
      })
    },
    queryDataInfo() {
      this.tableLoading = true
      // AgentList({type: 'group'}).then((res) => {
      //   console.log(res,'agent');
      //   if (res.status === 200 && res.data.code === 200) {
      //     this.agentList = res.data.result?.items || [];
      //   }
      // })
      SceneList().then((res) => {
        if (res.status === 200 && res.data) {
          this.sceneList = res.data.result || []
        }
      })
    },
    onClose(item) {
      this.formData.name = ''
      this.formData.agent_scene = []
      this.formData.description = 1
      this.formData.contributors = []
      this.formData.visibility = 'public'
      this.formData.ownerId = []
      this.formData.agent_id = ''
      this.formData.tag_ids = []
      this.formData.display_type = '1'
      this.$refs.form.clearValidate()
      if (this.formData.agent_scene_code === 'digital_twin_assistant_scene') {
        this.$router.push({
          path: '/planGenerate/index',
          query: {
            ...this.$route.query
          }
        })
      } else if (this.formData.agent_scene_code === 'operational_optimization_scene') {
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          // path: '/planGenerate/hqwPlanchat',
          query: {
            ...this.$route.query,
            status: item.status,
            id: item.id
          }
        })
      } else if (this.formData.agent_scene_code === 'other_assistant_scene') {
        this.$router.push({
          path: '/planGenerate/planchat',
          query: {
            ...this.$route.query,
            status: item.status,
            id: item.id
          }
        })
      } else {
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          query: {
            ...this.$route.query,
            status: item.status,
            id: item.id
          }
        })
      }
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          this.loading = true
          const outputFields = this.formData.domains.map((field) => {
            const obj = {}
            obj.field_name = field.label
            obj.field_code = field.key
            if (field.type === 'input') {
              obj.field_val = { value: field.value, label: field.value }
            } else {
              console.log(field, 'filed')
              if (field.value && field.value.length === 1) {
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(field.value[0])
                )
                obj.field_val = {
                  value: selectedOptions.value,
                  label: selectedOptions.label,
                  parentValue: selectedOptions.parentValue
                }
              } else if (field.value && field.value.length === 2) {
                const selectedValues = field.value
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(selectedValues[0])
                )
                const selectedSubOptions = selectedOptions?.children?.find(
                  (child) => child.value === selectedValues[1]
                )
                obj.field_val = {
                  value: selectedSubOptions.value,
                  label: selectedSubOptions.label,
                  parentValue: selectedValues[0]
                }
              }
            }
            return obj
          })
          console.log(outputFields)

          const param = {
            name: this.formData.name,
            scheme_detail_name: this.formData.name || this.formData.scheme_detail_name,
            description: this.formData.description,
            scene_version_id: this.formData.scene_version_id,
            agent_scene_code: this.formData.agent_scene_code,
            agent_id: this.formData.agent_id,
            contributors: this.formData.contributors,
            tag_ids: this.formData.tag_ids,
            ext_data_info: outputFields,
            visibility: this.formData.visibility
          }
          if (this.showDisplayType) {
            param.display_type = this.formData.display_type
          }
          if (this.formData.visibility === 'share') {
            param.share_userids = this.shareUsers.map((el) => {
              return el.id
            })
          }
          if (this.$route.query.id) {
            param.id = this.$route.query.id
            param.agent_scene = this.editData.agent_scene
            UpdateScheme(param)
              .then(async (res) => {
                this.loading = false
                console.log('编辑结果', res)
                if (res.status === 200 && res.data.code * 1 === 200) {
                  await bindTag({
                    tag_ids: this.formData.tag_ids,
                    biz_id: res.data.result.id
                  }).then((ress) => {
                    console.log('绑定成功', ress.data)
                  })
                  this.$message({
                    type: 'success',
                    message: '编辑成功!'
                  })
                  this.onClose(res.data.result)
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  })
                }
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            AddScheme(param)
              .then(async (res) => {
                this.loading = false
                console.log('创建结果', res)
                if (res.status === 200 && res.data.code * 1 === 200) {
                  await bindTag({
                    tag_ids: this.formData.tag_ids,
                    biz_id: res.data.result.id
                  }).then((ress) => {
                    console.log('绑定成功', ress.data)
                  })
                  this.$message({
                    type: 'success',
                    message: '创建成功!'
                  })
                  this.onClose(res.data.result)
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  })
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.editZhushou {
  display: flex;
  flex-direction: column;
  .headerBox {
    background: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    .headerTitle {
      font-size: 18px;
      color: #323233;
    }
  }
  .editBox {
    margin: 16px 20px;
    background: #fff;
    border-radius: 2px;
    flex: 1;
    padding: 16px 20px;
  }
  .dialog-footer {
    background: #fff;
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
</style>
