<template>
  <div class="iframe-box" v-loading="loading">
    <iframe
      id="agentIframe"
      :src="srcIframe"
      allow="clipboard-write"
      frameborder="0"
      class="my_iframe"
      @load="loading = false"
    />
  </div>
</template>
<script type="text/javascript">
export default {
  name: 'DuiqiModel',
  components: {},
  data() {
    return {
      searchInput: '',
      resourceType: '',
      loading: true,
      // srcIframe: 'http://localhost:3009/agentList',
      srcIframe: `${process.env.VUE_APP_AGENT_URL}/agentList`,
      agentMenuMap: {
        sceneModel: '/agentList/',
        abilityModel: '/modulList/',
        abilityRole: '/role/',
        abilityTool: '/toolList/',
        DuiqiModel: '/apiDocument',
        abilityDebug: '/agentgpt/',
        funcList: '/funList/',
        abilityComponentList: '/AbilityComponent',
        apiDocumentDetails: '/apiDocumentDetails',
      }
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(val) {
        console.log('路由变化了1', val)
        this.srcIframe = ''
        this.loading = true
        this.$nextTick(() => {
          // 赋值地址，再加载
          let path = process.env.VUE_APP_AGENT_URL + this.agentMenuMap[val.name]
          // let path = 'http://localhost:3009/agent' + this.agentMenuMap[val.name]
          if (val.name === 'apiDocumentDetails') {
            path += `?id=${this.$route.query.id}&workspaceId=${this.$route.query.workspaceId}`
              + `&is_public=${this.$route.query?.is_public || 0}&currentPage=${this.$route.query?.currentPage || 1}`
          }
          this.srcIframe = this.authSdk.transformToAuthUrl(path, 'local')
          const childFrame = document.getElementById('agentIframe')
          if (childFrame) {
            setTimeout(() => {
              childFrame.onload = () => {
                window.addEventListener('message', (event) => {
                  if (event.data?.type === 'routeChange') {
                    this.searchInput = event.data?.searchInput
                    this.resourceType = event.data?.resourceType
                    const routeUrl = this.$router.resolve({
                      path: '/planGenerate/abilityModel',
                      query: {
                        searchInput: event.data?.searchInput,
                        resourceType: event.data?.resourceType,
                        ...this.$route.query
                      }
                    })
                    window.open(routeUrl.href, '_blank')
                  }
                })
                console.log('agentIframe向下发送workspaceId', this.$route, 111111)
                setTimeout(() => {
                  this.loading = false
                  childFrame.contentWindow.postMessage(
                    JSON.stringify({
                      workspaceId: this.$route.query.workspaceId,
                      resourceType: this.$route.query?.resourceType,
                      searchInput: this.$route.query?.searchInput
                    }),
                    '*'
                  )
                }, 1000)
              }
            })
          }
        })
      }
    }
  },
  async mounted() {},
  created() {
    window.addEventListener('message', (event) => {
      if (event.data?.type === 'routeChange') {
        // console.log('event----',event)
        // this.searchInput = event.data?.searchInput
        // this.resourceType = event.data?.resourceType
        // 跳转到指定的路由
        // const routeUrl = this.$router.resolve({
        //   path:'/planGenerate/abilityModel',
        //   query: {
        //     searchInput: event.data?.searchInput,
        //     resourceType: event.data?.resourceType,
        //     ...this.$route.query
        //   }
        // })
        // // event.source.postMessage({ type: 'route',routeUrl:routeUrl.href,searchInput: event.data?.searchInput, resourceType: event.data?.resourceType}, '*');
        // console.log('父组件',routeUrl)
        // window.open(routeUrl.href,"_blank")
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.iframe-box {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 48px);
  background: #fff;
}
.my_iframe {
  width: 100%;
  height: 100%;
}
</style>
