<template>
    <div class="screenshot-container">
      <div v-if="imageSrc" class="cropper-wrap">
        <img ref="imageRef" :src="imageSrc" alt="Screenshot" />
        <button @click="capture">Capture Screenshot</button>
      </div>
      <!-- <button @click="capture">Capture Screenshot</button>
      <button v-if="imageSrc" @click="getCroppedImage">Get Cropped Image</button>
      <img v-if="croppedImageSrc" :src="croppedImageSrc" alt="Cropped Screenshot" /> -->
    </div>
  </template>
  
  <script>
  import html2canvas from 'html2canvas';
  import Cropper from 'cropperjs';
  import 'cropperjs/dist/cropper.css';
  
  export default {
    data() {
      return {
        imageSrc: null, // 用于存储截图后的图片路径
        croppedImageSrc: null, // 用于存储裁剪后的图片路径
        cropper: null, // 存储 Cropper 实例
        cropBoxData: null, // 存储裁剪框的数据
        minCropBoxWidth: 100, // 最小裁剪宽度
        minCropBoxHeight: 100, // 最小裁剪高度
      };
    },
    methods: {
      capture() {
        // 捕获页面或特定元素的快照
        const element = document.body; // 你可以根据需要选择不同的元素
        html2canvas(element).then((canvas) => {
          this.imageSrc = canvas.toDataURL();
          this.$nextTick(() => {
            if (this.cropper) {
              this.cropper.destroy(); // 如果已经有裁剪器实例，先销毁它
            }
            // 初始化新的裁剪器实例，并设置裁剪框的限制
            this.cropper = new Cropper(this.$refs.imageRef, {
              aspectRatio: NaN, // 自由比例
              viewMode: 1, // 禁止图像超出容器
              zoomable: false, // 可选：禁用缩放
              minCanvasWidth: this.minCropBoxWidth,
              minCanvasHeight: this.minCropBoxHeight,
              ready: () => {
                // 设置默认的裁剪框大小
                this.cropBoxData = this.cropper.getCropBoxData();
                this.cropper.setCropBoxData({
                  width: this.minCropBoxWidth,
                  height: this.minCropBoxHeight,
                });
              },
              crop: () => {
                // 每次裁剪框改变时更新数据
                this.cropBoxData = this.cropper.getCropBoxData();
              },
              cropend: () => {
                // 当裁剪结束时检查裁剪框是否符合最小尺寸要求
                if (this.cropBoxData.width < this.minCropBoxWidth || this.cropBoxData.height < this.minCropBoxHeight) {
                  this.cropper.setCropBoxData({
                    width: this.minCropBoxWidth,
                    height: this.minCropBoxHeight,
                  });
                }
              },
            });
          });
        });
      },
      getCroppedImage() {
        // 获取裁剪后的图片
        if (this.cropper) {
          this.croppedImageSrc = this.cropper.getCroppedCanvas().toDataURL();
        }
      },
    },
  };
  </script>
  
  <style scoped>
  .screenshot-container {
    text-align: center;
  position: fixed; /* 固定定位 */
  top: 0;
  left: 0;
  width: 100vw; /* 视口宽度 */
  height: 100vh; /* 视口高度 */
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* 确保覆盖其他内容 */
  }
  .cropper-wrap img {
    max-width: 100%;
    height: 100%;
    display: block;
    margin: 0 auto;
  }
  </style>