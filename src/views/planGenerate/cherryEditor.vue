<template>
  <div class="cherry-editor-container">
    <div id="cherryEditor" ref="cherryEditor" class="cherry-mount-node"></div>
  </div>
</template>

<script>
import Cherry from 'cherry-markdown';
import 'cherry-markdown/dist/cherry-markdown.css';

export default {
  name: 'CherryEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    modelValue: {
      type: String,
      default: ''
    },
    defaultModel: {
      type: String,
      default: 'editOnly'
    }
  },
  data() {
    return {
      editor: null,
      content: this.modelValue || this.value || '',
      mountId: "cherryEditor"
    }
  },
  watch: {
    modelValue: {
      handler(val) {
        if (this.editor && val !== this.content) {
          this.content = val;
          this.editor.setValue(val || '');
        }
      },
      immediate: true
    },
    value: {
      handler(val) {
        if (this.editor && val !== this.content) {
          this.content = val;
          this.editor.setValue(val || '');
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 确保挂载节点已准备好
    
    // 使用 MutationObserver 监听 DOM 变化
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const cherryNode = document.querySelector('.cherry');
          if (cherryNode && cherryNode.parentElement !== this.$refs.cherryEditor) {
            this.$refs.cherryEditor.appendChild(cherryNode);
          }
        }
      });
    });

    // 开始观察
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.$nextTick(() => {
      this.initEditor();
    });
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  },
  methods: {
    initEditor() {
      if (!this.$refs.cherryEditor) return;

      const config = {
        id: this.mountId,
        value: this.content,
        editor: {
          defaultModel: this.defaultModel,
          height: '100%'
        },
        engine: {
          syntax: {
            mathBlock: {
              engine: 'katex',
              inlineStyle: true
            },
            inlineMath: {
              engine: 'katex',
              inlineStyle: true
            }
          }
        },
        toolbars: {
          theme: 'light',
          toolbar: [
            'undo', 'redo', '|',
            // 把字体样式类按钮都放在加粗按钮下面
            {bold:['bold', 'italic', 'underline', 'strikethrough', 'sub', 'sup', 'ruby']},
            'color', 'size', '|', 'header', 'list', 'panel', '|',
            // 把插入类按钮都放在插入按钮下面
            {insert: ['image', 'audio', 'video', 'link', 'hr', 'br', 'code', 'formula', 'toc', 'table', 'drawIo']},
            'graph','codeTheme',
          ],
          sidebar: ['theme', 'copy','fullScreen','togglePreview'],
          toolbarRight: ['switchModel'],
          float: ['table', 'code', 'graph'],
          bubble: ['bold', 'italic', 'underline', 'strikethrough', 'sub', 'sup', 'ruby', '|', 'color','size',],
        },
        themeSettings: {
    // 主题列表，用于切换主题
          themeList: [
            { className: 'default', label: '默认' },
            { className: 'dark', label: '暗黑' },
            { className: 'light', label: '明亮' },
            { className: 'green', label: '清新' },
            { className: 'red', label: '热情' },
            { className: 'violet', label: '淡雅' },
            { className: 'blue', label: '清幽' },
          ],
          mainTheme: 'light',
          codeBlockTheme: 'default',
          inlineCodeTheme: 'red', // red or black
          toolbarTheme: 'dark', // light or dark 优先级低于mainTheme
        },
        callback: {
          onChange: (markdown) => {
            this.content = markdown;
            this.$emit('input', markdown);
            this.$emit('update:modelValue', markdown);
            this.$emit('change', markdown);
          },
          onFocus: () => {
            this.$emit('focus');
          },
          onBlur: () => {
            this.$emit('blur');
          }
        }
      };
      this.editor = new Cherry(config);
    }
  }
}
</script>

<style lang="scss">
.cherry-editor-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;

  :deep(.cherry) {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .cherry-toolbar {
      flex-shrink: 0;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      padding: 8px;
    }

    .cherry-editor-content {
      flex: 1;
      min-height: 0;
      display: flex;
      overflow: hidden;

      .cherry-editor {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .cherry-preview {
        flex: 1;
        min-height: 0;
        padding: 20px;
        border-left: 1px solid var(--el-border-color-light);
        background: var(--el-bg-color-page);
        color: var(--el-text-color-primary);
        overflow: auto;
      }
    }
  }
}
</style>
