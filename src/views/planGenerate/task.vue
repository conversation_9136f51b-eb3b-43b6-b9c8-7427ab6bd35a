<template>
    <div class="planContainer">
        <div class="planSearch">
            <div class="headerTitle">
              <div class="title">智能能力研发管理</div>
              <el-button type="primary" @click="() => $router.push({path: '/planGenerate/planchat', query: {...$route.query}})">返回智能能力研发</el-button>
            </div>
            <div class="searchFlex">
                <div class="searchItem">
                  <div class="searchLabel">名称：{{$route.query.name || '--'}}</div>
                </div>
                <div class="searchItem">
                  <div class="searchLabel">使用场景：{{$route.query.agent_scene_name || '--'}}</div>
                </div>
                <div class="searchItem">
                  <div class="searchLabel">创建时间：{{$route.query.create_time || '--'}}</div>
                </div>
                <div class="searchItem">
                  <div class="searchLabel">描述：{{$route.query.desc || ''}}</div>
                </div>
            </div>
        </div>
        <div class="containerCard">
          <div class="cardHeader">
            <el-button type="primary" @click="handleAddTask">新建任务</el-button>
          </div>
            <!-- 表格区域 -->
            <div v-if="tableData.list.length" class="cardContent">
              <el-table
                v-loading="tableLoading"
                :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
                :data="tableData.list"
                height="100%"
                size="small"
                style="width: 100%;height: 100%"
              >
                <el-table-column type="index" align="left" fixed :index="calcIndex" label="序号" width="50px" />
                <el-table-column prop="name" label="任务名称" fixed width="130" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="description" label="任务描述" min-width="160" max-width="200">
                  <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.description" placement="top">
                      <div class="descriptionTd">{{scope.row.description}}</div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="任务类型" min-width="100"></el-table-column>
                <el-table-column prop="task_input" label="任务输入" min-width="150" max-width="200">
                  <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.task_input" placement="top">
                      <div class="task_input">{{scope.row.task_input}}</div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="production_type" label="生成类型" min-width="170">
                  <template slot-scope="scope">
                    <div
                      v-for="(ptype, index) in scope.row.production_type.split('-')"
                      :key="index"
                      :class="[
                        'cutome-tag',
                        { '用户修改': 'running', '人工修改': 'running', 'AI生成': 'success', 'AI创建': 'success', 'AI创建-用户修改': 'fail' }[
                          ptype
                        ]
                      ]"
                      @click="handleDetailTo(scope.row)"
                    >
                      {{ptype}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间" min-width="150" />
                <el-table-column prop="update_time" label="修改时间" min-width="150" />
                <el-table-column prop="nickName" label="修改人" width="120"/>
                <el-table-column prop="tags" label="任务是否完成" width="120">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.statusFlag"
                      active-color="#406BD4"
                      inactive-color="#DCDDE0"
                      @change="changeStatus(scope.row)">
                    </el-switch>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="130" class-name="no-bor">
                  <template slot-scope="scope">
                    <el-link type="text" size="small" :underline="false" :disabled="judgeJumpFlag(scope.row)" @click="jumpIframe(scope.row)">访问</el-link>
                    <el-link type="text"  style="margin-left: 12px" size="small" :underline="false" @click="() => {editData = scope.row;createVisable = true;}">编辑</el-link>
                    <el-link type="text" style="margin-left: 12px" :underline="false" @click="deleteTask(scope.row)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="cardEmpty">
                <el-empty description="暂无任务"></el-empty>
            </div>
            <!-- 分页部分 -->
            <div style="text-align: right; padding-top: 16px">
            <el-pagination
                class="new-paper"
                layout="prev, pager, next, sizes, jumper"
                :page-sizes="[10, 20, 30, 40, 50]"
                :current-page.sync="tableData.page"
                :page-size="tableData.pageSize"
                :total="tableData.total || 0"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            >
            </el-pagination>
            </div>
        </div>
        <editModal :edit-data="editData" :visible="createVisable" @close="handleCreate" />
    </div>

  </template>

  <script>
  import { TaskPageList, PlanTaskDelete, UpdateTaskStatus} from '@/api/planGenerateApi.js'
  import { mapGetters } from 'vuex';
  import editModal from './editTask.vue';

  export default {
    components: {
        editModal
    },
    data() {
      return {
        isSuperAdmin: false, // 是否超级管理员
        rules: {
          name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
        },
        tableLoading: false, // 加载状态
        tableData: {
          list: [], // 表格数据
          page: 1,
          pageSize: 10,
          total: 0
        },
        searchForm: {
          searchKey: '' // 根据名称模糊搜索
        },
        createVisable: false,
        editData: {}
      };
    },
    computed: {
      ...mapGetters({
        isAdmin: 'common/getIsAdminGetter'
      })
    },
    watch: {},
    async created() {
    },
    // 生命周期 - 挂载完成（访问DOM元素）
    mounted() {
      console.log('方案生成页面');
      document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳转至';
      this.handlSearch();
    },
    methods: {
      judgeJumpFlag (row) {
        let flag = true;
        if (row.type === '文档设计' || row.type === '知识收集' || row.type === '规则开发' ||  row.type === '能力组装') {
          flag = false;
        } else {
          flag = true;
        }
        console.log('flag---', flag);
        return flag;
      }, 
      jumpIframe (row) {
        if (row.type === '知识收集') {
          this.$router.push({path: '/planGenerate/mechanism/knowledgeCollation', query: {scheme_id: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, knowledgeName: row.name, taskId: row.id, name: this.$route.query.name, id: this.$route.query.id, agent_scene_name: this.$route.query.agent_scene_name, create_time: this.$route.query.create_time, desc: this.$route.query.description}});
        }
        if (row.type === '文档设计') {
          this.$router.push({path: '/planGenerate/mechanism/designDocument', query: {schemaId: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, knowledgeName: row.name, taskId: row.id, name: this.$route.query.name, id: this.$route.query.id, agent_scene_name: this.$route.query.agent_scene_name, create_time: this.$route.query.create_time, desc: this.$route.query.description}});
        }
        // if (row.type === '数据开发') {
        //  this.$router.push({path: '/planGenerate/mechanism/knowledgeClassify', query: {schemaId: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, dataInfo: row.name, taskId: row.id}});
        // }
        if (row.type === '规则开发') {
          this.$router.push({path: '/planGenerate/mechanism/developmentMechanism', query: {schemaId: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, ruleName: row.name, taskId: row.id, name: this.$route.query.name, id: this.$route.query.id, agent_scene_name: this.$route.query.agent_scene_name, create_time: this.$route.query.create_time, desc: this.$route.query.description}});
        }
        if (row.type === '能力组装') {
          this.$router.push({path: '/planGenerate/mechanism/model', query: {schemaId: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, abilityName: row.name, taskId: row.id, name: this.$route.query.name, id: this.$route.query.id, agent_scene_name: this.$route.query.agent_scene_name, create_time: this.$route.query.create_time, desc: this.$route.query.description}});
        }
        // if (row.type === '模型开发') {
        //  this.$router.push({path: '/planGenerate/mechanism/devlopmentAlgorithm', query: {schemaId: this.$route.query.id, workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName, algName: row.name, taskId: row.id}});
        // }
      },
      handleAddTask(){
        this.editData = {};
        this.createVisable = true;
      },
      handlSearch() {
        this.tableData.page = 1;
        this.queryTableData();
      },
      calcIndex(index) {
        console.log('index', index, this.tableData.page);
        return this.tableData.page > 1 ? (index + 1) + (this.tableData.pageSize * (this.tableData.page - 1)) : index + 1
      },
      changeStatus(row) {
        UpdateTaskStatus({
          id: row.id,
          status: row.status === '未完成' ? '已完成' : '未完成'
        })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                  message: '状态修改成功',
                  type: 'success'
                });
              this.queryTableData();
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      queryTableData() {
        this.tableLoading = true;
        const param = {
          offset: this.tableData.page,
          limit: this.tableData.pageSize,
          scheme_id: this.$route.query.id,
          sort_field: 'create_time',
          order: 'desc',
        };
        TaskPageList(param)
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.tableData.list = res.data.result.items.map(item => {
                return {
                  ...item,
                  statusFlag: item.status !== '未完成'
                }
              });
              this.tableData.total = res.data.result.total;
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      deleteTask(row) {
        this.$confirm('此操作将删除该任务，是否继续?', '删除', {
          customClass: 'last-dialog',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.tableLoading = true;
            PlanTaskDelete({id: row.id}).then((res) => {
              this.tableLoading = false;
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                });
                this.tableData.page = 1;
                this.tableLoading = false;
                this.$nextTick(() => {
                  this.queryTableData();
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            })
            .finally(() => {
              this.tableLoading = false;
            });
          })
          .catch(() => {
            this.tableLoading = false;
            this.queryTableData();
          });
      },
      handleSizeChange(val) {
        this.tableData.page = 1;
        this.tableData.pageSize = val;
        this.queryTableData();
      },
      handleCurrentChange(val) {
        this.tableData.page = val;
        this.queryTableData();
      },
      handleCreate() {
        this.createVisable = false;
        this.queryTableData();
      },

      goToOldData() {
        this.$router.push({ path: '/imgDatasetManage', query: { ...this.$route.query } });

      },
      handleProject(row) {
        this.dataDetail = row;
        this.bindVisable = true;
      },
      handleTagDialog(row) {
        this.dataDetail = row;
        // this.tagList = item.tags || []
        this.tagVisable = true;
      }
    }
  };
  </script>
  <style lang="scss" scoped>
  .planContainer {
    height: calc(100vh - 100px);
    overflow: hidden;
    .containerCard {
        height: calc(100vh - 214px);
        overflow: hidden;
        margin: 16px 20px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        background-color: #fff;
        padding: 16px 20px;
        .cardHeader {
            text-align: right;
            .title {
                color: #323233;
                font-size: 16px;
                line-height: 30px;
            }
            .searchRight {
                float: right;
                .button-last {
                    line-height: 14px;
                }
            }
        }
        .cardEmpty {
            height: calc(100% - 46px);
            margin-top: 16px;
        }
        .cardContent {
            height: calc(100% - 96px);
            max-height: calc(100% - 96px);
            overflow-y: auto;
            margin-top: 16px;
            .cartItem {
                max-height: 252px;
                background: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #DCDDE0;
                .itemHeader {
                    font-size: 16px;
                    color: #323232;
                    line-height: 24px;
                    padding: 16px;
                    border-bottom: 1px solid #EBECF0;
                }
                .itemContent {
                    padding: 16px;
                    border-bottom: 1px solid #EBECF0;
                    .contentDesc {
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                        margin-top: 8px;
                        font-weight: normal;
                        color: #646566;
                        line-height: 22px;
                        font-size: 14px;
                        .descLabel {
                            color: #646566;
                        }
                        .descValue {
                            color: #323233;
                            line-height: 22px;
                        }
                    }
                    .itemProcess {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        padding-bottom: 8px;
                        .processItem {
                            width: 40px;
                            height: 40px;
                            border-radius: 4px;
                            position: relative;
                            background: rgba(235, 236, 240, 0.8);
                        }
                        .processLine {
                            flex: 1;
                            margin: 0px 8px;
                            height: 1px;
                            background: #C8C9CC;
                        }
                    }
                }
                .itemFooter {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-end;
                    align-items: center;
                    padding: 12px 16px;
                }
            }
        }
    }
  }
  .descriptionTd {
    max-width: 250px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .task_input {
    max-width: 180px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .planSearch {
    background-color: #fff;
    padding: 0px 20px 16px;
    .headerTitle {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .title {
          font-weight: bold;
          color: #323233;
          line-height: 26px;
          font-size: 18px;
          padding: 14px 0px;
        }
    }
    .button-last {
        line-height: 14px;
    }
    .searchFlex {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .searchItem {
          max-width: 300px;
          margin-right: 16px;
          width: 30%;
          color: #323233;
        }
    }
  }
  .cutome-tag {
    display: inline-block;
    border-radius: 2px;
    padding: 2px 8px;
    background: #f2f2f2;
    color: #323233;
    font-size: 14px;
    line-height: 20px;
    word-break: keep-all;
    position: relative;
    margin-right: 8px;
    &.running {
      background: #FFF2EB;
      color: #CC7846;
    }
    &.fail {
      background: #fcecec;
    }
    &.success {
      background: #E6ECFF;
      color: #4068D4;
    }
  }
  .el-popconfirm__action {
    margin-top: 10px;
  }
  ::v-deep .el-tooltip__popper {
    &.is-dark {
      background: rgba($color: #323233, $alpha: 0.8) !important;
    }
  }
  ::v-deep(.el-link.is-disabled) {
    .el-link--inner {
      opacity: 0.5;
    }
  }
  ::v-deep .el-button--text {
    background-color: transparent;
    color: #4068d4;
    border-color: transparent;
    padding: 6px 16px;
    border-radius: 2px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
  ::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}
::v-deep .el-switch__core:after {
    top: -3px;
    left: -4px;
    border: 1px solid #969799;
  }
::v-deep .el-switch__core{
  width: 28px !important;
  height: 12px;
}
::v-deep .el-switch.is-checked .el-switch__core::after{
  margin-left: -13px;
 border: 1px solid #406BD4 !important;
}
::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}
  .name-link {
    cursor: pointer;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; // 默认不换行；
    color: #4068d4;
    &:hover {
      color: #3455ad;
    }
    &.active {
      color: #264480;
    }
  }
  </style>
  <style lang="scss">
  .last-dialog {
    border-radius: 8px;
    .el-dialog__header {
      padding: 12px 20px;
      border-bottom: 1px solid #ebecf0;
      .el-dialog__title {
        font-size: 16px;
        color: #323233;
        line-height: 24px;
      }
      .el-dialog__headerbtn {
        top: 14px;
        .el-dialog__close {
          font-size: 18px;
        }
      }
    }
    .el-message-box__header {
      padding: 12px 20px;
      border-bottom: 1px solid #ebecf0 !important;
      .el-message-box__title {
        font-size: 16px;
        color: #323233;
        line-height: 24px;
      }
      .el-message-box__headerbtn {
        top: 14px;
        .el-message-box__close {
          font-size: 18px;
        }
      }
    }
    .el-message-box__content {
      padding: 16px 20px;
      .el-message-box__message {
        padding-left: 20px !important;
        padding-right: 20px !important;
      }
    }
    .el-message-box__btns {
      padding: 0px 20px;
      button {
        width: 60px !important;
      }
      .el-button {
        line-height: 20px !important;
      }
    }

    .el-dialog__body {
      padding: 16px 20px;
      max-height: 600px;
      overflow-y: auto;
    }
    &.small-last-dialog {
      .el-dialog__body {
        padding: 16px 20px;
        height: auto !important;
        max-height: 340px;
        overflow-y: auto;
      }
    }
    .el-dialog__footer {
      padding: 16px 20px;
      .el-button {
        line-height: 20px;
      }
    }
    .el-input__inner {
      border-radius: 2px;
    }
  }
  </style>
