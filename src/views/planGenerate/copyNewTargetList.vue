<template>
 <div :class="$store.state.planGenerate.isIframeHide ? 'page-container planContainerFrame' : 'page-container'">
  <div class="planSearch">
   <div class="headerTitle">能力仓库</div>
   <div class="search">
    <hearderContainer title="">
          <template #container>
            <div class="planSearch1">
              <condition-tag style="margin-bottom: 12px;" label="场景分类" ref="conditionTags" uniqueId="1" v-if="senceTypeList.length>0" :checkedTypeTags="checkedTypeTags" @selecType="selectType" :sence-type-list="senceTypeList"></condition-tag>
              <condition-tag :model="false" Icon="el-icon-price-tag" label="标签" ref="conditionTag" uniqueId="2" v-if="allTags.length>0" :checkedTypeTags="checkedTags" @selecType="selectTag" :sence-type-list="allTags"></condition-tag>
            </div>
          </template>
          <template #footer>
        <div style="display: flex; justify-content: flex-end; align-items: center;">
          <div class="footer-search">
            <el-popover
                v-model="showCustomSearch"
                popper-class="search"
                placement="bottom-end"
                trigger="click"
                :width="400"
                >
              <div>
                <el-form ref="form" :model="formData" label-position="left" label-width="97px" style="max-width: 100%; padding: 8px 16px;">
                <el-form-item   label="场景名称：">
                  <el-cascader
                      v-if="showFlag"
                      ref="cascaderSelect"
                      v-model="formData.scene_id"
                      show-all-levels
                      clearable
                      empty-text="暂无数据"
                      :props="props"
                      style="width: 100%"
                      @change="changeScene"
                      @expand-change="handleChangeExpand"
                    >
                      <template #empty> 暂无数据 </template>
                    </el-cascader>
                </el-form-item>
                <el-form-item label="最近更新人：">
                  <el-select
                      v-model="formData.updated_id"
                      placeholder="请选择创建人"
                      :remote-method="searchUser"
                      clearable
                      filterable
                      remote
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in userList"
                        :key="item.id"
                        :label="`${item.nickname}(${item.loginName})`"
                        :value="item.id"
                      />
                    </el-select>
                </el-form-item>
                <el-form-item >
                  <div class="w-full flex justify-end">
                    <el-button class="button-last" type="primary" @click="handlSearch">查询</el-button>
                    <el-button class="button-last" type="info" @click="handleReset">重置</el-button>
                    <el-button @click="showCustomSearch = false">取消</el-button>
                  </div>
                </el-form-item>
              </el-form>
              </div>
              <template #reference>
                <el-button slot="reference" class="button-last" type="text">高级搜索</el-button>
              </template>
            </el-popover>
            <el-input
                v-model.trim="formData.ability_name"
                clearable
                placeholder="请输入能力名称"
                class="search-input"
                @input="handlSearch"
              >
                <template #append>
                  <el-button type="primary" class="search-button" @click="handlSearch" icon="el-icon-search"></el-button>
                </template>
              </el-input>
          </div>
        </div>
      </template>
    </hearderContainer>
   </div>
  </div>
  <div class="containerCard">
   <div class="page-header">
    <div style="flex: 1; min-width: 120px">
     <el-radio-group v-model="tabPosition" size="small" @input="handleChange">
      <el-radio-button label="1">全部({{ allCount || 0 }})</el-radio-button>
      <el-radio-button label="3">在线({{ onlineCount || 0 }})</el-radio-button>
      <el-radio-button label="2">我的({{ mineCount || 0 }})</el-radio-button>
     </el-radio-group>
    </div>
    <div class="rg">
     <div v-if="$route.name !== ''" class="sortedScheme">
      <span>按调用量倒序：</span>
      <el-switch v-model="sortedScheme" :active-value="1" :inactive-value="0" />
     </div>
     <el-radio-group v-model="tabPosition2" style="min-width: 90px" size="small">
      <el-radio-button label="1">
       <i class="el-icon-document-copy"></i>
      </el-radio-button>
      <el-radio-button label="2">
       <i class="el-icon-menu"></i>
      </el-radio-button>
      <el-radio-button label="3">
       <i class="el-icon-s-operation"></i>
      </el-radio-button>

     </el-radio-group>
    </div>
   </div>
   <div class="page-table-items">
    <!-- 表格 -->
    <div v-if="tabPosition2 === '3' && tableData.length">
     <el-table :data="tableData" style="width: 100%" class="table-no-header">
      <el-table-column prop="name" label="能力名称" width="160">
       <template slot-scope="scope">
        <el-tooltip class="item" effect="dark" :content="scope.row.name" placement="top">
         <div style="
                     font-weight: bold;
                     color: #1d2129;
                     overflow: hidden;
                     text-overflow: ellipsis;
                     white-space: nowrap;
                   ">
          {{ scope.row.name }}
         </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="scope.row?.description" placement="top">
         <div style="
                     color: #646566;
                     overflow: hidden;
                     text-overflow: ellipsis;
                     white-space: nowrap;
                   ">
          {{ scope.row?.description }}
         </div>
        </el-tooltip>
       </template>
      </el-table-column>
      <el-table-column prop="scene_type" label="场景类型">
       <template slot-scope="scope">
        <div style="color: #646566">场景类型：</div>
        <div style="
                   color: #323233;
                   overflow: hidden;
                   text-overflow: ellipsis;
                   white-space: nowrap;
                 ">
         {{ scope.row.agent_scene_code_name || '--' }}
        </div>
       </template>
      </el-table-column>
      <el-table-column prop="scene_type" label="调用量">
       <template slot-scope="scope">
        <div style="color: #646566">调用量：</div>
        <div style="
                   color: #323233;
                   overflow: hidden;
                   text-overflow: ellipsis;
                   white-space: nowrap;
                 ">
         {{ scope.row.totalCallCount || 0 }}
        </div>
       </template>
      </el-table-column>
      <el-table-column prop="update_username" label="更新人" width="220">
       <template slot-scope="scope">
        <div style="color: #646566">更新人：</div>
        <div style="color: #323233">
         {{
          scope.row.update_nickName
           ? scope.row.update_nickName + '(' + scope.row.update_username + ')'
           : '--'
         }}
        </div>
       </template>
      </el-table-column>
      <el-table-column prop="update_username" label="归属人" width="220">
       <template slot-scope="scope">
        <div style="color: #646566">归属人：</div>
        <div style="color: #323233">
         {{ scope.row.owner_nickName + '(' + scope.row.owner_username + ')' }}
        </div>
       </template>
      </el-table-column>
      <el-table-column prop="tag" label="标签" width="220">
       <template slot-scope="scope">
        <div style="max-width: calc(220px); flex: 1">
         <selectItem :array-items="scope.row.tag?.map((tag) => {
          return { key: tag.id, label: tag.name };
         })
          " :max-length="2"></selectItem>
        </div>
       </template>
      </el-table-column>
      <el-table-column prop="address" label="更新时间" width="160">
       <template slot-scope="scope">
        <div style="color: #646566">更新时间：</div>
        <div style="color: #323233">
         {{ scope.row.update_time || '--' }}
        </div>
       </template>
      </el-table-column>
      <el-table-column label="操作" prop="operate" width="300px" fixed="right">
       <template slot-scope="scope">
        <el-button v-if="isSuperAdmin || userInfo.userId === scope.row.owner_id"
         :disabled="$route.name !== 'targetList'" type="text" @click="handleNav(scope.row)"> 智能能力研发 </el-button>
        <el-button v-if="scope.row.is_online" type="text" :disabled="$route.name !== 'targetList'"
         @click="handleShow(scope.row)"> 试一试 </el-button>
        <el-button type="text" :disabled="$route.name !== 'targetList'" @click="handleDetail(scope.row)"> 详情
        </el-button>
       </template>
      </el-table-column>
     </el-table>
    </div>
    <!-- 卡片信息 -->
    <template v-else-if="tabPosition2 === '1' && tableData.length">
     <TargetList :tableData="tableData" :pageParams="pageParams"></TargetList>
    </template>
    <!-- 卡片 -->
    <template v-else>
     <div v-for="(item, index) in tableData" :key="index" class="page-table-item2">
      <div class="headerBox" style="display: flex; flex-direction: row">
       <div style="flex-basis: 70%;flex-direction: column;width: 100%;">
        <div class="header">
         <el-popover placement="top-start" trigger="hover" width="240">
          <div>
           <div>
            发布人：{{
             `${item.publish_user_nickName}${item.publish_username ? '(' + item.publish_username + ')' : ''
             }` || '-'
            }}
           </div>
           <div>发布时间：{{ item.publish_date || '-' }}</div>
          </div>
          <i slot="reference" class="el-icon-warning-outline" />
         </el-popover>
         <el-tooltip class="item" effect="dark" :content="item.name" placement="top-start">
          <div :title="item.name" style="padding: 14px 14px 14px 0; font-size: 16px; font-weight: bold"
           class="hidden-sc">
           {{ item.name }}
          </div>
         </el-tooltip>
        </div>
        <div class="el-status">
         <div class="sence-tag">ID:{{ item.id }}</div>
         <Status :text="getStatusText(item)" :bg-color="statusTypeMap[item.is_online]?.bgColor"
          :dot-color="statusTypeMap[item.is_online]?.dotColor" />
         <div v-if="item.sort === 1" style="margin-left: 8px" class="sence-tag">置顶</div>
        </div>
       </div>
       <div v-if="item.abilityIcon && item.abilityIcon !== ''"
        style="flex: 1;height: 90px;display: flex;justify-content: flex-end;align-items: center;margin-right: 16px">
        <img style="height: 60px;width: 60px;border-radius: 5px" :src="item.abilityIcon">
       </div>
      </div>
      <div id="market-item" style="padding: 16px; width: 100%">
       <div class="statistics">
        <div>
         <el-tooltip effect="dark" content="调用量" placement="top">
          <img :src="require('@/assets/images/planGenerater/callNum.png')" />
         </el-tooltip>
         <span>{{ item.totalCallCount || 0 }}</span>
        </div>
        <div>
         <el-tooltip effect="dark" content="调用方" placement="top">
          <img :src="require('@/assets/images/planGenerater/userNum.png')" />
         </el-tooltip>
         <span>{{ item.totalAppCount || 0 }}</span>
        </div>
        <div>
         <el-tooltip effect="dark" content="版本数" placement="top">
          <img :src="require('@/assets/images/planGenerater/publishNum.png')" />
         </el-tooltip>
         <span>{{ item.publish_count || 0 }}</span>
        </div>
       </div>
       <div v-if="$route.name !== ''" :ref="'lineChart-' + tabPosition + '-' + index" class="line-chart"></div>
       <div v-if="$route.name !== 'targetList'" class="contentDesc">
        <div class="descLabel">空间名称：</div>
        <div class="descValue">
         {{ item.workspaceName || '--' }}
        </div>
       </div>
       <TagPopover v-if="item.tag?.length" :list="item.tag" parent-id="market-item" />
       <TagPopover v-else-if="item.abilityKeywords?.length" :list="item.abilityKeywords" parent-id="market-item" />
       <div v-else style="height: 25px"></div>
      </div>
      <div class="itemFooter">
       <el-button v-if="isSuperAdmin || userInfo.userId === item.owner_id" class="button-last" type="text"
        :disabled="$route.name !== 'targetList'" @click="handleNav(item)">智能能力研发</el-button>
       <el-button v-if="item.is_online" class="button-last" type="text" :disabled="$route.name !== 'targetList'"
        @click="handleShow(item)">试一试</el-button>
       <el-button class="button-last" type="text" :disabled="$route.name !== 'targetList'"
        @click="handleDetail(item)">详情</el-button>
      </div>
     </div>
    </template>

    <div v-if="!tableData.length" style="
           width: 100%;
           height: 100%;
           display: flex;
           flex-direction: row;
           justify-content: center;
         ">
     <el-empty description="暂无数据"></el-empty>
    </div>
   </div>
   <div class="page-footer">
    <el-pagination prev-text="上一页" next-text="下一页" background style="float: right" :current-page="pageParams.pageNum"
     :page-size="pageParams.pageSize" :page-sizes="[12, 24, 36, 48, 60]" layout="total, prev, pager, next"
     :total="pageParams.total" @size-change="getQueryPageList" @current-change="handleCurrentChange" />
   </div>
  </div>
  <previewTemplate ref="viewTemRef"></previewTemplate>
 </div>
</template>

<script>
import conditionTag from '@/components/conditionTag/index.vue'
import {
 queryDictConfig,
 queryAbilityMarket,
 queryUseTagsMarket,
 queryAbilityStatistics,
 queryAbilityMarketCount,
 querySchemeIdsOrderCallCount,
 queryUseMarketTagsWithCount,
 getSencetVisibleList
} from '@/api/planGenerateApi.js';
import hearderContainer from '@/components/hearderContainer/index.vue'
import { cloneDeep } from 'lodash';
import { isSuperAdminForCurUser } from '@/api/user';
import previewTemplate from './previewTemplate.vue';
import Status from '@/components/Status/index.vue';
import selectItem from './selectItem.vue';
import TagPopover from '@/components/tagPopover/index.vue';
import TargetList from '@/components/TargetList/index.vue';
import * as echarts from 'echarts';
import Vue from 'vue'
const userInfo = sessionStorage.getItem('USER_INFO')
 ? JSON.parse(sessionStorage.getItem('USER_INFO'))
 : Vue.prototype.userInfo ? Vue.prototype.userInfo : {};
const wid = localStorage.getItem('currentWorkSpace')
 ? JSON.parse(localStorage.getItem('currentWorkSpace')).workspaceId
 : '';
export default {
 components: {
  previewTemplate,
  Status,
  selectItem,
  TagPopover,
  TargetList,
  hearderContainer,
  conditionTag
 },
 data() {
  return {
   selectedValues: [],
   showFlag: false,
   firstSceneList: [],
   senceTypeList: [],
   checkedTypeTags: [],
   modelType: 1, // 默认是标准模式
   showCustomSearch: false, // 控制自定义搜索弹出框的显示与隐藏
   customFormData: {
    scene_id: '',
    updated_id: '',
    tags: [],
    workspaceId: ''
   },
   spaceList: [],
   spaceMap: {},
   userInfo: {},
   isFilterFlag: true,
   statusTypeMap: {
    false: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
    true: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
   },
   url: '',
   formData: {
    workspaceId: '',
    scene_id: '',
    ability_name: '',
    updated_id: '',
    tags: [],
    is_online: null,
   },
   isSuperAdmin: false,
   updated_id: '',
   agent_scene: '',
   allCount: 0,
   onlineCount: 0,
   mineCount: 0,
   isLoading: false,
   tabPosition: '1',
   tabPosition2: '1',
   tableData: [],
   userList: [],
   allTags: [],
   selectTags: [],
   checkedTags: [],// 筛选选择的标签
   pageParams: {
    pageNum: 1,
    pageSize: 12,
    name: '',
    scene_type: '',
    total: 0,
   },
   sortSchemeIds: [],
   sortedScheme: 1,
   props: {
    lazy: true,
    lazyLoad(node, resolve) {
     const { level } = node;
     if (level === 0) {
      queryDictConfig({ business_type: 'scene_type' }).then((res) => {
       if (res.status === 200 && res.data.code === 200) {
        const result = res.data.result?.config.map((item) => {
         return {
          value: item.code,
          label: item.name
         };
        });
        resolve(result || []);
       } else {
        this.$message({
         type: 'error',
         message: res.data?.msg || '接口异常!'
        });
       }
      });
     } else {
      getSencetVisibleList({
       'keyword': '',
       'user_id': userInfo.userId,
       'scene_type': node.data.value,
       'workspace_id': wid
      }).then((res) => {
       const result = res.data || [];
       if (result && result.length > 0) {
        const options = result.map((item) => {
         return {
          value: item.id,
          label: item.name,
          leaf: true
         };
        });
        resolve(options);
       } else {
        resolve([
         {
          value: '',
          label: '暂无',
          leaf: true,
          disabled: true
         }
        ]);
       }
      });
     }
    }
   }
  };
 },
 watch: {
  tabPosition2(val) {
   if (val === '2') {
    this.tableData?.forEach((item, index) =>
     this.initLineChart(index, this.tabPosition, item.dayCallList)
    );
   }
  },
  async sortedScheme(val) {
   if (val && this.$route.name !== '') {
    try {
     await this.getSchemeIdsOrderCallCount();
    } catch (e) {
    }
   } else {
    this.sortSchemeIds = []
   }
   this.getQueryPageList();
   this.getAbilityMarketCount();
  }
 },
 async created() {
  this.queryTagsWithCount();
 },
 // 生命周期 - 挂载完成（访问DOM元素）
 async mounted() {
  await this.queryAllTag()
  const query = this.$route.query
  if (query && query.name) {
   this.formData.ability_name = query?.name
  }
  await this.getSpaceList()
  this.userInfo = sessionStorage.getItem('USER_INFO')
   ? JSON.parse(sessionStorage.getItem('USER_INFO'))
   : Vue.prototype.userInfo ? Vue.prototype.userInfo : {};
  isSuperAdminForCurUser().then((res) => {
   if (res.status === 200) {
    this.isSuperAdmin = res.data.data;
   }
  });
  if (this.$route.name !== '') {
   try {
    await this.getSchemeIdsOrderCallCount();
   } catch (e) {
   }
  }
  await this.querySceneList();
  await this.getAbilityMarketCount();
  await this.getQueryPageList();
  if (this.$route.query && this.$route.query.scene_version_id && this.$route.query.agent_scene_code) {
   this.selectedValues = cloneDeep([this.$route.query.agent_scene_code, this.$route.query.scene_version_id])
  }
  this.showFlag = true;
 },
 methods: {
  changeScene(val) {
  },
  handleChangeExpand(val) {
  },
  // 获取第一层场景数据
  querySceneList() {
   queryDictConfig({ business_type: 'scene_type' }).then((res) => {

    if (res.status === 200 && res.data.code === 200) {
     this.senceTypeList = res.data.result?.config || [];
     this.$nextTick(()=> {
            this.$refs.conditionTags.observeResize();
          })
     this.firstSceneList = res.data.result?.config.map((item) => {
      return {
       value: item.code,
       label: item.name,
       is_display: item.is_display
      };
     });
     if (this.customFormData.scene_id.length > 0) {
      this.handleGetSecondScene(this.customFormData.scene_id[0]);
     }
    } else {
     this.senceTypeList = [];
     this.$message({
      type: 'error',
      message: res.data?.msg || '接口异常!'
     });
    }
   });
  },
  // 获取第二层场景数据
  handleGetSecondScene(val) {
   getSencetVisibleList({
    keyword: '',
    user_id: userInfo.userId,
    workspace_id: currentWorkspace.workspaceId,
    scene_type: val
   }).then((res) => {
    const result = res.data || [];

    if (result.length > 0) {
     result.forEach((item) => {
      this.sceneDetailList[item.scene_version_id] = item;
     })
     this.secondSceneList = result.map((item) => {
      return {
       value: item.scene_version_id,
       label: item.name
      };
     });
     this.$forceUpdate();
    } else {
     this.$message({
      type: 'error',
      message: res.data?.msg || '接口异常!'
     });
    }
   });
  },
  applyCustomSearch() {
   this.formData.scene_id = this.customFormData.scene_id;
   this.formData.updated_id = this.customFormData.updated_id;
   this.formData.tags = this.customFormData.tags;
   this.formData.workspaceId = this.customFormData.workspaceId;
   this.showCustomSearch = false;
   this.handlSearch();
  },
  async getSpaceList() {
   const vm = this
   const res = await vm.$axios.post(
    `${vm.baseUrl}/workspace/getWorkspacesByUserInfo`,
    { page: 1, pageSize: 200 }
   )
   if (res && res.data && res.data.status === 200) {
    this.spaceList = res.data.data
    this.spaceList.forEach(item => {
     this.spaceMap[item.id] = item;
    });
   }
  },
  getStatusText(item) {
   const text = this.statusTypeMap[item.is_online]?.text;
   if (item.is_online) {
    return text + `${item.online_version ? '：' + item.online_version : ''}`;
   }
   return text;
  },
  selectType(code) {
    if(Object.prototype.toString.call(code) === '[object Object]') {
        if(code.code) {
          code = code.code
        }else if(code.id) {
          code = code.id
        }
      }
   // 删除
   if (this.checkedTypeTags.indexOf(code) > -1) {
    this.checkedTypeTags = [];
   } else {
    // 增加
    this.checkedTypeTags = [code];
   }
   this.handlSearch();
  },
  selectTag(tag) {
    if(Object.prototype.toString.call(tag) === '[object Object]') {
        if(tag.code) {
          tag = tag.code
        }else if(tag.id) {
          tag = tag.id
        }
      }
   // 删除
   if (this.checkedTags.indexOf(tag) > -1) {
    const temp = [];
    this.checkedTags.forEach((item) => {
     if (item != tag) {
      temp.push(item);
     }
    });
    this.checkedTags = temp;
   } else {
    // 增加
    this.showType = tag;
    this.checkedTags.push(tag);
   }
   this.handlSearch();
  },
  queryTagsWithCount() {
   this.tableLoading = true;
   queryUseMarketTagsWithCount({ keyword: '' },'api/tag/list/with_use_statistic?biz_type=ability_market')
    .then((res) => {
     this.tableLoading = false;
     if (res.data) {
      const results = res.data
      this.allTags = results
      this.$nextTick(()=> {
            this.$refs.conditionTag.observeResize();
          })
     } else {
      this.$message({
       type: 'error',
       message: res.data?.msg || '接口异常!'
      });
     }
    })
    .finally(() => {
     this.tableLoading = false;
    });
  },
  queryAllTag(keyword = '') {
   this.tableLoading = true;
   queryUseTagsMarket({ keyword: keyword })
    .then((res) => {
     this.tableLoading = false;
     if (res.data) {
      this.selectTags = res.data;
     }
    })
    .finally(() => {
     this.tableLoading = false;
    });
  },
  handlSearch() {
   this.showSearchDialog = false; // 关闭弹出框
   this.pageParams.pageNum = 1;
   this.getAbilityMarketCount();
   this.getQueryPageList();
  },
  handleReset() {
   this.pageParams.pageNum = 1;
   this.formData.workspaceId = '';
   this.formData.ability_name = '';
   this.formData.scene_id = '';
   this.formData.updated_id = '';
   this.checkedTags = [];
   this.formData.tags = [];
   this.checkedTypeTags = [];
   this.getAbilityMarketCount();
   this.getQueryPageList();
  },
  handleCurrentChange(event) {
   this.pageParams.pageNum = event;
   this.getQueryPageList();
  },
  searchUser(userName = '', callback) {
   this.$post('/user/getAllUserListByUserName', {
    userName: userName
   }).then((data) => {
    this.userList = data;
    if (callback) {
     this.$nextTick(callback);
    } else {
     this.createUserName = userName;
    }
   });
  },
  handleChange(event) {
   if (+event === 1) {
    this.userList = [];
    this.updated_id = '';
    this.pageParams.pageNum = 1;
    this.formData.is_online = null;
   } else if (+event === 2) {
    this.userList = [
     {
      id: sessionStorage.getItem('userId'),
      nickname: sessionStorage.getItem('LOGIN_Name'),
      loginName: sessionStorage.getItem('loadItcode')
     }
    ];
    this.updated_id = sessionStorage.getItem('userId');
    this.formData.is_online = null;
    this.pageParams.pageNum = 1;
   } else {
    this.userList = [];
    this.updated_id = '';
    this.pageParams.pageNum = 1;
    this.formData.is_online = 1;
   }
   this.getQueryPageList();
  },
  async getAbilityMarketCount() {
   const res = await queryAbilityMarketCount({
    ...this.formData,
    scene_id: this.formData.scene_id ? this.formData.scene_id[1] : '',
    owner_id: this.userInfo.userId,
    agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
    tag_ids: this.checkedTags
   }, { workspaceId: this.formData.workspaceId });
   const data = res?.data?.result || {};
   this.allCount = data?.total_count || 0;
   this.mineCount = data?.mine_count || 0;
   this.onlineCount = data?.online_count || 0
  },
  async getSchemeIdsOrderCallCount() {
   const res = await querySchemeIdsOrderCallCount();
   this.sortSchemeIds = res?.data?.data || [];
  },
  async getQueryPageList() {
   let tags = this.formData.tags
   if (this.checkedTags != null && this.checkedTags.length > 0) {
    tags = this.formData.tags.concat(this.checkedTags);
   }
   const params = {
    ability_name: this.formData.ability_name,
    updated_id: this.formData.updated_id,
    scene_id: this.formData.scene_id ? this.formData.scene_id[1] : '',
    owner_id: this.tabPosition === '1' || this.tabPosition === '3' ? '' : this.userInfo.userId,
    page_num: this.pageParams.pageNum,
    page_size: this.pageParams.pageSize,
    agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
    tag_ids: tags,
    sorted_scheme_ids: this.sortSchemeIds,
   };
   if (this.formData.is_online) {
    params.is_online = this.formData.is_online;
   }
   queryAbilityMarket(params, { workspaceId: this.formData.workspaceId })
    .then(async (res) => {
     this.isLoading = true;
     const { data = {}, status } = res;
     if (status === 200 && data.code === 200) {

      const list = data.result?.items || [];
      const schemeIds = list?.map((item) => item.scheme_id);
      // 处理能力仓库底部卡片
      let statisticsData = {}
      if (this.$route.name !== '') {
       const results = await queryAbilityStatistics({ schemeIds, days: 7 });
       statisticsData = results?.data?.data;
      } else {
       statisticsData = {
        dayCallCountMap: {},
        totalDataMap: {}
       }
      }

      this.tableData = list?.map((item) => ({
       ...item,
       abilityIcon: item?.ext_info?.abilityIcon || '',
       abilityKeywords: Array.isArray(item?.ext_info?.abilityKeywords)
        ? item?.ext_info?.abilityKeywords?.map((i) => ({
         id: '',
         name: i
        }))
        : [],
       dayCallList: statisticsData?.dayCallCountMap[item.scheme_id] || [],
       totalCallCount: statisticsData?.totalDataMap[item.scheme_id]?.totalCallCount || 0,
       totalAppCount: statisticsData?.totalDataMap[item.scheme_id]?.totalAppCount || 0,
       workspaceName: this.spaceMap[item.work_space_id]?.workspaceName
      }));
      this.pageParams.total = data.result?.total;
      this.tableData.forEach((item, index) =>
       this.initLineChart(index, this.tabPosition, item.dayCallList)
      );
     } else {
      this.$message.error(res.msg || '应用创建失败!');
     }
    })
    .catch((_err) => {
     this.$message({
      type: 'error',
      message: _err.data?.msg || '接口异常!'
     });
    })
    .finally(() => {
     this.isLoading = false;
    });
  },
  initLineChart(index, type, list) {
   this.$nextTick(() => {
    const chartRef = this.$refs['lineChart-' + type + '-' + index][0];
    const xData = list?.map((item) => item.statisticsDate);
    const yData = list?.map((item) => item.callCount);
    const lineOption = {
     color: '#4B7AFA',
     tooltip: {
      trigger: 'axis',
      axisPointer: {
       type: 'shadow'
      }
     },
     grid: {
      left: 10,
      right: 20,
      top: '18%',
      bottom: 0,
      containLabel: true
     },
     xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
     },
     yAxis: {
      name: '调用量(次)',
      nameTextStyle: {
       fontSize: 10
      },
      type: 'value'
     },
     series: [
      {
       data: yData,
       type: 'line',
       areaStyle: {}
      }
     ]
    };
    if (!chartRef) return;
    echarts.dispose(chartRef);
    const chart = echarts.init(chartRef);
    chart.resize();
    chart.clear();
    chart.setOption(lineOption);
    window.addEventListener(
     'resize',
     () => {
      chart.resize();
     },
     false
    );
   });
  },
  handleShow(row) {
   this.$router.push({
    path: '/abilityCenter/test', query: {
     ...this.$route.query,
     id: row.id,
     scheme_id: row.scheme_id,
     agent_scene_code: row.agent_scene_code,
     name: row.name
    }
   })
  },
  handleDetail(row) {
   this.$router.push({
    path: '/abilityCenter/targetList/detail',
    query: {
     ...this.$route.query,
     id: row.id,
     agent_scene_code: row.agent_scene_code
    }
   });
  },
  async handleNav(data) {
   if (data.agent_scene_code === 'digital_twin_assistant_scene') {
    this.$router.push({
     path: '/planGenerate/wllsDevPlanchat',
     query: {
      ...this.$route.query,
      id: data.scheme_id
     }
    });
   } else if (data.agent_scene_code === 'operational_optimization_scene') {
    this.$router.push({
      path: '/planGenerate/ConfTaskPlanchat',
    //  path: '/planGenerate/hqwPlanchat',
     query: {
      ...this.$route.query,
      id: data.scheme_id
     }
    });
   } else {
    this.$router.push({
     path: '/planGenerate/planchat',
     query: {
      ...this.$route.query,
      id: data.scheme_id
     }
    });
   }
  }
 }
};
</script>
<style lang="scss" scoped>
.centered-search {
 display: flex;
 justify-content: center;
 align-items: center;
 width: 100%;

 /* 确保宽度占满父容器 */
 .search-button {
  background-color: #4068d4;
  /* 按钮背景色 */
  border-color: #4068d4;
  /* 按钮边框色 */
  color: #fff;
  /* 按钮文字颜色 */
  font-weight: 500;
  /* 按钮文字粗细 */
 }

 .search-button:hover {
  background-color: #66B1FF;
  /* 按钮悬停背景色 */
  border-color: #66B1FF;
  /* 按钮悬停边框色 */
 }

 .search-button:active {
  background-color: #3A8EE6;
  /* 按钮激活背景色 */
  border-color: #3A8EE6;
  /* 按钮激活边框色 */
 }
}

.modelType {
 display: flex;
 align-items: center;
 margin-right: 16px;
 font-size: 14px;
 color: #4068d4;
 line-height: 22px;
 position: relative;
 cursor: pointer;

 &::after {
  content: '';
  position: absolute;
  right: -8px;
  height: 12px;
  top: 6px;
  width: 1px;
  background: #c8c9cc;
 }
}

.bgimg {

 background-image: url('~@/assets/images/planGenerater/table-icon.png');
 /* 设置背景图片 */
 // background-color: #ff0000; /* 设置背景色为红色 */
 background-size: cover;
 /* 背景图片覆盖整个元素 */
 background-position: center;
 /* 背景图片居中 */
 width: 16px;
 /* 示例宽度 */
 height: 16px;
 /* 示例高度 */
}

.page-container {
 max-height: calc(100vh - 90px);
 height: calc(100vh - 90px);
 overflow: hidden;
 flex: 1;
 display: flex;
 flex-direction: column;

 &.planContainerFrame {
  max-height: calc(100vh) !important;
 }

 .planSearch {
  background-color: #fff;
  position: relative;

  .search {
   position: relative;
   overflow: hidden;
   .planSearch1 {
    padding: 0 20px;
  }
  .footer-search {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .el-input {
    width: 65%;
    margin: 5px 0px 5px 0px;
  }
}
  }

  .searchIcon {
   position: absolute;
   left: 50%;
   bottom: -10px;
   transform: translateX(-50%);

   .searchIconTaggle {
    width: 40px;
    height: 20px;
    background: #d9e6f8;
    border-radius: 2px;
    position: relative;
    border: 1px solid #f2f3f5;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4068d4;
    cursor: pointer;

    &:hover {
     background: #a1bbef;
    }
   }
  }

  .headerTitle {
   font-weight: bold;
   color: #323233;
   line-height: 26px;
   font-size: 18px;
   padding: 14px 20px;
  }

  .button-last {
   line-height: 14px;
  }
 }

 .containerCard {
  height: calc(100vh - 164px);
  max-height: calc(100vh - 164px);
  overflow: hidden;
  margin: 16px 20px 0px 20px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  background-color: #fff;
  padding: 16px 20px;

  & .page-header {
   width: 100%;
   display: flex;
   flex-direction: row;
   align-items: center;

   ::v-deep .el-radio-button__inner {
    height: 30px;
    line-height: 19px;
    padding: 4px 16px;
    font-size: 14px;
    border-radius: 2px 0 0 2px;
   }

   .rg {
    display: flex;
    align-items: center;

    .sortedScheme {
     display: flex;
     align-items: center;
     margin-right: 24px;

     >span {
      margin-right: 8px;
      font-size: 14px;
      color: #646566;
     }

    }
   }
  }

  & .page-table-items {
   height: calc(100% - 70px);
   margin-top: 16px;
   width: calc(100% + 12px);
   overflow-y: auto;
   display: flex;
   flex-wrap: wrap;
   justify-content: flex-start;
   align-content: flex-start;

   & .table-no-header {
    border-top: 1px solid #ebecf0;

    ::v-deep .el-table__header {
     display: none;
    }
   }

   & .page-table-item2 {
    width: calc(33.3333% - 12px);
    max-width: calc(33.3333% - 12px);
    margin-right: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
    border: 1px solid #dcdde0;

    .headerBox {
     display: flex;
     align-items: flex-start;
     flex-direction: column;
     width: 100%;
     border-bottom: 1px solid rgb(220, 221, 224);
    }

    .sence-tag {
     display: inline-flex;
     padding: 0 8px;
     margin-right: 10px;
     height: 24px;
     border-radius: 2px;
     background: #EBF9FF;
     color: #318DB8;
    }

    .el-status {
     margin-left: 16px;
     margin-bottom: 14px;
     display: flex;
     justify-content: left;
     align-items: center;
     border-radius: 2px;
     line-height: initial;
     font-size: 14px;

     .point {
      width: 5px;
      height: 5px;
      margin-right: 5px;
      background: #7d7e80;
      vertical-align: middle;
      display: inline-flex;
      align-items: center;
     }

     &.success {
      background: #d7eedb;

      .point {
       background: #39ab4c;
      }
     }
    }

    .header {
     flex: 1;
     display: flex;
     align-items: center;
     line-height: 24px;
     width: 100%;

     .el-icon-warning-outline {
      color: #4068d4;
      padding: 0 8px 0 16px;
      cursor: pointer;
     }
    }

    &:hover {
     box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
    }
   }
  }
 }
}

::v-deep .el-card__header {
 border: none;
 padding: 16px 0px 0px;
}

::v-deep .el-tooltip__popper {
 &.is-dark {
  background: rgba($color: #323233, $alpha: 0.8) !important;
 }
}

::v-deep .el-button.is-disabled {
 color: rgba($color: #4068d4, $alpha: 0.4);
}

::v-deep .el-button--info {
 background-color: #f2f3f5;
 color: #4068d4;
 border-color: #f2f3f5;

 &.is-disabled {
  opacity: 0.4;
  background-color: #f2f3f5 !important;
  color: #4068d4;
  border-color: #f2f3f5 !important;
 }

 &:hover {
  background-color: #ebecf0;
  border-color: #ebecf0;
  color: #4068d4;
 }

 &:active {
  background-color: #dcdde0;
  border-color: #dcdde0;
 }
}

::v-deep .el-button--text {
 background-color: #fff;
 color: #4068d4;
 border-color: #fff;
 padding: 6px 16px;
 border-radius: 2px;

 &.is-disabled {
  opacity: 0.4;
  background-color: #f2f3f5 !important;
  color: #4068d4;
  border-color: #f2f3f5 !important;
 }

 &:hover {
  background-color: #ebecf0;
  border-color: #ebecf0;
  color: #4068d4;
 }

 &:active {
  background-color: #dcdde0;
  border-color: #dcdde0;
 }
}

.itemFooter {
 border-top: 1px solid #c8c9cc;
 display: flex;
 flex-direction: row;
 justify-content: flex-end;
 align-items: center;
 padding: 12px 16px;
 max-width: 100%;
 overflow: hidden;
}

.hidden-sc {
 overflow: hidden;
 text-overflow: ellipsis;
 white-space: nowrap;
 flex: 1;
}

.scene-status {
 padding: 0 8px;
 height: 24px;
 border-radius: 2px;
 max-width: calc(100% - 80px);
 /* 设置文本溢出时的行为为省略号 */
 text-overflow: ellipsis;

 /* 设置超出容器的内容应该被裁剪掉 */
 overflow: hidden;

 /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
 white-space: nowrap;

 &.equi {
  background: #ebf9ff;
  color: #318db8;
 }

 &.client {
  background: #fff2eb;
  color: #cc7846;
 }

 &.other {
  background: #f3eafe;
  color: #7a4db8;
 }

 &.lingdao {
  background: #e6ecff;
  color: #3455ad;
 }
}

::v-deep .el-dialog__body {
 border-top: 1px solid #ebecf0;
 padding: 10px 20px;
}

.statistics {
 display: flex;
 align-items: center;
 background: #f6f7fb;

 >div {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  cursor: pointer;

  &:hover {
   background: #ebecf0;
  }

  >img {
   width: 32px;
   height: 32px;
   margin-right: 8px;
  }
 }
}

.line-chart {
 width: 100%;
 height: 162px;
 margin: 16px 0;
}

.contentDesc {
 margin: 16px 0;
 display: flex;
 flex-direction: row;
 justify-content: flex-start;
 align-items: center;
 margin-top: 8px;
 font-weight: normal;
 color: #646566;
 line-height: 22px;
 font-size: 14px;

 .descLabel {
  color: #646566;
  word-break: keep-all;
 }

 .descValue {
  color: #323233;
  line-height: 22px;
 }
}
</style>
