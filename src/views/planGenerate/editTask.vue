<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      :title="title"
      :visible.sync="visible"
      :before-close="onClose"
      append-to-body
      width="40%"
    >
      <el-form
        ref="form"
        label-position="right"
        label-width="99px"
        :model="formData"
        :rules="rules"
      >
        <el-form-item label="任务名称:" prop="task_name">
          <el-input
            v-model="formData.task_name"
            placeholder="请输入"
            show-word-limit
            maxlength="30"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="任务类型:" prop="type" v-show="false">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select v-model="formData.type" disabled placeholder="请选择类型" style="width: 100%">
                <el-option
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item label="关联父任务:" prop="parent_task_id" v-if="!editData.isAdd">
          <el-select :disabled="isEdit" v-model="formData.parent_task_id" clearable  filterable  placeholder="请选择关联父类" style="width: 100%">
                <el-option
                  v-for="item in parent_task_info"
                  :key="item.id"
                  :label="item.task_name"
                  :value="item.id"
                />
              </el-select>
        </el-form-item> -->
        <el-form-item label="场景类型:" prop="scene_id"  :rules="sceneIdRules">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select :disabled="editData.task_execute_info.scheme_id"  v-model="formData.scene_id" clearable  filterable  placeholder="请选择研发场景" style="width: 100%">
                <el-option
                  v-for="item in sceneList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="任务描述:" prop="task_desc">
          <el-input
            v-model="formData.task_desc"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生产目标:" prop="production_target">
          <el-input
            v-model="formData.production_target"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生产背景:" prop="production_background">
          <el-input
            v-model="formData.production_background"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="任务输入:" prop="task_input">
          <el-input
            v-model="formData.task_input"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <!-- <el-form-item label="跳转链接:" prop="adjust_url">
          <el-input
            v-model="formData.adjust_url"
            placeholder="请输入"
            style="width: 100%"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="createSubmit"
          >确定</el-button
        >
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { UpdateTask, scheme_task_upd,SceneTypeList, CreateTaskNew,get_parent_task_info } from '@/api/planGenerateApi.js';
import { isEmpty } from 'lodash';

export default {
  name: 'ModelDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      rules: {
      task_name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
        { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
      ],
      type: [

      ],
      scene_id: [
        // { required: this.formData.parent_task_id ? true :false, message: '请选择场景配置', trigger: 'change' }
      ],
      task_desc: [
        // 根据需要添加规则
      ],
      task_input: [
        // 根据需要添加规则
      ]
      // ... 其他规则
      },
      formData: {
        scene_id:'',
        task_name: '',
        task_desc: '',
        type: 'dev_product',
        task_input: '',
        parent_task_id:'',
        production_background:'',
        production_target:'',
        adjust_url: ''
      },
      typeList: [
          {
            name: '研发生产',
            value: 'dev_product'
          },
          {
            name: '其他任务',
            value: 'other_task'
          }
      ],
      sceneList:[],
      parent_task_info:[],
      loading: false
    };
  },
  computed: {
    isEdit() {
      if(this.$route.query.id){
        return !isEmpty(this.editData);
      }else{
        return !this.editData.task_id;
      }
    },
    title() {
      return !this.editData.isAdd? '编辑子任务' : '新建子任务';
    },
      // ... 其他计算属性
  sceneIdRules() {
    return [
      { required: !!this.formData.parent_task_id, message: '请选择场景配置', trigger: 'change' }
    ]
  }
  },
  watch: {
    visible: {
      handler(valNew,oldVal) {
        if (valNew) {
          this.queryDataInfo();
          this.get_parent_task_info();
          this.formData.parent_task_id = this.editData.task_id;
          if (!this.editData.isAdd) {
            this.formData.scene_id = this.editData?.task_execute_info?.scene_id
            this.formData.task_name = this.editData.task_name;
            this.formData.type = 'dev_product';
            this.formData.task_input = this.editData.task_input;
            this.formData.task_desc = this.editData.task_desc;
            this.formData.production_target = this.editData.production_target;
            this.formData.production_background = this.editData.production_background;
            this.formData.adjust_url = this.editData.adjust_url;
          }
        }
      },
      immediate: true,
      deep:true
    }
  },
  methods: {
    get_parent_task_info(){
      get_parent_task_info(
        {"scheme_id": this.$route.query.id || this.editData.id}
      ).then((res) => {
        console.log(res, '000');
        if (res.status === 200 && res.data) {
          this.parent_task_info = res.data.result;
        }
      });
    },
    queryDataInfo() {
      this.tableLoading = true;
      SceneTypeList(
        {"keyword": "", "scene_type": "", "dev_mode": ""}
      ).then((res) => {
        console.log(res, '000');
        if (res.status === 200 && res.data) {
          this.sceneList = res.data;
        }
      });
    },
    onClose() {
      this.formData.task_name = '';
      this.formData.task_desc = '';
      this.formData.task_input = '';
      this.formData.type = '';
      this.formData.parent_task_id = '';
      this.formData.production_background = '';
      this.formData.production_target = '';
      this.formData.adjust_url = ''
      this.formData.scene_id = ''
      this.$refs.form.clearValidate();
      this.$emit('close');
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          this.loading = true;
          const data = this.sceneList.filter(it => it.id ==this.formData.scene_id)
          const param = {
            task_name: this.formData.task_name,
            task_desc: this.formData.task_desc || '',
            production_target: this.formData.production_target,
            production_background: this.formData.production_background,
            parent_task_id: this.formData.parent_task_id || 0,
            type: this.formData.type,
            task_input: this.formData.task_input || '',
            production_type:'user_add',
            task_execute_info:{
              scene_id: this.formData.scene_id,
              scene_name: this.formData.scene_id ? data[0].name :'',
              scene_type:  this.formData.scene_id ? data[0].scene_type :'',
            },
            scheme_id: this.editData.scheme_id,
          };

          const paramUpdate ={
            task_id: this.editData.task_id,
            task_name: this.formData.task_name,
            task_desc: this.formData.task_desc || '',
            type: this.formData.type,
            production_target: this.formData.production_target,
            production_background: this.formData.production_background,
            parent_task_id: this.formData.parent_task_id || 0,
            task_input: this.formData.task_input || '',
            task_execute_info:{
              scene_id: this.formData.scene_id,
              scene_name: this.formData.scene_id ? data[0].name :'',
              scene_type:  this.formData.scene_id ? data[0].scene_type :'',
              scheme_id: this.editData?.task_execute_info?.scheme_id,
            },
            scheme_id:this.editData.scheme_id,
          }
          // if (this.editData.isAdd ) param.scheme_id = this.$route.query.id ? this.$route.query.id : this.editData.task_id;
          const request = !this.editData.isAdd ? scheme_task_upd : CreateTaskNew;
          request(!this.editData.isAdd  ? paramUpdate : param)
            .then((res) => {
              this.loading = false;
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  type: 'success',
                  message: `${!this.editData.isAdd ? '编辑' : '新建'}成功`
                });
                this.onClose();
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
