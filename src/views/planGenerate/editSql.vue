<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="生成sql"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="50%"
    >
      <div v-if="treeStatus ===3" style="margin-top: 20px">
        <el-result icon="error" title="生成sql失败！"></el-result>
      </div>
      <el-input
        v-else
        v-model.trim="textarea"
        type="textarea"
        :rows="16"
        placeholder="请输入sql语句">
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading || disableBtn" @click="onGenerateSQL">{{disableBtn===true ?'生成sql中...':'生成sql'}}</el-button>
        <el-button type="primary" :loading="loading" :disabled="loading || disableBtn || treeStatus === 3" @click="saveSql">保存</el-button>
        <el-button type="info" :loading="loading" :disabled="loading || disableBtn" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { queryAbilityMapping, updateAbilityMapping, CreateTask } from '@/api/planGenerateApi.js';

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    sqlData: {
      type: String,
      default: ''
    },
    treeStatus:{
      type: Number,
      default: -1,
    }
  },
  data() {
    return {
      textarea: '',
      loading: false,
      showFlag: false,
      dataStatus: '',
      disableBtn:false,
      configData: {}
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
          this.handleAbilityMapping();
          // 查询sql语句，todo
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
    sqlData: {
      handler(val) {
        if (val) {
          this.textarea = val
        }
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        if (val===2 ||val ===3) {
          this.disableBtn = false
        }
      },
      immediate: true
    }
  },
  methods: {
    handleAbilityMapping(){
      this.loading = true;
      queryAbilityMapping({scheme_id:this.$route.query.id})
        .then((res) => {
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.dataStatus = res.data.result.ability_status;
            this.textarea = res.data.result.sql;
            this.configData = res.data.result.config || {};
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    saveSql () {
      if (this.textarea) {
        this.loading = true;
          updateAbilityMapping({
            scheme_id:this.$route.query.id,
            // config: this.configData,
            sql: this.textarea
          }).then((res)=>{
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.$message({
                type: 'success',
                message: '保存完成!'
              });
              this.loading = false;
              this.textarea = '';
              this.$emit('close');
            }else{
              this.$message({
                type: 'success',
                message: '保存失败!'
              });
              this.laoding = false
            }
          }).catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.$message({
          type: 'error',
          message: '请生成sql内容后再保存！'
        });
      }
    },
    queryDataInfo() {
      console.log('d');
    },
    onClose() {
      if (!this.disableBtn) {
        this.textarea = '';
        this.$emit('close');
      } else {
        this.$message({
            type: 'warning',
            message: '请等待生成sql结束后再关闭'
          });
      }
    },
    onGenerateSQL(){
      this.disableBtn = true
      this.$emit('updateSQL')
    }
  }
};
</script>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
