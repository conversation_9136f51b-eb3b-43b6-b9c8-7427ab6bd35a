<template>
  <div class="iframe-box" v-loading="loading">
      <iframe
        id="duiqiChildFrame"
        :src="srcIframe"
        allow="clipboard-write"
        frameborder="0"
        class="my_iframe"
        @load="loading = false"
      />
  </div>
</template>
<script type="text/javascript">
export default {
  name: 'DuiqiModel',
  components: {
  },
  data () {
    return {
      loading: false,
      // srcIframe: 'http://localhost:3009/apiDocument/'
      srcIframe: `${process.env.VUE_APP_AGENT_URL}/apiDocument`,
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(val) {
        console.log('路由变化了1', val);
        this.srcIframe = ''
        this.loading = true;
        this.$nextTick(() => {
        // 赋值地址，再加载
          // const path = process.env.VUE_APP_AGENT_URL + '/apiDocument'
          const path = 'http://localhost:3009/apiDocument'
          this.srcIframe = this.authSdk.transformToAuthUrl(path,'local')
          console.log('this.srcIframe)--------------',this.srcIframe);
          const childFrame = document.getElementById('duiqiChildFrame');
          if (childFrame) {
            setTimeout(() => {
              childFrame.onload = () => {
                console.log('向下发送workspaceId');
                setTimeout(()=>{
                  childFrame.contentWindow.postMessage(JSON.stringify({workspaceId: this.$route.query.workspaceId}),'*');
                },500)
              }
            })
          }
        })
      }
    },
  },
  async mounted() {
    
  },
}
</script>

<style lang="scss" scoped>
.iframe-box{
  width: 100%;
  height: 100%
}
.my_iframe{
  width: 100%;
  height: 100%;
}
  
</style>
