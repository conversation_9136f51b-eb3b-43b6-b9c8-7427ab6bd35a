<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide ? 'planContainer planContainerFrame' : 'planContainer'
    "
  >
  <el-card class="box-card" shadow="never">
    <div class="planSearch">
      <!-- <div class="headerTitle">智能能力研发</div> -->
      <div class="search">
        <hearderContainer title="">
          <template #container>
            <div class="planSearch1" >
              <condition-tag label="场景分类" style="margin-bottom: 12px;" ref="conditionTags" uniqueId="1" :checkedTypeTags="checkedTypeTags" @selecType="selectType" :sence-type-list="senceTypeList"></condition-tag>
              <condition-tag :model="false" Icon="el-icon-price-tag" label="标签" ref="conditionTag" uniqueId="2" :checkedTypeTags="checkedTags" @selecType="selectTag" :sence-type-list="allTags"></condition-tag>
            </div>
          </template>
          <template #footer>
        <div style="display: flex; justify-content: flex-end; align-items: center;">
          <div class="footer-search">
            <el-popover
                v-model="showCustomSearch"
                popper-class="search"
                placement="bottom-end"
                trigger="click"
                :width="400"
                >
              <div>
                <el-form ref="form" :model="formData" label-position="left" label-width="84px" style="max-width: 100%; padding: 8px 16px;">
                <el-form-item v-if="$route.name !== 'planGenerateIndex'" label="空间名称：">
                  <el-select
                     v-model="formData.workspaceId"
                     filterable
                     clearable
                     placeholder="请选择"
                     style="width: 100%"
                     @change="handlSearch"
                   >
                     <el-option
                       v-for="item in spaceList"
                       :key="item.id"
                       :label="item.workspaceName"
                       :value="item.id"
                     />
                  </el-select>
                </el-form-item>
                <el-form-item   label="使用场景：">
                  <el-cascader
                    v-model="formData.agent_scene"
                    show-all-levels
                    clearable
                    empty-text="暂无数据"
                    :props="props"
                    style="width: 100%"
                    @change="changeScene"
                  >
                    <template #empty> 暂无数据 </template>
                  </el-cascader>
                </el-form-item>
                <!-- <el-form-item  label="状态：">
                  <el-select
                       v-model="formData.status"
                       clearable
                       placeholder="请选择状态"
                       style="width: 100%"
                     >
                       <el-option
                         v-for="item in statusList"
                         :key="item.value"
                         :label="item.name"
                         :value="item.value"
                       />
                  </el-select>
                </el-form-item> -->
                <el-form-item  label="创建人：">
                  <el-select
                       v-model="formData.createUserId"
                       placeholder="创建人"
                       :remote-method="searchUser"
                       clearable
                       filterable
                       remote
                       style="width: 100%"
                     >
                       <el-option
                         v-for="item in userList"
                         :key="item.user_id"
                         :label="`${item.nickName}(${item.username})`"
                         :value="item.user_id"
                       />
                  </el-select>
                </el-form-item>
                <el-form-item  label="贡献专家：">
                  <el-select
                     v-model="formData.contributors"
                     placeholder="请选择贡献专家"
                     :remote-method="searchUser"
                     clearable
                     filterable
                     remote
                     style="width: 100%"
                   >
                     <el-option
                       v-for="item in userList"
                       :key="item.user_id"
                       :label="`${item.nickName}(${item.username})`"
                       :value="item.user_id"
                     />
                  </el-select>
                </el-form-item>
                <el-form-item >
                  <div class="w-full flex justify-end">
                    <el-button class="button-last" type="primary" @click="handlSearch">查询</el-button>
                    <el-button class="button-last" type="info" @click="handleReset">重置</el-button>
                    <el-button @click="showCustomSearch = false">取消</el-button>
                  </div>
                </el-form-item>
              </el-form>
              </div>
              <template #reference>
                <el-button slot="reference" class="button-last" type="text">高级搜索</el-button>
              </template>
            </el-popover>
            <el-input
                v-model.trim="formData.name"
                clearable
                placeholder="请输入名称"
                @input="handlSearch">
                <template #append>
                  <el-button type="primary" class="search-button" @click="handlSearch" icon="el-icon-search"></el-button>
                </template>
              </el-input>
          </div>
        </div>
      </template>
        </hearderContainer>
      </div>
    </div>
  </el-card>
    <div
      v-loading="loading"
      :class="isFilterFlag ? 'containerCard containerCardSmall' : 'containerCard'"
    >
      <div :class="{ 'left-content': true, 'left-content-max': true }">
        <div class="table-content" v-if="showDataType === 'table'">
          <div class="cardHeader">
            <div class="btns"></div>
            <div class="searchRight">
              <div class="displayType">
                <!-- <div class="quick-search" style="margin-right: 10px;">
               <el-checkbox-group v-model="selectedFilters" @change="handleChangeMine">
                    <el-checkbox value="1" label="myCollect">我的收藏({{ collectTotal }})</el-checkbox>
                    <el-checkbox value="2" label="myAbility">我的({{ myTotal }}) </el-checkbox>
                  </el-checkbox-group>
                </div> -->
                <el-radio-group v-model="showDataType" style="min-width: 90px" size="small">

                  <el-radio-button label="card">
                    <i class="el-icon-menu"></i>
                  </el-radio-button>
                  <el-radio-button label="table">
                    <i class="el-icon-s-operation"></i>
                  </el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </div>
          <div
            v-if="tableData.list && tableData.list.length"
            :class="
              isFilterFlag
                ? $store.state.planGenerate.isIframeHide
                  ? 'cardContent cardContentFrame'
                  : 'cardContent cardContentSmall'
                : 'cardContent'
            "
          >
            <div style="margin-top: 12px">
              <el-table :data="tableTypeData.list" style="width: 100%; min-width: 600px">
                <el-table-column prop="id" label="ID" width="100"> </el-table-column>
                <el-table-column prop="name" label="任务名称" width="150"> </el-table-column>
                <el-table-column prop="nickName" label="提交人" width="120"> </el-table-column>
                <el-table-column prop="create_time" label="提交时间" width="120"> </el-table-column>
                <el-table-column prop="publish_status" label="任务状态" width="120">
                  <template slot-scope="scope">
                    <Status
                      :text="
                        developTaskStatus[
                          handleDevelopTaskStatus(scope.row)
                        ].text
                      "
                      :bg-color="
                        developTaskStatus[
                          handleDevelopTaskStatus(scope.row)
                        ]?.bgColor
                      "
                      :dot-color="
                        developTaskStatus[
                          handleDevelopTaskStatus(scope.row)
                        ]?.dotColor
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button @click="checkInto(scope.row)" type="text" size="small"
                      >查看详情</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div v-else class="cardEmpty">
            <el-empty description="暂无方案"></el-empty>
          </div>
          <!-- 分页部分 -->
          <div style="text-align: right; padding-top: 16px">
            <el-pagination
              class="new-paper"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="[12, 24, 36, 48, 60]"
              :current-page.sync="tableData.page"
              :page-size="tableData.pageSize"
              :total="tableData.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="card-content" v-if="showDataType === 'card'">
          <div class="cardHeader">
            <div class="btns">
            </div>
            <div class="searchRight">
              <div class="displayType">
                <!-- <div class="quick-search" style="margin-right: 10px;">
                  <el-checkbox-group v-model="selectedFilters" @change="handleChangeMine">
                    <el-checkbox value="1" label="myCollect">我的收藏({{ collectTotal }})</el-checkbox>
                    <el-checkbox value="2" label="myAbility">我的({{ myTotal }})</el-checkbox>
                  </el-checkbox-group>
                </div> -->
                <el-radio-group v-model="showDataType" style="min-width: 90px" size="small">
                  <el-radio-button label="card">
                    <i class="el-icon-menu"></i>
                  </el-radio-button>
                  <el-radio-button label="table">
                    <i class="el-icon-s-operation"></i>
                  </el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </div>
          <!-- 表格区域 -->
          <div
            v-if="tableData.list && tableData.list.length"
            :class="
              isFilterFlag
                ? $store.state.planGenerate.isIframeHide
                  ? 'cardContent cardContentFrame'
                  : 'cardContent cardContentSmall'
                : 'cardContent'
            "
          >
            <div v-for="(item, index) in tableData.list" :key="item.id_index" class="cartItem">
              <div class="cartItemBox">
                <div class="itemHeader">
                  <div class="itemHeaderTop">
                    <el-popover placement="top-start" trigger="hover" width="240">
                      <div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                          <div style="color: #646566">进度：</div>
                          <div style="flex: 1; max-width: calc(100% - 110px); color: #323233">
                            {{ statusMap(item.agent_scene_code, item.status) }}
                          </div>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                          <div style="color: #646566">贡献专家：</div>
                          <div style="flex: 1; max-width: calc(100% - 70px)">
                            <selectItem
                              class="contribution"
                              :array-items="selectUserFn(item.contributors)"
                              :max-length="1"
                            >
                            </selectItem>
                          </div>
                        </div>
                        <div style="display: flex; align-items: center">
                          <div style="color: #646566">创建时间：</div>
                          <div style="flex: 1; max-width: calc(100% - 70px); color: #323233">
                            {{ item.update_time || item.create_time }}
                          </div>
                        </div>
                      </div>
                      <i slot="reference" class="el-icon-warning-outline" />
                    </el-popover>
                    <div
                      :class="
                        $route.name !== 'planGenerateIndex'
                          ? 'itemHeaderTit isdisabled'
                          : 'itemHeaderTit'
                      "
                      @click="checkInto(item)"
                    >
                      <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                        <div class="title">{{ item.name }}</div>
                      </el-tooltip>
                    </div>
                    <div v-if="$route.name === 'planGenerateIndex'" class="itemHeaderBtn">
                      <el-dropdown
                        style="margin-left: 10px"
                        @command="(type) => handleCommand(type, item)"
                      >
                        <el-button type="text" size="mini"
                          ><img src="@/assets/images/planGenerater/more.png"
                        /></el-button>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item
                            v-if="
                              item.agent_scene_code !== 'device_ops_assistant_scene' &&
                              item.agent_scene_code !== 'device_ops_assistant_scene-v1' &&
                              item.agent_scene_code !== 'custom_cognition_assistant_scene' &&
                              item.agent_scene_code !== 'artificial_handle_scene' &&
                              item.agent_scene_code !== 'other_assistant_scene' &&
                              item.agent_scene_code !== 'visit_leader_cognition_scene' &&
                              item.agent_scene_code !== 'rule_generation_scene' &&
                              item.agent_scene_code !== 'intelligent_conversation_scene' &&
                              item.agent_scene_code !== 'sop_scene' &&
                              item.agent_scene_code !== 'digital_twin_assistant_scene' &&
                              item.agent_scene_code !== 'operational_optimization_scene'
                            "
                            command="task"
                            >任务</el-dropdown-item
                          >
                          <el-dropdown-item
                            :disabled="
                              (item.visibility === 'private' &&
                                item.username !== userInfo.username &&
                                !isAdmin) ||
                              (item.visibility === 'share' && !item.is_shared && !isAdmin)
                            "
                            command="edit"
                            >编辑</el-dropdown-item
                          >
                          <el-dropdown-item
                            v-if="!item.is_publish"
                            :disabled="item.username !== userInfo.username && !isAdmin"
                            command="remove"
                            >删除</el-dropdown-item
                          >
                          <el-dropdown-item
                            v-if="item.agent_scene_code === 'digital_twin_assistant_scene'"
                            :disabled="item.username !== userInfo.username && !isAdmin"
                            command="develop"
                            >转研发</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 8px">
                    <el-tooltip
                      effect="dark"
                      :content="item.nickName + '(' + item.username + ')'"
                      placement="top"
                    >
                      <span class="avator" style="word-break: keep-all">{{
                        item.nickName?.slice(-2)
                      }}</span>
                    </el-tooltip>
                    <div class="separate"></div>
                    <div class="sence-tag">ID:{{ item.id }}</div>
                    <div style="margin-left: 8px">
                      <Status
                        :text="statusTypeMap[item.publish_status]?.text"
                        :bg-color="statusTypeMap[item.publish_status]?.bgColor"
                        :dot-color="statusTypeMap[item.publish_status]?.dotColor"
                      />
                    </div>
                    <div
                      style="margin-left: 8px; word-break: keep-all"
                      v-if="item.sort == 1"
                      class="sence-tag"
                    >
                      置顶
                    </div>
                  </div>
                </div>
                <div class="itemContent">
                  <!--
                  need to check  device_ops_assistant_scene-v1 和 客户认知主页这没改 custom_cognition_assistant_scene
                  item.agent_scene_code === 'device_ops_assistant_scene' ||
                   item.agent_scene_code === 'sop_scene'  ||
                   item.agent_scene_code === 'visit_leader_cognition_scene' ||
                   item.agent_scene_code === 'intelligent_conversation_scene' ||
                   item.agent_scene_code === 'artificial_handle_scene' ||
                   item.agent_scene_code === 'rule_generation_scene' ||
                   item.agent_scene_code === 'device_ops_assistant_scene-v1'
                    -->
                  <div v-if="false" class="itemProcess">
                    <el-tooltip class="item" effect="dark" content="生成方案" placement="top">
                      <div :class="item.status >= 1 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planfangan" class="process-icon" />
                        <i v-if="item.status >= 1" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <!-- <div style="color: #323233; margin-left: 8px; flex: 1;word-break: keep-all;">生成方案</div> -->

                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="思维树生成" placement="top">
                      <div :class="item.status >= 2 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="decisiontree" class="process-icon" />
                        <i v-if="item.status >= 2" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="多模态数据对齐" placement="top">
                      <div :class="item.status >= 3 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="aligning" class="process-icon" />
                        <i v-if="item.status >= 3" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="
                        item.agent_scene_code === 'rule_generation_scene'
                          ? '规则生成'
                          : '能力代码生成'
                      "
                      placement="top"
                    >
                      <div :class="item.status >= 4 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="code" class="process-icon" />
                        <i v-if="item.status >= 4" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="能力测试与迭代" placement="top">
                      <div :class="item.status === 5 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="debug" class="process-icon" />
                        <i v-if="item.status === 5" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                  </div>
                  <div
                    v-else-if="item.agent_scene_code == 'other_assistant_scene'"
                    class="itemProcess"
                  >
                    <el-tooltip class="item" effect="dark" content="生成方案" placement="top">
                      <div :class="item.status >= 1 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planfangan" class="process-icon" />
                        <i v-if="item.status >= 1" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                  </div>
                  <div
                    v-else-if="
                      item.agent_scene_code == 'digital_twin_assistant_scene' ||
                      item.agent_scene_code == 'operational_optimization_scene' ||
                      item.agent_scene_code == 'device_ops_assistant_scene' ||
                      item.agent_scene_code === 'sop_scene' ||
                      item.agent_scene_code === 'visit_leader_cognition_scene' ||
                      item.agent_scene_code === 'intelligent_conversation_scene' ||
                      item.agent_scene_code === 'artificial_handle_scene' ||
                      item.agent_scene_code === 'rule_generation_scene' ||
                      item.agent_scene_code === 'custom_cognition_assistant_scene' ||
                      item.agent_scene_code === 'device_ops_assistant_scene-v1'
                    "
                    class="itemProcess"
                  >
                    <el-tooltip class="item" effect="dark" content="生成方案" placement="top">
                      <div :class="item.status >= 1 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planfangan" class="process-icon" />
                        <i v-if="item.status >= 1" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>

                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="执行任务" placement="top">
                      <div :class="item.status >= 2 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="siwei" class="process-icon" />
                        <i v-if="item.status >= 2" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip
                      v-if="item.agent_scene_code == 'operational_optimization_scene'"
                      class="item"
                      effect="dark"
                      content="能力测试与迭代"
                      placement="top"
                    >
                      <div :class="item.status === 7 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="aligning" class="process-icon" />
                        <i v-if="item.status === 7" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <el-tooltip
                      v-else
                      class="item"
                      effect="dark"
                      content="能力测试与迭代"
                      placement="top"
                    >
                      <div :class="item.status === 3 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="aligning" class="process-icon" />
                        <i v-if="item.status === 3" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                  </div>
                  <div v-else class="itemProcess">
                    <el-tooltip class="item" effect="dark" content="生成方案" placement="top">
                      <div :class="item.status >= 1 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planfangan" class="process-icon" />
                        <i v-if="item.status >= 1" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="生成任务" placement="top">
                      <div :class="item.status >= 2 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planrenwu" class="process-icon" />
                        <i v-if="item.status >= 2" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                    <div class="processLine"></div>
                    <el-tooltip class="item" effect="dark" content="执行任务" placement="top">
                      <div :class="item.status === 3 ? 'processItem success' : 'processItem'">
                        <SvgIcon name="planzhixingrenwu" class="process-icon" />
                        <i v-if="item.status === 3" class="el-icon-success"></i>
                      </div>
                    </el-tooltip>
                  </div>
                  <div v-if="$route.name !== 'planGenerateIndex'" class="contentDesc">
                    <div class="descLabel">空间名称：</div>
                    <div class="descValue">
                      {{ item.workspaceName || '--' }}
                    </div>
                  </div>
                  <div class="contentDesc">
                    <div class="descLabel">使用场景：</div>
                    <div class="descValue">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="
                          item.agent_scene_code_name
                            ? item.agent_scene_code_name +
                              (item.agent_scene_name ? '/' + item.agent_scene_name : '')
                            : item.agent_scene_name
                        "
                        placement="top"
                      >
                        <div class="descValueMax">
                          {{
                            item.agent_scene_code_name
                              ? item.agent_scene_code_name +
                                (item.agent_scene_name ? '/' + item.agent_scene_name : '')
                              : item.agent_scene_name
                          }}
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="contentDesc">
                    <div class="descLabel">描述：</div>
                    <div class="descValue">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="item.description ? item.description : '--'"
                        placement="top"
                      >
                        <div class="descValueMax">
                          {{ item.description || '--' }}
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                  <div v-if="item.tag && item.tag.length" class="contentDesc">
                    <div class="descValue" style="max-width: calc(100%); flex: 1">
                      <selectItem
                        :array-items="
                          item.tag?.map((tag) => {
                            return { key: tag.id, label: tag.name }
                          })
                        "
                        :max-length="2"
                        @clickFun="handleTagClick"
                      ></selectItem>
                    </div>
                  </div>
                </div>
              </div>
              <div class="itemFooter">
                <div class="footerLeft">
                  <div class="item">
                    <el-tooltip effect="dark" content="收藏量" placement="top">
                      <img
                        :src="
                          item.is_collected
                            ? require('@/assets/images/planGenerater/uncollect.png')
                            : require('@/assets/images/planGenerater/collect.png')
                        "
                        @click="collectDebounce(item)"
                      />
                    </el-tooltip>
                    <span>{{ item.collector || 0 }}</span>
                  </div>
                  <div class="item">
                    <el-tooltip effect="dark" content="调用量" placement="top">
                      <img :src="require('@/assets/images/planGenerater/callNum2.png')" />
                    </el-tooltip>
                    <span>{{ item.callNum || 0 }}</span>
                  </div>
                  <div class="item">
                    <el-tooltip effect="dark" content="浏览量" placement="top">
                      <img :src="require('@/assets/images/planGenerater/preview.png')" />
                    </el-tooltip>
                    <span>{{ item.view || 0 }}</span>
                  </div>
                </div>
                <div>
                  <el-button
                    v-if="item.agent_scene_code !== 'digital_twin_assistant_scene'"
                    type="info"
                    :disabled="
                      (item.visibility === 'private' &&
                        item.username !== userInfo.username &&
                        !isAdmin) ||
                      (item.visibility === 'share' && !item.is_shared && !isAdmin)
                    "
                    @click="checkInto(item)"
                  >
                    开始
                  </el-button>
                  <!-- <el-button
                    v-if="item.agent_scene_code == 'digital_twin_assistant_scene'"
                    :disabled="
                      (item.visibility === 'private' &&
                        item.username !== userInfo.username &&
                        !isAdmin) ||
                      (item.visibility === 'share' && !item.is_shared && !isAdmin)
                    "
                    type="info"
                    @click="developFn(item)"
                  >
                    研发进入
                  </el-button> -->
                  <!-- <el-button
                    v-if="item.agent_scene_code == 'digital_twin_assistant_scene'"
                    :disabled="
                      (item.visibility === 'private' &&
                        item.username !== userInfo.username &&
                        !isAdmin) ||
                      (item.visibility === 'share' && !item.is_shared && !isAdmin)
                    "
                    type="info"
                    @click="expertFn(item)"
                  >
                    专家进入
                  </el-button> -->
                </div>
              </div>
            </div>
          </div>
          <div v-else class="cardEmpty">
            <el-empty description="暂无方案"></el-empty>
          </div>
          <!-- 分页部分 -->
          <div style="text-align: right; padding-top: 16px">
            <el-pagination
              class="new-paper"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="[12, 24, 36, 48, 60]"
              :current-page.sync="tableData.page"
              :page-size="tableData.pageSize"
              :total="tableData.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <devlopModal
      :is-visible="devlopModalVisable"
      :cur-id="curId"
      :dev-person="devPersonInfo"
      @close="handleClose"
    />
    <editModal :id="editId" :edit-data="editData" :visible="createVisable" @close="handleCreate" />
  </div>
 </template>

 <script>
 import conditionTag from '@/components/conditionTag/index.vue'
 import hearderContainer from '@/components/hearderContainer/index.vue'
 import {
  abilityStatistics,
  SchemeStatusList,
  SchemeList,
  DeleteScheme,
  queryDictConfig,
  agentSenceList,
  getUserInfo,
  queryUseTags,
  queryCallCountStatistic,
  queryUseTagsWithCount,
  saveRel,
  deleteRel,
  schemeListCount,
  getSencetVisibleList,
  updateSchemeSort
 } from '@/api/planGenerateApi.js';
 import { mapGetters } from 'vuex';
 import { debounce } from 'lodash';
 import editModal from './edit.vue';
 import selectItem from './selectItem.vue';
 import Vue from 'vue';
 import Status from '@/components/Status/index.vue';
 import devlopModal from './wlls/devlopModal.vue';
 const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : Vue.prototype.userInfo
  ? Vue.prototype.userInfo
  : {};
 let title = '';
 if (sessionStorage.getItem('ACCOUNT_INFO')) {
  const account = JSON.parse(sessionStorage.getItem('ACCOUNT_INFO'));
  const titleParts = account.postName.split('/');
  title = titleParts[titleParts.length - 2];
 } else {
  title = '';
 }
 const wid = localStorage.getItem('currentWorkSpace')
  ? JSON.parse(localStorage.getItem('currentWorkSpace')).workspaceId
  : '';
 export default {
  components: {
    editModal,
    selectItem,
    Status,
    devlopModal,
    hearderContainer,
    conditionTag
  },
  data() {
    return {
      total: 0,
      itemsPerLine: 0,
      showExpandButton: false,
      totalTagWidth: 0,
      tagItemsLength: [],
      isExpanded: false,
      showCustomSearch: false, // 控制自定义搜索弹出框的显示与隐藏
      abilityStatisticsList: [],
      selectedFilters: [], // 选中的过滤条件
      turnFull: false,
      tableTypeOptions: [
        {
          label: '已完成',
          value: '3'
        }
      ],
      tableTypeData: {
        total: 0,
        developing: 0,
        notVerified: 0,
        completed: 0,
        list: []
      },
      showDataType: 'table',
      circleUrl: userInfo.headImageUrl,
      userInfo: {
        name: userInfo.nickName,
        title: title
      },
      capabilityDevelopInfo: [
        {
          title: '我的能力',
          value: 28,
          type: 'share'
        },
        {
          title: '待研发能力',
          value: 155,
          type: ''
        },
        {
          title: '已上线能力',
          value: 19,
          type: ''
        },
        {
          title: '能力调用次数',
          value: 9,
          type: ''
        }
      ],
      spaceMap: {},
      spaceList: [],
      developTaskStatus: {
        un_validate: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '待验证' },
        todo: { bgColor: '#ebeffa', dotColor: '#4068d4', text: '研发中' },
        online: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已完成' }
      },
      statusTypeMap: {
        offline: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        online: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' },
        un_publish: { bgColor: '#ebeffa', dotColor: '#4068d4', text: '未发布' }
      },
      wid: '',
      senceTypeList: [],
      checkedTypeTags: [],
      modelType: 1, // 默认是标准模式
      adminFlag: false,
      myTotal: 0,
      allTotal: 0,
      collectTotal: 0,
      pendingTotal: 0,
      isFilterFlag: true,
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      tableLoading: false, // 加载状态
      loading: false,
      statusList: [],
      userList: [],
      sceneList: [],
      formData: {
        workspaceId: '',
        status: '',
        tags: [],
        agent_scene: [],
        name: '',
        createUserId: '',
        contributors: ''
      },
      devPersonInfo: {},
      curId: '',
      selectTags: [],
      tableData: {
        process_status: '3',
        list: [], // 表格数据
        page: 0,
        pageSize: 12,
        total: 0,
        allist: [],
        allTotal: 0
      },
      searchForm: {
        searchKey: '' // 根据名称模糊搜索
      },
      createVisable: false,
      devlopModalVisable: false,
      editId: undefined,
      editData: {},
      SchemeToken: '',
      justMine: 0,
      allTags: [], // 所有标签
      tagsWithStatistic: [], // 带统计数量的标签
      checkedTags: [], // 筛选选择的标签
      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          console.log('node', node)
          const { level } = node
          if (level === 0) {
            queryDictConfig({ business_type: 'scene_type' }).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                const result = res.data.result?.config.map((item) => {
                  return {
                    value: item.code,
                    label: item.name
                  }
                })
                resolve(result || [])
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
          } else {
            console.log('ERJI')
            getSencetVisibleList({
              keyword: '',
              user_id: userInfo.userId,
              scene_type: node.data.value,
              workspace_id: wid
            })
              .then((res) => {
                console.log(res)
                const result = res.data?.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    leaf: true
                  }
                })
                console.log('result', result)
                resolve(result || [])
              })
              .catch(() => {
                resolve([])
              })
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    })
  },
  watch: {
  },
  async created() {
    await this.queryIsAdmin()
    await this.getAbilityStatistics()
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    this.showDataType = this.$route.query?.showDataType || 'table'
    await this.getSpaceList()
    document.getElementsByClassName('el-pagination__jump')[0].childNodes[0].nodeValue = '跳转至'
    await this.queryStatusData('')
    this.querySceneList()
    this.queryTagsWithCount()
    this.queryTableCount()
  },
  methods: {
    getAbilityStatistics() {
      abilityStatistics().then(res=> {
        if(res.data.code === 200) {
          this.capabilityDevelopInfo[3].value = res.data?.result?.call_size
          this.capabilityDevelopInfo[0].value = res.data?.result?.mine
          this.capabilityDevelopInfo[2].value = res.data?.result?.online_ability
          this.capabilityDevelopInfo[1].value = res.data?.result?.todo
          this.abilityStatisticsList = res.data.result
        }
      }).catch(err =>{
      this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
    })
    },
    handleDevelopTaskStatus(row) {
      if (row.agent_scene_code === 'other_assistant_scene') {
        return row.scheme_status === 1 ? 'online' : 'todo'
      } else {
        return row.is_publish
          ? 'online'
          : row.scheme_status === 6 ? 'un_validate' : 'todo'
      }
    },
    handleChangeSize() {
      this.turnFull = !this.turnFull
    },
    async getSpaceList() {
      const vm = this
      const res = await vm.$axios.post(`${vm.baseUrl}/workspace/getWorkspacesByUserInfo`, {
        page: 1,
        pageSize: 200
      })
      if (res && res.data && res.data.status === 200) {
        this.spaceList = res.data.data
        this.spaceList.forEach((item) => {
          this.spaceMap[item.id] = item
        })

        console.log('空间列表', this.spaceMap)
      }
    },
    async checkInto(item) {
      console.log('进入判断', item.agent_scene_id)
      if (
        (item.visibility === 'private' && item.username !== userInfo.username && !this.isAdmin) ||
        (item.visibility === 'share' && !item.is_shared && !this.isAdmin)
      ) {
        this.$message({
          type: 'warning',
          message: '没有权限查看！'
        })
      } else {
        if (item.agent_scene_code) {
          if (item.agent_scene_code === 'digital_twin_assistant_scene') {
            this.saveRelHandle(item.id, 'view', item.name, '专家进入')
            this.$router.push({
              path: '/planGenerate/ConfTaskPlanchat',
              // path: '/planGenerate/wllsExpertPlanchat',
              query: {
                ...this.$route.query,
                status: item.status,
                id: item.id,
                enterType: 'expert',
                fromMenu:'4',
                ability_id: item?.ability_id
              }
            })
          } else {
            if (item.agent_scene_code === 'operational_optimization_scene') {
              this.saveRelHandle(item.id, 'view', item.name, '开始')
              this.$router.push({
                path: '/planGenerate/ConfTaskPlanchat',
                // path: '/planGenerate/hqwPlanchat',
                query: {
                  ...this.$route.query,
                  id: item.id,
                  fromMenu:'4',
                  status: item.status,
                  ability_id: item?.ability_id
                }
              })
            } else if (
              item.agent_scene_code == 'device_ops_assistant_scene' ||
              item.agent_scene_code === 'sop_scene' ||
              item.agent_scene_code === 'visit_leader_cognition_scene' ||
              item.agent_scene_code === 'intelligent_conversation_scene' ||
              item.agent_scene_code === 'artificial_handle_scene' ||
              item.agent_scene_code === 'rule_generation_scene' ||
              item.agent_scene_code === 'custom_cognition_assistant_scene' ||
              item.agent_scene_code === 'device_ops_assistant_scene-v1'
            ) {
              this.saveRelHandle(item.id, 'view', item.name, '开始')
              this.$router.push({
                path: '/planGenerate/ConfTaskPlanchat',
                query: {
                  ...this.$route.query,
                  id: item.id,
                  fromMenu:'4',
                  status: item.status,
                  ability_id: item?.ability_id
                }
              })
            } else {
              this.saveRelHandle(item.id, 'view', item.name, '开始')
              this.$router.push({
                path: '/planGenerate/planchat',
                query: {
                  ...this.$route.query,
                  id: item.id,
                  fromMenu:'4',
                  status: item.status,
                  ability_id: item?.ability_id
                }
              })
            }
          }
        } else {
          this.$message({
            type: 'warning',
            message: '当前场景已删除！'
          })
        }
      }
    },
    querySceneList() {
      queryDictConfig({ business_type: 'scene_type' }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.senceTypeList = res.data.result?.config || [];
          this.$nextTick(()=> {
            this.$refs.conditionTags.observeResize();
          })
        } else {
          this.senceTypeList = []
        }
      })
    },
    developFn(item) {
      this.saveRelHandle(item.id, 'view', item.name, '研发进入')
      this.$router.push({
        path: '/planGenerate/wllsDevPlanchat',
        query: {
          ...this.$route.query,
          status: item.status,
          id: item.id,
          enterType: 'develop',
          ability_id: item?.ability_id
        }
      })
    },
    expertFn(item) {
      this.saveRelHandle(item.id, 'view', item.name, '专家进入')
      this.$router.push({
        path: '/planGenerate/ConfTaskPlanchat',
        // path: '/planGenerate/wllsExpertPlanchat',
        query: {
          ...this.$route.query,
          status: item.status,
          id: item.id,
          enterType: 'expert',
          ability_id: item?.ability_id
        }
      })
    },
    statusMap(code, status) {
      console.log(`statusMap===========${code}=====${status}`)
      if (
        [
          'device_ops_assistant_scene',
          'device_ops_assistant_scene-v1',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'rule_generation_scene',
          'intelligent_conversation_scene',
          'sop_scene',
          'custom_cognition_assistant_scene'
        ].indexOf(code) > -1
      ) {
        if (status === 1) {
          return '生成方案'
        } else if (status === 2) {
          if (code === 'custom_cognition_assistant_scene') {
            return '思维图生成'
          } else {
            return '思维树生成'
          }
        } else if (status === 3) {
          return '多模态数据对齐'
        } else if (status === 4) {
          return '能力代码生成'
        } else if (status === 5) {
          return '能力测试与迭代'
        } else {
          return '--'
        }
      } else if (code === 'other_assistant_scene') {
        if (status === 1) {
          return '生成方案'
        } else {
          return '--'
        }
      } else if (code === 'digital_twin_assistant_scene') {
        // 针对数字孪生
        return status === 1
          ? '生成方案'
          : status === 2
          ? '执行任务'
          : status === 3
          ? '能力测试与迭代'
          : '--'
      } else if (code === 'operational_optimization_scene') {
        // 针对数学模型生成
        return status === 1
          ? '生成方案'
          : status >= 2 && status <= 6
          ? '执行任务'
          : status === 7
          ? '能力测试与迭代'
          : '--'
      } else {
        if (status === 1) {
          return '生成方案'
        } else if (status === 2) {
          return '生成任务'
        } else if (status === 3) {
          return '执行任务'
        } else {
          return '--'
        }
      }
    },
    async queryIsAdmin() {
      const data = await this.$post(this.baseUrl + '/user/isAdminForCurUser')
      this.adminFlag = data
    },
    collectDebounce: debounce(function (item) {
      this.collectHandle(item)
    }, 1000),
    async collectHandle(item) {
      if (this.loading) {
        return false
      }
      this.loading = true
      if (!item.is_collected) {
        await this.saveRelHandle(item.id, 'collector')
        this.loading = false
      } else {
        await deleteRel({
          biz_id: item.id,
          biz_type: 'collector'
        })
          .then((res) => {
            console.log(res, '000')
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.$message({
                type: 'success',
                message: res.data?.msg || '取消收藏'
              })
              this.queryTableData(this.justMine)
              this.queryTableCount()
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    saveRelHandle(id, status, name, actionName) {
      saveRel({
        biz_id: id,
        biz_type: status
      }).then((res) => {
        console.log(res, '000')
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          if (status === 'collector') {
            this.$message({
              type: 'success',
              message: res.data?.msg || '收藏成功'
            })
          }
          this.queryTableData(this.justMine)
          this.queryTableCount()
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleTagClick(tag) {
      console.log('收到消息', tag)
      this.checkedTags = [tag]
      this.handlSearch()
    },
    selectType(code) {
      if(Object.prototype.toString.call(code) === '[object Object]') {
        if(code.code) {
          code = code.code
        }else if(code.id) {
          code = code.id
        }
      }
      // 删除
      if (this.checkedTypeTags.indexOf(code) > -1) {
        this.checkedTypeTags = []
      } else {
        // 增加
        this.checkedTypeTags = [code]
      }
      console.log('场景类型', this.checkedTypeTags)
      this.handlSearch()
    },
    selectTag(tag) {
      if(Object.prototype.toString.call(tag) === '[object Object]') {
        if(tag.code) {
          tag = tag.code
        }else if(tag.id) {
          tag = tag.id
        }
      }
      // 删除
      if (this.checkedTags.indexOf(tag) > -1) {
        const temp = []
        this.checkedTags.forEach((item) => {
          if (item != tag) {
            temp.push(item)
          }
        })
        this.checkedTags = temp
      } else {
        // 增加
        this.checkedTags.push(tag)
      }
      console.log('选额的', this.checkedTags);
      this.handlSearch();
    },
    selectUserFn(data) {
      let formattedUsers = []
      if (data?.length > 0) {
        formattedUsers = data.map((user, index) => {
          const label = `${user.nickname}(${user.loginName})`
          return { key: index + 1, label }
        })
      }
      return formattedUsers
    },
    selectTagFn(data) {
      let formattedUsers = []
      if (data?.length > 0) {
        formattedUsers = data.map((user, index) => {
          const label = `${user.nickname}(${user.loginName})`
          return { key: index + 1, label }
        })
      }
      return formattedUsers
    },
    async handleCommand(type, item) {
      console.log(type, item, 'ppo')
      if (type === 'task') {
        this.$router.push({
          path: '/planGenerate/task',
          query: {
            ...this.$route.query,
            name: item.name,
            id: item.id,
            agent_scene_name: item.agent_scene_name,
            create_time: item.create_time,
            desc: item.description,
            agent_scene_code_name: item.agent_scene_code_name
          }
        })
      } else if (type === 'edit') {
        this.editId = item.id
        this.editData = item
        this.$router.push({
          path: '/planGenerate/edit',
          query: { ...this.$route.query, id: item.id }
        })
      } else if (type === 'remove') {
        this.handleDelete(item)
      } else if (type === 'develop') {
        this.devPersonInfo = item.developer
        this.curId = item.id + ''
        this.devlopModalVisable = true
      } else if (type === 'top') {
        this.tableLoading = true
        await updateSchemeSort(item.id, 1, { workspaceId: this.formData.workspaceId })
        this.queryTableData(this.justMine)
      } else if (type === 'cancelTop') {
        this.tableLoading = true
        await updateSchemeSort(item.id, 0, { workspaceId: this.formData.workspaceId })
        this.queryTableData(this.justMine)
      }
    },
    searchUser(userName, callback) {
      getUserInfo({ nickName: userName }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.userList = res.data.result
        }
      })
    },
    handleChangeMine() {
        this.tableData.page = 1;
      this.queryTableData();
      this.queryTableCount();
    },
    changeMine(val) {
      // 只看我
      this.tableData.page = 1
      this.justMine = val
      this.queryTableData(val)
      this.queryTableCount()
    },
    changeProcessStatus(val) {
      this.tableData.process_status = val
      this.tableData.page = 1
      this.queryTableData()
      this.queryTableCount()
    },
    handlSearch() {
      this.tableData.page = 1
      this.queryTableData(this.justMine)
      this.queryTableCount()
    },
    handleReset() {
      this.tableData.page = 1
      this.justMine = 0
      this.formData.status = ''
      this.formData.agent_scene = ''
      this.formData.name = ''
      this.formData.createUserId = ''
      this.formData.contributors = ''
      this.formData.tags = []
      this.formData.workspaceId = ''
      this.checkedTags = []
      this.checkedTypeTags = []
      this.queryTableData(this.justMine)
      this.queryTableCount()
    },
    changeScene(val) {
      console.log('val', val)
    },
    async queryStatusData(scene) {
      this.tableLoading = true
      await SchemeStatusList({ scene: scene }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.statusList = res.data.result?.items || []
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
      await agentSenceList({ business_type: 'scene_type' }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.sceneList = res.data.result || []
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
      await this.queryTableData(this.justMine)
    },
    queryTableCount() {
      let tags = this.formData.tags
      if (this.checkedTags != null && this.checkedTags.length > 0) {
        tags = this.formData.tags.concat(this.checkedTags)
      }
      const obj = {
        offset: this.tableData.page,
        limit: this.tableData.pageSize,
        sort_field: 'create_time',
        order: 'desc',
        status: this.formData.status,
        agent_scene: this.formData.agent_scene[1] || '',
        name: this.formData.name,
        agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
        contributors: this.formData.contributors
      }
      const params = {
        tag_ids: tags,
        filter_data: {
          all: {
            ...obj,
            user_id: this.formData.createUserId
          },
          mine: {
            ...obj,
            user_id: userInfo.userId || ''
          },
          collector: {
            ...obj,
            user_id: this.formData.createUserId,
            myCollect: '1'
          },
          task: {
            ...obj,
            user_id: this.formData.createUserId,
            myCollect: '2'
          }
        }
      }
      schemeListCount(params, { workspaceId: this.formData.workspaceId })
        .then(async (res) => {
          const { data = {}, status } = res || {}
          if (status === 200 && data.code === 200) {
            this.myTotal = data.result.mine
            this.allTotal = data.result.all
            this.collectTotal = data.result.collector
            this.pendingTotal = data.result.task || 0
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
        .finally(() => {})
    },
    queryTableData(type) {
      // if (this.formData.createUserId) {
      //   this.justMine = 0;
      // }
      this.tableLoading = true
      let tags = this.formData.tags
      if (this.checkedTags != null && this.checkedTags.length > 0) {
        tags = this.formData.tags.concat(this.checkedTags)
      }

      const param = {
        offset: this.tableData.page,
        limit: this.tableData.pageSize,
        sort_field: 'create_time',
        order: 'desc',
        status: this.formData.status,
        agent_scene: this.formData.agent_scene[1] || '',
        name: this.formData.name,
        agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
        // user_id: type === 1 ? userInfo.userId || '' : this.formData.createUserId,
        user_id: this.formData.createUserId !== '' ? this.formData.createUserId : (userInfo.userId || ''),
        tag_ids: tags,
        contributors: this.formData.contributors,
        myCollect: type === 2 ? '1' : type === 3 ? '2' : '0',
        process_status: this.tableData.process_status
      };
       if (this.selectedFilters.includes('myAbility')) {
        // param.user_id = userInfo.userId
       } else {
         if (!this.formData?.createUserId) {
           param.user_id = ''
         }
       }
      if (this.selectedFilters.includes('myCollect')) {
          param.myCollect = '1'
        } else {
          param.myCollect = '0'

      }
      SchemeList(param, { workspaceId: this.formData.workspaceId })
        .then(async (res) => {
          const { data = {}, status } = res || {}
          if (status === 200 && data.code === 200) {
            const list = data.result?.items || []
            // list[0].agent_scene_code ='digital_twin_assistant_scene'
            const schemeIds = list?.map((item) => item.id)
            let results = {}
            let statisticsData = {}
            if (this.$route.name === 'planGenerateIndex') {
              results = await queryCallCountStatistic({ schemeIds })
              statisticsData = results?.data?.data || {}
            }
            this.tableTypeData.list = list
            this.tableData.list = list?.map((item) => ({
              ...item,
              callNum: statisticsData[item.id] || 0,
              workspaceName: this.spaceMap[item.work_space_id]?.workspaceName
            }))
            console.log(this.tableData.list, '表格列表')
            this.tableData.total = data.result.total
          } else {
            this.tableData.list = []
            this.tableData.total = 0
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.tableData.list = []
          this.tableData.total = 0
          console.log(_err)
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // async generateSignByObjectKey(urlKey) {
    //   return await this.$post('platform/obs/generateSignByObjectKey', {
    //     objectKey: urlKey
    //   }).then((res) => {
    //     return res;
    //   });
    // },
    handleSizeChange(val) {
      this.tableData.page = 1
      this.tableData.pageSize = val
      this.queryTableData(this.justMine)
      this.queryTableCount()
    },
    handleCurrentChange(val) {
      this.tableData.page = val
      this.queryTableData(this.justMine)
      this.queryTableCount()
    },
    handleCreate() {
      this.createVisable = false
      this.queryTagsWithCount()
      this.queryTableCount()
      this.queryTableData(this.justMine)
    },
    handleClose(val) {
      if (val) {
        this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'success'
        })
          .then(() => {
            this.queryTableData(this.justMine)
            this.queryTableCount()
          })
          .catch(() => {
            this.queryTableData(this.justMine)
            this.queryTableCount()
          })
      }
      this.devlopModalVisable = false
    },
    computeTask(tasks) {
      let indexNumber = 0
      tasks.forEach((item, index) => {
        if (item.status === 'complete') {
          indexNumber = index
        }
      })
      if (tasks.length < indexNumber + 3) {
        indexNumber = tasks.length - 3
      }
      const narr = indexNumber >= 3 ? tasks.slice(indexNumber - 3, indexNumber) : tasks.slice(0, 3)
      if (indexNumber < 3) {
        indexNumber = 3
      } else {
        if (indexNumber - 2 > 1) {
          const moreText = tasks
            .slice(0, indexNumber - 2)
            .map((item) => item.name)
            .join(',')
          narr[0] = {
            description: '+' + (indexNumber - 2),
            name: moreText,
            moreFlag: true,
            status: ''
          }
        }
      }
      const nnarr =
        tasks.length < indexNumber + 3
          ? tasks.slice(indexNumber + 3, tasks.length)
          : tasks.slice(indexNumber, indexNumber + 3)

      if (tasks.length > indexNumber + 3) {
        const moreText = tasks
          .slice(indexNumber + 2, tasks.length)
          .map((item) => item.name)
          .join(',')
        nnarr[nnarr.length - 1] = {
          description: '+' + (tasks.length - (indexNumber + 2)),
          name: moreText,
          moreFlag: true,
          status: ''
        }
      }

      return [...narr, ...nnarr]
    },
    handleDelete(row) {
      this.$confirm('此操作将删除该方案及任务，是否继续?', '删除', {
        customClass: 'last-dialog',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          DeleteScheme({ id: row.id })
            .then((res) => {
              this.tableLoading = false
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.queryTagsWithCount()
                this.queryTableData(this.justMine)
                this.queryTableCount()
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
            .finally(() => {
              this.tableLoading = false
            })
        })
        .catch(() => {
          this.tableLoading = false
          this.queryTableData(this.justMine)
          this.queryTableCount()
        })
    },
    queryAllTag(keyword) {
      this.tableLoading = true
      queryUseTags({ keyword: keyword })
        .then((res) => {
          this.tableLoading = false
          if (res.data) {
            this.selectTags = res.data
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    queryTagsWithCount() {
      this.tableLoading = true
      queryUseTagsWithCount({ keyword: '' })
        .then((res) => {
          this.tableLoading = false
          if (res.data) {
            const results = res.data;
            this.allTags = results;
            this.$nextTick(()=> {
            this.$refs.conditionTag.observeResize();
          })
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleProject(row) {
      this.dataDetail = row
      this.bindVisable = true
    },
    handleTagDialog(row) {
      this.dataDetail = row
      this.tagVisable = true
    }
  }
 }
 </script>
 <style lang="scss" scoped>
 .box-card {
  // margin-bottom: 16px;
  margin: 16px 16px 0px 16px;
  border: none;
  overflow: unset;
  :deep(.el-card__body) {
    padding: 16px 0px 0px 0px;
  }
}
 .modelType {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 14px;
  color: #4068d4;
  line-height: 22px;
  position: relative;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    right: -8px;
    height: 12px;
    top: 6px;
    width: 1px;
    background: #c8c9cc;
  }
 }

 .planContainer {
  max-height: calc(100vh - 90px);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;

  &.planContainerFrame {
    max-height: calc(100vh) !important;

    .containerCard {
      &.containerCardSmall {
        height: calc(100% - 0px) !important;
        max-height: calc(100% - 0px) !important;
      }
    }
  }

  .sence-tag {
    display: inline-flex;
    padding: 0 8px;
    height: 24px;
    border-radius: 2px;
    background: #ebf9ff;
    color: #318db8;
  }

  .separate {
    width: 1px;
    height: 12px;
    margin: 0 8px;
    background: #c8c9cc;
  }

  .avator {
    width: 28px;
    height: 28px;
    line-height: 28px;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    background: #e6ecff;
    color: #4068d4;
  }

  .containerCard {
    height: calc(100% - 164px);
    max-height: calc(100% - 164px);
    overflow: hidden;
    margin: 16px 16px !important;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    display: flex;
    .left-content {
      width: 70%;
      background-color: #fff;
      padding: 16px 20px;
      position: relative;
      .table-content,.card-content  {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
      .icon-content {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 50%;
        i {
          font-size: 20px;
        }
      }
      .icon-content-card {
        right: 0;
      }
    }
    .left-content-max {
      width: 100%;
    }
    .right-content {
      width: 30%;
      margin-left: 20px;
      background-color: #fff;

      .user {
        display: flex;
        justify-content: flex-start;
        padding: 24px 15px;
        border-bottom: 1px solid #ebecf0;
        .user-info {
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;
          span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #323233;
            font-style: normal;
          }
          .title {
            display: flex;
            align-items: center;
            background: #e6ecff;
            border-radius: 2px;
            padding: 1px 8px;
            span {
              font-size: 14px;
              color: #3455ad;
            }
          }
        }
      }
      .capability-develop {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 22px 23px;
        .title {
          img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
          span {
            font-size: 14px;
            font-weight: 500;
            font-family: fangsong;
            color: #000;
          }
        }
        .info {
          margin-top: 10px;
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 10px;
          flex-wrap: wrap;
          .info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 50%;
            margin-bottom: 40px;
            span {
              font-weight: 400;
              font-size: 14px;
              color: #646566;
            }
            .value {
              margin-top: 20px;
              font-weight: 600;
              font-size: 24px;
              color: #323233;
            }
          }
        }
      }
    }
    .right-content-min {
      width: 0;
    }
    &.containerCardSmall {
      height: calc(100% - 82px) !important;
      max-height: calc(100% - 82px) !important;
    }

    .cardHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;

      ::v-deep .el-radio-button__inner {
        height: 30px;
        line-height: 19px;
        padding: 4px 16px;
        font-size: 14px;
        border-radius: 2px 0 0 2px;
      }

      .title {
        color: #323233;
        font-size: 16px;
        line-height: 30px;
      }

      .searchRight {
        float: right;
        display: flex;
        align-items: center;
        .displayType {
          display: flex;
          align-items: center;
          color: #4068d4;
          margin-right: 10px;
          cursor: pointer;
        .quick-search {
          margin-right: 10px;
          .el-checkbox-group {
            display: flex;
            align-items: center;
            .el-checkbox {
              margin-right: 10px;
            }
          }
        }
        }
        .button-last {
          line-height: 14px;
        }
      }
    }

    .cardEmpty {
      height: calc(100% - 96px);
      margin-top: 16px;
    }

    .cardContent {
      // width: calc(100% + 12px);
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-content: flex-start;

      &.cardContent {
        // height: calc(100% - 96px);
        // max-height: calc(100vh - 300px);
      }

      &.cardContentFrame {
        height: calc(100vh - 190px) !important;
        max-height: calc(100vh - 190px) !important;
      }

      .cartItem {
        flex-basis: calc(33.3333% - 12px);
        // max-width: calc(33.3333% - 12px);
        margin-right: 12px;
        max-height: 323px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dcdde0;
        margin-top: 12px;
        overflow: hidden;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .cartItemBox {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          flex: 1;
          position: relative;
        }

        &:hover {
          box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
        }

        &:active {
          box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
          border-radius: 4px;
          border: 1px solid #406bd4;
        }

        .itemHeader {
          font-size: 16px;
          color: #323232;
          line-height: 24px;
          padding: 13px 16px;
          font-weight: bold;
          border-bottom: 1px solid #ebecf0;

          .itemHeaderTop {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .el-icon-warning-outline {
              color: #4068d4;
              padding: 0 8px 0 0px;
              cursor: pointer;
            }
          }

          .itemHeaderTit {
            width: 100%;
            flex: 1;
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; // 默认不换行；

            &:hover {
              .title {
                color: #4068d4;
              }
            }

            &.isdisabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
            }

            .title {
              max-width: calc(100%);
              font-size: 16px;
              font-weight: bold;
              color: #323233;
              cursor: pointer;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }
          }

          .itemHeaderBtn {
            display: flex;
            flex-direction: row;
            justify-content: end;
            align-items: center;
          }
        }

        .itemContent {
          padding: 0px 16px 16px 16px;

          .contentDesc {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            margin-top: 8px;
            font-weight: normal;
            color: #646566;
            line-height: 22px;
            font-size: 14px;

            .descLabel {
              color: #646566;
              word-break: keep-all;
            }

            .descValue {
              color: #323233;
              line-height: 22px;
              width: calc(100% - 72px);

              .contribution {
                font-size: 14px;

                ::v-deep .tagslist {
                  position: relative;

                  .tagslistLast {
                    position: relative;

                    &::after {
                      width: 1px !important;
                      content: '';
                      position: absolute;
                      left: 0px;
                      width: 1px;
                      height: 12px;
                      background: #c8c9cc;
                      top: 6px;
                    }
                  }

                  &:last-child {
                    &::after {
                      width: 0px;
                    }
                  }

                  &::after {
                    content: '';
                    position: absolute;
                    right: 0px;
                    width: 1px;
                    height: 12px;
                    background: #c8c9cc;
                    top: 6px;
                  }
                }

                ::v-deep .el-tag {
                  color: #323233;
                  background: transparent;
                  font-size: 14px;
                  border: none;
                  padding: 0 3px;
                  position: relative;
                  padding-right: 5px;
                  margin-right: 0px;
                }

                ::v-deep .el-button {
                  background: transparent;
                  border: none;
                }
              }

              .descValueMax {
                max-width: 100%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }

          .itemProcess {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding-top: 12px;
            padding-bottom: 4px;

            .processItem {
              width: 36px !important;
              height: 36px;
              word-break: keep-all;
              border-radius: 4px;
              position: relative;
              background: rgba(235, 236, 240, 0.8);
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;

              .el-icon-success {
                position: absolute;
                top: -4px;
                right: -4px;
                color: #39ab4c;
                font-size: 14px;
              }

              .process-span {
                margin-top: 0px;
              }

              &.success {
                background: #e6faea;

                .process-icon {
                  color: #2ca941 !important;
                }
              }

              &.running {
                background: #e5eaff;

                .process-icon {
                  color: #416cde !important;
                }
              }

              .process-icon {
                width: 16px;
                height: 16px;
                color: #7d7e80;
              }
            }

            .processLine {
              flex: 1;
              margin: 0px 8px;
              height: 1px;
              background: #c8c9cc;
            }
          }
        }

        .itemFooter {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-top: 1px solid #ebecf0;
          font-size: 14px;
          color: #646566;

          & .footerLeft {
            display: flex;

            & .item {
              margin-right: 16px;
            }
          }

          > div {
            display: flex;
            align-items: center;

            img {
              height: 16px;
              width: 16px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
 }

 .planSearch {
  background-color: #fff;
  // padding: 0px 20px 0px;
  position: relative;
 .search {
  margin-top: 10px;
  .planSearch1 {
    padding: 0 16px;
  }
  .footer-search {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .el-input {
    width: 65%;
    margin: 5px 0px 5px 0px;
  }
 }
 }
  .searchIcon {
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);

    .searchIconTaggle {
      width: 40px;
      height: 20px;
      background: #d9e6f8;
      border-radius: 2px;
      position: relative;
      border: 1px solid #f2f3f5;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4068d4;
      cursor: pointer;

      &:hover {
        background: #a1bbef;
      }
    }
  }

  .headerTitle {
    font-weight: bold;
    color: #323233;
    line-height: 26px;
    font-size: 18px;
    padding: 14px 20px;
  }

  .button-last {
    line-height: 14px;
  }

  .search {
    position: relative;
    overflow: hidden;
  }
 }

 .el-popconfirm__action {
  margin-top: 10px;
 }

 ::v-deep .el-card__header {
  border: none;
  padding: 16px 0px 0px;
 }

 ::v-deep .el-cascader {
  line-height: 30px !important;
  height: 30px;

  .el-input {
    height: 30px;
  }
 }

 ::v-deep .el-tooltip__popper {
  &.is-dark {
    background: rgba($color: #323233, $alpha: 0.8) !important;
  }
 }

 ::v-deep .el-button.is-disabled {
  color: rgba($color: #4068d4, $alpha: 0.4);
 }

 ::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  display: flex;
  align-items: center;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
 }

 ::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 8px;
  border-radius: 2px;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
 }

 .name-link {
  cursor: pointer;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap; // 默认不换行；
  color: #4068d4;

  &:hover {
    color: #3455ad;
  }

  &.active {
    color: #264480;
  }
 }

 ::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 7px 6px !important;

  img {
    height: 16px;
    margin-top: -2px;
  }
 }

 :deep(.el-input__inner) {
  border-radius: 2px;
  height: 30px;
  line-height: 30px;
 }

 .btns {
  display: flex;

  .btns-item {
    display: inline-block;
    position: relative;
    padding: 4px 16px;
    // width: 30px;
    // height: 30px;
    line-height: 22px;
    background: #f2f3f5;
    color: #4068d4;
    text-align: center;
    cursor: pointer;

    .view-icon {
      vertical-align: middle;
      color: #3f68d4;
    }

    &:hover {
      background: #eaeaed;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 1px;
      height: 18px;
      background: #dcdde0;
    }

    &:first-child {
      border-radius: 4px 0px 0px 4px;

      &::before {
        content: unset;
      }
    }

    &:last-child {
      border-radius: 0px 4px 4px 0px;
    }

    &.active {
      background: #4068d4;
      color: #ffffff;

      .view-icon {
        color: #ffffff;
      }

      &::before {
        content: unset;
      }
    }
  }
 }
 // 媒体查询，调整不同屏幕尺寸下的卡片宽度
 @media (max-width: 1500px) {
   .cartItem {
     flex-basis: calc(33.3333% - 12px) !important; // 一排显示3个卡片
   }

 }

 @media (max-width: 1280px) {
  .cartItem {
      flex-basis: calc(33.3333% - 12px) !important; // 一排显示3个卡片
    }
 }
 @media (max-width: 1080px) {
  .cartItem {
      flex-basis: calc(50% - 12px) !important; // 一排显示2个卡片
    }
 }
 @media (max-width: 768px) {
   .cartItem {
     flex-basis: calc(100% - 12px) !important; // 一排显示2个卡片
   }
 }

</style>
 <style lang="scss">
 .last-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;

    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-dialog__headerbtn {
      top: 14px;

      .el-dialog__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;

    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-message-box__headerbtn {
      top: 14px;

      .el-message-box__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__content {
    padding: 16px 20px;

    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }

  .el-message-box__btns {
    padding: 0px 20px;

    button {
      width: 60px !important;
    }

    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }

  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }

  .el-dialog__footer {
    padding: 16px 20px;

    .el-button {
      line-height: 20px;
    }
  }

  :deep(.el-input__inner) {
    border-radius: 2px;
    height: 30px;
    line-height: 30px;
  }
 }
 </style>

