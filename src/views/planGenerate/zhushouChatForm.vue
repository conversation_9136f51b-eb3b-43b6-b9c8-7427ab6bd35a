<template>
  <div
    :class="$store.state.planGenerate.isIframeHide ? 'con-form containerBox2IFrame' : 'con-form'"
  >
    <div
      id="left-content"
      :class="
        rightFullFlag ? 'optContent containerCard containerCardFull' : 'optContent containerCard'
      "
      :style="{
        width: leftWidth,
        maxWidth: leftWidth,
        userSelect: isDragging ? 'none' : 'auto',
        transition: isDragging ? 'none' : 'width 0.2s',
        position: thinkFullFlag ? '' : 'relative'
      }"
    >
      <div class="optHeader">
        <div class="rightTitle">思维树</div>
        <div class="rightTitleOpt">
          <el-tooltip
            class="item"
            effect="dark"
            :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'"
            placement="top"
          >
            <el-button type="info" size="mini" @click="changeShowType"
              ><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png" /><img
                v-else
                src="@/assets/images/planGenerater/markdown.png"
            /></el-button>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            :content="rightFullFlag ? '退出全屏' : '全屏'"
            placement="top"
          >
            <el-button
              :type="rightFullFlag ? 'primary' : 'info'"
              size="mini"
              @click="changeShowFull"
              ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                v-else
                src="@/assets/images/planGenerater/tuichuquanping.png"
            /></el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="optScroll">
        <div style="height: 100%; width: 100%" @mouseenter="fangda">
          <template v-if="jueceYulanFlag">
            <MyEditor id="MyEditorForm" ref="MyEditorForm" :md-content="treeData"></MyEditor>
            <!-- <pre v-if="(treeData && treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
            <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
          </template>
          <template v-else>
            <pre>{{ treeData }}</pre>
          </template>
        </div>
      </div>
    </div>
    <div
      v-if="planDetailShow && !rightFullFlag"
      id="resize"
      class="resize"
      title="收缩侧边栏"
      @mousedown="startDrag"
    >
      <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
      <div class="el-two-column__trigger-icon">
        <SvgIcon name="dragborder" class="process-icon" />
      </div>
      <div class="el-two-column__icon-bottom">
        <div class="el-two-column__icon-bottom-bar"></div>
      </div>
    </div>
    <div
      id="right-content"
      :class="
        !planDetailShow
          ? 'optContent form-wrap chatRight chatRightFull'
          : 'optContent form-wrap chatRight'
      "
      :style="{
        width: rightWidth,
        transition: isDragging ? 'none' : 'width 0.2s',
        userSelect: isDragging ? 'none' : 'auto'
      }"
    >
      <div class="optHeader">
        <div class="rightTitle">
          智能能力测试与迭代
          <span
            v-if="multipleSelection?.length && testing"
            style="margin-left: 15px; font-weight: 500; color: chocolate"
            >正在测试第 {{ resultTableData?.length + 1 }}/{{
              multipleSelection?.length
            }}
            条数据</span
          >
        </div>
        <div class="rightTitleOpt">
          <el-button
            type="primary"
            :disabled="testing || !multipleSelection?.length"
            @click="onTest"
            >{{ tried ? '重新测试' : '开始测试' }}</el-button
          >
          <el-tooltip class="item" effect="dark" content="选择要测试数据" placement="top">
            <el-button class="button-last" type="info" @click="openDialog"
              >选择要测试数据</el-button
            >
          </el-tooltip>
          <el-tooltip
            v-if="!rightFullFlag"
            class="item"
            effect="dark"
            :content="!planDetailShow ? '退出全屏' : '全屏'"
            placement="top"
          >
            <el-button
              :type="!planDetailShow ? 'primary' : 'info'"
              size="mini"
              @click="changeShowRight"
              ><img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img
                v-else
                src="@/assets/images/planGenerater/tuichuquanping.png"
            /></el-button>
          </el-tooltip>
        </div>
      </div>
      <div ref="scrollDom" class="form-info">
        <div class="form-c">
          <div class="fm">
            <el-result
              v-if="!tableColumns?.length"
              icon="warning"
              title="请返回第3步进行数据对齐并检查sql语句"
            >
              <template slot="icon">
                <img width="110" src="@/assets/images/e1.png" />
              </template>
            </el-result>
            <el-form ref="formRef" :model="formModal" :rules="formRules" inline label-width="auto">
              <el-row v-for="(column, key) of tableColumns" :key="key" type="flex">
                <!-- <el-col :span="12"> -->
                <el-form-item :label="column?.e_field_desc" :prop="column.d_mapping_field">
                  <el-input
                    v-model.trim="formModal[column.d_mapping_field]"
                    disabled
                    placeholder="请输入"
                    @input="onChange"
                  />
                  <!-- <span class="suffix">{{ key }}</span> -->
                </el-form-item>
                <!-- </el-col>
                <el-col :span="12"> -->
                <el-form-item label="频率" :prop="'pl' + (key + 1)" inline label-width="50px">
                  <div style="display: flex; align-items: center">
                    <el-input
                      v-model.trim="formModal['pl' + (key + 1)]"
                      disabled
                      placeholder="请输入"
                    />
                    <span class="suffix2">s/次</span>
                  </div>
                </el-form-item>
                <!-- </el-col> -->
              </el-row>
            </el-form>
            <div v-if="tried" class="test-result">
              <div>测试结果：</div>
              <span class="test-result-warn"
                >如果对测试结果不满意，可返回到之前的若干步骤重新调整</span
              >
            </div>
            <el-table v-if="tried" :data="resultTableData" border>
              <el-table-column label="数据类型" prop="testData" align="center" />
              <el-table-column label="样本结果" prop="result" align="center" />
              <el-table-column label="预测结果" prop="real" align="center" />
              <el-table-column prop="success" label="预测状态" width="100">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.success" type="success">成功</el-tag>
                  <template v-else>
                    <el-popover
                      placement="top-start"
                      title=""
                      width="200"
                      trigger="hover"
                      :content="scope.row.message"
                    >
                      <el-tag slot="reference" type="danger">失败</el-tag>
                    </el-popover>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <!-- <div v-if="testing || tried">
                <el-alert
                  class="c-alert" :closable="false" show-icon :type="multipleSelectionCopy?.testState ? 'success' : 'info'"
                  :title="'当前测试：' + (multipleSelectionCopy?.sample === 1 ? '正样本数据' : multipleSelectionCopy?.sample === 0 ? '负样本数据' : '当前测试数据')" :description="multipleSelectionCopy?.testState?  '测试成功' : '测试中……'" />
            </div> -->
          </div>
          <!-- <div v-loading="bkgLoading" element-loading-spinner="el-icon-loading" class="bkg-wrap">
            <el-result v-if="bkgUrl" style="position: sticky; top: 0;">
              <template slot="icon">
                <el-image :src="bkgUrl" fit="contain" />
              </template>
              <template slot="extra">
                <el-button v-if="tableColumns?.length" type="primary" :disabled="testing" @click="onTest">{{ tried ? '重新测试' : '开始测试' }}</el-button>
              </template>
            </el-result>
            <el-empty v-else description=" " style="position: sticky; top: 0;">
              <el-button v-if="tableColumns?.length" type="primary" :disabled="testing" @click="onTest">{{ tried ? '重新测试' : '开始测试' }}</el-button>
            </el-empty>
          </div> -->
        </div>
      </div>
      <div class="optFooter">
        <el-button
          class="button-last"
          :disabled="testing"
          type="primary"
          @click="changeViews(activeStep - 1)"
          >上一步</el-button
        >
        <el-button class="button-last" type="info" :disabled="hasChatingName !== ''" @click="save"
          >保存</el-button
        >
        <el-button
          class="button-last"
          type="primary"
          :disabled="lastLoading || hasChatingName !== ''"
          @click="handlePublish"
          >发布到集市</el-button
        >
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" title="请选择测试数据" width="880px">
      <el-table
        ref="multipleTable"
        :data="selectTableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="数据类型" prop="sample_type" align="center" fixed width="120">
          <!-- <template slot-scope="scope">
            {{ scope.row.sample_type === '正' ? '正样本数据' : '负样本数据' }}
          </template> -->
        </el-table-column>
        <el-table-column
          v-for="(column, key) of tableColumns"
          :key="key"
          :label="column?.e_field_desc"
          :prop="column?.d_mapping_field"
          align="center"
          width="120"
        >
          <template slot-scope="scope">{{
            scope.row[column?.d_mapping_field] === null ||
            scope.row[column?.d_mapping_field] === '' ||
            scope.row[column?.d_mapping_field] === undefined
              ? 1
              : scope.row[column?.d_mapping_field]
          }}</template>
        </el-table-column>
        <el-table-column label="样本结果" prop="eee" align="center" width="150">
          <template slot-scope="scope">
            <!-- {{ scope.row.sample_type === '正' ? '故障排查结束-正常' : '检查温控阀是否损坏' }} -->
            {{ scope.row.sample_result }}
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px; display: flex; justify-content: end">
        <el-button type="primary" @click="confirm">确认</el-button>
        <el-button @click="cancelSelection">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="last-dialog"
      :visible.sync="dialogVisible2"
      title="智能能力信息"
      :show-close="!lastLoading"
    >
      <el-dialog
        custom-class="last-dialog"
        :visible.sync="dialogVisible2"
        title="智能能力信息"
        :show-close="!lastLoading"
      >
        <el-form
          ref="lastFormRef"
          element-loading-spinner="el-icon-loading"
          v-loading="dialogContentLoading"
          :model="lastFormModal"
          label-width="120px"
          :rules="lastFormRules"
        >
          <!-- <el-form-item label="名称" prop="name">
            <el-input
              v-model.trim="lastFormModal.name"
              disabled
              maxlength="150"
              placeholder="请输入内容"
            />
          </el-form-item> -->
          <el-form-item label="智能能力图标:" style="margin-top: 0px" prop="abilityIcon">
            <div style="display: flex; height: 60px">
              <el-upload
                ref="uploadBtn"
                class="upload-demo"
                v-loading="formVisible.uploadBtn"
                :action="uploadUrl"
                list-type="picture-card"
                :data="uploadParam"
                :limit="1"
                :file-list="fileList"
                :multiple="false"
                accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                :before-upload="beforeUpload"
                :on-success="modelUploadSuccess"
                :on-exceed="uploadExceed"
              >
                <i slot="default" class="el-icon-plus"></i>
                <div slot="file" slot-scope="{ file }">
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-delete" @click="clearImage">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
              <div style="display: flex; justify-content: flex-start; align-items: flex-end">
                <el-button
                  type="text"
                  icon="el-icon-magic"
                  @click="generateAbilityIcon"
                  style="background-color: transparent; border: none"
                >
                  <svg
                    t="1724402472978"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="5972"
                    id="mx_n_1724402472978"
                    width="17"
                    height="17"
                  >
                    <path
                      d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                      fill="#4068d4"
                      p-id="5973"
                    ></path>
                    <path
                      d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                      fill="#4068d4"
                      p-id="5974"
                    ></path>
                  </svg>
                  AI生成
                </el-button>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-for="(domain, index) in lastFormModal.domains"
            :key="domain.key"
            :label="domain.label"
            :prop="'domains.' + index + '.value'"
            :rules="{
              required: true,
              message: `${domain.label}不能为空`,
              trigger: 'change'
            }"
          >
            <el-input v-if="domain.type === 'input'" v-model.trim="domain.value" />
            <el-cascader
              v-if="domain.type === 'ability'"
              :ref="'domainsSelect' + index"
              v-model="domain.value"
              :options="domain.domainsOptions"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="模式选择" prop="modeSelection">
            <el-radio-group v-model="lastFormModal.modeSelection">
              <el-radio label="dialogue" :disabled="enableDialogModal">对话模式</el-radio>
              <el-radio label="task">任务模式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="lastFormModal.modeSelection === 'dialogue'"
            label="开场白"
            prop="openingStatement"
          >
            <el-input
              v-loading="formVisible.openingStatement"
              v-model="lastFormModal.openingStatement"
              :autosize="{ minRows: 2, maxRows: 6 }"
              type="textarea"
              maxlength="250"
              placeholder="请输入内容"
            />
            <el-button
              type="text"
              icon="el-icon-magic"
              style="
                position: absolute;
                right: 0;
                bottom: 0;
                background-color: transparent;
                border: none;
              "
              @click="aiOpeningStatement"
            >
              <svg
                t="1724402472978"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="5972"
                id="mx_n_1724402472978"
                width="17"
                height="17"
              >
                <path
                  d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                  fill="#4068d4"
                  p-id="5973"
                ></path>
                <path
                  d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                  fill="#4068d4"
                  p-id="5974"
                ></path>
              </svg>
              AI生成
            </el-button>
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              v-model.trim="lastFormModal.description"
              type="textarea"
              maxlength="250"
              placeholder="请输入内容"
              resize="none"
            />
          </el-form-item>
          <div class="info-container" @click="toggleOtherInfo">
            <i
              class="el-icon-arrow-right"
              style="margin-left: 22px"
              :class="{ rotate: lastFormModal.isOtherInfoVisible }"
            ></i>
            其他信息
          </div>
          <div v-show="lastFormModal.isOtherInfoVisible">
            <el-form-item label="智能能力描述:" prop="abilityValue">
              <el-input
                v-loading="formVisible.abilityDesc"
                v-model="lastFormModal.abilityValue"
                type="textarea"
                placeholder="请输入智能能力描述及价值信息"
              ></el-input>
              <el-button
                type="text"
                style="
                  position: absolute;
                  right: 0;
                  bottom: 0;
                  background-color: transparent;
                  border: none;
                "
                @click="abilityDes"
              >
                <svg
                  t="1724402472978"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5972"
                  id="mx_n_1724402472978"
                  width="17"
                  height="17"
                >
                  <path
                    d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                    fill="#4068d4"
                    p-id="5973"
                  ></path>
                  <path
                    d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                    fill="#4068d4"
                    p-id="5974"
                  ></path>
                </svg>
                AI生成
              </el-button>
            </el-form-item>
            <el-form-item label="匹配意图:" prop="abilityIntents">
              <el-input
                v-loading="formVisible.abilityIntents"
                v-model="lastFormModal.abilityIntents"
                type="textarea"
                placeholder="请输入支持的使用方式，例如：用户风险感知模型检测到吗？"
              ></el-input>
              <el-button
                type="text"
                style="
                  position: absolute;
                  right: 0;
                  bottom: 0;
                  background-color: transparent;
                  border: none;
                "
                @click="aiMeMatch"
              >
                <svg
                  t="1724402472978"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5972"
                  id="mx_n_1724402472978"
                  width="17"
                  height="17"
                >
                  <path
                    d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                    fill="#4068d4"
                    p-id="5973"
                  ></path>
                  <path
                    d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                    fill="#4068d4"
                    p-id="5974"
                  ></path>
                </svg>
                AI生成
              </el-button>
            </el-form-item>
            <el-form-item label="标签关键词:" style="width: 100%">
              <div style="display: flex; justify-content: end">
                <el-select
                  ref="tagsSelect"
                  v-model="lastFormModal.tag_ids"
                  multiple
                  filterable
                  placeholder="请选择标签"
                  :filter-method="handleTagFilter"
                  clearable
                  style="width: 100%"
                  @keyup.native.enter="addBizTag"
                  @change="changeTags"
                >
                  <el-option
                    v-for="item in tagList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
              <div style="flex: 1">
                <selectItem
                  :array-items="
                    lastFormModal.ability_market_tags?.map((ability_market_tags) => {
                      return { key: ability_market_tags.id, label: ability_market_tags.name }
                    })
                  "
                  :maxLength="10"
                ></selectItem>
              </div>
            </el-form-item>
          </div>
        </el-form>
        <div style="margin-top: 20px; display: flex; justify-content: end">
          <el-button type="primary" :loading="lastLoading" @click="onPublish">确认</el-button>
          <el-button :disabled="lastLoading" @click="cancel2">取消</el-button>
        </div>
      </el-dialog>
      <div style="margin-top: 20px; display: flex; justify-content: end">
        <el-button type="primary" :loading="lastLoading" @click="onPublish">确认</el-button>
        <el-button :disabled="lastLoading" @click="cancel2">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetDecision,
  SchemeDetail,
  UpdateSchemeStatus,
  QueryAbilityData,
  OnCodeTest,
  SchemeSaveKnow,
  MarketAbilityPublish,
  CodePublish,
  queryDbList,
  querySchemeDetailById,
  getPublishAbilityDich,
  getExecuteSync,
  keywordTag,
  abilityValue,
  abilityIconInterface,
  matchableIntent,
  openingStatement,
  queryTagsMarket,
  addTagMarket,
  bindTagMarket
} from '@/api/planGenerateApi.js'
import service from '@/axios'
import MyEditor from './mdEditorPreview.vue'
import panzoom from 'panzoom'
import selectItem from '@/views/planGenerate/selectItem.vue'
/* const engineeringXKey = {
  dev: 'b47qxYdpI5te1LIph9va3hHa1P27Sfgn',
  fat: 'IyaLckB1nVOEaCvogNHwPVKruxJc5n5n',
  production: 'KVzUbdi7qn5k9eXSbVCv29s6luQRlhAD'
} */
const chatXKey = {
  dev: '3mAatDrr5DBBTgzcgrbLyjpdw2mwwALs',
  fat: 'uXnSpC7JDP6mNC6SFyAqNG1r45apCJPd',
  production: 'dZl9xUDmcJlRc9eLTm68P7R8qNWRzKM1'
}
const mechanismXKey = {
  dev: 'nNgvOaFowxDRtttq8G74oIUTwdAxUjrM',
  fat: 'P48VHX7vPz1ORjkizxRlmtq1EJo33l6D',
  production: 'LDPOUdvLalz50A63v6dWAC6CIuvGzknF'
}
const knowledgeXKey = {
  dev: 'HUkDdVqOQS1KF635cT0b5e07GfTsoxP8',
  fat: '',
  production: 'pUU2NrpnAb3TPjivhxfwdvn71XkZMzJL'
}
const rdfaUrl = `https://rdfa-gateway${
  process.env.VUE_APP_ENV === 'production' ? '' : '.' + process.env.VUE_APP_ENV
}.ennew.com`
// const userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}

export default {
  components: {
    selectItem,
    MyEditor
  },
  props: {
    activeStep: {
      type: Number,
      required: true
    },
    agentSenceCode: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: 0
    },
    hasChatingName: {
      type: String,
      default: ''
    }
  },
  data() {
    const openingStatementValidator = (rule, value, callback) => {
      if (this.lastFormModal.modeSelection === 'dialogue' && value === '') {
        callback(new Error('请输入开场白'))
      } else {
        callback()
      }
    }
    const abilityIconValidator = (rule, value, callback) => {
      if (this.lastFormModal.abilityIcon === '') {
        callback(new Error('请配置能力图标'))
      } else {
        callback()
      }
    }
    return {
      lastFormRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        iot_type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        modeSelection: [{ required: true, message: '请选择模式', trigger: 'change' }],
        openingStatement: [
          { required: true, validator: openingStatementValidator, trigger: 'blur' }
        ],
        abilityIcon: [{ required: true, validator: abilityIconValidator, trigger: 'blur' }],
        abilityValue: [{ required: true, message: '请输入能力价值', trigger: 'blur' }],
        abilityIntents: [{ required: true, message: '请输入能力意图', trigger: 'blur' }]
      },
      fileList: [],
      formVisible: {
        abilityDesc: false,
        abilityIntents: false,
        openingStatement: false,
        uploadBtn: false
      },
      dialogContentLoading: false,
      panZoomRef: null,
      bkgUrl: '',
      lastLoading: false,
      scheme_detail: '', // 方案明细
      toMessage: { content: '', image_key: '', image_path: '' },
      uploadUrl: '',
      uploadParam: {},
      tagList: [],
      allTagList: [],
      enableDialogModal: true,
      lastFormModal: {
        tag_ids: [],
        tag: [],
        ability_market_tags: [],
        name: this.$route.query.name,
        description: '',
        iot_type: 0,
        domains: [],
        modeSelection: 'task', // 模式选择
        openingStatement: '', // 开场白
        isOtherInfoVisible: true,
        abilityValue: '', // 能力价值
        abilityIntents: '', //  能力意图
        abilityIcon: '', //  能力发布图标  这是默认的图标 URL
        iconType: 'local',
        keyword: '',
        abilityKeywords: ['nihao', 'hah'] // 关键词标签
      },
      formModal: {},
      formRules: {},
      tried: false,
      testing: false,
      processVisable: false, // 思考过程弹窗标志
      bkgLoading: true,
      treeData: '',
      treeDataProcess: '',
      rightFullFlag: false,
      jueceYulanFlag: true,
      dialogVisible: false,
      dialogVisible2: false,
      tableColumns: [],
      multipleSelection: [],
      // multipleSelectionCopy: {},
      selectTableData: [{ sample: 1 }, { sample: 0 }],
      // testState: [0 ,0],
      resultTableData: [
        /* {
        testData: '正样本数据1',
        result: '检查冷却水泵',
        real : '检查冷却水泵'
      },{
        testData: '负样本数据1',
        result: '检查冷却塔风扇',
        real : '进行冷却器清洗'
      } */
      ],
      thinkFlag: false,
      thinkFullFlag: false,
      planDetailShow: true,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      schemeInfo: {}
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        if (val === 2) {
        }
      },
      immediate: true
    }
    // treeDataVal: {
    //   handler(val) {
    //     this.treeData = val
    //   },
    //   immediate: true
    // },
  },
  mounted() {
    if (this.agentSenceCode === 'device_ops_assistant_scene-v1') {
      this.queryDecision()
      this.querySchemaParam()
      this.schemeDetailById()
    }
  },
  methods: {
    handlePublish() {
      this.queryEqu()
      this.abilityTestZhuGe('获取')
    },
    queryEqu() {
      this.loading = true
      this.lastFormModal.domains = []
      getPublishAbilityDich({ scene_id: this.schemeInfo.agent_scene })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data?.result || []
            if (result.length > 0) {
              for (const item of result) {
                const domainItem = {
                  label: item.field_name,
                  key: item.field_code,
                  value: '',
                  type: item.field_val.type || '',
                  domainsOptions: []
                }
                if (item.field_val.type === 'ability') {
                  const res = await getExecuteSync({
                    scene_instance_id: this.$route.query.id,
                    ability_id: item.field_val.ability_id,
                    name: item.field_val.ability_id,
                    goal: ''
                  })
                  if (res.data && res.data.length > 0) {
                    const resultList = res.data
                    if (resultList && resultList.length > 0) {
                      resultList.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                          item.children.forEach((it) => {
                            if (it.children && it.children.length === 0) {
                              delete it.children
                            }
                          })
                        }
                      })
                      domainItem.domainsOptions = resultList
                    }
                  }
                  this.lastFormModal.domains.push(domainItem)
                } else {
                  this.lastFormModal.domains.push(domainItem)
                }
              }
            }
          }
          this.dialogVisible2 = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    uploadExceed() {
      this.$message.warning('仅能上传一张图片')
    },
    modelUploadSuccess(response, file) {
      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key
        this.toMessage.image_key = fileKey
        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.toMessage.image_path = res.data.data.path
              this.lastFormModal.abilityIcon = res.data.data.path
              this.lastFormModal.iconType = 'local'
            }
          })
      } else {
        this.$message.warning(`图片上传状态为:${this.uploadStatus}`)
      }
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$refs.uploadBtn.clearFiles()
          this.toMessage.image_path = ''
          this.toMessage.image_key = ''
          this.lastFormModal.abilityIcon = ''
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    openDialog() {
      this.dialogVisible = true
    },
    onChange() {
      this.multipleSelection = []
      this.$refs.multipleTable && this.$refs.multipleTable.clearSelection()
    },
    setFormData(data) {
      // console.log(data, this.formModal)
      const dataKeys = Object.keys(data)
      for (const key of Object.keys(this.formModal)) {
        let value
        if (dataKeys.length) {
          value = /^pl\d+$/.test(key) ? 1 : data[key]
        }
        console.log('赋值--', value)
        if (value === null || value === '' || value === undefined) {
          value = 1
        }
        this.$set(this.formModal, key, value)
      }
    },
    async onTest() {
      // this.testState=[];
      const validate = await this.$refs.formRef.validate().catch(() => {})
      if (validate) {
        this.abilityTestZhuGe('测试')
        this.testing = true
        this.resultTableData = []
        this.tried = false
        this.multipleSelectionCopy = {}
        // this.multipleSelectionCopy = JSON.parse( JSON.stringify (this.multipleSelection));
        const length = this.multipleSelection.length || 1
        let notLast = true
        await this.scrollToBottom()
        for (let i = 0; i < length; i++) {
          // this.testState.push(0);
          if (i >= length) {
            notLast = false
          }
          if (this.multipleSelection?.length > 0) {
            this.setFormData(this.multipleSelection[i])
            /* this.multipleSelectionCopy = JSON.parse( JSON.stringify (this.multipleSelection[i]));
            this.$set(this.multipleSelectionCopy, '', 0) */
          }
          await this.scrollToBottom()
          const date = Date.now()
          const results = await OnCodeTest({
            scheme_id: this.$route.query.id,
            params: this.formModal
          })
          console.log(results)
          this.tried = true
          this.resultTableData.push({
            testData: this.multipleSelection?.[i]?.sample_type
              ? this.multipleSelection?.[i]?.sample_type
              : '当前测试数据',
            // result: this.multipleSelection?.[i]?.sample_type === '负' ? '清洗冷却器' : this.multipleSelection?.[i]?.sample_type === '正' ? '故障排查结束-正常' : '',
            result: this.multipleSelection?.[i]?.sample_result || '--',
            real: results?.data?.result?.resp?.result || '--',
            // real: this.multipleSelection?.[i]?.sample_type === '正' ? this.multipleSelection?.[i]?.sample_result : results?.data?.result?.resp?.success ? results?.data?.result?.resp?.result : '--',
            success: results?.data?.result?.resp?.success,
            message: results?.data?.result?.resp?.message || ''
          })
          // this.testState[i] = 1;
          await this.scrollToBottom()
          // eslint-disable-next-line no-unmodified-loop-condition
          while (notLast && Date.now() - date < 3000) {}
          // console.log(date, Date.now() , Date.now()- date )
        }
        this.testing = false
        UpdateSchemeStatus({
          scheme_id: this.$route.query.id,
          scheme_status: 'ability_generate_and_debugging'
        })
      }
    },
    cancelSelection() {
      this.dialogVisible = false
      // this.multipleSelection = [];
    },
    abilityTestZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
        this.lastFormModal.abilityIcon = res.data.result.ext_info?.abilityIcon || ''
        this.lastFormModal.iconType = res.data.result.ext_info?.iconType || ''
        this.lastFormModal.abilityValue = res.data.result.ext_info?.abilityValue || ''
        this.lastFormModal.abilityIntents = res.data.result.ext_info?.abilityIntents || ''
        this.lastFormModal.abilityKeywords = res.data.result.ext_info?.abilityKeywords || []
        this.lastFormModal.modeSelection = res.data.result.ext_info?.mode_selection || 'task'
        this.lastFormModal.openingStatement = res.data.result.ext_info?.opening_statement || ''
        console.log('res.data.result.ext_info', res.data.result.ext_info)
        this.lastFormModal.ability_market_tags = res.data.result.ability_market_tags
        this.lastFormModal.tag_ids = res.data.result.ability_market_tags.map((item) => item.id)
        this.uploadUrl = res.data.result.ext_info?.abilityIcon
        if (res.data.result.ext_info?.abilityIcon || '' !== '') {
          this.fileList = [
            {
              url: res.data.result.ext_info?.abilityIcon,
              name: res.data.result.ext_info?.abilityIcon.split('/').pop().split('?')[0]
            }
          ]
        }
        this.getAbilityKeywords()
      })
    },
    confirm() {
      // console.log(this.multipleSelection[0])
      this.setFormData(this.multipleSelection?.[0] || {})

      this.dialogVisible = false
    },
    cancel2() {
      this.dialogVisible2 = false
      this.lastFormModal.description = ''
      this.lastFormModal.name = this.schemeInfo.name
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async scrollToBottom() {
      await this.$nextTick()
      this.$refs.scrollDom.scrollTop = this.$refs.scrollDom.scrollHeight
    },
    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('详情', res.data.result)
            this.schemeInfo = res.data.result || { ...this.$route.query }
            this.lastFormModal.name = res.data.result?.name
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
    },
    async onPublish() {
      this.abilityTestZhuGe('发布')
      this.$refs.lastFormRef.validate(async (validate) => {
        if (validate) {
          this.lastLoading = true
          const outputFields = this.lastFormModal.domains.map((field) => {
            const obj = {}
            obj.field_name = field.label
            obj.field_code = field.key
            if (field.type === 'input') {
              obj.field_val = { value: field.value, label: field.value }
            } else {
              if (field.value && field.value.length === 1) {
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(field.value[0])
                )
                obj.field_val = {
                  value: selectedOptions.value,
                  label: selectedOptions.label,
                  parentValue: selectedOptions.parentValue
                }
              } else if (field.value && field.value.length === 2) {
                const selectedValues = field.value
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(selectedValues[0])
                )
                const selectedSubOptions = selectedOptions?.children?.find(
                  (child) => child.value === selectedValues[1]
                )
                obj.field_val = {
                  value: selectedSubOptions.value,
                  label: selectedSubOptions.label,
                  parentValue: selectedSubOptions.parentValue
                }
              }
            }
            return obj
          })
          console.log(outputFields)

          const all = [
            SchemeSaveKnow({
              scheme_id: this.$route.query.id,
              scheme_detail_name: this.schemeInfo.scheme_detail_name || this.schemeInfo.name || '',
              op_type: 'publish',
              iot_type: Number(this.lastFormModal.iot_type)
            }),
            MarketAbilityPublish({
              ext_info: {
                abilityIcon: this.lastFormModal.abilityIcon,
                iconType: this.lastFormModal.iconType,
                abilityValue: this.lastFormModal.abilityValue,
                abilityIntents: this.lastFormModal.abilityIntents,
                abilityKeywords: this.lastFormModal.abilityKeywords,
                mode_selection: this.lastFormModal.modeSelection,
                opening_statement: this.lastFormModal.openingStatement
              },
              scheme_id: Number(this.$route.query.id),
              description: this.lastFormModal.description,
              iot_type: Number(this.lastFormModal.iot_type),
              ext_data_info: outputFields,
              tag_ids: this.lastFormModal.tag_ids
            })
          ]
          try {
            await Promise.all(all)
            const results1 = await CodePublish({ scheme_id: this.$route.query.id })
            if (results1.data.result) {
              const results2 = await service({
                method: 'post',
                headers: {
                  'X-GW-AccessKey': chatXKey[process.env.VUE_APP_ENV]
                },
                baseURL: process.env.VUE_APP_LLM_API,
                url: '/mechanism/ability/saveOrUpdate',
                data: {
                  abilityId: this.$route.query.id,
                  abilityName: this.lastFormModal.name,
                  abilityDesc: this.lastFormModal.description,
                  apiUrl: results1.data.result.service_url,
                  sceneType: results1.data.result.scene_id,
                  sceneName: results1.data.result.scene_name,
                  schemeId: this.$route.query.id
                }
              })
              if (results2.data.success) {
                this.$message({
                  type: 'success',
                  message: '发布成功',
                  duration: 1500,
                  onClose: () => {
                    this.lastLoading = false
                    // 向外层页面传递消息
                    console.log('向上发布消息', { success: true, result: results1.data.result })
                    window.parent.postMessage(
                      JSON.stringify({ success: true, result: results1.data.result }),
                      '*'
                    )
                    // 发布成功之后不再跳转页面
                    this.dialogVisible2 = false
                    // this.$router.push({ name: 'targetList' })
                  }
                })
              } else {
                this.$message.error(results2.data?.message || '发布失败')
                this.lastLoading = false
              }
            }
          } catch (e) {
            this.$message.error(e.message || e)
            this.lastLoading = false
          }
        }
      })
    },
    async save() {
      const results = await UpdateSchemeStatus({
        scheme_id: this.$route.query.id,
        scheme_status: 'ability_generate_and_debugging'
      })
      if (results.status === 200 && results.data.code === 200) {
        this.$message.success('保存成功')
      }
    },
    changeViews(val) {
      this.$emit('updateStep', val)
      this.abilityTestZhuGe('上一步')
    },
    async querySchemaParam() {
      const result = await QueryAbilityData({
        scheme_id: this.$route.query.id
      })
      const result2 = await queryDbList({
        sqlStatement: result.data.result.sql,
        scheme_id: this.$route.query.id
      })
      console.log('query', result2)
      if (!result2.data.success) {
        this.$message.error(result2.data.message)
      }
      if (result.data.result?.config?.data?.length && result2?.data?.data?.list?.length) {
        for (const [index, item] of result.data.result.config.data.entries()) {
          // const tempNumber = parseInt(Math.random() * 100);
          if (!item.d_mapping_field) {
            continue
          }
          this.tableColumns.push({
            d_mapping_field: item.d_mapping_field,
            e_field_desc: item.e_field_desc
          })
          /* this.$set(this.selectTableData[0], item.d_mapping_field, parseInt(Math.random() * 100))
          this.$set(this.selectTableData[1], item.d_mapping_field, parseInt(Math.random() * 100)); */
          // console.log('9999', result2.data.data.list);
          const temp = result2.data.data.list?.map((item) => {
            const resetVals = {}
            Object.keys(item).forEach((key) => {
              resetVals[key] = item[key] === '' ? 1 : item[key]
            })
            return resetVals
          })
          console.log('新数据', temp)
          this.selectTableData = temp
          // this.selectTableData = result2.data.data.list;
          this.$set(this.formModal, item.d_mapping_field, '')
          this.$set(this.formModal, 'pl' + (index + 1), '')
          this.$set(this.formRules, item.d_mapping_field, [{ required: true, message: '必填' }])
          // this.formRules[item.param_key] = [{required:true , message:'必填' }];
        }
      }
    },
    queryDecision() {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status:
          this.agentSenceCode === 'device_ops_assistant_scene-v1' ? 'decision_tree' : 'mind_map'
      }
      GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.treeData = res.data.result?.decision_making_content || ''
          this.treeDataProcess = res.data.result?.sub_content || ''
          if (this.agentSenceCode === 'device_ops_assistant_scene-v1') {
            console.log('--')
          } else {
            if (this.treeData) {
              // 思维图
              this.thinkingHandle()
            } else {
              const nodeMarkmap = document.getElementById('markmap')
              if (nodeMarkmap) {
                nodeMarkmap.innerHTML = ''
              }
            }
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    showSikao() {
      this.processVisable = true
    },
    closeSikaoRizhi() {
      this.processVisable = false
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = '50%'
        this.rightWidth = '100%'
      }
    }
  },
  toggleOtherInfo() {
    console.log('this.lastFormModal.isOtherInfoVisible', this.lastFormModal.isOtherInfoVisible)
    this.lastFormModal.isOtherInfoVisible = !this.lastFormModal.isOtherInfoVisible
  },
  addTag() {
    this.lastFormModal.abilityKeywords.push(this.lastFormModal.keyword)
    this.lastFormModal.keyword = ''
    console.log('addTag', this.lastFormModal.abilityKeywords)
  },
  tagHandleClose(tag) {
    this.lastFormModal.abilityKeywords.splice(this.lastFormModal.abilityKeywords.indexOf(tag), 1)
    console.log('tagHandleClose', this.lastFormModal.abilityKeywords)
  },
  // AI 生成开场白
  async aiOpeningStatement() {
    this.formVisible.openingStatement = true
    const param = {
      query: this.scheme_detail
    }
    const res = await openingStatement(param)
    if (res.status === 200 || res.data.code === 200) {
      this.lastFormModal.openingStatement = res.data.result.answer
      console.log('生成开场白', res)
    }
    this.formVisible.openingStatement = false
  },
  // Ai 生成能力描述和价值
  async abilityDes() {
    this.formVisible.abilityDesc = true
    const param = {
      query: this.scheme_detail
    }
    const res = await abilityValue(param)
    console.log('能力价值', res)
    if (res.status === 200 || res.data.code === 200) {
      this.lastFormModal.abilityValue = res.data.result.answer
    }
    this.formVisible.abilityDesc = false
  },
  // AI匹配意图
  async aiMeMatch() {
    this.formVisible.abilityIntents = true
    const param = {
      query: this.scheme_detail
    }
    const res = await matchableIntent(param)
    if (res.status === 200 || res.data.code === 200) {
      this.lastFormModal.abilityIntents = res.data.result.answer
      console.log('匹配意图接口', res)
    }
    this.formVisible.abilityIntents = false
  },
  // AI生成能力图标
  async generateAbilityIcon() {
    this.formVisible.uploadBtn = true
    const schemeId = Number(this.$route.query.id)
    abilityIconInterface({ scheme_id: schemeId })
      .then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          const result = res.data.result
          this.lastFormModal.abilityIcon = result?.icon_url
          this.lastFormModal.iconType = 'ai'
          console.log('图标地址：', this.lastFormModal.abilityIcon)
          this.fileList = [{ url: result?.icon_url, name: result?.icon_url }]
        }
      })
      .finally((res) => {
        this.formVisible.uploadBtn = false
      })
  },
  // 关键字标签
  async getAbilityKeywords() {
    if (this.lastFormModal.tag_ids.length > 0) {
      return
    }
    const param = {
      query: this.scheme_detail
    }
    await keywordTag(param)
      .then(async (res) => {
        if (res.status === 200 || res.data.code === 200) {
          const tags = []
          try {
            this.lastFormModal.abilityKeywords = JSON.parse(res.data.result.answer)
            for (const name of this.lastFormModal.abilityKeywords) {
              const res = await addTagMarket({ name: name })
              if (res.data) {
                tags.push({ id: res.data, name: name })
              }
            }
            await this.handleTagFilter('')
            this.lastFormModal.tag_ids = tags.map((item) => item.id)
            this.lastFormModal.ability_market_tags = tags
          } catch (e) {
            this.lastFormModal.abilityKeywords = []
          }
        }
      })
      .finally((res) => {})
  },

  async beforeUpload(file) {
    try {
      const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
        fileType: this.$fileUtil.getFileSuffix(file.name)
      })
      if (res.data.status === 200) {
        this.uploadUrl = res.data.data.obsUrl
        this.toMessage.image_key = res.data.data.key
        this.uploadParam = {
          key: res.data.data.key,
          accessKeyId: res.data.data.accessKeyId,
          signature: res.data.data.signature,
          policy: res.data.data.policy
        }
      }
    } catch (e) {
      console.log(e)
      this.$message.error('获取签名出错！')
    }
  },
  modelUploadSuccess(response, file) {
    this.uploadStatus = file.status
    if (this.uploadStatus === 'success') {
      const fileName = this.$fileUtil.getFileName(file.raw.name)
      const fileSize = file.raw.size / 1024
      const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
      const fileKey = this.uploadParam.key
      this.toMessage.image_key = fileKey
      this.$axios
        .post(this.baseUrl + '/file/add', {
          fileKey: fileKey,
          fileName: fileName,
          fileSize: fileSize,
          fileType: fileType,
          storagePlatform: 'Obs'
        })
        .then((res) => {
          if (res.data.status === 200) {
            this.toMessage.image_path = res.data.data.path
            this.lastFormModal.abilityIcon = res.data.data.path
            this.lastFormModal.iconType = 'local'
          }
        })
    } else {
      this.$message.warning(`图片上传状态为:${this.uploadStatus}`)
    }
  },
  clearImage() {
    this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.$refs.uploadBtn.clearFiles()
        this.toMessage.image_path = ''
        this.toMessage.image_key = ''
        this.lastFormModal.abilityIcon = ''
      })
      .catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
  },
  async searchTags(keyword) {
    await queryTagsMarket({
      keyword
    }).then((res) => {
      if (res.data) {
        this.tagList = res.data
        if (keyword === '') {
          this.allTagList = res.data
        }
      } else {
        this.tagList = []
      }
    })
  },
  async addBizTag() {
    if (this.tagKeyword !== '') {
      await addTagMarket({ name: this.tagKeyword })
      await this.searchTags('')
    }
  },
  async handleTagFilter(keyword) {
    this.tagKeyword = keyword
    await this.searchTags(keyword)
  },
  async changeTags(tagIds) {
    const tags = []
    tagIds.forEach((tagId) => {
      const filters = this.allTagList.filter((item) => item.id === tagId)
      if (filters.length > 0) {
        tags.push({ id: tagId, name: filters[0].name })
      }
    })
    this.lastFormModal.ability_market_tags = tags
  },
  searchTags2() {
    queryTagsMarket({
      keyword: ''
    }).then((res) => {
      if (res.data) {
        console.log('数据', res.data)
        this.tagList = res.data
        this.allTagList = res.data
        const temp = []
        console.log('变价回显', this.lastFormModal.ability_market_tags)
        this.lastFormModal.ability_market_tags?.forEach((titem) => {
          const filter = res.data.filter((item) => item.id === titem.id)
          if (filter.length) {
            temp.push(titem.id)
          }
        })
        this.lastFormModal.tag_ids = temp
      } else {
        this.tagList = []
      }
    })
  }
}
</script>

<style lang="scss" scoped>
:deep(.upload-demo) {
  flex-direction: column;
}
:deep(.el-upload) {
  display: inline-block;
}
:deep(.el-upload--picture-card) {
  height: 60px;
  width: 60px;
  line-height: 65px;
}
:deep(.el-upload-list__item) {
  height: 60px;
  width: 60px;
  border-radius: 0;
  border: none;
}

.upload-icon {
  font-size: 20px;
  color: #999;
}
.info-container {
  width: 100%;
  margin-bottom: 30px;
  background-color: #f0f0f0; /* 浅灰色 */
  padding: 10px;
  cursor: pointer;
}
.rotate {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}
.containerBox2IFrame {
  height: calc(100vh - 104px) !important;
  .containerCardFull {
    top: -16px !important;
    height: calc(100% - 0px) !important;
    max-height: calc(100% - 0px) !important;
  }
  .chatRightFull {
    top: -16px !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }
  .optScroll {
    height: calc(100vh - 220px) !important;
    max-height: calc(100vh - 220px) !important;
  }
}
.con-form {
  margin: 16px 16px 0;
  width: 100%;
  height: calc(100vh - 209px);
  overflow: hidden;
  display: flex;
  .containerCardFull {
    position: fixed !important;
    top: 44px;
    z-index: 2005;
    height: calc(100% - 38px) !important;
    max-height: calc(100% - 38px) !important;
    width: 100%;
    left: 0px;
    width: 100%;
    margin-left: 0px !important;
  }
  .optContent {
    height: 100%;
    overflow: hidden; /*  flex: 1; min-width: 462px; */
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: #fff;
    transition: 0.25s;
    .optHeader {
      padding: 0px 20px;
      border-bottom: 1px solid #ebecf0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .rightTitle {
        font-size: 14px;
        font-weight: bold;
        color: #323233;
        line-height: 22px;
        padding: 12px 0px;
      }
      .rightTitleOpt {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTextBtn {
          background-color: #406bd4;
          font-size: 12px;
          color: #fff;
          padding: 0px 6px;
          height: 24px;
          line-height: 24px;
          border-radius: 2px;
          margin-left: 8px;
          cursor: pointer;
          &:hover {
            background: #3455ad;
          }
          &:active {
            background: #264480;
          }
        }
        .rightBtn {
          // background: #F2F3F5;
          border-radius: 2px;
          width: 30px;
          height: 30px;
          color: #4068d4;
          margin-left: 8px;
          text-align: center;
          line-height: 28px;
          cursor: pointer;
          &:hover {
            background: #ebecf0;
          }
          &:active {
            background: #dcdde0;
          }
          &.rightBtnBlue {
            background-color: #406bd4;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          img {
            width: 16px;
            height: auto;
          }
        }
      }
    }
  }
  .optScroll {
    position: relative;
    height: calc(100% - 100px);
    // max-height: calc(100vh - 270px);
    overflow-y: auto;
    // overflow-x: hidden;
    padding: 20px;
    ::v-deep .el-textarea {
      margin-bottom: 10px;
    }
    .btn {
      position: absolute;
      bottom: 0;
      right: 20px;
    }
  }
  .form-wrap {
    flex: 2; /* margin-left: 16px; */ /* overflow-y: auto; */
    &.chatRightFull {
      position: fixed !important;
      top: 44px;
      z-index: 2005;
      height: calc(100% - 38px);
      width: 100%;
      left: 0px;
      width: 100%;
      margin-left: 0px !important;
    }
    .form-info {
      padding: 16px; /* min-width: 840px; */
      height: calc(100% - 110px);
      overflow: auto;
      .form-c {
        display: flex; /* flex-wrap: wrap; */
        gap: 20px; /* overflow: auto; */
        .fm {
          min-width: 460px;
          flex: 1;
          :deep(.el-form-item) {
            margin-bottom: 18px;
          }
          :deep(.el-form-item__content) {
            width: 110px;
          }
        }
        .bkg-wrap {
          flex: 1;
          min-width: 150px; /* min-height: 48vh; background: url(../../assets/images/1112.jpg) no-repeat 20% 16px / 386px; */
          position: relative;
          img {
            max-width: 386px;
            max-height: 330px;
            object-fit: contain;
          }
          .test-btn {
            position: absolute;
            left: calc(20% + 80px);
            top: 386px;
          }
        }
      }
      .test-result {
        display: flex;
        margin-bottom: 16px;
        .test-result-warn {
          font-size: 12px;
          color: red;
        }
      }
    }
    .suffix {
      position: absolute;
      right: -20px;
      top: 0;
    }
    .suffix2 {
      position: absolute;
      right: -32px;
      top: 0;
    }
  }
  .right-hide {
    width: 0;
    flex: 0;
    min-width: 0;
    margin-left: 0;
  }

  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:hover {
      background: #e0e6ff;
      .process-icon {
        color: #3455ad !important;
      }
    }
    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;
      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }
    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;
      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }
    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;
      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }
  .optFooter {
    // position: absolute;
    bottom: 0px;
    left: 0px;
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 20px;
    min-height: 54px;
  }
  .c-alert {
    margin-bottom: 16px;
  }
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
