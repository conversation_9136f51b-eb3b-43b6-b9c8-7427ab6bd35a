<template>
  <div class="planContainer" v-loading="taskLoading" element-loading-text="任务生成中...">
    <div class="task-btn">
      <el-button type="primary" icon="el-icon-plus" @click="add">新增任务</el-button>
    </div>

    <el-dialog custom-class="last-dialog" :title="isTaskEdit ? '编辑任务' : '创建任务'" :visible.sync="visible"
      :before-close="onClose" append-to-body width="40%">
      <el-form ref="form" label-position="right" label-width="99px" :model="formData" :rules="rules">
        <el-form-item label="任务名称:" prop="task_name">
          <el-input v-model="formData.task_name" placeholder="请输入" show-word-limit maxlength="30" style="width: 100%" />
        </el-form-item>
        <el-form-item label="任务描述:" prop="task_desc">
          <el-input v-model="formData.task_desc" type="textarea" :rows="2" placeholder="请输入" show-word-limit
            style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createSubmit">确定</el-button>
        <el-button type="info" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
    <div class="task-group" v-for="(item, index) in list" :key="item.task_id">
      <div class="task-group-header" @click="item.isShow = !item.isShow" @mouseenter="handleMouseEnter($event)"
        @mouseleave="handleMouseLeave($event)">
        <div class="task-group-info">
          <span class="task-group-index">{{ index + 1 }}</span>
          <div class="task-group-title">
            <div class="title">{{ item.task_name }}</div>
            <div class="desc">{{ item.task_desc }}</div>
          </div>
        </div>
        <div class="task-group-actions">
          <span class="date">{{ item.create_date }}</span>
          <el-dropdown>
            <i class="el-icon-more solid_class cur"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="() => { deleteTask(item) }" v-if="!item?.children?.length">
                删除任务
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleAddTask(item)">
                新增子任务
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleEditTask(item)">
                编辑任务
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <i :class="['toggle-icon', 'el-icon-arrow-down', { 'is-active': item.isShow }]"></i>
        </div>
      </div>
      <div class="task-table-wrapper" v-if="item.isShow">
        <el-table v-loading="tableLoading" :data="item.children" size="small" row-key="task_id" border
          default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" class="task-table"
          :header-cell-style="{ background: '#F6F7FB', color: '#323233' }">
          <el-table-column type="index" fixed label="方案ID" width="100px">
            <template slot-scope="scope">{{ scope.row.task_execute_info.scheme_id }}</template>
          </el-table-column>
          <el-table-column prop="task_name" label="任务名称" fixed min-width="150" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="task_execute_info" label="研发场景" min-width="150">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark"
                :content="sceneList.find(it => it.id == scope.row?.task_execute_info?.scene_id)?.name" placement="top">
                <div class="descriptionTd">
                  <el-tag :type="typeTag[sceneList.findIndex(it => it.id ==
                    scope.row?.task_execute_info?.scene_id)]
                    " effect="dark">
                    {{sceneList.find(it => it.id ==
                      scope.row?.task_execute_info?.scene_id)?.name}}
                  </el-tag>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="publish_status" label="发布状态" width="100">
            <template slot-scope="scope">
              <div class='descriptionTd' v-if="scope.row.parent_task_id != 0">
                <el-tag :type="{ '未发布': 'warning', '已发布': 'success' }[
                  scope.row?.publish_status ? '已发布' :
                    '未发布'
                ]" effect="dark">
                  {{ scope.row?.publish_status ? '已发布' :
                    '未发布' }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="production_type" label="生成类型" width="100">
            <template slot-scope="scope">
              <div v-for="(ptype, index) in scope.row.production_type.split('-')" :key="index" :class="[
                'cutome-tag',

              ]" @click="handleDetailTo(scope.row)">
                <el-tag :type="{ '用户添加': 'info', '用户修改': 'info', '人工修改': 'danger', 'AI生成': 'success', 'AI创建': '', 'AI创建-用户修改': 'warning' }[
                  ptype
                ]" effect="dark">
                  {{ ptype }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_date" label="创建时间" width="160" />
          <el-table-column label="操作" fixed="right" width="130" class-name="no-bor">
            <template slot-scope="scope">
              <div>
                <el-link v-if="scope.row.parent_task_id != 0" style="margin-right:20px" type="text" size="small"
                  :disabled="!scope?.row?.task_execute_info?.scene_id" :underline="false"
                  @click="jumpIframe(scope.row)">进入研发</el-link>
                <el-dropdown v-if="scope.row.parent_task_id != 0">
                  <i class="el-icon-more solid_class cur"></i>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="() => { edit(scope) }">
                      <el-link type="text" style="margin-left: 12px" size="small" :underline="false">编辑</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="!scope.row.task_execute_info.scheme_id"
                      @click.native="deleteTask(scope.row)">
                      <el-link type="text" style="margin-left: 12px" :underline="false">删除</el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <editModal :edit-data="editData" :visible="createVisable" @close="handleCreate" />
  </div>
</template>

<script>
import { scheme_task_upd, SceneTypeList, update_scheme_materialsFetch, get_scheme_materialsFetch, CreateTaskNew, AddScheme, TaskPageListNew, PlanTaskDeleteNew, querySchemeDetailById, startTaskGenerate, getVersionList } from '@/api/planGenerateApi.js'
import { mapGetters } from 'vuex';
import editModal from './editTask.vue';
import Status from './components/statusNew.vue'
import _ from 'lodash';
export default {
  components: {
    editModal,
    Status
  },
  data() {
    return {
      task_id: '',
      typeTag: ['', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning', '', 'success', 'info', 'danger', 'warning',],
      rules: {
        task_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        task_desc: [
          // 根据需要添加规则
        ],
      },
      formData: {
        task_name: '',
        task_desc: ''
      },
      visible: false,
      sceneList: [],
      childType: {
        dev_product: '研发生产',
        other_task: '其他任务'
      },
      childStatus: {
        0: '未开始',
        1: '进行中',
        2: '已完成'
      },
      taskLoading: false,
      isSuperAdmin: false, // 是否超级管理员
      tableLoading: false, // 加载状态
      list: [],
      searchForm: {
        searchKey: '' // 根据名称模糊搜索
      },
      createVisable: false,
      editData: {}
    };
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    })
  },
  watch: {},
  async created() {
    SceneTypeList(
      { "keyword": "", "scene_type": "", "dev_mode": "" }
    ).then((res) => {
      console.log(res, '000');
      if (res.status === 200 && res.data) {
        this.sceneList = res.data;
      }
    });
    this.handlSearch();
  },
  mounted() {
  },
  methods: {
    handleMouseEnter(event) {
      event.currentTarget.style.backgroundColor = '#ebecf0';
    },
    handleMouseLeave(event) {
      event.currentTarget.style.backgroundColor = '';
    },
    onClose() {
      this.formData.task_name = '';
      this.formData.task_desc = '';
      this.visible = false
    },
    handleEditTask(item) {
      this.isTaskEdit = true
      this.formData.task_name = item.task_name;
      this.formData.task_desc = item.task_desc;
      this.task_id = item.task_id
      this.visible = true
    },
    createSubmit() {
      this.$refs.form.validate(async (validate) => {
        if (validate) {
          this.loading = true;
          const data = this.sceneList.filter(it => it.id == this.formData.scene_id)
          const param = {
            task_name: this.formData.task_name,
            task_desc: this.formData.task_desc || '',
            scheme_id: this.$route.query.id,
            task_id: this.isTaskEdit ? this.task_id : undefined
          };
          const request = !this.isTaskEdit ? CreateTaskNew : scheme_task_upd;
          await request(param)
            .then(async (res) => {
              this.loading = false;
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  type: 'success',
                  message: `新建成功`
                });
                this.onClose();
                this.$nextTick(() => {
                 this.queryTableData();
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    add() {
      this.visible = true
      this.task_id = undefined
      this.isTaskEdit = false
    },
    regenerate() {
      this.setTaskLoading(true)
      startTaskGenerate({ task_button_type: 'override', session_id: this.sessionId })
      if (this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '') return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then(res => {
        const name = res.data.result.name;
      })
    },
    edit(scope) {
      // if(scope.row?.task_execute_info?.scheme_id) return false
      this.editData = scope.row;
      this.createVisable = true;
    },
    handleAddTask(item) {
      item.isAdd = true
      this.editData = item;
      console.log(this.editData, 'this.editData')
      this.createVisable = true;
    },
    setTaskLoading(val) {
      this.taskLoading = val
    },
    judgeJumpFlag(row) {
      let flag = true;
      if (row.type === '文档设计' || row.type === '知识收集' || row.type === '规则开发' || row.type === '能力组装') {
        flag = false;
      } else {
        flag = true;
      }
      console.log('flag---', flag);
      return flag;
    },
    async jumpIframe(row) {
      const { isTask, ...rest } = this.$route.query;
      if (row?.task_execute_info?.scheme_id) {//直接跳路由
        // planGenerate/ConfTaskPlanchat?workspaceId=1&workspaceName=默认空间&fromMenu=3&FullScreen=false&status=3&id=715&enterType=expert&ability_id=26
        const res = await get_scheme_materialsFetch({
          scheme_id: Number(row?.task_execute_info?.scheme_id)
        });
        // this.demand[0].requirement = res.data.result?.requirement || '';
        // this.demand[1].scheme_target = res.data.result?.content?.scheme_target || '';
        const content = res.data?.result?.content || {}
        this.content = {
          abilitys: content.abilitys || [],
          files: content.files || [],
          knowledges: content.knowledges || [],
          relatedUrls: content.relatedUrls || [],
          scheme_target: row.production_target || ''
        };
        await update_scheme_materialsFetch({
          content: this.content,
          requirement: row.production_background || '',
          id: res.data.result?.id || '',
          scheme_id: Number(row?.task_execute_info?.scheme_id)
        })
        const route = this.$router.resolve({
          path: '/planGenerate/ConfTaskPlanchat',
          query: {
            ...rest, // 保留当前路由的查询参数
            id: row?.task_execute_info?.scheme_id, // 添加新的查询参数
            fromMenu: '1' // 添加其他查询参数
          }
        });

        // 使用 window.open 在新标签页中打开目标路由
        let urlObj = new URL(route.href);
        urlObj.pathname = urlObj.pathname.replace('/expert', '');
        window.open(urlObj.pathname, '_blank');
      } else {//走创建流程
        const res = await getVersionList(row?.task_execute_info?.scene_id)
        let data = res.data.filter(it => it.status === 1)
        if (data && data.length > 0) {
          const res1 = await AddScheme({
            scene_version_id: data[0].scene_version_id,
            agent_scene_code: row?.task_execute_info?.scene_type,
            name: row?.task_name,
            description: row?.task_desc,
            visibility: "public",
            //task_input:row?.task_input || '',
            materials: {
              "requirement": row.production_background,
              "content": {
                "abilitys": [],
                "files": [],
                "knowledges": [],
                scheme_target: row.production_target,
                "relatedUrls": []
              }
            }
          })
          if (res1.data?.result?.status === 'success') {
            const route = this.$router.resolve({
              path: '/planGenerate/ConfTaskPlanchat',
              query: {
                ...rest, // 保留当前路由的查询参数
                id: res1.data?.result?.id, // 添加新的查询参数
                fromMenu: '1' // 添加其他查询参数
              }
            });
            // 使用 window.open 在新标签页中打开目标路由
            window.open(route.href, '_blank');
            //更新表格逻辑
            await scheme_task_upd({
              task_id: row.task_id,
              task_name: row.task_name,
              task_desc: row.task_desc,
              task_input: row?.task_input || '',
              type: row?.type || '',
              task_execute_info: Object.assign(row.task_execute_info, {
                scheme_id: res1.data?.result?.id
              }),
              scheme_id: this.$route.query.id
            })
            await this.handlSearch();
          } else {
            this.$message.error('创建对话失败')
          }
        } else {
          this.$message.warning('当前场景无可用版本')
        }

      }
    },
    handlSearch() {
      this.queryTableData();
    },
    async queryTableData() {
      this.tableLoading = true;
      const param = {
        scheme_id: this.$route.query.id
      };
      const res = await TaskPageListNew(param)
      if (res.status === 200 && res.data.code === 200) {
        this.tableLoading = false;

        console.log(1111, this.list)
        const newList = _.cloneDeep(res.data.result.map(item => {
          const newItem = { ...item, isShow: false, children: item.children ? item.children : [] };
          this.$set(newItem, 'statusFlag', item.status !== '未完成');
          return newItem;
        }));
        this.list = JSON.parse(JSON.stringify(newList));
      //   this.$nextTick(() => {
      //   this.$forceUpdate();
      // });
      }
    },
    deleteTask(row) {
      this.$confirm('此操作将删除该任务，是否继续?', '删除', {
        customClass: 'last-dialog',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true;
          PlanTaskDeleteNew({ task_id: row.task_id }).then((res) => {
            this.tableLoading = false;
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.tableLoading = false;
              this.$nextTick(() => {
                this.queryTableData();
              })
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
            .finally(() => {
              this.tableLoading = false;
            });
        })
        .catch(() => {
          this.tableLoading = false;
          this.queryTableData();
        });
    },
    handleCreate() {
      this.createVisable = false;
      this.queryTableData();
    },

    goToOldData() {
      this.$router.push({ path: '/imgDatasetManage', query: { ...this.$route.query } });

    },
    handleProject(row) {
      this.dataDetail = row;
      this.bindVisable = true;
    },
    handleTagDialog(row) {
      this.dataDetail = row;
      // this.tagList = item.tags || []
      this.tagVisable = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.planContainer {
  padding: 16px;
  background: #f5f7fa;
  height: auto;
  overflow: auto;
}

.task-group {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;

  .task-group-header {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
  }

  .task-group-info {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: auto;
  }

  .task-group-index {
    font-size: 14px;
    color: #606266;
  }

  .task-group-title {
    width: 100%;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }

    .desc {
      font-size: 14px;
      color: #606266;
      line-height: 1.4;
      word-wrap: break-word;
      white-space: normal;
      width: 100%;
    }
  }

  .task-group-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: 20px;

    .task-date {
      color: #909399;
      font-size: 14px;
      white-space: nowrap;
    }

    .add-task-btn {
      min-width: 88px;
      padding: 0 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .task-table {
    margin: 0;

    :deep(.el-table__header) {
      th {
        background-color: #F6F7FB;
        color: #323233;
        font-weight: 500;
      }
    }

    :deep(.el-table__row) {
      td {
        padding: 8px 0;
      }
    }
  }
}

.cutome-tag {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
  margin-right: 8px;

  &.success {
    color: #67C23A;
    background: rgba(103, 194, 58, 0.1);
  }

  &.running {
    color: #409EFF;
    background: rgba(64, 158, 255, 0.1);
  }

  &.fail {
    color: #F56C6C;
    background: rgba(245, 108, 108, 0.1);
  }
}

.descriptionTd {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toggle-icon {
  font-size: 16px;
  color: #909399;
  transition: transform 0.3s;
  margin-left: 16px;

  &.is-active {
    transform: rotate(180deg);
  }
}

.task-table-wrapper {
  transition: all 0.3s;

  :deep(.el-table .cell) {
    overflow: visible !important;
  }
}

.task-btn {
  position: absolute;
  top: 5px;
  right: 71px;
}

.task-group-header {
  transition: background-color 0.3s ease;
  // ... 其他样式保持不变 ...
}

.date {
  color: #606266;
}
</style>
<style lang="scss">
.last-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;

    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-dialog__headerbtn {
      top: 14px;

      .el-dialog__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;

    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-message-box__headerbtn {
      top: 14px;

      .el-message-box__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__content {
    padding: 16px 20px;

    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }

  .el-message-box__btns {
    padding: 0px 20px;

    button {
      width: 60px !important;
    }

    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }

  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }

  .el-dialog__footer {
    padding: 16px 20px;

    .el-button {
      line-height: 20px;
    }
  }

  .el-input__inner {
    border-radius: 2px;
  }
}

.cur {
  cursor: pointer;
}
</style>
