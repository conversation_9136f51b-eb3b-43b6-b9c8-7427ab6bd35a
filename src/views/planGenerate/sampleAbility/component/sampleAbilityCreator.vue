<template>
  <div class="sample-ability-creator">
    <el-dialog :visible="value" :width="width" custom-class="dialog-sample-ability" @close="$emit('input', false)">
      <template #title>
        <div class="dialog-title">
          新建能力样版
        </div>
      </template>
      
      <el-form ref="addCard" :model="addCardForm" :rules="rules" label-width="98px">
        <el-form-item label="能力列表：" prop="scheme_id">
         
          <el-select v-model="addCardForm.scheme_id" filterable class="select-sample" popper-class="ability-select" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in abilityList" :key="index" :label="item.name" :value="item.scheme_id">
              <span>{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样版类型：" prop="template_code">
          <el-select v-model="addCardForm.template_code" class="select-sample" popper-class="ability-select" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in templateList" :key="index" :label="item.template_name" :value="item.template_code">
              <span>{{ item.template_name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="solid-with-icon-btn" @click="operateEvent('confirm')">确认
        </el-button>
        <el-button class="hollow-with-icon-btn" @click="operateEvent('cancel')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { schemeCreateTemplate } from '@/api/planGenerateApi.js'
export default {
  name: 'SampleAbilityCreator',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
   templateList: {
      type: Array,
      default: () => []
    },
   abilityList: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '600px'
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.value, // 同步value值
      authenticationList: [], // 认证列表
      addCardForm: { // 新建样板能力表单
       scheme_id: '', // 组件
       template_code: '', // 样板类型
      },
      rules: {
       scheme_id: [{ required: true, message: '请选中能力列表', trigger: 'change/blur' }]
      }
    }
  },
  watch: {
    value(newVal) {
      this.dialogVisible = newVal
    },
    dialogVisible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
   async operateEvent(type) {
      switch (type) {
        case 'confirm':
          this.$refs.addCard.validate(async valid => {
            if (valid) {
             await this.getCreateTemplateList()
              this.$emit('confirm', this.addCardForm)
              this.dialogVisible = false
              this.addCardForm ={ // 新建样板能力表单
               scheme_id: '', // 组件
               template_code: '', // 样板类型
              }
            }
          })
          break
        case 'cancel':
          this.dialogVisible = false
          break
      }
    },
    async getCreateTemplateList() {
      const res = await schemeCreateTemplate(this.addCardForm)
      const { data = {}, status } = res || {}
      console.log('获取样板能力列表', res.code, status, data)
      if (data.code === 200 && status === 200) {
        this.$message.success('创建成功')
      } else {
        this.$message.error(data.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.dialog-sample-ability) {
 .el-dialog__header {
  border-bottom:1px solid #ebecf0;
 }
 .select-sample {
  width: 100%;
 }
}
</style>