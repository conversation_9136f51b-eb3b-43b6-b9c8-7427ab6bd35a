<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="展示sql"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="50%"
    >
      <el-input
        v-model.trim="textarea"
        type="textarea"
        :rows="16"
        placeholder="请输入sql语句">
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading || hasChatingName" @click="saveSql">保存</el-button>
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    sqlShowData: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      textarea: this.sqlShowData,
      loading: false,
      showFlag: false,
      dataStatus: '',
      configData: {}
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
    sqlShowData: {
      handler(val) {
        if (val) {
          this.textarea = val
        }else{
          this.textarea = ''
        }
      },
      immediate: true
    },
  },
  methods: {
    saveSql () {
      this.$emit('updateSQL',this.textarea)
      this.$emit('close');
    },
    onClose() {
      this.$emit('close');
    },
  }
};
</script>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
