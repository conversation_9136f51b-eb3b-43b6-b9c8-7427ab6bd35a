<template>
    <div :class="$store.state.planGenerate.isIframeHide ? 'containerBox2 containerBox2IFrame' : 'containerBox2'" style="width: 100%;">
      <div id="left-content" :style="{ width: leftWidth,maxWidth: leftWidth, marginRight: !rightFullFlag ? '0px' : '16px', userSelect: isDragging ? 'none' : 'auto', transition: isDragging ? 'none' : 'width 0.2s', position: thinkFullFlag ? '' : 'relative'}" :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'">
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">方案明细</div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
                <el-button type="info" :disabled="hasChatingName !== ''" size="mini" @click="() => {hisDetail = detailContent.text; isEdit = true}"><img src="@/assets/images/planGenerater/bianji.png"/></el-button>
              </el-tooltip>
              <template v-if="isEdit">
                <el-tooltip class="item" effect="dark" content="保存" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSave"><img src="@/assets/images/planGenerater/baocun.png"/></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSaveClose"><img src="@/assets/images/planGenerater/quxiao.png"/></el-button>
                </el-tooltip>
              </template>
              <el-tooltip class="item" effect="dark" :content="rightFullFlag ? '退出全屏' : '全屏'" placement="top">
                <el-button :type="rightFullFlag ? 'primary' : 'info'" size="mini" @click="changeShowFull"><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/></el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="optScroll">
            <div class="optContentBox customMd" @mouseenter="fangda">
              <MyEditor id="MyEditor2" ref='MyEditor2' :md-content="detailContent.text" :is-edit="isEdit" @updateContent="handleUpdateContent"></MyEditor>
            </div>

            <!-- <template v-if="isEdit">
              <div class="optContentBox">
                <el-input id="detail-content" v-model="detailContent.text"  type="textarea" :autosize="{ minRows: 12}" placeholder="请输入" @focus="startWriteOpt()"/>
              </div>
            </template>
            <template v-else>
              <div class="optContentBox" @mouseenter="fangda">
                <pre v-if="(detailContent.text?.indexOf('graph') > -1 || detailContent.text?.indexOf('flowchart') > -1) && detailContent.text?.indexOf('mermaid') < 0 && detailContent.text?.indexOf('```') < 0"><div class="language-mermaid">{{detailContent.text}}</div></pre>
                <vue-markdown v-else v-highlight :source="detailContent.text" class="markdown-body"></vue-markdown>
              </div>

              <el-link type="primary">{{ detailContent.file_url }}</el-link>
            </template> -->
          </div>
        </div>
        <div class="optFooter">
          <el-button class="button-last" :disabled="treeStatus == 1||treeStatus == 0 || hasChatingName !== ''||detailContent.text===''" type="primary" @click="regenerate()">{{treeStatus == 1||treeStatus == 0 ?'生成中...': (treeData !== '' ? '重新生成' : '生成')}}</el-button>
        </div>
      </div>
      <div v-if="planDetailShow && !rightFullFlag" id="resize" class="resize" title="收缩侧边栏" @mousedown="startDrag">
        <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
        <div class="el-two-column__trigger-icon"><SvgIcon name="dragborder" class="process-icon" /></div>
        <div class="el-two-column__icon-bottom"><div class="el-two-column__icon-bottom-bar"></div></div>
      </div>
      <div id="right-content" :style="{ width: rightWidth, marginRight: '16px', transition: isDragging ? 'none' : 'width 0.2s', userSelect: isDragging ? 'none' : 'auto'}" :class="!planDetailShow ? 'chatRight chatRightFull' : 'chatRight'">
        <div v-if="['device_ops_assistant_scene','device_ops_assistant_scene-v1','artificial_handle_scene','visit_leader_cognition_scene' ,'rule_generation_scene' ,'intelligent_conversation_scene', 'sop_scene'].indexOf(agentSenceCode) > -1" class="optContent">
          <div>
            <div class="optHeader">
              <div class="rightTitle">思维树</div>
              <div class="rightTitleOpt">
                <!-- <el-button class="rightTextBtn" :disabled='saveLoading' type="primary" @click="handleAbilityMapping">{{saveLoading?'加载数据对齐...':'数据对齐'}}</el-button> -->
                <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
                  <el-button type="info" @click="showSikao">生成过程</el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="复制" placement="top">
                  <el-button type="info" @click="copyText">复制</el-button>
                </el-tooltip> -->
                <el-tooltip class="item" effect="dark" :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'" placement="top">
                  <el-button type="info" size="mini" @click="changeShowType"><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png"/><img v-else src="@/assets/images/planGenerater/markdown.png"/></el-button>
                </el-tooltip>
                <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
                  <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                    <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/>
                  </el-button>
                  <!-- <div :class="!planDetailShow ? 'rightBtn rightBtnBlue' : 'rightBtn'" @click="changeShowRight"><img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/></div> -->
                </el-tooltip>
                <el-dropdown style="margin-left: 10px;" @command="handleCommand">
                    <el-button type="info" :disabled="hasChatingName !== ''" size="mini"><img src="@/assets/images/planGenerater/more.png"/></el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :disabled = "isCacheDisabled ||treeStatus == 1||treeStatus == 0" :command="isCache?'removeCache':'addCache'">{{ isCache?'取消缓存':'缓存' }}</el-dropdown-item>
                      <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
                      <el-dropdown-item command="copy">复制</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
              </div>
            </div>
            <div v-loading="taskLoading" class="optScroll" element-loading-text="思维树生成中..." element-loading-spinner="el-icon-loading">
              <el-dialog :visible.sync="dialogTableVisible" :modal-append-to-body='false' title="数据来源配置" :before-close="handleClose">
                <el-table v-show="dataStatus !== 'failed'" v-loading="dataStatus === 'generating'" element-loading-text="生成中..." element-loading-spinner="el-icon-loading" custom-class="el-loading-spinner2" class="transition-box" :data="gridData" max-height="400">
                  <el-table-column v-for="item in dataThDatas" :key="item.field" :prop="item.field" :label="item.name">
                    <template #default="scope">
                      <div v-if="item.field === 'param_key' || item.field === 'param_name'">
                        <el-input v-model.trim="scope.row[item.field]" placeholder="请输入内容"></el-input>
                      </div>
                      <div v-else>{{scope.row[item.field]}}</div>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-if="dataStatus === 'failed'" style="margin-top: 20px;width:100%;height:100%">
                  <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                    <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto"/>
                    <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">数据抽取失败，请<el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''" @click="regenerate()">重试</el-link></div>
                  </div>
                  <!-- <el-result icon="error" title="数据抽取失败！"><el-link :underline="false" @click="regenerate()">重试</el-link></el-result> -->
                </div>
                <div style='margin-top:15px;width: 100%;display: flex;flex-direction: column;align-items: center'>
                  <el-button v-if="dataThDatas.length" type="primary" :disabled ="saveLoading" @click="handleDone">完成</el-button>
                </div>
              </el-dialog>
                <div v-if="treeStatus == 1 ||treeStatus == 0" class="optContentBox">
                  <vue-markdown :source="treeData"></vue-markdown>
                </div>
                <div v-else-if="treeStatus == 3" style="width: 100%;height: 100%">
                  <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
                    <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto"/>
                    <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">思维树生成失败，请<el-link style="color: #4068d4" :underline="false" :disabled="hasChatingName !==''" @click="regenerate()">重试</el-link></div>
                  </div>
                  <!-- <el-alert :closable="false" type="error">
                    <span slot="title">思维树生成失败<el-link :underline="false" @click="regenerate()">重试</el-link></span>
                  </el-alert> -->
                </div>
                <div v-else class="optContentBox">
                  <div v-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
                    <MyEditorPreview id="MyEditor4" ref='MyEditor4' :md-content="treeData"></MyEditorPreview>
                    <!-- <pre v-if="(treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData?.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
                    <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
                  </div>
                  <div v-else>
                    <pre>{{treeData}}</pre>
                  </div>
                </div>
                <el-link type="primary">{{ detailContent.file_url }}</el-link>
            </div>
          </div>
        </div>
        <div v-else-if="agentSenceCode ==='custom_cognition_assistant_scene'" class="optContent">
          <div>
            <div class="optHeader">
              <div class="rightTitle">思维图</div>
              <div class="rightTitleOpt">
                <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
                  <el-button type="info" @click="showSikao">生成过程</el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="复制" placement="top">
                  <el-button type="info" @click="copyText">复制</el-button>
                </el-tooltip> -->
                <el-tooltip class="item" effect="dark" :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'" placement="top">
                  <el-button type="info" size="mini" @click="yuLan()"><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png"/><img v-else src="@/assets/images/planGenerater/markdown.png"/></el-button>
                </el-tooltip>
                <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
                  <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                    <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/>
                  </el-button>
                </el-tooltip>
                <el-dropdown style="margin-left: 10px;" @command="handleCommand">
                    <el-button type="info" :disabled="hasChatingName !== ''" size="mini"><img src="@/assets/images/planGenerater/more.png"/></el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :disabled="isCacheDisabled || treeStatus == 1||treeStatus == 0" :command="isCache?'removeCache':'addCache'">{{ isCache?'取消缓存':'缓存' }}</el-dropdown-item>
                      <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
                      <el-dropdown-item command="copy">复制</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
              </div>
            </div>
            <div v-loading="taskLoading" class="optScroll" element-loading-text="思维图生成中..." element-loading-spinner="el-icon-loading">
              <div v-if="treeStatus == 1 || treeStatus == 0" style="width: 100%;">
                <vue-markdown :source="treeData"></vue-markdown>
              </div>
              <div v-else class="optContentBox">
                <div v-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
                  <template v-if="treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1 || treeData?.indexOf('mermaid')>-1">
                    <MyEditorPreview id="MyEditor9" ref='MyEditor9' :md-content="treeData"></MyEditorPreview>
                    <!-- <pre v-if="(treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
                    <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
                  </template>
                  <template v-else>
                    <svg id="markmap" class="optContentBox"></svg>
                  </template>
                </div>
                <div v-else>
                  <pre>{{treeData}}</pre>
                </div>
              </div>
              <el-link type="primary">{{ detailContent.file_url }}</el-link>
            </div>
          </div>
        </div>
        <div v-else class="optContent">
          <div class="optHeader">
            <div class="rightTitle">任务列表</div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'" placement="top">
                <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                    <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png"/><img v-else src="@/assets/images/planGenerater/tuichuquanping.png"/>
                  </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="任务列表" placement="top">
                <el-button type="info" size="mini" @click="() => $router.push({path: '/planGenerate/task', query: {...$route.query}})"><img src="@/assets/images/planGenerater/task.png"/></el-button>
              </el-tooltip>
            </div>
          </div>
          <div v-loading="taskLoading" class="optScroll flex_column" element-loading-text="任务生成中..." element-loading-spinner="el-icon-loading">
            <div v-if="tableData.list.length" class="cardContent">
              <el-table
                v-loading="tableLoading"
                :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
                :data="tableData.list"
                height="100%"
                size="small"
                style="width: 100%;min-height: 300px"
              >
                <el-table-column type="index" align="left" fixed :index="calcIndex" label="序号" width="50px" />
                <el-table-column prop="name" label="任务名称" fixed width="130" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="description" label="任务描述" min-width="160" max-width="200">
                  <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.description" placement="top">
                      <div class="descriptionTd">{{scope.row.description}}</div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="任务类型" min-width="100"></el-table-column>
                <el-table-column prop="production_type" label="生成类型" min-width="170">
                  <template slot-scope="scope">
                    <div
                      v-for="(ptype, index) in scope.row.production_type.split('-')"
                      :key="index"
                      :class="[
                        'cutome-tag',
                        { '用户修改': 'running', '人工修改': 'running', 'AI生成': 'success', 'AI创建': 'success', 'AI创建-用户修改': 'fail' }[
                          ptype
                        ]
                      ]"
                      @click="handleDetailTo(scope.row)"
                    >
                      {{ptype}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="130" class-name="no-bor">
                  <template slot-scope="scope">
                    <el-link type="text" :disabled="hasChatingName !== ''" style="margin-left: 12px" :underline="false" @click="deleteTask(scope.row)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="cardEmpty">
                <el-empty description="暂无任务"></el-empty>
            </div>
            <!-- 分页部分 -->
            <div style="text-align: right; margin-top:15px;">
            <el-pagination
                v-if="tableData.list.length"
                class="new-paper"
                layout="prev, pager, next, sizes, jumper"
                :page-sizes="[10, 20, 30, 40, 50]"
                :current-page.sync="tableData.page"
                :page-size="tableData.pageSize"
                :total="tableData.total || 0"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="optFooter">
          <el-button class="button-last" :disabled="treeStatus == 1||treeStatus == 0" type="primary" @click="changeViews(0)">上一步</el-button>
          <el-button v-if="['device_ops_assistant_scene','custom_cognition_assistant_scene','device_ops_assistant_scene-v1' ,'artificial_handle_scene' ,'visit_leader_cognition_scene','rule_generation_scene' ,'intelligent_conversation_scene', 'sop_scene'].indexOf(agentSenceCode) > -1 && displayType === 1" class="button-last" :disabled="treeStatus == 1||treeStatus == 0||treeData ===''" type="primary" @click="changeViews(2)">下一步</el-button>
          <el-button v-if="['device_ops_assistant_scene','custom_cognition_assistant_scene','device_ops_assistant_scene-v1' ,'artificial_handle_scene' ,'visit_leader_cognition_scene','rule_generation_scene' ,'intelligent_conversation_scene', 'sop_scene'].indexOf(agentSenceCode) > -1 && displayType === 2" class="button-last" :disabled="treeStatus == 1||treeStatus == 0||treeData ===''" type="primary" @click="handleComplete()">完成</el-button>
        </div>
      </div>
      <treeProcess :is-visible="processVisable" :tree-process-val="treeDataProcess" @close="closeSikaoRizhi"/>
    </div>
  </template>

  <script>
  import { mapGetters } from 'vuex';
  import {
    CheckContentRight,
    updateAbilityMapping,
    SchemeDetail,
    GetDecision,
    queryAbilityMapping,
    PlanTaskDelete,
    TaskPageList,
    PlanTaskEdit,
    queryCache,
    addCache,
    removeCache,
    querySchemeDetailById
  } from '@/api/planGenerateApi.js'
  import { Transformer } from 'markmap-lib'
  import { Markmap } from 'markmap-view'
  import treeProcess from './treeProcess.vue';
  import panzoom from 'panzoom';
  import MyEditor from './mdEditor.vue';
  import MyEditorPreview from './mdEditorPreview.vue';

  export default {
    components: {
      treeProcess,
      MyEditor,
      MyEditorPreview
    },
    props: {
      agentSenceCode: {
        type: String,
        default: '',
      },
      treeDataVal: {
        type: String,
        default: '',
      },
      treeProcessVal: {
        type: String,
        default: '',
      },
      treeStatus:{
        type: Number,
        default: -1,
      },
      hasChatingName: {
        type: String,
        default: '',
      },
      displayType: {
        type: Number,
        default: 1,
      }
    },
    data() {
      return {
        contentEditor: '',
        saveLoading: false,
        isSuperAdmin: false, // 是否超级管理员
        rules: {
          name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
        },
        dialogTableVisible: false,
        gridData:[],
        changeData:[],
        tableLoading: false, // 加载状态
        detailContent: {text: '',file_url: ''},
        hisDetail: '',
        processContent: {text: ''},
        tableData: {
          list: [], // 表格数据
          page: 1,
          pageSize: 10,
          total: 0
        },
        systemMessages: '',
        timer: null,
        taskStatus: 0,
        isDragging: false,
        leftWidth: '50%',
        rightWidth: '',
        totalWidth: 1000,
        isEdit: false,
        planDetailShow: true,
        rightFullFlag: false,
        thinkFlag: false,
        thinkFullFlag: false,
        taskLoading: false,
        writeFlag: true,
        writeText: '',
        treeData:'',
        treeDataProcess: '',
        jueceYulanFlag: true,
        processVisable: false, // 思考过程弹窗标志
        panZoomRef: null,
        dataThDatas: [],
        dataStatus: '',
        checkNum: 0,
        isCache:false,
        isCacheDisabled:true,
      };
    },
    computed: {
      ...mapGetters({
        isAdmin: 'common/getIsAdminGetter'
      }),
    },
    watch: {
      treeStatus: {
        handler(val) {
          console.log("最后这个值是多少啊？啊？",this.taskLoading, val, this.agentSenceCode);
          if(val === 2 && (['device_ops_assistant_scene' ,'device_ops_assistant_scene-v1','artificial_handle_scene' ,'visit_leader_cognition_scene','intelligent_conversation_scene', 'sop_scene','rule_generation_scene'].indexOf(this.agentSenceCode) > -1)){
            console.log('重新生成完成');
            this.taskLoading = false
            setTimeout(()=>{
              this.queryCacheHandle()
            },2000)
          } else if(val === 2 && this.agentSenceCode ==='custom_cognition_assistant_scene'){
            this.taskLoading = false
            this.thinkingHandle()
            setTimeout(()=>{
              this.queryCacheHandle()
            },2000)
          }
          else if(val === 2 && ['device_ops_assistant_scene' ,'device_ops_assistant_scene-v1','artificial_handle_scene' ,'visit_leader_cognition_scene','intelligent_conversation_scene', 'sop_scene','rule_generation_scene'].indexOf(this.agentSenceCode) === 0){
            this.taskLoading = false
            this.queryTableData()
          }
          else if(val === 1 && ['device_ops_assistant_scene' ,'device_ops_assistant_scene-v1','artificial_handle_scene' ,'visit_leader_cognition_scene','intelligent_conversation_scene', 'sop_scene','rule_generation_scene'].indexOf(this.agentSenceCode) === 0){
            this.taskLoading = true
          }else if(val === 0){
            this.taskLoading = false
          } else if(val === 3) {
            // 生成失败
            this.taskLoading = false;
          }
        },
        immediate: true
      },
      treeDataVal: {
        handler(val) {
          this.treeData = val
        },
        immediate: true
      },
      treeProcessVal: {
        handler(val) {
          this.treeDataProcess = val
        },
        immediate: true
      },
    },
    async created() {

    },
    beforeDestroy() {
      clearInterval();
      clearInterval(this.timer);
      this.timer = null;
    },
    // 生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
      console.log('宽度', this.rightWidth);
      this.taskStatus = this.$route.query.status;
      const nodeMarkmap = document.getElementById('markmap');
      if(nodeMarkmap) {
        nodeMarkmap.innerHTML = '';
      }
      if(['device_ops_assistant_scene','device_ops_assistant_scene-v1' ,'custom_cognition_assistant_scene' ,'artificial_handle_scene' ,'visit_leader_cognition_scene' ,'rule_generation_scene','intelligent_conversation_scene', 'sop_scene'].indexOf(this.agentSenceCode) > -1){
        await this.queryDecision()
      }else{
        await this.queryTableData()
      }
      await this.queryPlanDetail();
      // await this.connectSocket();
    },
    methods: {
      setTaskLoading(val){
        this.taskLoading = val
      },
      handleComplete() {
        this.$router.push({path: '/planGenerate/index', query: {workspaceId: this.$route.query.workspaceId, workspaceName: this.$route.query.workspaceName}})
        querySchemeDetailById({scheme_id: Number(this.$route.query.id)}).then(res => {
          const name = res.data.result.name;
        })
      },
      async queryCacheHandle () {
        const name=this.agentSenceCode ==='custom_cognition_assistant_scene'?'mind_map':'decision_tree_generate'
        queryCache({scheme_id: this.$route.query.id,ability_name:name}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            if(res.data.result){
              this.isCache = res.data.result.isCache;
              this.isCacheDisabled = false
            }

          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      },
      handleCommand (command) {
        console.log(command,'111')
        const name=this.agentSenceCode ==='custom_cognition_assistant_scene'?'mind_map':'decision_tree_generate'
        if (command === 'sikao') {
          this.showSikao();
        }else if(command === 'addCache'){
          addCache({scheme_id: this.$route.query.id,ability_name:name}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '新增成功'
            });
            this.isCache = !this.isCache
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });

        } else if(command === 'removeCache'){
          removeCache({scheme_id: this.$route.query.id,ability_name:name}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '删除成功'
            });
            this.isCache = !this.isCache

          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
        } else {
          this.copyText();
        }
      },
      copyText() {
        // 获取需要复制的文本
        const text = this.treeData;
        navigator.clipboard.writeText(text)
        .then(() => {
          this.$message({
              type: 'success',
              message: '复制成功！'
            });
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          });
        });
      },
      fangda (e) {
        // console.log('开启缩放', e.target.getElementsByTagName('svg'));
        const svgdoms = e.target.getElementsByTagName('svg');
        const arr = [...svgdoms];
        arr.forEach((svgdom) => {
          if (svgdom.id.indexOf('mermaid') > -1) {
            panzoom(svgdom, {
              smoothScroll: false,
              bounds: true,
              // autocenter: true,
              zoomDoubleClickSpeed: 1,
              minZoom: 0.1,
              maxZoom: 20,
            })
          }
        })
      },
      // 显示生成过程
      showSikao () {
        console.log('生成过程显示');
        this.processVisable = true;
      },
      closeSikaoRizhi () {
        this.processVisable = false;
      },
      handleClose(){
        this.dialogTableVisible = false
        this.saveLoading = false
        clearInterval(this.timer)
        this.timer = null
      },
      handleDone () {
        console.log(this.gridData,'222')
        updateAbilityMapping({
          scheme_id:this.$route.query.id,
          config:{
            header: this.dataThs,
            data: this.gridData
          },
          ability_status:'finished'
        }).then((res)=>{
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.$message({
              type: 'success',
              message: '更新完成!'
            });

            this.dialogTableVisible = false
            this.saveLoading = false
          }else{
            this.$message({
              type: 'success',
              message: '更新失败!'
            });
            this.dialogTableVisible = false
            this.saveLoading = false
          }
        })
      },

      handleAbilityMapping(){
      this.saveLoading = true
      if (this.timer != null) {
      return
      }
      this.timer = setInterval(() => {
        queryAbilityMapping({scheme_id:this.$route.query.id})
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.dataStatus = res.data.result.ability_status;
              const status= res.data.result.ability_status
              let configData = {}
              try {
                configData = JSON.parse(res.data.result?.config) || {};
              } catch (error) {
                configData = res.data.result?.config || {}
              }
              if (status !== 'generating') {
                this.saveLoading = false
                clearInterval(this.timer)
                this.timer = null
              }
              console.log(configData,'111')
              this.dataThs = configData.header || {};
              this.dataThDatas = Object.keys(configData.header||{}).map(item => {return {name: configData.header[item], field: item}})
              this.gridData = configData.data || [];
              //   this.gridData = configData.map((item)=>{
            //   return {
            //     param_name: item.param_name,
            //     dataset: item.dataset,
            //     param_key: item.param_key,
            //     test_point:item.test_point,
            //     frequency: item.frequency,
            //     calculation_formula: item.calculation_formula,
            //   }
            // })
              this.dialogTableVisible = true
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            clearInterval(this.timer)
            this.timer = null
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
          });
      }, 1000)
      },
      // 关闭数据配置
      shujuCompete () {
        this.dialogTableVisible = false;
        if (this.treeData && (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1)) {

        } else {
          if(this.jueceYulanFlag){
            this.thinkingHandle()
          }
        }
      },
      // 预览
      yuLan(){
        this.jueceYulanFlag = !this.jueceYulanFlag
        if (this.treeData && (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1)) {

        } else {
          if(this.jueceYulanFlag){
            this.thinkingHandle()
          }
        }
      },
      // 思维图渲染模式切换
      changeShowType () {
        this.jueceYulanFlag = !this.jueceYulanFlag;
      },
      // 渲染思维图
      thinkingHandle(){
        if (this.treeData) {
          if (this.treeData.indexOf('mermaid') > -1 || this.treeData.indexOf('graph') > -1 || this.treeData.indexOf('flowchart') > -1) {

          } else {
            const transformer = new Transformer()
            const { root } = transformer.transform(this.treeData)
            this.$nextTick(() => {
              Markmap.create('#markmap',null,root)
            })
          }
        }
      },
      deleteTask(row) {
        this.$confirm('此操作将删除该任务，是否继续?', '删除', {
          customClass: 'last-dialog',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.tableLoading = true;
            PlanTaskDelete({id: row.id}).then((res) => {
              this.tableLoading = false;
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                });
                this.tableData.page = 1;
                this.tableLoading = false;
                this.$nextTick(() => {
                  this.queryTableData();
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            })
            .finally(() => {
              this.tableLoading = false;
            });
          })
          .catch(() => {
            this.tableLoading = false;
            this.queryTableData();
          });
      },
      queryTableData() {
        this.treeStatusLast = 2;
        this.tableLoading = true;
        const param = {
          offset: this.tableData.page,
          limit: this.tableData.pageSize,
          scheme_id: this.$route.query.id,
          sort_field: 'create_time',
          order: 'desc',
        };
        TaskPageList(param)
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.tableData.list = res.data.result.items.map(item => {
                return {
                  ...item,
                  statusFlag: item.status !== '未完成'
                }
              });
              this.tableData.total = res.data.result.total;

            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      calcIndex(index) {
        return this.tableData.page > 1 ? (index + 1) + (this.tableData.pageSize * (this.tableData.page - 1)) : index + 1
      },
      handleSizeChange(val) {
        this.tableData.page = 1;
        this.tableData.pageSize = val;
        this.queryTableData();
      },
      handleCurrentChange(val) {
        this.tableData.page = val;
        this.queryTableData();
      },
      changeViews(val) {
        this.$emit('updateStep', val)
        querySchemeDetailById({scheme_id: Number(this.$route.query.id)}).then(res => {
          const name = res.data.result.name;
        })
      },
      regenerate(){
        this.treeData = ''
        this.treeProcess = '';
        this.taskLoading = true;
        this.$emit('updateGenerate')
        querySchemeDetailById({scheme_id: Number(this.$route.query.id)}).then(res => {
          const name = res.data.result.name;
        })
      },
      queryDecision(){
        const params = {
          scheme_id: this.$route.query.id,
          scheme_status:(['device_ops_assistant_scene','device_ops_assistant_scene-v1' ,'artificial_handle_scene' ,'visit_leader_cognition_scene','rule_generation_scene','intelligent_conversation_scene', 'sop_scene'].indexOf(this.agentSenceCode) > -1)?'decision_tree':'mind_map'
        }
        GetDecision(params).then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.treeData = res.data.result?.decision_making_content || '';
            this.treeDataProcess = res.data.result?.sub_content || '';
            if(this.treeData){
                // 思维图
              this.thinkingHandle()
            }else{
              const nodeMarkmap = document.getElementById('markmap');
              if(nodeMarkmap) {
                nodeMarkmap.innerHTML = '';
              }
            }
            this.queryCacheHandle();
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      },
      async handleDetailSave(){
        const { id, ...rest } = this.detailContent;
        const res = await PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
        if(res?.data?.code !== 200){
          this.$message.error(res?.data?.msg || '编辑失败');
          return;
        }
        this.$message.success('编辑成功');
        this.isEdit = false;
        this.queryPlanDetail();
      },
      handleUpdateContent(val) {
        this.detailContent.text = val;
      },
      handleDetailSaveClose () {
        this.isEdit = false;
        this.detailContent.text = this.hisDetail;
      },
      goToDetail(task) {
        task.adjust_url && window.open(task.adjust_url)
      },
      startDrag(event) {
        if (!this.isDragging) {
          this.isDragging = true;
          this.startX = event.clientX;
          // console.log('this.startX', this.startX, this.rightWidth);
          const leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
          this.startWidth = leftWidth;
          document.addEventListener('mousemove', this.onDrag);
          document.addEventListener('mouseup', this.stopDrag);
        }
      },
      onDrag(event) {
        if (this.isDragging) {
          const deltaX = event.clientX - this.startX;
          const widthLeft = this.startWidth + deltaX;
          // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
          this.leftWidth = widthLeft + 'px';
          this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
        }
      },
      stopDrag() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.onDrag);
        document.removeEventListener('mouseup', this.stopDrag);
      },
      getWsID () {
        let workspaceId = '';
        // console.log('ceshi', router?.currentRoute?.query)
        if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
          workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
        } else {
          workspaceId = this.$router?.currentRoute?.query.workspaceId
        }
        if(!workspaceId) {
          try {
            const [hash, query] = window.location.href.split('#')[1].split('?')
            const params = Object.fromEntries(new URLSearchParams(query))
            workspaceId = params.workspaceId
          } catch (error) {
            console.log('error', error)
          }
        }
        return workspaceId
      },
      scrollToBottom() {
        this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10;
      },
      changeShowRight () {
        this.planDetailShow = !this.planDetailShow
        if (this.planDetailShow) {
          this.rightWidth = '';
          this.leftWidth = '50%'
        } else {
          this.rightWidth = '';
          this.leftWidth = '0px'
        }
      },
      changeShowFull () {
        this.rightFullFlag = !this.rightFullFlag;
        if(this.rightFullFlag) {
          this.leftWidth = '100%'
          this.rightWidth = '0';
        } else {
          this.leftWidth = '50%'
          this.rightWidth = '100%';
        }
      },
      closechangeThinkWrap () {
        this.thinkFlag = !this.thinkFlag;
        this.thinkFullFlag = false;
        if (this.thinkFlag) {
          this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
        } else {
          this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
        }
      },
      // 显示思考过程
      changeThinkWrap (data) {
        this.thinkFlag = !this.thinkFlag;
        this.thinkFullFlag = false;
        if (this.thinkFlag) {
          this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
        } else {
          this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
        }
        if (data) {
          this.processContent.text = data;
        } else {
          this.processContent.text = this.processContent.text || ''
        }
      },
      changeThinkFull () {
        this.thinkFullFlag = !this.thinkFullFlag;
      },
      saveFangan() {
        console.log('修改的值',this.contentEditor.getValue());
        if (this.hasChatingName === '') {
          this.detailContent.text = this.contentEditor.getValue()
          const { id, ...rest } = this.detailContent;
          PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
        }
      },
      async queryPlanDetail () {
        SchemeDetail({scheme_id: this.$route.query.id}).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.detailContent = res.data.result;
            // this.detailContent.text = '```mermaid\ngraph LR\nA[变压器运维方案]\nB[日常维护]\nC[定期检修]\nD[长期检修]\nE[变压器故障]\nF[常见故障]\nG[故障诊断]\nH[故障解决]\nA-->B\nB-->B1(检测冷却设备)\nB-->B2(检查油位)\nB-->B3(检查温度)\nA-->C\nC-->C1(3个月-检查油样)\nC-->C2(6个月或1年-检验绝缘电阻)\nC-->C3(1到2年-进行大修)\nA-->D\nD-->D1(长期维护节点-沟通制造商)\nA-->E\nE-->F\nF-->F1(变压器热故障)\nF-->F2(绝缘破损)\nF-->F3(电极磨损)\nF-->F4(机械故障)\nF-->F5(油位故障)\nF-->F6(油质故障)\nF-->F7(电磁故障)\nE-->G\nG-->G1(观察法)\nG-->G2(试验法)\nG-->G3(分析法)\nG-->G4(维护记录法)\nG-->G5(无损检测)\nG-->G6(红外热像技术)\nG-->G7(振动检测)\nE-->H\nH-->H1(调整变压器油位)\nH-->H2(修复冷却系统)\nH-->H3(修复绝缘破损)\nH-->H4(修理机械故障)\nH-->H5(更换电极部件)\nH-->H6(红外热像处理)\nH-->H7(无损检测和振动检测)\n```';
            if (res.data.result?.text) {
              this.taskStatus = 1;
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      },

      startWriteOpt () {
        const content = document.getElementById('detail-content');
        content.addEventListener('mouseup', () => {
          const selection = window.getSelection();
          console.log('selection', selection)
          if (selection.toString() !== '') {
            // 用户选中了文本，执行相应的动作
            console.log('用户选中了文本：' + selection.toString());
            this.writeText = selection.toString();
            const range = selection.getRangeAt(0);
            this.range = range;
            this.writeFlag = false;
          } else {
            this.writeText = '';
            this.writeFlag = true;
          }
        });
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  :deep(.el-loading-spinner) {
    width: 130px !important;
    background: none !important;
  }
  :deep(.el-loading-spinner2) {
    width: 130px !important;
    background: none !important;
    margin-top: 20px;
  }
  .cardEmpty {
    display: flex;
    width: 100%;
    height: 100%;
    align-items:center;
    justify-content: center;
  }
  .containerBox2{
    &.containerBox2IFrame {
      height: 100%;
      .containerBox {
        height: calc(100vh - 104px) !important;
        max-height: calc(100vh - 104px) !important;
      }
      .containerCardFull {
        top: -16px !important;
        height: calc(100% - 0px) !important;
        max-height: calc(100% - 0px) !important;
      }
      .fanganyouhua {
        background: #fff;
        top: 0px !important;
        height: calc(100vh - 0px) !important;
        max-height: calc(100vh - 0px) !important;
      }
      .chatRightFull {
        top: -16px !important;
        height: 100vh !important;
        max-height: 100vh !important;
      }
      .optScroll {
        height: calc(100vh - 220px) !important;
        max-height: calc(100vh - 220px) !important;
      }
      .chatRight .optScroll {
        height: calc(100vh - 220px) !important;
        max-height: calc(100vh - 220px) !important;
        .optContentBox {
          height: calc(100vh - 220px) !important;
        }
      }
    }
  }
  .chatContainer {
    height: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .headerBox{
      background-color: #fff;
      .headerTitle {
        padding: 14px 20px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #EBECF0;
        .title {
          font-weight: bold;
          color: #323233;
          line-height: 26px;
          font-size: 18px;
        }
      }
    }

    .containerBox2 {
      display: flex;
      flex-direction: row;
      height: calc(100%);
      max-height: calc(100%);
      overflow-y: hidden;
      position: relative;
      .showRightFix {
        position: absolute;
        right: 6px;
        top: 24px;
        width: 30px;
        height: 30px;
        background: #4068D4;
        border-radius: 2px;
        text-align: center;
        line-height: 27px;;
        z-index: 2;
        color: #fff;
        cursor: pointer;
        &:hover{
          background: #3455ad;
        }
        &:active{
          background: #264480;
        }
        img {
          width: 12px;
          height: auto;
        }
      }
      .containerCard {
        //height: calc(100% - 18px);
        // max-height: calc(100vh - 210px);
        overflow-y: hidden;
        overflow-x: hidden;
        margin: 16px 16px 0px 0px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        background-color: #fff;
        margin-left: 16px;
        &.containerCardFull {
          position: fixed !important;
          top: 32px;
          z-index: 2005;
          height: calc(100% - 50px);
          max-height: calc(100% - 50px);
          width: 100%;
          left: 0px;
          width: 100%;
          margin-left: 0px !important;
          .chatScroll {
            max-height: calc(100vh - 220px) !important;
          }
          .optScroll {
            height: calc(100vh - 170px) !important;
            max-height: calc(100vh - 170px) !important;
          }
          .optContentBox {
            height: calc(100vh - 200px) !important;
            max-height: calc(100vh - 200px) !important;
          }
        }
        .optContentBox {
          // height: calc(100vh - 340px);
          min-height: 100%;
          max-height: 100%;
          width: 100%;
          position:relative;
          overflow-y: auto;
        }
        .optHeader {
          padding: 0px 20px;
          border-bottom: 1px solid #EBECF0;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTitle {
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .optScroll  {
          position: relative;
          // height: calc(100vh - 330px);
          max-height: calc(100vh - 330px);
          overflow-y: hidden;
          overflow-x: hidden;
          padding: 20px;
          display:flex;
          ::v-deep .el-textarea{
            margin-bottom: 10px;
          }
          .btn{
            position: absolute;
            bottom: 0;
            right: 20px;
          }
        }
        .optContent {
          max-height: calc(100% - 60px);
          overflow-y: hidden;
        }
        .optFooter {
          position: absolute;
          bottom: 0px;
          left: 0px;
          width: 100%;
          background: #FFFFFF;
          box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 12px 20px;
          min-height: 54px;
      }
        .chatHeader {
          font-size: 14px;
          color: #323233;
          line-height: 24px;
          font-weight: bold;
          background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
          background-size: 100% 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          padding: 0px 20px;
          .rightTitle {
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .thinkContent  {
          margin-left: 16px;
          width: calc(100% - 32px);
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          max-height: 225px;
          height: 225px;
          overflow-y: auto;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #DCDDE0;
          transition: height 0.1s;
          &.thinkContentFull {
            position: absolute !important;
            left: 0px;
            width: calc(100vw - 214px) !important;
            height: calc(100vh - 150px) !important;
            overflow: hidden;
            z-index: 2;
            max-height: calc(100vh - 150px) !important;
            top: 0px;
          }
          .thinkHeader {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 12px 12px;
            .title {
              color: #323233;
              line-height: 20px;
              display: flex;
              align-items: center;
              img {
                height: 24px;
                width: 24px;
                margin-right: 4px;
              }
            }
            .thinkOpt {
              display: flex;
              .think-btn {
                font-size: 14px;
                margin-left: 4px;
                cursor: pointer;
                width: 24px;
                height: 24px;
                text-align: center;
                line-height: 22px;
                font-weight: bold;
                &.think-btn-blue {
                  background-color: #4068D4 !important;
                  border-radius: 4px;
                  &:hover{
                    background: #3455ad !important;
                  }
                  &:active{
                    background: #264480;
                  }
                }
                &:hover {
                  background-color: #ebecf0;
                  border-radius: 4px;
                }
                img {
                  width: 12px;
                  height: 12px;
                }
              }
            }
          }
          .thinkWrap {
            background: #FFFFFF;
            padding: 0px 12px 12px 36px;
            max-height: calc(100% - 40px);
            overflow-y: auto;
            .thinkItem {
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              justify-content: space-start;
              padding: 8px 12px;
              border-radius: 4px;
              border: 1px solid #DCDDE0;
              margin-top: 12px;
              &:first-child {
                margin-top: 0px;
              }
            }
            .itemContent {
              color: #646566;
              line-height: 22px;
              flex: 1;
              margin-left: 8px;
            }
          }
        }
      }
      .chatRight {
        flex: 1;
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.06);
        border-radius: 4px;
        height: calc(100% - 18px);
        max-height: calc(100% - 18px);
        overflow-y: hidden;
        margin-top: 16px;
        position: relative;
        &.chatRightFull {
          position: fixed !important;
          top: 32px;
          z-index: 2005;
          height: calc(100% - 50px);
          width: 100%;
          left: 0px;
          width: 100%;
          margin-left: 0px !important;
          .optScroll {
            height: calc(100vh - 150px) !important;
            max-height: calc(100vh - 150px) !important;
          }
          .optScroll2 {
            height: calc(100vh - 110px) !important;
            max-height: calc(100vh - 110px) !important;
          }
          .optContentBox {
            height: calc(100vh - 180px) !important;
          }
        }
        .optContentBox {
          height: calc(100vh - 340px);
          width: 100%;
          position:relative;
        }
        .optHeader {
          padding: 0px 20px;
          border-bottom: 1px solid #EBECF0;
          display: flex;
          flex-direction: row;
          align-items: center;
          .rightTitle {
            flex: 1;
            font-size: 14px;
            font-weight: bold;
            color: #323233;
            line-height: 22px;
            padding: 12px 0px;
          }
          .rightTitleOpt {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .rightTextBtn {
              background-color: #406BD4;
              font-size: 12px;
              color: #fff;
              padding: 0px 6px;
              height: 24px;
              line-height: 24px;
              border-radius: 2px;
              margin-left: 8px;
              cursor: pointer;
              &:hover{
                background: #3455ad;
              }
              &:active{
                background: #264480;
              }
            }
            .rightBtn {
              // background: #F2F3F5;
              border-radius: 2px;
              width: 30px;
              height: 30px;
              color: #4068D4;
              margin-left: 8px;
              text-align: center;
              line-height: 28px;
              cursor: pointer;
              &:hover{
                background: #ebecf0;
              }
              &:active{
                background: #dcdde0;
              }
              &.rightBtnBlue {
                background-color: #406BD4;
                &:hover{
                  background: #3455ad;
                }
                &:active{
                  background: #264480;
                }
              }
              img {
                width: 16px;
                height: auto;
              }
            }
          }
        }
        .optScroll  {
          position: relative;
          height: calc(100vh - 330px);
          max-height: calc(100vh - 330px);
          overflow-y: auto;
          overflow-x: hidden;
          padding: 20px;
          display: flex;
          ::v-deep .el-textarea{
            margin-bottom: 10px;
          }
          .btn{
            position: absolute;
            bottom: 0;
            right: 20px;
          }
        }
        .optContent {
          max-height: calc(100% - 60px);
          overflow-y: hidden;
        }
        .optFooter {
          position: absolute;
          bottom: 0px;
          left: 0px;
          width: 100%;
          background: #FFFFFF;
          box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 12px 20px;
          min-height: 54px;
      }
        }
    }
    .resize {
      cursor: col-resize;
      background-color: #f4f5f9;
      padding: 0px 8px;
      width: 10px;
      color: #c3cadd;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:hover {
        background: #e0e6ff;
        .process-icon {
          color: #3455ad !important;
        }
      }
      .el-two-column__icon-top {
        height: 50%;
        width: 4px;
        display: flex;
        flex-direction: column-reverse;
        .el-two-column__icon-top-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
        }
      }
      .el-two-column__trigger-icon {
        width: 25px;
        height: 25px;
        color: #c3cadd;
        .process-icon {
          width: 25px;
          color: #c3cadd;
        }
      }
      .el-two-column__icon-bottom {
        height: 50%;
        width: 4px;
        .el-two-column__icon-bottom-bar {
          height: 50%;
          width: 4px;
          background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
        }
      }
    }
    ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  }
  .descriptionTd {
    max-width: 250px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  ::v-deep .el-button--mini {
      line-height: 0px !important;
      padding: 8px 6px !important;
      img {
        height: 16px;
        margin-top: -2px;
      }
    }
    .flex_column{
      flex-direction: column;
    }
  </style>
