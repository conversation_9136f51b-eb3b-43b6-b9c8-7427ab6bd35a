<template>
    <div class="thinkItem">
        <div class="itemContent">
          <MyEditor id="MyFanganEditorSikao" ref='MyFanganEditorSikao' :mdContent="processContent"></MyEditor>
          <!-- <vue-markdown v-highlight :source="processContent" :options="options" class="markdown-body chat-markdown"></vue-markdown> -->
        </div>
    </div>
  </template>
  <script>
  import MarkdownItMermaid from 'markdown-it-mermaid';
  import MyEditor from './mdEditor.vue';

  export default {
    name: 'ProcessContent',
    components: {
      MyEditor
    },
    props: {
      processContent: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: '',
        options: {
            preset: 'default',
            html: true,
            breaks: true,
            linkify: true,
            typography: true,
            use: [
                [MarkdownItMermaid]
            ]
        }
      };
    },
    watch: {
        processContent: {
        handler(val) {
          if (val) {
            this.data = val
          }
        },
        immediate: true
      }
    }
  };
  </script>
  <style lang="scss" scoped>
  .thinkItem {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-start;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #DCDDE0;
    margin-top: 12px;
    &:first-child {
        margin-top: 0px;
    }
    .itemContent {
              color: #646566;   
              line-height: 22px;
              flex: 1;
              margin-left: 8px;
            }
    }
  </style>
  