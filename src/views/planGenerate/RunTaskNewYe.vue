<template>
  <div>
    <el-dialog
      custom-class="last-dialog2"
      title="执行任务"
      :visible.sync="showFlag"
      :before-close="onClose"
      :close="onClose"
      width="96%"
    >
      <div slot="title" class="dialog-title">
        <div class="title-text">执行任务</div>
        <div class="zuixiaohua" @click="miniModalShow">
          <el-tooltip class="item" effect="dark" content="最小化" placement="top">
            <img src="@/assets/images/planGenerater/full.png" />
          </el-tooltip>
        </div>
      </div>
      <div class="task-container">
        <div class="task-left" :style="fullSizeFlag ? 'height: calc( 100vh - 184px )' : 'height: calc( 70vh - 144px )'">
          <div class="task-list">
            <div v-for="(item, index) in taskList" :key="index" class="task-list-item">
              <div class="task-icon">
                <SvgIcon v-if="item.status !== 3" :name="iconMap(index, item.status)" />
                <div v-else-if="item.status === 3" class="loading-task">
                  <i class="el-icon-refresh-right"></i>
                </div>
                <div
                  v-if="index !== taskList.length - 1"
                  :class="
                    item.status === 0 || !item.status
                      ? 'task-line task-line-info'
                      : item.status === 2
                      ? 'task-line task-line-error'
                      : 'task-line'
                  "
                ></div>
              </div>
              <div class="task-text-wrap">
                <div class="task-text">
                  <div class="task-text-title" @click="showDetail(index)">{{ item.title }}</div>
                  <div class="task-text-link">
                    <el-link
                      v-if="item.status === 3"
                      type="primary"
                      :underline="false"
                      @click="stopGen(index)"
                      >停止生成</el-link
                    >
                    <el-tooltip class="item" effect="dark" content="重试" placement="top">
                      <el-link
                        v-if="
                          item.status === 1 ||
                          item.status === 2 ||
                          (taskList.filter((titem) => titem.status === 0).length === 7 &&
                            index === 0)
                        "
                        icon="el-icon-refresh"
                        :underline="false"
                        style="padding-left: 8px"
                        @click="redoTask(item, index)"
                      ></el-link>
                    </el-tooltip>

                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="flodMap[index] ? '收起' : '展开'"
                      placement="top"
                    >
                      <el-link
                        :icon="flodMap[index] ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"
                        :underline="false"
                        style="padding-left: 8px"
                        @click="foldFun(index)"
                      ></el-link>
                    </el-tooltip>
                  </div>
                </div>
                <div :class="['task-text-process', flodMap[index] ? 'show' : '']">
                  <div v-if="item.runCode === 'model_param'">
                    <pre style="white-space: pre-line; margin-bottom: -20px">
                      {{ treeDataProcess.replaceAll('**', '').replaceAll('`', '') || item.process }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === 'model_info'">
                    <pre style="white-space: pre-line; margin-bottom: -20px">
                      {{ runStreamVal.replaceAll('**', '').replaceAll('`', '') || item.process }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === 'code_analysis'">
                    <MyEditorPreview
                      :id="'MyEditorTree' + item.runCode"
                      :ref="'MyEditorTree' + item.runCode"
                      :height-sta="true"
                      :md-content="codeAnalysisProcessFirst || item.process || '暂无'"
                    ></MyEditorPreview>
                    <!-- {{ codeAnalysisProcessFirst || item.process }} -->
                  </div>
                  <div v-else-if="item.runCode === 'math_model'">
                    <pre style="white-space: pre-line" class="max_height">
                      {{ alignProcessData || item.process }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === 'decision_making_generate'">
                    <MyEditorPreview
                      :id="'MyEditorTree' + item.runCode"
                      :ref="'MyEditorTree' + item.runCode"
                      :md-content="codeProcessData || item.process"
                      :height-sta="true"
                    ></MyEditorPreview>
                  </div>
                  <div v-else-if="item.runCode === 'code_deploy'">
                    {{ item.status === 1 ? '部署成功' : '暂无' }}
                  </div>
                  <div v-else-if="item.runCode === 'code_test'">
                    {{ item.status === 1 ? '代码测试成功' : '暂无' }}
                  </div>
                  <div v-else>暂无</div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div id="content-item1" class="content-item"><DataAns :run-stream-list="runStreamList" :all-success="allSuccess" :template-id="templateId" :status="taskList?.[0]?.status"></DataAns></div>
          <div id="content-item2" class="content-item"><DataAlign :data-align-data-status="dataAlignDataStatus" :data-align-data="dataAlignData" :status="taskList?.[1]?.status"></DataAlign></div>
          <div id="content-item3" class="content-item"><CodeCreate :code-data="codeData" :code-data-status="codeDataStatus" :status="taskList?.[2]?.status" :process-status="processStatus"></CodeCreate></div>
          <div id="content-item4" class="content-item"><CodeDeploy :update-develop-flag="updateDevelopFlag"  :status="taskList?.[3]?.status" @handleOk="handleDevelopOk"></CodeDeploy></div>
          <div id="content-item5" class="content-item"><CodeTest :update-test-flag="updateTestFlag" :status="taskList?.[4]?.status" @handleOk="handleTestOk"></CodeTest></div> -->
        </div>
        <div :class="['task-right', detailIndex > -1 ? 'showRight' : '']" :style="fullSizeFlag ? 'height: calc( 100vh - 184px )' : 'height: calc( 70vh - 144px )'">
          <div v-if="detailIndex > -1" class="header-title">
            <div style="font-weight: bold">{{ taskList[detailIndex].title }}</div>
            <div class="editorMark">
              <p v-if="detailIndex == 3" style="color: transparent">请确保内容为Jason格式</p>
              <p
                v-if="detailIndex == 0 || detailIndex == 1 || detailIndex == 2"
                style="color: transparent"
              >
                markdown格式
              </p>
              <div>
                <el-button
                  v-if="detailIndex === 2 && isreadOnly"
                  :disabled="taskList[detailIndex].status === 3"
                  type="primary"
                  @click="redoTask(taskList[detailIndex], detailIndex)"
                  >辅助检查</el-button
                >
                <el-button
                  v-if="detailIndex < 4 && isreadOnly"
                  :disabled="taskList[detailIndex].status === 3"
                  type="primary"
                  @click="editJsonMark(taskList[detailIndex].runCode)"
                  >编辑</el-button
                >
                <el-button
                  v-if="detailIndex < 4 && !isreadOnly"
                  type="primary"
                  @click="updJsonMark(taskList[detailIndex].runCode)"
                  >保存</el-button
                >
                <el-button
                  v-if="detailIndex < 4 && !isreadOnly"
                  type="info"
                  @click="editJsonMark(taskList[detailIndex].runCode)"
                  >取消</el-button
                >

                <!-- <el-button
                  v-if="detailIndex === 0"
                  type="info"
                  @click="formatChecks(taskList[detailIndex].runCode)"
                  >格式校验</el-button
                > -->
              </div>
            </div>
            <el-link
              :underline="false"
              icon="el-icon-close"
              type="primary"
              @click="() => (detailIndex = -1)"
            ></el-link>
          </div>
          <div class="task-show">
            <codeJsonEdit
              v-if="detailIndex === 0"
              key="model_param"
              ref="model_param"
              :tree-data-val="treeDataVal"
              language-type="markdown"
              :stop-scroll="customStopScrollTobottom"
            >
            </codeJsonEdit>
            <!-- <TreeMind
              v-if="detailIndex === 0"
              :tree-data-val="treeDataVal"
              :tree-status="treeStatus"
              :status="taskList?.[0]?.status"
            ></TreeMind> -->
            <!-- <codeInput
              v-if="detailIndex === 1"
              ref="model_info"
              key="model_info"
              :run-stream-list="runStreamList"
            >
            </codeInput> -->
            <codeJsonEdit
              v-if="detailIndex === 1"
              ref="model_info"
              key="model_info"
              :tree-data-val="runStreamList"
              language-type="markdown"
              :stop-scroll="customStopScrollTobottom"
            >
            </codeJsonEdit>
            <!--
            <DataAns
              v-if="detailIndex === 1"
              :run-stream-list="runStreamList"
              :all-success="allSuccess"
              :template-id="templateId"
              :status="taskList?.[1]?.status"
            ></DataAns> -->
            <codeJsonEdit
              v-if="detailIndex === 2"
              key="math_model"
              ref="math_model"
              :tree-data-val="dataAlignData"
              :data-align-data-status="dataAlignDataStatus"
              :el-box="`math_model`"
              language-type="markdown"
              :stop-scroll="customStopScrollTobottom"
            >
            </codeJsonEdit>
            <!-- <DataAlign
              v-if="detailIndex === 2"
              :data-align-data-status="dataAlignDataStatus"
              :data-align-data="dataAlignData"
              :status="taskList?.[2]?.status"
            ></DataAlign> -->
            <CodeCreate
              v-if="detailIndex === 3"
              ref="decision_making_generate"
              key="decision_making_generate"
              :code-data="codeData"
              :code-data-status="codeDataStatus"
              :status="taskList?.[3]?.status"
              :process-status="processStatus"
            ></CodeCreate>
            <CodeDeploy
              v-show="detailIndex === 4"
              :update-develop-flag="updateDevelopFlag"
              :status="taskList?.[4]?.status"
              @handleOk="handleDevelopOk"
            ></CodeDeploy>
            <CodeTest
              v-if="detailIndex === 5"
              :update-test-flag="updateTestFlag"
              :status="taskList?.[5]?.status"
              @handleOk="handleTestOk"
            ></CodeTest>
            <CodeAns
              v-if="detailIndex === 6"
              :tree-process-val="codeAnalysisProcess"
              :tree-process-val-first="codeAnalysisProcessFirst"
              :status="taskList?.[6]?.status"
            ></CodeAns>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <!-- :disabled="
            (taskList[4].status !== 1 &&
              taskList.filter((item) => item.status === 0).length !== 7) ||
            loading
          " -->
        <el-button
          type="info"
          :loading="loading"
          :disabled="
            (taskList[4].status !== 1 &&
              taskList.filter((item) => item.status === 0).length !== 7) ||
            loading
          "
          @click.stop="reStart"
          >重试</el-button
        >
        <!-- <el-button type="primary" :loading="loading" :disabled="loading" @click="accessDevlop"
          >转研发</el-button
        > -->
        <el-button
          v-if="
            taskList[6].status === 1 && taskList.filter((item) => item.status === 1).length === 7
          "
          type="info"
          :loading="loading"
          :disabled="loading"
          @click="onClose('finish')"
          >完成</el-button
        >
        <el-popconfirm
          v-else-if="taskList.filter((item) => item.status === 3).length"
          title="是否终止执行当前任务？"
          confirm-button-text="终止"
          cancel-button-text="取消"
          @confirm="confirmCancel"
          @cancel="onClose"
        >
          <el-button
            slot="reference"
            style="margin-left: 12px"
            type="info"
            :loading="loading"
            :disabled="loading"
            >终止</el-button
          >
        </el-popconfirm>
        <el-button v-else type="info" :loading="loading" :disabled="loading" @click="onClose"
          >取消</el-button
        >
      </div>
      <devlopModal
        :is-visible="devlopModalVisable"
        :cur-id="curId"
        :dev-person="devPersonInfo"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>
<script>
// import MyEditor from './mdEditor.vue';
import TreeMind from './taskNew/treeData.vue';
import DataAns from './taskNew/dataAns.vue';
import DataAlign from './taskNew/dataAlign.vue';
import CodeCreate from './taskNew/codeCreate.vue';
import CodeDeploy from './taskNew/codeDeploy.vue';
import CodeTest from './taskNew/codeTest.vue';
import CodeAns from './taskNew/codeAns.vue';
import devlopModal from './devlopModal.vue';
import MyEditorPreview from './mdEditorPreview.vue';
import codeJsonEdit from './taskNew/codeJsonEdit.vue';
import codeInput from './taskNew/codeInput.vue';
import {
  startAlignDataGenerate,
  startAbilityGenerate,
  reexecuteTask,
  AbilityTest,
  GetDecision,
  AbilityPublish,
  updateTasksRes,
  CodeEdit
} from '@/api/planGenerateApi.js';
import dataAnsProcess from './taskNew/dataAnsProcess.vue';
export default {
  name: 'RunTaskDialog',
  components: {
    // MyEditor,
    MyEditorPreview,
    TreeMind,
    DataAns,
    DataAlign,
    CodeCreate,
    CodeDeploy,
    CodeTest,
    devlopModal,
    CodeAns,
    codeInput,
    dataAnsProcess,
    AbilityPublish,
    codeJsonEdit
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    setModelData: {
      type: Function
    },
    // 会话流的唯一id
    sessionId: {
      type: String,
      default: ''
    },
    runStreamVal: {
      type: String,
      default: ''
    },
    runStreamList: {
      type: String,
      default: ''
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    templateId: {
      type: [String, Number],
      default: ''
    },
    dataAlignDataStatus: {
      type: [String, Number],
      default: ''
    },
    dataAlignData: {
      type: String,
      default: ''
    },
    codeDataStatus: {
      type: [String, Number],
      default: ''
    },
    codeData: {
      type: String,
      default: ''
    },
    updateDevelopFlag: {
      type: [String, Number],
      default: 1
    },
    flowData: {
      type: Array,
      default() {
        return [];
      }
    },
    devPerson: {
      type: Object,
      default() {
        return {};
      }
    },
    processStatus: {
      type: [String, Number],
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    runStreamStatus: {
      type: Number,
      default: -1
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    codeAnalysisData: {
      type: String,
      default: ''
    },
    codeAnalysisDataStatus: {
      type: String | Number,
      default: ''
    },
    codeAnalysisProcessData: {
      type: String,
      default: ''
    },
    alignProcessData: {
      type: String,
      default: ''
    },
    codeProcessData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      customStopScrollTobottom: false,
      loading: false,
      showFlag: false,
      devlopModalVisable: false,
      updateTestFlag: 1, // 触发更新代码测试的
      devPersonInfo: {},
      curId: '',
      taskList: [],
      flodMap: [],
      detailIndex: -1,
      codeAnalysisProcessStatus: false,
      codeAnalysisProcess: '',
      codeAnalysisProcessFirst: '',
      ploading: false,
      treeData: '',
      treeDataProcess: '',
      taskLoading: false,
      processMap: [],
      isreadOnly: true,
      formatChecksStatus: true
    };
  },
  computed: {
    allCompleted() {
      const filter = this.taskList.filter((item) => item.status === 3);
      return !(filter.length > 0);
    }
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.scrollView(1);
          console.log('flowData watch isVisible', this.flowData);
          this.showFlag = val;
          this.taskList = this.flowData.slice(0);
          this.flowData.forEach((item, index) => {
            if (item.status === 3) {
              console.log('这里触发', item);
              this.foldFun(index);
            }
          });
        } else {
          this.detailIndex = -1;
          this.showFlag = false;
        }
      },
      immediate: true
    },
    flowData: {
      handler(val) {
        this.taskList = val;
        console.log('11更新任务列表状态', val);
        let lastRunIndex = 0;
        val.forEach((item, index) => {
          if (item.status === 3) {
            // this.detailIndex = index;
            lastRunIndex = index;
          }
        });
        console.log('lastRunIndex', val);
        this.detailIndex = this.initDetailIndex(val);
      },
      immediate: true,
      deep: true
    },
    updateDevelopFlag: {
      handler(val) {
        console.log('触发代码部署', val, this.taskList[4].status);
        const temp = this.taskList[4].status;
        if (this.detailIndex !== 4 && val !== 1) {
          // 如果没有打开代码部署详情，触发代码部署

          // 如果不是请求中，是从查看任务列表按钮进来的
          const statusArr = this.formData.map((item) => item.status);
          if (!statusArr.includes(3)) {
            return;
          }
          this.bushu();
          // this.detailIndex = 4;
        }
      },
      immediate: true
    },
    codeAnalysisData: {
      handler(val) {
        console.log('代码分析流', val);
        this.codeAnalysisProcess = val;
      },
      immediate: true
    },
    codeAnalysisProcessData: {
      handler(val) {
        console.log('代码分析过程数据', val);
        if (val) {
          this.foldFun(6);
        }
        this.codeAnalysisProcessFirst = val;
      },
      immediate: true
    },
    codeAnalysisDataStatus: {
      handler(val) {
        console.log('代码分析状态状变化', val);
        if (Number(val) === 1) {
          this.ploading = false;
          this.codeAnalysisProcessStatus = true;
        } else {
          this.codeAnalysisProcessStatus = false;
        }
        if (Number(val) === 2 || Number(val) === 3) {
          this.ploading = false;
          this.flodMap = [0, 0, 0, 0, 0, 0, 0];
          // this.foldFun(6); // 再合并上
        }
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成11');
          if (this.flodMap[0]) {
            // this.foldFun(0); // 合并
            // this.foldFun(1); // 合并
            this.flodMap = [0, 1, 0, 0, 0, 0, 0];
          }
        }
        if (Number(val) === 1) {
          this.flodMap = [1, 0, 0, 0, 0, 0, 0];
        }
      },
      immediate: true
    },
    runStreamStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成11');
          if (this.flodMap[1]) {
            // this.foldFun(0); // 合并
            // this.foldFun(1); // 合并
            this.flodMap = [0, 0, 1, 0, 0, 0, 0];
          }
        }
        if (Number(val) === 1) {
          this.flodMap = [0, 1, 0, 0, 0, 0, 0];
        }
      },
      immediate: true
    },
    runStreamList: {
      handler(val) {
        // if (val && this.taskList[1].status === 3) {
        //   this.flodMap = [0, 1, 0, 0, 0, 0, 0];
        // }
        if (Number(val) === 1) {
          this.flodMap = [0, 0, 0, 0, 0, 0, 0];
        }
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        console.log('思维树过程数据', val);
        if (val && this.flowData[0].status === 3) {
          // this.foldFun(0);
          this.flodMap = [1, 0, 0, 0, 0, 0, 0];
        }
        this.treeDataProcess = val;
        // console.log("this.treeDataProcess", this.treeDataProcess);
        
      },
      immediate: true
    },
    dataAlignDataStatus: {
      handler(val) {
        console.log('数据对齐状态变化', val, this.flowData[2].status);
        if ((Number(val) === 2 || Number(val) === 3) && this.flowData[2].status === 1) {
          if (this.flodMap[2]) {
            this.foldFun(2); // 合并
          }
        } else {
          if (Number(val) === 1 && this.flowData[2].status === 3) {
            console.log('数据对齐中', val);
            if (this.flodMap[1]) {
              this.foldFun(1); // 展开
            }
            this.foldFun(2); // 展开
          }
        }
      },
      immediate: true
    },
    processStatus: {
      handler(val) {
        console.log('代码生成过程日志', val);
        if (Number(val) === 1) {
          this.flodMap = [0, 0, 0, 1, 0, 0, 0];
          // this.foldFun(3); // 展开
        }
      },
      immediate: true
    },
    codeDataStatus: {
      handler(val) {
        if (Number(val) === 2 || Number(val) === 3) {
          if (this.flodMap[3]) {
            this.flodMap = [0, 0, 0, 0, 0, 0, 0];
            // this.foldFun(3); // 再合并上
          }
        }
      },
      immediate: true
    }
  },
  async mounted() {
    console.log('mounted flowData', this.flowData);
    this.detailIndex = await this.initDetailIndex(this.flowData);

  },
  methods: {
    formatChecks(nameRef) {
      if (!this.checksJson(nameRef)) {
        this.$message.warning('请确保为Jason格式');
      }
    },
    checksJson(nameRef) {
      try {
        const obj = JSON.parse(this.$refs[nameRef].showCodeData);
        // 如果到达这里，说明没有语法错误，且至少是一个有效的对象或数组

        // 如果需要更严格的验证（例如，字符串不能只是一个JSON片段，如数字或字符串），
        // 你可以在这里添加额外的检查
        // 例如，你可以检查obj是否是一个对象或数组
        if (obj && (typeof obj === 'object' || typeof obj === 'array')) {
          return true;
        } else {
          // 如果它不是对象或数组，则不是有效的JSON（按照某些标准）
          return false;
        }
      } catch (e) {
        // 如果解析失败，则不是有效的JSON
        return false;
      }
    },
    modelType(name) {
      if (name === 'model_param') {
        return 'model_param_extraction';
      } else if (name === 'model_info') {
        return 'modeling_info_structure';
      } else if (name === 'math_model') {
        return 'math_model_generate';
      } else {
        return 'decision_making_generate';
      }
    },
    updJsonMark(nameRef) {
      // if (nameRef === 'model_param') {
      //   if (!this.checksJson(nameRef)) {
      //     this.$message.warning('请确保为Jason格式');
      //     return false;
      //   }
      // }
      if (nameRef === 'decision_making_generate') {
        CodeEdit({
          scheme_id: this.$route.query.id,
          scheme_status: 'decision_ability',
          text: this.$refs[nameRef].showCodeData
        }).then((res) => {
          console.log(res, 'eeeeeeeeeeeeee');
          if (res.data.code === 200) {
            this.setModelData('codeData', this.$refs[nameRef].showCodeData);
            this.$message.success('保存成功');
          }
          this.editJsonMark(nameRef);
        });
      } else {
        updateTasksRes({
          scheme_id: this.$route.query.id,
          biz_type: this.modelType(nameRef),
          content: this.$refs[nameRef].showCodeData
        }).then((res) => {
          console.log(res, 'eeeeeeeeeeeeee');
          if (res.data.status === 'success') {
            if (nameRef === 'model_param') {
              this.setModelData('treeData', this.$refs[nameRef].showCodeData);
            } else if (nameRef === 'model_info') {
              console.log('model_infomodel_infomodel_info');
              this.setModelData('runStreamList', this.$refs[nameRef].showCodeData);
            } else {
              this.setModelData('dataAlignData', this.$refs[nameRef].showCodeData);
            }
            this.$message.success('保存成功');
          }
          this.editJsonMark(nameRef);
        });
      }
    },
    // 编辑mark方法
    editJsonMark(nameRef) {
      console.log(nameRef, 'nameRef');
      this.$nextTick(() => {
        if (nameRef === 'decision_making_generate') {
          this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly;
        } else {
          this.$refs[nameRef].isEdit = !this.$refs[nameRef].isEdit;
        }
      });
      this.isreadOnly = !this.isreadOnly;
    },
    // 初始化执行任务弹窗的展开详情值
    initDetailIndex(arr) {
      let detailIndex;
      const statusArr = arr.map((item) => item.status);
      if (statusArr.every((status) => status === 0)) {
        detailIndex = 0;
      } else if (statusArr.every((status) => status === 1)) {
        detailIndex = arr.length - 1;
      } else if (statusArr.includes(2)) {
        detailIndex = statusArr.lastIndexOf(2);
      } else if (statusArr.includes(3)) {
        detailIndex = statusArr.lastIndexOf(3);
      } else {
        detailIndex = statusArr.lastIndexOf(1);
      }
      return detailIndex;
    },
    iconMap(index, status) {
      const icons = [
        'siweishu1',
        'shujufenxi1',
        'shujuduiqi1',
        'daimashengcheng',
        'daimabushu',
        'daimaceshi',
        'daimafenxi'
      ];
      const statusColor = ['-default', '', '-error', ''];
      return icons[index] + statusColor[status];
    },
    handleClose(val) {
      this.devlopModalVisable = false;
      if (val) {
        this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'success'
        })
          .then(() => {
            this.devlopModalVisable = false;
            this.devPerson = val;
            this.devPersonInfo = val;
          })
          .catch(() => {
            this.devlopModalVisable = false;
            this.devPerson = val;
            this.devPersonInfo = val;
          });
      }
    },
    updateStep(scrollTop) {
      const allContents = document.querySelectorAll('.content-item');
      const content = document.querySelector('.task-left');
      const rectContent = [];

      console.log('content.innerHeight', content.scrollTop);
      allContents.forEach((ele) => {
        const eleRect = ele.getClientRects()[0];
        console.log('eleRect.top', eleRect.top, eleRect.height);
        if (
          (eleRect.top >= 0 && content.scrollTop - eleRect.top >= eleRect.height) ||
          (eleRect.top < 0 && content.scrollTop <= eleRect.height - Math.abs(eleRect.top)) ||
          eleRect.top >= 0
        ) {
          rectContent.push(ele);
        }
      });
      console.log('视窗rectContent', rectContent);
      let linkId;
      if (rectContent[0]) linkId = rectContent[0].id;
      // allLinks.forEach(link => link.classList.remove('active'))
      // const linkDom = document.querySelector(`a[href="#${linkId}"]`)
      // linkDom.classList.add('active')
    },
    scrollView(index) {
      const id = 'content-item' + (index + 1);
      const sectionEl = document.getElementById(id);
      console.log('000', sectionEl, id);
      // sectionEl.scrollIntoView({ behavior: "smooth" });
      sectionEl &&
        sectionEl.scrollIntoView({ block: 'start', inline: 'nearest', behavior: 'smooth' });
    },
    accessDevlop() {
      this.devPersonInfo = this.devPerson;
      this.curId = this.$route.query.id;
      this.devlopModalVisable = true;
    },
    onClose(val) {
      this.$emit('updateTaskModal', val);
    },
    // 最小化窗口
    miniModalShow() {
      this.$emit('updateTaskMiniModal');
    },
    // 确认关闭任务执行
    confirmCancel() {
      if (this.taskList[1].status === 3) {
        this.taskList[0].status = 1;
        this.taskList[1].status = 2;
        this.taskList[2].status = 0;
        this.taskList[3].status = 0;
        this.taskList[4].status = 0;
        this.taskList[5].status = 0;
        this.taskList[6].status = 0;
      }
      this.$emit('stopAbilityGen');
      // this.$emit('updateTaskModal');
    },
    stopGen(index) {
      this.$emit('stopAbilityGen', index);
    },
    // 重试
    reStart() {
      this.customStopScrollTobottom = false;
      this.$emit('handleReStart');
      reexecuteTask({ scheme_id: this.$route.query.id, task_name: 'model_param_extraction' });
      this.taskList[0].status = 3;
      this.taskList[1].status = 0;
      this.taskList[2].status = 0;
      this.taskList[3].status = 0;
      this.taskList[4].status = 0;
      this.taskList[5].status = 0;
      this.taskList[6].status = 0;
    },
    // 从点击的步骤开始执行，只针对已成功的步骤
    async redoTask(row, index) {
      if (this.taskList.filter((item) => item.status === 3).length) {
        this.$message({
          type: 'warning',
          message: '请等待其他任务执行后再重试'
        });
      } else {
        // model_param_extraction、modeling_info_structure、math_model_generate
        let indexId = '';
        if (index === 0) {
          indexId = 'model_param_extraction';
        }
        if (index === 1) {
          indexId = 'modeling_info_structure';
        }
        if (index === 2) {
          indexId = 'math_model_generate';
        }
        reexecuteTask({
          scheme_id: this.$route.query.id,
          task_name: indexId || row.runCode
        });
        this.detailIndex = index;
        if (row.runCode === 'model_param') {
          console.log('执行思维树生成');
          this.$emit('handleReStart');
          this.taskList[0].status = 3;
          this.taskList[1].status = 0;
          this.taskList[2].status = 0;
          this.taskList[3].status = 0;
          this.taskList[4].status = 0;
          this.taskList[5].status = 0;
          this.taskList[6].status = 0;
          this.$emit('updateFlowData', this.taskList);
        }
        // 除去测试不需要通知后端接口外，其他步骤需要通知后端存储
        if (row.runCode === 'model_info') {
          console.log('执行数据对齐分析');
          this.$emit('alignDataAns');
          this.taskList[0].status = 1;
          this.taskList[1].status = 3;
          this.taskList[2].status = 0;
          this.taskList[3].status = 0;
          this.taskList[4].status = 0;
          this.taskList[5].status = 0;
          this.taskList[6].status = 0;
          this.flodMap = [0, 1, 0, 0, 0, 0, 0];
          this.$emit('updateFlowData', this.taskList);
        }
        if (row.runCode === 'math_model') {
          console.log('执行多模态数据对齐');
          this.$emit('reAlignData');
          this.taskList[0].status = 1;
          this.taskList[1].status = 1;
          this.taskList[2].status = 3;
          this.taskList[3].status = 0;
          this.taskList[4].status = 0;
          this.taskList[5].status = 0;
          this.taskList[6].status = 0;
          this.$emit('updateFlowData', this.taskList, 'clear');
        }
        if (row.runCode === 'decision_making_generate') {
          console.log('执行代码生成');
          // 重新执行代码生成
          startAbilityGenerate({ ability_type: 'decision_ability', session_id: this.sessionId });
          this.taskList[0].status = 1;
          this.taskList[1].status = 1;
          this.taskList[2].status = 1;
          this.taskList[3].status = 3;
          this.taskList[4].status = 0;
          this.taskList[5].status = 0;
          this.taskList[6].status = 0;
          this.flodMap = [0, 0, 0, 1, 0, 0, 0];
          this.$emit('updateFlowData', this.taskList);
        }
        if (row.title === '代码部署') {
          console.log('执行代码部署');
          this.taskList[0].status = 1;
          this.taskList[1].status = 1;
          this.taskList[2].status = 1;
          this.taskList[3].status = 1;
          this.taskList[4].status = 3;
          this.taskList[5].status = 0;
          this.taskList[6].status = 0;
          this.flodMap = [0, 0, 0, 0, 1, 0, 0];
          this.$emit('updateFlowData', this.taskList);
          setTimeout(() => {
            this.updateDevelopFlag = Number(this.updateDevelopFlag) + 1;
          }, 500);
        }
        if (row.title === '代码测试') {
          console.log('执行代码测试');
          this.taskList[5].status = 3;
          this.taskList[6].status = 0;
          this.flodMap = [0, 0, 0, 0, 0, 1, 0];
          this.$emit('updateFlowData', this.taskList);
          await GetDecision({
            scheme_id: this.$route.query.id,
            scheme_status: 'decision_ability'
          })
            .then(async (codeRes) => {
              const codeData = codeRes.data.result?.decision_making_content || '';
              await AbilityTest({
                code_str: codeData,
                scheme_id: this.$route.query.id
              })
                .then((resTest) => {
                  console.log('测试结果', resTest);
                  if (resTest?.data?.code !== 200) {
                    this.handleTestOk(false);
                  } else {
                    this.handleTestOk(true);
                  }
                })
                .catch(() => {
                  this.taskList[5].status = 2;
                  this.taskList[6].status = 0;
                });
            })
            .catch(() => {
              this.taskList[5].status = 2;
              this.taskList[6].status = 0;
            });
          // setTimeout(() => {
          //   this.handleDevelopOk(true, '');
          // }, 500);
        }
        if (row.runCode === 'code_analysis') {
          console.log('代码参数解析生成');
          this.flodMap = [0, 0, 0, 0, 0, 0, 1];
          this.$emit('updateCodeAnalysis');
          // startAbilityGenerate({ability_type: 'decision_ability', session_id: this.sessionId})
          this.taskList[0].status = 1;
          this.taskList[1].status = 1;
          this.taskList[2].status = 1;
          this.taskList[3].status = 1;
          this.taskList[4].status = 1;
          this.taskList[5].status = 1;
          this.taskList[6].status = 3;
          this.$emit('updateFlowData', this.taskList);
        }
      }
    },
    // 代码部署完成通知
    async handleDevelopOk(flag, cdata) {
      console.log('代码部署结果', flag, this.taskList[5].status, this.detailIndex);
      const temps = this.taskList[5].status;
      if (flag) {
        if (temps !== 1) {
          this.flodMap = [0, 0, 0, 0, 0, 0, 1];
        }
        this.taskList[4].status = 1;
        if (temps === 0) {
          if (cdata) {
            this.taskList[5].status = 3;
            this.taskList[6].status = 0;
            await AbilityTest({
              code_str: cdata,
              scheme_id: this.$route.query.id
            }).then((resTest) => {
              console.log('测试结果33', resTest);
              if (resTest?.data?.code !== 200) {
                this.handleTestOk(false);
              } else {
                this.handleTestOk(true);
              }
            });
          }
          setTimeout(() => {
            this.updateTestFlag = Number(this.updateTestFlag) + 1;
          }, 500);
        } else {
          console.log('buxuyao');
        }
      } else {
        this.taskList[4].status = 3;
        this.taskList[5].status = 0;
        this.taskList[6].status = 0;
      }
      this.$emit('updateFlowData', this.taskList);
    },
    // 代码测试通知
    handleTestOk(flag) {
      if (this.taskList[4].status === 1) {
        console.log('测试成功表示', flag, this.taskList);
        const temp = this.taskList[6].status;
        if (temp !== 1) {
          this.flodMap = [0, 0, 0, 0, 0, 0, 1];
        }
        if (flag) {
          this.taskList[5].status = 1;
          if (temp === 0) {
            this.taskList[6].status = 3;
            // 触发代码解析
            this.$emit('updateCodeAnalysis');
          }
        } else {
          this.taskList[5].status = 2;
          this.taskList[6].status = 0;
        }
        this.$emit('updateFlowData', this.taskList);
      }
    },
    foldFun(index) {
      console.log(this.flodMap);

      // if (this.flodMap[runCode]) {
      //   this.flodMap[runCode] = 0;
      // } else {
      //   this.flodMap[runCode] = 1;
      // }
      const temp = [...this.flodMap];
      temp[index] = temp[index] ? 0 : 1;
      this.flodMap = temp;
      // this.taskList[index].foldFlag = !this.taskList[index].foldFlag
      // this.flagMap[index] = !this.flagMap[index]
    },
    showDetail(index) {
      this.customStopScrollTobottom = true;
      this.isreadOnly = true;
      if (this.detailIndex > -1) {
        if (this.detailIndex === index) {
          this.detailIndex = -1;
        } else {
          this.detailIndex = index;
        }
      } else {
        this.detailIndex = index;
      }
    },
    async bushu() {
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_ability'
      }).then(async (codeRes) => {
        const codeData = codeRes.data.result?.decision_making_content || '';
        // if (
        //   codeRes.data.result?.ext_info?.deploy_status &&
        //   codeRes.data.result?.ext_info?.deploy_status === 'deployed'
        // ) {
        //   this.handleDevelopOk(true, codeData);
        // } else {
        await AbilityPublish({
          code_str: codeData,
          scheme_id: this.$route.query.id
        })
          .then((res) => {
            if (res?.data?.code !== 200) {
              this.handleDevelopOk(false, '');
            } else {
              this.handleDevelopOk(true, codeData);
            }
          })
          .catch(() => {
            this.handleDevelopOk(false, '');
          });
        // }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.task-container {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  height: 100%;
  .task-left {
    flex: 1;
    // max-height: 100%;
    overflow-y: auto;
  }
  .task-list {
    padding: 16px 20px;
    .task-list-item {
      display: flex;
      flex-direction: row;
      align-items: stretch;
      justify-content: space-between;
      cursor: pointer;
      padding-bottom: 6px;
      .task-icon {
        width: 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .icon-svg {
          font-size: 32px;
          width: 32px;
          height: 32px;
        }
        img {
          width: 32px;
          height: 32px;
        }
        .task-line {
          flex: 1;
          min-height: 28px;
          width: 0px;
          border-left: 1px solid #dcdde0;
          height: 100%;
          // background: #DCDDE0;
          margin-bottom: 4px;
          margin-top: 4px;
          &.task-line-info {
            border-left: 1px dashed #dcdde0 !important;
          }
          &.task-line-error {
            border-left: 1px solid #ea4646 !important;
          }
        }
      }
      .task-text-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-right: 12px;
        margin-left: 12px;
        width: 80%;
      }
      .task-text-process {
        // flex: 1;
        background: #f6f7fb;
        border-radius: 2px;
        height: 0px;
        overflow: hidden;
        &.show {
          height: auto !important;
          padding: 16px;
        }
      }
      .task-text {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        margin-top: 6px;
        .task-text-title {
          font-weight: 400;
          flex: 1;
          font-size: 14px;
          color: #323233;
          line-height: 20px;
          flex: 1;
          margin-right: 8px;
          &:hover {
            color: #4068d4;
          }
        }
        .task-text-link {
          font-weight: 400;
          font-size: 14px;
          color: #4068d4;
          line-height: 20px;
          display: flex;
          align-items: center;
        }
      }
    }
    .loading-task {
      width: 32px;
      height: 32px;
      display: flex;
      border-radius: 50%;
      align-items: center;
      justify-content: center;
      background-color: #e7e9f8;
      color: #4068d4;
      i {
        font-size: 18px;
        animation: rotate 1s linear infinite;
      }
    }
  }
  .task-right {
    width: 0px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 16px 20px;
    &.showRight {
      width: 65%;
      // height: 600px;
      margin-left: 16px;
      border-left: 1px solid #dcdde0;
    }
    .header-title {
      padding: 12px 4px 12px 20px;
      font-weight: 500;
      font-size: 14px;
      color: #323233;
      line-height: 22px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .editorMark {
        display: flex;
        flex: 1;
        padding: 0 5px 0 12px;
        justify-content: space-between;
        align-items: center;
        div {
          display: flex;
        }
      }
    }
    .task-show {
      flex: 1;
      max-height: calc(100% - 44px);
      overflow-y: hidden;
      margin-left: 20px;
    }
  }
}
.last-dialog2 {
  border-radius: 8px;
  margin-top: 60px !important;
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-text {
      flex: 1;
      font-size: 16px;
      color: #323233;
      line-height: 24px;
      font-weight: bold;
    }
    .zuixiaohua {
      margin-right: 30px;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-top: 3px;
        cursor: pointer;
      }
    }
  }
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    height: calc(100vh - 240px) !important;
    max-height: calc(100vh - 240px) !important;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .el-button {
        line-height: 20px;
      }
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
.max_height{
  max-height: 500px;
  overflow: auto;
}
</style>
