<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="代码参数分析日志"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="50%"
      :show-close="loading ? false : true"
    >
      <div v-if="treeProcessData !== ''" id="treeProcessEl2" ref="treeProcess2" style="overflow-y: auto;">
        <MyEditor id="MyEditorTreeFirst" ref='MyEditorTreeFirst' :mdContent="treeProcessDataFirst"></MyEditor>
        <MyEditor id="MyEditorTree2" ref='MyEditorTree2' :mdContent="treeProcessData"></MyEditor>
        <!-- <vue-markdown v-highlight id="treeProcessMd" :source="treeProcessData" class="markdown-body"></vue-markdown> -->
      </div>
      <div v-else style="height: 400px;max-height: 400px;overflow-y: auto;">
        <el-empty :description="title ? '暂无' + title : '暂无分析日志'"></el-empty>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MyEditor from './mdEditor.vue';
export default {
  name: 'ModelDialog',
  components: {
    MyEditor
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    treeProcessVal: {
      type: String,
      default: null
    },
    treeProcessValFirst: {
      type: String,
      default: null
    },
    titleVal: String
  },
  data() {
    return {
      treeProcessData: '',
      treeProcessDataFirst: '',
      loading: false,
      showFlag: false,
      title: ''
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
          this.title = this.titleVal
          this.treeProcessData = this.treeProcessVal||''
          this.treeProcessDataFirst = this.treeProcessValFirst || ''
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        if (val) {
          this.treeProcessData = val
          this.loading = true;
          this.scrollFn();
        }
      },
      immediate: true
    },
    treeProcessValFirst: {
      handler(val) {
        if (val) {
          this.treeProcessDataFirst = val
          this.loading = true;
          this.scrollFn();
        }
      },
      immediate: true
    }
  },
  methods: {
    scrollFn(){
      const temp = document.getElementById('treeProcessEl2');
      // console.log('内Height', document.getElementById('treeProcessEl'), temp?.scrollTop, temp?.scrollHeight)
      if (temp) {
        temp.scrollIntoView({ block: 'end', inline: 'nearest' });
      }
    },
    onClose() {
      this.treeProcessData = '';
      this.treeProcessDataFirst = ''
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
