<template>
  <div class="knowledge-container">
    <div class="tabs-header">
      <div class="tabs-section">
        <el-tabs v-model="kType" class="knowledge-tabs">
          <el-tab-pane label="知识创作" name="1"></el-tab-pane>
          <el-tab-pane :disabled="isReadOnly" label="上传文件" name="2"></el-tab-pane>
        </el-tabs>
        <div class="tabs-border"></div>
      </div>
      <div class="action-section">
        <div class="action-buttons">
        <el-button
          v-if="kType === '1'"
          :loading="saveLoading"
          :disabled="isReadOnly"
          @click="save">保存</el-button>
        <el-button
          :loading="publishLoading"
          type="primary"
          :disabled="isReadOnly"
          @click="openPublishDialog">发布到知识库</el-button>
      </div>
      </div>
    </div>

    <div class="knowledge-content">
      <div v-if="kType === '1'" class="knowledge-edit">
        <div class="editor-section">
          <div class="editor-container">
              <cherry-editor
                ref="cherryEditor"
                v-model="detailContent"
                @change="handleUpdateContent"
                @focus="handleEditorFocus"
                @blur="handleEditorBlur"
              />

          </div>
        </div>
      </div>

      <div v-else class="upload-section">
        <div class="upload-wrapper">
          <el-upload
            class="upload"
            :file-list="fileList"
            :before-upload="beforeFileUpload"
            :on-change="fileUpload"
            :on-remove="fileUploadOnRemove"
            :on-success="fileUploadOnSuccess"
            :action="uploadUrl"
            :headers="uploadHeaders"
            drag
            multiple
            :limit="10"
            accept=".xlsx,.xls,.doc,.docx,.pdf,.ppt,.pptx,.txt,.cvs,.png,.jpg,.jpeg,.gif,.svg">
            <div class="upload-content">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="el-upload__tip">
                支持的文件格式包括：txt, text, pdf, xlsx, xls, docx, doc, html, htm, pptx, ppt<br/>
                单个文件最大不超过20MB，最多上传10个
              </div>
            </div>
          </el-upload>
        </div>
      </div>
    </div>


    <el-dialog title="发布到知识库" :visible.sync="publishDialogVisible">
      <div class="dialog-content">
        <div v-if="kType === '1'" class="input-item">
          <div class="label">知识主题:</div>
          <el-input
            v-model="thisKnowledgeTheme"
            placeholder="请输入知识主题"
            class="knowledge-theme-input"
            maxlength="20"
            show-word-limit>
          </el-input>
        </div>
        <div class="input-item">
          <div class="label">知识目录:</div>
          <el-select
            v-model="selectedDirectory"
            placeholder="请选择知识目录"
            class="directory-select-input">
            <el-option
              v-for="item in directoryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="publishDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPublish">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import CherryEditor from './cherryEditor.vue';
import { getObsUploadUrl, PlanTaskEdit, upload_knowledge_file, upload_knowledge_qa } from '@/api/planGenerateApi';
const chatXKey = {
  dev: '********************************',
  fat: 'uXnSpC7JDP6mNC6SFyAqNG1r45apCJPd',
  uat: 'BmopB4qgKgWEqvkWK2yyQhkUskg5vcjs',
  production: 'dZl9xUDmcJlRc9eLTm68P7R8qNWRzKM1'
}
export default {
  components: {
    CherryEditor,
  },
  props: {
   isReadOnly: {
   type: Boolean,
   default: false
 },
    schemeDetail: {
      type: String,
      default: ''
    },
    knowledgeTheme: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      kType: '1',
      detailContent: '',
      isEditorFocused: false,
      selectedDirectory: '',
      thisKnowledgeTheme: '',
      directoryOptions: [],
      saveLoading: false,
      publishLoading: false,
      uploadUrl: '',
      uploadHeaders: {},
      fileList: [],
      detailTitle: '',
      publishDialogVisible: false,
    }
  },
  mounted() {
    this.getKnowledgeList()
    if (this.schemeDetail) {
      this.detailContent = this.schemeDetail
    }
  },
  created() {
    if (!this.$route.query.id) {
      this.$router.push({
        path: '/planGenerate/index',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      });
    }
    this.thisKnowledgeTheme = this.knowledgeTheme;
    this.detailContent = this.schemeDetail;

    // 初始化上传配置
    this.uploadUrl = process.env.VUE_APP_LLM_API + '/upload/uploadFileToObs';
    this.uploadHeaders = {
      'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY_LLM,
    };
  },
  watch: {
    schemeDetail: {
      deep: true,
      handler (val) {
        this.detailContent = val
      }
    },
    knowledgeTheme: {
      deep: true,
      handler (val) {
        this.thisKnowledgeTheme = val
      }
    }
  },
  methods: {
    async getKnowledgeList() {
      try {
        const baseUrl = process.env.VUE_APP_LLM_API;
        const response = await axios.get(`${baseUrl}/knowledgeFolder/getKnowledgeList`, {
          headers: {
            'X-GW-AccessKey': chatXKey[process.env.VUE_APP_ENV]
          },
        });

        if (!response || !response.data) {
          throw new Error('接口返回数据格式错误');
        }

        if (response.data.success && response.data.code === '0') {
          this.directoryOptions = response.data.data || [];
          if (this.directoryOptions.length > 0) {
            this.selectedDirectory = this.directoryOptions[0].id;
          } else {
            console.log('知识目录为空');
          }
        } else {
          const errorMsg = response.data.message || '获取知识目录失败';
          console.error('获取知识目录失败:', errorMsg);
          this.$message.error(errorMsg);
        }
      } catch (error) {
        console.error('获取知识目录失败:', error);
        this.$message.error(error.message || '获取知识目录失败，请检查网络连接');
      }
    },
    handleDirectoryChange(value) {
      this.selectedDirectory = value;
      console.log('选择的目录:', value);
    },
    handleUpdateContent(content) {
      this.detailContent = content;
      this.$nextTick(() => {
        this.saveSchemeDetail();
      });
    },
    handleEditorFocus() {
      this.isEditorFocused = true;
    },
    handleEditorBlur() {
      this.isEditorFocused = false;
    },
    async save() {
      if (this.kType === '1') {
        // 确保获取到最新的编辑器内容
        this.detailContent = this.$refs.cherryEditor.editor.getValue();
      }

      this.saveLoading = true;
      try {
        await this.saveSchemeDetail();
        this.$message({
          message: '保存成功',
          type: 'success'
        });
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.saveLoading = false;
      }
    },

    async publish() {
      if (this.kType === '1') {
        // 确保获取到最新的编辑器内容
        this.detailContent = this.$refs.cherryEditor.editor.getValue();
      }

      this.publishLoading = true;
      try {
        switch (this.kType) {
          case '1':
            await this.qaKnowledgeBaseUpload();
            break;
          case '2':
            await this.fileKnowledgeBaseUpload();
            break;
        }

        this.$message({
          message: '发布成功',
          type: 'success'
        });
      } catch (error) {
        console.error('发布失败:', error);
        this.$message.error('发布失败');
      } finally {
        this.publishLoading = false;
      }
    },
    openPublishDialog() {
      this.publishDialogVisible = true;
    },
    async confirmPublish() {
      this.publishDialogVisible = false;
      await this.publish();
    },
    beforeFileUpload(file) {
      let stop = true;
      if (file.size > 20971520){
        stop = false;
        this.$message.warning(file.name + ': 超过20MB了')
      }
      return stop;
    },
    fileUpload(file) {
      // console.log(file)
      if (file?.response?.success) {

      }
    },
    fileUploadOnSuccess(response, file, fileList) {
      file.obsUrl = response?.data[0]?.url;
      this.fileList = fileList;
    },
    fileUploadOnRemove(file, fileList) {
      this.fileList = fileList;
    },
    // 保存方案明细
    saveSchemeDetail() {
      return PlanTaskEdit({
        scheme_id: this.$route.query.id,
        text: this.detailContent,
        file_url: ''
      });
    },
    qaKnowledgeBaseUpload() {
      this.publishLoading = true
      upload_knowledge_qa({
        question: this.thisKnowledgeTheme,
        answer: this.detailContent,
        baseId: this.selectedDirectory
      }).then((res) => {
        if (res.status === 200) {
          if (res.data?.success) {
            this.saveSchemeDetail();
            this.$emit('updateSchemeDetailName', this.thisKnowledgeTheme)
            this.$message({
              message: '发布成功！',
              type: 'success'
            });
          } else {
            this.$message({
              message: `发布失败：${res.data?.message}`,
              type: 'warning'
            });
          }
        } else {
          this.$message({
            message: '发布失败：请联系系统管理员',
            type: 'warning'
          });
        }
      }).finally(() => {
        this.publishLoading = false
      })
    },
    fileKnowledgeBaseUpload() {
      if (!this.selectedDirectory) {
        this.$message({
          message: '请选择知识目录',
          type: 'warning'
        });
        return;
      }

      if (this.fileList.length === 0) {
        this.$message({
          message: '请先上传文件',
          type: 'warning'
        });
        return;
      }

      const files = this.fileList.map(f => ({
        fileUrl: f.obsUrl,
        fileName: f.name
      }));

      this.publishLoading = true;
      upload_knowledge_file({
        files: files,
        baseId: this.selectedDirectory
      }).then((res) => {
        if (res.status === 200) {
          if (res.data?.success) {
            this.$message({
              message: '发布成功！',
              type: 'success'
            });
            this.fileList = []
          } else {
            this.$message({
              message: `发布失败：${res.data?.message}`,
              type: 'warning'
            });
          }
        } else {
          this.$message({
            message: '发布失败：请联系系统管理员',
            type: 'warning'
          });
        }
      }).finally(() => {
        this.publishLoading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.knowledge-container {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
}

.tabs-header {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 24px;
  background-color: #fff;
  z-index: 1;
  border-bottom: 1px solid #E4E7ED;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0;
    border: none;
  }

  :deep(.el-tabs__nav-wrap) {
    margin: 0;
    padding: 0;
    &::after {
      display: none;
    }
  }

  :deep(.el-tabs__nav) {
    height: 44px;
    border: none;
  }

  :deep(.el-tabs__item) {
    position: relative;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    padding: 0 20px;
    color: #606266;
    transition: all 0.25s ease;
    margin: 0 4px;

    &:first-child {
      margin-left: 0;
    }

    &:hover {
      color: #409EFF;
    }

    &.is-active {
      color: #409EFF;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #409EFF;
        transition: transform 0.25s ease;
      }
    }
  }

  :deep(.el-tabs__active-bar) {
    display: none;
  }
}

.tabs-section {
  flex: 1;
  display: flex;
  flex-direction: column;

  .knowledge-tabs {
    flex: 1;
  }
}

.action-section {
  display: flex;
  align-items: center;
  gap: 16px;

  .directory-select {
    flex-shrink: 0;

    .directory-select-input {
      width: 200px;
    }

    :deep(.el-input__wrapper) {
      height: 32px;
    }

    :deep(.dir-icon) {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      vertical-align: text-bottom;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    height: 44px;
    padding: 0;

    .el-button {
      margin-left: 12px;
      min-width: 68px;
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
      font-weight: 400;
      transition: all 0.25s ease;

      &:first-child {
        margin-left: 0;
      }

      &--default {
        &:hover, &:focus {
          border-color: #C0C4CC;
          color: #606266;
          background-color: #F2F6FC;
        }
      }

      &--primary {
        background-color: #2468F2;
        border-color: #2468F2;

        &:hover, &:focus {
          background-color: #4785F5;
          border-color: #4785F5;
        }

        &:active {
          background-color: #1B4FBE;
          border-color: #1B4FBE;
        }
      }
    }
  }
}

.knowledge-content {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.knowledge-edit {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.input-group {
  position: relative;
  flex-shrink: 0;
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.input-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.label {
  flex-shrink: 0;
  font-weight: bold;
  margin-bottom: 10px;
}

.editor-section {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.editor-container {
  position: relative;
  flex: 1;
  min-height: 0;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.editor-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.upload-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.upload-wrapper {
  width: 100%;
  height: 360px;
  display: flex;
  flex-direction: column;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.upload {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-upload) {
    height: 100%;
    width: 100%;
    display: flex;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }
}

.upload-content {
  text-align: center;

  :deep(.el-icon-upload) {
    font-size: 48px;
    margin-bottom: 16px;
  }

  :deep(.el-upload__text) {
    margin-bottom: 8px;
    font-size: 14px;
  }

  :deep(.el-upload__tip) {
    font-size: 12px;
    line-height: 1.5;
  }
}
</style>
