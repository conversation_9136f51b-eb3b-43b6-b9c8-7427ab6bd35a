<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      :title="id  ? '编辑' : '新建'"
      :visible.sync="visible"
      :before-close="onClose"
      width="40%"
    >
      <el-form
        ref="form"
        label-position="right"
        label-width="99px"
        :model="formData"
        :rules="rules"
      >
        <el-form-item label="名称:" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入"
            show-word-limit
            maxlength="50"
            style="width: 100%"
          />
        </el-form-item>
        <!-- <el-form-item label="智能小组:" prop="agent_id">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select
                v-model="formData.agent_id"
                placeholder="请选择"
                style="width: 100%"
              >
              <el-option
                        v-for="item in agentList"
                        :key="item.id"
                        :label="item.display.name"
                        :value="item.id"
                      />
              </el-select>
            </div>
          </div>
        </el-form-item> -->
        <el-form-item label="使用场景:" prop="agent_scene">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="display: flex;flex: 1">
              <el-select v-model="formData.agent_scene_code" filterable placeholder="请选择使用场景" style="padding:0 5px;" @change="sceneChange">
                <el-option
                  v-for="item in dictList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                ref="sceneSelect"
                v-model="formData.agent_scene"
                clearable
                placeholder="请选择使用场景"
                style="width: 100%"

              >
                <el-option
                  v-for="item in senceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item label="方案名称:" prop="scheme_detail_name">
          <el-input
            v-model="formData.scheme_detail_name"
            placeholder="请输入"
            show-word-limit
            maxlength="50"
            style="width: 100%"
          />
        </el-form-item> -->
        <el-form-item label="贡献专家:" prop="ownerId">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select
                ref="onwerSelect"
                v-model="formData.ownerId"
                style="width:100%"
                multiple
                filterable
                remote
                placeholder="请选择用户"
                :remote-method="searchUser"
                clearable
                @change="changeList"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="`${item.nickname}${item.loginName ? ('('+item.loginName+')') : ''}`"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="标签:" prop="tags">
          <div style="display: flex; flex-direction: row; align-items: center">
            <div style="flex: 1">
              <el-select
              ref="tagsSelect"
                v-model="formData.tag_ids"
                style="width:100%"
                multiple
                filterable
                remote
                allow-create
                placeholder="请选择标签"
                :remote-method="searchTags"
                clearable
                @change="changeTags"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="描述:" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            show-word-limit
            maxlength="30"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="createSubmit"
          >确定</el-button
        >
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {  AddScheme, UpdateScheme, SceneList, queryDictConfig, agentSenceList, queryTags, addTag, bindTag, queryUseTags } from '@/api/planGenerateApi.js'
export default {
  name: 'ModelDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: Number || String,
      default: null
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formData: {
        name: '',
        description: '',
        agent_scene_code:'',
        agent_scene:'',
        agent_id: '',
        ownerId:[],
        contributors:[],
        tag_ids: [],
        scheme_detail_name: ''
      },
      userList: [],
      agentList: [{id: '001',description: 'ddd'}],
      tagList: [],
      allTagList: [],
      sceneList: [],
      senceOptions:[],
      dictList:[],
      allTags: [],
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // scheme_detail_name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        agent_scene: [{ required: true, message: '请选择使用场景', trigger: 'blur' }],
      },
      loading: false,
      schemeId: '',
    };
  },
  watch: {
    visible: {
     async handler(val) {
        if (val) {
         await this.queryDataInfo();
         await this.queryDict();
         await this.queryAllTag();
        //  await this.searchTags2('');
          console.log('this.editData', this.editData);
          if (this.editData.name) {
            this.formData.agent_scene_code = this.editData.agent_scene_code
            this.formData.name = this.editData.name;
            this.formData.scheme_detail_name = this.editData.scheme_detail_name;
            this.formData.agent_id = this.editData.agent_id;
            await this.querySence2(this.formData.agent_scene_code);
            // this.formData.agent_scene_code = [this.editData.agent_scene_code, this.editData.agent_scene];
            this.formData.description = this.editData.description;
            this.formData.contributors = this.editData.contributors
            //this.formData.tag_ids = this.editData.tag?.map(item => item.id);
            const ownerIds =this.editData.contributors?.map(item=>item.id)
            // this.formData.agent_scene = this.editData.agent_scene
            if(this.editData.contributors?.length >0){
              this.$nextTick(()=>{
                this.editData.contributors.forEach((ele) => {
                this.$refs.onwerSelect?.cachedOptions.push({
                  currentLabel:ele.nickname+'('+ele.loginName+')', // 	当前绑定的数据的label
                  currentValue: ele.id, // 当前绑定数据的value
                  label: ele.nickname+'('+ele.loginName+')', // 当前绑定的数据的label
                  value:  ele.id // 当前绑定数据的value
                })
              });
                this.$set(this.formData, 'ownerId', ownerIds);
              })

            }
          }
        } else {
          this.formData.name = '';
          this.formData.scheme_detail_name = '';
          this.formData.agent_id = '';
          this.formData.agent_scene = [];
          this.formData.contributors = [];
          this.formData.ownerId = []
          this.formData.description = '';
          this.formData.tag_ids = [];
        }
      },
      immediate: true
    }
  },

  methods: {
    async mounted() {
      await this.searchTags('')
    },
    queryDict(){
      queryDictConfig({business_type: 'scene_type'}).then((res) => {
                if (res.status === 200 && res.data.code === 200) {
                  this.dictList = res.data.result?.config.map(item => {
                    return {
                      value: item.code,
                      label: item.name
                    }}
                  );

                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  });
                }
              });
    },
    querySence2(val,type){
      agentSenceList({name: '', scene_type:val }).then((res) => {
                if (res.status === 200 && res.data.code === 200) {
                  const result = res.data.result
                  if(result && result.length >0){
                    this.senceOptions = result.map(item => {
                      return {
                        value: item.id,
                        label: item.name,
                      }}
                    );
                    const filter = result.filter(item  => item.id === this.editData.agent_scene)
                    if (filter.length) {
                      this.formData.agent_scene = this.editData.agent_scene
                    } else {
                      this.formData.agent_scene = ''
                    }
                  } else {
                    this.senceOptions = [];
                    this.formData.agent_scene = ''
                  }
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  });
                }
              });
    },
    querySence(val,type){
      agentSenceList({name: '', scene_type:val }).then((res) => {
                if (res.status === 200 && res.data.code === 200) {
                  const result = res.data.result
                  if(result && result.length >0){
                    this.senceOptions = result.map(item => {
                      return {
                        value: item.id,
                        label: item.name,
                      }}
                    );
                    if(type === 'change'){
                      this.formData.agent_scene = this.senceOptions[0].value
                    }
                  }
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  });
                }
              });
    },
    sceneChange(val){
      this.querySence(val,'change')
    },
    changeList(val) {
      this.$nextTick(()=>{
        this.formData.contributors = this.$refs.onwerSelect.selected.map((item=>{
          return {
            id:item.currentValue,
            nickname:item.currentLabel.slice(0, item.currentLabel.indexOf('(')),
            loginName:item.currentLabel.match(/\((.+)\)/)[1]
          }
        }))
      })
    },
    async changeTags (val) {
      console.log('改变',val);
      const temp = [];
      val.forEach(async tagid => {
        const tagTotal = [...this.allTags, ...this.allTagList];
        const filters = tagTotal.filter(item => item.id === tagid)
        if (filters.length === 0) {
          console.log('标签长度', tagid.length);
          if (tagid.length && tagid.length <=15) {
            await addTag({
              name: tagid
            }).then(async (res) => {
              if (res.data) {
                console.log('添加成功', res.data);
                temp.push(res.data);
                await queryTags({
                  keyword: ''
                }).then(res => {
                  if(res.data){
                    const mergedArr = [...res.data, ...this.allTags];
                    const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse);
                    this.tagList = uniqueArr;
                    this.allTagList = uniqueArr;
                    this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = tagid
                    this.formData.tag_ids = temp;
                    console.log('this.formData.tag_ids', this.formData.tag_ids);
                    this.$nextTick(() => {
                      this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = tagid
                    })
                  } else {
                    this.tagList = [];
                  }
                })
              }
            })
          } else {
            this.$message({
              type: 'warning',
              message: '标签最长为15个字符，请修改!'
            });
          }

        } else {
          const mergedArr = [...this.allTags, ...this.allTagList];
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse);
          this.tagList = uniqueArr;
          this.allTagList = uniqueArr;
          const filters = tagTotal.filter(item => item.id === tagid)
          if (filters.length) {
            // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            temp.push(tagid);
            this.formData.tag_ids = temp;
            console.log('this.formData.tag_ids', this.formData.tag_ids);
            // this.$nextTick(() => {
            //   this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            // })
          } else {
            temp.push(tagid);
            this.formData.tag_ids = temp;
            console.log('this.formData.tag_ids', this.formData.tag_ids);
          }

        }
      })
    },
    searchUser(userName) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data
      })
    },
    async searchTags(keyword) {
      await queryTags({
        keyword
      }).then(res => {
        if (res.data) {
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
          }
        } else {
          this.tagList = [];
        }
      })
    },
    queryAllTag() {
      queryUseTags({ keyword: '' })
        .then((res) => {
          if (res.data) {
            console.log(res.data);
            this.allTags = res.data;
            this.$nextTick(() => {
              this.searchTags2();
            });
          }
        })
        .finally(() => {
        });
    },
    searchTags2() {
      queryTags({
        keyword: ''
      }).then(res => {
        if(res.data){
          const mergedArr = [...res.data, ...this.allTags];
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse);
          this.allTagList = uniqueArr;
          this.tagList = uniqueArr;
          const temp = [];
          console.log('回显', this.editData.tag, this.tagList);
          this.editData.tag?.forEach(titem => {
            const filter = uniqueArr.filter(item  => item.id === titem.id)
            if (filter.length) {
              temp.push(titem.id);
            }
          });
          this.formData.tag_ids = temp;
        } else {
          this.allTagList = [...this.allTags];
          this.tagList = [...this.allTags];
        }
      })
    },
    queryDataInfo() {
      this.tableLoading = true;
      // AgentList({type: 'group'}).then((res) => {
      //   console.log(res,'agent');
      //   if (res.status === 200 && res.data.code === 200) {
      //     this.agentList = res.data.result?.items || [];
      //   }
      // })
      SceneList().then((res) => {
          if (res.status === 200 && res.data) {
            this.sceneList = res.data.result || [];
          }
        });
    },
    onClose() {
      this.formData.name = '';
      this.formData.agent_scene = [];
      this.formData.description = 1;
      this.formData.contributors= [];
      this.formData.ownerId = []
      this.formData.agent_id = '';
      this.formData.tag_ids = [];
      this.$refs.form.clearValidate();
      this.$emit('close');
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          this.loading = true;
          const param = {
            name: this.formData.name,
            scheme_detail_name:  this.formData.name || this.formData.scheme_detail_name,
            description: this.formData.description,
            agent_scene: this.formData.agent_scene,
            agent_scene_code: this.formData.agent_scene_code,
            agent_id: this.formData.agent_id,
            contributors:this.formData.contributors,
            tag_ids: this.formData.tag_ids
          }
          if (this.id) {
            param.id = this.id;
            UpdateScheme(param).then(async (res) => {
              this.loading = false;
              console.log('编辑结果',res);
              if (res.status === 200 && res.data.code*1 === 200) {
                await bindTag({
                  tag_ids:this.formData.tag_ids, biz_id:res.data.result.id
                }).then((ress) => {
                  console.log('绑定成功', ress.data);
                })
                this.$message({
                  type: 'success',
                  message: '编辑成功!'
                });
                this.onClose();
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            }).finally(() => {
              this.loading = false;
            });
          } else {
            AddScheme(param).then(async (res) => {
              this.loading = false;
              console.log('创建结果',res);
              if (res.status === 200 && res.data.code*1 === 200) {
                await bindTag({
                  tag_ids:this.formData.tag_ids, biz_id:res.data.result.id
                }).then((ress) => {
                  console.log('绑定成功', ress.data);
                })
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                });
                this.onClose();
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            }).finally(() => {
              this.loading = false;
            });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
