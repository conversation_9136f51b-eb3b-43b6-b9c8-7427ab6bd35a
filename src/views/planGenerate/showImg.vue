<template>
    <div class="img_s">
    <img class="wheelImg" @wheel="handleScroll" @mousedown="handleMouseDown"
        :src="src" alt=""
        :style="imgStyle">
    </div>
</template>
<script>
  const Mode = {
    CONTAIN: {
      name: 'contain',
      icon: 'el-icon-full-screen'
    },
    ORIGINAL: {
      name: 'original',
      icon: 'el-icon-c-scale-to-original'
    }
  };
  const isFirefox = function () {
    return !Vue.prototype.$isServer && !!window.navigator.userAgent.match(/firefox/i);
  };
  const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';
  const isServer = Vue.prototype.$isServer;
  export const on = (function () {
    if (!isServer && document.addEventListener) {
      return function (element, event, handler) {
        if (element && event && handler) {
          element.addEventListener(event, handler, false);
        }
      };
    } else {
      return function (element, event, handler) {
        if (element && event && handler) {
          element.attachEvent('on' + event, handler);
        }
      };
    }
  })();
  
  /* istanbul ignore next */
  export const off = (function () {
    if (!isServer && document.removeEventListener) {
      return function (element, event, handler) {
        if (element && event) {
          element.removeEventListener(event, handler, false);
        }
      };
    } else {
      return function (element, event, handler) {
        if (element && event) {
          element.detachEvent('on' + event, handler);
        }
      };
    }
  })();
  export default {
    props:{
        src:{
            type: String
        }
    },
    data() {
      return {
        transform: {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false
        }
      }
    },
    computed: {
      imgStyle() {
        const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
        const style = {
          transform: `scale(${scale}) rotate(${deg}deg)`,
          transition: enableTransition ? 'transform .3s' : '',
          'margin-left': `${offsetX}px`,
          'margin-top': `${offsetY}px`
        };
        if (this.mode === Mode.CONTAIN) {
          style.maxWidth = style.maxHeight = '100%';
        }
        return style;
      },
    },
    methods: {
      rafThrottle(fn) {
        let locked = false;
        return function (...args) {
          if (locked) return;
          locked = true;
          window.requestAnimationFrame(_ => {
            fn.apply(this, args);
            locked = false;
          });
        };
      },
      handleScroll() {
        this._mouseWheelHandler = this.rafThrottle(e => {
          if (e.target._prevClass == 'wheelImg') {
            const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
            if (delta > 0) {
              this.handleActions('zoomIn', {
                zoomRate: 0.015,
                enableTransition: false
              });
            } else {
              this.handleActions('zoomOut', {
                zoomRate: 0.015,
                enableTransition: false
              });
            }
          }
  
        });
        on(document, 'keydown', this._keyDownHandler);
        on(document, mousewheelEventName, this._mouseWheelHandler);
      },
      handleActions(action, options = {}) {
        if (this.loading) return;
        const { zoomRate, rotateDeg, enableTransition } = {
          zoomRate: 0.2,
          rotateDeg: 90,
          enableTransition: true,
          ...options
        };
        const { transform } = this;
        switch (action) {
          case 'zoomOut':
            if (transform.scale > 0.2) {
              transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));
            }
            break;
          case 'zoomIn':
            transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
            break;
          case 'clocelise':
            transform.deg += rotateDeg;
            break;
          case 'anticlocelise':
            transform.deg -= rotateDeg;
            break;
        }
        transform.enableTransition = enableTransition;
      },
      handleMouseDown(e) {
        if (this.loading || e.button !== 0) return;
  
        const { offsetX, offsetY } = this.transform;
        const startX = e.pageX;
        const startY = e.pageY;
        this._dragHandler = this.rafThrottle(ev => {
          this.transform.offsetX = offsetX + ev.pageX - startX;
          if (this.transform.offsetX >= 785) {
            this.transform.offsetX = 785
          }
          this.transform.offsetY = offsetY + ev.pageY - startY;
        });
        on(document, 'mousemove', this._dragHandler);
        on(document, 'mouseup', ev => {
          off(document, 'mousemove', this._dragHandler);
        });
  
        e.preventDefault();
      }
    },
  }
  </script>
  
  <style lang="postcss" scoped>
  
  .img_s {
    position: relative;
    overflow: hidden;
    /* 防止图片超出边界 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* height: calc(100vh - 602px); */
    width: 100%;
  
    img {
      position: absolute;
      transform: translate(-50%, -50%);
      max-width: 100%;
      max-height: calc(100% - 40px);
      transition: transform 0.3s ease-in-out;
      width: auto;
      /* height: calc(100vh - 629px); */
      /* object-fit: scale-down; */
      border-radius: 4px;
      /* border: 1px solid #EBECF0; */
      overflow: hidden;
      position: relative;
    }
  }
  </style>
