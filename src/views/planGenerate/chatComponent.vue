<template>
    <div class="chat-container">
      <!-- 聊天消息显示区域 -->
      <div class="chat-messages">
        <div v-for="(message, index) in chatMessages" :key="index" class="chat-message">
          <div class="message-text">{{ message.text }}</div>
          <div class="message-time">{{ message.time }}</div>
        </div>
      </div>
      <!-- 用户输入区域 -->
      <div class="chat-input">
        <input
          v-model="newMessage"
          type="text"
          placeholder="输入消息111..."
          @keyup.enter="sendMessage"
        />
        <button @click="sendMessage">发送</button>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      chatMessages: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        newMessage: '' // 用户输入的新消息
      };
    },
    methods: {
      sendMessage() {
        if (this.newMessage.trim() === '') return; // 如果消息为空，则不发送
        const message = {
          text: this.newMessage,
          time: new Date().toLocaleTimeString() // 获取当前时间
        };
        this.$emit('send-message', message); // 触发发送消息事件
        this.newMessage = ''; // 清空输入框
      }
    }
  }
  </script>
  
  <style scoped>
  .chat-container {
    width: 100%;
    height: 100%;
    overflow-y: auto; /* 支持滚动 */
    padding-right: 16px; /* 右侧内边距 */
    display: flex;
    flex-direction: column;
  }
  
  /* 聊天消息显示区域 */
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 16px;
  }
  
  /* 聊天消息样式 */
  .chat-message {
    margin-bottom: 16px;
  }
  
  .message-text {
    font-size: 14px;
    color: #333;
  }
  
  .message-time {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
  
  /* 用户输入区域 */
  .chat-input {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-top: 1px solid #eee;
  }
  
  .chat-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
  }
  
  .chat-input button {
    padding: 8px 16px;
    background: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .chat-input button:hover {
    background: #0056b3;
  }
  </style>