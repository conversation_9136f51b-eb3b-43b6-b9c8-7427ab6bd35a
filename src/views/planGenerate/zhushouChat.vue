<template>
  <div id="chatContainer" v-loading="taskGenLoading" element-loading-text="任务生成中..."
    element-loading-spinner="el-icon-loading" class="chatContainer" :class="{
      'chatContainerFrame': $store.state.planGenerate.isIframeHide
  , 'EmbedModeFrame': isEmbedMode}">
    <div class="headerBox">
      <!-- <div v-if="schemeInfo.agent_scene_code !== 'other_assistant_scene'" class="headerStep"
        :style="{ width: displayType === 2 ? '400px' : '100%' }">
        <el-steps :active="activeStep" finish-status="success" simple class="myStep">
          <el-step title="方案明细">
            <img slot="icon" :src="
                activeStep !== 0
                  ? require('@/assets/images/icon/1_icon.png')
                  : require('@/assets/images/icon/1_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step :title="
              [
                'device_ops_assistant_scene',
                'device_ops_assistant_scene-v1',
                'artificial_handle_scene',
                'visit_leader_cognition_scene',
                'rule_generation_scene',
                'intelligent_conversation_scene',
                'sop_scene'
              ].indexOf(schemeInfo.agent_scene_code) > -1
                ? '思维树'
                : schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
                ? '思维图'
                : '任务列表'
            ">
            <img slot="icon" :src="
                activeStep !== 1
                  ? require('@/assets/images/icon/2_icon.png')
                  : require('@/assets/images/icon/2_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step v-if="
              [
                'device_ops_assistant_scene',
                'custom_cognition_assistant_scene',
                'device_ops_assistant_scene-v1',
                'artificial_handle_scene',
                'visit_leader_cognition_scene',
                'rule_generation_scene',
                'intelligent_conversation_scene',
                'sop_scene'
              ].indexOf(schemeInfo.agent_scene_code) > -1 && displayType === 1
            " title="多模态数据对齐">
            <img slot="icon" :src="
                activeStep !== 2
                  ? require('@/assets/images/icon/3_icon.png')
                  : require('@/assets/images/icon/3_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step v-if="
              [
                'device_ops_assistant_scene',
                'custom_cognition_assistant_scene',
                'device_ops_assistant_scene-v1',
                'artificial_handle_scene',
                'visit_leader_cognition_scene',
                'intelligent_conversation_scene',
                'sop_scene'
              ].indexOf(schemeInfo.agent_scene_code) > -1 && displayType === 1
            " title="能力代码生成">
            <img slot="icon" :src="
                activeStep !== 3
                  ? require('@/assets/images/icon/4_icon.png')
                  : require('@/assets/images/icon/4_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step v-if="schemeInfo.agent_scene_code === 'rule_generation_scene' && displayType === 1" title="规则生成">
            <img slot="icon" :src="
                activeStep !== 3
                  ? require('@/assets/images/icon/4_icon.png')
                  : require('@/assets/images/icon/4_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step v-if="
              [
                'device_ops_assistant_scene',
                'custom_cognition_assistant_scene',
                'device_ops_assistant_scene-v1',
                'artificial_handle_scene',
                'visit_leader_cognition_scene',
                'rule_generation_scene',
                'intelligent_conversation_scene',
                'sop_scene'
              ].indexOf(schemeInfo.agent_scene_code) > -1 && displayType === 1
            " title="智能能力测试与迭代">
            <img slot="icon" :src="
                activeStep !== 4
                  ? require('@/assets/images/icon/5_icon.png')
                  : require('@/assets/images/icon/5_icon_success.png')
              " class="empty-space" />
          </el-step>
        </el-steps>
      </div> -->
    </div>
    <div v-if="activeStep * 1 === 0" :class="
        schemeInfo.agent_scene_code === 'other_assistant_scene'
          ? 'containerBox containerBoxBig'
          : 'containerBox'
      ">
      <div v-show="phoneFlag" id="left-phone" class="phoneContent">
        <el-tooltip class="item" effect="dark" :content="
            currentRoleInfo?.name
              ? `${currentRoleInfo?.name}（${currentRoleInfo.alias}）`
              : '专家工作台'
          " placement="top">
          <div v-if="currentRoleInfo?.name" class="phoneTitle">
            {{ currentRoleInfo?.name }}（{{ currentRoleInfo.alias }}）
          </div>
          <div v-else class="phoneTitle">专家工作台</div>
        </el-tooltip>
        <div class="phonePhoto">
          <div v-show="
              ([
                'process stream',
                'process stream running',
                'process running',
                'processing',
                'scheme generating',
                'clear_history',
                'process_stream_message'
              ].indexOf(systemMessages) > -1 &&
                !historyChat.messages[historyChat.messages.length - 1].id) ||
hasChatingName !== '' || isOccupied ||
              qaBoxLoading
            " class="bubble">
            <img src="@/assets/images/planGenerater/voice.gif" />
          </div>
          <div class="phonePh">
            <img v-if="currentRoleInfo?.icon" :src="currentRoleInfo.icon" />
            <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
          </div>
        </div>
        <div class="phoneStatus">
          <div v-if="phoneStatus === 'start'" @click="startWeb">
            <img src="@/assets/images/planGenerater/call.png" width="40px" />
          </div>
          <div v-if="phoneStatus === 'running'" @click="
              () => {
                stopWeb()
                phoneStatus = 'start'
              }
            ">
            <img src="@/assets/images/planGenerater/voice.gif" width="60px" />
            <div>正在听...</div>
          </div>
          <div v-if="phoneStatus === 'shibie'">
            <img src="@/assets/images/planGenerater/loading.gif" width="60px" />
            <div>语音识别中...</div>
          </div>
        </div>
        <div class="phoneClose">
          <div :class="
              !(
                ([
                  'process stream',
                  'process stream running',
                  'process running',
                  'processing',
                  'scheme generating',
                  'clear_history',
                  'process_stream_message'
                ].indexOf(systemMessages) > -1 &&
                  !historyChat.messages[historyChat.messages.length - 1].id) ||
  hasChatingName !== '' || isOccupied ||
                shibieLoading ||
                qaBoxLoading
              )
                ? 'phoneStop phoneStopDisabled'
                : 'phoneStop'
            " @click="stopThink">
            <img src="@/assets/images/planGenerater/stop.png" />
            <!-- <el-button type="danger" icon="el-icon-video-pause" :disabled="!(   )" circle ></el-button> -->
          </div>
          <div class="phoneClone" @click="closePhone">
            <img src="@/assets/images/planGenerater/close.png" />
          </div>
        </div>
      </div>
      <div v-if="planDetailShow" v-loading="kickLoading" element-loading-spinner="el-icon-loading" id="left-content"
        :style="{
          width: leftWidth,
          marginRight: planDetailShow && !phoneFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: thinkFullFlag ? '' : 'relative',
          marginLeft: phoneFlag ? '0px' : '0px',
          height: (rightFullFlag && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
          maxHeight: (rightFullFlag && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
          top: (rightFullFlag && !isHideIframe) ?  '48px':  '0px',
          left: !rightFullFlag ? '0px!important' : isHideIframe ? '0px' : '64px!important',
          maxWidth: !rightFullFlag ? '100%!important' : isHideIframe ? 'calc( 100% - 0px )!important' : 'calc( 100% - 64px )!important',
        }" :class="
          rightFullFlag ? 'containerCard customMd containerCardFull' : 'customMd containerCard'
        ">
        <div class="cardContent">
          <div v-show="!phoneFlag" class="chatHeader">
            <div class="rightTitle"> <img :src="require('@/assets/images/ai.png')" /></div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" content="语音通话" placement="top">
                <div :class="
                    ([
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
  hasChatingName !== '' || isOccupied ||
                    shibieLoading ||
                    qaBoxLoading
                      ? 'rightBtn rightBtnDisabled'
                      : 'rightBtn'
                  " @click="realTime">
                  <img :class="{ 'cust-disabled': isReadOnly }" src="@/assets/images/planGenerater/phone.png" style="width: 25px" />
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="rightFullFlag ? '退出全屏' : '全屏'" placement="top">
                <div :class="rightFullFlag ? 'rightBtn rightBtnBlue' : 'rightBtn'" @click="changeShowFull">
                  <img v-if="rightFullFlag" src="@/assets/images/planGenerater/tuichuquanping.png" /><img v-else
                    src="@/assets/images/planGenerater/full.png" />
                </div>
              </el-tooltip>
            </div>
          </div>
          <div ref="chatBox" class="chatScroll" :style="{ userSelect: isDragging ? 'none' : 'auto' }">
            <div v-for="(item, index) in historyChat.messages" :key="item.id">
              <div v-if="item.content" class="gptAnsWrap">
                <div v-if="item.author?.role !== 'user_proxy'" class="gptAvator" style="margin-right: 6px">
                  <img v-if="item.agent_role_id && agentAvatorInfoMap[item.agent_role_id]?.icon"
                    :src="agentAvatorInfoMap[item.agent_role_id]?.icon" />
                  <img v-else-if="agentAvatorInfo.icon" :src="agentAvatorInfo.icon" />
                  <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
                </div>
                <div style="flex: 1">
                  <div :class="
                      item.author?.role === 'user_proxy' ? 'chat-time chat-time-user' : 'chat-time'
                    ">
                    <div v-if="item.author?.role !== 'user_proxy'" class="user">
                      <span class="time">{{ getHours(item.create_time) }} </span>
                      <span v-if="
                          item.author?.role !== 'user_proxy' &&
                          item.agent_role_id &&
                          agentAvatorInfoMap[item.agent_role_id]?.alias
                        " class="agent-mark">{{ agentAvatorInfoMap[item.agent_role_id]?.alias }}</span>
                      <span v-else-if="item.author?.role !== 'user_proxy' && agentAvatorInfo.alias"
                        class="agent-mark">{{ agentAvatorInfo.alias }}</span>
                      <span v-if="item.agent_role_id && agentAvatorInfoMap[item.agent_role_id]?.name">{{
                        agentAvatorInfoMap[item.agent_role_id].name }}</span>
                      <span v-else-if="agentAvatorInfo.name">{{ agentAvatorInfo.name }}</span>
                      <span v-else>
                        <template v-if="item.content.nickName">{{ item.content.nickName }}
                          {{
                          item.content.username ? '（' + item.content.username + '）' : ''
                          }}</template>
                        <template v-else>智能能力研发</template>
                      </span>
                    </div>
                    <div v-else class="user">
                      <span class="time">{{ getHours(item.create_time) }} </span>
                      {{ item.content.nickName }}
                      {{ item.content.username ? '（' + item.content.username + '）' : '' }}
                    </div>
                  </div>
                  <div :class="
                      item.author?.role === 'user_proxy' ? 'gptAnsBox gptUserBox' : 'gptAnsBox'
                    ">
                    <div :style="{
                        maxWidth: 'calc(100% - 80px)',
                        flex: item.author?.role === 'user_proxy' ? 'none' : 1
                      }">
                      <div :class="item.author?.role === 'user_proxy' ? 'gptAns gptUser' : 'gptAns'">
                        <pre v-if="
                            !item.id &&
                            systemMessages !== 'process waiting' &&
                            systemMessages !== 'scheme generating' &&
                            (item.content?.chat_message.content.indexOf('graph') > -1 ||
                              (item.content?.chat_message.content.indexOf('flowchart') > -1 &&
                                item.content?.chat_message.content.indexOf('mermaid') > -1))
                          ">{{ item.content?.chat_message.content }}</pre>
                        <MyEditorPreview v-else :id="item.id + index + ''" :ref="item.id + index"
                          :chat-message="item.content"></MyEditorPreview>
                        <!-- <pre v-if="(item.content?.parts.indexOf('graph') > -1 || item.content?.parts.indexOf('flowchart') > -1) && item.content?.parts.indexOf('mermaid')<0"><div class="language-mermaid">{{item.content?.parts}}</div></pre>
                            <vue-markdown v-highlight v-else :source="item.content?.parts" class="markdown-body markdown-gitbook"></vue-markdown> -->
                      </div>
                    </div>
                    <!-- <div v-if="item.author?.role === 'user_proxy'" class="gptAvator">
                            <img src="@/assets/images/planGenerater/user-chat-icon.png" style="margin-left: 3px"/>
                        </div> -->
                  </div>
                  <div v-show="item.author?.role !== 'user_proxy' && !item.auto" class="think-wrap">
                    <div v-if="
                        !item.id &&
                        systemMessages !== 'process waiting' &&
                        systemMessages !== 'scheme generating'
                      " v-show="!phoneFlag" class="think-btn" @click="stopThink">
                      终止生成
                    </div>
                    <!-- <div v-if="!item.id && index === (historyChat.messages.length - 1)" class="think-btn">重新思考</div> -->
                    <div :class="{ 'cust-disabled': isReadOnly }" class="think-btn" @click="changeThinkWrap(item.sub_content, item.id)">
                      生成过程
                    </div>
                    <div v-show="!phoneFlag" :class="
                        insertWriteFlag ||
                        systemMessages === 'process running' ||
                        systemMessages === 'process stream' ||
                        systemMessages === 'process_stream_message'
                          ? 'think-btn think-btn-disabled'
                          : 'think-btn'
                      " @click="handleDetailOpt('insert', item.content?.chat_message.content)">
                      插入
                    </div>
                    <div v-show="!phoneFlag" :class="
                        replaceWriteFlag ||
                        systemMessages === 'process running' ||
                        systemMessages === 'process stream' ||
                        systemMessages === 'process_stream_message'
                          ? 'think-btn think-btn-disabled'
                          : 'think-btn'
                      " @click="handleDetailOpt('replace', item.content?.chat_message.content)">
                      替换
                    </div>
                    <div v-show="!phoneFlag"
                      :class="isOccupied || hasChatingName || isReadOnly ? 'think-btn think-btn-disabled' : 'think-btn'"
                      @click="handleSave(item.content?.chat_message.content)">
                      采纳
                    </div>
                  </div>
                </div>
                <div v-if="item.author?.role === 'user_proxy'" class="gptAvator" style="margin-left: 6px">
                  <div class="userAvator">{{ item.content.nickName?.slice(-2) }}</div>
                  <!-- <img src="@/assets/images/planGenerater/user-chat-icon.png" style="margin-left: 3px"/> -->
                </div>
              </div>
            </div>
            <div v-if="
                systemMessages !== 'process running' &&
                systemMessages !== 'process stream' &&
                systemMessages !== 'process_stream_message' &&
                systemMessages.indexOf('received feedback') < 0 &&
                systemMessages !== 'scheme generating' &&
                systemMessages !== 'process stream running' &&
                systemMessages !== 'clear_history' &&
                qaList.length &&
                systemMessages !== 'clear history completed'
              " class="qaBox">
              <div class="qaflex">
                <div v-for="item in qaList" class="qaitem" @click="handleAsk(item)">{{ item }}</div>
              </div>
            </div>
            <div v-if="
                qaBoxLoading &&
                systemMessages !== 'clear_history' &&
                systemMessages !== 'clear history completed'
              " class="qa-loading-spinner"></div>
            <!-- v-if="systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' || systemMessages.indexOf('received feedback') > -1" -->
            <div v-if="ansBoxLoading" class="qa-loading-spinner3"></div>
            <div v-if="
                systemMessages === 'process running' ||
                systemMessages === 'process stream' ||
                systemMessages === 'process_stream_message' ||
                systemMessages.indexOf('received feedback') > -1
              " class="gptAnsWrap">
              <div class="gptAvator">
                <img v-if="currentRoleInfo.icon" :src="currentRoleInfo.icon" />
                <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
              </div>
              <div class="gptAnsBox" style="flex-direction: column">
                <div class="chat-time">
                  <div class="user">
                    <span class="time">{{ getHours(new Date()) }} </span>
                    <span v-if="currentRoleInfo.alias" class="agent-mark">{{
                      currentRoleInfo.alias
                      }}</span>
                    <span v-if="currentRoleInfo.name">{{ currentRoleInfo.name }}</span>
                    <span v-else>
                      <template>智能能力研发</template>
                    </span>
                  </div>
                </div>
                <div class="gptAns2">
                  <div class="qa-loading-spinner2"></div>
                </div>
                <div class="think-wrap">
                  <div v-show="!phoneFlag" class="think-btn" @click="stopThink">终止生成</div>
                  <div class="think-btn" @click="changeThinkWrap('', 'running')">生成过程</div>
                </div>
              </div>
            </div>
            <div v-if="systemMessages === 'scheme generating'" class="gptAnsWrap">
              <div class="gptAvator">
                <img v-if="currentRoleInfo.icon" :src="currentRoleInfo.icon" />
                <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
              </div>
              <div class="gptAnsBox" style="flex-direction: column">
                <div class="chat-time">
                  <div class="user">
                    <span class="time">{{ getHours(new Date()) }} </span>
                    <span v-if="currentRoleInfo.alias" class="agent-mark">{{
                      currentRoleInfo.alias
                      }}</span>
                    <span v-if="currentRoleInfo.name">{{ currentRoleInfo.name }}</span>
                    <span v-else>
                      <template>智能能力研发</template>
                    </span>
                  </div>
                </div>
                <div class="gptAns2">
                  <div class="qa-loading-spinner2"></div>
                </div>
              </div>
            </div>
            <el-alert v-if="taskStatusText === 'scheme generating error'" :closable="false" title="方案生成失败 "
              type="error"></el-alert>
            <el-alert v-if="systemMessages === 'process error'" :closable="false" title="会话失败！ "
              type="error"></el-alert>
            <el-alert v-if="agentError" :closable="false" type="error">
              <span slot="title">人机对话连接失败请刷新后再进行对话 &nbsp; &nbsp;<el-link type="error" text
                  :style="{ verticalAlign: 'baseline' }" @click="refresh">刷新</el-link></span>
            </el-alert>
            <KickFeat class="kickfeat-component" v-if="hasChatingName || isOccupied" :isOccupied="isOccupied"
              :kickLoading="kickLoading" :hasChatingName="hasChatingName" @e-update:kickedloading="handleUpdateKickedLoading"
              @e-update:kicked="startPolling" />
          </div>
          <div :style="{ height: thinkFlag ? '225px' : '0px', borderWidth: thinkFlag ? '1px' : '0px' }" :class="
              thinkFullFlag
                ? rightFullFlag
                  ? 'thinkContent thinkContentFullFull'
                  : globalNavigatorStatus
                  ? 'thinkContent thinkContentFull'
                  : 'thinkContent thinkContentFullSmall'
                : 'thinkContent'
            ">
            <div class="thinkHeader">
              <div class="title"><img src="@/assets/images/planGenerater/think.png" />生成过程</div>
              <div class="thinkOpt">
                <el-tooltip class="item" effect="dark" :content="thinkFullFlag ? '退出全屏' : '全屏'" placement="top">
                  <div :class="thinkFullFlag ? 'think-btn think-btn-blue' : 'think-btn'" @click="changeThinkFull">
                    <img v-if="thinkFullFlag" src="@/assets/images/planGenerater/tuichuquanping.png" /><img v-else
                      src="@/assets/images/planGenerater/full.png" />
                  </div>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="关闭" placement="top">
                  <div class="think-btn" @click="closechangeThinkWrap">
                    <i class="el-icon-close" style="color: #4068d4"></i>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="thinkWrap">
              <div class="thinkItem">
                <div class="itemContent">
                  <MyEditorPreview id="MyFanganEditorSikao" ref="MyFanganEditorSikao" :md-content="processContent.text">
                  </MyEditorPreview>
                  <!-- <vue-markdown v-highlight :source="processContent.text"  class="markdown-body chat-markdown"></vue-markdown> -->
                </div>
              </div>
              <!-- <div class="thinkItem">
                  <div><i class="el-icon-success"></i></div>
                  <div class="itemContent">开始一个新目标【玻璃窑炉方向有哪些机理知识】</div>
                </div>
                <div class="thinkItem">
                  <div><i class="el-icon-success"></i></div>
                  <div class="itemContent">总目标是要了解玻璃窑炉燃烧和换热机理知识。其中一个子任务是了解玻璃窑炉燃烧和换热机理知识，然后深入了解玻璃窑炉燃烧和换热机理</div>
                </div> -->
            </div>
          </div>
          <div v-if="!thinkFullFlag" v-show="!phoneFlag"
            :class="inputHeight > 80 ? 'clearChatBtnBox clearChatBtnBoxHigh' : 'clearChatBtnBox'">
            <ul v-if="showGuess" :class="inputHeight > 80 ? 'guess guessHigh' : 'guess'">
              <div v-if="guessList.length > 0">
                <li v-for="(el, index) in guessList" :key="index" @click="handleGussText(el)">
                  <el-tooltip class="item" :content="el" effect="dark" placement="bottom">
                    <span>{{ el }}</span>
                  </el-tooltip>
                </li>
              </div>
              <div v-else>
                <span style="font-size: 12px">暂无数据</span>
              </div>
            </ul>
            <div v-if="guessList.length" ref="clearChat" :class="
                [
                  'process stream',
                  'process stream running',
                  'process running',
                  'processing',
                  'scheme generating',
                  'clear_history',
                  'process_stream_message'
                ].indexOf(systemMessages) > -1
                  ? 'clearChat think-btn-disabled'
                  : 'clearChat'
              " @click="toggleGuess">
              指令中心
            </div>
            <div v-if="!hasChatingName && !isOccupied">
              <el-popover v-model="chendianVisable" placement="top-start" :visible-arrow="false" :offset="0"
                :popper-class="
                  inputHeight > 80 ? 'chendianfangan chendianfanganHigh' : 'chendianfangan'
                " trigger="click" @hide="
                  () => {
                    planSearchFlag = false
                  }
                ">
                <ul class="guessPovper">
                  <div>
                    <div class="guess-header">
                      <div v-if="!planSearchFlag" class="title">
                        根据你的问题，从已沉淀的知识库中推荐以下相关方案：
                      </div>
                      <div v-if="planSearchFlag" class="title">
                        <el-input v-model.trim="planSearch" class="samllinput" style="width: 290px" placeholder="请输入"
                          @input="chendianSearchFun"></el-input>
                      </div>
                      <div v-if="!planSearchFlag" class="plansearch" @click="
                          () => {
                            planSearchFlag = true
                          }
                        ">
                        <i class="el-icon-search"></i>
                      </div>
                      <div v-if="planSearchFlag" class="plansearch" @click="
                          () => {
                            chendianList = globalChendianList
                            planSearch = ''
                            planSearchFlag = false
                          }
                        ">
                        <i class="el-icon-circle-close"></i>
                      </div>
                      <div v-if="!planSearchFlag" class="huanyihuan" @click="chendianFun">
                        换一换
                      </div>
                    </div>
                    <li v-for="(el, index) in chendianList" :key="index" @click="handleChendianText(el)">
                      <el-tooltip class="item" :content="el.question" effect="dark" placement="bottom">
                        <span>{{ el.question }}</span>
                      </el-tooltip>
                    </li>
                    <div v-if="chendianList.length === 0">
                      <span style="font-size: 12px">暂无参考方案</span>
                    </div>
                  </div>
                </ul>
                <div v-if="schemeInfo.agent_scene_code !== 'other_assistant_scene'" slot="reference" :class="
                    [
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1
                      ? 'clearChat think-btn-disabled'
                      : 'clearChat'
                  " @click="chendianFun">
                  参考已沉淀方案
                </div>
              </el-popover>
            </div>
            <div v-if="agentRoleList.length > 1">
              <el-popover v-model="chendianVisable2" placement="top-start" :visible-arrow="false" :offset="0"
                :popper-class="
                  inputHeight > 80 ? 'chendianfangan chendianfanganHigh' : 'chendianfangan'
                " trigger="click">
                <ul class="guessPovper">
                  <div>
                    <li v-for="(el, index) in agentRoleList" :key="index" @click="handleChangeRole(el)">
                      <div class="otherRole" style="padding: 6px 0px">
                        <img v-if="el.icon" :src="el.icon" />
                        <div style="flex: 1">{{ el.name }}</div>
                        <div class="mark-tag" style="margin-left: 16px">{{ el.alias }}</div>
                      </div>
                    </li>
                    <div v-if="agentRoleList.length < 1">
                      <span style="font-size: 12px">暂无其他角色</span>
                    </div>
                  </div>
                </ul>
                <div slot="reference" :class="
                    [
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1
                      ? 'clearChat think-btn-disabled'
                      : 'clearChat'
                  ">
                  生产辅助角色
                </div>
              </el-popover>
            </div>
            <!-- <div v-if="(systemMessages === 'process waiting'|| systemMessages === 'scheme generating completed' ||systemMessages ==='process completed') && !hasChatingName">
                <div class="clearChat" @click="overChat">保存方案</div>
              </div> -->
          </div>
          <div v-if="showSelectVisable" v-show="!phoneFlag"
            :class="inputHeight > 80 ? 'selectRole selectRoleHigh' : 'selectRole'">
            <div v-for="(el, index) in agentRoleList" :key="index" @click="handleSelectRole(el)">
              <div class="roleItem" style="padding: 6px 0px">
                <img v-if="el.icon" :src="el.icon" />
                <el-tooltip class="item" effect="dark" :content="el.name" placement="top">
                  <div class="name" style="flex: 1">{{ el.name }}</div>
                </el-tooltip>
                <div class="mark-tag" style="margin-left: 16px">{{ el.alias }}</div>
              </div>
            </div>
          </div>
          <div v-show="!phoneFlag" class="chatFooter">
            <!-- systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' -->
            <div class="chatFooterTextInput">
              <el-tooltip class="item" effect="dark" content="清空聊天记录" placement="top">
                <div :class="
                    [
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 ||
  hasChatingName || isOccupied ||
                    qaBoxLoading
                      ? 'think-btn-disabled'
                      : 'clear'
                  " @click="
                    () => {
                     if(isReadOnly) return
                      if (
                        [
                          'process stream',
                          'process stream running',
                          'process running',
                          'processing',
                          'scheme generating',
                          'clear_history',
                          'process_stream_message'
                        ].indexOf(systemMessages) < 0 &&
                        !hasChatingName &&
                        !qaBoxLoading
                      ) {
                        clearHistory()
                      }
                    }
                  ">
                  <SvgIcon :class="{ 'cust-disabled': isReadOnly }" name="del" class="icon clear-icon" />
                </div>
              </el-tooltip>
              <div id="myInputText" class="chatInput">
                <div v-if="currentRoleInfoTemp.name" class="roleTag">
                  <el-tag size="small" closable @close="removeRole">{{ currentRoleInfoTemp.name }}
                  </el-tag>
                </div>
                <el-input v-if="speakingFlag === 'running' || shibieLoading" ref="myChatInputText"
                  v-model.trim="yuyinText" clearable readonly type="textarea" resize="none"
                  :autosize="{ minRows: 2, maxRows: 2 }" :disabled="
                    ([
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
                    hasChatingName !== '' || isOccupied
                  " style="width: 100%" placeholder="">
                </el-input>
                <el-input v-else ref="myChatInputText" v-model="toMessage.content" clearable type="textarea"
                  id="saveImgPlanChat" resize="none" :autosize="{ minRows: 2, maxRows: 2 }" :disabled="
                    ([
                      'process stream',
                      'process running',
                      'process stream running',
                      'processing',
                      'scheme generating',
                      'process_stream_message',
                      'process stream running',
                      'clear_history'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
hasChatingName !== '' || isOccupied ||isReadOnly
                  " style="width: 100%" :placeholder="
                    navType === 'Mac'
                      ? '请输入你的问题，可通过cmd+回车换行'
                      : '请输入你的问题，可通过Ctrl+回车换行'
                  " @input="handleInput" @keydown.native="carriageReturn($event)">
                </el-input>
              </div>
              <div class="send-btn">
                <el-upload ref="uploadBtn" :action="uploadUrl" :show-file-list="false" :data="uploadParam" :limit="1"
                  accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG" :before-upload="beforeUpload"
                  :on-success="modelUploadSuccess">
                  <el-tooltip class="item" effect="dark" :content="
                      instanceInfo?.enable_image_text_msg
                        ? '支持图片上传，基于图文问答'
                        : '当前能力不支持图文消息'
                    ">
                    <el-button :type="true ? 'primary' : 'info'" icon="el-icon-picture-outline" circle
                      :disabled="!instanceInfo?.enable_image_text_msg ||isReadOnly"></el-button>
                  </el-tooltip>
                </el-upload>
                <div :class="
                    ([
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
  hasChatingName !== '' || isOccupied ||
                    shibieLoading
                      ? 'yuyinBtn yuyinBtnDisabled'
                      : 'yuyinBtn'
                  ">
                  <img :class="{ 'cust-disabled': isReadOnly }" v-if="speakingFlag === 'start'" src="@/assets/images/planGenerater/shuohua.png"
                    @click="startRecording" />
                  <img v-if="speakingFlag === 'running'" src="@/assets/images/planGenerater/yuyin.gif"
                    @click="stopRecording" />
                  <img v-if="speakingFlag === 'end'" src="@/assets/images/planGenerater/zhuanhuan-loading.gif" />
                </div>
                <div class="send-desc">
                  <img src="@/assets/images/planGenerater/back.png" style="margin-right: 4px" />发送
                </div>
                <el-button type="primary" icon="el-icon-position" :disabled="
                    ([
                      'process stream',
                      'process running',
                      'process stream running',
                      'processing',
                      'scheme generating',
                      'process_stream_message',
                      'process stream running',
                      'clear_history'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
hasChatingName !== '' || isOccupied ||
                    toMessage.content === '' ||
                    speakingFlag === 'running' ||
                    shibieLoading || isReadOnly
                  " circle @click="sendMessage" ></el-button>
              </div>
            </div>
            <div class="chatFooterImage" v-if="this.toMessage?.image_path || '' != ''"
              style="height: 140px; background-color: #e5e5e5">
              <el-image style="width: 100px; height: 100px; margin-left: 10px; margin-top: 10px"
                :src="this.toMessage.image_path" :fit="fit" :preview-src-list="[this.toMessage.image_path]"></el-image>
              <div style="width: 120px; display: flex; justify-content: center; align-items: center">
                <el-button type="text" icon="el-icon-delete" circle
                  style="color: red; padding-top: 0px; bckground-color: #e5e5e5" @click="clearImage"></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="planDetailShow && !rightFullFlag" v-show="!phoneFlag" id="resize" class="resize" title="收缩侧边栏"
        @mousedown="startDrag">
        <div class="el-two-column__icon-top">
          <div class="el-two-column__icon-top-bar"></div>
        </div>
        <div class="el-two-column__trigger-icon">
          <SvgIcon name="dragborder" class="process-icon" />
        </div>
        <div class="el-two-column__icon-bottom">
          <div class="el-two-column__icon-bottom-bar"></div>
        </div>
      </div>
      <div v-show="!phoneFlag" id="right-content" :style="{
          width: rightWidth,
          marginLeft: planDetailShow ? '0px' : '16px',
          transition: isDragging ? 'none' : 'width 0.2s',
          userSelect: isDragging ? 'none' : 'auto',
          height: (!planDetailShow && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
          maxHeight: (!planDetailShow && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
          top: (!planDetailShow && !isHideIframe) ?  '48px':  '0px',
          left: planDetailShow ? '0px!important' : isHideIframe ? '0px' : '64px!important',
          maxWidth: planDetailShow ? '100%!important' : isHideIframe ? 'calc( 100% - 0px )!important' : 'calc( 100% - 64px )!important',
        }" :class="!planDetailShow ? 'chatRight chatRightFull customMd' : 'chatRight customMd'">
        <zhushouChatKnowledge :isReadOnly="isReadOnly" v-if="schemeInfo.agent_scene_code === 'other_assistant_scene'"
          :schemeDetail="detailContent.text" :knowledgeTheme="knowledgeTheme"
          @updateSchemeDetailName="updateSchemeDetailName" />
        <div :class="
            schemeInfo.agent_scene_code !== 'other_assistant_scene'
              ? 'optContent'
              : 'optContent optContent2'
          " v-if="schemeInfo.agent_scene_code !== 'other_assistant_scene'">
          <div class="optHeader">
            <div class="tabClass">
              <div :class="activeZl ? 'rightTitle' : 'rightTitle activeZl'" @click="changeActiveZl(false)">
                <button :class="['modern-button', { active: !activeZl }]"><i class="el-icon-document"></i>方案</button>
              </div>
              <div :class="activeZl ? 'ziliao activeZl' : 'ziliao'" @click="changeActiveZl(true)">
                <button :class="['modern-button', { active: activeZl }]"><i class="el-icon-folder"></i>任务</button>
              </div>
            </div>

            <div class="rightTitleOpt" v-show="!activeZl">
              <el-tooltip v-if="!dagangFlag && !isEdit" class="item" effect="dark" content="脑图" placement="top">
                <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied"
                  type="info" size="mini" @click="changeDagang"><img
                    src="@/assets/images/planGenerater/naotu.png" /></el-button>
              </el-tooltip>
              <el-tooltip v-if="dagangFlag && !isEdit" class="item" effect="dark" content="大纲" placement="top">
                <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied"
                  type="info" size="mini" @click="changeDagang"><img
                    src="@/assets/images/planGenerater/dagang.png" /></el-button>
              </el-tooltip>
              <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
                <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied"
                  type="info" size="mini" @click="
                    () => {
                      hisDetail = detailContent.text
                      isEdit = true
                    }
                  "><img src="@/assets/images/planGenerater/bianji.png" /></el-button>
              </el-tooltip>
              <template v-if="isEdit">
                <el-tooltip class="item" effect="dark" content="保存" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSave"><img
                      src="@/assets/images/planGenerater/baocun.png" /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSaveClose"><img
                      src="@/assets/images/planGenerater/quxiao.png" /></el-button>
                </el-tooltip>
              </template>
              <el-tooltip v-if="abilityList.length" class="item" effect="dark" content="方案优化" placement="top">
                <el-button :disabled="abilityList.length === 0 || hasChatingName !== '' || isOccupied" type="info"
                  size="mini" @click="showYouhua"><img
                    src="@/assets/images/planGenerater/fanganyouhua.png" /></el-button>
              </el-tooltip>
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top">
                <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png" />
                </el-button>
              </el-tooltip>
              <el-dropdown :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' "
                style="margin-left: 10px" @command="handleCommand">
                <el-button type="info" size="mini"><img src="@/assets/images/planGenerater/more.png" /></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="rightTitleOpt" v-show="activeZl">
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top">
                <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png" />
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div v-show="!activeZl" :class="
              schemeInfo.agent_scene_code !== 'other_assistant_scene' ? 'optScroll' : 'optScroll2'
            ">
            <template>
              <div v-show="!dagangFlag" id="detail-content" :class="
                  schemeInfo.agent_scene_code == 'other_assistant_scene'
                    ? 'optContentBox h-full-340'
                    : 'optContentBox h-full'
                " @mouseenter="fangda" @mouseup="fangda2">
                <MyEditor id="MyEditor" ref="MyEditor" :md-content="detailContent.text" :is-edit="isEdit"
                  @updateContent="handleUpdateContent"></MyEditor>
              </div>
              <div v-show="dagangFlag" style="width: 100%; height: 100%" @mouseenter="fangda">
                <svg id="markmap" class="mark-map"></svg>
              </div>
              <el-link type="primary">{{ detailContent.file_url }}</el-link>
              <div class="others" v-if="
                  detailContent.text != '' && schemeInfo.agent_scene_code == 'other_assistant_scene'
                ">
                <div class="o-l">
                  <div class="lt">关联文章/知识库</div>
                  <div class="k-list">
                    <div class="k-box">
                      <img src="/img/icon_baselist.png" />
                      <div class="kt">城市基础设施安全工程</div>
                    </div>
                    <div class="k-box">
                      <img src="/img/icon_baselist.png" />
                      <div class="kt">基础知识</div>
                    </div>
                  </div>
                </div>
                <div class="o-r">
                  <div class="rt">相关问题</div>
                  <div class="q-list">
                    <div class="q-box">
                      <div class="qt">如何避免私自安装燃气设备</div>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                    <div class="q-box">
                      <div class="qt">燃气阀门的常见隐患有哪些</div>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                    <div class="q-box">
                      <div class="qt">如何检查燃气表是否漏气</div>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                    <div class="q-box">
                      <div class="qt">燃气管道安装需要注意什么</div>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                    <div class="q-box">
                      <div class="qt">燃气具超期服役会导致什么问题</div>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <taskNew v-show="activeZl" ref="taskNewRef"></taskNew>
        </div>
        <div class="optFooter" v-if="schemeInfo.agent_scene_code !== 'other_assistant_scene'">
          <el-button class="button-last" type="primary" @click="regenerate()">生成
          </el-button>
        </div>
        <treeProcess :is-visible="processVisable" :tree-process-val="optionDataProcess" @close="closeSikaoRizhi" />
        <planOptimize :is-visible="youhuaVisable" :tree-status="planStatus" :sql-data="optimizeData"
          :agent-scene="schemeInfo.agent_scene" @close="closeYouhuaEdit" @updateOptimize="emitOptimize" />
      </div>
    </div>
    <div v-if="activeStep * 1 === 1" class="containerBox">
      <zhushouChatTree :display-type="displayType" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
        :tree-process-val="treeDataProcess" :tree-status="treeStatus" @updateStep="handleUpdateStep"
        @updateGenerate="handleGenerate" ref="zhushouChatTreeRef"></zhushouChatTree>
    </div>
    <div v-if="
        activeStep * 1 === 3 &&
        [
          'device_ops_assistant_scene',
          'device_ops_assistant_scene-v1',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'intelligent_conversation_scene',
          'sop_scene'
        ].indexOf(schemeInfo.agent_scene_code) > -1
      " class="containerBox">
      <zhushouThree :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :tree-process-status="abilityDataProcessStatus" :tree-status="treeStatus" :code-analysis-data="codeAnalysisData"
        :code-analysis-data-status="codeAnalysisDataStatus" :code-analysis-process-data="codeAnalysisProcessData"
        @updateStep="handleUpdateStep" @updateCodeGenerate="handleCodeGenerate"
        @updateCodeAnalysis="handleUpdateCodeAnalysis" @stopAbilityGen="stopAbilityGen"></zhushouThree>
    </div>
    <div v-if="activeStep * 1 === 3 && schemeInfo.agent_scene_code === 'rule_generation_scene'" class="containerBox">
      <zhushouRule :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :tree-process-status="abilityDataProcessStatus" :tree-status="treeStatus" @updateStep="handleUpdateStep"
        @updateRuleGenerate="handleRuleGenerate" @stopAbilityGen="stopAbilityGen"></zhushouRule>
    </div>
    <div v-if="
        activeStep * 1 === 2 && schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
      " class="containerBox">
      <zhushouChatDuiqiClient :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :sql-status="sqlStatus" :tree-status="treeStatus" :sql-data="sqlData" @updateStep="handleUpdateStep"
        @updateCodeGenerate="handleDataAlign" @updateSQLSteam="handleEmitSQL"></zhushouChatDuiqiClient>
    </div>
    <div v-if="
        activeStep * 1 === 3 && schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
      " class="containerBox">
      <zhushouThree :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :tree-process-status="abilityDataProcessStatus" :tree-status="treeStatus" :code-analysis-data="codeAnalysisData"
        :code-analysis-data-status="codeAnalysisDataStatus" :code-analysis-process-data="codeAnalysisProcessData"
        @updateStep="handleUpdateStep" @updateCodeGenerate="handleCodeGenerate"
        @updateCodeAnalysis="handleUpdateCodeAnalysis" @stopAbilityGen="stopAbilityGen"></zhushouThree>
    </div>
    <div v-if="
        activeStep * 1 === 4 && schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
      " class="containerBox">
      <zhushouChatBi :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
        :tree-process-val="abilityDataProcess" :tree-status="treeStatus" @updateStep="handleUpdateStep"
        @updateCodeGenerate="handleCodeGenerate"></zhushouChatBi>
    </div>
    <div v-if="
        activeStep * 1 === 4 &&
        [
          'device_ops_assistant_scene',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'intelligent_conversation_scene',
          'sop_scene'
        ].indexOf(schemeInfo.agent_scene_code) > -1
      " class="containerBox">
      <zhushouChatTest :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
        :tree-status="treeStatus" :session-id="sessionId" :publish-ability="detailContent.publish_ability"
        @updateStep="handleUpdateStep" @updateGenerate="handleGenerate" />
    </div>
    <div v-if="activeStep * 1 === 4 && schemeInfo.agent_scene_code === 'rule_generation_scene'" class="containerBox">
      <zhushouChatRuleTest :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
        :tree-status="treeStatus" :session-id="sessionId" @updateStep="handleUpdateStep"
        @updateGenerate="handleGenerate" />
    </div>
    <div v-if="activeStep * 1 === 4 && schemeInfo.agent_scene_code === 'device_ops_assistant_scene-v1'"
      class="containerBox">
      <zhushouChatForm :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
        :tree-status="treeStatus" @updateStep="handleUpdateStep" @updateGenerate="handleGenerate" />
    </div>
    <div v-if="
        activeStep * 1 === 2 &&
        [
          'device_ops_assistant_scene',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'rule_generation_scene',
          'intelligent_conversation_scene'
        ].indexOf(schemeInfo.agent_scene_code) > -1
      " class="containerBox">
      <zhushouChatDuiqi :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :sql-status="sqlStatus" :tree-status="treeStatus" :sql-data="sqlData" @updateStep="handleUpdateStep"
        @updateCodeGenerate="handleDataAlign" @updateSQLSteam="handleEmitSQL"></zhushouChatDuiqi>
    </div>
    <div v-if="activeStep * 1 === 2 && schemeInfo.agent_scene_code === 'sop_scene'" class="containerBox">
      <zhushouChatDuiqiSop :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :sql-status="sqlStatus" :tree-status="treeStatus" :sql-data="sqlData" @updateStep="handleUpdateStep"
        @updateCodeGenerate="handleDataAlign" @updateSQLSteam="handleEmitSQL"></zhushouChatDuiqiSop>
    </div>
    <div v-if="activeStep * 1 === 2 && schemeInfo.agent_scene_code === 'device_ops_assistant_scene-v1'"
      class="containerBox">
      <zhushouChatDuiqiOld :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-process-val="abilityDataProcess"
        :sql-status="sqlStatus" :tree-status="treeStatus" :sql-data="sqlData" @updateStep="handleUpdateStep"
        @updateCodeGenerate="handleDataAlign" @updateSQLSteam="handleEmitSQL"></zhushouChatDuiqiOld>
    </div>
    <div v-if="isEmbedMode" class="EmbedModeWrap">
      <el-button>完成</el-button>
    </div>
    <transition name="fade">
      <div v-if="hasChatingName !== '' || isOccupied" class="overlay" v-loading="kickLoading"
        element-loading-spinner="el-icon-loading"></div>
    </transition>
    <editModal :is-visible="editVisible" @updateEditModal="updateEditModal"></editModal>
  </div>
</template>

<script>
import { mapGetters ,mapState, mapMutations} from 'vuex'
import {
  querySchemeDetailById,
  chatDisconnect,
  SchemeSaveKnow,
  SchemeDetail,
  queryGreeting,
  queryChatIsUseing,
  SchemeTasks,
  SchemeConversationDetail,
  PlanTaskEdit,
  queryQuickReply,
  getSuggestionList,
  queryAbilityList,
  queryAgentInfoDetail,
  queryQuestionList,
  queryAnsRole,
  startConversationWithRole,
  startSchemeGenerate,
  startAppendHistory,
  startDecisionTreeGenerateRequest,
  startClearHistory,
  startStopThinking,
  startTaskGenerate,
  startAlignDataGenerate,
  startAbilityGenerate,
  startSchemeOptimize,
  startSqlGenerate,
  startConversation,
  startCodeAnalysis,
  startRuleGenerate,
  UpdateScheme,
  getBaidu,
  getInstanceInfo,
  knowledgeQaSave
} from '@/api/planGenerateApi.js'
import dayjs from 'dayjs'
import editModal from './ConfTask/editModal.vue'
import 'highlight.js/styles/stackoverflow-dark.css'
import '@/style/github-markdown.css'
import zhushouChatTree from './zhushouChatTree.vue'
import zhushouChatForm from './zhushouChatForm.vue'
import zhushouChatBi from './zhushouChatBi.vue'
import zhushouThree from './zhushouThree.vue'
import zhushouChatDuiqi from './zhushouChatDuiqiNew.vue'
import zhushouChatDuiqiOld from './zhushouChatDuiqiOld.vue'
import zhushouChatDuiqiClient from './zhushouChatDuiqiClient.vue'
import zhushouChatTest from './zhushouChatTest.vue'
import zhushouRule from './zhushouRule.vue'
import zhushouChatRuleTest from './zhushouChatRuleTest.vue'
import zhushouChatKnowledge from './zhushouChatKnowledge.vue'
import treeProcess from './treeProcess.vue'
import planOptimize from './fanganyouhua.vue'
import panzoom from 'panzoom'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import MyEditor from './mdEditor.vue'
import MyEditorPreview from './mdEditorPreview.vue'
import Recorder from 'js-audio-recorder'
import zhushouChatDuiqiSop from './zhushouChatDuiqiNewSop.vue'
import io from 'socket.io-client'
import base64 from 'base64-js'
import CryptoJs from 'crypto-js'
import Bus from '../../components/bus'
import KickFeat from './ConfTask/components/KickFeat.vue'
import taskNew from './taskNew.vue'
const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
}
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {}


  const messageRoleToHandlersMap = {
  decision_tree_generate: 'handleProcessTreeDetail', // 思维树生成
  decision_tree_gen_process: 'handleProcessTreeData', // 思维树生成过程
  scheme_generate: 'handleTaskDetail', // 方案明细的流式消息
  scheme_gen_process: 'handleProcessSchemeData', // 方案明细的过程流式消息
  gen_process: 'handleProcessDetail', // 聊天的生成过程的流式消息
  ability_generate: 'handleAbilityDetail', // 能力生成
  ability_gen_process: 'handleCodeProcesData', // 能力生成过程
  sql_generate: 'handleSQLData', // SQL 流消息
  sql_gen_process: '',
  align_data_gen_process: 'handleAlignProcessData', // 数据对齐过程流，不过通用监听那个不从这里返回了。
  scheme_optimize: 'handleOptimizeData', // 方案优化
  scheme_optimize_gen_process: 'handleOptimizeProcessData', // 方案优化过程
  code_analysis: 'handleCodeAnalysisData', // 代码分析
  code_analysis_gen_process: 'handleCodeAnalysisProcessData', // 代码分析过程
  rule_generate: 'handleRuleDetail', // 规则生成
  rule_generate_gen_process: 'handleProcessRuleData', // 规则生成过程
  model_param_extraction: 'modelData', //模型参数识别
  model_param_extraction_process: 'modelData', //模型参数识别过程
  modeling_info_structure: 'modelData', // 结构化建模信息
  modeling_info_structure_process: 'modelData', // 结构化建模信息过程
  math_model_generate: 'modelData', //生成数学模型
  math_model_generate_process: 'modelData' //生成数学模型过程
}


export default {
  components: {
    taskNew,
    zhushouChatTree,
    zhushouChatForm,
    zhushouThree,
    treeProcess,
    planOptimize,
    zhushouChatBi,
    zhushouChatDuiqi,
    zhushouChatTest,
    zhushouChatDuiqiOld,
    zhushouChatDuiqiClient,
    MyEditor,
    MyEditorPreview,
    zhushouRule,
    zhushouChatRuleTest,
    zhushouChatDuiqiSop,
    zhushouChatKnowledge,
    KickFeat,
    editModal
  },
  data() {
    return {
      activeZl:false,
      editModalVisable: false,
      kickLoading: false,
      isOccupied: false,
      knowledgeTheme: '',
      displayType: 1,
      speakingFlag: 'start',
      phoneFlag: false,
      phoneStatus: 'start',
      recorderEx: new Recorder(parameter),
      recorderPhoneEx: new Recorder(parameter),
      qaBoxLoading: false,
      contentEditor: '',
      qaList: [],
      agentRoleFlag: false,
      planSearchFlag: false,
      planSearch: '',
      testdata:
        'graph TD\nA([开始]) --> B1{检查电源插座是否有电}\nB1 -->|有电| B2[检查家里主电源开关是否打开]\nB2 -->|打开| C1[检查家庭电路中断器是否跳闸]\nC1 -->|跳闸| E1[复位中断器-然后检查电是否恢复]\nE1 -->|电恢复| END([结束])\nE1 -->|电未恢复| F[联系专业电工进行故障排查和维修]\nC1 -->|没有跳闸| D1[检查电路配线是否受损]\nD1 -->|受损| E2[修复或更换电路线路-然后检查电是否恢复]\nE2 -->|电未恢复| F\nD1 -->|未受损| F\nB2 -->|没有打开| B3[打开电源开关-然后检查电是否恢复]\nB3 -->|电未恢复| F\nB1 -->|没有电| C1',
      isSuperAdmin: false, // 是否超级管理员
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      tableLoading: false, // 加载状态
      detailContent: { text: '', file_url: '' },
      hisDetail: '',
      processContent: { text: '' },
      processRunningContent: '', // 最后一条正在思考的过程内容
      processList: [],
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      historyChat: {
        conversation_id: '',
        messages: []
      },
      currentText: '',
      toMessage: { content: '', image_key: '', image_path: '' },
      socket: null,
      systemMessages: '',
      taskStatusText: '',
      timer: null,
      agentError: false,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '40%',
      rightWidth: '60%',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskGenLoading: false,
      insertWriteFlag: true,
      replaceWriteFlag: true,
      writeText: '',
      greets: '',
      taskGenType: '',
      sessionId: '',
      activeStep: 0,
      treeData: '', // 思维图、思维树内容
      optionDataProcess: '',
      treeDataProcess: '', // 生成过程
      treeDataProcessNodes: [], // 有分组的生成过程
      abilityDataProcess: '', // 生成过程
      abilityDataProcessStatus: '', // 生成过程状态
      codeAnalysisData: '', // 代码分析
      codeAnalysisDataStatus: '', // 代码分析过程状态
      codeAnalysisProcessData: '', // 代码分析过程
      codeAnalysisProcessDataStatus: '', // 代码分析过程状态
      treeStatus: -1,
      cursorInsertPosition: '',
      userInfoData: {}, // 当前用户登录信息
      showGuess: false, // 是否显示参考提示词列表标识
      guessList: [], // 参考提示词的内容列表
      hasChatingName: '', // 开始会话的时候，判断当前有没有被其他人使用中
      panZoomRef: null,
      processVisable: false, // 思考过程弹窗标志
      sqlData: '', // sql流数据
      sqlStatus: -1,
      chendianVisable: false, // 参考已沉淀方案弹窗标志
      chendianVisable2: false,
      globalChendianList: [],
      chendianList: [
        // {
        //     "question": "分析",
        //     "answer": "# 方案明细\n\n根据您遇到的空压机排气高温故障，以下是我为您准备的解决方案：\n\n1. **检查压缩机排气温度**\n\n   - 确保压缩机排气温度不超过厂家规定的最高限制值；\n   - 如果温度过高，可能是由于压缩机工作不正常导致的，需要进一步诊断原因。\n\n2. **检查压缩机冷却系统**\n\n   - 检查冷却系统的运行情况；\n   - 清洁冷却器，确保散热良好；\n   - 检查冷却系统的冷却剂水平及质量。\n\n3. **检查冷却风扇**\n\n   - 确保冷却风扇正常工作；\n   - 检查风扇叶片是否有损坏或堵塞。\n\n4. **检查排气系统管道**\n\n   - 检查排气系统管道是否存在堵塞或破损；\n   - 清除堵塞物，修复破损部分。\n\n请根据上述方案逐步进行排查和修复，如有需要，请随时告知进展情况，我将为您提供进一步的支持和建议。\n"
        // },
        // {
        //     "question": "空压机排气",
        //     "answer": "# 高温故障检测流程\n\n- 排气管温度检查\n  - 50 摄氏度：排气温度高告警\n  - <=50摄氏度：end\n\n- 排气温度高告警\n  - 检测环境温度\n\n- 检测环境温度\n  - 40 摄氏度：请加大站房通风，降低环境温度\n  - <=40摄氏度：检测喷油温度\n- 检测喷油温度\n  - 70 摄氏度：检测冷却器进油和出油温度\n  - <=70摄氏度：检测喷油压力\n- 检测喷油压力\n  - 2.5bar: 检测断油电磁阀阀位\n  - <= 2.5bar: 请检查供油软管是否爆裂、是否有漏油点\n\n- 检测断油电磁阀阀位\n  - 不正常：检修断油阀\n\n- 检测冷却器进油和出油温度\n\n  - 温差<=5摄氏度：检查温控阀是否损坏\n\n  - 温差>5摄氏度：\n    - 【风冷机型】检查冷却风扇电流\n    - 【水冷机型】检查冷却水进水压力\n\n- 检查冷却风扇电流\n\n  - 电流>0：请清洗冷却器\n  - 电流<=0：请检修冷却风扇\n\n- 检查冷却水进水压力\n\n  - 小于或等于1bar：检查冷却水泵\n  - 大于1bar：检查冷却水进水温度\n\n- 检查冷却水进水温度\n\n  - 大于或等于32摄氏度：请检查冷却塔风扇"
        // }
      ], // 沉淀方案列表
      youhuaVisable: false,
      planStatus: -1, // 方案优化流状态
      optimizeData: '', // 方案优化流数据
      abilityList: [], // 方案优化可选能力列表，用来判断方案优化按钮是否可用
      dagangFlag: false, // 大纲视图
      lastClickTime: 0, // 点击间隔
      inputHeight: 60,
      navType: '',
      agentAvatorInfo: {}, // 角色基本信息
      agentRoleList: [], // 角色列表
      agentAvatorInfoMap: {}, // 角色信息map
      currentRoleInfo: {}, // 当前角色id
      showSelectVisable: false,
      currentRoleInfoTemp: {}, // 临时
      agentAvatorInfoList: [],
      ansBoxLoading: false,
      ansRole: '',
      audioChunks: [],
      yuyinText: '',
      shibieLoading: false,
      eventSource: null,
      schemeInfo: {},
      instanceInfo: {},
      websocket: null,
      audioContext: null,
      isReadOnly: false,
      websocketPara: {
        URI: 'wss://vop.baidu.com/realtime_asr',
        APPKEY: 'zrhz2KGQLCxgVrIPdvcUa9c2',
        DEV_PID: 15372, // 声道，支持 1 或 2， 默认是1,
        sample: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        CHUNK_SIZE: 1024, // 每个音频帧的大小，单位为字节
        APPID: 60519323
      },
      uploadUrl: '',
      uploadParam: {}
    }
  },
  computed: {
   ...mapState('operations', ['editVisible']),
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter',
      globalNavigatorStatus: 'common/getMenuCollapseGetter'
    }),
    isEmbedMode() {
      return this.$route.query.embed === "true"; // URL 参数控制
      // return true
    },
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },
  },
  watch: {},
  async created() {
    if (!this.$route.query.id) {
      this.$router.push({
        path: '/planGenerate/first',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      })
    }
  },
  async beforeRouteEnter(to, from, next) {
    console.log("from xxx2", from);
    console.log("to xxx2", to);
    if (from.path === "/planGenerate/ConfTaskPlanchat") {
      next();
      return
    }
    try {
      // 在 next 回调之外执行异步操作
      if( to.query.id != null && to.query.id != undefined && to.query.id != '' ) {
       const res = await querySchemeDetailById({ scheme_id: Number(to.query.id) });
       console.log("res 0000222222", res.data.result.agent_scene_code);
       if (['dev_assistant_scene', 'other_assistant_scene'].includes(res.data.result.agent_scene_code)) {
         next();
         return
       }
       if (['digital_twin_assistant_scene'].includes(res.data.result.agent_scene_code)) {
         next({ path: '/planGenerate/ConfTaskPlanchat', query: to.query });
         return
       }

      }

      next();
      // 根据异步操作的结果来决定是否重定向
      // if (res.status === 200 && res.data.code === 200) {
      //   if (res.data.result.version === 'v1') {
      //     console.log("正常版本 2", res.data.result.version);
      //     // 版本是 'v1'，不需要重定向，所以直接调用 next() 并传递 to 对象
      //     next();
      //   } else {
      //     console.log("跳转版本 2", res.data.result.version);
      //     // 版本不是 'v1'，需要重定向，同时保留 to 的查询参数
      //     next({ path: '/planGenerate/ConfTaskPlanchat', query: to.query });
      //   }
      // }
    } catch (error) {
      // 处理错误，可能需要重定向到错误页面或者显示错误消息
      console.error(error);
      next(); // 假设有一个错误页面的路由
    }
  },
  beforeDestroy() {
    Bus.$off('operations-research-changeDisplayType')
    clearInterval()
    clearInterval(this.timer)
    this.eventSource && this.eventSource.close()
    this.timer = null
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
  //  if (window.top !== window.self) {
  //   window.addEventListener('message', (event) => {
  //     try {
  //       const datajson = JSON.parse(event.data)
  //       this.setEmbed(datajson.embed)
  //       console.log('顶部收到空间信息2222555', event.data);
  //     } catch (error) {

  //     }
  //   });
  // }
   this.isReadOnly = Boolean(this.$route.query.isReadOnly) || false
   console.log("mounted 0000", this.$route.query.isReadOnly);
    Bus.$on('operations-research-edit', (data) => {
      this.editFn()
    })
    this.saveImg()
    Bus.$on('operations-research-changeDisplayType', (data) => {
      this.changeDisplayType()
    })
    await this.schemeDetailById()
    await this.getBaiduFn()
    await this.getInstance()
    setTimeout(() => {
      if (window.message) {
        console.log('window.message', window.message)
        this.toMessage = window.message
        // this.currentText = window.customeDescription;
        this.sendMessage()
        window.message = null
      }
    }, 1000)
  },
  methods: {
    regenerate(){
    //     this.treeData = ''
    //     this.treeProcess = '';
    //     this.taskLoading = true;
    //     this.$emit('updateGenerate')
    this.activeZl = true
    this.$refs.taskNewRef.setTaskLoading(true)
    this.handleGenerate();
    if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({scheme_id: Number(this.$route.query.id)}).then(res => {
        const name = res.data.result.name;
      })
    },
    changeActiveZl(val){
      this.activeZl = val
    },
   ...mapMutations('operations',['researchEdit','setEmbed']),
    editFn() {
      this.editModalVisable = true
    },
    updateEditModal() {
      // this.schemeDetailById()
      // this.editModalVisable = false
    },
    saveImg() {
      const tath = this
      document.getElementById('saveImgPlanChat').addEventListener('paste', async function (event) {
        event.preventDefault();

        // 检查是否启用了图像和文本消息功能
        const enableImageTextMsg = tath.instanceInfo?.enable_image_text_msg

        // 获取剪切板数据
        const clipboardData = event.clipboardData || window.clipboardData;

        if (!clipboardData || !clipboardData.items) {
          console.warn('No clipboard data available.');
          return;
        }

        let hasImage = false;
        let textContent = '';
        const promises = [];

        console.log('Clipboard items:', clipboardData.items);

        for (let i = 0; i < clipboardData.items.length; i++) {
          const item = clipboardData.items[i];

          if (item && item.type) {
            console.log('Item type:', item.type, 'Item kind:', item.kind);

            if (item.type.startsWith("image/") && tath.instanceInfo?.enable_image_text_msg) {
              // 处理图像
              hasImage = true;
              const blob = item.getAsFile();
              if (blob) {
                await tath.newUpdown(blob, blob.type, blob.size, blob.name);
              }
            } else if (item.kind === 'string' && item.type === 'text/plain') {
              // 处理文本
              console.log('Found string item:', item);
              promises.push(new Promise((resolve) => {
                item.getAsString(function (str) {
                  console.log('getAsString callback triggered with:', str);
                  resolve(str || ''); // 如果 str 为空，返回空字符串
                });
              }));
            }
          }
        }

        // 等待所有文本 Promise 完成
        try {
          const allTexts = await Promise.all(promises);
          textContent = allTexts.join('');
          console.log('All texts:', allTexts);
        } catch (error) {
          console.error('Error processing clipboard text:', error);
        }

        // 如果有文本内容，插入到文档中
        if (textContent) {
          console.log('Inserting text:', textContent);
          document.execCommand('insertText', false, textContent);
        }

        // 调试信息
        console.log('Images:', hasImage ? 'Yes' : 'No');
        console.log('Text Content:', textContent);

        // 如果未启用图像和文本消息功能，仅处理文本
        if (!enableImageTextMsg && textContent) {
          console.warn('Image and text message feature is not enabled. Only text will be processed.');
        }
      });
    },
    async newUpdown(files) {
      try {
        const formData = new FormData();
        const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
          fileType: 'png'
        })
        if (res.data.status === 200) {
          formData.append('key', res.data.data.key)
          formData.append('accessKeyId', res.data.data.accessKeyId)
          formData.append('signature', res.data.data.signature)
          formData.append('policy', res.data.data.policy)
          formData.append('file', files)
        }
        const res1 = await this.$axios.post(res.data.data.obsUrl, formData)
        const fileName = this.$fileUtil.getFileName(files.name)
        const fileSize = files.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(files.name)
        const fileKey = res.data.data.key
        this.toMessage.image_key = res.data.data.key
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        await this.$axios
          .post('/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs',

          })
          .then((res1) => {
            if (res1.data.status === 200) {
              console.log(res1.data.data, '文件id')
              this.toMessage.image_path = res1.data.data.path
            }
          })
        // await this.save()
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    handleUpdateKickedLoading(val) {
      this.kickLoading = val
    },
    alertKicked() {
      this.$alert('对话已被他人占用', '提示', {
        confirmButtonText: '确定',
        customClass: 'my-message-box1',
        showClose: false,
        callback: action => {
          // this.$message({
          //   type: 'info',
          //   message: `action: ${action}`
          // });
          if (action === 'confirm') {
            let fromMenu = this.$route.query.fromMenu
            if(fromMenu === '1'){
              // urlPath= '/planGenerate/first' 
              // label= '专家生产'
              this.$router.push({
                path: '/planGenerate/first',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '3'){
              // label= '研发生产'
              // urlPath= '/planGenerate/index' 
              this.$router.push({
                path: '/planGenerate/index',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '2'){
              // label= '任务规划'
              // urlPath= '/planGenerate/taskRd' 
              this.$router.push({
                path: '/planGenerate/taskRd',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '4'){
              // label= '训练与验证'
              // urlPath= '/planGenerate/validation' 
              this.$router.push({
                path: '/planGenerate/validation',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }
          }
        }
      });
    },
    async startPolling() {
      const poll = async () => {
        try {
          const res = await queryChatIsUseing({ scheme_id: this.$route.query.id });
          if (res && res.data.result.is_using === false) {
            console.log('Polling stopped as the condition met.');
            await this.getChatIsUseing()
            this.kickLoading = false
            return; // 停止轮询
          } else {
            await chatDisconnect({ session_id: this.sessionId, scheme_id: this.$route.query.id })
          }
        } catch (error) {
          console.error('Error during polling:', error);
          // 根据需求决定是否停止轮询或继续
        }
        // 在操作完成后，等待2秒再进行下一次轮询
        setTimeout(poll, 4000);
      };

      // 启动第一次轮询
      poll();
    },
    async handleUpdateHasChatingName(val) {
      // this.hasChatingName = val
      await this.getChatIsUseing()
    },
    async handleSSEMessagebyRole(message) {
      console.log('handleSSEMessagebyRole 0', message.role)
      for (const role of Object.keys(messageRoleToHandlersMap)) {
        // 使用 for...of 循环
        if (role === message.role) {
          const handlerName = messageRoleToHandlersMap[role]
          if (this[handlerName]) {
            await this[handlerName](message) // 直接 await，支持异步
          } else {
            console.error(`No handler found for this role: ${role}`)
          }
        }
      }
    },
    async knowledgeSave() {
      if (this.knowledgeTheme.trim() === '') {
        this.$message({
          type: 'info',
          message: '请完善你的知识主题'
        })
      } else if (this.detailContent.text.trim() === '') {
        this.$message({
          type: 'info',
          message: '请完善你的知识内容'
        })
      } else {
        const question = this.knowledgeTheme.trim()
        const answer = this.detailContent.text
        const res = await knowledgeQaSave({ question: question, answer: answer })
        if (res.status === 200 && res.data.result.success) {
          this.$message({
            type: 'success',
            message: '知识沉淀成功啦'
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.result.question
          })
        }
      }
    },
    async getInstance() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      const instance = await getInstanceInfo({ instanceId: this.$route.query.id })
      if (instance.status === 200) {
        this.instanceInfo = instance.data
        console.log('this.instanceInfo', this.instanceInfo)
      }
    },
    getBaiduFn() {
      getBaidu({})
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            const data = res.data.result
            console.log(data, 'baidu1')
            const baiduInfo = this.decryptFn(data)
            console.log(baiduInfo, 'baiduInfo字符串')
            const regex = /{([^}]*)}/g
            const match = baiduInfo.match(regex)
            if (match) {
              const extractedData = match[0]
              const baiduObj = JSON.parse(extractedData)
              this.websocketPara.URI = baiduObj.uri
              this.websocketPara.APPKEY = baiduObj.appkey
              this.websocketPara.DEV_PID = +baiduObj.dev_pid
              this.websocketPara.APPID = +baiduObj.app_id
            }
          }
        })
        .finally(() => {
          this.qaBoxLoading = false
        })
    },
    decryptFn(data) {
      const nKey = CryptoJs.enc.Base64.parse(data.key)
      const iv = CryptoJs.enc.Utf8.parse('\0'.repeat(16))
      const encrypted = CryptoJs.AES.decrypt(data.text, nKey, {
        iv: iv
      })
      return CryptoJs.enc.Utf8.stringify(encrypted)
    },
    startWeb() {
      console.log('----开始监听说话------')
      const that = this
      if (
        ([
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 &&
          !this.historyChat.messages[this.historyChat.messages.length - 1].id) ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false
      } else {
        // 获取麦克风输入流
        navigator.mediaDevices.getUserMedia({ audio: true }).then(function (stream) {
          that.websocket = new WebSocket(
            that.websocketPara.URI + '?sn=sn' + Math.round(Math.random() * 10000),
            'websocket'
          )
          that.websocket.onopen = function () {
            let buffer
            const req = {
              type: 'START',
              data: {
                appid: that.websocketPara.APPID,
                appkey: that.websocketPara.APPKEY,
                dev_pid: that.websocketPara.DEV_PID,
                cuid: userInfo.userId,
                sample: that.websocketPara.sample,
                format: 'pcm'
              }
            }
            that.phoneStatus = 'running'
            const message = JSON.stringify(req)
            console.log('发送开始帧：' + message)
            that.websocket.send(message)
            // 初始化音频上下文
            that.audioContext = new AudioContext()
            // 创建音频输入节点
            const source = that.audioContext.createMediaStreamSource(stream)
            // 创建缓冲区节点
            const scriptProcessor = that.audioContext.createScriptProcessor(
              that.websocketPara.CHUNK_SIZE,
              1,
              1
            )
            source.connect(scriptProcessor)
            scriptProcessor.connect(that.audioContext.destination)
            scriptProcessor.onaudioprocess = async function (event) {
              const audioData = event.inputBuffer.getChannelData(0)
              const sampleRate = that.audioContext?.sampleRate
              const sampleCount = audioData.length
              const sampleLength = Math.floor(
                sampleCount / (sampleRate / that.websocketPara.sample)
              )
              const sampleData = new Float32Array(sampleLength)
              for (let i = 0; i < sampleLength; i++) {
                sampleData[i] = audioData[Math.floor((i * sampleRate) / that.websocketPara.sample)]
              }
              // 将音频数据转换为二进制数据类型
              buffer = new ArrayBuffer(sampleData.length * 2)
              const view = new DataView(buffer)
              for (let i = 0; i < sampleLength; i++) {
                view.setInt16(i * 2, sampleData[i] * 0x7fff, true)
              }
              that.websocket?.send(buffer)
            }
          }
          that.websocket.onmessage = that.onmessage
          that.websocket.onerror = that.onerror
          that.websocket.onclose = that.onclose
        })
      }
    },
    onerror() {
      console.log('WebSocket 连接发生错误')
    },
    onmessage(event) {
      console.log('接收到的message', event.data)
      const webMessage = JSON.parse(event.data)
      if (webMessage.type === 'FIN_TEXT' && webMessage.err_msg === 'OK') {
        // this.currentText = webMessage.result;
        this.toMessage.content = webMessage.result
        this.phoneStatus = 'shibie'
        this.sendMessage()
        this.stopWeb()
      }
    },
    onclose() {
      console.log('WebSocket连接关闭')
    },
    stopWeb() {
      // 关闭 WebSocket 连接
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close()
        this.audioContext = null
      }
    },
    // 更新scheme_detail_name
    async updateSchemeDetailName(newSchemeDetailName) {
      const params = {
        id: this.schemeInfo.id + '',
        display_type: this.displayType,
        name: this.schemeInfo.name,
        scheme_detail_name: newSchemeDetailName,
        description: this.schemeInfo.description,
        agent_scene: this.schemeInfo.agent_scene,
        agent_scene_code: this.schemeInfo.agent_scene_code,
        agent_id: this.schemeInfo.agent_id || '',
        contributors: this.schemeInfo.contributors,
        tag_ids: this.schemeInfo.tag_ids,
        ext_data_info: this.schemeInfo.ext_data_info,
        visibility: this.schemeInfo.visibility,
        share_userids: this.schemeInfo.share_infos
      }
      UpdateScheme(params).then(async (res) => {
        if (res.status === 200 && res.data.code * 1 === 200) {
          this.schemeInfo = { ...this.schemeInfo, display_type: this.displayType }
        }
      })
    },
    // 切换模式
    async changeDisplayType() {
      this.taskGenLoading = true
      const newdisplay_type = this.displayType === 1 ? 2 : 1
      const share_infos =
        this.schemeInfo.share_infos?.map((el) => {
          return el.user_id
        }) || []
      const params = {
        id: this.schemeInfo.id + '',
        display_type: newdisplay_type + '',
        name: this.schemeInfo.name,
        scheme_detail_name: this.schemeInfo.scheme_detail_name || this.schemeInfo.name,
        description: this.schemeInfo.description,
        agent_scene: this.schemeInfo.agent_scene,
        agent_scene_code: this.schemeInfo.agent_scene_code,
        agent_id: this.schemeInfo.agent_id || '',
        contributors: this.schemeInfo.contributors,
        tag_ids: this.schemeInfo.tag_ids,
        ext_data_info: this.schemeInfo.ext_data_info,
        visibility: this.schemeInfo.visibility,
        share_userids: share_infos
      }
      UpdateScheme(params)
        .then(async (res) => {
          this.taskGenLoading = false
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.schemeInfo = { ...this.schemeInfo, display_type: newdisplay_type }
            this.displayType = newdisplay_type
            Bus.$emit('operations-displayType', this.displayType)
          }
        })
        .finally(() => {
          this.taskGenLoading = false
        })
    },
    async schemeDetailById() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.schemeInfo = res.data.result || { ...this.$route.query }
            this.researchEdit(this.schemeInfo)
            // Bus.$emit('operations-research', this.schemeInfo)
            this.knowledgeTheme = this.schemeInfo.scheme_detail_name || this.schemeInfo.name
            this.displayType = res.data.result?.display_type
              ? Number(res.data.result?.display_type)
              : 1
            Bus.$emit('operations-displayType', this.displayType)

            console.log('详情', this.schemeInfo)
            this.eventSource && this.eventSource.close()
            this.eventSource = null
            this.totalWidth = document.getElementById('chatContainer').getBoundingClientRect().width
            this.leftWidth = document.getElementById('left-content').getBoundingClientRect().width
            console.log('宽度', this.rightWidth)
            this.taskStatus = this.$route.query.status
            await this.queryTask()
            document.addEventListener('click', this.hideGuess)
            await this.queryAgentAvator()
            await this.queryPlanDetail()
            await this.handleAbility()
            await this.queryDetail()
            await this.getQuickReply()
            await this.getChatIsUseing()
            console.log('操作系统', window.navigator.userAgent)
            if (window.navigator.userAgent.indexOf('Mac') > -1) {
              this.navType = 'Mac'
            } else {
              this.navType = 'Windows'
            }
          }
        })
        .catch((_err) => {
          // this.$message({
          //   type: 'error',
          //   message: _err.data?.msg || '接口异常!'
          // });
          console.log(_err.data?.msg || '接口异常!')
        })
    },
    async startRecording() {
     if(this.isReadOnly) return
      if (
        ([
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 &&
          !this.historyChat.messages[this.historyChat.messages.length - 1].id) ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false
      } else {
        try {
          this.recorderEx.start()
          this.speakingFlag = 'running'
          this.yuyinText = '正在语音中...'
        } catch (err) {
          console.error('无法获取媒体流:', err)
          this.speakingFlag = 'start'
        }
      }
    },
    stopRecording() {
      this.speakingFlag = 'end'
      this.recorderEx.stop()
      setTimeout(() => {
        const wavBlob = this.recorderEx.getWAVBlob() // blob格式
        console.log('this.audioChunks33', wavBlob)
        this.yuyinText = '正在转换文字中...'
        this.runExcute(wavBlob)
      })
    },
    async runExcute(audioBlob) {
      this.shibieLoading = true
      // this.currentText = '';
      this.toMessage = { content: '', image_key: '', image_path: '' }
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
        : process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
      // const url = 'http://10.20.50.95:80/voice/conversion/text'
      console.log('url', url, audioBlob)
      await this.$axios
        .post(url, audioBlob, {
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          headers: {
            'Content-Type': 'application/octet-stream'
          },
          onDownloadProgress: (event) => {
            const xhr = event.target
            const { responseText } = xhr
            console.log('流信息', responseText)
            let chunk = ''
            let dataArray
            const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2)
            if (lastIndex !== -1) {
              chunk = responseText.slice(0, lastIndex)
              dataArray = chunk.match(/(.*(\n\n\")?\})(\n\n)?/g)
              const lastText = JSON.parse(dataArray[dataArray.length - 2]?.replace('data:', ''))
              console.log('lastTextlastTextlastText', lastText)
              if (lastText) {
                // this.currentText = lastText?.message;
                this.toMessage.content = lastText?.message
                if (this.phoneStatus === 'shibie') {
                  this.sendMessage()
                  this.phoneStatus = 'start'
                  console.log(this.phoneStatus, '状态')
                }
              } else {
                this.phoneStatus = 'start'
              }
            }
          },
          onError: function (error) {
            // 处理流错误
            console.error(error)
            this.speakingFlag = 'start'
            this.shibieLoading = false
            this.phoneStatus = 'start'
          }
        })
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流结束', response)
          this.speakingFlag = 'start'
          this.phoneStatus = 'start'
          this.shibieLoading = false
        })
        .catch((err) => {
          this.loading = false
          console.log('识别接口错误', err)
          this.speakingFlag = 'start'
          this.phoneStatus = 'start'
          this.shibieLoading = false
        })
    },
    clearHistory() {
      console.log('清空聊天记录')
      this.systemMessages = 'clear_history'
      this.qaList = []
      if (this.agentRoleList.length > 1) {
        this.currentRoleInfo = this.agentRoleList[0]
      }
      startClearHistory({ session_id: this.sessionId })
    },
    async handelQuestion() {
      this.qaBoxLoading = true
      this.$nextTick(async () => {
        this.scrollToBottom()
        await queryQuestionList({ session_id: this.sessionId })
          .then((res) => {
            this.qaBoxLoading = false
            if (res.status === 200 && res.data.code === 200) {
              this.qaList = res.data?.result || []
              this.$nextTick(() => {
                this.scrollToBottom()
              })
            }
          })
          .finally(() => {
            this.qaBoxLoading = false
          })
      })
    },
    handleAsk(qa) {
      this.qaList = []
      startConversation({
        messages: qa,
        agent_role_id: this.currentRoleInfo.id,
        session_id: this.sessionId
      })
    },
    handleInput(val) {
      if (val.endsWith('@')) {
        // 输入了 @ 字符，触发选人逻辑
        if (this.agentRoleList.length > 1) {
          this.showSelectVisable = true
        }
      }
    },
    handleSelectRole(role) {
      // 将选中的人员添加到输入框中
      this.currentRoleInfoTemp = role
      // this.currentText = this.currentText.slice(0, -1);
      this.toMessage.content = this.toMessage.content.slice(0, -1)
      console.log('选择角色', role, this.toMessage)
      this.$refs.myChatInputText?.focus()
      this.$nextTick(() => {
        const target = document.getElementById('myInputText')
        this.inputHeight = target.getBoundingClientRect().height
      })
    },
    removeRole() {
      this.currentRoleInfoTemp = {}
      this.currentRoleInfo = this.agentAvatorInfo
      this.$refs.myChatInputText?.focus()
      this.$nextTick(() => {
        const target = document.getElementById('myInputText')
        this.inputHeight = target.getBoundingClientRect().height
      })
    },
    handleChangeRole(row) {
      const lastMess = this.historyChat.messages[this.historyChat.messages.length - 1]
      console.log('角色切换判断', lastMess, this.systemMessages)
      if (
        lastMess.id &&
        this.systemMessages !== 'process running' &&
        this.systemMessages !== 'process stream' &&
        this.systemMessages !== 'process_stream_message' &&
        this.systemMessages !== 'process stream running'
      ) {
        this.currentRoleInfo = row
        startConversationWithRole({ agent_role_id: row.id, session_id: this.sessionId })
        this.chendianVisable2 = false
      } else {
        if (lastMess?.author?.role === 'system') {
          this.currentRoleInfo = row
          startConversationWithRole({ agent_role_id: row.id, session_id: this.sessionId })
          this.chendianVisable2 = false
        } else {
          this.chendianVisable2 = false
          console.log('不发送角色切换')
        }
      }
    },
    changeDagang() {
      this.dagangFlag = !this.dagangFlag
      if (!this.dagangFlag) {
        const nodeMarkmap = document.getElementById('markmap')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
      } else {
        const transformer = new Transformer()
        const { root } = transformer.transform(this.detailContent.text)
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root)
        })
      }
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showSikao()
      } else if (command === 'youhua') {
        this.showYouhua()
      } else if (command === 'zhishi') {
        // this.saveKnowladge()
      } else {
        this.copyText()
      }
    },
    showYouhua() {
      this.youhuaVisable = true
    },
    closeYouhuaEdit() {
      this.youhuaVisable = false
      this.queryPlanDetail()
    },
    // 方案优化信号推送
    emitOptimize(val) {
      startSchemeOptimize({ scheme_optimize_ability_id: val, session_id: this.sessionId })
    },
    copyText() {
      // 获取需要复制的文本
      const text = this.detailContent.text
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功！'
          })
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          })
        })
    },
    handleEmitSQL() {
      startSqlGenerate({ session_id: this.sessionId })
    },
    // 搜索
    chendianSearchFun(val) {
      if (val) {
        const temp = this.globalChendianList.filter((item) => item.question.indexOf(val) > -1)
        this.chendianList = temp
      } else {
        this.chendianList = [...this.globalChendianList]
      }
    },
    showRolePop() {
      this.agentRoleFlag = true
      const target = document.getElementById('myInputText')
      // console.log('输入框高度', target.getBoundingClientRect().height);
      this.inputHeight = target.getBoundingClientRect().height
    },
    // 获取参考已沉淀方案列表
    chendianFun() {
      getSuggestionList().then(async (res) => {
        if (res.status === 200 && res.data.success) {
          console.log('沉淀方案列表', res.data.data)
          this.chendianList = res.data.data || []
          this.globalChendianList = res.data.data || []
          this.chendianVisable = true
          const target = document.getElementById('myInputText')
          // console.log('输入框高度', target.getBoundingClientRect().height);
          this.inputHeight = target.getBoundingClientRect().height
        } else {
          this.$message({
            type: 'error',
            message: res.data?.message || '接口异常!'
          })
        }
      })
    },

    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    fangda2(e) {
      if (this.isEdit) {
        const selection = this.$refs.MyEditor.$refs.editorFin?.getCurrentSelectedStr()
        console.log('触发绑定', selection)
        if (selection) {
          this.writeText = selection
          this.insertWriteFlag = true
          this.replaceWriteFlag = false
        } else {
          this.writeText = selection
          this.insertWriteFlag = false
          this.replaceWriteFlag = true
        }
      }
    },
    async handleAbility() {
      queryAbilityList({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.data) {
            this.abilityList = res.data || []
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示')
      this.processVisable = true
    },
    closeSikaoRizhi() {
      this.processVisable = false
    },
    // 参考提示词快捷回复语查询接口
    getQuickReply() {
      queryQuickReply({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.guessList = res.data.result ? JSON.parse(res.data.result) : []
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    // 查询当前是否正在跟其他用户对话
    async getChatIsUseing() {
      queryChatIsUseing({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('判断是否可用', res.data)
          if (res.data.result.is_using) {
            this.hasChatingName = res.data.result?.using_user?.nickName
          } else {
            this.hasChatingName = ''
            this.sessionId = res.data.result.session_id || '1'
            if (
              this.historyChat.messages.length > 0 &&
              this.historyChat.messages[this.historyChat.messages.length - 1].id
            ) {
              this.systemMessages = 'process waiting'
            }
            const url = process.env.VUE_APP_PLAN_API.startsWith('/')
              ? window.location.origin + process.env.VUE_APP_PLAN_API + '/stream'
              : process.env.VUE_APP_PLAN_API + '/stream'
            console.log('url', url)
            const userInfo = sessionStorage.getItem('USER_INFO')
              ? JSON.parse(sessionStorage.getItem('USER_INFO'))
              : {}

            this.eventSource = new EventSource(
              url +
                '?scheme_id=' +
                this.$route.query.id +
                '&user_id=' +
                (userInfo.userId || 'str') +
                '&work_space_id=' +
                (this.$route.query.workspaceId + '' || '1') +
                '&tenant_id=' +
                (userInfo.tenantId || 'str') +
                '&session_id=' +
                (res.data.result.session_id || '1')
            )

            this.eventSource.addEventListener('open', () => {
              console.log('连接已建立')
              this.agentError = false
            })

            this.eventSource.addEventListener('system', async (event) => {
              console.log('event.data 001', event.data)
              if (event.data === 'occupied') {
                this.eventSource.close()
                this.isOccupied = true
                this.alertKicked()
                // await queryChatIsUseing({ scheme_id: this.$route.query.id }).\
                // this.getChatIsUseing()
              }
            })

            this.eventSource.addEventListener('messages', (event) => {
              console.log('messages收到消息：', event.data)
              this.handleMessage(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('stream_message收到消息：', event.data)
              this.handleMessageStream(JSON.parse(event.data))
              if (message.action === 'end') {
                if (this.phoneFlag) {
                  this.startWeb()
                }
              }
            })
            this.eventSource.addEventListener('system_message', (event) => {
              // console.log('system_message', JSON.parse(event.data));
              this.handleSystemMessage(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('task_message', (event) => {
              console.log('task_messages', event.data)
              const message = JSON.parse(event.data)
              this.taskGeneratorStatus = message.data
              if (message.data !== 'task generating') {
                this.queryTask()
              }
              if (message.data === 'task generating completed') {
                // this.$refs.zhushouChatTreeRef.queryTableData()
                // this.$refs.zhushouChatTreeRef.setTaskLoading(false)
                this.$refs.taskNewRef.setTaskLoading(false)
                this.$refs.taskNewRef.queryTableData()
                this.taskStatus = 1
                this.treeStatus = 2
                this.taskGenLoading = false
              }
              if (message.data === 'task generating error') {
                this.taskGenLoading = false
                this.$refs.taskNewRef.queryTableData()
                this.$refs.taskNewRef.setTaskLoading(false)
                // this.$refs.zhushouChatTreeRef.setTaskLoading(false)
                this.treeStatus = 2
                this.$message({
                  type: 'error',
                  message: this.taskGenType === 'override' ? '任务覆盖失败' : '任务追加失败!'
                })
              }
            })
            this.eventSource.addEventListener('scheme_stream_message', (event) => {
              console.log('scheme_stream_message', event.data)
              this.handleTaskDetail(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('process_stream_message', (event) => {
              console.log('process_stream_message', event.data)
              this.systemMessages = 'process_stream_message'
              this.handleProcessDetail(JSON.parse(event.data))
            })
            this.eventSource.addEventListener('decision_tree_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received decision_tree_stream_message:', message)
              if (message.action !== 'end') {
                this.treeStatus = 0
              }
              this.handleProcessTreeDetail(message)
            })
            // 代码生成 BI 能力代码
            this.eventSource.addEventListener('ability_generate_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received ability_generate_stream_message:', message)
              // bi报表loading状态
              if (message.action !== 'end') {
                this.treeStatus = 0
              }
              this.handleAbilityDetail(message)
            })
            // BI报表、能力代码生成，生成过程流式过程
            this.eventSource.addEventListener('ability_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received ability_process_stream_message:', message)
              this.handleProcessAbilityData(message)
            })

            this.eventSource.addEventListener('decision_tree_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received decision_tree_process_stream_message:', message)
              this.handleProcessTreeData(message)
            })
            this.eventSource.addEventListener('scheme_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received scheme_process_stream_message:', message)
              this.handleProcessSchemeData(message)
            })
            this.eventSource.addEventListener('sql_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received sql_stream_message:', message)
              this.handleSQLData(message)
            })
            this.eventSource.addEventListener('align_data_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received align_data_stream_message:', message)
              console.log('有这个类型的消息吗？------')
            })
            this.eventSource.addEventListener('align_data_process_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received align_data_process_stream_message:', message)
              this.handleProcessAbilityData(message)
            })
            this.eventSource.addEventListener('scheme_optimize_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received scheme_optimize_stream_message:', message)
              this.handleOptimizeData(message)
            })
            // 代码分析流
            this.eventSource.addEventListener('code_analysis_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('代码分析-Received code_analysis_stream_message:', message)
              this.handleCodeAnalysisData(message)
            })
            // 代码分析过程流
            this.eventSource.addEventListener('code_analysis_process_stream_message', (event) => {
              // console.log('代码分析过程流', event.data);
              const message = JSON.parse(event.data)
              console.log('Received code_analysis_process_stream_message:', message)
              if (message.active === 'start') {
                this.codeAnalysisDataStatus = '1'
              }
              this.handleCodeAnalysisProcessData(message)
            })
            // 规则生成
            this.eventSource.addEventListener('rule_generate_stream_message', (event) => {
              const message = JSON.parse(event.data)
              console.log('Received rule_generate_stream_message:', message)
              // bi报表loading状态
              if (message.action !== 'end') {
                this.treeStatus = 0
              }
              this.handleRuleDetail(message)
            })
            // 规则生成日志过程流式过程
            this.eventSource.addEventListener(
              'rule_generate_process_stream_message',
              (event) => {
                const message = JSON.parse(event.data)
                console.log('Received rule_generate_process_stream_message:', message)
                this.handleProcessRuleData(message)
              },
              false
            )

            this.eventSource.addEventListener('error', () => {
              console.log('连接出错')
              this.agentError = true
              // this.treeStatus = 3;
              // this.abilityDataProcessStatus = 3;
              // 回到第一步
              this.stepIndex = 0
              this.systemMessages = ''
              this.changeViews(0)
            })
            this.eventSource.addEventListener('disconnect', () => {
              console.log('连接断开')
              this.agentError = true
              this.systemMessages = ''
            })

            this.eventSource.addEventListener('common_stream_message', (event) => {
              console.log('common_stream_message 0', JSON.parse(event.data))
              this.handleSSEMessagebyRole(JSON.parse(event.data))
              // this.handleSystemMessage(JSON.parse(event.data))
            })
          }
        } else {
          this.hasChatingName = ''
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleSystemMessage(message) {
      this.systemMessages = message.data
      if (message.data === 'scheme generating error') {
        this.taskStatusText = 'scheme generating error'
        this.treeStatus = 2
      }
      // 清空聊天记录成功
      if (message.data === 'clear history completed' || message.data === 'clear history error') {
        this.clearChat(message.data === 'clear history completed')
      }
      if (message.data === 'illegal agent_id') {
        this.agentError = true
        this.queryTask()
      } else {
        this.agentError = false
        this.queryTask()
      }
      if (message.data === 'process running') {
        console.log('----正在会话中')
      }
      if (
        message.data === 'process completed' ||
        message.data === 'process error' ||
        message.data === 'scheme generating error'
      ) {
        console.log('结束----')
        this.queryTask()
      }
      if (message.data === '403 error') {
        this.refresh()
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
        this.qaList = []
        startConversation({
          messages: lastMessage.content.chat_message.content,
          agent_role_id: this.agentAvatorInfo.id,
          session_id: this.sessionId
        })
      }
      if (message.data === 'process completed') {
        this.taskStatus = 1
        this.queryTask()
      }
      if (message.data === 'process waiting') {
        this.scrollToBottom()
      }
      if (message.data === 'scheme generating completed') {
        this.treeStatus = 2
      }
      if (message.data === 'decision tree generating completed') {
        this.treeStatus = 2
      }
      if (message.data === 'decision tree generating') {
        this.treeStatus = 1
        console.log('this.treeStatus思维树', this.treeStatus)
      }
      if (message.data === 'decision tree generating error') {
        this.treeStatus = 3
      }
      if (message.data === 'ability generating completed') {
        this.treeStatus = 2
        console.log('ability generating completed BI-CODE', message.data)
      }
      if (message.data === 'ability generating') {
        this.treeStatus = 1
      }
      if (message.data === 'ability generating error') {
        this.treeStatus = 3
        console.log('ability generating error BI-CODE', message.data)
      }
      if (message.data === 'append_history completed') {
        console.log('append_history completed', message.data)
        this.queryDetail()
      }
      if (message.data === 'append_history error') {
        console.log('append_history error', '增加会话历史记录失败')
      }
      if (message.data === 'align_data generating completed') {
        this.treeStatus = 2
        console.log('align_data generating completed数据对齐完成 ======', message.data)
      }
      if (message.data === 'align_data generating') {
        this.treeStatus = 1
      }
      if (message.data === 'align_data generating error') {
        this.treeStatus = 3
      }
      if (message.data === 'sql generating completed') {
        this.sqlStatus = 2
        console.log('sql generating completed数据对齐完成 ======', message.data)
      }
      if (message.data === 'sql generating') {
        this.sqlStatus = 1
      }
      if (message.data === 'sql generating error') {
        this.sqlStatus = 3
      }

      if (message.data === 'scheme optimize completed') {
        this.planStatus = 2
        console.log('scheme optimize completed方案优化完成 ======', message.data)
      }
      if (message.data === 'scheme optimize generating') {
        this.planStatus = 1
      }
      if (message.data === 'scheme optimize generating error') {
        this.planStatus = 3
      }
      if (message.data === 'code_analysis generating error') {
        console.log('code_analysis generating error')
        this.codeAnalysisDataStatus = '3'
      }
      if (message.data === 'code_analysis generating completed') {
        console.log('代码分析完成')
        this.codeAnalysisDataStatus = '2'
      }
      if (message.data === 'code_analysis generating') {
        console.log('代码分析中。。。')
      }

      if (message.data === 'rule generating completed') {
        this.treeStatus = 2
        console.log('rule generating completed规则生成完成 ======', message.data)
      }
      if (message.data === 'rule generating') {
        this.treeStatus = 1
      }
      if (message.data === 'rule generating error') {
        this.treeStatus = 3
      }
    },
    // 查询场景模版基本信息
    async queryAgentAvator() {
      queryAgentInfoDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        // console.log('头像', res);
        if (res.status === 200 && res.data) {
          // console.log('场景头像',res.data);
          this.agentRoleList = res.data || []
          if (res.data && res.data.length) {
            this.agentAvatorInfo = {
              name: res.data?.[0]?.name,
              alias: res.data?.[0]?.alias,
              icon: res.data?.[0]?.icon,
              id: res.data?.[0]?.id
            }
            this.currentRoleInfo = res.data?.[0] || {}
            const temp = {}
            const temp2 = []
            res.data.map((item) => {
              temp[item.id] = {
                name: item.name,
                alias: item.alias,
                icon: item.icon,
                id: item.id
              }
              temp2.push('@' + item.name + ' ')
            })
            this.agentAvatorInfoMap = temp
            this.agentAvatorInfoList = temp2
          }
        }
      })
    },
    toggleGuess() {
      this.showGuess = !this.showGuess
      const target = document.getElementById('myInputText')
      // console.log('输入框高度', target.getBoundingClientRect().height);
      this.inputHeight = target.getBoundingClientRect().height
    },
    hideGuess(e) {
      if (!this.$refs.clearChat?.contains(e.target)) {
        this.showGuess = false
        this.showSelectVisable = false
      }
    },
    getHours(v) {
      const date = new Date(v)
      const hours = date.getHours()
      const minutes = date.getMinutes()
      return `${hours}:${minutes < 10 ? '0' + minutes : minutes}`
    },
    refresh() {
      this.eventSource && this.eventSource.close()
      this.getChatIsUseing()
    },
    changeViews(val) {
      this.panZoomRef = null
      // this.currentText = '';
      this.toMessage = { content: '', image_key: '', image_path: '' }
      this.speakingFlag = 'start'
      // 向外层页面传递消息
      console.log('当前在第几步', val)
      this.codeAnalysisDataStatus = ''
      window.parent.postMessage(JSON.stringify({ stepIndex: val }), '*')
      this.activeStep = val
    },
    changeViewsCallZhuge(val) {
      this.changeViews(val)
    },
    handleUpdateStep(v) {
      this.treeData = ''
      this.treeStatus = -1
      this.codeAnalysisData = ''
      this.codeAnalysisDataStatus = ''
      this.changeViews(v)
      if (v === 0) {
        this.leftWidth = '40%'
        this.rightWidth = '60%'
        this.queryPlanDetail()
      } else if (v === 1) {
        this.leftWidth = '40%'
        this.rightWidth = '60%'
      }
      this.codeAnalysisDataStatus = 0
    },
    async handleDetailSave() {
      const { id, ...rest } = this.detailContent
      const res = await PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id })
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败')
        return
      }
      this.$message.success('编辑成功')
      this.isEdit = false
      const content = document.getElementById('detail-content')
      content.removeEventListener('mousedown', (e) => {})
      content.removeEventListener('mouseup', (e) => {})
      this.insertWriteFlag = true
      this.replaceWriteFlag = true
      this.queryTask()
      this.queryPlanDetail()
    },
    handleUpdateContent(val) {
      this.detailContent.text = val
    },
    handleDetailSaveClose() {
      this.isEdit = false
      this.insertWriteFlag = true
      this.replaceWriteFlag = true
      this.detailContent.text = this.hisDetail
      const content = document.getElementById('detail-content')
      content.removeEventListener('mousedown', (e) => {})
      content.removeEventListener('mouseup', (e) => {})

      if (this.dagangFlag) {
        const nodeMarkmap = document.getElementById('markmap')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
        const transformer = new Transformer()
        const { root } = transformer.transform(this.hisDetail)
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root)
        })
      }
    },
    // 知识保存接口
    saveKnowladge() {
      SchemeSaveKnow({
        scheme_id: this.$route.query.id,
        scheme_name: this.schemeInfo.name,
        scheme_content: this.detailContent.text
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$message({
            type: 'success',
            message: '知识保存成功！'
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    goToDetail(task) {
      task.adjust_url && window.open(task.adjust_url)
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    getWsID() {
      let workspaceId = ''
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?')
          const params = Object.fromEntries(new URLSearchParams(query))
          workspaceId = params.workspaceId
        } catch (error) {
          console.log('error', error)
        }
      }
      return workspaceId
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 100
    },
    handleTaskDetail(message) {
      if (message.action === 'start') {
        this.detailContent.text = message.data
      } else {
        this.detailContent.text = this.detailContent.text + message.data
      }
      if (message.action === 'end') {
        this.taskStatus = 1
      }
    },
    handleAlignProcess(message) {
      if (message.action === 'start') {
        this.sqlData = message.data
      } else {
        this.sqlData = this.sqlData + message.data
      }
      if (message.action === 'end') {
        console.log('数据对齐流信息结束')
        this.treeStatus = 2
      }
    },
    handleOptimizeData(message) {
      if (message.action === 'start') {
        this.optimizeData = message.data
      } else {
        this.optimizeData = this.optimizeData + message.data
      }
      if (message.action === 'end') {
        console.log('方案优化流信息结束')
        this.treeStatus = 2
      }
    },
    // 代码分析过程消息处理
    handleCodeAnalysisProcessData(message) {
      if (message.action === 'start') {
        this.codeAnalysisProcessData = message.data
        this.codeAnalysisProcessDataStatus = '1'
      } else {
        this.codeAnalysisProcessData = this.codeAnalysisProcessData + message.data
        this.codeAnalysisProcessDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======', this.codeAnalysisProcessData)
        this.codeAnalysisProcessDataStatus = '2'
      }
    },
    // 代码分析消息处理
    handleCodeAnalysisData(message) {
      if (message.action === 'start') {
        this.codeAnalysisData = message.data
        this.codeAnalysisDataStatus = '1'
      } else {
        this.codeAnalysisData = this.codeAnalysisData + message.data
        this.codeAnalysisDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======', this.codeAnalysisData)
        this.codeAnalysisDataStatus = '2'
      }
    },
    handleSQLData(message) {
      if (message.action === 'start') {
        this.sqlData = message.data
      } else {
        this.sqlData = this.sqlData + message.data
      }
      if (message.action === 'end') {
        console.log('sql流信息结束')
        this.treeStatus = 2
      }
    },
    handleProcessTreeDetail(message) {
      if (message.action === 'start') {
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      if (message.action === 'end') {
        this.treeStatus = 2
      }
    },
    handleAbilityDetail(message) {
      if (message.action === 'start') {
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      // if (message.action === 'end') {
      //   this.treeStatus = 2
      // }
    },
    handleRuleDetail(message) {
      if (message.action === 'start') {
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      // if (message.action === 'end') {
      //   this.treeStatus = 2
      // }
    },
    handleProcessSchemeData(message) {
      if (message.action === 'start') {
        this.optionDataProcess = message.data
      } else {
        this.optionDataProcess = this.optionDataProcess + message.data
      }
      if (message.action === 'end') {
        console.log('方案生成过程的数据======', this.optionDataProcess)
      }
    },
    handleProcessAbilityData(message) {
      if (message.action === 'start') {
        this.abilityDataProcess = message.data
        this.abilityDataProcessStatus = '1'
      } else {
        this.abilityDataProcess = this.abilityDataProcess + message.data
        this.abilityDataProcessStatus = '1'
      }
      if (message.action === 'end') {
        console.log('能力生成过程的数据======', this.abilityDataProcess)
        this.abilityDataProcessStatus = '2'
      }
    },
    handleProcessRuleData(message) {
      if (message.action === 'start') {
        this.abilityDataProcess = message.data
        this.abilityDataProcessStatus = '1'
      } else {
        this.abilityDataProcess = this.abilityDataProcess + message.data
        this.abilityDataProcessStatus = '1'
      }
      if (message.action === 'end') {
        console.log('规则生成过程的数据======', this.abilityDataProcess)
        this.abilityDataProcessStatus = '2'
      }
    },
    handleProcessTreeData(message) {
      if (message.action === 'start') {
        this.treeDataProcess = message.data
        this.treeDataProcessNodes = [{ node_id: '1', data: message.data || '' }]
      } else {
        // 如果有node_id代表我分组任务流程过程
        if (message.node_id) {
          const filtersData = this.treeDataProcessNodes.filter(
            (item) => item.node_id === message.node_id
          )
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === message.node_id) {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            this.treeDataProcessNodes = temp
          } else {
            this.treeDataProcessNodes.push({ node_id: message.node_id, data: message.data })
          }
          // console.log('分组数据', this.treeDataProcessNodes);

          const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
          // console.log('拼接后的', conData);
          this.treeDataProcess = conData
        } else {
          const filtersData = this.treeDataProcessNodes.filter((item) => item.node_id === '1')
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === '1') {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            this.treeDataProcessNodes = temp
            const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
            // console.log('拼接后的', conData);
            // console.log('分组数据', this.treeDataProcessNodes);
            this.treeDataProcess = conData
          } else {
            this.treeDataProcess = this.treeDataProcess + message.data
          }
        }
      }
      if (message.action === 'end') {
        console.log('思维树生成过程的数据======', this.treeDataProcess)
      }
    },
    handleProcessDetail(message) {
      if (message.action === 'start') {
        console.log('思考结束开始')
        this.processRunningContent = message.data
        this.processContent.text = message.data
      } else {
        this.processRunningContent = this.processRunningContent + message.data
        this.processContent.text = this.processContent.text + message.data
      }
      if (message.action === 'end') {
        console.log('思考结束s ')
        this.processRunningContent = this.processRunningContent + message.data
        this.processContent.text = this.processContent.text + message.data
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
    stopThink() {
      startStopThinking({ session_id: this.sessionId })
      this.refresh()
      this.systemMessages = ''
      const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
      lastMessage.sub_content = this.processRunningContent
      lastMessage.id = dayjs().format('MMDDHHmm')
      this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
      console.log('当前对话结束后将思考过程拼接到消息中', lastMessage)
      this.phoneStatus = 'start'
      this.stopWeb()
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      // this.queryDetail();
    },
    // 能力代码终止生成
    stopAbilityGen() {
      startStopThinking({ session_id: this.sessionId })
      this.codeAnalysisDataStatus = ''
      this.abilityDataProcessStatus = ''
      this.refresh()
    },
    async queryDetail() {
      SchemeConversationDetail({ conversation_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          // const temp = res.data.result;
          // const lastMessage = temp.messages[temp.messages.length - 1];
          // lastMessage.content.parts = this.testdata;
          // temp.messages[temp.messages.length - 1] = lastMessage
          this.historyChat = res.data.result || {
            messages: []
          }
          console.log('聊天详情内容-', this.historyChat)
          if (res.data.result.messages?.length) {
            this.systemMessages = 'process waiting'
          } else {
            console.log('this.greets', this.greets)
            await this.queryGreets()
          }
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    generTask(type) {
      startTaskGenerate({ task_button_type: type, session_id: this.sessionId })
      this.taskGenType = type
      this.taskGenLoading = true
    },
    // 回车发送消息
    carriageReturn(event) {
      // const e = window.event || arguments[0];
      // console.log('event', event)
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault()
          this.$nextTick(() => {
            if (this.toMessage.content) {
              this.sendMessage()
            }
          })
        } else {
          if (event.ctrlKey || event.metaKey) {
            this.toMessage.content += '\n'
            const target = document.getElementById('myInputText')
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              })
            }
          } else {
            event.preventDefault()
          }
        }
      }
      // 英文下｜中文下： 13 Enter Enter
      // 中文下有文字没进入输入框情况是：299 Enter Enter
      // if (e.key === 'Enter' && e.code === 'Enter' && e.keyCode === 13) {
      //   // console.log('this.text', this.currentText);
      //   this.$nextTick(() => {
      //     if (this.currentText) {
      //       this.sendMessage();
      //     }
      //   });
      // }
    },

    handleMessage(message) {
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {}
      this.historyChat.messages.push({
        author: { role: message.role },
        auto: message.role === 'auto' || message.data === '请您发送反馈信息',
        content: {
          // parts: message?.content || message?.data,
          chat_message_type:
            message?.image_key !== undefined && message?.image_key !== '' ? 'img_text' : 'text',
          chat_message: {
            content: message?.content || message?.data,
            image_path: message?.image_path,
            image_key: message?.image_key
          },
          nickName: message.role === 'user_proxy' ? userInfo.nickName : '',
          username: message.role === 'user_proxy' ? userInfo.username : ''
        },
        agent_role_id: this.currentRoleInfo?.id,
        create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
      })
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    handleMessageStream(message) {
      console.log('会话消息', message)
      requestIdleCallback(()=>{
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {}
      if (message.action === 'start') {
        this.systemMessages = 'process stream'
        this.historyChat.messages.push({
          author: { role: message.role },
          content: {
            chat_message_type: 'text',
            chat_message: {
              content: message?.content || message?.data
            },
            nickName: message.role === 'user_proxy' ? userInfo.nickName : '智能伙伴',
            username: message.role === 'user_proxy' ? userInfo.username : ''
          },
          agent_role_id: this.currentRoleInfo?.id,
          create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } else {
        this.systemMessages = 'process stream running'
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
        lastMessage.content.chat_message.content =
          lastMessage.content.chat_message.content + message.data
        // lastMessage.content.parts = lastMessage.content.parts + message.data;
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
        // console.log('最后拼接的', lastMessage);
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
      if (message.action === 'end') {
        this.systemMessages = ''
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
        lastMessage.sub_content = this.processRunningContent
        lastMessage.id = dayjs().format('MMDDHHmm')
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
        console.log('当前对话结束后将思考过程拼接到消息中', lastMessage)
        // this.qaBoxLoading = true;
        // this.$nextTick(() => {
        //   this.scrollToBottom();
        //   // 查询猜你想问
        //   this.handelQuestion();
        // })
        this.$nextTick(async () => {
          this.scrollToBottom()
          // 查询猜你想问，暂时先屏蔽
          // this.handelQuestion();
          // 循环模式，直到speaker_selection返回的角色type=user的时候，停止循环
          this.ansBoxLoading = true
          let roleId = ''
          console.log('开启下一次的轮训，直到type==user')
          await queryAnsRole({ session_id: this.sessionId, scheme_id: this.$route.query.id })
            .then((res) => {
              this.ansBoxLoading = false
              console.log('查询角色的结果', res)
              if (res.status === 200 && res.data.code === 200 && res.data.result?.id) {
                roleId = res.data.result?.id || ''
                if (res.data.result.type !== 'user') {
                  this.currentRoleInfo = this.agentAvatorInfoMap[res.data.result?.id]
                  startConversation({
                    messages: '',
                    agent_role_id: roleId,
                    session_id: this.sessionId
                  })
                } else {
                  this.qaBoxLoading = true
                  this.handelQuestion()
                }
              } else {
                this.currentRoleInfo = this.agentAvatorInfo
              }
            })
            .finally(() => {
              this.ansBoxLoading = false
            })
        })
      }
      })
    },
    autoFeedback() {
      this.qaList = []
      startConversation({
        messages: '',
        agent_role_id: this.agentAvatorInfo.id,
        session_id: this.sessionId
      })
    },
    overChat() {
      startSchemeGenerate({ session_id: this.sessionId })
    },
    handleDataAlign(params) {
      console.log('数据对齐参数', params)
      startAlignDataGenerate({ ...params, session_id: this.sessionId })
    },
    handleCodeGenerate(val) {
      startAbilityGenerate({ ability_type: val, session_id: this.sessionId })
    },
    handleRuleGenerate(val) {
      startRuleGenerate({ ability_type: val, session_id: this.sessionId })
    },
    // 代码分析
    handleUpdateCodeAnalysis() {
      console.log('触发代码分析')
      startCodeAnalysis({ session_id: this.sessionId })
    },
    handleGenerate() {
      console.log('场景类型---', this.schemeInfo.agent_scene_code)
      if (
        [
          'device_ops_assistant_scene',
          'device_ops_assistant_scene-v1',
          'custom_cognition_assistant_scene',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'intelligent_conversation_scene',
          'rule_generation_scene',
          'sop_scene'
        ].indexOf(this.schemeInfo.agent_scene_code) > -1
      ) {
        startDecisionTreeGenerateRequest({ session_id: this.sessionId })
      } else {
        startTaskGenerate({ task_button_type: 'override', session_id: this.sessionId })
        this.taskGenType = 'override'
        this.treeStatus = 1
      }
    },
    handleGussText(text) {
      console.log('发送参考提示词消息', text)
      this.currentRoleInfo = this.agentAvatorInfo
      this.qaList = []
      startConversation({
        messages: text,
        agent_role_id: this.agentAvatorInfo.id,
        session_id: this.sessionId
      })
    },
    // 点击知识沉淀提供的方案
    handleChendianText(data) {
      this.chendianVisable = false
      this.planSearchFlag = false
      // this.$refs.chendianfanganRef.hide();
      startAppendHistory({
        qa: data,
        agent_role_id: this.agentAvatorInfo.id,
        session_id: this.sessionId
      })
    },
    async sendMessage() {
      // 将消息发送到服务器
      // console.log('发送消息', this.currentText);
      console.log('发送消息', this.toMessage)
      this.qaList = []
      let roleId = ''
      if (this.currentRoleInfoTemp.id) {
        this.currentRoleInfo = this.currentRoleInfoTemp
        console.log('使用角色', this.currentRoleInfoTemp)
        roleId = this.currentRoleInfoTemp.id
      } else {
        if (this.agentRoleList.length > 1) {
          this.ansBoxLoading = true
          await queryAnsRole({
            session_id: this.sessionId,
            scheme_id: this.$route.query.id,
            message: this.toMessage.content
          })
            .then((res) => {
              this.ansBoxLoading = false
              console.log('查询角色的结果', res)
              if (
                res.status === 200 &&
                res.data.code === 200 &&
                res.data.result?.id &&
                res.data.result?.id !== 'user'
              ) {
                roleId = res.data.result?.id || ''
                this.currentRoleInfo = this.agentAvatorInfoMap[res.data.result?.id]
              } else {
                this.currentRoleInfo = this.agentAvatorInfo
                roleId = this.agentAvatorInfo.id
              }
            })
            .finally(() => {
              this.ansBoxLoading = false
            })
        } else {
          this.currentRoleInfo = this.agentAvatorInfo
          roleId = this.agentAvatorInfo.id
        }
      }
      startConversation({
        // messages: this.currentText,
        messages: this.toMessage,
        agent_role_id: this.currentRoleInfo?.id,
        session_id: this.sessionId
      })
      // this.currentText = '';
      this.toMessage = { content: '', image_key: '', image_path: '' }
      this.speakingFlag = 'start'
      this.yuyinText = ''
      this.taskStatusText = ''
      if (this.phoneFlag) {
        this.phoneStatus = 'shibie'
      }

      this.currentRoleInfoTemp = {}
    },
    uploadScriptCallback(file, fileList) {
      // console.log('上传成功', fileList);
      this.formData.labelFile = fileList[0].fileId
    },
    uploadScriptStatusCb(status) {
      this.upLoadFileFlag = status
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = '40%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    closePhone() {
      this.phoneFlag = false
      this.leftWidth = '40%'
      this.phoneStatus = 'start'
      this.stopThink()
      this.stopWeb()
    },
    realTime() {
      if(this.isReadOnly) return
      this.phoneFlag = true
      this.leftWidth = '100%'
      this.startWeb()
    },

    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = '40%'
        this.rightWidth = '60%'
      }
    },
    closechangeThinkWrap() {
      this.thinkFlag = !this.thinkFlag
      this.thinkFullFlag = false
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 550px)'
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 220px)'
      }
    },
    // 显示思考过程
    changeThinkWrap(data, id) {
      if(this.isReadOnly) return
      console.log('显示思考过程', data, this.processRunningContent)
      this.thinkFlag = !this.thinkFlag
      this.thinkFullFlag = false
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 550px)'
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 220px)'
      }
      if (data) {
        this.processContent.text = data
      } else {
        if (data === undefined) {
          console.log('没有内容', data, id)
          if (id) {
            this.processContent.text = ''
          } else {
            this.processContent.text = this.processRunningContent || ''
          }
        } else {
          if (id === 'running') {
            this.processContent.text = ''
          } else {
            this.processContent.text = this.processRunningContent || ''
          }
        }
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag
    },
    queryTask() {
      SchemeTasks({ scheme_id: this.$route.query.id, order: 'updated' }).then((res) => {
        // console.log(res, '000');
        if (res.status === 200 && res.data.code === 200) {
          this.taskList = res.data.result.items
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    saveFangan() {
      console.log('修改的值', this.contentEditor.getValue())
      if (this.hasChatingName === '') {
        this.detailContent.text = this.contentEditor.getValue()
        const { id, ...rest } = this.detailContent
        PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id })
        // this.handleDetailSave()
      }
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        // console.log(res, '000');
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result
          // this.publishStatus = res.data.result?.publish_ability;
          // this.detailContent.text = '```mermaid\ngraph LR\nA[变压器运维方案]\nB[日常维护]\nC[定期检修]\nD[长期检修]\nE[变压器故障]\nF[常见故障]\nG[故障诊断]\nH[故障解决]\nA-->B\nB-->B1(检测冷却设备)\nB-->B2(检查油位)\nB-->B3(检查温度)\nA-->C\nC-->C1(3个月-检查油样)\nC-->C2(6个月或1年-检验绝缘电阻)\nC-->C3(1到2年-进行大修)\nA-->D\nD-->D1(长期维护节点-沟通制造商)\nA-->E\nE-->F\nF-->F1(变压器热故障)\nF-->F2(绝缘破损)\nF-->F3(电极磨损)\nF-->F4(机械故障)\nF-->F5(油位故障)\nF-->F6(油质故障)\nF-->F7(电磁故障)\nE-->G\nG-->G1(观察法)\nG-->G2(试验法)\nG-->G3(分析法)\nG-->G4(维护记录法)\nG-->G5(无损检测)\nG-->G6(红外热像技术)\nG-->G7(振动检测)\nE-->H\nH-->H1(调整变压器油位)\nH-->H2(修复冷却系统)\nH-->H3(修复绝缘破损)\nH-->H4(修理机械故障)\nH-->H5(更换电极部件)\nH-->H6(红外热像处理)\nH-->H7(无损检测和振动检测)\n```';
          if (res.data.result?.text) {
            this.taskStatus = 1
            if (this.dagangFlag) {
              const nodeMarkmap = document.getElementById('markmap')
              if (nodeMarkmap) {
                nodeMarkmap.innerHTML = ''
              }
              const transformer = new Transformer()
              const { root } = transformer.transform(res.data.result?.text)
              this.$nextTick(() => {
                Markmap.create('#markmap', null, root)
              })
            }
          }
          if (res.data.result?.sub_content) {
            this.optionDataProcess = res.data.result?.sub_content
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    async queryGreets() {
      queryGreeting({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code * 1 === 200) {
          this.greets = res.data.result
          if (!this.historyChat.messages.length) {
            this.handleMessage({
              role: 'auto',
              data: this.greets || res.data.result || '请问有什么我可以帮您？'
            })
          }
        }
      })
    },
    // 清空聊天记录
    clearChat(type) {
      if (type) {
        queryGreeting({ scheme_id: this.$route.query.id }).then(async (res) => {
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.greets = res.data.result
            this.historyChat.messages = [
              {
                author: { role: 'agent' },
                auto: true,
                agent_role_id: this.currentRoleInfo?.id,
                content: {
                  chat_message: { content: res.data.result || '请问有什么我可以帮您？' }
                },
                create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
              }
            ]
          }
        })
        this.systemMessages = ''
        this.qaList = []
        // this.historyChat.messages = [{
        //   author: {role: 'agent'},
        //   auto: true,
        //   content: {
        //     parts: this.greets || '请问有什么我可以帮您？'
        //   },
        //   create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        // }]
      } else {
        this.qaList = []
        this.$message({
          type: 'error',
          message: '清空聊天记录失败!'
        })
      }
    },
    handleSave(data) {
      if(this.isReadOnly) return
      this.activeZl = false
      this.detailContent.text = data
      this.handleDetailSave()
    },
    // 处理插入、替换功能
    handleDetailOpt(type, data) {
      if (
        !this.isEdit &&
        (this.systemMessages === 'process running' ||
          this.systemMessages === 'process stream' ||
          this.systemMessages === 'process_stream_message')
      ) {
        return false
      } else {
        if (!this.isInsertFlag && type === 'insert' && this.isEdit) {
          console.log('插入位置', this.$refs.MyEditor)
          console.log(this.$refs.MyEditor.$refs, this.$refs.MyEditor.$refs)
          this.$refs.MyEditor.$refs.editorFin.insert(() => {
            return { text: data }
          })
          this.insertWriteFlag = true
          this.$message({
            type: 'success',
            message: '插入成功!'
          })
        }
        if (!this.isReplaceFlag && type === 'replace' && this.isEdit) {
          this.$refs.MyEditor.$refs.editorFin.replaceSelectionText(data)
          this.replaceWriteFlag = true
          this.$message({
            type: 'success',
            message: '替换成功!'
          })
        }
      }
    },
    async beforeUpload(file) {
      try {
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        })
        if (res.data.status === 200) {
          this.uploadUrl = res.data.data.obsUrl
          this.toMessage.image_key = res.data.data.key
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    modelUploadSuccess(response, file) {
      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        this.$refs.uploadBtn.clearFiles()
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key
        this.toMessage.image_key = fileKey
        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.toMessage.image_path = res.data.data.path
            }
          })
      } else {
        this.$message.warning(`模型上传状态为:${this.uploadStatus}`)
      }
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.toMessage.image_path = ''
          this.toMessage.image_key = ''
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.cust-disabled {
 cursor: not-allowed !important;
 opacity: 0.6;
}
.displayType {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 14px;
  color: #4068d4;
  line-height: 22px;
  position: relative;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    right: -8px;
    height: 12px;
    top: 6px;
    width: 1px;
    background: #c8c9cc;
  }
}

:deep(.markdown-body) {
  ol {
    list-style: decimal !important;

    > li {
      list-style: decimal !important;
    }
  }
}

:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.qa-loading-spinner {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

.qa-loading-spinner2 {
  width: 42px;
  height: 36px;
  margin-left: 2px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

.qaBox {
  margin-left: 40px;

  .qatitle {
    font-size: 12px;
    color: #696a6f;
    line-height: 20px;
  }

  .qaflex {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    max-width: 100%;
    overflow-x: auto;

    .qaitem {
      background: #f6f8fb;
      border-radius: 2px;
      color: #4068d4;
      font-size: 14px;
      margin-bottom: 6px;
      padding: 4px 12px;
      cursor: pointer;
      word-break: keep-all;
      white-space: nowrap;

      &:hover {
        background: #eff3ff;
      }

      &:active {
        background: #eff3ff;
      }
    }
  }
}

.chatContainer {
  // 兼容header
  // max-height: calc(100vh - 88px);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  position: relative;

  .headerBox {
    background-color: #fff;

    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;

      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }

      .sence-tag {
        margin-left: 16px;
        padding: 0 8px;
        height: 24px;
        border-radius: 2px;
        max-width: calc(100vw - 380px);
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
        background: #ebf9ff;
        color: #318db8;
      }
    }

    .headerStep {
      .myStep {
        background: #fff;
        padding: 13px 20px;

        :deep(.el-step__arrow) {
          margin: 0 16px;

          &::before {
            content: '';
            position: static;
            height: 1px;
            width: 100%;
            background: #c8c9cc;
            transform: none;
            display: block;
          }

          &::after {
            display: none;
          }
        }

        :deep(.is-process) {
          color: #4068d4;

          .el-step__icon {
            color: #4068d4;

            &.is-text {
              border: none;
            }
          }
        }

        :deep(.is-success) {
          color: #000;
          border-color: #4068d4;

          .el-icon-check {
            color: #4068d4;
          }

          .el-step__icon {
            color: #4068d4;

            &.is-text {
              border: 1px solid #4068d4;
            }
          }
        }

        .empty-space {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &.chatContainerFrame {
    height: 100%;

    .containerBox {
      // height: calc(100vh - 104px) !important;
      // max-height: calc(100vh - 104px) !important;
    }

    .containerCardFull {
      // top: -16px !important;
      // height: calc(100% - 0px) !important;
      // max-height: calc(100% - 0px) !important;
    }

    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }

    .chatRightFull {
      // top: -16px !important;
      // height: 100vh !important;
      // max-height: 100vh !important;
    }

    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }

    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;

      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }

  .containerBox {
    display: flex;
    flex-direction: row;
    // height: calc(100vh - 104px);
    // max-height: calc(100vh - 104px);
    overflow-y: hidden;
    overflow-x: hidden;
    position: relative;
    flex:1;
    &.containerBoxBig {
      // height: calc(100vh - 96px) !important;
      // max-height: calc(100vh - 96px) !important;
    }

    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;

      &:hover {
        background: #3455ad;
      }

      &:active {
        background: #264480;
      }

      img {
        width: 12px;
        height: auto;
      }
    }

    .phoneContent {
      overflow-y: hidden;
      overflow-x: hidden;
      background-image: url('@/assets/images/planGenerater/phoneBg.png');
      background-size: cover;
      margin: 16px 0px 0px 16px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      width: 30%;
      display: flex;
      flex-direction: column;
      position: relative;

      .phoneTitle {
        display: flex;
        width: 100%;
        justify-content: center;
        font-weight: 500;
        font-size: 16px;
        color: #323233;
        line-height: 24px;
        margin-top: 16px;

        span {
          width: 80%;
          text-align: center;
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
          overflow: hidden;
        }
      }

      .phonePhoto {
        display: flex;
        justify-content: center;
        align-content: center;
        margin-top: 25%;
        position: relative;

        .phonePh {
          width: 100px;
          height: 100px;
          box-shadow: 0 0 0 4px white;
          border-radius: 50%;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .bubble {
          width: 80px;
          height: 80px;
          position: absolute;
          top: -50px;
          right: 50px;
          background-image: url('@/assets/images/planGenerater/bubble.png');
          background-size: contain;
          background-repeat: no-repeat;

          img {
            position: absolute;
            left: 25px;
            top: 15px;
            width: 50%;
            height: 50%;
          }
        }
      }

      .phoneStatus {
        margin-top: 35%;
        display: flex;
        justify-content: center;
        cursor: pointer;
      }

      .phoneClose {
        position: absolute;
        left: 50%;
        bottom: 5%;
        transform: translate(-50%, 0%);
        width: 150px;
        height: 100px;
        display: flex;
        justify-content: space-between;
      }

      .phoneStop {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #4775f3;
        border-radius: 50%;

        &.phoneStopDisabled {
          opacity: 0.5;
          cursor: not-allowed;
          // pointer-events: none; /* 阻止事件 */
        }

        img {
          width: 100%;
          height: 100%;
        }
      }

      .phoneClone {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f7622c;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .containerCard {
      //height: calc(100% - 18px);
      // max-height: calc(100vh - 160px);
      // max-height: 100%;
      // height: 100%;
      overflow-y: hidden;
      overflow-x: hidden;
      // margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: #fff;
      // margin-left: 16px;
      // height: calc(100% - 18px);
      // max-height: calc(100% - 18px);

      &.containerCardFull {
        position: fixed !important;
        top: 50px;
        z-index: 777;
        height: calc(100% - 50px);
        max-height: calc(100% - 50px);
        width: 100%;
        margin-left: 0px !important;

        .chatScroll {
          // max-height: calc(100vh - 182px) !important;
        }
      }

      .cardContent {
        display: flex;
        flex-direction: column;
        height: calc(100%);
        max-height: calc(100%);
        position: relative;
      }

      .chatScroll {
        // max-height: calc(100vh - 220px);
        flex: 1;
        overflow-y: auto;
        padding: 0px 16px 16px;
        transition: all 0.3s ease-out;
        overflow-x: hidden;
        padding-top: 16px;

        .gptAnsWrap {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 18px;
          max-width: 100%;

          &:hover {
            .chat-time {
              .time {
                opacity: 1 !important;
              }
            }
          }

          .chat-time {
            height: 17px;
            line-height: 17px;
            // margin: 4px 0px;
            margin-bottom: 4px;
            display: flex;
            opacity: 1;
            justify-content: flex-start;
            width: calc(100% - 0px);

            &.chat-time-user {
              justify-content: flex-end;

              .user {
                flex-direction: row;
              }
            }

            .user {
              color: #646566;
              font-size: 12px;
              display: flex;
              flex-direction: row-reverse;

              .time {
                font-size: 12px;
                opacity: 0;
                padding: 0 5px;
              }

              span {
                color: #646566;
                font-size: 12px;
              }
            }

            .agent-mark {
              background: #eff3ff;
              border-radius: 2px;
              padding: 0px 8px;
              color: #4068d4 !important;
              line-height: 17px;
              font-size: 12px;
              margin-left: 4px;
              display: inline-block;
              max-width: 260px;
              /* 设置文本溢出时的行为为省略号 */
              text-overflow: ellipsis;

              /* 设置超出容器的内容应该被裁剪掉 */
              overflow: hidden;

              /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
              white-space: nowrap;
            }
          }

          .gptAnsBox {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;

            &.gptUserBox {
              justify-content: flex-end;
              align-items: flex-start;
            }
          }

          .gptAvator {
            width: 34px;
            height: 34px;
            margin-right: 3px;

            img {
              width: 34px;
              height: 34px;
            }

            .userAvator {
              width: 34px;
              height: 34px;
              border-radius: 50%;
              background: #e6ecff;
              color: #2856b3;
              line-height: 34px;
              text-align: center;
              font-size: 12px;
              font-weight: normal;
            }
          }

          .gptAns {
            background: #f6f8fb;
            border-radius: 4px;
            color: #323232;
            line-height: 20px;
            position: relative;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            &.gptUser {
              background: rgba(194, 210, 255, 0.56);
            }
            pre {
              font-family: SYZT, SimHei, SimSun, serif, PingFangSC-Regular, PingFang SC;
              font-size: 14px;
              line-height: 20px;
              color: #1f2328 !important;
              word-break: break-word;
              font-weight: 400;
              vertical-align: baseline;
            }
          }

          .gptAns2 {
            tab-size: 2;
            background: #f6f8fb;
            border-radius: 6px;
            color: #4068d4;
            line-height: 20px;
            position: relative;
            //padding: 8px 12px;
            display: flex;
            align-items: center;
          }

          .think-wrap {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            cursor: pointer;
            margin-top: 8px;
            // margin-left: 37px;
            .think-btn {
              border-radius: 2px;
              border: 1px solid rgba(64, 107, 212, 0.4);
              font-size: 12px;
              padding: 2px 8px;
              font-weight: 500;
              color: #4068d4;
              line-height: 18px;
              margin-right: 8px;
              word-break: keep-all;

              &:hover {
                background: #f6f7fb;
              }

              &:active {
                background: #eff3ff;
              }

              &.think-btn-disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }
            }
          }
        }
      }

      .chatHeader {
        font-size: 14px;
        color: #323233;
        line-height: 24px;
        font-weight: bold;
        background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
          > img {
            width: 20px;
            height: 20px;
            content: url('@/assets/images/ai-loading.png');
          }
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnDisabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none; /* 阻止事件 */
            }

            &.rightBtnBlue {
              background-color: #406bd4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .thinkContent {
        margin-left: 16px;
        width: calc(100% - 32px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        max-height: 225px;
        height: 225px;
        overflow-y: auto;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dcdde0;
        transition: height 0.1s;

        &.thinkContentFull {
          position: fixed !important;
          left: 60px;
          width: calc(100vw - 90px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 888 !important;
          max-height: calc(100vh - 150px) !important;
          top: 210px;
        }

        &.thinkContentFullFull {
          position: fixed !important;
          left: 0px;
          margin-left: 0px;
          width: 100vw !important;
          height: calc(100vh - 50px) !important;
          overflow: hidden;
          z-index: 888 !important;
          max-height: calc(100vh - 50px) !important;
          top: 50px;
        }

        &.thinkContentFullSmall {
          position: fixed !important;
          left: 180px;
          width: calc(100vw - 200px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 888 !important;
          max-height: calc(100vh - 150px) !important;
          top: 210px;
        }

        .thinkHeader {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 12px 12px;

          .title {
            color: #323233;
            line-height: 20px;
            display: flex;
            align-items: center;

            img {
              height: 24px;
              width: 24px;
              margin-right: 4px;
            }
          }

          .thinkOpt {
            display: flex;

            .think-btn {
              font-size: 14px;
              margin-left: 4px;
              cursor: pointer;
              width: 24px;
              height: 24px;
              text-align: center;
              line-height: 22px;
              font-weight: bold;

              &.think-btn-blue {
                background-color: #4068d4 !important;
                border-radius: 4px;

                &:hover {
                  background: #3455ad !important;
                }

                &:active {
                  background: #264480;
                }
              }

              &:hover {
                background-color: #ebecf0;
                border-radius: 4px;
              }

              img {
                width: 12px;
                height: 12px;
              }
            }
          }
        }

        .thinkWrap {
          background: #ffffff;
          padding: 0px 12px 12px 36px;
          max-height: calc(100% - 40px);
          overflow-y: auto;

          .thinkItem {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: start;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dcdde0;
            margin-top: 12px;

            &:first-child {
              margin-top: 0px;
            }
          }

          .itemContent {
            color: #646566;
            line-height: 22px;
            flex: 1;
            margin-left: 8px;
          }
        }
      }

      .clearChatBtnBox {
        // position: absolute;
        cursor: pointer;
        bottom: 78px;
        margin-left: 48px;
        // width: 100%;
        background: #ffffff;
        z-index: 2;
        display: flex;
        align-items: center;

        &.clearChatBtnBoxHigh {
          bottom: 100px !important;
        }

        .guess {
          margin-bottom: 5px;
          background: #fff;
          box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
          border-radius: 2px;
          max-width: 500px;
          min-width: 88px;
          padding: 5px;
          max-height: 150px;
          overflow-y: auto;
          position: absolute;
          bottom: 102px;
          left: 45px;
          z-index: 7;

          &.guessHigh {
            bottom: 132px !important;
          }

          .guess-header {
            font-size: 12px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 4px 8px;
            border-bottom: 1px solid #ebecf0;

            .title {
              color: #646566;
              font-size: 12px;
            }

            .huanyihuan {
              font-size: 12px;
              margin-left: 2px;
              color: #4068d4;
              cursor: pointer;

              &:hover {
                color: #3455ad;
              }

              &:active {
                color: #264480;
              }
            }

            .plansearch {
              cursor: pointer;

              &:hover {
                color: #3455ad;
              }

              &:active {
                color: #264480;
              }
            }
          }

          li {
            span {
              display: block;
              line-height: 32px;
              padding: 0 5px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            &:hover {
              background: #eff3ff;
            }

            &:active {
              background: #eff3ff;
            }
          }
        }
      }

      .clearChat {
        cursor: pointer;
        border-radius: 2px;
        border: 1px solid rgba(64, 107, 212, 0.4);
        font-size: 14px;
        display: inline-block;
        padding: 2px 8px;
        font-weight: 500;
        font-size: 12px;
        color: #4068d4;
        line-height: 18px;
        margin-right: 8px;
        word-break: break-all;
        margin-right: 8px;

        &:hover {
          background: #f6f7fb;
        }

        &.think-btn-disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .selectRole {
        position: absolute;
        z-index: 99;
        left: 46px;
        bottom: 72px;
        width: 260px;
        max-height: 400px;
        overflow-y: auto;
        background: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 4px;
        display: flex;
        flex-direction: column;

        .selectRoleHigh {
          bottom: 102px !important;
        }

        .roleItem {
          display: flex;
          flex-direction: row;
          align-items: center;

          img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 4px;
          }

          .name {
            cursor: pointer;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; // 默认不换行；
          }

          .mark-tag {
            background: #eff3ff;
            border-radius: 2px;
            padding: 0px 8px;
            color: #4068d4 !important;
            line-height: 17px;
            font-size: 12px;
            margin-left: 4px;
            display: inline-block;
            max-width: 260px;
            /* 设置文本溢出时的行为为省略号 */
            text-overflow: ellipsis;

            /* 设置超出容器的内容应该被裁剪掉 */
            overflow: hidden;

            /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
            white-space: nowrap;
          }
        }
      }

      .chatFooter {
        padding: 12px 16px;
        bottom: 0px;
        left: 0px;

        .chatFooterTextInput {
          display: flex;
          position: relative;
          width: 100%;
          background: #ffffff;
          justify-content: flex-start;
          align-items: center;
        }

        .chatFooterImage {
          margin-left: 31px;
        }

        .think-btn-disabled {
          width: 25px;
          height: 25px;
          cursor: pointer;
          line-height: 25px;
          margin-right: 6px;
          text-align: center;
          opacity: 0.5;
          cursor: not-allowed;
        }

        .clear {
          width: 25px;
          height: 25px;
          cursor: pointer;
          line-height: 25px;
          margin-right: 6px;
          text-align: center;

          &:hover {
            background: #ebecf0;
          }

          &:active {
            background: #dcdde0;
          }
        }

        .chatInput {
          flex: 1;
          border-radius: 4px;
          border: 1px solid #c8c9cc;

          .roleTag {
            padding: 2px 4px;
            // background: #E6ECFF;
            // color: #3455AD;
            // font-size: 12px;
            // padding: 2px 4px;
            // border-radius: 2px;
          }
        }

        .send-btn {
          position: absolute;
          right: 4px;
          display: flex;
          flex-direction: row;
          align-items: center;
          // margin-top: 6px;
          font-size: 12px;
          bottom: 4px;

          .send-desc {
            font-size: 10px;
            color: #969799;

            img {
              width: 10px;
              height: 10px;
            }
          }

          .yuyinBtn {
            cursor: pointer;
            margin-right: 8px;

            &.yuyinBtnDisabled {
              cursor: not-allowed;
            }

            img {
              width: 24px;
              height: 24px;
            }
          }

          :deep(.el-button) {
            width: 20px;
            height: 20px;
            padding: 0px;
            background: linear-gradient(180deg, #69a0ff 0%, #375fcb 100%);
            border-radius: 12px !important;
            border: none;
            margin-left: 12px;
            margin-right: 8px;
            line-height: 20px;

            i {
              font-size: 12px;
              margin-left: -1px;
            }
          }
        }

        .upload-demo {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .updloadBtn {
          width: 18px;
          height: 16px;
          background: url(@/assets/images/planGenerater/upload-icon.png) no-repeat;
          background-size: contain;
        }

        :deep(.el-input__inner) {
          border-radius: 4px;
          border-color: #c8c9cc;
          color: #323233;

          &:focus {
            border-color: #406bd4;
          }
        }

        :deep(.el-textarea__inner) {
          border-radius: 4px;
          border: none !important;
          color: #323233;
          padding-right: 114px;

          &:focus {
            border-color: #406bd4;
          }
        }
      }
    }

    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      // height: calc(100% - 18px);
      // max-height: calc(100% - 18px);
      overflow-y: hidden;
      // margin-top: 16px;
      // margin-right: 16px;
      position: relative;

      &.chatRightFull {
        position: fixed;
        top: 50px;
        z-index: 888;
        height: calc(100% - 50px);
        width: 100%;
        margin-left: 0px !important;

        .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }

        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }

        .optContentBox {
          height: calc(100vh - 180px) !important;
          max-height: calc(100vh - 180px) !important;
        }
      }

      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }

      .optContent2 {
        max-height: calc(100% - 0px) !important;
        overflow-y: hidden;
      }

      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnDisabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none; /* 阻止事件 */
            }

            &.rightBtnBlue {
              background-color: #406bd4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .optScroll {
        position: relative;
        height: calc(100vh - 220px);
        max-height: calc(100vh - 220px);
        overflow-y: hidden;
        overflow-x: hidden;
        padding: 20px;
        display: flex;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optScroll2 {
        position: relative;
        height: calc(100vh - 210px);
        max-height: calc(100vh - 210px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optContentBox {
        //height: calc(100% - 340px);
        // max-height: calc(100vh - 340px);
        // max-height: 100%;
        // height: 100%;
        overflow-y: hidden;
        width: 100%;
        position: relative;
        background: transparent !important;
      }
      .h-full {
        max-height: 100%;
        height: 100%;
      }
      .h-full-340 {
        height: calc(100% - 340px);
      }

      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }

  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      background: #e0e6ff;

      .process-icon {
        color: #3455ad !important;
      }
    }

    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;

      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }

    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;

      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }

    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;

      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }

  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;

    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }

    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }

    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }

  ::v-deep .el-button--mini {
    line-height: 0px !important;
    padding: 8px 6px !important;

    img {
      height: 16px;
      margin-top: -2px;
    }
  }

  ::v-deep .is-wait .el-step__icon.is-text {
    border: none !important;
  }


  &.EmbedModeFrame{
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #f4f5f9;
    display: flex;
    max-height: 100vh;
    width: 100%;
    height: 100vh;
    .EmbedModeWrap{
      position: fixed;
      bottom: 27px;
      right: 27px;
    }

    .containerBox{
      height: 100%;
      max-height: 100%;
    }

    .containerCardFull{
      top: 0px !important;
      height: calc(100%) !important;
    }

    .chatRightFull{
      top: 0px !important;
      height: calc(100%) !important;
    }
  }
}

.clear-icon {
  width: 20px;
  height: 20px;
  margin-top: 3px;
}

:deep(.fousetest) {
  border: 1px solid #4068d4;
}

.mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}



/* 定义 fade 过渡的 CSS 类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s ease, transform 1s ease;
  /* 增加持续时间 */
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.6);
  /* 添加缩放效果 */
}

.fade-enter-to,
.fade-leave {
  opacity: 1;
  transform: scale(1);
}
.overlay{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  /* 半透明白色背景 */
  z-index: 1000;
  /* 确保覆盖层位于较高层级 */
  cursor: not-allowed;
  /* 更改鼠标指针样式，提示用户不可交互 */
}

.kickfeat-component{
  position: relative;
  z-index: 1001;
}
</style>
<style lang="scss">
@import 'github-markdown-css/github-markdown.css';
@import 'highlight.js/styles/stackoverflow-dark.css';

.samllinput {
  .el-input__inner {
    border-radius: 2px;
    border-color: #c8c9cc;
    height: 20px !important;
    line-height: 20px !important;
    font-size: 12px;
    color: #323233;

    &:focus {
      border-color: #406bd4;
    }
  }
}

.chendianfangan {
  padding: 0px !important;
  bottom: 98px;
  top: inherit !important;

  &.chendianfanganHigh {
    bottom: 124px !important;
  }

  .guessPovper {
    // margin-bottom: 5px;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    max-width: 500px;
    min-width: 88px;
    padding: 5px;
    max-height: 150px;
    overflow-y: auto;
    //position: absolute;
    //bottom: 23px;
    //left: 0px;
    //z-index: 7;
    .guess-header {
      font-size: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      border-bottom: 1px solid #ebecf0;

      .title {
        color: #646566;
        font-size: 12px;
      }

      .huanyihuan {
        font-size: 12px;
        margin-left: 2px;
        color: #4068d4;
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }

      .plansearch {
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }
    }

    .otherRole {
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 4px;
      }

      .mark-tag {
        background: #eff3ff;
        border-radius: 2px;
        padding: 0px 8px;
        color: #4068d4 !important;
        line-height: 17px;
        font-size: 12px;
        margin-left: 4px;
        display: inline-block;
        max-width: 260px;
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
      }
    }

    li {
      cursor: pointer;

      span {
        display: block;
        line-height: 32px;
        padding: 0 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        background: #eff3ff;
      }

      &:active {
        background: #eff3ff;
      }
    }
  }
}

.my-message-box1 {
  :deep(.el-message-box__btns .el-button) {
    width: 62px !important;
  }

  :deep(.el-message-box__header .el-message-box__status) {
    display: block !important;
  }

  .el-message-box__content {
    padding: 10px 30px !important;
  }
}
.activeZl {
  color: #4068D4 !important;
  font-weight: bold !important;
}
.newClass {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* padding-bottom: 30px; */
}
.modern-button-container {
  display: flex;
  gap: 10px;
  align-items: center;
}
.modern-button {
  color: #007BFF;
  border: none;
  border-radius: 20px; /* 调整按钮圆角 */
  padding: 0px 16px; /* 调整按钮高度和内边距 */
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease, border-radius 0.3s ease; /* 添加过渡效果 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.modern-button:hover {
  background-color: #cce5ff; /* 悬停时的颜色 */
}
.modern-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
}
.modern-button.active {
  background-color: #007BFF; /* 选中时的深蓝色 */
  color: white;
}
.modern-button-secondary {
  background-color: #e6f9e6; /* 未选中时的浅绿色 */
  color: #28a745;
  border: none;
  border-radius: 12px; /* 调整按钮圆角 */
  padding: 8px 16px; /* 调整按钮高度和内边距 */
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease, border-radius 0.3s ease; /* 添加过渡效果 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.modern-button-secondary:hover {
  background-color: #d4edda; /* 悬停时的颜色 */
}
.modern-button-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.5);
}
.modern-button-secondary.active {
  background-color: #28a745; /* 选中时的深绿色 */
  color: white;
}
.tabClass{
  display: flex;
  justify-content: center;
  align-items: center;
}
.ziliao {
  margin-left:16px;
  margin-right: 23px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  transition: color 0.3s ease, font-weight 0.3s ease; /* 添加过渡效果 */
}
</style>
<style scoped>
#left-content{
  margin: 0!important;
}
</style>
