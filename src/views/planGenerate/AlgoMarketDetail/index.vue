<template>
  <!-- 应用详情 -->
  <div class="page">
    <div class="info">
      <div class="info-top">
        <div class="info-title">
          <span>{{ baseInfo?.name }}</span>
          <Status :text="statusTypeMap[baseInfo?.isOnline]?.text"
            :bg-color="statusTypeMap[baseInfo.isOnline]?.bgColor"
            :dot-color="statusTypeMap[baseInfo.isOnline]?.dotColor" />
        </div>
        <!-- <div style="display: flex;">
          <el-button type="primary" @click="() => bushuFlag = true">部署配置</el-button>
          <div v-if="isEdit" style="margin-left: 8px">
            <el-button type="primary" @click="saveHandle">保存</el-button>
            <el-button type="info" @click="closeHandle">取消</el-button>
          </div>
          <el-button v-else type="info" @click="editHandle">编辑</el-button>
        </div> -->
      </div>
      <div style="margin:0 20px;width: 80%;display: flex;flex-direction: column;gap: 10px">
        <!-- <div v-if="baseInfo.abilityValue">
          <span style="font-weight: bold;font-size: 16px">能力价值</span>
          <br />
          <div style="word-wrap: break-word;white-space: pre-wrap;margin-left: 10px">{{ baseInfo.abilityValue }}</div>
        </div>
        <div v-if="baseInfo.abilityIntents">
          <span style="font-weight: bold;font-size: 16px">能力的意图匹配信息（可支持的提问方式）</span>
          <br />
          <div style="word-wrap: break-word;white-space: pre-wrap;margin-left: 10px">{{ baseInfo.abilityIntents }}</div>
        </div> -->
        <!-- <p style="font-weight: bold;font-size: 16px;margin-top: 20px"
          v-if="baseInfo.abilityIntents || baseInfo.abilityValue">
          能力基础信息</p> -->
      </div>

      <el-descriptions title="" :column="3">
        <el-descriptions-item label="创建时间">{{ baseInfo?.createTime }}</el-descriptions-item>
        <!-- <el-descriptions-item label="发布时间">{{ baseInfo?.update_date }}</el-descriptions-item> -->
        <el-descriptions-item label="发布人">{{ baseInfo?.userName }}</el-descriptions-item>
        <!-- <el-descriptions-item label="关联能力">
          <el-select v-if="isEdit" ref="abilitySelect" v-model="baseForm.abilityIds" style="width:300px"
            placeholder="请选择关联能力" :remote-method="searchAbility" clearable filterable multiple remote
            @change="changeList">
            <el-option v-for="item in abilityList" :key="item.ability_id" :label="item.ability_name"
              :value="item.ability_id" />
          </el-select>
          <span v-show="!isEdit">{{ abilityNames }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="当前版本">{{ baseInfo?.current_version_name }}</el-descriptions-item>
        <el-descriptions-item label="置顶">
          <span v-if="!isEdit">{{ baseInfo?.sort == 0 ? '关闭' : '开启' }}</span>
          <el-select v-else v-model="baseForm.sort" style="width: 200px;">
            <el-option label="开启" :value="1">开启</el-option>
            <el-option label="关闭" :value="0">关闭</el-option>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="描述">
          <el-input v-if="isEdit" v-model.trim="description" type="textarea" :rows="1" style="width:90%"
            placeholder="请输入描述信息"></el-input>
          <span v-else>{{ baseInfo?.description }}</span>
        </el-descriptions-item>
        <el-descriptions-item v-for="(item, index) in extDataList" :key="index" :label="item.name">
          {{ item.value }}
        </el-descriptions-item> -->
      </el-descriptions>
      <!-- <el-descriptions title="" :column="1">
        <el-descriptions-item label="标签">
          <el-select v-if="isEdit" ref="tagsSelect" v-model="baseForm.tag_ids" style="width:328px" multiple filterable
            placeholder="请选择标签" clearable :filter-method="handleTagFilter" @keyup.native.enter="addBizTag"
            @change="changeTags">
            <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <div v-else style="width: 328px">
            <selectItem :array-items="baseInfo.tag?.map(tag => { return { key: tag.id, label: tag.name } })" :max-length="2">
            </selectItem>
          </div>
        </el-descriptions-item>
      </el-descriptions> -->
      <el-tabs v-model="activeName">
        <el-tab-pane label="接口定义" name="interface" />
      </el-tabs>
    </div>
    <div class="tab-content">
      <Interface v-if="activeName === 'interface'" :version-id="baseInfo.id" />
    </div>
    <!-- <BushuModal :is-visible="bushuFlag" :bushu-id="bushuId" @close="handleClose" /> -->
  </div>
</template>

<script type="text/javascript">
// import Publish from './component/publish.vue'
import Interface from './components/interface.vue'
// import RegisterPage from './component/register.vue'
// import AbilitiesAuth from './component/abilitiesAuth.vue'
// import Feedback from './component/feedback.vue'
// import Fabu from './component/fabu.vue'
import {
  getAgentAbility
} from '@/api/aiServiceDeploy.js';
import { getMarketAppInfo, updateAbility, queryAbilityMarket, queryTagsMarket, addTagMarket, bindTagMarket, AbilityEngineServiceUpdate } from '@/api/planGenerateApi.js'
import Status from '@/components/Status/index.vue';
// import selectItem from '../selectItem.vue';
// import BushuModal from './component/bushuModal.vue';
export default {
  name: 'AlgoMarketDetail',
  components: { Interface, Status },
  data() {
    return {
      activeName: 'interface',
      baseInfo: {
        description: ''
      },
      tagKeyword: '',
      userList: [],
      extDataList: [],
      abilityList: [],
      tagList: [],
      allTagList: [],
      baseForm: {
        abilityIds: [],
        relate_abilities: [],
        tag_ids: [],
        sort: 0
      },
      description: '',
      statusTypeMap: {
        0: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        1: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
      },
      isEdit: false,
      abilityNames: '',
      bushuFlag: false,
      bushuId: '',
    }
  },
  created() {
    this.getMarketInfo()
  },
  mounted() {
    this.searchTags2('');
  },
  methods: {
    handleClose(val) {
      if (val) {
        console.log('更新部署', val);
        AbilityEngineServiceUpdate({
          ability_id: +this.$route.query?.id || '',
          engine_service_id: val.id
        }).then((res) => {
          console.log('部署结果', res);
          this.getMarketInfo();
        })
      }
      this.bushuFlag = false;
    },
    searchAbility(val) {
      queryAbilityMarket({
        ability_name: val,
        page_num: 1,
        page_size: 100
      })
        .then((res) => {
          this.isLoading = true;
          if (res.status === 200 && res.data.code === 200) {
            this.abilityList = res.data.result?.items?.map(item => {
              return {
                ability_id: item.id,
                ability_name: item.name
              }
            })
          } else {
            this.$message.error(res.msg || '应用创建失败!');
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    editHandle() {
      this.isEdit = true;
      // this.$nextTick(() => {
      //   const abilitiesList=this.baseInfo?.relate_abilities||[]
      //   const abilityIds =this.baseInfo.relate_abilities?.map(item=>item.ability_id)
      //   this.baseForm.abilityIds = this.baseInfo.relate_abilities?.map(item=>item.ability_id)
      //   this.abilityNames =this.baseInfo.relate_abilities?.map(item=>item.ability_name).join(',')
      //   if(abilitiesList.length >0){
      //     abilitiesList.forEach((ele) => {
      //       this.$refs.abilitySelect.cachedOptions.push({
      //         currentLabel:ele.ability_name,
      //         currentValue: ele.ability_id,
      //         label: ele.ability_name,
      //         value:  ele.ability_id
      //       })
      //     });
      //     this.$set(this.baseForm, 'abilityIds', abilityIds);
      //   }
      // })
    },
    closeHandle() {
      this.description = this.baseInfo.description;
      this.isEdit = false;
    },
    saveHandle() {
      const params = {
        ability_id: +this.$route.query?.id || '',
        relate_abilities: this.baseForm.relate_abilities,
        description: this.description,
        tag_ids: this.baseForm.tag_ids,
        sort: this.baseForm.sort
      }
      const relate_abilities = [...this.baseForm.relate_abilities];
      updateAbility(params).then(async res => {
        if (res?.data?.code === 200) {
          await bindTagMarket({
            tag_ids: this.baseForm.tag_ids, biz_id: this.$route.query?.id
          }).then((ress) => {
            console.log('绑定成功', ress.data);
          })
          this.baseInfo.description = this.description;
          this.baseInfo.sort = this.baseForm.sort;
          this.getMarketInfo();
          this.abilityNames = relate_abilities.map(item => item.ability_name).join(',')
          this.isEdit = false;
          this.baseForm.relate_abilities = []
          this.$message.success('保存成功')
        } else {
          this.$message.error('保存失败')
        }
      })
    },
    changeList(val) {
      this.$nextTick(() => {
        this.baseForm.relate_abilities = this.$refs.abilitySelect.selected.map((item => {
          return {
            ability_id: item.currentValue,
            ability_name: item.currentLabel
          }
        }))
      })
    },
    async getMarketInfo() {
      console.log(this.baseInfo, 'baseInfo')
      await getAgentAbility({
        aiServiceId: this.$route.query?.aiServiceId || '',
        pathName: this.$route.query?.pathName || ''
      }).then(res => {
        console.log("获取详情成功", res);
        if (res?.data?.status === 200) {
          this.baseInfo = {
            description: '',
            sort: 0,
            ...res?.data.data,
            isOnline: res?.data?.data?.isOnline ? 1 : 0
          }
          console.log("this.baseInfo", this.baseInfo);
          // this.baseInfo.abilityValue = res.data.result?.ext_info?.abilityValue || '';
          // this.baseInfo.abilityIntents = res.data.result?.ext_info?.abilityIntents || '';
          // this.bushuId = res.data.result.engine_service_id || ''
          // this.baseForm.tag_ids = res?.data?.result?.tag?.map(item => item.id) || [];
          // this.baseInfo.status = 1
          // this.description = res?.data?.result?.description;
          // const abilitiesList = res?.data?.result.relate_abilities || []
          // this.extDataList = res?.data?.result.ext_data_info || []
          // const abilityIds = res?.data?.result.relate_abilities?.map(item => item.ability_id)
          // this.baseForm.sort = res?.data?.result?.sort || 0;
          // this.baseForm.abilityIds = res?.data?.result.relate_abilities?.map(item => item.ability_id)
          // this.abilityNames = res?.data?.result.relate_abilities?.map(item => item.ability_name).join(',')
          // if (abilitiesList.length > 0) {
          //   abilitiesList.forEach((ele) => {
          //     this.$refs.abilitySelect.cachedOptions.push({
          //       currentLabel: ele.ability_name,
          //       currentValue: ele.ability_id,
          //       label: ele.ability_name,
          //       value: ele.ability_id
          //     })
          //   });
          //   this.$set(this.baseForm, 'abilityIds', abilityIds);
          // }
          // if (res?.data?.result.ext_data_info && res?.data?.result.ext_data_info.length > 0) {
          //   this.extDataList = res?.data?.result.ext_data_info?.map(item => { return { value: item.field_val?.label, name: item.field_name } })
          // }
        } else {

          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch((err) => {
        console.log('err', err)
      })
    },
    async changeTags(val) {
      console.log('改变', val);
      const temp = [];
      val.forEach(tagid => {
        const filters = this.allTagList.filter(item => item.id === tagid);
        if (filters.length === 0) {
          if (tagid.length && tagid.length <= 15) {
            addTagMarket({
              name: tagid
            }).then(async (res) => {
              if (res.data) {
                console.log('添加成功', res.data);
                temp.push(res.data);
                queryTagsMarket({
                  keyword: ''
                }).then(res => {
                  if (res.data) {
                    this.tagList = res.data;
                    this.allTagList = res.data;
                    this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length - 1].currentLabel = tagid;
                    this.baseForm.tag_ids = temp;
                    console.log('this.baseForm.tag_ids', this.baseForm.tag_ids);
                    this.$nextTick(() => {
                      this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length - 1].currentLabel = tagid;
                    });
                  } else {
                    this.tagList = [];
                  }
                });
              }
            });
          } else {
            this.$message({
              type: 'warning',
              message: '标签最长为15个字符，请修改!'
            });
          }

        } else {
          temp.push(tagid);
          this.baseForm.tag_ids = temp;
        }
      });
    },
    async addBizTag() {
      if (this.tagKeyword !== '') {
        await addTagMarket({ name: this.tagKeyword })
        await this.searchTags('')
      }
    },
    async handleTagFilter(keyword) {
      this.tagKeyword = keyword
      await this.searchTags(keyword)
    },
    searchTags(keyword) {
      queryTagsMarket({
        keyword
      }).then(res => {
        if (res.data) {
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
          }
        } else {
          this.tagList = [];
        }
      })
    },
    searchTags2() {
      queryTagsMarket({
        keyword: ''
      }).then(res => {
        if (res.data) {
          this.tagList = res.data;
          this.allTagList = res.data;
          const temp = [];
          console.log('变价回显', this.baseInfo.tag);
          this.baseInfo.tag?.forEach(titem => {
            const filter = res.data.filter(item => item.id === titem.id)
            if (filter.length) {
              temp.push(titem.id);
            }
          });
          this.baseForm.tag_ids = temp;
        } else {
          this.tagList = [];
        }
      })
    },
  }
}
</script>
<style lang="less" scoped>
:deep(.el-descriptions-item__label:not(.is-bordered-label)) {
  word-break: keep-all;
}

.page {
  display: flex;
  flex-direction: column;

  .info {
    width: 100%;
    background: #fff;
    border-bottom: 2px solid #E4E7ED;
    // padding-left: 20px;

    .info-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 20px;

      .info-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        >span {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #323233;
          line-height: 26px;
          margin-right: 12px;
        }
      }
    }

    .el-descriptions {
      padding-left: 20px;

      :deep(.el-descriptions-item__container) {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
      }
    }

    .el-tabs {
      padding-left: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        height: 0px;
      }
    }
  }

  .tab-content {
    flex: 1;
    margin: 16px 20px 0 20px;
    background: #fff;
    padding: 16px 20px;
  }

  .el-select {
    width: 100%;
  }

  :deep {
    .el-tabs__header {
      margin-bottom: 0px;
    }

    .el-dialog__header {
      border-bottom: 1px solid #ebecf0;
    }
  }
}

::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
</style>
