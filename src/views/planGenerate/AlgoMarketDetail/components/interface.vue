<template>
  <div>
    <div class="search">
      <el-descriptions title="" :column="1">
        <el-descriptions-item label="请求地址" :label-style="{ 'width': '80px' }">{{ baseInfo?.req_url
          }}</el-descriptions-item>
        <el-descriptions-item label="请求类型" :label-style="{ 'width': '80px' }">POST</el-descriptions-item>
        <el-descriptions-item label="请求参数" :label-style="{ 'width': '80px' }">
          <!-- <el-table
            :data="tableReqData"
            style="width: 100%"
            row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
          >
            <el-table-column prop="param_name" label="参数名称" width="200"/>
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是':'否' }}
              </template>
</el-table-column>
<el-table-column prop="data_type" label="数据类型" />

</el-table> -->
          <div class="div-textarea">
            <div class="div-box">
              <codemirror v-model="inParam" :options="cmOptions" :placeholder="JSON.stringify(jsonSample, null, 4)" />
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="请求头" :label-style="{ 'width': '80px' }">
          <el-table :data="tableHeaderData" style="width: 100%" row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233' }">
            <el-table-column prop="param_name" label="参数名称" width="200" />
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型" />
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="响应参数" :label-style="{ 'width': '80px' }">
          <!-- <el-table :data="tableRespData" style="width: 100%" row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233'}">
            <el-table-column prop="param_name" label="参数名称" width="200" />
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是':'否' }}
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型" />
          </el-table> -->
          <div class="div-textarea">
            <div class="div-box">
              <el-input v-model.trim="outParam" type="textarea" resize="none" :readonly="true"
                :placeholder="JSON.stringify(jsonSample, null, 4)" />
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>


  </div>
</template>
<script type="text/javascript">
import { codemirror } from 'vue-codemirror';

// require styles
import 'codemirror/lib/codemirror.css';

// require active-line.js
import 'codemirror/addon/selection/active-line.js';
// styleSelectedText
import 'codemirror/addon/selection/mark-selection.js';
// hint
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/javascript-hint.js';
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/lint.js';

import { queryAbilityApi } from '@/api/planGenerateApi'
import {
  queryAlgoConfigInfo,
  getAiServiceParam
} from '@/api/aiServiceDeploy.js';

export default {
  name: 'InterfaceCom',
  components: { codemirror },
  props: {
    versionId: {
      type: [String, Number],
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      baseInfo: {},
      tableLoading: false,
      tableReqData: [],
      tableRespData: [],
      tableHeaderData: [{
        param_name: 'Content-Type',
        param_desc: 'MIME类型',
        mandatory: true,
        data_type: 'string'
      }, {
        param_name: 'X-GW-Authorization',
        param_desc: 'token,通过appKey和appSecret获取',
        mandatory: true,
        data_type: 'string'
      }],
      cmOptions: {
        // codemirror options
        tabSize: 4,
        mode: {
          name: 'application/json',
          json: true
        },
        theme: 'base16-dark',
        // lineNumbers: true,
        line: true,
        indentWithTab: true,
        readOnly: true,
        maxScanLineLength: 500
        // lineWrapping: true
        // more codemirror options, 更多 codemirror 的高级配置...
      },
      inParam: '',
      outParam: '',
      jsonSample: {},
    }
  },
  watch: {
    versionId: {
      immediate: true,
      handler(val) {
        if (val) {
          console.log('interface versionId', val);
          this.getDeviceBindList()
          this.queryServiceParam()
        }
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    handlSearch() {

    },
    async queryServiceParam() {
      try {
        const res = await getAiServiceParam({
          aiServiceId: +this.$route.query?.aiServiceId || '',
          pathName: this.$route.query?.pathName,
          devType: "1",
        })
        console.log('res getAiServiceParam', res);
        if(res.data.status === 200) {
          this.inParam = res.data.data?.formParamJsonStr || "{}"
          this.outParam = res.data.data?.resultParamJsonStr || "{}"
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 绑定信息
    async getDeviceBindList() {
      this.tableLoading = true
      await queryAlgoConfigInfo({
        aiServiceId: +this.$route.query?.aiServiceId || '',
        // version_id: this.versionId,
      }).then(res => {
        console.log('interface res', res);
        this.tableLoading = false
        if (res?.data?.status === 200) {
          const data = res?.data?.data
          this.baseInfo = {
            req_url: data.requestUrlPrefix ? data.requestUrlPrefix + this.$route.query?.pathName : '',
          }
        } else {
          this.$message.error('获取失败')
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  text-align: right;
  margin-bottom: 16px;

  :deep(.search-input) {
    .el-input__inner {
      height: 30px;
      line-height: 30px;
      border-color: #c8c9cc !important;
      border-right: none;
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
    }

    .el-input-group__append {
      border-color: #c8c9cc !important;
      background-color: transparent;
      padding: 0px 12px;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
  }
}

.el-link {
  margin-right: 16px;

  &:last-child {
    margin-right: 0;
  }
}


:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebecf0;
}

:deep {
  .hide-expand .el-table__expand-column .el-icon {
    visibility: hidden;
  }
}


.div-textarea {
  border: 1px solid #dcdfe6;
  overflow-y: scroll;
  width: 100%;

  .div-box {
    display: inline-block;
    min-width: 100%;
  }

  ::v-deep .el-textarea__inner {
    border: none;
    height: 307px;
  }
}
</style>
