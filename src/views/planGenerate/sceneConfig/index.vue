<template>
  <!-- 应用详情 -->
  <div class="page">
    <div class="info">
      <div class="info-top">
        <div class="info-title">
          <el-link
            class="back-link"
            :underline="false"
            @click="
              () => $router.push({ path: getReturnPathBySceneCode(), query: { ...$route.query } })
            "
            ><i class="el-icon-arrow-left"></i>返回</el-link>
          <span>{{ baseInfo?.name }}</span>
          <div class="sence-tag">{{ baseInfo.agent_scene_code_name }}</div>
        </div>
        <div class="button-container">
          <el-button-group>
            <el-button type="warning" @click="restoreHandle">重置</el-button>
            <el-button type="primary" @click="saveHandle()">保存</el-button>
          </el-button-group>
        </div>
      </div>
      <el-descriptions title="" :column="2">
        <el-descriptions-item label="当前版本">{{ scene_version }}</el-descriptions-item>
        <el-descriptions-item label="描述">{{ baseInfo?.description }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ baseInfo?.update_date }}</el-descriptions-item>
        <el-descriptions-item label="标签">
          <div style="width: 328px">
            <selectItem
              :array-items="
                baseInfo.tags?.map((tag) => {
                  return { key: tag.id, label: tag.name }
                })
              "
              :max-length="2"
            ></selectItem>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="tab-content">
      <ScenePage :id="$route.query?.id" ref="scenePageRef" />
    </div>
  </div>
</template>

<script type="text/javascript">
import ScenePage from './component/scene.vue'
import { getInstanceInfo, querySchemeDetailById } from '@/api/planGenerateApi.js'
import selectItem from '../selectItem.vue'
export default {
  name: 'MarketDetail',
  components: { ScenePage, selectItem},
  data() {
    return {
      baseInfo: {},
      scene_version: ''
    }
  },
  async created() {
    await this.getDetailInfo()
    await this.getInstance()
  },
  mounted() {},
  methods: {
    // 获取返回智能能力研发的path
    getReturnPathBySceneCode() {
      const agentSceneCode = this.baseInfo.agent_scene_code
      if (agentSceneCode === 'other_assistant_scene') {
        return '/planGenerate/planchat'
      }
      return '/planGenerate/ConfTaskPlanchat'
      // if (agentSceneCode === 'operational_optimization_scene') {
      //   return '/planGenerate/ConfTaskPlanchat'
      //   // return '/planGenerate/hqwPlanchat'
      // } else if (agentSceneCode === 'digital_twin_assistant_scene') {
      //   const wllsChatPath = sessionStorage.getItem('wllsChatPath')
      //   if (
      //     wllsChatPath === '/planGenerate/wllsDevPlanchat' ||
      //     wllsChatPath === '/planGenerate/wllsExpertPlanchat'
      //   ) {
      //     return wllsChatPath
      //   }
      //   return '/planGenerate/wllsDevPlanchat'
      // } else if (agentSceneCode === 'other_assistant_scene') {
      //   return '/planGenerate/planchat'
      // }
      // return '/planGenerate/ConfTaskPlanchat'
      // return '/planGenerate/planchat';
    },
    restoreHandle() {
      // 还原配置
      if (this.$refs.scenePageRef) {
        this.$refs.scenePageRef.restoreHandle()
      }
    },
    saveHandle() {
      // 还原配置
      if (this.$refs.scenePageRef) {
        this.$refs.scenePageRef.saveHandle()
      }
    },
    async getInstance() {
     if( this.$route.query?.id == null || this.$route.query?.id == undefined || this.$route.query?.id == '' ) return
      await getInstanceInfo({
        instanceId: +this.$route.query?.id || ''
      })
        .then((res) => {
          if (res.status === 200) {
            this.scene_version = res?.data.scene_version
          } else {
            this.$message.error(res?.data?.msg || '查询失败')
          }
        })
        .catch((err) => {
          console.log('err', err)
        })
    },
    async getDetailInfo() {
      await querySchemeDetailById({
        scheme_id: +this.$route.query?.id || ''
      })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('详情', res.data.result)
            const datas = res.data.result || {}
            this.baseInfo = { ...datas }
          } else {
            this.$message.error(res?.data?.msg || '查询失败')
          }
        })
        .catch((err) => {
          console.log('err', err)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .info {
    width: 100%;
    background: #fff;
    border-bottom: 2px solid #e4e7ed;

    .info-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 20px;

      .info-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .back-link {
          display: inline-flex;
          align-items: center;
          margin-right: 16px;
          font-size: 14px;
          color: #606266;
          transition: color 0.3s;

          &:hover {
            color: #409EFF;
          }
        }

        > span {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #323233;
          line-height: 26px;
          margin-right: 12px;
        }

        .sence-tag {
          display: inline-flex;
          align-items: center;
          margin-left: 16px;
          padding: 0 8px;
          height: 24px;
          border-radius: 2px;
          max-width: calc(100vw - 380px);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          background: #ebf9ff;
          color: #318db8;
        }
      }

      .button-container {
        display: flex;
        align-items: center; /* 垂直居中对齐 */
        gap: 16px; /* 返回按钮与按钮组之间的间距 */

        .el-button-group {
          margin-left: 0; /* 移除可能的默认外边距 */
        }
      }
    }

    .el-descriptions {
      padding-left: 20px;
      :deep(.el-descriptions-item__container) {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
      }
    }

    .el-tabs {
      padding-left: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        height: 0px;
      }
    }
  }

  .tab-content {
    flex: 1;
    margin: 16px 20px 16px 20px;
    overflow-y: auto;
  }

  .el-select {
    width: 100%;
  }

  :deep {
    .el-tabs__header {
      margin-bottom: 0px;
    }

    .el-dialog__header {
      border-bottom: 1px solid #ebecf0;
    }
  }
}
</style>
