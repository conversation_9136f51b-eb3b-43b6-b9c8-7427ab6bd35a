<template>
  <div class="assistSetting">
    <div class="basicContent">
      <el-form ref="form" label-width="140px" :model="formLabelAlign" style="width: 100%" :rules="rules">
        <el-form-item label="开场白" prop="greetings">
          <el-input
            v-model="formLabelAlign.greetings"
            type="textarea"
            :autosize="{ minRows: 2 }"
            placeholder="请输入开场白"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="参考指令" prop="quick_reply_list">
          <el-select
            v-model="formLabelAlign.quick_reply_list"
            placeholder="请输入"
            :remote-method="searchData"
            clearable
            filterable
            multiple
            remote
            style="width:100%"
          >
            <el-option
              v-for="item in refpromptsList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >

            <template #default>
              <el-tooltip effect="dark" placement="top-start" :gpu-acceleration="false">
                <template #content>
                  <div class="tooltip-content">
                    {{ item.name }}
                  </div>
                </template>
                <span style="float: left" class="truncate-text">
                  {{ item.name }}
                </span>
              </el-tooltip>
                <span style="float: right;"><selectItem :array-items="item.tags?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem></span>
            </template>
            <!-- <span style="float: left">{{ item.name }}</span> -->
            <!-- <span style="float: right;"><selectItem :array-items="item.tags?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem></span> -->
          </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="聊天管理员" prop="agent_role_id">
          <chooseRole
            ref="chooseManagerRef"
            :init-data="initData"
            :selected-list="formLabelAlign.agent_role_id ? [formLabelAlign.agent_role_id] : []"
            :isSingleSelect="true"
            roleType="manager"
            @getValue="(val) => formLabelAlign.agent_role_id = val[0]"
          ></chooseRole>
        </el-form-item>
        <el-form-item label="用户角色" prop="user_agent_role_id">
          <chooseRole
            ref="chooseUserRoleRef"
            :init-data="initData"
            :selected-list="formLabelAlign.user_agent_role_id ? [formLabelAlign.user_agent_role_id] : []"
            :isSingleSelect="true"
            roleType="user"
            @getValue="(val) => formLabelAlign.user_agent_role_id = val[0]"
          ></chooseRole>
        </el-form-item>
        <el-form-item label="历史消息最大条数" prop="chat_history_size">
          <el-input-number
            v-model="formLabelAlign.chat_history_size"
            :min="0"
            :max="24"
            :step="1"
            step-strictly
          />
        </el-form-item>
        <el-form-item label="生产辅助角色" prop="agent_role_ids">
          <chooseRole
          ref="chooseRoleRef"
          :init-data="initData"
          :selected-list="formLabelAlign.agent_role_ids"
          @getValue="getRoleIds"
          ></chooseRole>
        </el-form-item>
    </el-form>
    </div>
    <!-- <div class="optFooter"> -->
      <!-- <el-button class="button-last"  type="primary" @click="changeViews(0)">上一步</el-button>
      <el-button class="button-last"  type="primary" @click="changeViews(2)">下一步</el-button> -->
      <!-- <el-button class="button-last" type="info" @click="() => $router.push({path: '/planGenerate/planchat', query: {...$route.query}})">返回智能能力研发</el-button> -->
    <!-- </div> -->
  </div>
</template>
<script type="text/javascript">
import { getRefpromptsList, getRoleList} from '@/api/planGenerateApi'
import chooseRole from './chooseRole.vue'
import selectItem  from '../../selectItem.vue';
import { cloneDeep } from 'lodash';
export default {
  name: 'InterfaceCom',
  components: { chooseRole,selectItem },
  props: {
    baseData: {
      type: Object,
      default () {
        return {}
      }
    },
    initData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data() {
    return {
      refpromptsList: [],
      managerList:[],
      userRoleList:[],
      formLabelAlign:{
        quick_reply_list: [],
        agent_role_id:'',
        chat_history_size: 0,
        agent_role_ids:[],
        greetings:'',
        user_agent_role_id: '',
      },
      rules: {
        greetings: [{ required: true, message: '请输入开场白' }],
        chat_history_size: [{ required: true, message: '请输入历史消息最大条数' }],
        agent_role_ids: [{ required: true, message: '请选择生产辅助角色' }],
      },
    }
  },
  watch:{
    baseData: {
      deep:true,
      immediate: true,
      handler(val) {
        if (val) {
          this.formLabelAlign.quick_reply_list = val.quick_reply_list
          this.formLabelAlign.agent_role_id = val.agent_role_id
          this.formLabelAlign.chat_history_size = val.chat_history_size
          this.formLabelAlign.agent_role_ids = val.agent_role_ids
          this.formLabelAlign.greetings = val.greetings
          this.formLabelAlign.user_agent_role_id = val.user_agent_role_id
        }
      }
    }
  },
  created() {
  },
  mounted() {
    this.searchManagerData();
    this.searchUserRoleData();
    this.searchData();

  },
  methods: {
    restoreHandle(){
      this.$set(this.formLabelAlign,'quick_reply_list',this.initData.quick_reply_list)
      this.$set(this.formLabelAlign,'agent_role_id',this.initData.agent_role_id)
      this.$set(this.formLabelAlign,'chat_history_size',this.initData.chat_history_size)
      this.$set(this.formLabelAlign,'agent_role_ids',this.initData.agent_role_ids)
      this.$set(this.formLabelAlign,'greetings',this.initData.greetings)
      this.$set(this.formLabelAlign,'user_agent_role_id',this.initData.user_agent_role_id)
      this.$refs.chooseRoleRef?.restoreHandle()
    },
    getRoleIds(val){
      this.formLabelAlign.agent_role_ids = val;
    },
    searchManagerData(val) {
      getRoleList({
        keyword: val||'',
        type:'manager'
      }).then((res) => {
        if (res.status === 200) {
          this.managerList =res.data?.map((item)=>{return {id:item.id,name:item.name,alias:item.alias}})
        }
      });
    },
    searchUserRoleData(val) {
      getRoleList({
        keyword: val||'',
        type:'user'
      }).then((res) => {
        if (res.status === 200) {
          this.userRoleList =res.data?.map((item)=>{return {id:item.id,name:item.name,alias:item.alias}})
        }
      });
    },
    searchData(val) {
      getRefpromptsList({
        page: 1,
        page_size: 999,
        tags_or_content: val
      }).then((res) => {
        if (res.status === 200) {
          this.refpromptsList =res.data.data.map((item)=>{return {title: item.title,id:item.id,name:`${item.title}:  ${item.content}`,tags:item.tags}})
          console.log('22222222-3--3-3',this.refpromptsList)
          // this.refpromptsList =res.data.data.map((item)=>{return {id:item.id,name:item.content,tags:item.tags}})
        }
      });
    },
    getRefpromptsHandle(val){

    },
    // changeViews(val){
    //   this.$refs.form.validate((valid) => {
    //     if (valid) {
    //       console.log('向上返回formLabelAlign',this.formLabelAlign)
    //       this.$emit('updateBaseData',this.formLabelAlign)
    //     }
    //   });
    // },
    getData() {
      return this.formLabelAlign;
    },
    async valid() {
      let result = false;
      await this.$refs.form.validate((valid) => {
        result = valid;
      });
      return result;
    },
    // 删除行
    handleRemove (rowIndex) {
      rowIndex = (this.pageNo - 1)*this.pageSize + rowIndex;
      const res = this.tableData.splice(rowIndex, 1);
      console.log('删除行', res, this.tableData )
    },

  }
}
</script>
<style lang="less" scoped>
.tooltip-content {
  max-width: 800px;
  white-space: pre-wrap;
}

.truncate-text {
  width: 800px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.assistSetting{
  padding:10px 16px;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .basicContent{
    width: 100%;
    height: calc(100% - 54px);
    padding-bottom: 54px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .optContent{
    flex: 1; /* 平分高度 */
    display: flex;
    flex-direction: column;
    .rightTitle{
      font-weight: 400;
      font-size: 14px;
      color: #323233;
      line-height: 44px;
    }
  }
  }

  .optFooter{
    position: fixed;
    bottom: 0px;
    left: 0px;
    z-index: 99999;
    width: 100%;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 20px;
    min-height: 54px;

  }
}


</style>
