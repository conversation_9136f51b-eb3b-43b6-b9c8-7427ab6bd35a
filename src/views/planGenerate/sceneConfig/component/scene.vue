<template>
  <el-tabs type="border-card">
    <el-tab-pane label="生产助手配置">
      <assistPage ref="assistPageRef" :init-data="baseInfo" :base-data="curBaseInfo"></assistPage>
    </el-tab-pane>
    <el-tab-pane label="生产工具配置">
      <prodPage
        ref="prodPageRef"
        :init-data="baseInfo"
        :ability-collect-data="curBaseInfo.ability_collect"
        :system-ability-config-data="curBaseInfo.system_ability_config"
        :user-ability-config-data="curBaseInfo.user_ability_config"
        @updateStep="changeViews"
        @updateCollectData="updateCollectDataFn"
      ></prodPage>
    </el-tab-pane>
    <el-tab-pane label="高级配置">
      <basicsPage
        ref="basicsPageRef"
        :init-data="baseInfo"
        :instantiation-data="curBaseInfo.init_work_bench_dict"
        :ability-data="curBaseInfo.publish_ability_dict"
      ></basicsPage>
    </el-tab-pane>
    <el-tab-pane label="环境变量配置">
      <envVariables
        ref="envVariablesRef"
        :init-data="baseInfo"
        :cur-base-info="curBaseInfo"
        :global-constant-data="curBaseInfo.global_constant"
        @updateGlobalConstant="updateGlobalConstantFn"
        @updateStep="changeViews"
        @extractVariable="extractVariableFn"
      ></envVariables>
    </el-tab-pane>
  </el-tabs>
</template>
<script type="text/javascript">
import basicsPage from './basics.vue';
import assistPage from './assist.vue';
import prodPage from './prodProcess.vue';
import envVariables from './envVariables.vue';
import { extractGlobalConstantFromAbility, getInstanceInfo, updateInstanceInfo } from '@/api/planGenerateApi.js';
import { cloneDeep } from 'lodash';
export default {
  name: 'SceneCom',
  components: { envVariables, basicsPage, assistPage, prodPage },
  props: {
    id: {
      type: [String, Number],
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      tempList: [],
      baseInfo: {},
      curBaseInfo: {},
      activeStep: -1
    };
  },

  created() {
    this.getInstance();
  },
  mounted() {},
  methods: {
    // 一键还原
    restoreHandle() {
      // this.curBaseInfo = {...this.baseInfo}
      this.curBaseInfo = cloneDeep(this.baseInfo);
      this.$refs.basicsPageRef?.restoreHandle();
      this.$refs.assistPageRef?.restoreHandle();
      this.$refs.prodPageRef?.restoreHandle();
      this.$refs.envVariablesRef?.restoreHandle();
    },
    async saveHandle() {
      const basicData = this.$refs.basicsPageRef?.getData();
      this.updateAbilityDataFn(basicData.abilityTableData);
      this.updateInstanceDataFn(basicData.instantiationTableData);
      const valid = await this.$refs.assistPageRef?.valid();
      if (!valid) {
        this.$message.error('请完善【生产助手配置】选项');
        return;
      }
      const assistData = this.$refs.assistPageRef?.getData();
      this.updateBaseDataFn(assistData);
      this.$refs.prodPageRef?.saveHandle();
      this.$refs.envVariablesRef?.saveHandle();
    },
    updateAbilityDataFn(newData) {
      this.curBaseInfo.publish_ability_dict = newData;
    },
    updateInstanceDataFn(newData) {
      this.curBaseInfo.init_work_bench_dict = newData;
    },
    updateBaseDataFn(newData) {
      this.curBaseInfo.quick_reply_list = newData.quick_reply_list;
      this.curBaseInfo.agent_role_id = newData.agent_role_id;
      this.curBaseInfo.chat_history_size = newData.chat_history_size;
      this.curBaseInfo.agent_role_ids = newData.agent_role_ids;
      this.curBaseInfo.greetings = newData.greetings;
      this.curBaseInfo.user_agent_role_id = newData.user_agent_role_id;
      console.log(this.curBaseInfo, '已更新的数据');
    },
    updateCollectDataFn(abilityCollect, systemAbilityConfig, userAbilityConfig) {
     console.log('djosjdfaosjdfosdjfsodjasiojf')
     this.tempList = abilityCollect || []
      this.curBaseInfo.ability_collect = abilityCollect;
      this.curBaseInfo.system_ability_config = systemAbilityConfig;
      this.curBaseInfo.user_ability_config = userAbilityConfig.map(item => {
       const ability_type = item.saveType ? item.saveType : 'default'
       delete item.saveType
       return {
        ...item,
        ability_type: ability_type
       }
      });
      console.log(this.curBaseInfo, '最后一步已更新的数据');
    },
    updateGlobalConstantFn(globalConstant) {
      this.curBaseInfo.global_constant = globalConstant;
      console.log('环境变量已更新的数据', this.curBaseInfo);
    },
    async getInstance() {
     if( this.id == null || this.id == undefined || this.id == '' ) return
      await getInstanceInfo({
        instanceId: this.id || ''
      })
        .then((res) => {
          if (res.status === 200) {
            this.baseInfo = cloneDeep(res?.data);
            // this.baseInfo = {
            //  ...res.data,
            //  template_id_extern_binding: [
            //  {
            //   agent_template_id:'ef013fd5-dd8a-4bd9-8ec6-d7949d6965b1',
            //   external_variables:[
            //    {
            //     alias:null,
            //     key: 'test',
            //     source: 'extern',
            //     type: 'str',
            //     value: ''
            //    },
            //    {
            //     alias:null,
            //     key: 'test2',
            //     source: 'extern',
            //     type: 'str',
            //     value: ''
            //    }
            //   ],
            //  }
            // ]
            // };
            // 1. 将 tempList 转换为按 title + order 分组的映射
           const tempMap = this.tempList.reduce((map, group) => {
             const key = `${group.title}_${group.order}`;
             const configMap = group.config.reduce((innerMap, item) => {
               innerMap[item.name] = item;
               return innerMap;
             }, {});
             map[key] = configMap; // 存储每个组的 config 映射
             return map;
           }, {});
           let ability_collect = res?.data?.ability_collect
           // 2. 遍历 abilityCollectData 的所有外层对象
           ability_collect.forEach(abilityGroup => {
             const key = `${abilityGroup.title}_${abilityGroup.order}`;
             const tempConfigMap = tempMap[key]; // 找到对应的 tempList 配置组
           
             if (tempConfigMap) {
               abilityGroup.config.forEach(abilityItem => {
                 const tempItem = tempConfigMap[abilityItem.name];
                 if (tempItem?.templateList) {
                   // 合并 templateList 到当前配置项
                   abilityItem.templateList = tempItem.templateList;
                 }
               });
             }
           });
            console.log('获取实例信息', this.tempList,ability_collect);
            this.curBaseInfo = {
              ...res?.data,
              ability_collect: ability_collect
            };
          } else {
            this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg);
          }
        })
        .catch((err) => {
          console.log('err', err);
        });
    },
    // 提取环境变量
    extractVariableFn() {
      const param = this.$refs.prodPageRef.getAbilityConfig()
      extractGlobalConstantFromAbility(param).then(res => {
        console.log('提取变量结果：', res)
        if (res.status === 200) {
          this.$refs.envVariablesRef.envs = res.data || []
          const message = this.$refs.envVariablesRef.envs.length > 0 ? '变量提取成功!' : '完成，生产工具配置不包含环境变量！'
          this.$message.success(message);
        }
      }).catch((err) => {
        console.log('err', err);
        this.$message.error('变量提取失败123！');
      });
    },
    async changeViews(val) {
     const ability_collect = cloneDeep(this.curBaseInfo?.ability_collect);
     ability_collect?.forEach((item) => {
       item.config?.forEach((obj) => {
        if(obj.templateList) {
         delete obj.templateList
        }
       })
     })
      if (val === 'save') {
       updateInstanceInfo({
          instanceId: this.id || '',
          owner_id: this.curBaseInfo.owner_id,
          scene_version_id: this.curBaseInfo.scene_version_id,
          greetings: this.curBaseInfo.greetings,
          quick_reply_list: this.curBaseInfo.quick_reply_list,
          agent_role_id: this.curBaseInfo.agent_role_id,
          user_agent_role_id: this.curBaseInfo.user_agent_role_id,
          agent_role_ids: this.curBaseInfo.agent_role_ids,
          chat_history_size: this.curBaseInfo.chat_history_size,
          ability_collect: ability_collect,
          system_ability_config: this.curBaseInfo.system_ability_config,
          user_ability_config: this.curBaseInfo.user_ability_config,
          global_constant: this.curBaseInfo.global_constant,
          init_work_bench_dict: this.curBaseInfo.init_work_bench_dict,
          publish_ability_dict: this.curBaseInfo.publish_ability_dict
        })
          .then((res) => {
            if (res.status === 200) {
              this.$message.success('保存成功');
              // this.$router.push({
              //   path: '/planGenerate/index',
              //   query: {
              //     ...this.$route.query
              //   }
              // });
              this.getInstance()
            } else {
              this.$message.error(res?.data?.msg || '修改错误');
            }
          })
          .catch((err) => {
            console.log('err', err);
          });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: #fff;
  overflow: auto;
  .headerStep {
    .myStep {
      background: #fff;
      :deep(.el-step__arrow) {
        margin: 0 16px;
        &::before {
          content: '';
          position: static;
          height: 1px;
          width: 100%;
          background: #c8c9cc;
          transform: none;
          display: block;
        }
        &::after {
          display: none;
        }
      }
      :deep(.is-process) {
        color: #4068d4;
        .el-step__icon {
          color: #4068d4;
          &.is-text {
            border: none;
          }
        }
      }
      :deep(.is-success) {
        color: #000;
        border-color: #4068d4;
        .el-icon-check {
          color: #4068d4;
        }
        .el-step__icon {
          color: #4068d4;
          &.is-text {
            border: 1px solid #4068d4;
          }
        }
      }
      .empty-space {
        width: 100%;
        height: 100%;
      }
    }
  }
  .bodyContent {
    margin-top: 16px;
    width: 100%;
    height: calc(100% - 60px);
    background: #fff;
  }
}
</style>
