<template>
  <div>
    <div class="selected-list" :class="{ 'single-select': isSingleSelect }">
      <div v-if="!isSingleSelect || showSelectedList?.length === 0" class="add-role-button" @click="openDialog">
        <i class="el-icon-plus"></i>
        <span class="add-role-text">{{ placeholder || '添加角色' }}</span>
      </div>
      <el-card
        v-for="(item, index) in showSelectedList"
        :key="item.id"
        class="box-card"
        :class="{ 'selected-card': item.selected }"
        @click="toggleCardSelection(item)"
      >
        <div slot="header" class="card-header">
          <div class="img-item">
            <img v-if="item.icon" :src="item.icon" class="margin-right-12" />
            <el-tooltip class="item" effect="dark" :content="`${item.description}`" placement="top">
              <div class="role-name-container margin-right-12">
                <div class="role-name">{{ item.name }}</div>
                <div class="role-alias-text">{{ item.alias }}</div>
              </div>
            </el-tooltip>
          </div>
          <i
            class="el-icon-close"
            style="line-height: 40px; cursor: pointer;"
            @click.stop="deleteRole(item, index)"
          />
        </div>
      </el-card>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      :before-close="handleClose"
      destroy-on-close
    >
      <div>
        <div class="flex-between header-content">
          <div class="flex-center">
            <el-checkbox v-model="isSelectedAll" :disabled="isSingleSelect" label="全选" @change="selectAll"></el-checkbox>
            <el-divider direction="vertical"></el-divider>
            <span>
              <span>已选</span>
              <span class="primary-color">{{ currentSelectedList.length }}</span>
              <span>项</span>
            </span>
          </div>
          <el-input
            v-model="searchVal"
            style="width: 300px"
            :placeholder="searchPlaceholder"
            suffix-icon="el-icon-search"
            clearable
            @input="handleSetListChecked"
          />
        </div>
        <div
          v-for="item in tableData"
          :key="item.id"
          class="item-row"
          :class="{ selected: item.checked }"
        >
          <div class="margin-right-12">
            <el-checkbox v-model="item.checked" @change="selectedChanged(item)"></el-checkbox>
          </div>
          <div class="role-img margin-right-12">
            <img v-if="item.icon" :src="item.icon" />
            <!-- <img
              v-else
              src="@assets/images/listDefault.png"
              alt=""
              srcset=""
            /> -->
            <div class="role-txt">
              <div class="txt-content">
                <el-tooltip v-if="item.name || item.tool_name" effect="dark" placement="top">
                  <template #content>
                    <div>
                      {{ item.name || item.tool_name }}
                    </div>
                  </template>
                  <span class="txt-line">
                    {{ item.name || item.tool_name }}
                  </span>
                </el-tooltip>
              </div>
              <el-tooltip
                v-if="item.prompt || item.description || item?.goal"
                effect="dark"
                placement="top"
              >
                <template slot="content">
                  <div>
                    {{ item.prompt || item.description || item?.goal }}
                  </div>
                </template>
                <div class="txt-line">
                  {{ item.prompt || item.description || item?.goal }}
                </div>
              </el-tooltip>
            </div>
          </div>
          <div class="role-box">
            <div class="role-title">角色：</div>

            <el-tooltip v-if="item.name || item.tool_name" effect="dark" placement="top">
              <template slot="content">
                {{ item?.alias }}
              </template>
              <div class="role-alias inline-b">
                {{ item?.alias }}
              </div>
            </el-tooltip>
          </div>
          <div class="role-tag">
            <div class="role-title">标签：</div>
            <selectItem
              v-if="item.tags?.length"
              :array-items="
                item.tags?.map((tag) => {
                  return { key: tag.id, label: tag.name };
                })
              "
              :max-length="2"
            ></selectItem>
            <span v-else>-</span>
          </div>
        </div>
        <div class="margin-top-12 page">
          <el-pagination
            small
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="prev, pager, next"
            :total="totalPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <template slot="footer">
        <div>
          <el-button type="primary" @click="submit"> 确定 </el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script type="text/javascript">
import { cloneDeep } from 'lodash';
import { getRoleList } from '@/api/planGenerateApi';
import selectItem from '../../selectItem.vue';
import { cachedRequest } from './requestCache';
export default {
  name: 'ChooseRole',
  components: { selectItem },
  props: {
    isSingleSelect: {
      type: Boolean,
      default: false,
    },
    selectedList: {
      type: Array | String,
      default: function () {
        return [];
      }
    },
    initData: {
      type: Object,
      default () {
        return {}
      }
    },
    placeholder: {
      type: String,
      default: ''
    },
    roleType: {
      type: String,
      default: 'assistant',
      validator: function (value) {
        return ['assistant', 'user', 'manager'].includes(value)
      }
    }
  },
  data() {
    return {
      newSelectList:[],
      curSelectList:[],
      showSelectedList: [],
      currentSelectedList: [],
      dialogVisible: false,
      tableData: [],
      allTableData: [],
      isSelectedAll: false,
      searchVal: '',
      allRoleList: [],
      currentPage:1,
      pageSize:5,
      totalPage:0,
    };
  },

  watch: {
    selectedList:{
      handler(val) {
        if (val || this.isSingleSelect) {
          this.newSelectList = val
          this.initAllList()
        }
      },
      deep: true,
      immediate: true
    },
    curSelectList: {
      handler(val) {
        if (val || val == '') {
          this.showSelectedList = [];
          if (this.allRoleList && this.allRoleList.length) {
            if(typeof val === 'string' && this.isSingleSelect) {
              val = [val]
            }
            val.forEach((obj) => {
              const filter = this.allRoleList.filter((item) => item.id === obj);
              if(filter.length) {
                this.showSelectedList.push(filter[0])
              }
            });
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    dialogTitle() {
      const prefix = this.isSingleSelect ? '选择' : '选择（可多选）';
      switch(this.roleType) {
        case 'user':
          return `${prefix}用户角色`;
        case 'manager':
          return `${prefix}聊天管理员`;
        default:
          return `${prefix}生产辅助角色`;
      }
    },
    searchPlaceholder() {
      switch(this.roleType) {
        case 'user':
          return '请搜索用户角色名称';
        case 'manager':
          return '请搜索聊天管理员名称';
        default:
          return '请搜索生产辅助角色名称';
      }
    }
  },

  methods: {
    async restoreHandle(){
      this.newSelectList = this.initData.agent_role_ids
      await this.initAllList()
    },
    handleSetListChecked() {
      const list = this.allRoleList.filter((obj) => obj.name.indexOf(this.searchVal) > -1);
      this.totalPage = list.length;
      // 前端手动分页
      this.tableData = list.slice(
        (this.currentPage-1) * this.pageSize,
        this.currentPage * this.pageSize
      );
      this.tableData.forEach((res) => {
        const data = this.currentSelectedList.find((obj) => obj.id === res.id);
        if (data) {
          res.checked = true;
        }else{
          res.checked =false
        }
      });
      const allChecked = this.tableData.every((item) => item.checked === true);
      if(allChecked){
        this.isSelectedAll = true
      }else{
        this.isSelectedAll = false
      }
    },

    selectedChanged(data) {
      if(this.isSingleSelect) {
        if(data.checked) {
          this.currentSelectedList.forEach((item) => {
            item.checked = false;
          });
          // 清空选中的列表
          this.currentSelectedList = [];
          // 添加当前选中的角色
          this.currentSelectedList.push(data);
          this.tableData.forEach(item => {
            if(item.id !== data.id) {
              item.checked = false
            }
          })
        }
      }else {
        const isSelected = this.currentSelectedList.find((obj) => obj.id === data.id);
        if (data.checked && !isSelected) {
          this.currentSelectedList.push(data);
        }
        if (!data.checked && isSelected) {
          const currentIndex = this.currentSelectedList.findIndex((obj) => obj.id === data.id);
          this.currentSelectedList.splice(currentIndex, 1);
        }
        const allChecked = this.tableData.every((item) => item.checked === true);
        if(allChecked){
          this.isSelectedAll = true
        }else{
          this.isSelectedAll = false
        }
      }
    },
    deleteRole(data, index) {
      this.showSelectedList.splice(index, 1);
      this.$emit(
        'getValue',
        this.showSelectedList.map((obj) => obj.id)
      );
    },
    submit() {
      this.showSelectedList =cloneDeep(this.currentSelectedList);
      this.$emit(
        'getValue',
        this.showSelectedList.map((obj) => obj.id)
      );
      this.handleClose();
    },
    handleCurrentChange(val) {
      this.isSelectedAll = false
      this.currentPage = val;
      this.handleSetListChecked();
    },
    handleSizeChange() {
      this.currentPage = 1;
      this.handleSetListChecked();
    },
    selectAll(val) {
      console.log(val,'0999');
      if (val) {
        this.tableData.forEach((item) => {
          item.checked = true;
          const isSelected = this.currentSelectedList.find((obj) => obj.id === item.id);
          if (!isSelected) {
            this.currentSelectedList.push(item);
          }
        });
      } else {
        this.tableData.forEach((item) => {
          item.checked = false;
          const isSelected = this.currentSelectedList.find((obj) => obj.id === item.id);
          const index = this.currentSelectedList.findIndex((obj) => obj.id === item.id);
          if (isSelected) {
            this.currentSelectedList.splice(index, 1);
          }
        });
      }
    },
    handleClose() {
      this.currentSelectedList = [];
      this.dialogVisible = false;
    },
    openDialog() {
      this.currentSelectedList = cloneDeep(this.showSelectedList);
      this.isSelectedAll = false
      this.dialogVisible = true;
      this.handleSizeChange()
    },

    async initAllList() {
      try {
        const param = {
          keyword: this.searchVal,
          type: this.roleType === 'user' ? 'user' :
               this.roleType === 'manager' ? 'manager' : 'assistant'
        }
        const cacheKey = this.generateCacheKey(param);
        const res = await cachedRequest(cacheKey, () =>
          getRoleList(param)
        );
        const result = res.data;
        this.allRoleList = result || [];
        this.totalPage = result?.length;
        this.curSelectList = cloneDeep(this.newSelectList);
      } catch (error) {
        console.error('Error fetching role list:', error);
      }
    },
    generateCacheKey(params) {
     if (params == null) return String(params);
     if (typeof params !== 'object') return String(params);
     const sortedParams = Object.keys(params)
       .sort()
       .reduce((acc, key) => {
       acc[key] = params[key] === null ? 'null' : params[key]; // 统一处理null
      return acc;
    }, {});
    return JSON.stringify(sortedParams);
    },
    toggleCardSelection(item) {
      // Toggle the selected state
      this.$set(item, 'selected', !item.selected);

      // You can emit an event if needed
      this.$emit('cardSelected', item);
    }
  }
};
</script>
<style lang="scss" scoped>
.add-role-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 180px;
  height: 40px;
  border: 1px dashed #c0c4cc;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 12px;
  transition: all 0.3s;
  margin-right: 12px;

  &:hover {
    border-color: #409EFF;
    color: #409EFF;
    background-color: rgba(64, 158, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .el-icon-plus {
    font-size: 20px;
    margin-right: 8px;
  }

  .add-role-text {
    font-size: 14px;
  }
}

.selected-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start; /* 确保所有元素从顶部对齐 */
  line-height: 40px; /* 与卡片高度一致 */

  &.single-select {
    height: 40px; /* 单选模式下固定高度 */
  }
      .box-card {
        width: 180px;
        /* 移除浮动，使用flex布局 */
        margin-right: 12px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #e4e7ed;
        padding: 0;
        height: 40px; /* 设置固定高度与加号按钮一致 */
        box-sizing: border-box;

        :deep(.el-card__body) {
          padding: 0;
          display: none; /* 隐藏body，因为所有内容都在header中 */
        }

        :deep(.el-card__header) {
          padding: 0;
          border-bottom: none;
          height: 40px; /* 固定高度 */
          box-sizing: border-box;
        }

        &.selected-card {
          border-color: #409EFF;
          box-shadow: 0 0 8px rgba(64, 158, 255, 0.1);
          background-color: rgba(64, 158, 255, 0.05);

          .role-name {
            color: #409EFF;
          }
        }

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }
    .card-header {
      width: 100%;
      margin: 0;
      display: flex;
      padding: 0 10px;
      height: 40px;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .img-item {
        width: calc(100% - 16px);
        display: flex;
        align-items: center;
        height: 40px; /* 与卡片高度保持一致 */
        img {
          width: 24px;
          height: 24px;
          transition: transform 0.3s ease;
          object-fit: contain;
        }
      }

      .el-icon-close {
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
      }
    }

    &:hover img {
      transform: scale(1.1);
    }

    &:hover .el-icon-close {
      color: #f56c6c;
    }

    .el-icon-close {
      transition: all 0.3s;
      color: #c0c4cc;
    }
  }
}
.page {
  height: 30px;
  justify-content: flex-end;
  display: flex;
  margin-top: 10px;
  ::v-deep .el-pagination:not(.new-paper) button {
    width: 30px;
  }
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-center {
  display: flex;
  align-items: center;
}
.primary-color {
  color: var(--el-color-primary);
}
.header-content {
  margin-bottom: 12px;
}
.role-item {
  &:hover {
    background-color: var(--el-color-neutral-light-5);
  }
  &.selected {
    background-color: var(--el-color-primary-light-9);
  }
}
.margin-right-12 {
  margin-right: 12px;
}
.margin-top-12 {
  margin-top: 12px;
}
.margin-bottom-12 {
  margin-bottom: 12px;
}
.float-right {
  justify-content: flex-start;
}
.item-row {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebecf0;
  cursor: pointer;
  .role-img {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: calc(100% - 26px - 120px - 170px - 24px);
    img {
      width: 40px;
      height: 40px;
      vertical-align: middle;
    }
    .role-txt {
      width: calc(100% - 60px);
      margin-left: 20px;
      .txt-content {
        width: 100%;
        line-height: 24px;
        font-weight: 500;
        font-size: 14px;
        display: flex;
        margin-bottom: 2px;
        color: #1d2129;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .txt-line {
        width: 100%;
        height: 24px;
        line-height: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .role-box {
    width: 120px;
    margin-right: 12px;
  }
  .role-title {
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 22px;
    color: #646566;
  }
  .role-tag {
    width: 170px;
    margin-right: 12px;
    :deep(.el-dropdown) {
      display: flex;
    }
  }
}
.role-desc {
  width: calc(100% - 50px);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.inline-b {
  display: inline-block;
}
.role-alias {
  background-color: #d7eedb;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 6px;
  max-width: 100px;
  width: fit-content;
  border-radius: 3px;
  font-size: 14px;
  border: 1px solid #d7eedb;
  color: #39ab4c;
  height: 24px;
  line-height: 24px;
}
.line-height-30 {
  line-height: 30px;
}
.role-name-container {
  display: flex;
  flex-direction: column;
  max-width: calc(100% - 36px);
  height: 40px; /* 调整为与卡片高度一致 */
  justify-content: center; /* 垂直居中两行文本 */
  box-sizing: border-box;

  .role-name {
    font-weight: 500;
    font-size: 13px;
    color: #323232;
    line-height: 18px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }

  .role-alias-text {
    font-size: 11px;
    color: #646566;
    line-height: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }
}
/* 响应式布局 - 仅在非单选模式下应用 */
.selected-list:not(.single-select) {
  @media (max-width: 1800px) {
    .box-card, .add-role-button {
      width: calc(20% - 12px); /* 5 cards per row on large screens */
    }
  }

  @media (max-width: 1500px) {
    .box-card, .add-role-button {
      width: calc(25% - 12px); /* 4 cards per row on medium-large screens */
    }
  }

  @media (max-width: 1200px) {
    .box-card, .add-role-button {
      width: calc(33.333% - 12px); /* 3 cards per row on medium screens */
    }
  }

  @media (max-width: 992px) {
    .box-card, .add-role-button {
      width: calc(50% - 12px); /* 2 cards per row on small screens */
    }
  }

  @media (max-width: 576px) {
    .box-card, .add-role-button {
      width: 100%; /* 1 card per row on very small screens */
      margin-right: 0;
    }
  }
}
</style>
