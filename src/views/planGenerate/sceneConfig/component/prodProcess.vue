<template>
 <div class="prodSetting">
  <el-tabs class="ability-config-tabs" v-model="activeTab" tab-position="left" @tab-click="handleTabClick">
   <el-tab-pane :key="0" label="API生成" :name="'0'">
    <el-form ref="formRef" :model="formData" class="form-box">
     <div v-for="(item, index) in configList" :key="item.title">
      <div class="el-form-item--group-title-left">{{ item.title }}</div>
      <div v-for="(obj, ind) in item.config" :key="ind">
       <el-form-item label-width="168px" class="form-item" :label="obj.name" :prop="`${obj.ability_type}_${obj.name}`" :rules="rules(obj)">
        <template v-if="obj?.extern_mapping.length > 0" #label>
         <i style="cursor: pointer; margin-right: 8px;" :class="obj.isDown ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          @click="toggleArrow(obj)"></i>
         {{ obj.name }} <span class="variable-count">({{ getFilledVariableCount(obj) }}/{{ obj.extern_mapping.length }})</span>
        </template>
        <el-select v-model="formData[`${obj.ability_type}_${obj.name}`]" :placeholder="`请输入${obj.name}`"
         :remote-method="(query) => searchData(query, obj.ability_type)" clearable filterable
         :multiple="obj.multi_select" remote style="width: 200px;" @focus="(event) => searchData('', obj.ability_type)"
         @change="(query) => changeFn(query, obj.ability_type, ind, index,obj)">
         <el-option v-for="item in optionsList[obj.ability_type]" :key="item.id" :label="item.name" :value="item.id">
          <span style="float: left">{{ item.name }}</span>
          <span class="rightItem">
           <selectItem :array-items="item.tags?.map((tag) => {
            return { key: tag.id, label: tag.name };
           })
            " :max-length="2"></selectItem>
          </span>
         </el-option>
        </el-select>
        <span class="midle-switch" style="margin: 0 10px;">
         <el-tooltip content="是否显示产物" placement="top">
          <el-switch v-model="obj.is_ability_result"></el-switch>
         </el-tooltip>
        </span>
        <span class="component">
         <el-tooltip content="产物格式" placement="top">
          <el-select class="disply-component" v-model="obj.display_component" disabled placeholder="请选择展示方式" clearable>
           <el-option v-for="item in displayComponentOptions" :key="item.code" :label="item.name" :value="item.code">
           </el-option>
          </el-select>
         </el-tooltip>
        </span>
        <span class="role-component">
         <choose-role ref="chooseRoleRef" :init-data="initData" :selected-list="obj.default_agent_role"
          :isSingleSelect="true" @getValue="(val) => getRoleIds(obj, val)" placeholder="选择默认角色"></choose-role>
        </span>
       </el-form-item>
        <div v-if="obj?.extern_mapping.length > 0 && !obj.isDown && formData.extern_mapping[obj?.value]?.length >0 "
          style="margin: 8px 0 16px 42px; display: flex; flex-wrap: nowrap; gap: 16px;
          background-color: #FAFBFF; padding: 12px 16px; border-radius: 4px;
          border: 1px solid #EBEEF5; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">
        <div v-for="(extItem, extIndex) in obj?.extern_mapping"  :key="`${obj.ability_type}_${obj.name}-${extIndex}`" style="flex: 0 0 calc(50% - 8px);">
         <el-form-item class="varias"  :label="extItem.key" :prop="`extern_mapping.${obj.value}[${extIndex}].value`"
          :rules="obj.is_required ? { required: true, message: `请选择${extItem.key}`, trigger: 'change' } : []">
          <template #label>

           <el-tooltip placement="top">
            <template #content>
             {{ extItem.key }}
            </template>
            <i style="cursor: pointer; margin-right: 8px;" class="el-icon-question"
          ></i>
           </el-tooltip>
           {{ extItem.key }}
          </template>
          <el-select v-model="formData.extern_mapping[obj?.value][extIndex].value" :placeholder="`请输入${extItem.key}`"
            clearable filterable
           :multiple="obj.multi_select" remote  @focus="(event) => searchData('', extItem.value)"
           @change="(query) => realtimeUpdateAbilityConfig(query, obj)">
           <el-option v-for="t in obj?.templateList" :key="`${obj.ability_type}_${obj.name}-${extIndex}-${t?.name}`" :label="t?.name" :value="t.name">

           </el-option>
          </el-select>
         </el-form-item>

        </div>
       </div>
      </div>
     </div>
    </el-form>
   </el-tab-pane>
   <!-- 系统能力配置 -->
   <el-tab-pane v-for="(systemAbilityItem, index) in systemAbilityConfig" :key="'100' + index"
    :label="systemAbilityItem.name" :name="'100' + index">
    <el-form :ref="'systemAbility.' + index" :model="systemAbilityItem" label-width="168px" class="form-box">
     <el-form-item class="form-item" :key="'systemAbility.' + index" :label="systemAbilityItem.name" prop="value"
      :rules="rules(systemAbilityItem, 'change')">
      <el-select v-model="systemAbilityItem.value" :placeholder="`请输入${systemAbilityItem.name}`"
       :remote-method="(query) => searchData(query, systemAbilityItem.ability_type)" clearable filterable
       :multiple="systemAbilityItem.multi_select" remote style="width: 200px;"
       @focus="(evnet) => searchData('', systemAbilityItem.ability_type)"
       @change="(query) => systemAbilityChangeFn(query, systemAbilityItem.ability_type, index)">
       <el-option v-for="item in optionsList[systemAbilityItem.ability_type]" :key="item.id" :label="item.name"
        :value="item.id">
        <span style="float: left">{{ item.name }}</span>
        <span class="rightItem">
         <selectItem :array-items="item.tags?.map((tag) => {
          return { key: tag.id, label: tag.name };
         })
          " :max-length="2">
         </selectItem>
        </span>
       </el-option>
      </el-select>
      <span class="midle-switch" style="margin: 0 10px;">
       <el-tooltip content="是否显示产物" placement="top">
        <el-switch v-model="systemAbilityItem.is_ability_result" disabled></el-switch>
       </el-tooltip>
      </span>
      <span class="component">
       <el-tooltip content="产物格式" placement="top">
        <el-select class="disply-component" v-model="systemAbilityItem.display_component" placeholder="请选择展示方式" clearable disabled>
         <el-option v-for="i in displayComponentOptions" :key="i.code" :label="i.name" :value="i.code">
         </el-option>
        </el-select>
       </el-tooltip>
      </span>
      <span class="role-component">
       <choose-role ref="chooseRoleRef" :init-data="initData" :selected-list="systemAbilityItem.default_agent_role"
        :isSingleSelect="true" @getValue="(val) => getRoleIds(systemAbilityItem, val)" placeholder="选择默认角色"></choose-role>
      </span>
     </el-form-item>
     <el-form-item label="默认提示词">
      <el-input style="width: 68%;" v-model="systemAbilityItem.default_chat_message" placeholder="请输入提示词"
       type="textarea" :autosize="{ minRows: 3, maxRows: 3 }">
      </el-input>
     </el-form-item>
    </el-form>
   </el-tab-pane>
   <!-- 用户自定义能力配置 -->
   <el-tab-pane v-for="(userAbilityItem, index) in userAbilityConfig" :key="'200' + index" :name="'200' + index">
    <template #label>
     <div class="tab-label-container">
      <span class="tab-title">{{ userAbilityItem.name }}</span>
      <div v-if="activeTab === '200' + index">
       <div class="tab-actions">
        <el-button type="text" size="mini" icon="el-icon-edit" @click.stop="editTabName(index)"></el-button>
        <el-button type="text" size="mini" icon="el-icon-delete" @click.stop="confirmDeleteTab(index)"></el-button>
       </div>
      </div>
     </div>
    </template>
    <el-form :ref="'userAbility.' + index" :model="userAbilityItem" label-width="168px" class="form-box">
     <el-form-item class="form-item" :key="'userAbility.' + index" :label="userAbilityItem.name" prop="value"
      :rules="rules(userAbilityItem, 'change')">
      <el-select v-model="userAbilityItem.value" :placeholder="`请输入${userAbilityItem.name}`"
       :remote-method="(query) => searchData(query, userAbilityItem.ability_type)" clearable filterable
       :multiple="userAbilityItem.multi_select" remote style="width: 200px;"
       @focus="(event) => searchData('', userAbilityItem.ability_type)"
       @change="(query) => userAbilityChangeFn(query, userAbilityItem.ability_type, index)">
       <el-option v-for="item in optionsList[userAbilityItem.ability_type]" :key="item.id" :label="item.name"
        :value="item.id">
        <span style="float: left">{{ item.name }}</span>
        <span class="rightItem">
         <selectItem :array-items="item.tags?.map((tag) => {
          return { key: tag.id, label: tag.name };
         })
          " :max-length="2">
         </selectItem>
        </span>
       </el-option>
      </el-select>
      <span class="midle-switch" style="margin: 0 10px;">
       <el-tooltip content="是否显示产物" placement="top">
        <el-switch v-model="userAbilityItem.is_ability_result" disabled></el-switch>
       </el-tooltip>
      </span>
      <span class="component">
       <el-tooltip content="产物格式" placement="top">
        <el-select class="disply-component" v-model="userAbilityItem.display_component" placeholder="请选择展示方式">
         <el-option v-for="i in displayComponentOptions" :key="i.code" :label="i.name" :value="i.code">
         </el-option>
        </el-select>
       </el-tooltip>
      </span>
      <span class="role-component">
       <choose-role ref="chooseRoleRef" :init-data="initData" :selected-list="userAbilityItem.default_agent_role"
        :isSingleSelect="true" @getValue="(val) => getRoleIds(userAbilityItem, val)" placeholder="选择默认角色"></choose-role>
      </span>
     </el-form-item>
     <el-form-item label="默认提示词">
      <el-input style="width: 68%;" v-model="userAbilityItem.default_chat_message" placeholder="请输入提示词" clearable
       type="textarea" :autosize="{ minRows: 3, maxRows: 3 }">
      </el-input>
     </el-form-item>
    </el-form>
   </el-tab-pane>
   <el-tab-pane key="300" name="300">
    <template #label>
     <div class="tab-label-container">
      <!-- 添加标签页按钮 -->
      <el-button slot="add" type="text" @click="addTab" class="add-tab-btn" icon="el-icon-plus">
       新增智能工具
      </el-button>
     </div>
    </template>
   </el-tab-pane>
   <!-- 新增标签名称的对话框 -->
   <el-dialog title="新增智能工具" :visible.sync="addDialogVisible" width="30%">
    <el-input v-model="addTabName" placeholder="请输入智能工具名称" maxlength="20" :show-word-limit="true"></el-input>
    <span slot="footer" class="dialog-footer">
     <el-button @click="addDialogVisible = false">取 消</el-button>
     <el-button type="primary" @click="confirmAddTabName">确 定</el-button>
    </span>
   </el-dialog>
   <!-- 编辑标签名称的对话框 -->
   <el-dialog title="编辑自定义能力名称" :visible.sync="editDialogVisible" width="30%">
    <el-input v-model="editingTabName" placeholder="请输入标签名称" maxlength="20" :show-word-limit="true"></el-input>
    <span slot="footer" class="dialog-footer">
     <el-button @click="editDialogVisible = false">取 消</el-button>
     <el-button type="primary" @click="confirmEditTabName">确 定</el-button>
    </span>
   </el-dialog>
  </el-tabs>
 </div>
</template>
<script type="text/javascript">
import chooseRole from './chooseRole.vue'
import { getTemplateList, queryDisplayComponents } from '@/api/planGenerateApi';
import selectItem from '../../selectItem.vue';
import { cloneDeep } from 'lodash';

export default {
 name: 'InterfaceCom',
 components: { selectItem, chooseRole },
 props: {
  abilityCollectData: {
   type: Array,
   default() {
    return [];
   }
  },
  systemAbilityConfigData: {
   type: Array,
   default() {
    return [];
   }
  },
  userAbilityConfigData: {
   type: Array,
   default() {
    return [];
   }
  },
  initData: {
   type: Object,
   default() {
    return {};
   }
  }
 },
 data() {
  return {
   pendingPromises: [],
   hasProcessedConfig: false,
   formList: [],
   newTab: {},
   resultprocessConfig: [],
   // templateIdExternBinding: [],
   activeTab: '0',

   addDialogVisible: false,
   addTabName: '',
   // 编辑用户自定义能力名称相关
   editDialogVisible: false,
   currentEditIndex: -1,
   editingTabName: '',

   optionsList: {},
   optionsQueriedList: [],
   formData: {
    extern_mapping: {}
   },
   configList: [],
   systemAbilityConfig: [],
   userAbilityConfig: [],
   curCollectData: [],
   tempList: [],
   displayComponentOptions: []
  };
 },
 watch: {
  abilityCollectData: {
   immediate: true,
   deep: true,
   handler(val) {
    if (val) {
     // console.log('wathchabilityCollectData',JSON.stringify(val))
     this.configList = cloneDeep(val);
     this.initFn();
    }
   }
  },
  systemAbilityConfigData: {
   immediate: true,
   deep: true,
   handler(val) {
    if (val) {
     this.systemAbilityConfig = cloneDeep(val);
     this.initFn();
    }
   }
  },
  userAbilityConfigData: {
   immediate: true,
   deep: true,
   handler(val) {
    if (val) {
     const handleVal = val?.map(item => {
      return {
       ...item,
       ability_type: 'default,general_use',
       saveType: item.ability_type
      }
     })
     this.userAbilityConfig = cloneDeep(handleVal);
     this.initFn();
    }
   }
  }
 },
 created() {
 },
 mounted() {
  this.initFn();
  this.getDisplayComponents();
 },
 methods: {
  realtimeUpdateAbilityConfig(val, ability_type) {
  //  this.configList.forEach(item => {
  //   item.config.forEach(obj => {
  //     if (obj.ability_type === ability_type) {
  //       obj.value = value;
  //     }
  //   })
  // })

   // this.$set(this.configList[activeValue].config[ind], 'value', val);
  },
  toggleArrow(item) {
    this.$set(item, 'isDown', !item.isDown);
  },
  initFn() {
   if(this.configList.length > 0) {
    this.resultprocessConfig = this.processConfig(this.configList) || []
   }
   this.configList.forEach(item => {
    item.config.forEach(obj => {
      if (obj?.extern_mapping?.length > 0) {
        this.$set(obj, 'isDown', true); // 默认折叠
      }
      console.log('formData', this.initData, this.formData);
      // const config  = this.mergeTemplates(val.config,this.configList.config)
    })
   })

   if (this.configList && this.configList.length > 0) {
    this.configList.forEach((item, ind) => {
     this.configList[ind].config.forEach(async (obj, index) => {
      await this.searchData('', this.configList[ind].config[index].ability_type);
      const attr = obj.ability_type + '_' + obj.name;
      this.$set(
       this.formData,
       attr,
       this.configList[ind].config[index].value
      );
     });
    });
    if (this.systemAbilityConfig && this.systemAbilityConfig.length > 0) {
     this.systemAbilityConfig.forEach(async (element, index) => {
      await this.searchData('', this.systemAbilityConfig[index].ability_type);
     });
    }
    ;
    if (this.userAbilityConfig && this.userAbilityConfig.length > 0) {
     this.userAbilityConfig.forEach(async (element, index) => {
      await this.searchData('', this.userAbilityConfig[index].ability_type);
     });
    }
   }
  },
  processConfig (jsonData) {
 // 提取所有的 config 数组
 const allConfigs = jsonData.flatMap(item => item.config);

 // 使用 Set 去重并提取所有的 name
 const uniqueNames = [...new Set(allConfigs.map(config => config.name))];

 // 构建新的数组
 const result = uniqueNames.map((name, index) => {
   // 获取当前 name 之前的所有 name 作为 options
   const options = uniqueNames.slice(0, index).map(prevName => ({
     name: prevName,
     value: ""
   }));

   return {
     name: name,
     options: options
   };
 });

 return result;
},
  getRoleIds(systemAbilityItem, roleIds) {
   systemAbilityItem.default_agent_role = roleIds[0]
   console.log("systemAbilityItem:", systemAbilityItem, roleIds);
   console.log("roleIds:", systemAbilityItem);
  },
  getAbilityConfig() {
   return {
    ability_collect: this.configList,
    system_ability_config: this.systemAbilityConfig,
    user_ability_config: this.userAbilityConfig
   }
  },
  getDisplayComponents() {
   queryDisplayComponents({})
    .then((res) => {
     if (res.status === 200 || res.data?.status === 200) {
      this.displayComponentOptions = res.data;
     }
     console.log('前端展示类型：', this.displayComponentOptions);
    })
    .finally(() => {
    });
  },
  restoreHandle() {
   this.configList = cloneDeep(this.initData.ability_collect);
   this.systemAbilityConfig = cloneDeep(this.initData.system_ability_config);
   this.userAbilityConfig = cloneDeep(this.initData.user_ability_config);
   this.initFn();
   this.activeTab = '0';
  },
  // 处理标签页点击事件
  handleTabClick(tab) {
   this.activeTab = tab.name;
  },
  // 编辑标签名称
  editTabName(index) {
   this.currentEditIndex = index;
   this.editingTabName = this.userAbilityConfig[index].name;
   this.editDialogVisible = true;
  },

  // 添加标签页
  addTab() {
   this.addDialogVisible = true;
  },
  addAbilityNameCheck(newName) {
   let checkResult = true;
   this.systemAbilityConfig.forEach(item => {
    if (item.name === newName) {
     checkResult = false;
    }
   });
   this.userAbilityConfig.forEach(item => {
    if (item.name === newName) {
     checkResult = false;
    }
   });
   return checkResult;
  },
  confirmAddTabName() {
   const nameCheck = this.addAbilityNameCheck(this.addTabName);
   if (nameCheck === false) {
    this.$message({
     type: 'warning',
     message: `已存在“${this.addTabName}”能力!`
    });
    return
   }
   const newIndex = this.userAbilityConfig.length;
   this.newTab = {
    name: this.addTabName,
    ability_type: 'default',
    is_required: true,
    multi_select: false,
    is_ability_result: true,
    display_component: 'EnnMarkdown',
    value: ''
   };
   this.userAbilityConfig.push(this.newTab);
   this.addDialogVisible = false;
   this.addTabName = '';
   this.activeTab = '200' + newIndex;
   // console.log('activeTab=====', this.activeTab);
   this.searchData('', 'default')
  },
  editAbilityNameCheck(newName, editIndex) {
   let checkResult = true;
   this.systemAbilityConfig.forEach(item => {
    if (item.name === newName) {
     checkResult = false;
    }
   });
   this.userAbilityConfig.forEach((item, index) => {
    if (item.name === newName && index !== editIndex) {
     checkResult = false;
    }
   });
   return checkResult;
  },
  // 确认编辑标签名称
  confirmEditTabName() {
   if (this.currentEditIndex !== -1) {
    const nameCheck = this.editAbilityNameCheck(this.editingTabName, this.currentEditIndex);
    if (nameCheck === false) {
     this.$message({
      type: 'warning',
      message: `已存在“${this.editingTabName}”能力!`
     });
     return
    }
    this.userAbilityConfig[this.currentEditIndex].name = this.editingTabName;
    this.editDialogVisible = false;
   }
  },
  // 确认删除标签页
  confirmDeleteTab(index) {
   // 弹出确认对话框
   this.$confirm('确定要删除此自定义能力吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
   }).then(() => {
    // 用户确认删除
    this.deleteTab(index);
    this.$message({
     type: 'success',
     message: '删除成功!'
    });
   });
  },

  // 删除标签页
  deleteTab(index) {
   // 如果删除的是当前激活的标签页，切换到前一个标签页或首个
   if (this.userAbilityConfig.length <= 1) {
    this.activeTab = '0';
   } else {
    this.activeTab = index > 0
     ? '200' + (index - 1)
     : '200' + (index + 1);
   }
   // console.log('activeTab=====', this.activeTab);
   // 删除标签页
   this.userAbilityConfig.splice(index, 1);
  },


  changeFn(val, type, ind, activeValue,object) {
   if(this.resultprocessConfig.length > 0) {
    const templateList = this.resultprocessConfig?.find(item => object.name === item.name)?.options || []
    if(templateList) {
     this.configList[activeValue].config[ind].templateList = templateList
    }
   }
   if(this.optionsList[type]?.length > 0) {
    const abilityList = this.optionsList[type]
    const obj =  abilityList?.find(item => item.id === val)
    if(obj?.external_variables) {
     if(obj?.external_variables?.length > 0 && object?.extern_mapping?.length > 0) {
      obj.extern_mapping = this.mergeMappings(obj?.external_variables,object?.extern_mapping,false)
     }else {
      obj.extern_mapping = obj?.external_variables
     }
     const collect = this.configList[activeValue]?.config[ind]
     if(collect?.extern_mapping ) {
      this.$set(this.configList[activeValue].config[ind], 'extern_mapping', obj.extern_mapping);
      this.formData.extern_mapping[val] = obj.extern_mapping
     }
    }
   }
   this.$set(this.configList[activeValue].config[ind], 'value', val);
  },
  systemAbilityChangeFn(val, item, index) {
   this.$set(this.systemAbilityConfig, 'value', val);
  },
  userAbilityChangeFn(val, item, index) {

    const selectAbiltiy = this.tempList ?.find(i => i.id === val)
     if(selectAbiltiy) {
      this.userAbilityConfig[index].saveType = selectAbiltiy.ability_type;
      // item.ability_type = selectAbiltiy.ability_type
     }
   this.$set(this.userAbilityConfig, 'value', val);
  },
  rules(obj, triggerType = 'blur') {
   return {
    required: obj.is_required,
    message: `${obj.name}不能为空`,
    trigger: triggerType
   };
  },
  async searchData(query, type) {
   if (this.optionsQueriedList.includes(type) && (query === null || query === undefined || query === '')) {
    return;
   }
   console.log('djaosdjafpsdjapsjdp')
   if (query === null || query === undefined || query === '') {
    this.optionsQueriedList.push(type);
   } else {
    this.optionsQueriedList = this.optionsQueriedList.filter(item => item !== type);
   }
      // 创建并保存当前请求的 Promise
      const requestPromise = getTemplateList({
    page: 1,
    page_size: 999,
    keyword: query,
    ability_type: type === 'default' ? 'default,general_use' : type,
    resource_type: 2,
    status: 1
   }).then((res) => {
    console.log('dddd', type, this.optionsList);
    if (this.formList.length < 1 && (type === 'default,general_use' || type === 'default')) {
     this.tempList = res.data || [];
    }
    if (res.status === 200) {
     this.$set(
      this.optionsList,
      type,
      res.data?.map((item) => {
       return { id: item.id, name: item.name, tags: item.tags, external_variables: item.external_variables };
      })
     );
     console.log('djaspdpsadj33-333-3-33-3gggggggg');
    }
   });
   // 将 Promise 加入队列
   this.pendingPromises = this.pendingPromises || [];
   this.pendingPromises.push(requestPromise);
      // 确保所有请求完成后执行一次配置处理
      requestPromise.finally(() => {
    this.pendingPromises = this.pendingPromises.filter(p => p !== requestPromise);
    if (this.pendingPromises.length === 0 && !this.hasProcessedConfig) {
     this._processConfig();
     this.hasProcessedConfig = true;
    }
   });
   await requestPromise;
  },
  _processConfig() {
   console.log('djaspdpsadj33-333-3-33-3', this.optionsList);
   this.configList?.forEach(item => {
    item.config.forEach(this.processConfigItem);
   });
   this.systemAbilityConfigData.forEach(this.processConfigItem);
   this.userAbilityConfig.forEach(this.processConfigItem);
   console.log('dsajoasodsds', this.optionsList);
},
  processConfigItem ( obj ) {
   console.log('osvjvjasdasd-asd-s-a-1111',this.optionsList)
   // 统一trim处理
   const abilityType = obj.ability_type?.trim() || ''
   // 新增逻辑：匹配选中项
 const selectedAbility = this.optionsList[abilityType]?.find((item) => {
    // 处理可能的类型差异（数字/字符串）
    return String(item.id) === String(obj.value)
  })
  if(selectedAbility?.external_variables?.length > 0) {
   if( obj?.extern_mapping?.length > 0 ) {
    obj.extern_mapping = this.mergeMappings(selectedAbility?.external_variables,obj?.extern_mapping,false)
   }else {
    obj.extern_mapping = selectedAbility?.external_variables
   }
   this.formData.extern_mapping[obj.value] = obj.extern_mapping
   const templateList = this.resultprocessConfig?.find(item => obj.name === item.name)?.options || []
   this.$set(obj, 'templateList', templateList);
  }
     },
  saveHandle() {
   this.$refs.formRef.validate((valid) => {
    if (valid) {
     let dynamicValid = true;
     for (let i = 0; i < this.systemAbilityConfig.length; i++) {
      const form = this.$refs[`systemAbility.${i}`][0];
      if (form) {
       form.validate((subValidate) => {
        if (!subValidate) {
         dynamicValid = false;
        }
       });
      }
     }
     if (dynamicValid) {
      for (let i = 0; i < this.userAbilityConfig.length; i++) {
       const form = this.$refs[`userAbility.${i}`][0];
       if (form) {
        form.validate((subValidate) => {
         if (!subValidate) {
          dynamicValid = false;
         }
        });
       }
      }
     }
     if (!dynamicValid) {
      this.$message.error('请完善【生产工具配置】选项');
      throw new Error('请完善【生产工具配置】选项');
     }
     // 执行提交操作
     console.log('====saveHandle======', this.systemAbilityConfig);
     this.$emit('updateCollectData', this.configList, this.systemAbilityConfig, this.userAbilityConfig);
     // this.changeViews('save');
    } else {
     this.$message.error('请完善【生产工具配置】选项');
     throw new Error('请完善【生产工具配置】选项');
    }
   });
  },
  changeViews(val) {
   this.$emit('updateStep', val);
  },
  handleClick(tab, event) {
   this.optionsList = {};
  },
  mergeMappings(inputMapping, variables, type) {
 // 创建变量查找表（O(1) 查找复杂度）
 const variableMap = new Map(
  variables.map(v => [v.key, v])
 );
 return inputMapping.map((item) => {
  const variable = variableMap.get(item.key);
  if (type) {
   return {
    ...item,
    type: variable?.type || item.type,
    source: variable?.source || item.source,
   }
  } else {
   return {
    type: variable?.type || item.type, // 非严格模式下回退原始类型
    value: variable?.value || item.value, // 非严格模式下回退原始值
    source: variable?.source || item.source, // 非严格模式下回退原始 source
    variable: variable?.variable || item.variable, // 非严格模式下回退原始变量
    key: item.key, // 保留原始 key
   };
  }
 })
},
  mergeTemplates(inputMapping, variables, type) {
 // 创建变量查找表（O(1) 查找复杂度）
 const variableMap = new Map(
  variables.map(v => [v.key, v])
 );
 return inputMapping.map((item) => {
  const variable = variableMap.get(item.key);
   return {
    ...item,
    templateList: variable?.type || item.type,
   }
 })
 },

 getFilledVariableCount(obj) {
  if (!obj.value || !this.formData.extern_mapping[obj.value]) {
   return 0;
  }
  return this.formData.extern_mapping[obj.value].filter(v => v.value).length;
 }
 }
};
</script>
<style lang="scss" scoped>
.ability-config-tabs {
 :deep(.el-tabs__content) {
  overflow-x: auto;
 }
}
:deep(.role-form) {
    .box-card {
     width: 180px !important;
    }
    .el-form-item__content {
     line-height: 22px !important;
    }
   }

/* 调整choose-role组件在表单项中的显示 */
:deep(.choose-role) {
    display: inline-flex;
    align-items: center;
    height: 36px;

    .el-card {
      height: 36px;
      line-height: 36px;
      margin: 0;
    }

    .el-card__body {
      padding: 0 10px !important;
      display: flex;
      align-items: center;
      height: 36px;
    }

    .el-tag {
      margin: 0;
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
    }
}

:deep(.role-component) {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    white-space: nowrap;
    margin-left: 20px;
    height: 40px; /* 统一高度 */

    .box-card {
     width: 180px !important;
     margin-top: 0 !important;
     margin-bottom: 0 !important;
    }

    .el-form-item__content {
     line-height: 40px !important;
     height: 40px;
    }

    .el-card__body {
     padding: 10px !important;
     display: flex;
     align-items: center;
    }
   }
:deep(.varias) {
 margin-left: 12px;
 display: flex;
 flex-wrap: nowrap;
 .el-form-item__label{
  display: flex;
  align-items: center;
 }
}
.el-form-item--group-title-left {
 margin-bottom: 16px;
 display: flex;
 align-items: center;
 transition: all 0.3s ease;

 &::before {
  content: "";
  width: 2px;
  height: 12px;
  background: var(--color-primary);
  margin-right: 8px;
 }

 .variable-count {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
  align-self: flex-end;
 }
}

.tab-label-container {
 display: flex;
 align-items: center;
 justify-content: space-between;
 width: 100%;
}

.tab-title {
 flex-grow: 1;
 text-align: right;
 /* 文字右对齐 */
 padding-right: 0px;
 /* 可选：添加一些右侧间距 */
}

.tab-actions {
 display: flex;
 align-items: center;
}

.prodSetting {
 padding: 10px 16px;
 width: 100%;
 height: 100%;
 position: relative;
 display: flex;
 flex-direction: column;
 align-items: flex-start;

 .el-tabs {
  width: 100%;
 }

 .form-box {
  width: 100%;
  min-width: 900px;
  overflow-x: auto;
  :deep(.el-form-item) {
   margin-bottom: 16px;
   white-space: nowrap;
  }
  :deep(.form-item) {
   .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 20px;
    height: 40px; /* 统一高度 */
   }
   .el-select {
    width: 200px;
    flex-shrink: 0;
   }
   .component {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    height: 40px; /* 统一高度 */
    .disply-component {
     width: 150px !important;
     flex: none;
    }
   }
   .shrink-0 {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 40px; /* 统一高度 */
   }
   .midle-switch {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    white-space: nowrap;
    margin: 0 10px !important;
    height: 40px; /* 统一高度 */
   }
  }
 }

 .margin-left-22 {
  margin-left: 22px;
 }

 ::v-deep .el-tabs__active-bar {
  display: none !important;
 }

 .el-tabs__item.is-active i {
  color: #969799;
 }

 .optFooter {
  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 99999;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 20px;
  min-height: 54px;
 }
}

.rightItem {
 float: right;
 display: flex;
 align-items: center;
 height: 100%;

 :deep(.el-dropdown) {
  display: flex;
 }
}

/* 调整role-component样式，确保水平对齐 */
.role-component {
 display: flex;
 align-items: center;
 flex-shrink: 0;
 white-space: nowrap;
 margin-left: 20px;
 height: 40px; /* 统一高度 */
}

:deep(.role-component) {
 .box-card {
  width: 180px !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
 }

 .el-form-item__content {
  line-height: 40px !important;
  height: 40px;
 }

 .el-card__body {
  padding: 10px !important;
  display: flex;
  align-items: center;
 }

 .el-card {
  height: 36px;
  margin: 0;
  display: flex;
  align-items: center;
 }

 .el-tag {
  margin: 0;
  height: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
 }
}
</style>
