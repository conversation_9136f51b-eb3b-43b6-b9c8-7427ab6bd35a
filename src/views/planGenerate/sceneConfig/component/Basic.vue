<template>
  <div class="basic-content">
    <el-form ref="form" label-position="right" :model="basicData" :rules="rules">
      <el-form-item prop="ownerId">
        <div style="display: flex; flex-direction: row; align-items: center">
          <div style="flex: 1">
            <el-select
              ref="onwerSelect"
              v-model="basicData.ownerId"
              style="width: 100%"
              multiple
              filterable
              remote
              placeholder="请选择贡献专家"
              :remote-method="searchUser"
              clearable
              @change="changeList"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="`${item.nickname}${item.loginName ? '(' + item.loginName + ')' : ''}`"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="tags">
        <div style="display: flex; flex-direction: row; align-items: center">
          <div style="flex: 1">
            <el-select
              ref="tagsSelect"
              v-model="basicData.tag_ids"
              style="width: 100%"
              multiple
              filterable
              remote
              allow-create
              placeholder="请选择标签"
              :remote-method="searchTags"
              clearable
              @change="changeTags"
            >
              <el-option
                v-for="item in tagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="可见范围:" prop="visibility">
        <el-radio-group v-model="basicData.visibility">
          <el-radio label="public">公开</el-radio>
          <el-radio label="private">私有</el-radio>
          <el-radio label="share">分享</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="basicData.visibility === 'share'">
        <div class="flex">
          <div>
            <i
              class="el-icon-circle-plus-outline"
              style="color: blue; cursor: pointer"
              @click="shareFrame()"
            />
            已分享:
          </div>
          <div v-if="shareUsers.length > 0" style="width: 90%">
            <span v-for="(item, index) in shareUsers" :key="item.id">
              {{ item.nickname }}<span v-if="index !== shareUsers.length - 1">，</span>
            </span>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <shareMember
      ref="shareMemberRef"
      :share-users="shareUsers"
      :modal-title="modalTitle"
      @shareMemberList="shareMemberList"
    />
  </div>
</template>

<script>
import {
  AddScheme,
  queryDictConfig,
  agentSenceList,
  queryTags,
  addTag,
  bindTag,
  queryUseTags,
  getInitWorkBenchDich,
  getExecuteSync,
  getSencetVisibleList,
  getUserLastSceneList,
  saveSimpleSchemeGenerate
} from '@/api/planGenerateApi.js';
export default {
  props: {},
  data() {
    const checkShare = (rule, value, callback) => {
      if (value.trim() === '') {
        return callback(new Error('可见范围不能为空'));
      } else if (value === 'share' && this.shareUsers.length === 0) {
        return callback(new Error('分享人员不能为空'));
      } else {
        callback();
      }
    };
    return {
      basicData: {
        name: '',
        description: '',
        agent_scene_code: '',
        agent_scene: '',
        agent_id: '',
        ownerId: [],
        share_userids: [], // 分享的用户
        contributors: [],
        tag_ids: [],
        scene_version_id: '',
        scheme_detail_name: '',
        domains: [],
        visibility: 'public',
        display_type: '1'
      },
      rules: {
        visibility: [
          { required: true, message: '请选择可见范围', trigger: 'blur' },
          { validator: checkShare, trigger: 'blur' }
        ]
      },
      userList: [],
      tagList: [],
      shareUsers: [],
      modalTitle: '请选择分享用户',
    };
  },
  methods: {
    searchTags2() {
      queryTags({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          const mergedArr = [...res.data, ...this.allTags];
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse);
          this.allTagList = uniqueArr;
          this.tagList = uniqueArr;
          const temp = [];
          // console.log('回显', this.editData.tags, this.tagList);
          this.editData.tags?.forEach((titem) => {
            const filter = uniqueArr.filter((item) => item.id === titem.id);
            if (filter.length) {
              temp.push(titem.id);
            }
          });
          this.formData.tag_ids = temp;
        } else {
          this.allTagList = [...this.allTags];
          this.tagList = [...this.allTags];
        }
      });
    },
    queryAllTag() {
      queryUseTags({ keyword: '' })
        .then((res) => {
          if (res.data) {
            console.log(res.data);
            this.allTags = res.data;
            this.$nextTick(async () => {
              await this.searchTags2();
            });
          }
        })
        .finally(() => {});
    },
    searchTags(keyword) {
      queryTags({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
          }
        } else {
          this.tagList = [];
        }
      });
    },
    async changeTags(val) {
      console.log('改变', val);
      const temp = [];
      val.forEach(async (tagid) => {
        const tagTotal = [...this.allTags, ...this.allTagList];
        const filters = tagTotal.filter((item) => item.id === tagid);
        console.log(filters, '666');
        if (filters.length === 0) {
          console.log('标签长度', tagid.length);
          if (tagid.length && tagid.length <= 15) {
            await addTag({
              name: tagid
            }).then(async (res) => {
              if (res.data) {
                console.log('添加成功', res.data);
                temp.push(res.data);
                await queryTags({
                  keyword: ''
                }).then((res) => {
                  if (res.data) {
                    const mergedArr = [...res.data, ...this.allTags];
                    const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(
                      JSON.parse
                    );
                    this.tagList = uniqueArr;
                    this.allTagList = uniqueArr;
                    this.$refs.tagsSelect.selected[
                      this.$refs.tagsSelect.selected.length - 1
                    ].currentLabel = tagid;
                    this.formData.tag_ids = temp;
                    console.log('this.formData.tag_ids', this.formData.tag_ids);
                    this.$nextTick(() => {
                      this.$refs.tagsSelect.selected[
                        this.$refs.tagsSelect.selected.length - 1
                      ].currentLabel = tagid;
                    });
                  } else {
                    this.tagList = [];
                  }
                });
              }
            });
          } else {
            this.$message({
              type: 'warning',
              message: '标签最长为15个字符，请修改!'
            });
          }
        } else {
          const mergedArr = [...this.allTags, ...this.allTagList];
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse);
          this.tagList = uniqueArr;
          this.allTagList = uniqueArr;
          const filters = tagTotal.filter((item) => item.id === tagid);
          if (filters.length) {
            // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            temp.push(tagid);
            this.formData.tag_ids = temp;
            console.log('this.formData.tag_ids', this.formData.tag_ids);
            this.$nextTick(() => {
              // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            });
          } else {
            temp.push(tagid);
            this.formData.tag_ids = temp;
            console.log('this.formData.tag_ids', this.formData.tag_ids);
          }
        }
      });
    },
    searchUser(userName) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data;
      });
    },
    changeList(val) {
      this.$nextTick(() => {
        this.basicData.contributors = this.$refs.onwerSelect.selected.map((item) => {
          return {
            id: item.currentValue,
            nickname: item.currentLabel.slice(0, item.currentLabel.indexOf('(')),
            loginName: item.currentLabel.match(/\((.+)\)/)[1]
          };
        });
      });
    },
    shareMemberList(Arr) {
      this.shareUsers = Arr || [];
      this.formData.share_userids = Arr.map((el) => {
        return el.id;
      });
      this.shareToolTip = Arr.map((el) => {
        return el.nickname;
      });
    },
    shareFrame() {
      this.$refs.shareMemberRef.handleOpen();
    },
  },
  async mounted() {
    await this.queryAllTag();
  }
};
</script>

<style lang="scss" scoped>
.basic-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: #fff;
  overflow: auto;
}
</style>
