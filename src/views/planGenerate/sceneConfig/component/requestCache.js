const API_CACHE = new Map();
/**
 * 带命名空间的缓存请求方法
 * @param key 唯一缓存标识（建议标准化参数）
 * @param requestFn 实际请求函数
 * @param ttl 缓存有效期（毫秒，默认30秒）
 * @param apiNamespace API命名空间（用于隔离不同接口）
 */
export const cachedRequest = ( key,requestFn,ttl = 30000,apiNamespace = 'default') => {
 const cacheKey = `${apiNamespace}:${key}`; // 命名空间隔离
 const now = Date.now();
 // 有效缓存检查
 if (API_CACHE.has(cacheKey)) {
  const entry = API_CACHE.get(cacheKey);
 // 返回未过期的缓存数据
 if (now - entry.timestamp < ttl) {
   return entry.data ?? entry.promise;
 }
 }
 // 创建新请求
const promise = requestFn()
 .then(res => {
     API_CACHE.set(cacheKey, { 
     timestamp: now, 
     promise,
     data: res 
   });
   return res;
 })
 .catch(err => {
   API_CACHE.delete(cacheKey);
   throw err;
 });

API_CACHE.set(cacheKey, { timestamp: now, promise });
return promise;
}