<template>
  <div>
    <el-button type="primary" @click="extractVariable" class="mt-4">从生产工具配置中提取</el-button>
    <el-table :data="envs" style="width: 100%">
      <el-table-column label="类型" width="180">
        <template #default="scope">
          <el-select v-model="scope.row.variable_type" disabled placeholder="选择类型">
            <el-option label="默认" value=""></el-option>
            <el-option label="知识库" value="knowledge"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="名称" width="220">
        <template #default="scope">
          <el-input v-model="scope.row.variable_name" disabled></el-input>
        </template>
      </el-table-column>
      <el-table-column label="值">
        <template #default="scope">
          <div v-if="scope.row.variable_type === 'knowledge'">
            <el-select
              v-model="scope.row.variable_value"
              filterable
              placeholder="请选择知识库">
              <el-option
                v-for="item in knowledgeBaseList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div v-else>
            <el-input v-model="scope.row.variable_value"></el-input>
          </div>
        </template>
      </el-table-column>
      <!--<el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button type="text" style="color: #F56C6C" @click="deleteVariable(scope.row)">删除</el-button>
        </template>
      </el-table-column>-->
    </el-table>
  </div>
</template>
<script>
import { getKnowledgeList } from '@/api/planGenerateApi';
import { cloneDeep } from 'lodash';

export default {
  props: {
    globalConstantData: {
      type: Array,
      default() {
        return [];
      }
    },
    curBaseInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    initData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      envs: [],
      knowledgeBaseList: [],
    };
  },
  watch: {
    globalConstantData: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.envs = cloneDeep(val);
        }
      }
    }
  },
  created() {},
  async mounted() {
    await this.getKnowledge()
  },
  methods: {
    restoreHandle() {
      // console.log('env-restore-handle', this.initData)
      this.envs = cloneDeep(this.initData.global_constant);
    },
    saveHandle() {
      // 执行提交操作
      console.log('====saveHandle======', this.envs)
      this.$emit('updateGlobalConstant', this.envs);
      this.changeViews('save');
    },
    changeViews(val) {
      this.$emit('updateStep', val);
    },
    extractVariable() {
      this.$emit('extractVariable')
    },
    async getKnowledge() {
      getKnowledgeList().then(async res=> {
        const { data = {}, status } = res;
        if (status === 200 ) {
          this.knowledgeBaseList = data.data
        }
      })
    },
  },
};
</script>
