<template>
  <div class="basicSetting">
    <div class="basicContent">
      <div class="optContent">
      <div class="optHeader">
        <div class="rightTitle">实例创建配置</div>
      </div>
      <div class="optScroll">
        <div ref="tabelRef" style="width: 100%;height: 100%;" class="table-box">
          <el-table :data="instantiationTableData" style="width: 100%" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }">
            <el-table-column prop="field_name" label="名称">
              <template slot-scope="scope">
                <el-input v-model="scope.row.field_name" :disabled="Boolean($route.query.id)"/>
              </template>
            </el-table-column>
            <el-table-column prop="field_code" label="code">
              <template slot-scope="scope">
                <el-input v-model="scope.row.field_code" :disabled="Boolean($route.query.id)"/>
              </template>
            </el-table-column>
            <el-table-column prop="field_val" label="类型">
              <template slot-scope="scope">
                <div class="flex">
                  <el-select v-model="scope.row.field_val.type" :disabled="Boolean($route.query.id)" style="width:200px;" class="margin-right-12" @change="(val) => changeIType(val, scope.$index)">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    v-show="scope.row.field_val.type === 'ability'"
                    v-model="scope.row.field_val.ability_id"
                    :disabled="Boolean($route.query.id)"
                    placeholder="请输入"
                    :remote-method="getAbilityOption"
                    clearable
                    filterable
                    remote
                    style="width:280px;"
                  >
                    <el-option
                      v-for="item in optionsList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                      <span style="float: left">{{ item.name }}</span>
                      <span class="rightItem"><selectItem :array-items="item.tags?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem></span>
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="x"
              fixed="right"
              label="操作"
              class-name="no-bor"
              width="64"
            >
              <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" content="删除" placement="top">
                  <el-link :underline="false" icon="el-icon-remove-outline" :disabled="Boolean($route.query.id)" @click="handleInstanRemove(scope.$index)"></el-link>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="新增" placement="top">
                  <el-link v-if="(instantiationTableData.length - 1) === scope.$index" :disabled="Boolean($route.query.id)" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleInstanAddRow(instantiationTableData.length)"></el-link>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="optContent">
      <div class="optHeader">
        <div class="rightTitle">能力发布配置</div>
      </div>
      <div class="optScroll">
        <div ref="tabelRef" style="width: 100%;height: 100%;" class="table-box">
          <el-table :data="abilityTableData" style="width: 100%" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }">
            <el-table-column prop="field_name" label="名称">
              <template slot-scope="scope">
                <el-input v-model="scope.row.field_name" :disabled="Boolean($route.query.id)"/>
              </template>
            </el-table-column>
            <el-table-column prop="field_code" label="code">
              <template slot-scope="scope">
                <el-input v-model="scope.row.field_code" :disabled="Boolean($route.query.id)"/>
              </template>
            </el-table-column>
            <el-table-column prop="field_val" label="类型">
              <template slot-scope="scope">
                <div class="flex">
                  <el-select v-model="scope.row.field_val.type" :disabled="Boolean($route.query.id)" style="width:200px;" class="margin-right-12" @change="(val) => changeCType(val, scope.$index)">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    v-show="scope.row.field_val.type === 'ability'"
                    v-model="scope.row.field_val.ability_id"
                    :disabled="Boolean($route.query.id)"
                    placeholder="请输入"
                    :remote-method="getAbilityOption"
                    clearable
                    filterable
                    remote
                    style="width:280px;"
                  >
                    <el-option
                      v-for="item in optionsList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                      <span style="float: left">{{ item.name }}</span>
                      <span class="rightItem"><selectItem :array-items="item.tags?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem></span>
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="x"
              fixed="right"
              label="操作"
              class-name="no-bor"
              width="64"
            >
              <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" content="删除" placement="top">
                  <el-link :underline="false" :disabled="Boolean($route.query.id)" icon="el-icon-remove-outline" @click="handleAbilityRemove(scope.$index)"></el-link>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="新增" placement="top">
                  <el-link v-if="(abilityTableData.length - 1) === scope.$index" :disabled="Boolean($route.query.id)" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAbilityAddRow(abilityTableData.length)"></el-link>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    </div>
    <!-- <div class="optFooter"> -->
      <!-- <el-button :disabled="treeStatusLast === 1 || treeStatusLast === 0 " type="primary" @click="showSqlModal">生成sql</el-button> -->
      <!-- <el-button class="button-last"  type="primary" @click="changeViews(1)">下一步</el-button> -->
      <!-- <el-button class="button-last" type="info" @click="() => $router.push({path: '/planGenerate/planchat', query: {...$route.query}})">返回智能能力研发</el-button>
    </div> -->
  </div>
</template>
<script type="text/javascript">
import { getTemplateList } from '@/api/planGenerateApi'
import selectItem  from '../../selectItem.vue';
export default {
  name: 'InterfaceCom',
  components: { selectItem },
  props: {
    instantiationData: {
      type: Array,
      default () {
        return []
      }
    },
    abilityData: {
      type: Array,
      default () {
        return []
      }
    },
    initData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data() {
    return {
      options:[{label:'能力（下拉框）',value:'ability'},{label:'输入框',value:'input'}],
      optionsList:[],
      instantiationTableData: [],
      abilityTableData:[],
      tableLoading: false,
    }
  },
  watch:{
    instantiationData: {
      immediate: true,
      deep:true,
      handler(val) {
        if (val) {
          console.log('0009',val)
          this.instantiationTableData = val
        }
      }
    },
    abilityData: {
      immediate: true,
      handler(val) {
        if (val) {
          this.abilityTableData = val
        }
      }
    }
  },
  created() {
  },
  mounted() {
    this.getAbilityOption()
  },
  methods: {
    restoreHandle(){
      this.instantiationTableData = this.initData.init_work_bench_dict
      this.instantiationTableData = this.initData.publish_ability_dict
    },
    getAbilityOption(query){
      getTemplateList({
        page: 1,
        page_size: 999,
        keyword: query||'',
        ability_type:'scene_config'
      }).then((res) => {
        if (res.status === 200) {
          this.optionsList =res.data?.map((item)=>{return {id:item.id,name:item.name,tags:item.tags}})
        }
      });
    },
    changeIType(val, index) {
      console.log('切换类型', val, index);

    },
    changeCType(val, index) {
      console.log('切换类型', val, index);

    },
    handleInstanAddRow (rowIndex) {
      const addData = {field_name:'',field_code:'',field_val: {
				'type': 'input',
				'ability_id': null
			},isAdd: true};
      this.instantiationTableData.splice(rowIndex, 0, addData);
      console.log('增加行', this.instantiationTableData);
    },
    handleAbilityAddRow (rowIndex) {
      const addData = {field_name:'',field_code:'',field_val: {
				'type': 'input',
				'ability_id': null
			},isAdd: true};
      this.abilityTableData.splice(rowIndex, 0, addData);
      console.log('增加行', this.abilityTableData);
    },
    // changeViews(val){
    //   this.$emit('updateAbilityData',this.abilityTableData)
    //   this.$emit('updateInstanceData',this.instantiationTableData)
    //   this.$emit('updateStep',val)
    // },
    getData() {
      return {
        abilityTableData: this.abilityTableData,
        instantiationTableData: this.instantiationTableData,
      }
    },
    // 删除行
    handleInstanRemove (rowIndex) {
      const res = this.instantiationTableData.splice(rowIndex, 1);
      console.log('删除行', res, this.instantiationTableData )
    },
    // 删除行
    handleAbilityRemove (rowIndex) {
      const res = this.abilityTableData.splice(rowIndex, 1);
      // const res2 = this.fieldsList.splice(rowIndex, 1);
      console.log('删除行', res, this.abilityTableData)
    },

  }
}
</script>
<style lang="less" scoped>
.basicSetting{
  padding:10px 16px;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .basicContent{
    height: calc(100% - 54px);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .optContent{
    flex: 1; /* 平分高度 */
    display: flex;
    flex-direction: column;
    .rightTitle{
      font-weight: 400;
      font-size: 14px;
      color: #323233;
      line-height: 44px;
    }
  }
  }

  .optFooter{
    position: fixed;
    bottom: 0px;
    left: 0px;
    z-index: 99999;
    width: 100%;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.08);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 20px;
    min-height: 54px;

  }
}
.margin-right-12{
  margin-right: 12px;
}
</style>
