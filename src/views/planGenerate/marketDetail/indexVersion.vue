<template>
  <!-- 应用详情 -->
  <div class="page">
    <div class="info">
      <div class="info-top" :class="!isFilterFlag ? 'info-padding' : ''">
        <div class="info-title">
          <span>{{ baseInfo?.name }}</span>
          <span v-if="baseInfo?.status" class="el-status">
            <Status
              :text="statusTypeMap[baseInfo?.status]?.text"
              :bg-color="statusTypeMap[baseInfo?.status]?.bgColor"
              :dot-color="statusTypeMap[baseInfo?.status]?.dotColor"
            />
          </span>
        </div>
      </div>
      <div v-show="isFilterFlag">
       <el-descriptions title="" :column="3">
         <el-descriptions-item label="发布时间">{{ baseInfo?.update_time }}</el-descriptions-item>
         <el-descriptions-item label="创建时间">{{ baseInfo?.createTime }}</el-descriptions-item>
         <el-descriptions-item label="发布人">{{ baseInfo?.ownerName }}</el-descriptions-item>
       </el-descriptions>
      </div>
      <div class="searchIcon">
       <div class="line"></div>
        <div
          class="searchIconTaggle"
          @click="
            () => {
              isFilterFlag = !isFilterFlag;
            }
          ">
          <i v-if="isFilterFlag" class="el-icon-arrow-up"></i>
          <i v-else class="el-icon-arrow-down"></i>
        </div>
       <div class="line"></div>
      </div>
      <el-tabs v-if="$route.query?.capability_type !== 'api_service_development'" v-model="activeName">
        <el-tab-pane label="文件" name="modelFile"/>
      </el-tabs>
      <el-tabs v-else v-model="activeName">
        <el-tab-pane label="方案" name="scheme"/>
        <el-tab-pane label="思维树" name="mind"/>
        <el-tab-pane label="代码" name="code"/>
      </el-tabs>
    </div>
    <div class="tab-content">
      <ModelFile v-if="activeName === 'modelFile'" :agent_scene_code="$route.query.agent_scene_code" :scheme_id="$route.query.scheme_id"/>
      <Scheme v-if="activeName === 'scheme'" :agent_scene_code="$route.query.agent_scene_code" :scheme_id="$route.query.scheme_detail_id"/>
      <Mind v-if="activeName === 'mind'" :agent_scene_code="$route.query.agent_scene_code" :scheme_id="$route.query.decision_tree_id"/>
      <Code v-if="activeName === 'code'" :agent_scene_code="$route.query.agent_scene_code" :scheme_id="$route.query.code_id" />
    </div>

  </div>
</template>

<script type="text/javascript">
import Scheme from './component/scheme.vue'
import ModelFile from './component/modelFile.vue'
import Mind from './component/mind.vue'
import Code from './component/code.vue'
import Status from '@/components/Status/index.vue';
export default {
  name: 'MarketDetail',
  components: { Scheme, Mind, Code, Status, ModelFile},
  data() {
    return {
      isFilterFlag: false,
      activeName: this.$route.query?.capability_type !== 'api_service_development' ? 'modelFile' : 'scheme',
      baseInfo: {},
      userList: [],
      statusTypeMap: {
        'offline': { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        'online': { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
      },
    }
  },
  created() {
    this.getMarketInfo()
  },
  mounted() {

  },
  methods: {


    searchUser(userName, callback) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data;
        if (callback) {
          this.$nextTick(callback);
        } else {
          this.createUserName = userName;
        }
      });
    },

    getMarketInfo() {
      this.baseInfo.name = this.$route.query.version
      this.baseInfo.createTime = this.$route.query.create_date
      this.baseInfo.ai_service_name = this.$route.query.ai_service_name
      this.baseInfo.update_time = this.$route.query.update_time
      this.baseInfo.ownerName = this.$route.query.user_name
      this.baseInfo.status = this.$route.query.status
      // await getMarketAppInfo({
      //   id: this.$route.query?.edgeId || ''
      // }).then(res => {
      //   if (res?.data?.status === 200) {
      //     this.baseInfo = {
      //       ...res?.data?.data
      //     }
      //     this.searchUser(this.baseInfo?.ownerName)
      //   } else {
      //     this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
      //   }
      // }).catch((err) => {
      //   console.log('err', err)
      // })
    },
  }
}
</script>
<style lang="less" scoped>
.page {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .info {
    width: 100%;
    background: #fff;
    border-bottom:2px solid #E4E7ED;
    .line {
     // width: 1px;
     flex: 1;
     height: 1px;
     background-color: #ebecf0;
    }
    .searchIcon {
     display: flex;
     justify-content: center;
     align-items: center;
     .searchIconTaggle {
      padding: 0px 12px;
      border-radius: 2px;
      height: 20px;
      line-height: 20px;
      cursor: pointer;
      border: 1px solid #F2F3F5;
     }
    }

    .info-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding:12px 20px;

      .info-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        >span {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #323233;
          line-height: 26px;
          margin-right: 12px;
        }
      }
    }
    .info-padding {
     padding: 12px 20px 2px 20px;
    }
    .el-descriptions {
      padding-left: 20px;
      :deep(.el-descriptions-item__container) {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
      }
    }

    .el-tabs {
      padding-left: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        height: 0px;
      }
    }
  }

  .tab-content {
    flex: 1;
    overflow: hidden;
    margin: 16px 20px 0px 20px;
    background: #fff;
    padding: 16px 20px;
  }

  .el-select {
    width: 100%;
  }

  :deep {
    .el-tabs__header{
       margin-bottom: 0px;
    }

    .el-dialog__header {
      border-bottom: 1px solid #ebecf0;
    }
  }
}

</style>
