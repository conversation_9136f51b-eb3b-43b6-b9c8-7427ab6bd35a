<template>
  <div class="chatTest" :loading="loading">
    <div class="chatBox">
      <div class="chatContent">
        <div v-if="!noParams" class="chatContentBox">
          <div v-for="(el,index) in historyChat " :key="index" :class="el.role === 'user' ? 'gptAnsWrap userWrap': 'gptAnsWrap'">
            <div v-if="el.loading" class="qa-loading-spinner3"></div>
            <div v-if="el.content && !el.loading" class="gptContent">{{ el.content }}</div>
          </div>
          <div v-if="historyChat.length === 0 && !noParams">
            <div class="qsBox">
              <div class="qs">你好！会话输入可以测试能力</div>
            </div>
          </div>
        </div>
        <el-empty v-if="noParams" :image="require('@/assets/images/planGenerater/emptyParams.png')" description="暂无参数或参数存在多个，无法使用会话模式，请使用其他模式">
        </el-empty>
      </div>
      <div class="chatInputBox">
        <div class="chatFooter">
          <div id="myInputText" class="chatInput">
            <div class="chatInputInner">
              <el-input
                ref="myChatInputText"
                v-model="currentText"
                clearable
                type="textarea"
                resize="none"
                :autosize="{ minRows: 2, maxRows: 2 }"
                style="width: 100%"
                :placeholder="
                  navType === 'Mac'
                    ? '请输入你的问题，可通过cmd+回车换行'
                    : '请输入你的问题，可通过Ctrl+回车换行'
                "
                :disabled="isDisabled ||completeLoading || noParams"
                @keydown.native="carriageReturn($event)"
              />
            </div>
          </div>
          <div class="send-btn">
            <!-- <div class="yuyinBtn">
              <img
                v-if="speakingFlag === 'start'"
                src="@/assets/images/planGenerater/shuohua.png"
                @click="startRecording"
              />
              <img
                v-if="speakingFlag === 'running'"
                src="@/assets/images/planGenerater/yuyin.gif"
                @click="stopRecording"
              />
              <img
                v-if="speakingFlag === 'end'"
                src="@/assets/images/planGenerater/zhuanhuan-loading.gif"
              />
            </div> -->
            <div class="send-desc">
              <img src="@/assets/images/planGenerater/back.png" style="margin-right: 4px" />发送
            </div>
            <el-button
              type="primary"
              icon="el-icon-position"
              circle
              :disabled="isDisabled ||completeLoading"
              @click="sendMessage"
            ></el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryAbilityApi } from '@/api/planGenerateApi.js';
import Recorder from 'js-audio-recorder';
import axios from 'axios'
import { cloneDeep } from 'lodash';
const cancelToken = axios.CancelToken
let source = cancelToken.source()
const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
};
export default {
  name: 'TestChat',
  components: {},
  props: {
    agentSceneCode: {
      type: String,
      default: ''
    },
    scheme_id: {
      type: Number | String,
      default: ''
    },
    id: {
      type: Number|String,
      default: ''
    },
  },
  data() {
    return {
      navType: '',
      loading: false,
      isDisabled: false,
      historyChat: [],
      historyParamsChat: [],
      speakingFlag: 'start',
      currentText: '',
      chatLoading: false,
      shibieLoading: false, // 语音中
      recorderEx: new Recorder(parameter),
      yuyinText: '',
      searchText: '',
      jsonData:null,
      completeLoading:false,
      noParams: false
    };
  },
  mounted() {
    this.getCodeParams()
    this.historyChat = []
    this.historyParamsChat = []
  },

  beforeDestroy () {
    source.cancel()
    source = cancelToken.source()
  },
  methods: {
    // 回车发送消息
    carriageReturn(event) {
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault();
          this.$nextTick(() => {
            if (this.currentText) {
              this.sendMessage();
            }
          });
        } else {
          if (event.ctrlKey || event.metaKey) {
            this.currentText += '\n';
            const target = document.getElementById('myInputText')
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50;
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              })
            }
          } else {
            event.preventDefault();
          }
        }
      }
    },
    async handleTest(datas) {
      this.completeLoading = true;
      this.historyChat.push({'role':'assistant','content':'','loading':true})
      const lastWithMessage =this.historyChat[this.historyChat.length - 1]
        let url = ''
        const userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}
        let params = {};
        url = process.env.VUE_APP_PLAN_API.startsWith('/') ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute' : process.env.VUE_APP_PLAN_API +  '/code/execute'
        params = {
          'scheme_id':this.scheme_id,
          'params': {[this.curKey]:datas},
          'oper_type': 'ability'
        };

        this.$axios.post(url, {
          header: {
            tenant_id: userInfo.tenantId || 'str',
            user_id: userInfo.userId || 'str',
            session_id: '1',
            work_space_id: this.$route.query.workspaceId + ''
          },
          data: params || {}
        }, {
          timeout: 280000,
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
          headers: {
            affinitycode: userInfo.userId || '',
            'Content-Type': 'application/octet-stream'
          },
          cancelToken:source.token,
          onDownloadProgress:(event) => {
            const xhr = event.target;
            const { responseText } = xhr;
            let newStr
            console.log(responseText,'---')
            if(responseText?.includes('code')){
              // 处理返回体数据
              try {
                const val = JSON.parse(responseText).result?.resp?.result
                try {
                  const resultData=  JSON.parse(val)
                  newStr=resultData.result
                } catch (error) {
                  newStr= val
                }
              } catch (error) {
                const val =  responseText?.result?.resp?.result
                const resultData=  val
                newStr=resultData.result
              }
              
            }else{
              newStr = responseText.replace(/[\r\n]/g, '')
            }
            this.$nextTick(() => {
              lastWithMessage.loading =false
              console.log('----流----',newStr)
              lastWithMessage.content =newStr
            })
          },
          onError: function(error) {
            // 处理流错误
            console.error(error);
            lastWithMessage.loading =false
          }
        }).then(async res => {
          // 关闭数据流
          console.log('----数据流----',res);
          this.historyParamsChat = this.historyChat.map(({ loading, ...rest }) => rest);
          this.completeLoading = false;

        }).catch(() => {
          this.completeLoading = false;
          lastWithMessage.loading =false
        })
      },
    async sendMessage() {
      console.log('发送消息', this.currentText);
      // 将消息发送到服务器
      this.historyChat.push({'role':'user','content':this.currentText,'loading':false})
      this.historyParamsChat = this.historyChat.map(({ loading, ...rest }) => rest);
      this.handleTest(JSON.stringify(this.historyParamsChat))
      console.log(this.historyChat ,'historyChat')
      this.currentText = ''
      this.speakingFlag = 'start'
      this.yuyinText = ''

    },
    async startRecording() {
      if (this.chatLoading || this.shibieLoading) {
        return false;
      } else {
        try {
          this.recorderEx.start();
          this.speakingFlag = 'running';
          this.yuyinText = '正在语音中...';
        } catch (err) {
          console.error('无法获取媒体流:', err);
          this.speakingFlag = 'start';
        }
      }
    },
    stopRecording() {
      this.speakingFlag = 'end';
      this.recorderEx.stop();
      setTimeout(() => {
        const wavBlob = this.recorderEx.getWAVBlob(); // blob格式
        console.log('this.audioChunks33', wavBlob);
        this.yuyinText = '正在转换文字中...';
        this.runExcute(wavBlob);
      });
    },
    async runExcute(audioBlob) {
      this.shibieLoading = true;
      this.currentText = '';
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
        : process.env.VUE_APP_PLAN_API + '/voice/conversion/text';
      console.log('url', url, audioBlob);
      await this.$axios
        .post(url, audioBlob, {
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          headers: {
            'Content-Type': 'application/octet-stream'
          },
          onDownloadProgress: (event) => {
            const xhr = event.target;
            const { responseText } = xhr;
            console.log('流信息', responseText);
            let chunk = '';
            let dataArray;
            const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2);
            if (lastIndex !== -1) {
              chunk = responseText.slice(0, lastIndex);
              dataArray = chunk.match(/(.*(\n\n\")?\})(\n\n)?/g);
              const lastText = JSON.parse(dataArray[dataArray.length - 2].replace('data:', ''));
              console.log('lastTextlastTextlastText', lastText);
              if (lastText) {
                this.currentText = lastText?.message;
              }
            }
          },
          onError: function (error) {
            // 处理流错误
            console.error(error);
            this.speakingFlag = 'start';
            this.shibieLoading = false;
          }
        })
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流结束', response);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log('识别接口错误', err);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        });
    },
    getCodeParams(){
      queryAbilityApi({'ability_id': Number(this.id)}).then(res => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('测试参数',res.data.result);
            this.newTableData = res?.data?.result?.req_body|| [];
            if(this.newTableData.length > 0){
              if (this.newTableData.length > 1) {
                this.noParams = true;
                this.isDisabled = true;
              }else{
                this.noParams = false;
                this.curKey = this.newTableData[0].param_name
              }
            }else{
              this.noParams = true;
              this.$message.warning('暂无参数');
            }

          }
      })
    }

  }
};
</script>

<style lang="less" scoped>
.qsBox {
    display: flex;
    justify-content: flex-start;
    .qs {
      font-size: 14px;
      color: #323233;
      line-height: 20px;
      margin: 20px;
      background: #FFFFFF;
      border-radius: 4px;
      padding: 8px 16px;
    }
  }
.qa-loading-spinner3{
    width: 42px;
    height: 36px;
    background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #F6F8FB;
    background-size: 100% 100%;
    position: relative;
    border-radius: 6px;
  }
.chatTest {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  .chatHis {
    width: 320px;
    background: #fff;
    border-radius: 4px 0px 0px 4px;
    padding: 16px;
    .hisSearch {
      display: flex;
      align-items: center;
    }
    .hisBox {
      max-height: calc(100% - 60px);
      overflow-y: auto;
      margin-top: 16px;
      .hisItem {
        border-bottom: 1px solid #ebecf0;
        padding: 10px 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
        position: relative;
        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
        &:hover {
          background: #f6f7fb;
          border-radius: 2px;
        }
        &.activeItem {
          background: #eff3ff;
          border-radius: 2px 0px 0px 2px;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            right: 0px;
            top: 0px;
            height: 100%;
            width: 4px;
            background: #4068d4;
            border-radius: 0px 2px 2px 0px;
          }
        }
      }
    }
  }
  .chatBox {
    flex: 1;
    background: url('@/assets/images/planGenerater/market-bg.png') no-repeat;
    background-size: cover;
    border-radius: 0px 4px 4px 0px;
    display: flex;
    flex-direction: column;
    width:100%;
    .chatContent {
      flex: 1;
      padding: 24px;
      position: relative;
      .chatContentBox {
        max-height: 100%;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        .greets {
          width: 100%;
          background: #fff;
          border-radius: 4px;
          padding: 16px;
          margin-bottom: 16px;
          .title {
            font-size: 16px;
            color: #323233;
            line-height: 24px;
            text-align: left;
            font-weight: bold;
            margin-bottom: 16px;
          }
          .descBox {
            background: #f6f7fb;
            border-radius: 4px;
            padding: 12px;
            .titleBox {
              display: flex;
              flex-direction: row;
              align-items: center;
              img {
                width: 24px;
                height: 24px;
                background: #e6ecff;
                border-radius: 4px;
              }
              .greetName {
                font-size: 14px;
                color: #323233;
                line-height: 20px;
                margin-left: 12px;
              }
            }
            .desc {
              font-size: 14px;
              color: #646566;
              line-height: 22px;
              text-align: left;
            }
          }
        }
        .gptAnsWrap {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 18px;
          &.userWrap {
            display: flex;
            justify-content: flex-end !important;
            .gptContent {
              background-color: #c2d2ff;
              color: #323233;
            }
          }
          .gptContent {
            max-width: 100%;
            background-color: #fff;
            border-radius: 4px;
            padding: 8px 12px;
            word-break:break-all;
          }
        }
      }
      .el-empty{
        position: absolute;
        left: 50%;
        top:50%;
        transform: translate(-50%,-50%);
      }
    }
    .chatInputBox {
      .chatFooter {
        position: relative;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: transparent;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 12px 16px;
        .clear {
          width: 32px;
          height: 32px;
          cursor: pointer;
          margin-right: 8px;
          text-align: center;
          img {
            width: 32px;
            height: 32px;
          }
        }
        .chatInput {
          flex: 1;

          // border: 1px solid #C8C9CC;
          // border: 2px solid;
          // border-image: linear-gradient(156deg, rgba(206, 140, 255, 1), rgba(117, 154, 255, 1), rgba(78, 126, 245, 1), rgba(100, 196, 255, 1)) 2 2;
          border-radius: 4px;
          background: linear-gradient(
            156deg,
            rgba(206, 140, 255, 1),
            rgba(117, 154, 255, 1),
            rgba(78, 126, 245, 1),
            rgba(100, 196, 255, 1)
          );
          .chatInputInner {
            margin: 2px;
            background: #fff;
            border-radius: 2px;
          }
        }
        .send-btn {
          position: absolute;
          right: 20px;
          display: flex;
          flex-direction: row;
          align-items: center;
          // margin-top: 6px;
          font-size: 12px;
          bottom: 18px;
          .send-desc {
            font-size: 10px;
            color: #969799;
            img {
              width: 10px;
              height: 10px;
            }
          }
          .yuyinBtn {
            cursor: pointer;
            margin-right: 8px;
            &.yuyinBtnDisabled {
              cursor: not-allowed;
            }
            img {
              width: 24px;
              height: 24px;
            }
          }
          :deep(.el-button) {
            width: 20px;
            height: 20px;
            padding: 0px;
            background: linear-gradient(180deg, #69a0ff 0%, #375fcb 100%);
            border-radius: 12px !important;
            border: none;
            margin-left: 12px;
            margin-right: 8px;
            line-height: 20px;
            i {
              font-size: 12px;
              margin-left: -1px;
            }
          }
        }
        .upload-demo {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .updloadBtn {
          width: 18px;
          height: 16px;
          background: url(@/assets/images/planGenerater/upload-icon.png) no-repeat;
          background-size: contain;
        }
        :deep(.el-input__inner) {
          border-radius: 4px;
          border-color: #c8c9cc;
          color: #323233;
          &:focus {
            border-color: #406bd4;
          }
        }
        :deep(.el-textarea__inner) {
          border-radius: 4px;
          border: none !important;
          color: #323233;
          padding-right: 114px;
          &:focus {
            border-color: #406bd4;
          }
        }
      }
    }
  }
}
</style>
