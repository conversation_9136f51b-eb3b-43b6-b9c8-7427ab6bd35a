<template>
<div style="width:100%;height:100%;">
    <div v-loading="loading" class="dialog-container">
        <div v-loading="taskLoading" class="content left">
        <div class="title">
            <div class="text">请求参数</div>
            <el-radio-group v-model="reqActive" @change="changeType">
            <el-radio label="table">表格</el-radio>
            <el-radio label="JSON">JSON</el-radio>
            </el-radio-group>
        </div>
          <div v-if="['expert_general','device_ops_assistant_scene','artificial_handle_scene','visit_leader_cognition_scene', 'intelligent_conversation_scene','sop_scene','digital_twin_assistant_scene','operational_optimization_scene'].indexOf(agentSceneCode) > -1" class="box">
            <div v-if="reqActive === 'table'">
                <div class="planSearch" style="padding: 10px 0 10px 0">
                <div class="searchFlex" :style="{justifyContent: agentSceneCode ==='device_ops_assistant_scene' ? 'space-between' : 'flex-end'}">
                    <div v-if="agentSceneCode ==='device_ops_assistant_scene'" class="searchLeft">
                    <div class="searchItem" style="width: 50%">
                        <div class="searchLabel">测点时间：</div>
                        <el-date-picker
                        v-model="formData.time"
                        type="datetime"
                        :picker-options="pickerOptions1"
                        popper-class="mytest"
                        placeholder="选择时间">
                        </el-date-picker>
                    </div>
                    <div class="searchItem" style="width: 50%">
                        <div class="searchLabel">设备ID</div>
                        <el-select v-model="formData.equipId" placeholder="请选择" filterable remote :remote-method="filterEquip" :loading="loading2" @change="changeEquip">
                        <el-option
                            v-for="fitem in equipList"
                            :key="fitem.deviceId+''"
                            :label="fitem.deviceName"
                            :value="fitem.deviceId+''"
                        ></el-option>
                        </el-select>
                    </div>
                    </div>
                    <div>
                    <el-button class="button-last" type="primary" :disabled="testing" @click="handlTest">测试</el-button>
                    </div>
                </div>
                </div>
                <div style=" height:460px ; overflow: auto;">
                <el-table v-loading="tableLoading" :max-height="400" style="width: 100%;margin-top: 16px" size="medium" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }" :data="tableData">
                    <el-table-column min-width="170" prop="param_name" label="名称"></el-table-column>
                    <el-table-column min-width="170" prop="param_desc" label="描述"></el-table-column>
                    <el-table-column min-width="100" prop="data_type" label="类型">
                    <template #default="scope">
                        <el-select v-model="scope.row.data_type" placeholder="请选择">
                        <el-option key="str" label="String" value="String"></el-option>
                        <el-option key="int" label="Number" value="Number"></el-option>
                        <el-option key="object" label="Object" value="Object"></el-option>
                        <el-option key="Array" label="Array" value="Array"></el-option>
                        <el-option key="bol" label="Boolean" value="bool"></el-option>
                        <el-option key="file" label="File" value="File"></el-option>
                        </el-select>
                    </template>
                    </el-table-column>
                    <el-table-column min-width="240" prop="data_type" class-name="no-bor" label="值">
                    <template #default="scope">
                        <div v-if="scope.row.data_type === 'File'" style="width: 220px">
                        <shardUploaderTool
                        :accept="''"
                        upload-tip="小于20M"
                        :limit="20"
                        :multiple="false"
                        :cover="true"
                        :index-ref="scope.$index"
                        :shard-limit="1"
                        @onFilesChange="uploadScriptCallback"
                        @onFilesStatus="uploadShardStatusCb"
                        @on-remove="removeFile"
                        />
                        </div>
                        <div v-else-if="scope.row.data_type === 'bool' || scope.row.data_type === 'Boolean'" style="width: 160px">
                        <el-select v-model="scope.row.param_value" placeholder="请选择">
                        <el-option key="1" value="true">true</el-option>
                        <el-option key="2" value="false">false</el-option>
                        </el-select>
                        </div>
                        <el-input v-else v-model="scope.row.param_value" placeholder="请输入"></el-input>
                    </template>
                    </el-table-column>
                </el-table>
                </div>
            </div>
            <div v-else class="box">
                <div style="display: flex;justify-content: flex-end;align-items: flex-end;margin-bottom: 16px">
                  <el-button class="button-last" type="info" :disabled="testing" @click="meihuaJSON">格式化</el-button>
                  <el-button class="button-last" type="primary" :disabled="testing" @click="handlTest">测试</el-button>
                </div>
                <div ref="editor" class="editor" style="flex:1;max-height: 100%;"></div>

            </div>
            </div>
            <div v-else class="box">
              <div v-if="reqActive ==='JSON'" style="flex:1;max-height: 100%;">
                  <div style="display: flex;justify-content: flex-end;align-items: flex-end;margin-bottom: 16px">
                  <el-button class="button-last" type="info" :disabled="testing" @click="meihuaJSON">格式化</el-button>
                  <el-button class="button-last" type="primary" :disabled="testing" @click="handlTestV1">测试</el-button>
                  </div>
                  <div ref="editor" class="editor" style="flex:1;max-height: 100%;"></div>
              </div>
              <div v-else>
                  <div class="planSearch" style="padding: 0px 0 10px 0">
                  <div class="searchFlex">
                      <div><span v-if="multipleSelection?.length && testing" style="font-weight: 500; color:chocolate">正在测试第 {{resultTableData?.length+1}}/{{multipleSelection?.length}} 条数据</span></div>
                      <div>
                      <el-button class="button-last" type="info" :disabled="testing || !tableColumns?.length" @click="() => dialogVisibleV1 = true">选择要测试数据</el-button>
                      <el-button class="button-last" type="primary" :disabled="testing || !tableColumns?.length" @click="handlTestV1">测试</el-button>
                      </div>
                  </div>
                </div>
                <div style="height:460px ; overflow: auto;">
                <el-result v-if="!tableColumns?.length" icon="warning" title="sql语句异常，请检查sql语句是否正确">
                    <template slot="icon">
                    <img width="110" src="@/assets/images/e1.png" />
                    </template>
                </el-result>
                <el-form ref="formRef" :model="formModal" :rules="formRules" inline label-width="auto">
                    <el-row v-for="(column,key) of tableColumns" :key="key" type="flex">
                        <el-form-item :label="column?.e_field_desc" :prop="column.d_mapping_field">
                        <el-input v-model="formModal[column.d_mapping_field]" disabled placeholder="请输入" @input="onChange"/>
                        </el-form-item>
                        <el-form-item label="频率" :prop="'pl'+(key+1)" inline label-width="50px">
                        <el-input v-model="formModal['pl'+(key+1)]" disabled placeholder="请输入" />
                        <span class="suffix2">s/次</span>
                        </el-form-item>
                    </el-row>
                </el-form>
                </div>
            </div>
            </div>
        </div>
        <div class="lineV"></div>
        <div id="rightBox" ref="rightBox" class="content right">
        <div class="title">
            <div class="text">返回结果</div>
            <el-radio-group v-model="repActive" @change="changeShowType">
            <el-radio label="table">表格</el-radio>
            <el-radio label="JSON">JSON</el-radio>
            </el-radio-group>
        </div>
        <div class="box">
            <div v-if="['expert_general','device_ops_assistant_scene','artificial_handle_scene','visit_leader_cognition_scene', 'intelligent_conversation_scene','sop_scene','digital_twin_assistant_scene', 'operational_optimization_scene'].indexOf(agentSceneCode) > -1" class="box">
              <div v-if="repActive === 'table'">
                  <div style="padding-right: 10px; height:460px ; overflow: auto;margin-top: 16px">
                  <el-table size="medium" max-height="460" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"  :data="Object.keys(jsonData).length ? [jsonData] : []">
                      <el-table-column prop="message" label="描述" />
                      <el-table-column prop="result" label="结果">
                      <template slot-scope="scope">
                          {{ scope.row.result || '--' }}
                      </template>
                      </el-table-column>
                      <el-table-column prop="success" label="状态">
                      <template slot-scope="scope">
                          {{ scope.row.success ? '成功' :'失败' }}
                      </template>
                      </el-table-column>
                  </el-table>
                  </div>
              </div>
              <div v-if="repActive === 'JSON'" style="max-height:540px ; overflow: auto;flex:1;margin-top: 16px">
                  <!-- <JsonViewer
                  class="json-content"
                  :value="jsonData"
                  :expand-depth="5"
                  ></JsonViewer> -->
                  <div ref="editorResult" class="editor"></div>
              </div>
            </div>
            <div v-else class="box">
              <div v-if="repActive === 'table'" style="margin-top: 16px;">
                  <el-table v-if="tried" :width="tWidth" size="medium" max-height="460" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"  :data="resultTableData">
                  <el-table-column prop="testData" label="数据类型" width="90"/>
                  <el-table-column prop="result" label="样本结果" width="150">
                      <template slot-scope="scope">
                      {{ scope.row.result || '--' }}
                      </template>
                  </el-table-column>
                  <el-table-column label="预测结果" prop="real" :width="tWidth - 320">
                      <template slot-scope="scope">
                          {{ scope.row.real || '--' }}
                      </template>
                  </el-table-column>
                  <el-table-column prop="success" label="状态" width="80">
                      <template slot-scope="scope">
                      <el-tag v-if="scope.row.success" type="success">成功</el-tag>
                      <template v-else>
                          <el-popover
                          placement="top-start"
                          title=""
                          width="200"
                          trigger="hover"
                          :content="scope.row.message">
                          <el-tag slot="reference" type="danger">失败</el-tag>
                          </el-popover>
                      </template>
                      </template>

                  </el-table-column>
                  </el-table>
                  <el-table v-else size="medium" :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"  :data="Object.keys(resultTableData).length ? resultTableData : []">
                      <el-table-column prop="message" label="描述" />
                      <el-table-column prop="result" label="结果">
                      <template slot-scope="scope">
                          {{ scope.row.result || '--' }}
                      </template>
                      </el-table-column>
                      <el-table-column prop="success" label="状态">
                      <template slot-scope="scope">
                          {{ scope.row.success ? '成功' :'失败' }}
                      </template>
                      </el-table-column>
                  </el-table>
              </div>
              <div v-if="repActive === 'JSON'" style="max-height:540px ; overflow: auto;flex:1;margin-top: 16px">
                  <!-- <JsonViewer
                  class="json-content"
                  :value="resultTableData"
                  :expand-depth="5"
                  ></JsonViewer> -->
                  <div ref="editorResult" class="editor"></div>
              </div>
            </div>
        </div>
        </div>
    </div>
    <el-dialog custom-class="last-dialog" :center="true" :visible.sync="dialogVisibleV1" :modal="false" title="请选择测试数据" width="880px">
        <el-table ref="multipleTable" :data="selectTableData" border style="width: 100%;" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="数据类型" prop="sample_type" align="center" fixed width="120">
        </el-table-column>
        <el-table-column v-for="(column,key) of tableColumns" :key="key" :label="column?.e_field_desc" :prop="column?.d_mapping_field" align="center" width="120">
            <template slot-scope="scope">{{(scope.row[column?.d_mapping_field] === null || scope.row[column?.d_mapping_field] === '' || scope.row[column?.d_mapping_field] === undefined) ? 1 : scope.row[column?.d_mapping_field]}}</template>
        </el-table-column>
        <el-table-column label="样本结果" prop="eee" align="center" width="150">
            <template slot-scope="scope">
            <!-- {{ scope.row.sample_type === '正' ? '故障排查结束-正常' : '检查温控阀是否损坏' }} -->
            {{ scope.row.sample_result }}
            </template>
        </el-table-column>
        </el-table>
        <div style="margin-top: 20px; display:flex; justify-content:end;">
        <el-button type="primary" @click="confirm">确认</el-button>
        <el-button @click="cancelSelection">取消</el-button>
        </div>
    </el-dialog>
</div>
  </template>

  <script>
  import JsonViewer from 'vue-json-viewer'
  import dayjs from 'dayjs'
  import * as monaco from 'monaco-editor';
  import axios from 'axios'
  import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue'
  import { allEquipList, queryAbilityMapping, OnCodeTest, OnCodeTestNew, QueryAbilityData, queryDbList, queryOneEquipDetail, queryCodeParams, queryAbilityApi } from '@/api/planGenerateApi.js'
  const cancelToken = axios.CancelToken
  let source = cancelToken.source()
  export default {
    name: 'PreviewTemplate',
    components: {
      JsonViewer,
      shardUploaderTool
    },
    props: {
      agentSceneCode: {
        type: String,
        default: ''
      },
      schemeTestId: {
        type: Number|String,
        default: ''
      },
      id: {
        type: Number|String,
        default: ''
      },
    },
    data() {
      return {
        taskLoading: false,
        scheme_id: '',
        // agentSceneCode: '',
        dialogVisible: false,
        dialogVisibleV1: false,
        loading: false,
        tableLoading:false,
        reqActive: 'table',
        repActive: 'table',
        repJsonData: {},
        formData: {
          equipId: '',
          equipName: '',
          time: ''
        },
        tableData: [],
        newTableData: [],
        equipList: [],
        globalList: [],
        loading2: false,
        gridData: [],
        jsonData: {},
        hasJsonFlag: false,
        formModal: { },
        formRules: { },
        tableColumns: [],
        multipleSelection: [],
        // multipleSelectionCopy: {},
        selectTableData: [{ sample: 1 },{ sample: 0 }],
        testing: false,
        resultTableData: [],
        tWidth: '100%',
        editor: null,
        editorResult: null,
        filesList: [],
        tried: true,
        editorOptions: {
          selectOnLineNumbers: true,
          roundedSelection: false,
          scrollBeyondLastLine: false,
          readOnly: false,
          theme: 'vs-dark',
          language: 'json',
          wordWrap: 'on', // 设置自动换行
          formatOnType: true, // 设置自动格式化
          formatOnPaste: true, // 设置自动格式化
          insertSpaces: true, // 设置缩进方式为插入空格
          tabSize: 2, // 设置缩进大小为2
          minimap: {
            enabled: false // 不要小地图
          },
          fontFamily:
            'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
          folding: true
        }
      }
    },
    computed: {
      getUploadTip() {
        const temp = this.accept.map(item => item.substring(1, item.length))
        return `请上传与该模板匹配的测试图片，图片大小不超过4M，支持${temp.join('、')}`
      },
      pickerOptions1() {
        return {
          disabledDate(time) {
            const maxDate = Date.parse(dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'));
            return time.getTime() > maxDate;
          }
        }
      }
    },
    watch: {
      schemeTestId: {
        handler(val) {
          console.log('agentSceneCode', val, this.agentSceneCode);
          this.scheme_id = val
          this.handleOpen(val);
        },
        immediate: true
      }
    },
    mounted(){
      // this.handleOpen();
    },
    beforeDestroy() {
      source.cancel()
      source = cancelToken.source()
    },
    methods: {
      changeType() {
        this.$nextTick(() => {
          this.initEditor();
        })
      },
      meihuaJSON() {
        const data = this.editor.getValue();
        if (data) {
          // 格式化JSON数据并进行自动换行
          try {
            const formattedValue = JSON.stringify(JSON.parse(data), null, 2);
            this.editor.setValue(formattedValue);
          } catch (error) {
            console.log('格式化错误');
          }
        }

      },
      initEditor() {
        this.editor = monaco.editor.create(this.$refs.editor, {
          value: '',
          ...this.editorOptions
        });
        // 注册JSON模式
        monaco.languages.register({
          id: 'json',
        });
        // 设置JSON模式为编辑器的语言
        monaco.editor.setModelLanguage(this.editor.getModel(), 'json');
      },
      initResultJson() {
        monaco.editor.create(this.$refs.editorResult, {
          value: JSON.stringify(['expert_general', 'device_ops_assistant_scene','artificial_handle_scene','visit_leader_cognition_scene', 'intelligent_conversation_scene','sop_scene','digital_twin_assistant_scene','operational_optimization_scene'].indexOf(this.agentSceneCode) > -1 ? this.jsonData : this.resultTableData, null, 2),
          ...this.editorOptions,
          readOnly: true
        });
        // 注册JSON模式
        monaco.languages.register({
          id: 'json',
        });
        // 设置JSON模式为编辑器的语言
        monaco.editor.setModelLanguage(this.editor.getModel(), 'json');
      },
      changeShowType (val) {
        if(val === 'JSON') {
          this.$nextTick(() => {
            this.initResultJson(this.jsonData);
          })
        }
      },
      // 设备选择框
      async filterEquip(val) {
        this.loading2 = true;
        if (val) {
          await allEquipList({deviceId: val, 'scheme_id':this.scheme_id}).then(res => {
            this.loading2 = false;
            if (res.status === 200 && res.data.code === 200) {
              this.equipList = res.data.result || []
            }
          })
        } else {
          this.loading2 = false;
          this.equipList = this.globalList;
        }
      },
      getCodeParams(id, mid) {
        this.tableLoading = true
        queryAbilityApi({'ability_id': Number(mid)}).then(res => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('测试参数',res.data.result);
            this.newTableData = res?.data?.result?.req_body|| [];
          } else {
            this.newTableData = [];
          }
          // 物联场景走查询设备并且映射字段值，其他场景直接赋值
          if (this.agentSceneCode ==='device_ops_assistant_scene') {
            this.$nextTick(() => {
              this.getAllEquip(id)
            })
          } else {
            const temp = [];
            const temp2 = {};
            const files = [];
            res.data.result.req_body.forEach(item => {
              // 过滤掉deviceId和detectionTime
              if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
                temp2[item.param_name] = ''
                temp.push({...item, param_value: ''});
                files.push('');
              }
            })
            this.filesList = files;
            this.tableData = temp;
          }
        }).finally(()=>{
          this.tableLoading = false
        })
      },
      // 查询所有设备
      getAllEquip(id) {
        allEquipList({'scheme_id':Number(id)}).then(res => {
          if (res.status === 200 && res.data.code === 200) {
            this.equipList = res.data.result || []
            this.globalList = res.data.result || []
            if (res.data.result && res.data.result.length) {
              this.formData.equipId = res.data.result?.[0]?.deviceId + '' || '';
              this.formData.equipName = res.data.result?.[0]?.deviceName || '';
              console.log('所有设备', this.equipList);
              this.changeEquip(res.data.result?.[0].deviceId)
            } else {
              this.formData.equipId = ''
              this.formData.equipName = ''
              const temp = [];
              const temp2 = {};
              const files = [];
              this.newTableData.forEach(item => {
                // 过滤掉deviceId和detectionTime
                if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
                  temp2[item.param_name] = ''
                  temp.push({...item, param_value: ''});
                  files.push('');
                }
              })
              this.filesList = files;
              this.tableData = temp;
            }

          }
        })
      },
      changeEquip(val) {
        if (val) {
          this.tableLoading = true
          this.tableData = []
          const filtes = this.equipList.filter(item => item.deviceId == val)
          const curRow = filtes[0]
          console.log('当前行',this.gridData)
          this.equipName = curRow.deviceName || '';
          this.fixFiled = {};
          queryOneEquipDetail({
            'scheme_id': this.scheme_id,
            'detectionTime': dayjs(this.formData.time).format('YYYY-MM-DD HH:mm:ss'),
            'device_type_code': curRow.device_type_code,
            'deviceId': val
          }).then(res => {
            const temp = [];
            const temp2 = {};
            const files = [];
            this.newTableData.forEach(item => {
              // 过滤掉deviceId和detectionTime
              if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
                const filters = res.data?.result.filter(ritem => ritem.device_attribute_code === item.param_name)
                if (filters.length) {
                  temp2[item.param_name] = filters[0].device_attribute_value;
                  // item['param_value'] = filters[0].device_attribute_value;
                  temp.push({...item, param_value: filters[0].device_attribute_value});
                } else {
                  temp2[item.param_name] = ''
                  temp.push({...item, param_value: ''});
                }
                files.push('');
              }
            })
            this.filesList = files;
            this.tableData = temp;
            console.log('最后的数据--tableData',this.tableData);
          }).finally(()=>{
            this.tableLoading = false
          });
        }
      },
      handleAbilityMapping(id){
        this.loading = true;
        queryAbilityMapping({scheme_id: id})
          .then(async (res) => {
            if (res.status === 200 && res.data.code*1 === 200) {
              const configData = res.data.result?.config || {}
              const temp = configData?.data || [];
              this.gridData = configData?.data || [];
              await this.getCodeParams(id);
              this.loading = false;
            } else {
              this.loading = false;
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            this.loading = false;
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.loading = false;
          });
      },
      handleClose() {
        this.editor&&this.editor.dispose();
        this.jsonData = {};
        this.repJsonData = {};
        this.resultTableData = [];
        this.tableData = [];
        this.tableColumns = [];
        this.multipleSelection = [];
        this.selectTableData = [];
        this.repActive = 'table'
        this.reqActive = 'table'
        this.dialogVisible = false;
      },
       // 开启弹窗
       async handleOpen() {
        this.repActive = 'table'
        this.reqActive = 'table'
        this.resultTableData = [];
        this.tableColumns = [];
        this.multipleSelection = [];
        this.selectTableData = [];
        this.tableData = [];
        this.jsonData = {};
        // this.scheme_id = this.$route.query.scheme_id;
        if (this.agentSceneCode === 'device_ops_assistant_scene-v1') {
          this.querySchemaParam(this.scheme_id);
          this.dialogVisible = true;
        } else {
          const abc = dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
          this.$set(this.formData, 'time', abc || '');
          // this.handleAbilityMapping(mid);
          this.getAllEquip(this.scheme_id);
          await this.getCodeParams(this.scheme_id, this.id);
          this.dialogVisible = true;
        }
        this.$nextTick(() => {
          console.log('gggg', document.getElementById('rightBox').getBoundingClientRect())
          this.tWidth = document.getElementById('rightBox').getBoundingClientRect().width;
        })
        // this.tWidth = this.$ref.rightBox.width;
      },
      handlTest() {
        this.repActive = 'table'
        if (this.agentSceneCode === 'device_ops_assistant_scene') {
          if (!this.formData.time) {
            this.$message.warning('请选择测点时间后再进行测试')
            return false;
          }
          let params = {};
          if (this.reqActive === 'table') {
            const datas = {};
            this.tableData.forEach((item, index) => {
              if(item.data_type !=='File') {
                if(item.data_type =='Boolean' || item.data_type =='bool') {
                  console.log('进入这里', item.data_type);
                  const temp = item.param_value === 'true';
                  datas[item.param_name] = temp
                } else if(item.data_type =='Array' || item.data_type =='Object') {
                  try {
                    const temp = JSON.parse(item.param_value);
                    datas[item.param_name] = temp
                  } catch (error) {
                    const temp = item.param_value;
                    datas[item.param_name] = temp
                  }
                } else if(item.data_type =='String') {
                  datas[item.param_name] = item.param_value
                } else if(item.data_type =='Number') {
                  const temp = Number(item.param_value);
                  if (isNaN(temp)) {
                    datas[item.param_name] = item.param_value
                  } else {
                    datas[item.param_name] = temp
                  }
                } else {
                  if (item.param_value !== '') {
                    datas[item.param_name] = item.param_value
                  } else {
                    console.log('不传');
                    // datas[item.name] = item.value
                  }
                }
              } else {
                datas[item.param_name] = this.filesList[index]
              }
            })
            params = {
              'scheme_id':this.scheme_id,
              'deviceId': this.formData.equipId,
              'detectionTime': dayjs(this.formData.time).format('YYYY-MM-DD HH:mm:ss'),
              'params': datas,
              'oper_type': 'ability'
            };
          } else {
            const editData = this.editor.getValue();
            try {
              params = {
                'scheme_id':this.scheme_id,
                ...JSON.parse(editData),
                'oper_type': 'ability'
              };
            } catch (error) {
              this.$message.warning('输入的json格式错误，请修正后再进行测试')
              return false;
            }
          }
          this.taskLoading = true;
          const url = process.env.VUE_APP_PLAN_API.startsWith('/') ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/executev1' : process.env.VUE_APP_PLAN_API +  '/code/executev1'
          console.log('url', url)
          const userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}

          this.$axios.post(url, {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: params || {}
          }, {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/octet-stream'
            },
            onDownloadProgress:(event) => {
              this.taskLoading = false;
              const xhr = event.target;
              const { responseText } = xhr;
              this.$nextTick(() => {
                console.log('----流1----',responseText)
                this.jsonData = {
                  message: '执行成功',
                  result: responseText,
                  success: true
                }
              })
            },
            onError: function(error) {
              // 处理流错误
              console.error(error);
              this.taskLoading = false;
            }
          }).then(async res => {
            // 关闭数据流
            console.log('数据流',res);
            this.taskLoading = false;
            if (res.status === 200 && res.data.code === 200) {
              console.log('测试结果', res.data.result);
              if (res.data.result) {
                this.jsonData = res.data.result?.resp ||{}
              } else {
                this.jsonData = {
                  message: res.data.msg || '错误信息',
                  result: '',
                  success: false
                }
              }
            } else {
              if (res.status === 200) {
                this.$nextTick(() => {
                  console.log('----流000----',res.data)
                  this.jsonData = {
                    message: '执行成功',
                    result: res.data,
                    success: true
                  }
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            }
          }).catch(() => {
            this.taskLoading = false;
          })
          // OnCodeTestNew(params).then(async (res) => {
          //   this.taskLoading = false;
          //   if (res.status === 200 && res.data.code === 200) {
          //     console.log('测试结果', res.data.result);
          //     if (res.data.result) {
          //       this.jsonData = res.data.result?.resp ||{}
          //     } else {
          //       this.jsonData = {
          //         message: res.data.msg || '错误信息',
          //         result: '',
          //         success: false
          //       }
          //     }
          //   } else {
          //     this.$message({
          //       type: 'error',
          //       message: res.data?.msg || '接口异常!'
          //     });
          //   }
          // }).catch((_err)=>{
          //   this.$message({
          //   type: 'error',
          //   message: _err.data?.msg || '接口异常!'
          // });

          // }).finally(() => {
          //   this.taskLoading = false;
          // });
        } else {
          const datas = {'oper_type': 'ability'};
          this.tableData.forEach((item, index) => {
            if(item.data_type !=='File') {
              if(item.data_type =='Boolean' || item.data_type =='bool') {
                console.log('进入这里', item.data_type);
                const temp = item.param_value === 'true';
                datas[item.param_name] = temp
              } else if(item.data_type =='Array' || item.data_type =='Object') {
                try {
                  const temp = JSON.parse(item.param_value);
                  datas[item.param_name] = temp
                } catch (error) {
                  const temp = item.param_value;
                  datas[item.param_name] = temp
                }
              } else if(item.data_type =='String') {
                datas[item.param_name] = item.param_value
              } else if(item.data_type =='Number') {
                const temp = Number(item.param_value);
                if (isNaN(temp)) {
                  datas[item.param_name] = item.param_value
                } else {
                  datas[item.param_name] = temp
                }
              } else {
                if (item.param_value !== '') {
                  datas[item.param_name] = item.param_value
                } else {
                  console.log('不传');
                  // datas[item.name] = item.value
                }
              }
            } else {
              datas[item.param_name] = this.filesList[index]
            }
          })
          let params = {}
          if (this.reqActive === 'table') {
            params = {
              'scheme_id':this.scheme_id,
              'params': datas
            };
          } else {
            const editData = this.editor.getValue();
            try {
              params = {
                'scheme_id':this.scheme_id,
                'params': JSON.parse(editData),
                'oper_type': 'ability'
              };
            } catch (error) {
              params = {
                'scheme_id':this.scheme_id,
                'params': editData,
                'oper_type': 'ability'
              };
            }
          }
          this.taskLoading = true;
          const url = process.env.VUE_APP_PLAN_API.startsWith('/') ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute' : process.env.VUE_APP_PLAN_API +  '/code/execute'
          console.log('url', url)
          const userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}
          this.$axios.post(url, {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: { ...params, scene_type: this.agentSceneCode || '' } || {}
          }, {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/octet-stream'
            },
            cancelToken:source.token,
            onDownloadProgress:(event) => {
              this.taskLoading = false;
              const xhr = event.target;
              const { responseText } = xhr;
              const newStr = responseText.replace(/[\r\n]/g, '')
              this.$nextTick(() => {
                console.log('----流2----',responseText,newStr)
                this.jsonData = {
                  message: '执行成功',
                  result: newStr,
                  success: true
                }
              })
            },
            onError: function(error) {
              // 处理流错误
              console.error(error);
              this.taskLoading = false;
            }
          }).then(async res => {
            // 关闭数据流
            console.log('数据流',res);
            this.taskLoading = false;
            if (res.status === 200 && res.data.code === 200) {
              console.log('测试结果', res.data.result);
              if (res.data.result) {
                this.jsonData = res.data.result?.resp ||{}
              } else {
                this.jsonData = {
                  message: res.data.msg || '错误信息',
                  result: '',
                  success: false
                }
              }
            } else {
              if (res.status === 200) {
                this.$nextTick(() => {
                  const newStr = res.data.replace(/[\r\n]/g, '')
                  console.log('----流4----',res.data,newStr)
                  this.jsonData = {
                    message: '执行成功',
                    result: newStr,
                    success: true
                  }
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            }
          }).catch(() => {
            this.taskLoading = false;
          })
          // OnCodeTest(params).then(async (res) => {
          //   this.taskLoading = false;
          //   if (res.status === 200 && res.data.code === 200) {
          //     console.log('测试结果', res.data.result);
          //     if (res.data.result) {
          //       this.jsonData = res.data.result?.resp ||{}
          //     } else {
          //       this.jsonData = {
          //         message: res.data.msg || '错误信息',
          //         result: '',
          //         success: false
          //       }
          //     }

          //   } else {
          //     this.$message({
          //       type: 'error',
          //       message: res.data?.msg || '接口异常!'
          //     });
          //   }
          // }).finally(() => {
          //   this.taskLoading = false;
          // });
        }
      },
      async handlTestV1 () {
        console.log('test');
        if (this.reqActive === 'JSON') {
          let params = {};
          const editData = this.editor.getValue();
          try {
            params = {
              'scheme_id':this.scheme_id,
              'params': JSON.parse(editData),
              'oper_type': 'ability'
            };
          } catch (error) {
            params = {
              'scheme_id':this.scheme_id,
              'params': editData,
              'oper_type': 'ability'
            };
          }
          this.testing = true;
          const results = await OnCodeTest({ ...params, scene_type: this.agentSceneCode || ''})
          console.log(results)
          this.resultTableData = [results.data.result?.resp];
          // this.repJsonData = results.data.result?.resp;
          this.tried = false;
          this.testing = false;
        } else {
          this.tried = true;
          const validate = await this.$refs.formRef.validate().catch(()=>{})
          if (validate) {
            this.testing = true;
            this.resultTableData = [];
            this.multipleSelectionCopy = {};
            // this.multipleSelectionCopy = JSON.parse( JSON.stringify (this.multipleSelection));
            const length = this.multipleSelection.length || 1;
            let notLast = true;
            for (let i = 0; i < length; i++) {
              if (i >= length) {
                notLast = false;
              }
              if (this.multipleSelection?.length > 0) {
                this.setFormData(this.multipleSelection[i]);
              }
              const date = Date.now();
              const results = await OnCodeTest({
                scheme_id: this.scheme_id,
                scene_type: this.agentSceneCode || '',
                params: this.formModal,
                'oper_type': 'ability'
              })
              console.log(results)
              this.resultTableData.push({
                testData: this.multipleSelection?.[i]?.sample_type ? this.multipleSelection?.[i]?.sample_type : '当前测试数据',
                result: this.multipleSelection?.[i]?.sample_result || '--',
                success: results?.data?.result?.resp?.success,
                message: results?.data?.result?.resp?.message || '',
                real: results?.data?.result?.resp?.result || '--'
                // real: this.multipleSelection?.[i]?.sample_type === '正' ? this.multipleSelection?.[i]?.sample_result : results?.data?.result?.resp?.success ? results?.data?.result?.resp?.result : '--'
              });
              console.log('message', this.resultTableData);
            }
            this.testing = false;
          }
        }
      },
      async querySchemaParam(id) {
        const result = await QueryAbilityData({
          scheme_id: id
        })
        const result2 = await queryDbList(
          {
            sqlStatement: result.data.result.sql,
            scheme_id: id,
          })
        console.log('query', result2);
        if (!result2.data.success) {
          this.$message.error(result2.data.message);
        }
        if (result.data.result?.config?.data?.length && result2?.data?.data?.list?.length) {
          for (const [index,item] of result.data.result.config.data.entries()) {
            // const tempNumber = parseInt(Math.random() * 100);
            if (!item.d_mapping_field) {
              continue;
            }
            this.tableColumns.push({
              d_mapping_field:item.d_mapping_field,
              e_field_desc:item.e_field_desc,
            });
            /* this.$set(this.selectTableData[0], item.d_mapping_field, parseInt(Math.random() * 100))
            this.$set(this.selectTableData[1], item.d_mapping_field, parseInt(Math.random() * 100)); */
            // console.log('9999', result2.data.data.list);
            const temp = result2.data.data.list?.map(item => {
              const resetVals = {};
              Object.keys(item).forEach(key => {
                resetVals[key] = item[key] === '' ? 1 : item[key];
              });
              return resetVals;
            })
            console.log('新数据', temp);
            this.selectTableData = temp;
            // this.selectTableData = result2.data.data.list;
            this.$set(this.formModal, item.d_mapping_field, '');
            this.$set(this.formModal, 'pl'+(index+1), '');
            this.$set(this.formRules, item.d_mapping_field, [{required:true , message:'必填' }]);
          }
        }
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      cancelSelection() {
        this.dialogVisibleV1 = false;
        // this.multipleSelection = [];
      },
      setFormData(data) {
        // console.log(data, this.formModal)
        const dataKeys = Object.keys(data)
        for(const key of Object.keys(this.formModal)) {
          let value;
          if (dataKeys.length) {
            value = /^pl\d+$/.test(key) ? 1 : data[key]
          }
          console.log('赋值--', value);
          if (value === null || value === '' || value === undefined) {
            value = 1;
          }
          this.$set(this.formModal, key, value);
        }
      },
      confirm(){
        // console.log(this.multipleSelection[0])
        this.setFormData(this.multipleSelection?.[0] || {})

        this.dialogVisibleV1 = false;
      },
      uploadScriptCallback (fileData, fileList, index) {
        console.log('文件上传成功过',fileData, index)
        this.filesList[index] = fileData.path;
        console.log(this.filesList);
      },
      uploadShardStatusCb (status) {},
      removeFile (fileId, filelist, index) {
        console.log('删除的行', index)
        this.filesList[index] = '';
      },
    }
  }
  </script>

  <style lang="less" scoped>
  :deep(.el-date-editor--datetime .el-input__prefix) {
    left: calc(100% - 30px);
  }
  :deep(.el-input__icon) {
    line-height: 30px;;
  }
  :deep(.el-date-editor--datetime .el-input__suffix) {
    right: 25px;
  }
  :deep(.el-input--prefix .el-input__inner) {
      padding-left: 10px;
  }
  .editor {
      height: calc(100% - 100px);
      width: 100%;
      margin-left: 1px;
      flex:1
    }
  .mt {
    margin-top: 16px;
  }
  .dialog-container {
    height: 100%;
    display: flex;
    overflow: hidden;
    background: #fff;
    padding: 16px 20px;
    border-radius: 2px;;

    .lineV {
      height: 100%;
      border-left: 1px solid #f3f3f3;
      margin:0 10px;
    }
    .right {
      margin-left: 10px;
      width: calc(48% - 30px) !important;
    }
    .content {
      flex: 1;
      height: 100%;
      width: calc(50% - 30px);
      max-width: calc(50% - 30px);
      overflow: hidden;
      display:flex;
      flex-direction: column;
      .title{
        .text{
          height: 32px;
          font-weight: bold;
          line-height: 32px;
          margin-bottom: 5px;
        }
      }
      .box{
        width: 100%;
        max-height: 100%;
        display: flex;
        flex-direction: column;
        flex:1;
      }
    }
  }
  .planSearch {
      background-color: #fff;
      padding: 0px 20px 16px;
      .headerTitle {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
        padding: 14px 0px;
      }
      .button-last {
        line-height: 14px;
      }
      .searchFlex {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        .searchLeft {
          display: flex;
          flex-direction: row;
          align-items: center;
          flex: 1;
          .searchItem {
            max-width: 300px;
            margin-right: 16px;
            width: 30%;
            .searchLabel {
              font-weight: 400;
              color: #646566;
              line-height: 22px;
              font-size: 14px;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
    :deep(.el-form-item) {
      margin-bottom: 6px;
      flex: 1;
      margin-right: 24px;
    }
    :deep(.el-form-item__label-wrap) {
      margin-left: 0px !important;
      float: none;
      display: block;
    }
    :deep(.el-form-item__content) {
      display: flex;
      align-items: center;
      span {
        width: 35px;
        word-break: keep-all;
      }
    }
    :deep(.upload-demo) {
      display: flex;
      flex-direction: column !important;
      align-items: flex-start !important;
    }
  </style>
  <style lang="scss">
  .mytest {
    font-size: 14px;;
    .el-button--text {
      display: none !important;
    }
  }
  ::v-deep .el-button--mini {
    line-height: 0px !important;
    padding: 8px 6px !important;
    img {
      height: 16px;
      margin-top: -2px;
    }
  }
  .last-dialog {
    border-radius: 8px;
    .el-dialog__header {
      padding: 12px 20px;
      border-bottom: 1px solid #ebecf0;
      .el-dialog__title {
        font-size: 16px;
        color: #323233;
        line-height: 24px;
      }
      .el-dialog__headerbtn {
        top: 14px;
        .el-dialog__close {
          font-size: 18px;
        }
      }
    }
    .el-message-box__header {
      padding: 12px 20px;
      border-bottom: 1px solid #ebecf0 !important;
      .el-message-box__title {
        font-size: 16px;
        color: #323233;
        line-height: 24px;
      }
      .el-message-box__headerbtn {
        top: 14px;
        .el-message-box__close {
          font-size: 18px;
        }
      }
    }
    .el-message-box__content {
      padding: 16px 20px;
      .el-message-box__message {
        padding-left: 20px !important;
        padding-right: 20px !important;
      }
    }
    .el-message-box__btns {
      padding: 0px 20px;
      button {
        width: 60px !important;
      }
      .el-button {
        line-height: 20px !important;
      }
    }

    .el-dialog__body {
      padding: 16px 20px;
      max-height: 600px;
      overflow-y: auto;
    }
    &.small-last-dialog {
      .el-dialog__body {
        padding: 16px 20px;
        height: auto !important;
        max-height: 340px;
        overflow-y: auto;
      }
    }
    .el-dialog__footer {
      padding: 16px 20px;
      .el-button {
        line-height: 20px;
      }
    }
    .el-input__inner {
      border-radius: 2px;
    }
  }
  </style>
