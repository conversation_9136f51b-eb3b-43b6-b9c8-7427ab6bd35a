<template>
  <div class="feedback">
    <div v-if="tableData.list.length >0" class="header">
      用户建议 （{{tableData.total }}）
    </div>
    <div class="content">
      <div v-if="tableData.list.length >0">
      <el-card v-for="(item,index) in tableData.list" :key="index" class="box-card">
        <div class="feed"> <b>建议：</b> {{item.feedback_content}}</div>
        <div class="info">
          <Status
            :text="statusTypeMap[item.feedback_index]?.text"
            :bg-color="statusTypeMap[item.feedback_index]?.bgColor"
            :dot-color="statusTypeMap[item.feedback_index]?.dotColor"
          />
          <div class="info-item">
            <el-tooltip effect="dark" :content="item.feedback_user_nick_name + '(' + item.feedback_user_id + ')'" placement="top">
              <span v-if="item.feedback_user_nick_name" class="avator">{{ item.feedback_user_nick_name?.slice(-2) }}</span>
            </el-tooltip>
            <div class="separate"></div>
            <span>{{ item.feedback_time }}</span>
            <div class="separate"></div>
            <span>{{ item.ability_version }}</span>
          </div>

        </div>
      </el-card>
    </div>
    <el-empty v-else description="暂无数据" />
    </div>
    <div v-if="tableData.list.length >0"  style="text-align: right; padding-top: 16px">
        <el-pagination
          class="new-paper"
          layout="prev, pager, next, sizes, jumper"
          :page-sizes="[12, 24, 36, 48, 60]"
          :current-page.sync="tableData.page"
          :page-size="tableData.pageSize"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
  </div>

</template>
<script type="text/javascript">
import { queryFeedbackList } from '@/api/planGenerateApi'
import Status from '@/components/Status/index.vue';
export default {
  name: 'FeedbackCom',
  components: { Status },
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 12,
        total: 0,
      },
      statusTypeMap: {
        'error': { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '不准确' },
        'exact': { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '精确' }
      },
    }
  },
  watch:{
    versionId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getBindList()
        }
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    handleSizeChange(val) {
      this.tableData.page = 1;
      this.tableData.pageSize = val;
      this.getBindList();
    },
    handleCurrentChange(val) {
      this.tableData.page = val;
      this.getBindList();
    },
    // 反馈列表
    async getBindList() {
      await queryFeedbackList({
        ability_id: +this.$route.query?.id || '',
        page: this.tableData.page,
        page_size: this.tableData.pageSize,
      }).then(res => {
        if (res?.data?.code === 200) {
          console.log(res?.data.result,'99')
          this.tableData.list = res?.data?.result?.data||[]
          this.tableData.total = res?.data?.result?.total;
        } else {
          this.tableData.list =[]
          this.tableData.total = 0
          this.$message.error(res?.data?.msg)
        }
      }).catch(() => {
      })
    }
  }
}
</script>
<style lang="less" scoped>
.feedback{
  width:100%;
  height:100%;
  .header{
    margin-bottom: 16px;
    font-weight: bold;
  }
  .content{
    max-height: calc(100% - 100px);
    height: calc(100% - 100px);
  }
}
.box-card {
  margin-bottom: 16px;
  .info{
    display:flex;
    align-items: center;
    margin-top: 16px;
    .info-item{
      display:flex;
      align-items: center;
      margin-left:16px;
      color:#646566;
      .separate {
        width: 1px;
        height: 12px;
        margin: 0 8px;
        background: #C8C9CC;
      }
      .avator {
        width: 28px;
        height: 28px;
        line-height: 28px;
        border-radius: 50%;
        font-size: 10px;
        font-weight: 600;
        text-align: center;
        background: #E6ECFF;
        color: #4068D4;
      }
    }
  }
}




</style>
