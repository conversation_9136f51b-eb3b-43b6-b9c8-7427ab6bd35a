<template>
  <div v-loading="loading" style="height:100%">
    <svg v-if="agent_scene_code  === 'custom_cognition_assistant_scene'" id="markmap" class="optContentBox"></svg>
    <div v-else class="optContentBox" @mouseenter="fangda">
      <MyEditor id="MyFanganScheme" ref='MyFanganScheme' :mdContent="detailContent"></MyEditor>
      <!-- <pre v-if="(detailContent?.indexOf('graph') > -1 || detailContent?.indexOf('flowchart') > -1) && detailContent?.indexOf('mermaid')<0 && detailContent?.indexOf('```')<0"><div class="language-mermaid">{{detailContent}}</div></pre>
      <vue-markdown v-else :source="detailContent" class="markdown-body"></vue-markdown> -->
    </div>
  </div>
</template>
<script type="text/javascript">
import Status from '@/components/Status/index.vue'
import { queryComponentById } from '@/api/planGenerateApi.js'
import panzoom from 'panzoom';
import MyEditor from '../../mdEditor.vue';
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
export default {
  name: 'ExampleCom',
  components: { Status, MyEditor },
  props: {
    scheme_id:{
      type: String,
      default: '',
    },
    agent_scene_code:{
      type: String,
      default: '',
    }
  },
  watch: {
    scheme_id: {
      handler(val) {
        if (val) {
          this.panZoomRef = null;
          this.handleScheme(val);
        } 
      },
      immediate: true
    },
  },
  data() {
    return {
      detailContent: '',
      loading: false,
      panZoomRef: null
    };
  },
  methods: {
    fangda (e) {
        // console.log('开启缩放', e.target.getElementsByTagName('svg'));
        const svgdoms = e.target.getElementsByTagName('svg');
        const arr = [...svgdoms];
        arr.forEach((svgdom) => {
          if (svgdom.id.indexOf('mermaid') > -1) {
            panzoom(svgdom, {
              smoothScroll: false,
              bounds: true,
              // autocenter: true,
              zoomDoubleClickSpeed: 1,
              minZoom: 0.1,
              maxZoom: 20,
            })
          }
        })
      },
    handleScheme(id){
      this.loading = true;
      queryComponentById({component_id: id, type: this.agent_scene_code  === 'custom_cognition_assistant_scene' ? 'mind_map' : 'decision_tree'})
        .then((res) => {
          this.loading = false;
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.detailContent = res.data.result?.decision_making_content || '';
            this.panZoomRef = null;
            if (this.agent_scene_code  === 'custom_cognition_assistant_scene') {
              const nodeMarkmap = document.getElementById('markmap');
              if(nodeMarkmap) {
                nodeMarkmap.innerHTML = '';
              }
              const transformer = new Transformer()
              const { root } = transformer.transform(this.treeData)
              this.$nextTick(() => {
                Markmap.create('#markmap',null,root)
              })
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .catch((_err) => {
          this.loading = false;
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
  }

}
</script>
<style lang="less" scoped>
.optContentBox {
  height: 100%;
}
:deep(.vuepress-markdown-body){
  min-height: 100%!important;
  height: 100%!important;
}
</style>
