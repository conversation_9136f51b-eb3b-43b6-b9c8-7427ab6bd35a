<template>
  <div>
    <el-card class="container" shadow="never">
      <h2>基本信息</h2>
      <el-form ref="form-data" label-width="108px" :rules="rules" :model="ruleForm">
        <el-form-item label="返回状态码">
          <el-row type="flex" justify="center">
            <el-col :span="4">
              <el-input v-model="resCode" placeholder="请输入" />
            </el-col>
            <el-col :span="8">
              <el-form-item label="响应时间">
                <el-col :span="8">
                  <el-select v-model="currentResTimeType" placeholder="请选择" ƒ @change="exchange">
                    <el-option
                      v-for="item in resTimeType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-col>
                <el-col v-show="currentResTimeType !== '1'" :span="16">
                  <el-form-item v-show="currentResTimeType === '2'" prop="minCallTime">
                    <el-input v-model="ruleForm.minCallTime">
                      <i slot="suffix" style="font-style: normal; margin-right: 10px">秒</i>
                    </el-input>
                  </el-form-item>
                  <el-form-item v-show="currentResTimeType === '3'" prop="maxCallTime">
                    <el-input v-model="ruleForm.maxCallTime">
                      <i slot="suffix" style="font-style: normal; margin-right: 10px">秒</i>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-show="currentResTimeType === '1'" :span="8">
                  <el-form-item prop="minCallTime">
                    <el-input
                      v-model="ruleForm.minCallTime"
                      class="el-input-l"
                      suffix-icon="el-icon-minus"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-show="currentResTimeType === '1'" :span="8">
                  <el-form-item prop="maxCallTime">
                    <el-input v-model="ruleForm.maxCallTime" class="el-input-r">
                      <i slot="suffix" style="font-style: normal; margin-right: 10px">秒</i>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调用时间">
                <el-col :span="12">
                  <el-select v-show="defaultTime" v-model="currentCallTime" class="input-global">
                    <el-option
                      v-for="item in callTimeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-date-picker
                    v-show="!defaultTime"
                    v-model="value2"
                    :picker-options="pickerOptions"
                    :clearable="false"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="选择日期"
                    popper-class="-picker"
                  />
                  <el-time-picker
                    v-show="!defaultTime"
                    v-model="value1"
                    :clearable="false"
                    is-range
                    value-format="HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围"
                  />
                </el-col>
                <el-col :span="4"  >
                  <el-button type="primary" size="small" @click="defaultTime = !defaultTime">
                    {{ defaultTime ? '自定义' : '默认项' }}
                  </el-button>
                </el-col>
              </el-form-item>
            </el-col>
            <el-col :span="2" class="flex-end-center">
              <el-button size="small"   @click="resetForm()">
                重置
              </el-button>
              <el-button type="primary" size="small" @click="queryAlldata()"> 查询 </el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="loading" class="container" shadow="never">
      <div class="log-list">
        <h2>数据列表</h2>
        <div class="flex-end-center">
          <el-tooltip class="item" effect="dark" content="" placement="top">
            <div slot="content">刷新日志</div>
            <div @click="logRefresh()">
              <img src="@/assets/images/service/refresh.png" alt="" width="16" height="16" />
            </div>
          </el-tooltip>
<!--
          <el-tooltip class="item" effect="dark" content="" placement="top">
            <div slot="content">下载当页日志</div>
            <div @click="logDownload()">
              <img src="@/assets/images/service/download.png" alt="" width="16" height="16" />
            </div>
          </el-tooltip> -->
        </div>
      </div>

      <div class="TableBox">
        <el-table
          :header-cell-style="{
            background: '#F6F7FB',
            'font-size': '14px',
            color: '#323233',
            height: '46px',
            padding: '0'
          }"
          height="500"
          :data="logList"
          style="width: 100%"
          :default-sort="{ prop: 'date', order: 'descending' }"
          @sort-change="sortChange"
          @expand-change="expandChange"
        >
          <el-table-column type="expand">
            <template slot-scope="scope">
              <div class="table-inner">
                <p class="table-inner-header">请求内容:</p>
                <Json-viewer
                  v-if="scope.row.input"
                  :value="jsowviewerInput(scope.row.input)"
                  :expand-depth="4"
                  :copyable="{ copyText: '复制', copiedText: '已复制', timeout: 2000 }"
                >
                  <template slot="copy" slot-scope="{ copied }">
                    {{ copied ? '已复制' : '复制JSON' }}
                  </template>
                </Json-viewer>
                <p class="table-inner-header">返回内容:</p>
                <Json-viewer
                  :value="jsowviewerOutput(scope.row.output, scope.row.resCode, scope.row.callMsg)"
                  :expand-depth="4"
                  :copyable="{ copyText: '复制', copiedText: '已复制', timeout: 2000 }"
                >
                  <template slot="copy" slot-scope="{ copied }">
                    {{ copied ? '已复制' : '复制JSON' }}
                  </template>
                </Json-viewer>
              </div>
            </template>
          </el-table-column>

          <el-table-column show-overflow-tooltip prop="id" label="ID" min-width="90" />
          <el-table-column
            width="150"
            prop="startTime"
            label="调用时间"
            sortable="custom"
            :formatter="formatterTime"
          />
          <!-- <el-table-column min-width="100" prop="appName" label="应用名称">
            <template #default="scope">
              <a @click.prevent="goToAppDetail(scope.row)">{{ scope.row.appName }}</a>
            </template>
          </el-table-column>
          <el-table-column min-width="100" prop="appOwner" label="应用所属人" /> -->
          <el-table-column min-width="100" prop="xcontentType" label="调用类型" />
          <el-table-column
            min-width="120"
            prop="xcontentSize"
            label="参数大小(k)"
            :formatter="xcontentSize"
          />
          <el-table-column min-width="170" prop="resCode" label="返回状态码" />
          <el-table-column
            min-width="170"
            prop="callMsg"
            label="状态码说明"
            :show-overflow-tooltip="true"
          />
          <el-table-column width="150" prop="traceId" label="traceId">
            <template slot-scope="scope">
              <div class="flex">
                <el-tooltip
                  v-if="scope.row.traceId"
                  style="
                    width: calc(100% - 32px);
                    display: inline-block;
                    overflow: hidden;
                    vertical-align: middle;
                    text-align: left;
                  "
                  effect="dark"
                  placement="top"
                >
                  <div slot="content">
                    {{ scope.row.traceId }}
                  </div>
                  <div
                    style="
                      overflow: hidden;
                      width: 80%;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                  >
                    {{ scope.row.traceId }}
                  </div>
                </el-tooltip>

                <el-tooltip
                  v-if="scope.row.traceId"
                  style="
                    display: inline-block;
                    overflow: hidden;
                    vertical-align: middle;
                    text-align: left;
                  "
                  effect="dark"
                  content="复制"
                  placement="bottom"
                >
                  <div style="cursor: pointer" @click="handleCopyLink(scope.row.traceId)">
                    <i class="el-icon-document-copy" />
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>

          <el-table-column min-width="130" prop="callTime" label="响应时间(秒)" sortable="custom" />
        </el-table>
      </div>
      <div class="PagePaging">
        <el-pagination
          prev-text="上一页"
          next-text="下一页"
          background
          layout="total, sizes, prev, pager, next,jumper"
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50]"
          :total="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <el-image-viewer
      v-if="showViewer"
      :append-to-body="true"
      style="width: 100%; height: 100%"
      :on-close="closeViewer"
      :url-list="[url]"
    />
  </div>
</template>

<script>
import moment from 'moment';
import JsonViewer from 'vue-json-viewer';
import callTimeList from './callTimeList.js';
import { commonLogQuery } from '@/api/index.js';
import Vue from 'vue';
export default {
  name: 'LogCallTab',
  components: {
    JsonViewer
  },
  props: {
    // serviceName: {
    //   type: String,
    //   required: true,
    //   default: '',
    // },
    schenmeId: {
      required: true,
      default: 0,
    },
    // subServiceId: {
    //   required: true,
    //   default: '',
    // },
  },

  data() {
    return {
      startTime: moment().format('YYYY') + '-01-01T00:00:00',
      endTime: moment().format('YYYY') + '-12-01T00:00:00',
      rules: {
        minCallTime: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (!/(^[1-9]\d*$)/.test(value) && value !== null && value !== '') {
                callback(new Error('输入框内容只能是正整数'));
              } else if (this.ruleForm.maxCallTime && value) {
                if (+this.ruleForm.maxCallTime < +value) {
                  callback(new Error('输入的起始值必须小于终止值'));
                }
              }
              callback();
            },
            trigger: 'blur'
          }
        ],
        maxCallTime: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (!/(^[1-9]\d*$)/.test(value) && value !== null && value !== '') {
                callback(new Error('输入框内容只能是正整数'));
              } else if (this.ruleForm.minCallTime && value) {
                if (+this.ruleForm.minCallTime > +value) {
                  callback(new Error('输入的起始值必须小于终止值'));
                }
              }
              callback();
            },
            trigger: 'blur'
          }
        ]
      },
      proDialog: {
        proDialogVisible: false,
        percent: 0
      },
      pickerOptions: {
        disabledDate(time) {
          const time1 = Date.now() - 30 * 60 * 60 * 24 * 1000;
          return time.getTime() > Date.now() || time.getTime() < time1;
        }
      },
      topList: [
        {
          label: 'top5',
          value: '5'
        },
        {
          label: 'top10',
          value: '10'
        }
      ],
      currentYear: +moment().format('YYYY'),
      currentYearList: [],
      currentTop: '5',
      pageNum: 1,
      pageSize: 10,
      pageTotal: 0,
      currentResTimeType: '1',
      resTimeType: [
        {
          label: '区间',
          value: '1'
        },
        {
          label: '>=',
          value: '2'
        },
        {
          label: '<',
          value: '3'
        }
      ],
      logList: [],
      value1: ['00:00:00', '23:59:59'],
      value2: moment().subtract(1, 'days').format('YYYY-MM-DD'),
      defaultTime: true,
      algCode: null,
      timeout: null,
      restaurants: [],
      restaurantsUser: [],
      callTimeList: [],
      currentCallTime: '3',
      appName: [],
      userAppId: '',
      user: '',
      userId: '',
      userName: '',
      appService: [],
      myChart: null,
      myChart2: null,
      myChart3: null,
      myChart4: null,
      currentRequestUrl: '',
      showViewer: false,
      url: '',
      resCode: null,
      RequestUrlList: '',
      loading: false,
      sortType: null,
      sortField: null,
      ruleForm: {
        minCallTime: null,
        maxCallTime: null
      },
      resCodeList: [],
      codeMaxCount: 400,
      startDayTime: moment().format('YYYY-MM') + '-01T00:00:00',
      endDayTime: moment().format('YYYY-MM') + '-31T00:00:00',
      currentMonth: '',
      option: {}
    };
  },
  created() {
    this.callTimeList = callTimeList.value;
    const current = moment().format('YYYY');
    const value = moment().format('MM');
    this.currentMonth = value;
    const index = this.startTime.lastIndexOf('-');
    const days = moment(moment().format('YYYY-MM'), 'YYYY-MM').daysInMonth();
    this.startDayTime =
      this.startTime.slice(0, index - 2) +
      value +
      '-01' +
      this.startTime.slice(index + 3, this.startTime.length + 1);
    this.endDayTime =
      this.startTime.slice(0, index - 2) +
      value +
      '-' +
      days +
      this.startTime.slice(index + 3, this.startTime.length + 1);
    for (let i = 2021; i <= current; i++) {
      this.currentYearList.push({ value: i, label: i + '年' });
    }
  },

  mounted() {
    this.handleApi();
  },

  methods: {
    async handleApi() {
      // try {
      //   const params = {
      //     id: this.serviceId,
      //   };
      //   const algCode = await getBasic(params);
      //   this.algCode = algCode.data.data.serviceIdName;
      // } catch (error) {
      //   throw new Error(error, 'algCode');
      // }

      // // 接口地址信息
      // await this.getInterfaceInfo();
      // //  应用名称
      // await this.getAppService();
      await this.queryAlldata();
      // this.$nextTick(() => {
      //   this.getTimeStatics();
      // });
    },
    goToAppDetail(val) {
      const id = val.aiServiceAppId;
      const title = val.appName;
      this.$router.push({
        path: '/ServiceManagement',
        query: {
          id,
          title,
          workspaceId: this.$route.query.workspaceId || null,
          workspaceName: this.$route.query.workspaceName || null
        }
      });
    },
    resetForm() {
      this.defaultTime = true;
      this.currentCallTime = '3';
      this.userAppId = '';
      this.userId = null;
      this.resCode = null;
      this.ruleForm.minCallTime = null;
      this.ruleForm.maxCallTime = null;
      this.value1 = ['00:00:00', '23:59:59'];
      this.value2 = moment().subtract(1, 'days').format('YYYY-MM-DD');
    },
    // 展开json-view数据，重写a标签文件下载
    async expandChange(row, expandedRows) {
      try {
        setTimeout(() => {
          const urlList = document.querySelectorAll('.jv-item a');
          if (urlList.length > 0) {
            urlList.forEach((el) => {
              el.addEventListener('click', (e) => {
                const reg = /\.(png|jpg|gif|pdf)$/;
                if (!reg.test(el.innerHTML)) {
                  e.preventDefault();
                  const downloadUrl = el?.href;
                  // const downloadUrl = el.innerHTML;
                  const fileNameList = el.innerHTML.split('/');
                  const filenameStr = fileNameList[fileNameList.length - 1];
                  const idx = filenameStr.indexOf('?');
                  const filename = filenameStr.substring(0, idx);
                  this.proDialog.percent = 0;
                  this.$fileUtil.download(downloadUrl, filename, this.proDialog);
                }
              });
            });
          }
        }, 100);
      } catch (error) {
        throw new Error(error);
      }
    },
    jsowviewerInput(val) {
      try {
        return JSON.parse(val);
      } catch (error) {
        const input = { input: JSON.stringify(val) };
        return JSON.parse(JSON.stringify(input));
      }
    },
    jsowviewerOutput(val, code, msg) {
      try {
        if (code != '200' || val === '') {
          const output = { callMsg: msg };
          return JSON.parse(JSON.stringify(output));
        } else {
          return JSON.parse(val);
        }
      } catch (error) {
        const input = { output: val };
        return JSON.parse(JSON.stringify(input));
      }
    },
    formatterTime(row, column, cellValue) {
      return cellValue.replace('T', ' ');
    },

    // 复制
    handleCopyLink(str) {
      const _this = this;
      this.$copyText(str).then(
        function (e) {
          _this.$message({
            showClose: true,
            message: '复制成功',
            type: 'success'
          });
        },
        function (e) {
          _this.$message({
            showClose: true,
            message: '复制失败，请手动复制',
            type: 'error'
          });
        }
      );
    },
    leaveUserApp() {
      if (!this.restaurants.some((el) => el.value === this.userAppId)) {
        this.userAppId = '';
      }
    },
    leaveUserId() {
      if (!this.restaurantsUser.some((el) => el.value === this.userId)) {
        this.userId = '';
      }
    },
    xcontentSize(row, column, cellValue) {
      if (cellValue === -1) {
        return '--';
      }
      return cellValue;
    },
    sortChange(column) {
      const sortType = column.order === 'ascending' ? 'asc' : 'desc';
      const sortField = column.prop === 'startTime' ? 'start_time' : 'call_time';
      this.sortType = sortType;
      this.sortField = sortField;
      this.getAlldata();
    },
    selectTop(val) {
      try {
        const resCodeListSort = JSON.parse(JSON.stringify(this.resCodeList));
        const contrast = (property) => {
          return function (a, b) {
            const val1 = a[property];
            const val2 = b[property];
            return val2 - val1;
          };
        };
        if (val == 10) {
          resCodeListSort.sort(contrast('count')).splice(10);
        } else {
          resCodeListSort.sort(contrast('count')).splice(5);
        }
      } catch (error) {
        throw new Error(error, 'top报错');
      }
    },

    // 分页大小
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getAlldata(false);
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getAlldata(false);
    },
    // 日志下载
    logDownload() {
      if (this.logList.length < 1) return;
      const params = {
        algCode: this.algCode,
        maxStartTime: this.defaultTime
          ? this.callTimeList[this.currentCallTime].maxStartTime.split(' ').join('T')
          : this.value2 + 'T' + this.value1[1],
        minStartTime: this.defaultTime
          ? this.callTimeList[this.currentCallTime].minStartTime.split(' ').join('T')
          : this.value2 + 'T' + this.value1[0],
        page: this.pageNum,
        pageSize: this.pageSize,
        sortType: this.sortType || 'desc',
        sortField: this.sortField || 'start_time',
        requestUrlPrefix: this.requestUrlPrefix
        // serviceName: this.serviceName,
      };
      this.$axios
        .post(this.baseUrl + '/openApi/service/result/downloadLogs', params, {
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json; application/octet-stream'
          }
        })
        .then((res) => {
          if (res.status == 200) {
            const disposition = res.headers['content-disposition'];
            let fileName = disposition.substring(
              disposition.indexOf('filename=') + 9,
              disposition.length
            );
            fileName = decodeURI(escape(fileName));
            fileName = fileName.replace(/\"/g, '');
            console.log(res, '获取下载');
            const fileDownload = require('js-file-download');

            fileDownload(res.data, fileName);
          }
        });
    },
    // 响应时间 区间和>= < 切换
    exchange() {
      this.ruleForm.maxCallTime = null;
      this.ruleForm.minCallTime = null;
    },
    // oss地址预览
    previewfile(val) {
      if (val.indexOf('.png') !== -1 || val.indexOf('.jpg') !== -1) {
        this.url = val;
        this.showViewer = true;
      } else {
        window.open(val);
      }
    },
    // 查询全量数据
    queryAlldata() {
      // this.getGroupByCallTime()
      this.pageSize = 10;
      this.pageNum = 1;
      this.getAlldata();
    },
    async logRefresh() {
      this.pageNum = 1;
      this.pageSize = 10;
      this.loading = true;
      await this.getAlldata();
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },

    // 应用名称列表
    querySearchAsync(queryString, cb) {
      if (this.user !== '') {
        const arr = [];
        this.appService.forEach((el) => {
          if (el.id === this.user) {
            arr.push({
              userAppId: el.userAppId,
              value: el.appName
            });
          }
        });
        this.restaurants = arr;
      }
      if (this.userAppId == '' && this.userId == '') {
        this.setAppServiceValue();
      }
      const results = queryString
        ? this.restaurants.filter(this.createStateFilter(queryString))
        : this.restaurants;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 100 * Math.random());
    },

    // 所属人列表
    querySearchAsyncUser(queryString, cb) {
      if (this.userApp !== '') {
        const arr = [];
        this.appService.forEach((el) => {
          if (el.userAppId === this.userApp) {
            arr.push({
              userId: el.id,
              value: el.userName
            });
          }
        });
        this.restaurantsUser = arr;
      }
      if (this.userAppId == '' && this.userId == '') {
        this.setAppServiceValue();
      }
      const results = queryString
        ? this.restaurantsUser.filter(this.createStateFilterUser(queryString))
        : this.restaurantsUser;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 100 * Math.random());
    },
    createStateFilter(queryString) {
      const restaurants = this.restaurants;
      return (restaurants) => {
        const res = restaurants.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;

        return res;
      };
    },
    createStateFilterUser(queryString) {
      const restaurantsUser = this.restaurantsUser;
      return (restaurantsUser) => {
        const res = restaurantsUser.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;

        return res;
      };
    },
    handleSelectAppName(val) {
      this.userApp = val.userAppId;
      if (this.userAppId) {
        this.userId = this.appService.find((el) => el.userAppId === val.userAppId).userName;
      }
    },

    handleUser(val) {
      this.user = val.userId;
    },

    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    },

    // 应用名称和所属人list
    setAppServiceValue() {
      this.restaurants = this.appService.map((res) => {
        return {
          userAppId: res.userAppId,
          value: res.appName
        };
      });
      this.restaurantsUser = this.appService.map((res) => {
        return {
          userId: res.id,
          value: res.userName
        };
      });
    },

    async getTimeStatics() {
      this.myChart3 = this.$echarts.init(document.getElementById('time-statistics-left'));
      this.myChart4 = this.$echarts.init(document.getElementById('time-statistics-right'));

      this.myChart3.off('click');
      this.myChart3.getZr().on('click', (params) => {
        const pointInPixel = [params.offsetX, params.offsetY];
        if (this.myChart3.containPixel('grid', pointInPixel)) {
          const xIndex = this.myChart3.convertFromPixel({ seriesIndex: 0 }, [
            params.offsetX,
            params.offsetY
          ])[0];

          let value = Math.abs(xIndex) + 1;
          const index = this.startTime.indexOf('-');
          if (value < 10) value = '0' + value;

          const days = moment(
            this.startTime.slice(0, index) + '-' + value,
            'YYYY-MM'
          ).daysInMonth();

          this.startDayTime =
            this.startTime.slice(0, index + 1) +
            value +
            '-01' +
            this.startTime.slice(index + 6, this.startTime.length + 1);
          this.endDayTime =
            this.startTime.slice(0, index + 1) +
            value +
            '-' +
            days +
            this.startTime.slice(index + 6, this.startTime.length + 1);
          this.currentMonth = value;
        }
      });

      const params1 = {
        algCode: this.algCode,
        startTime: this.startTime,
        endTime: this.endTime
      };
      const respMonth = await statisticsMonthServiceCall(params1);
      const drawLineOne = this.getResMonth(respMonth);
      const c = drawLineOne.dataY.map((el) => {
        return el[1];
      });
      const sumY1 = this.sumItem(c);
      drawLineOne.dataY.push({ 0: '总计', 1: sumY1 });
    },

    // 切换年份
    selectYear(val) {
      this.startTime = val + '-01-01T00:00:00';
      this.endTime = val + '-12-01T00:00:00';
      const index = this.startDayTime.indexOf('-');
      this.startDayTime = val + this.startDayTime.slice(index, this.startDayTime.length);
      this.endDayTime = val + this.endDayTime.slice(index, this.endDayTime.length);
      this.getTimeStatics();
    },

    // 数组相加
    sumItem(arr) {
      const array = [];
      for (let i = 0; i < arr.length; i++) {
        arr[i].forEach((val, idx) => {
          if (array[idx] === null || array[idx] === '' || !array[idx]) {
            array[idx] = 0;
          }

          array[idx] += val?.callCount;
        });
      }

      return array;
    },

    getResMonth(respMonth) {
      if (respMonth.data.status === 200) {
        const data = respMonth.data.data;
        const time = this.startTime;
        const index = this.startTime.lastIndexOf('-');
        const dataX1 = [];
        const dataList = Object.entries(data);
        for (let i = 1; i <= 12; i++) {
          if (i < 10) i = '0' + i;
          const obj = {
            statisticsDate: time.slice(0, index - 2) + i + time.slice(index, time.length),
            callName: '',
            callCount: 0,
            successCount: 0
          };
          dataX1.push(obj);
        }
        return this.cloneDataY(dataList, dataX1);
      }
    },

    cloneDataY(dataList, dataX1) {
      const dataY = [];
      let count = 0;
      while (count < dataList.length) {
        // 这里必须深拷贝
        dataY.push(JSON.parse(JSON.stringify(dataX1)));
        count++;
      }
      const filterDataY = dataY.map((el, index) => {
        for (const j of dataList[index][1]) {
          el.forEach((ele, idx) => {
            if (j.statisticsDate === ele.statisticsDate) {
              ele.callCount = j.callCount;
              ele.successCount = j.successCount;
            }
          });
        }
        return { 0: dataList[index][0], 1: el };
      });
      if (filterDataY.length <= 0) {
        filterDataY.push({ 0: 'null', 1: dataX1 });
      }
      const dataX = dataX1.map((el) => {
        const index = el.statisticsDate.lastIndexOf('-');
        return el.statisticsDate.slice(0, index);
      });

      const a = {
        dataX: dataX,
        dataY: filterDataY
      };
      return a;
    },

    // 过滤获取按天计算的调用次数
    getResDay(respDay) {
      if (respDay.data.status === 200) {
        const data = respDay.data.data;
        const dataX1 = [];
        const index = this.startDayTime.lastIndexOf('-');
        const time = this.startDayTime;
        const days = moment(time.slice(0, index), 'YYYY-MM').daysInMonth();
        for (let i = 1; i <= days; i++) {
          if (i < 10) i = '0' + i;
          const obj = {
            statisticsDate: time.slice(0, index + 1) + i + time.slice(index + 3, time.length + 1),
            callName: '',
            callCount: 0,
            successCount: 0
          };
          dataX1.push(obj);
        }
        const dataList = Object.entries(data);

        const dataY = [];
        let count = 0;
        while (count < dataList.length) {
          // 这里必须深拷贝
          dataY.push(JSON.parse(JSON.stringify(dataX1)));
          count++;
        }
        const filterDataY = dataY.map((el, index) => {
          for (const j of dataList[index][1]) {
            el.forEach((ele, idx) => {
              if (j.statisticsDate === ele.statisticsDate) {
                ele.callCount = j.callCount;
                ele.successCount = j.successCount;
              }
            });
          }

          return { 0: dataList[index][0], 1: el };
        });

        if (filterDataY.length <= 0) {
          filterDataY.push({ 0: 'null', 1: dataX1 });
        }
        const dataX = dataX1.map((el) => {
          const index = el.statisticsDate.indexOf('T');
          return el.statisticsDate.slice(0, index);
        });

        const a = {
          dataX: dataX,
          dataY: filterDataY
        };
        return a;
      }
    },

    async getAlldata(refreshAll = true) {
      try {
        if (refreshAll) {
          this.callTimeList = [
            {
              value: '0',
              minStartTime: moment().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
              maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              label: '当日最近5分钟'
            },

            {
              value: '1',
              minStartTime: moment().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
              maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              label: '当日最近30分钟'
            },
            {
              value: '2',
              minStartTime: moment().subtract(60, 'minute').format('YYYY-MM-DD HH:mm:ss'),
              maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              label: '当日最近1小时'
            },
            {
              value: '3',
              minStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
              maxStartTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
              label: '当日全天'
            }
          ];
        }
        const userAppIdList = [];
        if (this.userAppId === '' && this.user !== '') {
          this.restaurants.forEach((el) => {
            if (el.id === this.user) userAppIdList.push(el.userAppId);
          });
        } else if (this.userAppId !== '') {
          userAppIdList.push(this.userApp);
        }
        const params = {
          resCode: this.resCode === '' ? null : this.resCode,
          algCode: this.algCode,
          maxCallTime: this.ruleForm.maxCallTime === '' ? null : this.ruleForm.maxCallTime,
          maxStartTime: this.defaultTime
            ? this.callTimeList[this.currentCallTime].maxStartTime.split(' ').join('T')
            : this.value2 + 'T' + this.value1[1],
          minCallTime: this.ruleForm.minCallTime === '' ? null : this.ruleForm.minCallTime,
          minStartTime: this.defaultTime
            ? this.callTimeList[this.currentCallTime].minStartTime.split(' ').join('T')
            : this.value2 + 'T' + this.value1[0],
          userAppIdList: userAppIdList,
          schemeId: this.schenmeId
        };
        Object.assign(params, {
          page: this.pageNum,
          pageSize: this.pageSize,
          sortType: this.sortType || 'desc',
          sortField: this.sortField || 'start_time'
        });
        this.logList = [];

        if (!params.schemeId) {
          // this.$nextTick(() => {
          //   this.$message.error({
          //     message: '接口地址为空，无法调用接口'
          //   })
          // })
        } else {
          commonLogQuery(params).then((commonLog) => {
            if (commonLog.data.status === 200) {
              const { total, list } = commonLog.data.data;
              this.logList = list;
              this.pageTotal = total;
            } else {
              this.$message.error({
                message: commonLog.data.msg
              });
            }
          });
        }
      } catch (error) {
        throw new Error(error, 'getAlldata');
      }
    },
    numberFormat(codeMaxCount) {
      const _codeMaxCount = codeMaxCount;
      if (codeMaxCount % 5 !== 0) {
        for (let i = 1; i < _codeMaxCount * 4; i++) {
          if ((codeMaxCount + i) % 5 == 0) {
            return codeMaxCount + i;
          }
        }
      }
      return codeMaxCount;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  overflow-y: hidden;
  position: relative;
  margin: 10px;
  .select-year {
    position: absolute;
    right: 35px;
    top: 15px;
  }

  h2 {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #323233;
    line-height: 30px;
  }

  .el-table {
    .table-inner {
      background: #f2f3f5;
      padding: 10px;
      .table-inner-header {
        margin: 0 16px;
      }
      .jv-container.jv-light {
        background: #f2f3f5;
      }
      ::v-deep .jv-container .jv-code {
        overflow: hidden;
        padding: 0px 20px;
      }
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .el-button {
    float: right;
    &:first-child {
      background: #f2f3f5;
      color: #4068d4;
      border: none;
    }
  }
  .log-list {
    display: flex;
    position: relative;
    > div {
      position: absolute;
      right: 20px;
      > div {
        cursor: pointer;
        padding: 4px 6px;
        margin-left: 8px;
        background: #f2f3f5;
      }
    }
  }
  .input-global {
    width: 100%;
  }
  .el-input-l {
    ::v-deep .el-input__inner {
      border-right: none;
      border-radius: 4px 0 0 4px;
    }
    ::v-deep .el-input__suffix {
      right: -10px;
    }
  }
  .el-input-r {
    ::v-deep .el-input__inner {
      border-left: none;
      border-radius: 0 4px 4px 0;
    }
  }
  .exhibition {
    display: flex;
    position: relative;
    .current-month {
      position: absolute;
      right: 40px;
      top: 10px;
    }
    .exhibition-box {
      border: 1px solid #eaebf0;
      padding: 5px;
      margin: 5px 10px;
      width: 50%;
      position: relative;
      .exhibition-box-inner {
        position: absolute;
        right: -30px;
        top: 10px;
        z-index: 1;
        ::v-deep .el-input {
          width: 70%;
        }
      }
    }
  }
  .TableBox {
    padding: 20px;
    overflow-y: auto;
    .el-button--small {
      font-size: 14px;
    }
  }

  .PagePaging {
    margin: 20px;
    text-align: right;
    ::v-deep {
      .el-pagination .el-pagination__total,
      .el-pagination .el-pagination__sizes {
        float: left;
      }
    }
  }
  ::v-deep .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 240px;
  }
  ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 240px;
  }
  ::v-deep .el-input--prefix .el-input__inner {
    padding-left: 40px;
  }
  ::v-deep .el-date-editor {
    .el-range-separator {
      width: 9%;
      line-height: 32px;
    }
    .el-range__icon {
      line-height: 25px;
    }
  }
}
</style>
<style lang="scss">
.-picker {
  .el-picker-panel__body {
    .el-date-picker__header {
      span:nth-child(3) {
        pointer-events: none;
      }
    }
  }
}
</style>
