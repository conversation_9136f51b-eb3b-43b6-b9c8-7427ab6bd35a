<template>
  <div class="card">
    <div v-if="isconfirm && Object.keys(descriptionObj).length < 1" class="tab-add">
      <el-card class="box-card">
        <el-button @click="openModal('set')" type="default">新增卡片<i class="el-icon-plus"></i></el-button>
      </el-card>
    </div>
    <div v-loading="isloading" v-if="Object.keys(descriptionObj).length > 0" class="tab-card">
      <div class="editCard">
        <div class="heard">
          <el-button @click="openModal('put')" type="primary">编辑卡片</el-button>
          <el-button v-if="descriptionObj?.shelves.length<=0" @click="onTheRack">上架</el-button>
        </div>
      </div>
      <el-divider></el-divider>
      <el-descriptions title="" :column="3">
        <el-descriptions-item label="卡片ID">{{ descriptionObj?.id }}</el-descriptions-item>
        <el-descriptions-item label="卡片名称">{{ descriptionObj?.name }}</el-descriptions-item>
        <el-descriptions-item label="组件ID">{{ descriptionObj?.material_info?.id }}</el-descriptions-item>
        <el-descriptions-item label="组件名称">{{ descriptionObj?.material_info?.name }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ descriptionObj?.createdAt | formatterTime }}</el-descriptions-item>
        <el-descriptions-item label="上线时间">{{ descriptionObj?.updatedAt | formatterTime }}</el-descriptions-item>
        <!-- <el-descriptions-item label="版本">{{descriptionObj?.material_info?.version}}</el-descriptions-item> -->
        <el-descriptions-item label="版本">{{ descriptionObj?.version }}</el-descriptions-item>
        <el-descriptions-item label="上架状态">{{ descriptionObj?.shelves?.length > 0 ? '已上架' : '未上架' }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">{{ descriptionObj?.description }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="" :column="1">
        <el-descriptions-item label="标签">

          <!-- <el-select v-if="isEdit" ref="tagsSelect" v-model="baseForm.tag_ids" style="width:328px" multiple filterable
            placeholder="请选择标签" clearable :filter-method="handleTagFilter" @keyup.native.enter="addBizTag"
            @change="changeTags">
            <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <div v-else style="width: 328px">
            <selectItem :array-items="arrayItems" :max-length="2">
            </selectItem>
          </div> -->
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="" :column="1">
        <el-descriptions-item label="卡片预览">
          <div class="preview" v-if="descriptionObj?.thumbnail!=''">
            <img width="300" :src="descriptionObj?.thumbnail" alt="卡片预览" />
          </div>
          <div v-else>
            无
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" custom-class="dialog-card" @closed="operateEvent('cancel')">
      <template #title>
        <div class="dialog-title">
          {{ dialogHeard }}
        </div>
      </template>
      <el-form ref="addCard" :model="addCardForm" :rules="rules" label-width="98px">
        <el-form-item label="卡片名称：" prop="name">
          <el-input v-model="addCardForm.name" :readonly="type === 'put'" placeholder="请输入卡片名称"></el-input>
        </el-form-item>
        <el-form-item label="组件：" prop="materials">
          <el-select v-model="addCardForm.materials" placeholder="请选择组件" clearable @change="dataCompare">
            <el-option v-for="(item,index) in materialsList" :key="index" :label="item.name1"
                       :value="item.id">
              <span>{{ item.name1 }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布类型：" prop="releaseType" v-if="type === 'put'">
          <el-radio-group v-model="addCardForm.releaseType" size="small">
            <el-radio-button label="custome"> custome</el-radio-button>
            <el-radio-button label="major"> major</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="版本：" prop="version">
          <el-input v-if="addCardForm.releaseType=='custome'" v-model="addCardForm.version" style="width: 50%;"
                    placeholder="请输入版本"></el-input>
          <el-input v-else value="" style="width: 50%;" placeholder="版本号自动生成（非兼容型升级）" disabled></el-input>
        </el-form-item>
        <el-form-item label="是否授权：" prop="accredit">
          <el-switch v-model="addCardForm.accredit" disabled active-text="是" inactive-text="否">
          </el-switch>
        </el-form-item>
        <el-form-item label="认证：" prop="authentication">
          <el-select v-model="addCardForm.authentication" value-key="app_id" placeholder="请选择认证" clearable @change="dataCompare">
            <el-option v-for="(item,index) in authenticationList" :key="index" :label="item.app_name" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="addCardForm.description" rows="3" type="textarea" placeholder="不超过300个字符"
                    maxlength="300"
                    show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-check" class="solid-with-icon-btn"
                   :disabled="dataUpdateDisable"
                   @click="operateEvent('confirm')">确认
        </el-button>
        <el-button class="hollow-with-icon-btn" icon="el-icon-close" @click="operateEvent('cancel')">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogUpload" width="40%" custom-class="dialog-card" @closed="operateEvent('cancel')">
      <template #title>
        <div class="dialog-title">
          上架
        </div>
      </template>
      <el-form ref="Upload" :model="upLoadForm" :rules="rules" label-width="98px">
        <el-form-item label="卡片名称：" prop="name">
          <el-input v-model="upLoadForm.name" placeholder="请输入卡片名称"></el-input>
        </el-form-item>
        <el-form-item label="版本：" prop="version">
          <el-input v-model="upLoadForm.version" placeholder="版本"></el-input>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="upLoadForm.description" rows="3" type="textarea" placeholder="不超过300个字符"
                    maxlength="300"
                    show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="缩略图：" prop="abilityIcon">
          <div>
            <el-upload ref="uploadBtn" class="upload-demo" :action="uploadUrl"
                       list-type="picture-card" :data="uploadParam" :limit="1" :file-list="fileList" :multiple="false"
                       accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG" :before-upload="beforeUpload"
                       :on-success="modelUploadSuccess"
                       :on-exceed="uploadExceed">
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{ file }">
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-delete" @click="clearImage">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-check" class="solid-with-icon-btn"
                   @click="operateEvent('confirm')">确认
        </el-button>
        <el-button class="hollow-with-icon-btn" icon="el-icon-close" @click="operateEvent('cancel')">取消</el-button>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import dayJs from 'dayjs';
import { Message } from 'element-ui';
import selectItem from '../../selectItem.vue';
import {
  queryAbilitiesAuthList, materials, cardMaterialInfo, registerCard, cardInfo, updateCard,
  querySchemeDetailById,
  get_obs_pre_sign_url,
  cardShelveEnnew
} from '@/api/planGenerateApi.js';
import { version } from 'process';

const validatePass = (rule, value, callback) => {
  console.log('失去焦点了吗', value);
  if (!value) {
    callback(new Error('请输入版本'));
  } else {
    const versionPattern = /^(?!0+\.0+\.0+$)(0[1-9]*|[1-9]\d*)\.(0[1-9]*|[1-9]\d*)\.(0[1-9]*|[1-9]\d*)$/;
    if (!versionPattern.test(value)) {
      callback(new Error('版本号格式不正确'));
    } else {
      callback();
    }
  }
};
export default {
  name: 'card',
  components: { selectItem },
  props: {
    scheme_id: {
      type: Number,
      default: ''
    },
    abilityInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    arrayItems: {
      type: Array,
      default() {
        return [];
      }
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      abilityIcon: '',
      toMessage: { content: '', image_key: '', image_path: '' },
      fileList: [],
      uploadUrl: '#',
      uploadParam: {},
      upLoadForm: {
        name: '',
        version: '',
        description: '',
        abilityIcon: ''
      },
      dialogUpload: false,
      nodeItem: {},
      enpmPkgName: '',
      dialogHeard: '新增卡片',
      type: '',
      tableSearch: {
        page: 1,
        page_size: 12,
        ability_id: +this.$route.query?.id || ''
      },
      descriptionObj: {},
      // descriptionObj: {
      //   id: '',
      //   shelves: [],
      //   name: '卡片名称',
      //   material_info: {
      //     id: '',
      //     name: '',
      //     version: ''
      //   },
      //   createdAt: '',
      //   updatedAt: '',
      //   isDirty: '',
      //   description: '',
      //   thumbnail: ''
      //   // thumbnail: require('@/assets/images/planGenerater/runerror.png')
      // },
      isloading: false,
      dialogVisible: false,
      dataUpdateDisable: false,
      addCardForm: {
        name: '',
        materials: '',
        releaseType: 'custome',
        accredit: true,
        authentication: '',
        description: ''
      },
      baseForm: {
        abilityIds: [],
        relate_abilities: [],
        tag_ids: [],
        sort: 0
      },
      // baseInfo: {
      //   description: ''
      // },
      rules: {
        releaseType: [{ required: true, message: '请选择发布类型', trigger: 'change' }],
        version: [{ required: true, message: '请输入版本', trigger: 'blur' }, {
          validator: validatePass,
          trigger: 'blur'
        }],
        name: [{ required: true, message: '请输入卡片名称', trigger: 'blur' }],
        materials: [{ required: true, message: '请选择组件', trigger: 'change' }],
        accredit: [{ required: true, message: '请选择是否授权', trigger: 'blur' }],
        authentication: [{ required: true, message: '请选择认证', trigger: 'change' }],
        abilityIcon: [{ required: true, message: '请上传缩略图', trigger: 'blur' }]
      },
      materialsList: [],
      authenticationList: [],
      // isEdit: false,
      tagList: [],
      allTagList: [],
      isconfirm: true,
      submitStatus: true
    };
  },
  watch: {
    // 'formmodel.action': {
    //   handler (val) {
    //     const permiss = /^([1-9]|[1-9]\d|100)$/

    //   }
    // }
  },
  created() {
  },
  mounted() {
    this.queryAbilitiesAuthFun();
    this.getCardInfo(this.abilityInfo);
  },
  filters: {
    formatterTime(val) {
      return dayJs(val).format('YYYY-MM-DD HH:mm:ss');
      // console.log('val-----------------',val)
    }
  },
  methods: {
    async getCardShelveEnnew(params) {
      try {
        const res = await cardShelveEnnew(params);
        if (res.data.code === 200 && res.status === 200) {
          this.$message({
            type: 'success',
            // message: res?.data?.result || '新增成功'
            message: '上架成功'
          });
          this.queryAbilitiesAuthFun();
          this.getCardInfo(this.abilityInfo);
          this.closeDialog();
        } else {
          // console.log('res---------res',11)
          // console.log('res---------res',eval(res.data.message))
          Message.error(red?.data?.serverErrorMsg || res?.data?.msg || res?.message);
        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
      }
    },
    async beforeUpload(file) {
      try {
        const res = await get_obs_pre_sign_url({ file_type: this.$fileUtil.getFileSuffix(file.name) });
        if (res.data.code === 200) {
          this.uploadUrl = res.data.result.obsUrl;
          this.toMessage.image_key = res.data.result.key;
          this.uploadParam = {
            key: res.data.result.key,
            accessKeyId: res.data.result.accessKeyId,
            signature: res.data.result.signature,
            policy: res.data.result.policy,
            'x-obs-acl': 'public-read',
            'content-type': 'image/jpeg'
          };
        }
      } catch (e) {
        console.log(e);
        this.$message.error('获取签名出错！');
      }
    },
    modelUploadSuccess(response, file) {
      // this.uploadStatus = file.status
      console.log('response-----------------', this.abilityIcon);
      this.abilityIcon = this.uploadUrl + '/' + this.uploadParam.key;
      this.upLoadForm.abilityIcon = this.uploadUrl + '/' + this.uploadParam.key;
      console.log('response-----------------', this.$refs.Upload);

      this.$refs.Upload.validateField('abilityIcon');
      // this.lastFormModal.iconType = 'local'
    },
    uploadExceed() {
      this.$message.warning('仅能上传一张图片');
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.toMessage.image_path = '';
          this.toMessage.image_key = '';
          // this.lastFormModal.abilityIcon = ''
          this.$refs.uploadBtn.clearFiles();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    abilityTestZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.scheme_id) }).then((res) => {
        const name = res.data.result.name;
        this.uploadUrl = res?.data?.result?.ext_info?.abilityIcon;
        // this.upLoadForm.abilityIcon = res?.data?.result?.ext_info?.abilityIcon
      });
    },
    async onTheRack() {
      this.type = 'remark';
      this.dialogUpload = true;
      // this.upLoadForm = {}
      // this.upLoadForm = JSON.parse(JSON.stringify(this.descriptionObj))
      this.abilityTestZhuGe();
    },
    async getUpdateCard(params) {

      try {
        const res = await updateCard(params);
        if (res.data.code === 200 && res.status === 200) {
          this.$message({
            type: 'success',
            // message: res?.data?.result || '新增成功'
            message: '编辑成功'
          });
          this.queryAbilitiesAuthFun();
          this.getCardInfo(this.abilityInfo);
          this.closeDialog();

        } else {
          Message.error(red?.data?.serverErrorMsg || res?.data?.msg || res?.message);
        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
      }
    },
    async getCardInfo(params) {
      try {
        this.isloading = true;
        const res = await cardInfo(params);
        if (res.data.code === 200 && res.status === 200) {
          this.descriptionObj = res?.data?.result || {};
          this.isloading = false;
        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
        this.isloading = false;
      }

    },
    async getRegisterCardFun(params) {
      console.log('新增---------');
      try {
        const res = await registerCard(params);
        if (res.data.code === 200 && res.status === 200) {
          this.$message({
            type: 'success',
            // message: res?.data?.result || '新增成功'
            message: '新增成功'
          });
          this.queryAbilitiesAuthFun();
          this.getCardInfo(this.abilityInfo);
          this.closeDialog();

        } else {
          Message.error(red?.data?.serverErrorMsg || res?.data?.msg || res?.message);
        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
      }

    },
    async getCardMaterialInfo() {
      try {
        const res = await cardMaterialInfo(this.abilityInfo);
        if (res.data.code === 200 && res.status === 200) {
          this.enpmPkgName = res?.data?.result?.enpmPkgName;
          this.nodeItem = {
            id: res?.data?.result?.id,
            version: res?.data?.result?.version,
            propsSchema: {}
          };
        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
      }
    },
    async getMaterials(params) {
      try {
        const res = await materials(params);
        if (res.data.code === 200 && res.status === 200) {
          this.materialsList = res?.data?.result?.rows?.map(i => {
            return {
              ...i,
              name1: i.project?.name + '(' + i.version + ')',
              // disabled: false
              disabled: i.id === this.descriptionObj?.material_info?.id ? true : false
            };
          });
          // this.materialsList = res?.data?.result?.rows
          console.log('res----77', this.materialsList);

        }
      } catch (error) {
        Message.error(error?.data?.serverErrorMsg || error?.data?.msg || error);
      }
      //  console.log('res----qqqq',res)
    },
    async queryAbilitiesAuthFun() {
      // this.tableLoading = true
      await queryAbilitiesAuthList(this.tableSearch).then(res => {
        console.log('res---------', res);
        // this.tableLoading = false
        if (res?.data?.code === 200 && res.status === 200) {
          this.authenticationList = res?.data?.result?.data;
          console.log('res.data.result', res?.data?.result);

        } else {
          // this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
        // this.tableLoading = false
      });
    },
    dataCompare() {
      if (this.descriptionObj?.ability_auth_conf?.app_key == this.addCardForm.authentication?.app_key && this.descriptionObj?.material_info?.id == this.addCardForm?.materials) {
        this.dataUpdateDisable = true;
      } else {
        this.dataUpdateDisable = false;
      }
    },
    closeDialog() {
      this.dialogVisible = false;
      this.dialogUpload = false;
    },
    resetDialog() {
      this.$refs.editPwd.resetFields();
      this.closeDialog();
    },
    operateEvent(param) {
      if (param === 'confirm') {
        if (this.type === 'set' || this.type === 'put') {
          this.$refs.addCard.validate((valid) => {
            console.log('valid', valid);
            if (valid) {
              if (this.type === 'set') {
                console.log('新增---------');
                const params = {
                  ...this.abilityInfo,
                  name: this.addCardForm.name,
                  description: this.addCardForm.description,
                  material_id: this.addCardForm.materials,
                  ability_auth_mode: 'openapi',
                  version: this.addCardForm.version,
                  releaseType: 'custome',
                  scheme_id: this.scheme_id,
                  ability_auth_conf: {
                    'app_key': this.addCardForm.authentication.app_key,
                    'app_secret': this.addCardForm.authentication.app_secret
                  }
                };
                this.getRegisterCardFun(params);
              } else if (this.type === 'put') {
                const node = this.materialsList.find(item => item.id === this.addCardForm.materials);
                if (this.addCardForm?.releaseType === 'custome') {
                  const version = this.descriptionObj?.version;
                  if (version === this.addCardForm?.version) {
                    this.$message.warning('版本号相同！');
                    return
                  } else {
                    const index = this.compareVersions(this.addCardForm?.version, version);
                    if (index < 0) {
                      this.$message.warning(`版本号${this.addCardForm?.version}必须大于当前版本号${version}`);
                      return
                    } else if (index === 0) {
                      this.$message.warning('版本号相同！');
                      return
                    }
                  }
                }
                const params = {
                  ...this.abilityInfo,
                  ioc_card_code: this.addCardForm?.id,
                  name: this.addCardForm.name,
                  version: this.addCardForm.version,
                  description: this.addCardForm.description,
                  material_id: this.addCardForm.materials,
                  ability_auth_mode: 'openapi',
                  scheme_id: this.scheme_id,
                  ability_auth_conf: {
                    'app_key': this.addCardForm.authentication.app_key,
                    'app_secret': this.addCardForm.authentication.app_secret
                  },
                  releaseType: this.addCardForm?.releaseType,
                  nodes: [{
                    id: node?.name,
                    version: node.version,
                    propsSchema: {
                      'app_key': this.addCardForm.authentication.app_key,
                      'app_secret': this.addCardForm.authentication.app_secret
                    }
                  }]
                };
                this.getUpdateCard(params);
              }
              this.isconfirm = false;
            }
          });

        } else {
          this.$refs.Upload.validate((valid) => {
            if (valid) {
              const params = {
                ...this.abilityInfo,
                shelve_name: this.upLoadForm.name,
                shelve_description: this.upLoadForm.description,
                shelve_version: this.upLoadForm.version,
                shelve_thumbnail: this.abilityIcon
              };
              this.getCardShelveEnnew(params);

              this.isconfirm = false;

            }
          });
          // }
        }
      } else { // 取消
        this.closeDialog();
      }
    },
    compareVersions(version1, version2) {
      // 将版本号字符串拆分为数组
      const v1Parts = version1.split('.').map(Number);
      const v2Parts = version2.split('.').map(Number);

      // 获取两个版本号数组的最大长度
      const maxLength = Math.max(v1Parts.length, v2Parts.length);

      // 逐个比较数组中的元素
      for (let i = 0; i < maxLength; i++) {
        // 如果当前部分不存在，则视为 0
        const v1Part = v1Parts[i] || 0;
        const v2Part = v2Parts[i] || 0;

        if (v1Part > v2Part) {
          return 1; // version1 大于 version2
        } else if (v1Part < v2Part) {
          return -1; // version1 小于 version2
        }
      }

      return 0; // 版本号相等
    },
    editHandle() {
      this.isEdit = true;
    },
    closeHandle() {
      // this.description = this.baseInfo.description;
      this.isEdit = false;
      this.upLoadForm = {
        name: '',
        materials: '',
        accredit: true,
        authentication: '',
        description: ''
      };
    },
    saveHandle() {

    },
    async openModal(type) {
      if (type === 'put') {
        this.type = 'put';
        this.dialogHeard = '编辑卡片';
        this.dataUpdateDisable = true;
        await this.getCardMaterialInfo();
        this.addCardForm = JSON.parse(JSON.stringify({
          ...this.addCardForm,
          ...this.descriptionObj
        }));
        await this.getMaterials({ enpmPkgName: this.enpmPkgName });
        if (this.descriptionObj && this.descriptionObj.material_info && this.descriptionObj.material_info.id) {
          this.addCardForm.materials = this.descriptionObj?.material_info?.id;
        }
        if (this.addCardForm && this.addCardForm.ability_auth_conf && this.addCardForm.ability_auth_conf.app_key) {
          this.addCardForm.authentication = this.authenticationList.find(item => item.app_key === this.addCardForm?.ability_auth_conf?.app_key);

        }
        this.addCardForm.accredit = true;
        this.addCardForm.releaseType = 'custome';
      } else if (type === 'set') {
        this.dataUpdateDisable = false;
        this.addCardForm = {
          name: '',
          releaseType: 'custome',
          materials: '',
          accredit: true,
          authentication: '',
          description: ''
        };
        this.type = 'set';
        this.dialogHeard = '新增';
        console.log('this.baseInfo', this.baseInfo);
        this.getMaterials({});
      }
      this.dialogVisible = true;
    },
    async changeTags(val) {
    },
    async addBizTag() {
      // if (this.tagKeyword !== ''){
      //   await addTagMarket({name: this.tagKeyword})
      //   await this.searchTags('')
      // }
    },
    async handleTagFilter(keyword) {
      // this.tagKeyword = keyword
      // await this.searchTags(keyword)
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-card {
  .dialog-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #303133;
    line-height: 24px;
  }

  .dialog-title::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 18px;
    margin-right: 8px;
    background: #409eff !important;
  }
}

:deep(.el-divider--horizontal) {
  margin: 16px 0;
}

:deep(.el-form-item__content), :deep(.el-form-item__label) {
  line-height: 32px;
}

.el-descriptions {
  padding-left: 20px;

  :deep(.el-descriptions-item__container) {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #323233;
    line-height: 22px;
  }
}

.card {
  .box-card {
    text-align: right;
  }

  .tab-add {
    margin: 0 20px;
  }

  .preview {
    img {
      object-fit: cover;
    }
  }
}

.editCard {
  padding-right: 20px;

  .heard {
    text-align: right;
  }
}
</style>
