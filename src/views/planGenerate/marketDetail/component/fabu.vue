<template>
  <div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
      @row-click="handleReload"
    >
      <el-table-column type="index" label="序号" width="70" fixed="left" />
      <el-table-column prop="version" label="版本号" width="100"/>
      <el-table-column prop="create_time" width="170" label="创建时间" />
      <el-table-column prop="update_time" width="170" label="发布时间" />
      <el-table-column
        prop="ai_service_name"
        label="实例名称"
      >
        <template slot-scope="{ row }">
          {{row.ai_service_name}}({{row.ai_service_id}})
        </template>
      </el-table-column>
      <el-table-column prop="create_by_name" label="发布人" />
    </el-table>
    <!-- 分页部分 -->
    <div style="text-align: right; padding-top: 16px">
        <el-pagination
          class="new-paper"
          layout="prev, pager, next, sizes, jumper"
          :page-sizes="[10, 20, 30, 50, 60]"
          :current-page.sync="formData.page"
          :page-size="formData.pageSize"
          :total="formData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
  </div>
</template>
<script type="text/javascript">
import Status from '@/components/Status/index.vue'
import { queryAbilityFabuVersion,onLineStatus,offlineStatus} from '@/api/planGenerateApi.js'

export default {
  name: 'ExampleCom',
  components: { Status },
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      exampleStatusTypeMap : {
        'offline': { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线'},
        'online': { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线'},
      },
      formData: {
        page: 0,
        pageSize: 10,
        total: 0
      },
    }
  },

  created() {
  },
  mounted() {
    this.queryAbilityVersionList()
  },
  methods: {
    handleReload(row, column) {
      if (!column || ((column.type === "default" && column.property) || column.type === "index")) {
        this.$router.push({
          path: '/abilityCenter/targetList/versionDetail',
          query: {
            ...this.$route.query,
            versionId:row.id,
            scheme_id: row.scheme_detail_id,
            status: row.status,
            code_id:row.code_id,
            create_date: row.create_time,
            ai_service_name: row.ai_service_name,
            update_time: row.update_time,
            version: row.version,
            user_name: row.create_by_name,
            decision_tree_id: row.decision_tree_id,
          }
        });
      }
    },
    handleStatus (v) {
      this.$confirm(
        `此操作将${v.status === 'offline' ? '上线' : '下线'}该版本, 是否继续?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          if (v.status === 'offline') {
            onLineStatus({
              version_id: v.id
            }).then((res) => {
              if(res?.data?.code === 200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败!'
                })
              }
            })
          } else {
            offlineStatus({
              version_id:v.id
            }).then((res) => {
              if(res?.data?.code ===200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败!'
                })
              }
            })
          }

        })
        .catch(() => {
          
        })
    },
    handleSizeChange(val) {
      this.formData.page = 1;
      this.formData.pageSize = val;
      this.queryAbilityVersionList()
    },
    handleCurrentChange(val) {
      this.formData.page = val;
      this.queryAbilityVersionList()
    },
    // 列表
    async queryAbilityVersionList() {
      this.tableLoading = true
      await queryAbilityFabuVersion({
        ability_id: +this.$route.query?.id || '',
        page: this.formData.page,
        page_size: this.formData.pageSize
      }).then(res => {
        this.tableLoading = false
        if (res?.data?.code === 200) {
          this.tableData = res?.data?.result?.data || []
          this.formData.total = res.data.result.total || 0;
        } else {
          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
:deep(.el-table__body-wrapper) .el-table__cell {
  cursor: pointer;
}
</style>
