<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="注册配置"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="60%"
      :destroy-on-close="true"
    >
      <div>
        <div class="headerStep">
          <el-steps :active="activeStep" finish-status="success" simple class="myStep">
            <el-step title="选择注册目标">
              <img
                slot="icon"
                :src="
                  activeStep !== 0
                    ? require('@/assets/images/icon/1_icon.png')
                    : require('@/assets/images/icon/1_icon_success.png')
                "
                class="empty-space"
              />
            </el-step>
            <el-step title="填充注册信息">
              <img
                slot="icon"
                :src="
                  activeStep !== 1
                    ? require('@/assets/images/icon/2_icon.png')
                    : require('@/assets/images/icon/2_icon_success.png')
                "
                class="empty-space"
              />
            </el-step>
          </el-steps>
        </div>
        <div v-if="activeStep * 1 === 0" class="containerBox">
          <el-form
            ref="form"
            label-position="right"
            label-width="90px"
            :model="formData"
            :rules="rules"
          >
            <el-form-item label="目标平台:" prop="target">
              <el-select v-model="formData.target" style="width: 100%" placeholder="请选择">
                <el-option
                  :disabled="registerList?.includes(item.value)"
                  v-for="item in targetPlatformOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="activeStep * 1 === 1 && formData.target === 'agent-tool'" class="containerBox">
          <el-form
            ref="formConfig"
            label-position="right"
            label-width="130px"
            :model="formConfig"
            :rules="rulesConfig"
          >
            <el-form-item label="可见性:" prop="visibility">
              <el-radio-group v-model="formConfig.visibility">
                <el-radio v-for="item in visibilityOptions" :key="item.id" :label="item.id">{{
                  item.name
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="请求方式:" prop="request_method">
              <el-input v-model="formConfig.request_method" :disabled="disableVal" />
            </el-form-item>
            <el-form-item label="接口地址:" prop="request_url">
              <el-input v-model="formConfig.request_url" :disabled="disableVal" />
            </el-form-item>
            <el-form-item label="query参数:" prop="query_params">
              <el-input
                v-if="!formConfig.query_params"
                v-model="formConfig.query_params"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 40 }"
                :disabled="disableVal"
              />
              <pre v-else>{{ JSON.parse(formConfig.query_params) }}</pre>
            </el-form-item>
            <el-form-item label="body参数:" prop="body_params">
              <el-input v-model="formConfig.body_params" :disabled="disableVal" />
            </el-form-item>
            <el-form-item label="请求头:" prop="request_headers">
              <el-input v-model="formConfig.request_headers" :disabled="disableVal" />
            </el-form-item>
            <el-form-item label="返回信息解析规则:" prop="response_parse_rule">
              <el-input v-model="formConfig.response_parse_rule" :disabled="disableVal" />
            </el-form-item>
          </el-form>
        </div>
        <div v-if="activeStep * 1 === 1 && formData.target === 'agent-module'" class="containerBox">
          <el-form
            ref="moduleFormConfig"
            label-position="right"
            label-width="180px"
            :model="moduleFormConfig"
            :rules="moduleRulesConfig"
          >
            <el-form-item prop="code_name" label="名称：" required>
              <el-input v-model="moduleFormConfig.code_name" maxlength="50" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item prop="code_func_name" label="函数名：">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="top"
                  content="用户生成代码，只能包含小写字母、下划线、数字。"
                >
<!--                  <el-icon><Warning /></el-icon>-->
                </el-tooltip>
                函数名：
              </template>
              <el-input
                v-model="moduleFormConfig.code_func_name"
                maxlength="50"
                placeholder="请输入函数名"
              />
            </el-form-item>
            <el-form-item prop="tags" label="标签：">
              <el-select
                v-model="moduleFormConfig.tags"
                placeholder="请选择"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="4"
                class="prompt-tags"
                :filter-method="handleTagFilter"
                @keyup.enter.native="handleTagAdd"
              >
                <el-option
                  v-for="item in tagOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="code_type" label="接口来源：">
              <el-select v-model="moduleFormConfig.code_type" disabled filterable placeholder="请选择">
                <el-option
                  v-for="item in apiTypeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  :disabled="!item.enable"
                  v-show="item.name!='函数组件'"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="public_description" label="描述：">
              <el-input
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 5 }"
                v-model="moduleFormConfig.public_description"
                maxlength="500"
                placeholder="请输入描述"
              />
            </el-form-item>
          </el-form>
        </div>
        <div v-if="activeStep * 1 === 1 && formData.target === 'ennew-aip'" class="containerBox">
          <el-form
            ref="capacityForm"
            label-position="right"
            label-width="130px"
            :model="capacityForm"
            :rules="capacityRules"
          >
            <el-form-item label="能力基本信息:" class="baseInfo"> </el-form-item>
            <el-form-item label="能力名称:" prop="name">
              <el-input v-model="capacityForm.name" :disabled="disableVal" />
            </el-form-item>
            <el-form-item label="能力描述:" prop="description">
              <el-input
                v-model="capacityForm.description"
                type="textarea"
                maxlength="300"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 40 }"
              />
            </el-form-item>
            <el-form-item label="商品配置信息:" class="baseInfo"> </el-form-item>
            <el-form-item label="定价:" class="price_item" prop="price">
              <div class="price_item_content">
                <el-input v-model="capacityForm.price" placeholder="请输入价格" type="number" />
                <span class="unit">元/次</span>
              </div>
              <el-tooltip class="item" effect="dark" content="计费方式：按量后计费" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="activeStep * 1 === 1" type="primary" @click="handleGoBack"
          >上一步</el-button
        >
        <el-button
          v-if="activeStep * 1 === 0"
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="createSubmit"
          >下一步</el-button
        >
        <el-button
          v-if="activeStep * 1 === 1"
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="createSave"
          >注册</el-button
        >
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAbilityEngineServiceList,
  queryAbilityType,
  getCreateInfoById,
  createCognitiveAbility, agentGetTagList, agentAddTag, queryDuiqiApiName, getWsID
} from '@/api/planGenerateApi.js';

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    abilityId: Number,
    baseInfo: {
      type: Object
    },
    registerList: {
      type: Array
    }
  },
  data() {
    return {
      targetPlatformOptions: [
        { label: '原子能力设计（专家生产）', value: 'agent-tool' },
        { label: '能力组件（专家生产）', value: 'agent-module' },
        { label: '能力空间', value: 'ennew-aip' }
      ],
      showFlag: false,
      disableVal: true,
      activeStep: 0,
      toolModelOptions: [{ id: 1, tool_name: 'aaa' }],
      abliityTypeOptions: [],
      visibilityOptions: [
        { id: 0, name: '私有' },
        { id: 1, name: '公开' }
      ],
      formData: {
        target: ''
      },
      rules: {
        target: [{ required: true, message: '请选择目标平台', trigger: 'blur' }]
      },
      formConfig: {
        visibility: 0,
        tool_name: '',
        integration_type: '',
        tool_id: '',
        request_method: '',
        request_url: '',
        query_params: '',
        body_params: '',
        request_headers: '',
        response_parse_rule: '',
        variableMap: []
      },
      searchVal: '',
      tagAddIds: [],
      tagOptions: [],
      apiTypeOptions: [],
      moduleFormConfig: {
        code_name: '',
        code_func_name: '',
        code_type: 'gpts_ability',
        public_description: '',
        tags: [],
        tag_ids: []
      },
      rulesConfig: {
        tool_name: [{ required: true, message: '请填写任务名称', trigger: 'blur' }],
        integration_type: [{ required: true, message: '请选择工具类型', trigger: 'blur' }],
        tool_id: [{ required: true, message: '请选择工具', trigger: 'blur' }]
      },
      moduleRulesConfig: {
        code_name: [{ required: true, message: '请输入名称' }, {max: 50, message: '最长50个字符', trigger: 'blur' }],
        public_description: [{ required: true, message: '请输入描述' }, {max: 100, message: '最长100个字符', trigger: 'blur' }],
        code_func_name: [{ required: true, validator: this.funcNameValidator }, {max: 50, message: '最长50个字符', trigger: 'blur' }],
        code_type: [{ required: true, message: '请选择接口来源' }],
        tags: [{ required: true, message: '请选择标签' }]
      },
      capacityForm: {
        name: '',
        description: '',
        price: ''
      },
      capacityRules: {
        name: [{ required: true, message: '请输入智能能力名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入智能能力描述', trigger: 'blur' }],
        // industrySector: [{ required: true, message: '请选择行业领域', trigger: 'blur' }],
        // service: [{ required: true, message: '请选择服务方式', trigger: 'blur' }],
        price: [{ required: true, message: '请输入定价', trigger: 'blur' }]
      },
      shiliList: [],
      variableMapList: [],
      variableValueList: [],
      selectedList: [],
      tool: {},
      loading: false
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val;
          this.getAbilityType();
        } else {
          this.showFlag = false;
          this.activeStep = 0;
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getApiTypeOptionsFn();
    this.getTagListFn();
  },
  methods: {
    funcNameValidator(_, value, callback) {
      const reg = /^[a-z0-9_]*$/;
      if (!value) {
        callback(new Error('请输入函数名'));
        return;
      }
      if (!reg.test(value)) {
        callback(new Error('只能包含小写字母、下划线、数字'));
        return;
      }
      callback();
    },
    async handleTagAdd() {
      if (!this.searchVal.trim()) return;
      agentAddTag({
        biz_type: 'code_module',
        name: this.searchVal,
      }).then(async (res) => {
        this.tagAddIds.push(res.data);
        await this.getTagListFn(this.searchVal.trim());
      })
    },
    handleTagFilter(value) {
      this.searchVal = value;
      this.getTagListFn(value);
    },
    async getTagListFn(value) {
      await agentGetTagList({
        biz_type: 'code_module',
        keyword: value || '',
      }).then((res) => {
        this.tagOptions = res.data || [];
      })
    },
    async getApiTypeOptionsFn() {
      await queryDuiqiApiName().then((res) => {
        if (res.data) {
          this.apiTypeOptions = eval(res.data);
        }
      });
    },
    getAbilityType() {
      queryAbilityType({})
        .then((res) => {
          if (res.status === 200 || res.data?.status === 200) {
            this.abliityTypeOptions = res.data;
          }
        })
        .finally(() => {});
    },
    queryDataInfo() {
      this.loading = true;
      getAbilityEngineServiceList()
        .then((res) => {
          this.loading = false;
          if (res.status === 200 && res.data?.status === 200) {
            this.shiliList = res.data.data;
            this.formData.chooseData = Number(this.bushuId) || '';
          } else {
            this.shiliList = [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 通过能力仓库的id获取创建信息
    handleGetCreateInfoById() {
      const params = {
        gpts_ability_id: window.CUSTOM_ABILITY_ID
      };
      getCreateInfoById(params)
        .then((res) => {
          if (res.status === 200 || res.data?.status === 200) {
            this.formConfig = { ...this.formConfig, ...res.data };
            this.tool = res.data;
            delete this.tool.variables;
          }
        })
        .catch((error) => {
          this.$message.error(
            JSON.parse(JSON.stringify(error)).status === 409 ? '未找到对应的能力配置' : error
          );
        });
    },
    // 注册认知能力
    handleCreateCognitiveAbility() {
      const params = {
        gpts_ability_id: window.CUSTOM_ABILITY_ID,
        target_platform: this.formData.target
      };
      if (this.formData.target === 'ennew-aip') {
        params.ennewRegistrationInfo = {
          ability_name: this.capacityForm.name,
          description: this.capacityForm.description,
          unitPrice: this.capacityForm.price || 0
        }
      } else if (this.formData.target === 'agent-tool') {
        params.tool = this.tool
        params.visibility = this.formConfig.visibility
      } else if (this.formData.target === 'agent-module') {
        params.code_module = this.moduleFormConfig
        params.code_module.tag_ids = this.moduleFormConfig.tags
      }
      createCognitiveAbility(params)
        .then((res) => {
          if (res.status === 200) {
            if (this.formData.target === 'agent-module') {
              this.$message.success('创建成功，跳转到能力组件详情生成代码！');
              this.$router.push({
                path: '/planGenerate/apiDocumentDetails',
                query: {
                  id: res.data,
                  workspaceId: getWsID(),
                  is_public: 0,
                  currentPage: 1
                }
              });
            } else {
              this.$message.success('创建成功！');
              this.onClose('refresh');
            }
          }
        })
        .catch((error) => {
          console.error('请求失败:', error); // 打印错误信息
          if (error.response && error.response.data) {
            const errorMsg = error.response.data.detail || '服务端异常，请联系管理员排查';
            this.$message.error(errorMsg);
          } else {
            this.$message.error('请求失败，请稍后重试');
          }
        });
    },
    onClose(val) {
      this.formData.chooseData = '';
      this.loading = false;
      this.showFlag = false;
      this.$emit('close', val);
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          this.activeStep = 1;
          if (this.formData.target === 'ennew-aip') {
            // 带入详情页面的描述
            this.capacityForm.description = this.baseInfo.description || '';
            this.capacityForm.name = this.baseInfo.ability_name || '';
          } else if (this.formData.target === 'agent-module') {
            // 带入详情页面的描述
            this.moduleFormConfig.public_description = this.baseInfo.description || '';
            this.moduleFormConfig.code_name = this.baseInfo.ability_name || '';
            if (this.moduleFormConfig.public_description === '') {
              this.moduleFormConfig.public_description = `创建来自能力集市【${this.moduleFormConfig.code_name}】`;
            }
          }else {
            this.handleGetCreateInfoById();
          }
        }
      });
    },
    handleGoBack() {
      this.activeStep = 0;
    },
    createSave() {
      // 测试路由使用
      // this.$router.push({
      //   path: '/planGenerate/apiDocumentDetails',
      //   query: {
      //     id: 'b0c9789a-7f91-4913-bdf7-dbe27308851a',
      //     workspaceId: getWsID(),
      //     is_public: 0,
      //     currentPage: 1
      //   }
      // });

      if (this.formData.target === 'ennew-aip') {
        this.$refs.capacityForm.validate((validate) => {
          if (validate) {
            this.handleCreateCognitiveAbility();
          }
        });
      } else if (this.formData.target === 'agent-module') {
        this.$refs.moduleFormConfig.validate((validate) => {
          if (validate) {
            this.handleCreateCognitiveAbility();
          }
        });
      } else {
        this.handleCreateCognitiveAbility();
      }
    }
    // 保存恩牛场景能力
  }
};
</script>
<style lang="scss" scoped>
.headerStep {
  width: 100%;
  border-bottom: 1px solid #ebecf0;
  .myStep {
    background: #fff;
    width: 400px;
    border-radius: 0;
    padding: 5px 0 15px 0;
    :deep(.el-step__arrow) {
      margin: 0 16px;
      &::before {
        content: '';
        position: static;
        height: 1px;
        width: 100%;
        background: #c8c9cc;
        transform: none;
        display: block;
      }
      &::after {
        display: none;
      }
    }
    :deep(.is-process) {
      color: #4068d4;
      .el-step__icon {
        color: #4068d4;
        &.is-text {
          border: none;
        }
      }
    }
    :deep(.is-success) {
      color: #000;
      border-color: #4068d4;
      .el-icon-check {
        color: #4068d4;
      }
      .el-step__icon {
        color: #4068d4;
        &.is-text {
          border: 1px solid #4068d4;
        }
      }
    }
    .empty-space {
      width: 100%;
      height: 100%;
    }
  }
}
.containerBox {
  margin-top: 20px;
}
:deep(.el-select-dropdown__item) {
  max-width: 700px;
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;
  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;
  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;
}
:deep(.el-form-item) {
  margin-bottom: 8px;
}

.option-custom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;

  .left {
    display: flex;
    flex-direction: column;
    .top {
      font-weight: 500;
      margin-bottom: 2px;
      color: #323233;
      font-size: 12px;
    }
    .bottom {
      color: #646566;
      font-size: 12px;
    }
  }
  .right {
    span {
      padding: 4px;
      background: #f2f3f5;
      color: #4b7aff;
      font-size: 12px;
    }
  }
}
pre {
  border: 1px solid #e4e7ed;
  color: #c0c4cc;
  background-color: #f5f7fa;
}
:deep(.baseInfo) {
  .el-form-item__label {
    color: #333333;
    font-weight: 650;
  }
}
:deep(.price_item) {
  .el-form-item__content {
    display: flex;
    align-items: center;
    .price_item_content {
      width: 100%;
      position: relative;
      .unit {
        position: absolute;
        right: 10px;
        bottom: -4px;
        color: #c0c4cc;
      }
    }
  }
}
:deep(
    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button
  ) {
  -webkit-appearance: none;
  margin: 0;
}
</style>
<style lang="scss">
:deep(.el-select) {
  width: 100%;
}
.prompt-tags {
  :deep(.el-tag.el-tag--info) {
    --el-tag-hover-color: #4068d4;
    --el-tag-text-color: #4068d4;
    background-color: #e6ecff;
    border: 0;
  }
}
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 10px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
.custom-select-dropdown {
  height: auto !important;
  line-height: normal;
}
</style>
