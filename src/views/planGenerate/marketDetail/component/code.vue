<template>
  <div style="height: 100%">
    <MonacoEditor
      class="editor"
      :value="detailContent"
      language="python"
      :scrollBeyondLastLine="true"
      theme="vs-dark"
      :diff-editor="false"
      :options="options"
    />
    <!-- <vue-markdown v-highlight :source="detailContent" class="markdown-body"></vue-markdown> -->
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue'
import MonacoEditor from '@/components/MonacoEditor'
import { queryComponentById } from '@/api/planGenerateApi.js'
import mermaid from 'mermaid'
mermaid.initialize({startOnLoad: true,
  theme: 'default',
  flowchart: {
      htmlLabels: true,
      useMaxWidth: true
  },
  zoom: true,
});
export default {
  name: 'ExampleCom',
  components: {MonacoEditor},
  props: {
    scheme_id:{
      type: String,
      default: '',
    }
  },
  watch: {
    scheme_id: {
      handler(val) {
        if (val) {
          this.handleDetailCode(val);
        } 
      },
      immediate: true
    },
  },
  data() {
    return {
      detailContent: '',
      loading: false,
      options: {
          readOnly: true,
          lineNumbers: true,
          fontSize: 15,
          mouseStyle: 'default',
          colorDecorators: true,
          foldingStrategy: 'indentation', // 代码可分小段折叠
          automaticLayout: true, // 自适应布局
          overviewRulerBorder: false, // 不要滚动条的边框
          autoClosingBrackets: true,
          renderLineHighlight: 'all',
          wordWrap: 'on',
          scrollBeyondLastLine: true,
          tabSize: 4, // tab 缩进长度
          minimap: {
            enabled: true // 不要小地图
          },
          fontFamily:
            'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
          folding: true
        },
    };
  },
  watch: {
    scheme_id: {
      handler(val) {
        if (val) {
          console.log(val,'11111')
          this.handleDetailCode(val);
        }
      },
      immediate: true
    },
  },
  created() {
  },
  methods: {
    handleDetailCode(id){
      this.loading = true;
      queryComponentById({component_id: id, type: 'decision_ability'})
        .then((res) => {
          this.loading = false;
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.detailContent = res.data.result?.decision_making_content || '';
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .catch((_err) => {
          this.loading = false;
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
  }
}
</script>
<style lang="less" scoped>
.editor {
  width: 100%;
  height: 100%;
}
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
</style>
