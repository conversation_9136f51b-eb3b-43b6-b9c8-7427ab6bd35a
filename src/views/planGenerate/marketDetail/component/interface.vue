<template>
  <div>
    <div class="search">
      <el-descriptions title="" :column="1">
        <el-descriptions-item label="请求地址" :label-style="{'width': '80px'}">{{ baseInfo?.req_url }}</el-descriptions-item>
        <el-descriptions-item label="请求类型" :label-style="{'width': '80px'}">{{ baseInfo?.req_type }}</el-descriptions-item>
        <el-descriptions-item label="请求参数" :label-style="{'width': '80px'}">
          <el-table
            :data="tableReqData"
            style="width: 100%"
            row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
          >
            <el-table-column prop="param_name" label="参数名称" width="200"/>
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是':'否' }}
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型" />

          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="请求头" :label-style="{'width': '80px'}">
          <el-table
            :data="tableHeaderData"
            style="width: 100%"
            row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
          >
            <el-table-column prop="param_name" label="参数名称" width="200"/>
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是':'否' }}
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型"/>
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="响应参数" :label-style="{'width': '80px'}">
          <el-table
            :data="tableRespData"
            style="width: 100%"
            row-key="id"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
          >
            <el-table-column prop="param_name" label="参数名称" width="200"/>
            <el-table-column prop="param_desc" label="参数说明" />
            <el-table-column prop="mandatory" label="是否必传">
              <template slot-scope="scope">
                {{ scope.row.mandatory === true ? '是':'否' }}
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型"/>
          </el-table>
        </el-descriptions-item>
      </el-descriptions>
    </div>


  </div>
</template>
<script type="text/javascript">
import { queryAbilityApi } from '@/api/planGenerateApi'

export default {
  name: 'InterfaceCom',
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      baseInfo: {},
      tableLoading: false,
      tableReqData: [],
      tableRespData: [],
      tableHeaderData: [],
    }
  },
  watch:{
    versionId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getDeviceBindList()
        }
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    handlSearch() {

    },
    // 绑定信息
    async getDeviceBindList() {
      this.tableLoading = true
      await queryAbilityApi({
        ability_id: +this.$route.query?.id || '',
        // version_id: this.versionId,
      }).then(res => {
        this.tableLoading = false
        if (res?.data?.code === 200) {
          const data = res?.data?.result
          this.baseInfo = {
            req_url:data.req_url,
            req_type:data.req_type,
          }
          this.tableReqData =data?.req_body ||[]
          this.tableRespData =data?.resp_body||[]
          this.tableHeaderData =data?.req_header ||[]
        } else {
          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  text-align: right;
  margin-bottom: 16px;

  :deep(.search-input) {
    .el-input__inner {
      height: 30px;
      line-height: 30px;
      border-color: #c8c9cc !important;
      border-right: none;
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
    }
    .el-input-group__append {
      border-color: #c8c9cc !important;
      background-color: transparent;
      padding: 0px 12px;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
  }
}

.el-link {
  margin-right: 16px;
  &:last-child {
    margin-right: 0;
  }
}


:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebecf0;
}

:deep {
  .hide-expand .el-table__expand-column .el-icon {
    visibility: hidden;
  }
}
</style>
