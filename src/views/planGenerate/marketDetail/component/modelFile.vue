<template>
 <div class="file-main">
  <el-card class="box-card" shadow="nerver">
   <div slot="header" class="slot-header">
    <div class="p-file-name">
     <span v-for="(pathName, index) in selectedPaths" :key="index">
      <span :class="index < selectedPaths?.length - 1 && 'a-class'" @click="clickByLevel(index, pathName,'header')">
       {{ pathName }}
       <span v-if="index < selectedPaths?.length - 1">/</span>
      </span>
     </span>
    </div>
    <el-button type="text" size="small" @click="handleDown"> 下载 </el-button>
   </div>
   <template v-if="isShowFileList">
    <div v-for="item in modelFiles" :key="item.objectKey" class="div-item">
     <el-checkbox v-if="!item.isDir" v-model="item.checked">
      <div @click="clickByObject(item)">
       <img v-if="item.isDir" src="@/assets/images/fileList.png" />
       <img v-else src="@/assets/images/file1.png" />
       <span> {{ item.objectName }}</span>
      </div>
     </el-checkbox>
     <div v-else @click="clickByObject(item)">
      <img v-if="item.isDir" src="@/assets/images/fileList.png" />
      <img v-else src="@/assets/images/file1.png" />
      <span> {{ item.objectName }}</span>
     </div>
    </div>
   </template>
   <div v-if="!isShowFileList" v-loading="codeLoading" class="div-code" style="min-height: 200px">
    <highlightjs v-if="codeStrList.length && !isShowFileList" autodetect :code="codeStr" />
    <img v-if="isImage && imageUrl" :src="imageUrl" style="width: 80%; height: 80%" />
    <span v-if="!codeStrList.length && !isShowFileList && !codeLoading && !isImage" style="color: #999">
     该文件不可预览
    </span>
   </div>
  </el-card>
 </div>
</template>
<script>
import { queryListFiles, queryObjectKey } from '@/api/planGenerateApi';
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import _ from 'lodash'
// 可预览格式
const overviewTypes = ['json', 'txt', 'md', 'py', 'sh', 'ipynb', 'png', 'jpg', 'jpeg', 'csv', 'yaml']
const imageTypes = ['png', 'jpg', 'jpeg']
export default {
 props: {
  scheme_id: {
   type: [String, Number]
  }
 },
 data() {
  return {
   isImage: false,
   imageUrl: '',
   textMarkDown: '',
   selectedPaths: [],
   modelFiles: [],
   codeStr: '',
   isShowFileList: true,
   codeLoading: true,
  }
 },
 computed: {
  codeStrList() {
   if (this.codeStr && this.codeStr.length) {
    return this.codeStr.split('\n');
   } else {
    return [];
   }
  }
 },
 mounted() {
  this.getCapabilityType('', 'first')
  // this.getCapabilityType()
  // this.getOssUrl('uploader/model/chinese_alpaca_plus_combine_7b/README.md', true)
 },
 methods: {
  async download(urls) {
   const zip = new JSZip();
   try {
    await this.zipFiles(zip, urls);
    zip.generateAsync({ type: 'blob' }).then(function (content) {
     FileSaver.saveAs(content, '文件下载.zip');
    });
   } catch (e) {
    console.log(e);
   }
  },
  handleDown() {
   const selected = this.modelFiles.filter((item) => item.checked);
   if (!selected?.length) {
    this.$message.warning(`请选择文件`);
    return;
   }
   if (!selected?.length > 12) {
    this.$message.warning(`已选文件不能超过20条`);
    return;
   }
   Promise.all(selected.map((item) => this.generateSignByObjectKey(item.objectKey)))
    .then((res) => {
     const data = res;
     this.download(data);
    })
    .catch((err) => {
     console.log(err);
    });
  },
  // 当前url获取blob 对象
  async getBlob(url) {
   return new Promise((resolve) => {
    fetch(url)
     .then((response) => {
      return response.blob();
     })
     .then((res) => {
      const blob = new Blob([res]);
      resolve(blob);
     });
   });
  },
  async zipFiles(zip, urls) {
   return new Promise((resolve, reject) => {
    let curIdx = 0;
    urls.forEach((item, index) => {
     const endNum = item.indexOf('?');
     const startNum = item.lastIndexOf('/') + 1;
     const name = item.substring(startNum, endNum);
     this.getBlob(item)
      .then((res) => {
       zip.file(name, res);
      })
      .finally(() => {
       curIdx++;
       if (curIdx === urls.length) resolve();
      });
    });
   });
  },
  async generateSignByObjectKey(urlKey) {
   try {
    const res = await queryObjectKey({ 'objectKey': urlKey });
    // 明确返回 res.data.result，假设接口响应结构为 { data: { result: ... } }
    return res?.data?.result;
   } catch (err) {
    // 错误处理（可选：抛出错误或返回默认值）
    console.error(`请求 ${urlKey} 失败:`, err);
    throw err; // 保持 Promise 链的 reject 状态
    // 或返回默认值（如 null），避免 Promise.all 整体失败
    // return null;
   }
  },
  async getObjectKey(urlKey, overview = null) {
   try {
    if (!urlKey) return
    const res = await queryObjectKey({
     'objectKey': urlKey,
    })
    if (res.status === 200 || res.data?.status === 200) {
     if (this.isImage) {
      this.imageUrl = res?.data.result;
      this.codeLoading = false;
      return;
     }
     if (overview !== null) {
      this.FetchContent(res.data?.result, overview)
     }
    }
   } catch (error) {

   }
  },
  async getCapabilityType(objectKey, type = null) {
   try {
    const res = await queryListFiles({
     "prefix": objectKey || '',
     "scheme_id": +this.$route?.query?.scheme_id,
     'version': this.$route?.query?.version,
    })
    if (res.status === 200 || res.data?.status === 200) {
     const resData = res.data?.result[0] || []
     if (type === 'first') {
      const parts = resData?.objectKey?.split('/').filter(part => part !== '');
      parts?.pop(); // 确保移除最后一个有效分段
      this.selectedPaths.push(parts?.join('/'))
     }
     this.modelFiles = []
     this.modelFiles = res.data?.result || []
     // this.readmeKey = _.find(res.data?.result, item => item.objectName === 'README.md')?.objectKey
     await this.getObjectKey(resData?.objectKey)
    }
   } catch (error) {

   }
  },
  clickByLevel(level, object,type=null) {
   if(level === this.selectedPaths.length - 1 && type === 'header' ) return
   this.isShowFileList = true
   if (level < this.selectedPaths.length - 1) {
    const newPaths = this.selectedPaths.filter((item, index) => index <= level)
    this.selectedPaths = newPaths
   }
   let objectKey = ''
   this.selectedPaths.forEach(path => { objectKey += path + '/' })
   this.getCapabilityType(objectKey)
   // this.getFileList(objectKey)
   // this.getFileList('uploader/model/chinese_alpaca_plus_combine_7b/')
  },
  getFileList(objectName) {
   this.modelFiles = []
   this.readmeKey = 'uploader/model/chinese_alpaca_plus_combine_7b/README.md'
   // listObjects(objectName).then(res => {
   //  this.modelFiles = res.data.data
   //  this.readmeKey = _.find(res.data.data, item => item.objectName === 'README.md')?.objectKey
   // })
  },
  FetchContent(url, overview) {
   fetch(url)
    .then(response => response.text())
    .then(data => {
     // data就是文件的内容
     this.textLoading = false
     this.codeLoading = false
     if (overview) {
      this.textMarkDown = data
     } else {
      this.codeStr = data
     }
    })
    .catch(error => { console.log('error', error) });
  },
  clickByObject(object) {
   this.selectedPaths.push(object.objectName)
   if (object.isDir) { // 文件夹
    this.clickByLevel(this.selectedPaths.length - 1, object)
   } else {
    this.codeStr = ''
    this.imageUrl = '';
    this.isShowFileList = false
    if (overviewTypes.includes(object?.objectType)) {
     this.codeLoading = true
     this.isImage = imageTypes.includes(object?.objectType);
     this.getObjectKey(object?.objectKey, false)
    } else {
     this.codeLoading = false
    }
   }
  },
 }
}
</script>
<style lang="scss" scoped>
.file-main {
 height: 100%;
}

.a-class {
 cursor: pointer;

 &:hover {
  color: #0f55fa;
  text-decoration: underline;
 }
}

.div-code {
 padding: 20px;

 code {
  all: initial;
  /*清除继承样式*/
  display: block;
  /*设置布局流，避免换行导致的错误布局*/
  white-space: nowrap;
  overflow: auto;
  margin: 6px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 20px;

  pre {
   margin: 0;
   padding: 0;
   display: inline-block;

   &.span-num {
    padding: 0 20px 0 30px;
   }
  }
 }
}

.div-item {
 padding: 8px 16px;
 font-size: 14px;
 font-family: PingFangSC-Regular, PingFang SC;
 font-weight: 400;
 color: #343a40;
 line-height: 22px;
 cursor: pointer;

 &:not(:last-child) {
  border-bottom: 1px solid #e7e9ea;
 }

 img {
  width: 14px;
  height: auto;
 }

 img,
 span {
  vertical-align: middle;
 }
}

.div-item img {
 width: 14px;
 height: auto;
}

.p-file-name {
 font-size: 14px;
 font-family: PingFangSC-Regular, PingFang SC;
 font-weight: 400;
 color: #343a40;
 line-height: 20px;
}

:deep(.box-card) {
 height: 100%;
 display: flex;
 flex-direction: column;

 .slot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
 }

 .el-card__header {
  background: #f0f4ff;
  border-bottom: 1px solid #e7e9ea;
  padding: 8px 16px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #6c757d;
  line-height: 22px;
 }

 .el-select .el-input.is-focus .el-input__inner,
 .el-select .el-input__inner:focus,
 .el-input--suffix .el-input__inner {
  border-radius: 4px;
  border: 1px solid #ced4da;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #343a40;
  line-height: 22px;
 }

 .el-card__body {
  flex: 1;
  overflow-y: auto;
 }
}
</style>