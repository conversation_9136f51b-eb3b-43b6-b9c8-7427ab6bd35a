<template>
  <div>
    <el-table
      v-loading="tableLoading"
      :data="tableData.data"
      style="width: 100%"
      :key="tableKey"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
    >
      <el-table-column prop="app_name" label="接入方名称" />
      <el-table-column prop="app_description" label="接入方描述" />
      <el-table-column prop="app_id" label="appid" />
      <el-table-column prop="app_key" label="appkey" />
      <el-table-column prop="note" label="备注" />
      <el-table-column prop="approve_code" label="复制分享地址">
        <template slot-scope="{ row }"> 
          <el-tooltip class="item" effect="dark" :content="row.toolp_link" placement="top">
              <span @click="toCopy(row)" class="nowarp"> {{ link(row) }} </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop=""
        label="操作"
        fixed="right"
        width="100" 
      >
        <template slot-scope="{ row }"> 
          <el-link type="primary" :underline="false" @click="edit(row)">编辑</el-link>
          <el-dropdown trigger="click" class="el-dropdown-link" placement="bottom">
            <i style="color: #4068d4; cursor: pointer;" class="el-icon-more"></i>
            <el-dropdown-menu slot="dropdown">
              <!-- <el-dropdown-item><el-link type="primary" class="card" :underline="false" @click="approve('add',row)"  >新增卡片授权</el-link></el-dropdown-item> -->
              <el-dropdown-item><el-link type="primary" class="card" :underline="false" @click="approve('put',row)">启用授权</el-link></el-dropdown-item>
              <el-dropdown-item><el-link type="primary" class="card" :underline="false" @click="approve('stop',row)">停用授权</el-link></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData?.data?.length >0"  style="text-align: right; padding-top: 16px">
        <el-pagination
          class="new-paper"
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[12, 24, 36, 48, 60]"
          :current-page.sync="tableData.page"
          :page-size="tableData.pageSize"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <el-dialog
      lock-scroll
      custom-class="last-dialog2"
      ref="dialog"
      :visible.sync="showDialog"
      :before-close="onClose"
      :close="onClose"
      width="580px"
    >
      <div slot="title">编辑</div>
      <div>
        <el-form ref="ruleForm" label-width="120px">
        <el-form-item label="接入方名称：" prop="app_name">
          <el-input
            v-model="app_name"
            disabled
          />
        </el-form-item> 
        <el-form-item label="备注：" prop="note">
          <el-input
            placeholder="请输入备注内容"
            v-model="note"
            type="textarea"
            maxlength="30"
            clearable
          />
        </el-form-item>
      </el-form>
      </div>
      <div slot="footer">
        <el-button
            slot="reference"
            style="margin-left: 12px"
            type="primary"
            @click="submit()"
            >确定</el-button>
            <el-button
            slot="reference"
            style="margin-left: 12px"
            @click="onClose()"
            >取消</el-button>
      </div>
    </el-dialog>
    <div class="show-visible">
      <el-dialog :visible.sync="showVisible" lock-scroll custom-class="custom-dialog" width="40%" :before-close="handleClose">
        <div slot="title">{{ visibleTitle }}</div>
          <div class="dialog-cn">
                <el-input type="textarea" style="width: 100%; margin-right: 10px" readonly id="textToCopy" :rows="4" v-model="copyLink"
                  placeholder=""></el-input>
          </div>
          <div slot="footer">
        <el-button
            slot="reference"
            style="margin-left: 12px"
            type="primary"
            @click="confirm"
            >确定</el-button>
            <el-button
            slot="reference"
            style="margin-left: 12px"
            @click="cancel"
            >取消</el-button>
      </div>
        </el-dialog>
    </div>
  </div>
</template>
<script type="text/javascript">
import { v4 as uuidv4 } from 'uuid';
import { queryAbilitiesAuthList, editAbilitiesAuthRemake, setApproveCode} from '@/api/planGenerateApi.js'
export default {
  name: 'ExampleCom',
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      screeUuid: '',
      hrefPath: '',
      approveCode: '',
      parts: [],
      approveCode: '',
      screeUuid: '',
      visibleTitle: '新增卡片授权',
      tableKey: 0,
      operateConfig: {
        operateName: '操作',
        count: 1,
        width: 80
      },
      showCopy: false,
      params: {
        id: '',
        approveCode: ''
      },
      copyLink: '',
      showVisible: false,
      id:'',
      app_name:'',
      note: '',
      showDialog: false,
      tableLoading: false,
      tableSearch: {
        page:1,
        page_size:12,
        ability_id: +this.$route.query?.id || ''
      },
      tableData: {
        data: [
        ],
        page: 1,
        pageSize: 12,
        total: 0,
      }
    }
  },

  created() {
  },
  mounted() {
    this.queryAbilitiesAuthFun()
    this.hrefPath = window.location.href?.split('?');
    this.hrefPath.pop();
    if(this.hrefPath[1]) {
      this.parts = this.hrefPath[1].split('/');
      this.parts.pop();
      this.hrefPath[1] = this.parts.join('/'); 
    }else if(this.hrefPath[0]) {
      this.parts = this.hrefPath[0].split('/');
      this.parts.pop();
      this.hrefPath[0] = this.parts.join('/'); 
    }
    this.screeFull = this.hrefPath.join('?') + '/approvecode' + '?' + `workspaceId=${+this.$route.query?.workspaceId || ''}`;
  },
  computed: {
   link() {
      return this.getLink
    },
  },
  methods: {
    getLink(row) {
    if (!row.approve_code) return '';
    const copyLink = this.screeFull + `&approveCode=${row?.approve_code || ''}`;
    return copyLink;
      },
    //复制
    async toCopy(row) {
      // 获取输入框元素
      const textField = document.getElementById('textToCopy')
        // 复制文本到剪贴板
        try {
          await navigator.clipboard.writeText(this.copyLink ? this.copyLink : this.screeFull + `&approveCode=${row?.approve_code || ''}`)
          this.$message.success('链接复制成功')
        } catch (err) {
          this.$message.error(err)
        }
        textField.addEventListener('copy', function (e) {
          e.preventDefault()
        })
    },
    handleClose(done) {
      done()
    },
    approve(command,row) {
      this.params.id = row.id
      this.showVisible = true
      switch (command) {
        // case 'add':
        // this.screeUuid = uuidv4()
        //   this.visibleTitle = '新增卡片授权'
        //   this.approveCode = row?.approve_code ? row?.approve_code : this.screeUuid
        //   this.copyLink = this.screeFull + `&approveCode=${this.approveCode}`;
        //   break
        case 'put':
          this.screeUuid = uuidv4()
          this.visibleTitle = '更新卡片授权'
          this.approveCode = row?.approve_code ? row?.approve_code : this.screeUuid
          this.copyLink = this.screeFull + `&approveCode=${this.approveCode}`;
          break
        case 'stop':
          this.visibleTitle = '停用卡片授权'
          this.approveCode = row?.approve_code || ''
          if(this.approveCode) {
           this.copyLink =  this.copyLink ? this.copyLink : this.screeFull + `&approveCode=${this.approveCode}`;
          }else {
           this.copyLink = ''
          }
          break
      }
      this.params.approveCode = this.approveCode
    },
    confirm(){
      if(this.visibleTitle === '停用卡片授权') {
        this.params.approveCode = ''
      }
      setApproveCode(this.params).then(res => {
        if(res.status === 200 && res.data?.code === 200) {
          this.$message.success(`${this.visibleTitle}成功`)
          this.queryAbilitiesAuthFun()
        }
      }).catch(err => {
        this.$message.error('内部异常')
      })
      this.cancel()
    },
    cancel(){
      this.showVisible = false
    },
    onClose(){
      this.showDialog = false
    },
    async submit(){
      await editAbilitiesAuthRemake({
        note: this.note,
        id: this.id
      }).then(res => {
        this.showDialog = false
        this.queryAbilitiesAuthFun()
      })
    },
    edit(row){
      this.app_name = row.app_name
      this.id = row.id
      this.note = row.node
      this.showDialog = true
    },
    handleSizeChange(val) {
      this.tableSearch.page = 1;
      this.tableSearch.page_size = val;
      this.queryAbilitiesAuthFun();
    },
    handleCurrentChange(val) {
      this.tableSearch.page = val;
      this.queryAbilitiesAuthFun();
    },
    async queryAbilitiesAuthFun() {
      this.tableLoading = true
      await queryAbilitiesAuthList(this.tableSearch).then(res => {
        this.tableLoading = false
        if (res?.data?.code === 200) {
          this.tableData = { ...res?.data.result };
          this.tableData.data = this.tableData.data.map(item => {
            return {
              ...item,
              toolp_link: this.screeFull + `&approveCode=${item?.approve_code || ''}`
            }
          })
          this.tabelData.pageSize = this.tableSearch.page_size;
        } else {
          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.nowarp {
  color: #4068d4;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.show-visible {
:deep(.el-dialog__wrapper) {
  display: flex;
  align-items: center;
}
:deep(.el-input__inner ) {
  border-radius: 6px;
}
.custom-dialog {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* 确保对话框在视口高度内垂直居中 */
}
}
.el-link {
  cursor: pointer;
  &:first-child {
    margin-right: 16px;
  }
}
.el-link.card {
  margin-right: 16px
}
.el-link.card:last-of-type {
  margin-right: 0px
}
</style>
