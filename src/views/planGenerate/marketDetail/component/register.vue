<template>
  <div>
    <div class="info-top">
      <div class="info-title">
        <span>{{ `目标平台（${tableData?.length || 0}）` }}</span>
      </div>
      <div style="display: flex">
        <el-button
          type="primary"
          :disabled="
            !(
              registerSign &&
              (isSuperAdmin || baseInfo.user_id === userInfo.userId) &&
              baseInfo.is_online
            )
          "
          @click="handleNewAdd"
          >新增注册</el-button
        >
      </div>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
    >
      <el-table-column type="index" label="ID" width="70" fixed="left" />
      <el-table-column prop="target_platform" label="注册平台">
        <template slot-scope="scope">
          <span>{{ targetPlatforms[scope.row.target_platform] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" />
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <Status
            :text="exampleStatusTypeMap[scope.row?.status].text"
            :bg-color="exampleStatusTypeMap[scope.row?.status]?.bgColor"
            :dot-color="exampleStatusTypeMap[scope.row?.status]?.dotColor"
          />
        </template>
      </el-table-column>
      <el-table-column prop="create_user_nickname" label="创建人" />
      <el-table-column  prop="" label="操作" fixed="right" width="140">
        <template slot-scope="{ row }">
          <el-link
            v-if="row.target_platform == 'aip-agent'"
            :disabled="
              !((isSuperAdmin || baseInfo.user_id === userInfo.userId) && baseInfo.is_online)
            "
            type="primary" :underline="false" @click="handleRegister(row)">{{
            row.status === 1 ? '下线' : '上线'
          }}</el-link>
          <el-link
            v-if="row.target_platform == 'agent-tool'"
            :disabled="!(isSuperAdmin || baseInfo.user_id === userInfo.userId)"
            type="primary" :underline="false" @click="deleteRegister(row)">注销</el-link>
        </template>
      </el-table-column>
    </el-table>
    <RegisterModal
      v-if="registerVisible"
      :is-visible="registerVisible"
      @close="handleClose"
      :baseInfo="baseInfo"
      :registerList="registerList"
    />
  </div>
</template>
<script type="text/javascript">
import Status from '@/components/Status/index.vue';
import RegisterModal from './registerModal.vue';
import {
  getAbilityRegistrationList,
  deleteCognitiveAbility,
  updateCognitiveAbility
} from '@/api/planGenerateApi.js';
import { isSuperAdminForCurUser } from '@/api/user';

export default {
  name: 'ExampleCom',
  components: { Status, RegisterModal },
  props: {
    baseInfo: {
      type: Object
    }
  },
  data() {
    return {
      tableLoading: false,
      registerVisible: false,
      tableData: [],
      exampleStatusTypeMap: {
        0: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        1: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
      },
      statusMap: {
        1: '生效中'
      },
      userInfo: {},
      isSuperAdmin: false,
      targetPlatforms: {
        'agent-tool': '原子能力设计（专家生产）',
        'agent-module': '能力组件（专家生产）',
        'ennew-aip': '能力空间'
      },
      registerList: [],
      registerSign: true
    };
  },
  computed: {},
  created() {
    this.handleGetIsSuperAdmin();
  },
  mounted() {
    this.handleGetAbilityRegistrationList();
    this.userInfo = JSON.parse(window.sessionStorage.getItem('USER_INFO'));
  },
  methods: {
    // 是否是超级管理员
    handleGetIsSuperAdmin() {
      isSuperAdminForCurUser().then((res) => {
        if (res.status === 200) {
          this.isSuperAdmin = res.data.data;
        }
      });
    },
    handleNewAdd() {
      this.registerVisible = true;
      window.CUSTOM_ABILITY_ID = this.baseInfo.id;
    },
    handleClose(val) {
      console.log('新增注册---handleClose')
      if (val) {
        // 新增注册、刷新列表
        this.handleGetAbilityRegistrationList();
      }
      this.registerVisible = false;
    },
    deleteRegister(row) {
      const tips = '注销';
      this.$confirm(`此操作将${tips}该数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let { gpts_ability_id, id } = row;
          deleteCognitiveAbility({
            gpts_ability_id,
            gpts_ability_registration_id: id
          })
            .then((res) => {
              if (res?.data?.code === 200 || res.status === 200) {
                this.handleGetAbilityRegistrationList();
                this.$message({
                  type: 'success',
                  message: `${tips}成功！`
                });
              } else {
                this.$message({
                  type: 'error',
                  message: `${tips}失败：` + res?.data?.code
                });
              }
            })
            .catch((error) => {
              this.$message({
                type: 'warning',
                message: '对应工具已被引用，禁止注销！'
              });
            });
        })
        .catch(() => {});
    },
    handleRegister(row) {
      const tips = row.status === 1 ? '下线' : '上线';
      this.$confirm(`此操作将${tips}该数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let { gpts_ability_id, id } = row;
          updateCognitiveAbility({
            gpts_ability_id,
            gpts_ability_registration_id: id,
            status: row.status === 1 ? 0 : 1
          })
            .then((res) => {
              if (res?.data?.code === 200 || res.status === 200) {
                this.handleGetAbilityRegistrationList();
                this.$message({
                  type: 'success',
                  message: `${tips}成功！`
                });
              } else {
                this.$message({
                  type: 'error',
                  message: `${tips}失败！`
                });
              }
            })
            .catch((error) => {
              let errMsg = row.status === 1 ? '对应工具已被引用，禁止下线！' : '能力状态未上线，暂不能注册上线！'
              if (row.target_platform === 'ennew-aip') {
                errMsg = `能力空间同步${tips}失败，请刷新后重试！`
              }
              this.$message({
                type: 'warning',
                message: errMsg
              });
            });
        })
        .catch(() => {});
    },

    // 获取能力注册列表
    async handleGetAbilityRegistrationList() {
      this.tableLoading = true;
      await getAbilityRegistrationList({
        gpts_ability_id: this.baseInfo.id
      })
        .then((res) => {
          this.tableLoading = false;
          if (res?.status === 200) {
            this.tableData = res?.data;
            // 返回一个数组和一个标志，长度为2，同时包含两个目标平台才不能注册
            this.registerList = this.tableData.map((item) => item.target_platform);
            this.registerSign = !(
              this.registerList.length === 2 &&
              this.registerList.includes('agent-tool') &&
              this.registerList.includes('ennew-aip')
            );
          } else {
            this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg);
          }
        })
        .catch(() => {
          this.tableLoading = false;
        });
    }
  }
};
</script>
<style lang="less" scoped>
.info-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  .info-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    > span {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #323233;
      line-height: 26px;
      margin-right: 12px;
    }
  }
}
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
</style>
