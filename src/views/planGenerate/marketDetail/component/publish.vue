<template>
  <div>
    <el-table
      v-loading="tableLoading"
        :data="tableData.data"
      style="width: 100%"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
      @row-click="handleReload"
    >
      <el-table-column type="index" label="序号" width="70" fixed="left" />
      <el-table-column prop="version" label="版本号" />
      <el-table-column prop="create_date" label="创建时间" />
      <el-table-column prop="update_date" label="发布时间" />
      <el-table-column
        prop="status"
        label="实例状态"
      >
        <template slot-scope="{ row }">
          <Status
            :text="exampleStatusTypeMap[row?.status]?.text"
            :bg-color="exampleStatusTypeMap[row?.status]?.bgColor"
            :dot-color="exampleStatusTypeMap[row?.status]?.dotColor"
          />
        </template>
      </el-table-column>
      <el-table-column prop="updated_name" label="发布人" />
      <el-table-column
        prop=""
        label="操作"
        fixed="right"
        width="140"
      >
        <template slot-scope="{ row }">
          <el-link type="primary" :underline="false" @click="handleReload(row)">版本详情</el-link>
          <el-link type="primary" :underline="false" @click="handleStatus(row)">{{ row.status === 'offline' ? '上线' : '下线' }}</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.data.length >0"  style="text-align: right; padding-top: 16px">
        <el-pagination
          class="new-paper"
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[12, 24, 36, 48, 60]"
          :current-page.sync="tableData.page"
          :page-size="tableData.pageSize"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
  </div>
</template>
<script type="text/javascript">
import Status from '@/components/Status/index.vue'
import { queryAbilityVersion,onLineStatus,offlineStatus} from '@/api/planGenerateApi.js'

export default {
  name: 'ExampleCom',
  components: { Status },
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    },
    schemeId: {
      type: [String, Number],
      default () {
        return ''
      }
    },
    capability_type: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      tableSearch: {
        page:1,
        page_size:12,
        ability_id: +this.$route.query?.id || ''
      },
      tableData: {
        data: [],
        page: 1,
        pageSize: 12,
        total: 0,
      },
      exampleStatusTypeMap : {
        'offline': { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线'},
        'online': { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线'},
      }
    }
  },

  created() {
  },
  mounted() {
    this.queryAbilityVersionList()
  },
  methods: {
    handleReload(row, column) {
      if (!column || ((column.type === "default" && column.property) || column.type === "index")) {
        this.$router.push({
          path: '/abilityCenter/targetList/versionDetail',
          query: {
            ...this.$route.query,
            versionId:row.id,
            scheme_detail_id: row.scheme_detail_id,
            scheme_id: this.schemeId,
            status: row.status,
            code_id:row.code_id,
            create_date: row.create_date,
            version: row.version,
            user_name: row.user_name,
            decision_tree_id: row.decision_tree_id,
            capability_type: this.capability_type,
            update_time: row.update_date
          }
        });
      }
    },
    handleStatus (v) {
      this.$confirm(
        `此操作将${v.status === 'offline' ? '上线' : '下线'}该版本, 是否继续?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          if (v.status === 'offline') {
            onLineStatus({
              version_id: v.id
            }).then((res) => {
              if(res?.data?.code === 200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败!'
                })
              }
            })
          } else {
            offlineStatus({
              version_id:v.id
            }).then((res) => {
              if(res?.data?.code ===200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败: ' + res?.data?.msg
                })
              }
            })
          }

        })
        .catch(() => {

        })
    },
    handleSizeChange(val) {
      this.tableSearch.page = 1;
      this.tableSearch.page_size = val;
      this.queryAbilityVersionList();
    },
    handleCurrentChange(val) {
      this.tableSearch.page = val;
      this.queryAbilityVersionList();
    },
    // 实例信息
    async queryAbilityVersionList() {
      this.tableLoading = true
      await queryAbilityVersion(this.tableSearch).then(res => {
        this.tableLoading = false
        if (res?.data?.code === 200) {
          this.tableData = { ...res?.data.result };
          this.tabelData.pageSize = this.tableSearch.page_size;
        } else {
          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
:deep(.el-table__body-wrapper) .el-table__cell {
  cursor: pointer;
}
</style>
