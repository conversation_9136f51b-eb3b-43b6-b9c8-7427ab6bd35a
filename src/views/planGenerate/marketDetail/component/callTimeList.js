import moment from 'moment'
const value = [
  {
    value: '0',
    minStartTime: moment().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '当日最近5分钟'
  },
  {
    value: '1',
    minStartTime: moment().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '当日最近30分钟'
  },
  {
    value: '2',
    minStartTime: moment().subtract(60, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '当日最近1小时'
  },
  {
    value: '3',
    minStartTime: moment().startOf('day').format('-MM-DD HH:mm:ss'),
    maxStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    label: '当日全天'
  }
]

export default { value }
