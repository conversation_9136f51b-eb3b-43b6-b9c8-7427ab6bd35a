<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="实例部署配置"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="40%"
    >
      <div>
        <el-form
          ref="form"
          label-position="right"
          label-width="0px"
          :model="formData"
          :rules="rules"
        >
          <el-form-item label="" prop="chooseData">
            <el-select v-model="formData.chooseData" clearable :multiple="false" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in shiliList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
                <span style="float: left">{{ item.name }}</span>
              </el-option>
            </el-select>
            <div style="font-size: 14px;color:#656566;"><i class="el-icon-warning-outline" style="margin-right: 4px"></i>版本发布之后，新配置生效</div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="createSubmit">确定</el-button>
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAbilityEngineServiceList } from '@/api/planGenerateApi.js';

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    bushuId: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      showFlag: false,
      formData: {
        chooseData: '',
      },
      shiliList: [],
      rules: {
        chooseData: [{ required: true, message: '请选择部署配置', trigger: 'blur' }],
      },
      loading: false,
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
          this.queryDataInfo();
        } else {
          this.showFlag = false;
        }
      },
      immediate: true, deep: true
    }
  },
  methods: {
    queryDataInfo() {
      this.loading = true;
      const isSimulation = this.$route.query.agent_scene_code === 'expert_general';
      getAbilityEngineServiceList(isSimulation).then((res) => {
        this.loading = false;
        if (res.status === 200 && res.data?.status === 200) {
          this.shiliList = res.data.data;
          this.formData.chooseData = Number(this.bushuId) || ''
        } else {
          this.shiliList = [];
        }
      }).finally(() => {
        this.loading = false;
      });;
    },
    onClose() {
      this.formData.chooseData = '';
      this.loading = false;
      this.showFlag = false;
      this.$refs.form.clearValidate();
      this.$emit('close');
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          const filters = this.shiliList.filter(item => item.id === this.formData.chooseData)
          console.log('filters[0]', filters);
          if (filters.length) {
            this.formData.chooseData = '';
            this.$refs.form.clearValidate();
            this.$emit('close', filters[0]);
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-select-dropdown__item) {
  max-width: 700px;
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;

  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;

  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;
}
</style>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
