<template>
  <div
    id="chatContainer"
    v-loading="taskGenLoading"
    element-loading-text="沉淀生成中..."
    element-loading-spinner="el-icon-loading"
    :class="
      $store.state.planGenerate.isIframeHide ? 'chatContainer chatContainerFrame' : 'chatContainer'
    "
  >
    <div
      :class="
        schemeInfo.agent_scene_code === 'other_assistant_scene'
          ? 'containerBox containerBoxBig'
          : 'containerBox'
      "
    >

      <div
        id="left-content"
        :style="{
          width: leftWidth,
          maxWidth: leftWidth,
          marginRight: planDetailShow && !phoneFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: thinkFullFlag ? '' : 'relative',
          marginLeft: phoneFlag ? '0px' : '0px'
        }"
        :class="
          rightFullFlag ? 'containerCard customMd containerCardFull' : 'customMd containerCard'
        "
      >
        <div class="cardContent">
          <homePage v-if="showHome"></homePage>
          <div
          v-show="!showHome"
            ref="chatBox"
            class="chatScroll"
            :style="{ userSelect: isDragging ? 'none' : 'auto' }"
          >
            <div v-for="(chatMessage, index) in historyChat.messages.slice(1)" :key="index">
              <div class="gptAnsWrap">
                <div
                  v-if="chatMessage?.role !== 'user_proxy'"
                  class="gptAvator"
                  style="margin-right: 12px"
                >
                  <img
                    v-if="
                      chatMessage.agent_role_id &&
                      agentAvatorInfoMap[chatMessage.agent_role_id]?.icon
                    "
                    :src="agentAvatorInfoMap[chatMessage.agent_role_id]?.icon"
                  />
                  <img v-else-if="agentAvatorInfo.icon" :src="agentAvatorInfo.icon" />
                  <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
                </div>
                <div
                  :style="{
                    flex: 1
                  }"
                >
                  <div
                    :class="
                      chatMessage?.role === 'user_proxy' ? 'gptAnsBox gptUserBox' : 'gptAnsBox'
                    "
                  >
                    <div>
                      <div v-if="chatMessage?.role !== 'user_proxy'" class="gptAns left-message" :class="chatMessage.content.some(item=>(item.type ==='buttons' && item.content)) ? 'buttons-margin': chatMessage.content.some(item=>(item.type ==='card' && item.content)) ? 'text-margin card-margin' : 'text-margin'">
                        <div
                          v-for="(chatMessageContent, itemBIndex) in chatMessage.content"
                          :key="itemBIndex"
                          class="all"
                          :class="!chatMessageContent.content ? 'hidden-heigth': '' "
                        >
                          <div v-if="chatMessageContent.type === 'text'" class="text">
                            <MyEditorPreview
                              id="MyJsonEditorResult"
                              ref="MyJsonEditorResult"
                              :md-content="chatMessageContent.content"
                            ></MyEditorPreview>
                          </div>
                          <div v-if="chatMessageContent.type === 'card'" class="card">
                            <div
                              v-for="(messageCardItem, ind1) in chatMessageContent.content"
                              :key="ind1"
                              :disabled="inputDisabled"
                              class="card-adress card-item"
                              @click="handleButtonCardClick({ ...messageCardItem, level: 2 })"
                            >
                              <div class="items" v-if="messageCardItem.img">
                                <div class="card-img"><img :src="messageCardItem.img" alt=""></div>
                                <div class="card-button">{{ messageCardItem.content }}</div>

                              </div>
                              <div v-else>
                                <div class="card-content" @click="handleCardClick(messageCardItem)">
                                  <span class="card-h">{{ messageCardItem.title }}</span>
                                  <span style="width: 200px; overflow: hidden;">{{ messageCardItem.text }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div v-if="chatMessageContent.type === 'img'" class="img">
                            <img :src="chatMessageContent.url" />
                          </div>
                          <div v-if="chatMessageContent.type === 'buttons'" class="buttons flex-wrap">
                            <div  v-for="(messageButtonItem, btnIndex) in chatMessageContent?.content"
                            :key="btnIndex" style="padding: 5px 0;">
                              <el-button
                                class="buttons-item"
                                :disabled="inputDisabled"
                                plain
                                @click="handleButtonCardClick({ ...messageButtonItem, level: 2 })"
                                >{{ messageButtonItem.content }}</el-button
                              >
                              <!-- <el-button
                                v-if="messageButtonItem.content !== '知识沉淀' && messageButtonItem.content !== '能力沉淀'"
                                class="buttons-item"
                                :disabled="inputDisabled"
                                plain
                                @click="handleButtonCardClick({ ...messageButtonItem, level: 2 })"
                                >{{ messageButtonItem.content }}</el-button
                              >                          -->
                            </div>
                          </div>
                          <div v-if="chatMessageContent.type === 'loading'" class="loading">
                            <span class="loading-text">正在输入</span>
                          </div>
                        </div>
                      </div>
                      <div v-else class="gptAns user-message">
                        <span>
                          {{ chatMessage.message }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="chatMessage.author?.role === 'user_proxy'"
                  class="gptAvator"
                  style="margin-left: 6px"
                >
                  <img
                    src="@/assets/images/planGenerater/user-chat-icon.png"
                    style="margin-left: 3px"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="chatFooter">
             <div  class="items-tag" v-for="(chatMessageContent, itemBIndex) in historyChat?.messages[0]?.content" :key="itemBIndex">
               <div >
                <div v-if="chatMessageContent.type === 'buttons'" class="buttons">
                            <el-button
                              v-for="(messageButtonItem, btnIndex) in chatMessageContent?.content"
                              :key="btnIndex"
                              class="buttons-item"
                              :disabled="inputDisabled"
                              @click="handleButtonCardClick({ ...messageButtonItem, level: 2 },'hidHome')"
                              >{{ messageButtonItem.content }}</el-button
                            >
                          </div>
               </div>
             </div>
            <div class="chatFooterTextInput">
              <div id="myInputText" class="chatInput">
                <div v-if="currentRoleInfoTemp.name" class="roleTag">
                  <el-tag size="small" closable @close="removeRole"
                    >{{ currentRoleInfoTemp.name }}
                  </el-tag>
                </div>
                <el-input
                  ref="myChatInputText"
                  v-model="toMessage.content"
                  clearable
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 2 }"
                  style="width: 100%"
                  :placeholder="
                    navType === 'Mac'
                      ? '请输入你的问题，可通过cmd+回车换行'
                      : '请输入你的问题，可通过Ctrl+回车换行'
                  "
                  @input="handleInput"
                  @keydown.native="carriageReturn($event)"
                  :disabled="inputDisabled"
                >
                </el-input>
              </div>
              <div class="send-btn">
                <div class="upload-btn">
                  <!-- <el-tooltip
                    class="item"
                    effect="dark"
                    placement="top"
                    content="
                      instanceInfo?.enable_image_text_msg
                        ? '支持图片上传，基于图文问答'
                        : '当前能力不支持图文消息'
                    "
                  > -->
                    <!-- <el-button
                      :type="true ? 'primary' : 'info'"
                      icon="el-icon-picture-outline"
                      circle
                      :disabled="!instanceInfo?.enable_image_text_msg"
                    ></el-button> -->
                    <el-dropdown @command="handleCommand"
                        @visible-change="visibleChange"
                        :disabled="!instanceInfo?.enable_image_text_msg">
                        <el-button :class="(inputDisabled) ? 'active-upload': 'expert-upload'" :disabled="!instanceInfo?.enable_image_text_msg">
                        </el-button>
                       <el-dropdown-menu slot="dropdown">
                         <el-dropdown-item command="personInfo">
                          <el-upload
                              ref="uploadBtn"
                              :action="uploadUrl"
                              :show-file-list="false"
                              :data="uploadParam"
                              :limit="1"
                              class="upload-btn"
                              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                              :before-upload="beforeUpload"
                              :on-success="modelUploadSuccess"
                            >图片上传
                          </el-upload>
                          </el-dropdown-item>
                         <el-dropdown-item command="changePWD">文件上传</el-dropdown-item>
                       </el-dropdown-menu>
                     </el-dropdown>
                    <!-- <el-button class="expert-upload" :disabled="!instanceInfo?.enable_image_text_msg">
                    </el-button> -->
                     <!-- <span ></span> -->
                      <!-- <img src="@/assets/images/planGenerater/expert-upload.png" alt=""> -->
                  <!-- </el-tooltip> -->
                 </div>
                <div
                  :class="
                    ([
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat.messages[historyChat.messages.length - 1].id) ||
                    hasChatingName !== '' ||
                    shibieLoading
                      ? 'yuyinBtn yuyinBtnDisabled'
                      : 'yuyinBtn'
                  "
                >
                <el-button v-if="speakingFlag === 'start'" @click="startRecording" :class="(inputDisabled) ? 'active-voic': 'expert-voic'">
                </el-button>
                 <!-- <button v-if="speakingFlag === 'start'" @click="startRecording" class="expert-voic"> -->
                  <!-- <img
                    v-if="speakingFlag === 'start'"
                    src="@/assets/images/planGenerater/expert-voice.png"
                    @click="startRecording"
                  /> -->
                 <!-- </button> -->
                  <img
                    v-if="speakingFlag === 'running'"
                    src="@/assets/images/planGenerater/yuyin.gif"
                    @click="stopRecording"
                  />
                  <img
                    v-if="speakingFlag === 'end'"
                    src="@/assets/images/planGenerater/zhuanhuan-loading.gif"
                  />
                </div>

                <el-tooltip class="item" effect="dark" content="请输入你的问题" placement="top">
                  <el-button class="send-desc" :disabled="!toMessage.content" :class="(active && toMessage.content) ? 'active': ''" @click="handleInputVal">
                  </el-button>
                    <!-- <img  src="@/assets/images/planGenerater/expert-send.png" alt=""> -->
                    <!-- <img src="@/assets/images/planGenerater/back.png" style="margin-right: 4px" />发送 -->
                </el-tooltip>
                <!-- <el-button
                  type="primary"
                  icon="el-icon-position"
                  circle
                  :disabled="!toMessage.content"
                  @click="handleInputVal"
                ></el-button> -->

              </div>
            </div>
            <div
              class="chatFooterImage"
              v-if="this.toMessage?.image_path || '' != ''"
              style="height: 140px; background-color: #e5e5e5"
            >
              <el-image
                style="width: 100px; height: 100px; margin-left: 10px; margin-top: 10px"
                :src="this.toMessage.image_path"
                :fit="fit"
                :preview-src-list="[this.toMessage.image_path]"
              ></el-image>
              <div
                style="width: 120px; display: flex; justify-content: center; align-items: center"
              >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  circle
                  style="color: red; padding-top: 0px; background-color: #e5e5e5"
                  @click="clearImage"
                ></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import homePage from './homePage.vue';
import { mapGetters } from 'vuex';
import { AddScheme, bindTag, getWsID, getCurrentUserInfo } from '@/api/planGenerateApi.js';
import 'highlight.js/styles/stackoverflow-dark.css';
import '@/style/github-markdown.css';
import MyEditorPreview from '@/views/planGenerate/mdEditorPreview.vue'
import Recorder from 'js-audio-recorder';
const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
};
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {};
export default {
  components: {
    MyEditorPreview,
    homePage,
    // IconSvg
  },
  data() {
    return {
      inputDisabled1:true,
      active:false,
      showHome:true,
      showButtons:false,
      displayType: 1,
      inputDisabled: false, // 新增属性，是否禁用输入框
      speakingFlag: 'start',
      phoneFlag: false,
      phoneStatus: 'start',
      recorderEx: new Recorder(parameter),
      recorderPhoneEx: new Recorder(parameter),
      qaBoxLoading: false,
      contentEditor: '',
      qaList: [],
      agentRoleFlag: false,
      planSearchFlag: false,
      planSearch: '',
      testdata:
        'graph TD\nA([开始]) --> B1{检查电源插座是否有电}\nB1 -->|有电| B2[检查家里主电源开关是否打开]\nB2 -->|打开| C1[检查家庭电路中断器是否跳闸]\nC1 -->|跳闸| E1[复位中断器-然后检查电是否恢复]\nE1 -->|电恢复| END([结束])\nE1 -->|电未恢复| F[联系专业电工进行故障排查和维修]\nC1 -->|没有跳闸| D1[检查电路配线是否受损]\nD1 -->|受损| E2[修复或更换电路线路-然后检查电是否恢复]\nE2 -->|电未恢复| F\nD1 -->|未受损| F\nB2 -->|没有打开| B3[打开电源开关-然后检查电是否恢复]\nB3 -->|电未恢复| F\nB1 -->|没有电| C1',
      isSuperAdmin: false, // 是否超级管理员
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      tableLoading: false, // 加载状态
      detailContent: { text: '', file_url: '' },
      hisDetail: '',
      processContent: { text: '' },
      processRunningContent: '', // 最后一条正在思考的过程内容
      processList: [],
      taskList: [],
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      historyChat: {
        conversation_id: '',
        messages: []
      },
      currentText: '',
      toMessage: { content: '', image_key: '', image_path: '' },
      socket: null,
      systemMessages: '',
      taskStatusText: '',
      timer: null,
      agentError: false,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '100%',
      rightWidth: '',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskGenLoading: false,
      insertWriteFlag: true,
      replaceWriteFlag: true,
      writeText: '',
      greets: '',
      taskGenType: '',
      sessionId: '',
      activeStep: 0,
      treeData: '', // 思维图、思维树内容
      optionDataProcess: '',
      treeDataProcess: '', // 生成过程
      treeDataProcessNodes: [], // 有分组的生成过程
      abilityDataProcess: '', // 生成过程
      abilityDataProcessStatus: '', // 生成过程状态
      codeAnalysisData: '', // 代码分析
      codeAnalysisDataStatus: '', // 代码分析过程状态
      codeAnalysisProcessData: '', // 代码分析过程
      codeAnalysisProcessDataStatus: '', // 代码分析过程状态
      treeStatus: -1,
      cursorInsertPosition: '',
      userInfoData: {}, // 当前用户登录信息
      showGuess: false, // 是否显示参考提示词列表标识
      guessList: [], // 参考提示词的内容列表
      hasChatingName: '', // 开始会话的时候，判断当前有没有被其他人使用中
      panZoomRef: null,
      processVisable: false, // 思考过程弹窗标志
      sqlData: '', // sql流数据
      sqlStatus: -1,
      chendianVisable: false, // 参考已沉淀方案弹窗标志
      chendianVisable2: false,
      globalChendianList: [],
      chendianList: [], // 沉淀方案列表
      youhuaVisable: false,
      planStatus: -1, // 方案优化流状态
      optimizeData: '', // 方案优化流数据
      abilityList: [], // 方案优化可选能力列表，用来判断方案优化按钮是否可用
      dagangFlag: false, // 大纲视图
      lastClickTime: 0, // 点击间隔
      inputHeight: 60,
      navType: '',
      agentAvatorInfo: {}, // 角色基本信息
      agentRoleList: [], // 角色列表
      agentAvatorInfoMap: {}, // 角色信息map
      currentRoleInfo: {}, // 当前角色id
      showSelectVisable: false,
      currentRoleInfoTemp: {}, // 临时
      agentAvatorInfoList: [],
      ansBoxLoading: false,
      ansRole: '',
      audioChunks: [],
      yuyinText: '',
      shibieLoading: false,
      eventSource: null,
      schemeInfo: {},
      instanceInfo: {},
      websocket: null,
      audioContext: null,
      websocketPara: {
        URI: 'wss://vop.baidu.com/realtime_asr',
        APPKEY: 'zrhz2KGQLCxgVrIPdvcUa9c2',
        DEV_PID: 15372, // 声道，支持 1 或 2， 默认是1,
        sample: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        CHUNK_SIZE: 1024, // 每个音频帧的大小，单位为字节
        APPID: 60519323
      },
      uploadUrl: '',
      uploadParam: {},
      newParams: {},
      callFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter',
      globalNavigatorStatus: 'common/getMenuCollapseGetter'
    })
  },
  watch: {
    'historyChat.messages': {
      immediate:true,
      deep:true,
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    }
  },
  beforeDestroy() {},
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    this.initChat();
    // import('@/assets/images/icon/test.svg');
  },
  methods: {
   handleCommand (command) {
      switch (command) {
        case 'personInfo':

          break
        case 'changePWD':

          break
      }
    },
    visibleChange (val) {
      if (val) {

      }
    },
    // 用户点击卡片或按钮
    handleButtonCardClick(obj,type='') {
      if(type ==='hidHome') {
        this.showHome = false
      }
      this.userSendMessage(obj);
    },
    handleAdreessClick(obj,type='') {

      // obj = obj.map(item=>{
      //   return {
      //     ...item,
      //     level: 2
      //   }
      // })
      console.log('111111111',obj)
      // this.userSendMessage(obj);
      this.userSendMessage({
        content:'沉淀新能力',
        event: "sendMessage",
        level: 2,
        type: 'button'
      });
    },
    // 用户手动输入
    handleInputVal() {
      this.showHome = false
      this.userSendMessage({
        content: this.toMessage.content,
        event: 'sendMessage',
        type: 'manualInput'
      });
    },

    initChat() {
      this.fetchStream();
    },
    asiistSendMessage(assistMessageContent) {
      if (
        this.historyChat.messages.length > 0 &&
        this.historyChat.messages[this.historyChat.messages.length - 1].role === 'assist'
      ) {
        this.historyChat.messages.pop();
      }
      this.handlePushMessages({
        role: 'assist',
        body: assistMessageContent
      });

      // 如果是新建 则调用新建接口直接跳转
      assistMessageContent?.forEach((x) => {
        if (x?.event === 'create') {
          this.callFlag = true;
          this.newParams = x.params;
          this.handleNewBuild();
        }
      });
    },
    userSendMessage(obj) {
      console.log(obj,222222);
      if (this.toMessage.content) {
        window.message = {
          content: this.toMessage.content,
          image_key: '',
          image_path: ''
        };
        this.toMessage.content = '';
      }
      if (obj?.event === 'sendMessage') {
        const msg = obj.params?.message
          ? obj.params.message
          : obj.type === 'card'
          ? obj.title
          : obj.content;
        console.log('进来了吗---',msg,obj.params,obj)
        this.handlePushMessages({
          message: msg,
          source: obj.type,
          role: 'user_proxy'
        });
      } else if (obj.event === 'openlink') {
        if (obj?.params?.url) {
          window.open(obj?.params?.url, '_blank');
        }
      }
      if (obj?.event !== 'openlink') {
        this.fetchStream();
      }
    },
    handlePushMessages(obj) {
     this.scrollToBottom();
      this.historyChat.messages.push({
        role: obj?.role,
        content: obj.body,
        message: obj.message,
        source: obj.source
      });
      this.scrollToBottom();
    },
    async handleNewBuild() {
      this.taskGenLoading = true;
      const param = {
        name: this.newParams.scene_name,
        description: this.newParams.scene_name,
        scene_version_id: this.newParams.scene_id,
        agent_scene_code: this.newParams.scene_type,
        image_key: '',
        scheme_detail_name: '',
        agent_id: '',
        contributors: [],
        tag_ids: [],
        ext_data_info: [],
        visibility: 'public'
      };
      if (this.newParams.scene_name) {
        window.message = {
          content: this.newParams.scene_name,
          image_key: '',
          image_path: ''
        };
      }
      AddScheme(param)
        .then(async (res) => {
          if (res.status === 200 && res.data.code * 1 === 200) {
            await bindTag({
              tag_ids: [],
              biz_id: res.data.result.id
            }).then((ress) => {
              console.log('绑定成功', ress.data);
            });
            this.$message({
              type: 'success',
              message: '创建成功!'
            });

            this.$store.commit('planGenerate/setInitSengMsg', window.message.content);
            this.onClose(res.data.result, this.newParams.scene_type);
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onClose(item, scene) {
      window.customeDescription = window.message.content;
      this.taskGenLoading = false;
      if(scene === 'digital_twin_assistant_scene' ){
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          // path: '/planGenerate/hqwPlanchat',
          query: {
            ...this.$route.query,
            id: item.id,
            fromMenu: '1',
            status: item.status
          }
        })
      }
      else {
        this.$router.push({
          path: '/planGenerate/planchat',
          query: {
            ...this.$route.query,
            status: item.status,
            fromMenu: '2',
            id: item.id
          }
        });
      }
      this.newParams = {};
      this.toMessage.content = '';
    },
    // 回车发送消息
    carriageReturn(event) {
      // const e = window.event || arguments[0];
      // console.log('event', event)
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode === 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault();
          this.$nextTick(() => {
            if (this.toMessage.content) {
              this.showHome = false
              this.handleInputVal();
            }
          });
        } else {
          if (event.ctrlKey || event.metaKey) {
            this.toMessage.content += '\n';
            const target = document.getElementById('myInputText');
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50;
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              });
            }
          } else {
            event.preventDefault();
          }
        }
      }
      // 英文下｜中文下： 13 Enter Enter
      // 中文下有文字没进入输入框情况是：299 Enter Enter
      // if (e.key === 'Enter' && e.code === 'Enter' && e.keyCode === 13) {
      //   // console.log('this.text', this.currentText);
      //   this.$nextTick(() => {
      //     if (this.currentText) {
      //       this.sendMessage();
      //     }
      //   });
      // }
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 100;
    },
    async getToken() {
      const url =
        'https://middle-open-platform.ennew.com/admin/client/getToken?appKey=656d978e-779f-420b-ac25-010f9e0ff021&appSecret=YXpRNkdwVnpvUmNPVzF4OHdEeDVlaVBzVllIZjhpU01qUjN0TG15dEhFa0VweWVYQkw5YUtYQWpCNlNxV01hbg==';

      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('Network response was not ok ' + response.statusText);
        }
        const data = await response.json();
        console.log('Success:', data);
        return data.data;
      } catch (error) {
        console.error('Error:', error);
      }
    },
    // addAssistLoading: {},
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
    async fetchStream() {
      try{
        this.inputDisabled = true; // 设置输入框禁用
        const raw = JSON.stringify({
          chat_history: this.historyChat.messages,
          user_id: getCurrentUserInfo().userId,
          work_space_id: getWsID(),
          tenant_id: getCurrentUserInfo().tenantId,
          session_id: '1',
          env: process.env.VUE_APP_ENV,
          ennUnifiedCsrfToken:localStorage.getItem('ennUnifiedCsrfToken'),
          ennUnifiedAuthorization:localStorage.getItem('ennUnifiedAuthorization')
        });
        let assistMessageContent = [];
        assistMessageContent.push({ type: 'loading' });
        this.asiistSendMessage(assistMessageContent);

        const token = await this.getToken();
        const myHeaders = new Headers();
        myHeaders.append('X-GW-Authorization', token);
        myHeaders.append('Content-Type', 'application/json');
        myHeaders.append('Connection', 'keep-alive');
        myHeaders.append('ennUnifiedCsrfToken', localStorage.getItem('ennUnifiedCsrfToken'));
        myHeaders.append('ennUnifiedAuthorization', localStorage.getItem('ennUnifiedAuthorization'));


        const requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow'
        };
        const url=process.env.VUE_APP_ENV==='fat'?'https://open-platform-gateway.ennew.com/algorithm/alg102692/execute?scheme_id=18476':'https://open-platform-gateway.ennew.com/algorithm/alg102692/execute?scheme_id=18242'
        const response = await fetch(url, requestOptions );

        assistMessageContent = [];
        // this.historyChat.messages.pop();
        console.log('11111',response,url)
        if (!response.ok) {
          this.scrollToBottom();
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        this.scrollToBottom();
        const reader = response.body.getReader();

        const decoder = new TextDecoder();
        const result = '';
        let buffer = ''; // 用于存储未完成的部分数据
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }
          // await this.sleep(10);

          // 解码当前数据块并追加到缓冲区
          buffer += decoder.decode(value, { stream: true });
          console.log('11111',buffer)
          // 处理缓冲区中的完整数据
          const parts = buffer.split('data: ');
          for (const line of parts) {
            if (line.trim()) {
              // 忽略空行
              const trimmedLine = line.trim();
              if (trimmedLine === '[DONE]') {
                continue;
              }
              let messageJson = {};
              try {
                messageJson = JSON.parse(trimmedLine);
              } catch (e) {
                buffer = trimmedLine;
                continue;
              }
              if (messageJson.result && messageJson.result.type === 'text') {
                if (
                  assistMessageContent.length === 0 ||
                  assistMessageContent[assistMessageContent.length - 1].type !== 'text'
                ) {
                  const content = { type: messageJson.result.type, content: '' };
                  assistMessageContent.push(content);
                  this.asiistSendMessage(assistMessageContent);
                }
                assistMessageContent[assistMessageContent.length - 1].content +=
                  messageJson.result.content;
              } else if (messageJson.result && messageJson.result.type === 'img') {
                const content = { type: 'img', url: messageJson.result.url };
                assistMessageContent.push(content);
                this.asiistSendMessage(assistMessageContent);
              } else if (messageJson.result && messageJson.result.type === 'button') {
                if (
                  assistMessageContent.length === 0 ||
                  assistMessageContent[assistMessageContent.length - 1].type !== 'buttons'
                ) {
                  const content = { type: 'buttons', content: [] };
                  assistMessageContent.push(content);
                  this.asiistSendMessage(assistMessageContent);
                }
                const itemContent = {
                  type: 'button',
                  ...messageJson.result
                };
                assistMessageContent[assistMessageContent.length - 1].content.push(itemContent);
              } else if (messageJson.result && messageJson.result.type === 'card') {
                if (
                  assistMessageContent.length === 0 ||
                  assistMessageContent[assistMessageContent.length - 1].type !== 'card'
                ) {
                  const content = { type: 'card', content: [] };
                  assistMessageContent.push(content);
                  this.asiistSendMessage(assistMessageContent);
                }
                const itemContent = {
                  type: 'card',
                  ...messageJson.result
                };
                assistMessageContent[assistMessageContent.length - 1].content.push(itemContent);
              } else if (messageJson.result && messageJson.result.type === 'command') {
                const content = {
                  type: 'command',
                  ...messageJson.result
                };
                assistMessageContent.push(content);
                this.asiistSendMessage(assistMessageContent);
              }
            }
            buffer = '';
          }

          console.log(result); // 处理每一块数据
        }
        console.log('Stream complete');
        this.inputDisabled = false; // 设置输入框启用
        return result;
      }
      catch(err){
        console.error('featchStream error',err)
      }
      finally{
       this.scrollToBottom();
        this.inputDisabled = false; // 设置输入框启用
      }
    },
    handleInput(val) {
      console.log('val---',val)
      this.active = true
      if(!val) {
        this.active = false
      }
    },
    beforeUpload() {},
    modelUploadSuccess() {},
    startRecording() {},
    async handleCardClick(item) {
      if (!item?.title) return;
      try {
        // 先获取空间信息
        const wsId = getWsID();
        const res = await this.$post(`${this.baseUrl}/workspace/detail`, { id: wsId });

        // 保存空间信息
        if (res?.workspaceName) {
          const currWS = {
            workspaceId: res.id,
            workspaceName: res.workspaceName
          };
          localStorage.setItem('currentWorkSpace', JSON.stringify(currWS));
          this.$store.commit('workSpace/setCurrentWorkSpace', currWS);
        }

        // 确保有空间信息后再跳转
        this.$router.push({
          path: '/abilityCenter/targetList',
          query: {
            name: item.title,
            workspaceId: wsId
          }
        });
      } catch (error) {
        console.error('获取空间信息失败:', error);
        // 即使获取失败也允许跳转，使用已有的空间信息
        this.$router.push({
          path: '/abilityCenter/targetList',
          query: {
            name: item.title,
            workspaceId: getWsID()
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.displayType {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 14px;
  color: #4068d4;
  line-height: 22px;
  position: relative;
  cursor: pointer;
  &::after {
    content: '';
    position: absolute;
    right: -8px;
    height: 12px;
    top: 6px;
    width: 1px;
    background: #c8c9cc;
  }
}
:deep(.markdown-body) {
  ol {
    list-style: decimal !important;
    > li {
      list-style: decimal !important;
    }
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.qa-loading-spinner {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}
.qa-loading-spinner2 {
  width: 42px;
  height: 36px;
  margin-left: 2px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}
.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}
.qaBox {
  margin-left: 40px;
  .qatitle {
    font-size: 12px;
    color: #696a6f;
    line-height: 20px;
  }
  .qaflex {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    max-width: 100%;
    overflow-x: auto;
    .qaitem {
      background: #f6f8fb;
      border-radius: 2px;
      color: #4068d4;
      font-size: 14px;
      margin-bottom: 6px;
      padding: 4px 12px;
      cursor: pointer;
      word-break: keep-all;
      white-space: nowrap;

      &:hover {
        background: rgba(194, 210, 255, 0.56);
      }
    }
  }
}
.chatContainer {
  height: 100%;
  overflow: hidden;
  background: transparent;
  flex: 1;
  display: flex;
  flex-direction: column;
  // width: 80%;
  // margin: 0 auto;
  .headerBox {
    background-color: #fff;
    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;

      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }

      .sence-tag {
        margin-left: 16px;
        padding: 0 8px;
        height: 24px;
        border-radius: 2px;
        max-width: calc(100vw - 380px);
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
        background: #ebf9ff;
        color: #318db8;
      }
    }

    .headerStep {
      .myStep {
        background: #fff;
        padding: 13px 20px;

        :deep(.el-step__arrow) {
          margin: 0 16px;

          &::before {
            content: '';
            position: static;
            height: 1px;
            width: 100%;
            background: #c8c9cc;
            transform: none;
            display: block;
          }

          &::after {
            display: none;
          }
        }

        :deep(.is-process) {
          color: #4068d4;

          .el-step__icon {
            color: #4068d4;

            &.is-text {
              border: none;
            }
          }
        }

        :deep(.is-success) {
          color: #000;
          border-color: #4068d4;

          .el-icon-check {
            color: #4068d4;
          }

          .el-step__icon {
            color: #4068d4;

            &.is-text {
              border: 1px solid #4068d4;
            }
          }
        }

        .empty-space {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  &.chatContainerFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }

    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }

    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }

    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }

    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }

    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;

      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }
  .containerBox {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 90px);
    max-height: calc(100vh - 90px);
    overflow-y: hidden;
    position: relative;
    &.containerBoxBig {
      height: calc(100vh - 147px) !important;
      max-height: calc(100vh - 147px) !important;
    }

    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;

      &:hover {
        background: #3455ad;
      }

      &:active {
        background: #264480;
      }

      img {
        width: 12px;
        height: auto;
      }
    }

    .phoneContent {
      overflow-y: hidden;
      overflow-x: hidden;
      background-image: url('@/assets/images/planGenerater/phoneBg.png');
      background-size: cover;
      margin: 16px 0px 0px 16px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      width: 30%;
      display: flex;
      flex-direction: column;
      position: relative;

      .phoneTitle {
        display: flex;
        width: 100%;
        justify-content: center;
        font-weight: 600;
        font-size: 16px;
        color: #323233;
        line-height: 24px;
        margin-top: 16px;

        span {
          width: 80%;
          text-align: center;
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
          overflow: hidden;
        }
      }

      .phonePhoto {
        display: flex;
        justify-content: center;
        align-content: center;
        margin-top: 25%;
        position: relative;

        .phonePh {
          width: 100px;
          height: 100px;
          box-shadow: 0 0 0 4px white;
          border-radius: 50%;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .bubble {
          width: 80px;
          height: 80px;
          position: absolute;
          top: -50px;
          right: 50px;
          background-image: url('@/assets/images/planGenerater/bubble.png');
          background-size: contain;
          background-repeat: no-repeat;

          img {
            position: absolute;
            left: 25px;
            top: 15px;
            width: 50%;
            height: 50%;
          }
        }
      }

      .phoneStatus {
        margin-top: 35%;
        display: flex;
        justify-content: center;
        cursor: pointer;
      }

      .phoneClose {
        position: absolute;
        left: 50%;
        bottom: 5%;
        transform: translate(-50%, 0%);
        width: 150px;
        height: 100px;
        display: flex;
        justify-content: space-between;
      }

      .phoneStop {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #4775f3;
        border-radius: 50%;

        &.phoneStopDisabled {
          opacity: 0.5;
          cursor: not-allowed;
          // pointer-events: none; /* 阻止事件 */
        }

        img {
          width: 100%;
          height: 100%;
        }
      }

      .phoneClone {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f7622c;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .containerCard {
      //height: calc(100% - 18px);
      // max-height: calc(100vh - 160px);
      overflow-y: hidden;
      overflow-x: hidden;
      // margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: transparent;
      margin-left: 16px;
      //height: calc(100% - 18px);
      //max-height: calc(100% - 18px);

      &.containerCardFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        //height: calc(100% - 50px);
        //max-height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;

        .chatScroll {
          // max-height: calc(100vh - 182px) !important;
        }
      }

      .cardContent {
        display: flex;
        flex-direction: column;
        height: calc(100%);
        max-height: calc(100%);
        position: relative;
        width: 900px;
        margin: 0 auto;
      }
      .chatScroll {
        // max-height: calc(100vh - 320px);
        flex: 1;
        overflow-y: auto;
        padding: 0px 80px 16px 44px;
        transition: all 0.3s ease-out;
        overflow-x: hidden;
        padding-top: 16px;
        .init {
          display: flex;
          margin-bottom: 18px;
          img {
            width: 34px;
            height: 34px;
          }
          .gptAnsBox {
            .gptAns {
              background: #F6F7FB;
              border-radius: 4px;
              color: #323232;
              line-height: 20px;
              position: relative;
              padding: 8px 12px;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
          }
        }

        .gptAnsWrap {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 18px;
          max-width: 100%;

          &:hover {
            .chat-time {
              .time {
                opacity: 1 !important;
              }
            }
          }

          .chat-time {
            height: 17px;
            line-height: 17px;
            // margin: 4px 0px;
            margin-bottom: 4px;
            display: flex;
            opacity: 1;
            justify-content: flex-start;
            width: calc(100% - 0px);

            &.chat-time-user {
              justify-content: flex-end;

              .user {
                flex-direction: row;
              }
            }

            .user {
              color: #646566;
              font-size: 12px;
              display: flex;
              flex-direction: row-reverse;

              .time {
                font-size: 12px;
                opacity: 0;
                padding: 0 5px;
              }

              span {
                color: #646566;
                font-size: 12px;
              }
            }

            .agent-mark {
              background: #eff3ff;
              border-radius: 2px;
              padding: 0px 8px;
              color: #4068d4 !important;
              line-height: 17px;
              font-size: 12px;
              margin-left: 4px;
              display: inline-block;
              max-width: 260px;
              /* 设置文本溢出时的行为为省略号 */
              text-overflow: ellipsis;

              /* 设置超出容器的内容应该被裁剪掉 */
              overflow: hidden;

              /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
              white-space: nowrap;
            }
          }

          .gptAnsBox {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            border-radius: 5px;
            .left-message {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              padding:16px; /* 内边距 */
              background-color: #f9f9f9; /* 背景颜色 */
              font-family: Arial, sans-serif; /* 字体 */
              color: #323233; /* 文字颜色 */
              line-height: 1.6; /* 行高 */
              border-radius: 12px;
              .all {
                // margin-bottom: 16px;
                .text {
                  line-height: 20px;
                }
                .card {
                  display: flex;
                  flex-wrap: wrap; /* 添加这一行 */
                  .card-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    border: 1px solid #e0e0e0;
                    padding: 12px 16px; /* 增加内边距以适应更大的元素 */
                    margin: 0px 16px 16px 0px; /* 增加外边距以适应更大的元素 */
                    color: #606266;
                    background-color: #fff;
                    border-radius: 6px; /* 增大圆角半径以增强现代感 */
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
                    transition: all 0.3s ease-in-out; /* 添加过渡动画 */
                    width: 240px; /* 设置固定宽度以保证一致的布局 */
                    .card-h {
                      font-weight: 600;
                      font-size: 14px;
                      color: #323233;
                      line-height: 22px;
                      width: 200px;
                      margin-bottom: 8px;
                      text-align: left;
                      -webkit-line-clamp: 1;
                    }
                    .card-h:hover {
                      color: #4068D4;
                    }
                    .card-t {
                      font-weight: 400;
                      font-size: 14px;
                      color: #646566;
                      line-height: 22px;
                    }
                    &:hover {
                      background: linear-gradient(132deg, #f6f7f8 0%, #e1edf6 100%);
                      border-color: #2980b9;
                      transform: translateY(-5px); /* 鼠标悬停时稍微提升卡片 */
                      cursor: pointer;
                    }

                    span {
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      font-size: 1rem; /* 调整字体大小 */
                    }
                  }
                  .card-item:last-of-type {
                    margin-right: 0;
                  }
                  .card-adress:hover {
                    // background: #ffffff;
                  }
                  .card-adress {
                    // margin-top: 16px;
                    .items {
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                    }
                    img {
                      width: 28px;
                      height: 28px;
                      object-fit: contain;
                      margin-bottom: 16px;
                    }
                    .card-button {
                      font-weight: 400;
                      font-size: 14px;
                      color: #4068D4;
                      line-height: 20px;
                    }
                  }
                }
              }
              img {
                width: 500px;
                height: 100px;
                object-fit: contain;
              }
              .buttons {
                display: flex;
                align-items: center;
                margin-top: 16px;
                flex-wrap: wrap;
                .buttons-item {
                  margin-right: 8px;
                  border: none;
                  color: #323233;
                  // color: #323233;
                }
                .buttons-item:hover {
                  color: #4068D4;
                  background: #fff;
                }
              }
            }
            .card-margin {
              padding-bottom: 0px !important;
            }
            .text-margin {
              .all{
                margin-bottom: 12px
              }
              .all:last-of-type {
                margin-bottom: 0px;
              }
              .hidden-heigth {
                margin-bottom: 16px;
              }
            }
            .buttons-margin {
              .hidden-heigth {
                margin-bottom: 24px;
                margin-top: 12px;
              }
            }
            &.gptUserBox {
              justify-content: flex-end;
              align-items: flex-start;
            }
          }

          .gptAvator {
            width: 34px;
            height: 34px;
            margin-right: 3px;

            img {
              width: 34px;
              height: 34px;
            }

            .userAvator {
              width: 34px;
              height: 34px;
              border-radius: 50%;
              background: #e6ecff;
              color: #2856b3;
              line-height: 34px;
              text-align: center;
              font-size: 12px;
              font-weight: normal;
            }
          }

          .gptAns {
            background: #f6f8fb;
            border-radius: 4px;
            color: #323232;
            line-height: 20px;
            position: relative;
            padding: 8px 12px;
            display: flex;
            align-items: center;

            &.gptUser {
              background: rgba(194, 210, 255, 0.56);
            }
          }
          .user-message {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            padding: 16px;
            background-color: #EFF3FF;
            border-radius: 12px;
            color: #323233;
            line-height: 1.6;
          }

          .gptAns2 {
            background: #f6f8fb;
            border-radius: 6px;
            color: #4068d4;
            line-height: 20px;
            position: relative;
            //padding: 8px 12px;
            display: flex;
            align-items: center;
          }

          .think-wrap {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            cursor: pointer;
            margin-top: 8px;
            // margin-left: 37px;
            .think-btn {
              border-radius: 2px;
              border: 1px solid rgba(64, 107, 212, 0.4);
              font-size: 12px;
              padding: 2px 8px;
              font-weight: 500;
              color: #4068d4;
              line-height: 18px;
              margin-right: 8px;
              word-break: keep-all;

              &:hover {
                background: #f6f7fb;
              }

              &:active {
                background: #eff3ff;
              }

              &.think-btn-disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }
            }
          }
        }
      }
      .thinkContent {
        margin-left: 16px;
        width: calc(100% - 32px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        max-height: 225px;
        height: 225px;
        overflow-y: auto;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #dcdde0;
        transition: height 0.1s;

        &.thinkContentFull {
          position: fixed !important;
          left: 60px;
          width: calc(100vw - 90px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 999 !important;
          max-height: calc(100vh - 150px) !important;
          top: 210px;
        }

        &.thinkContentFullFull {
          position: fixed !important;
          left: 0px;
          margin-left: 0px;
          width: 100vw !important;
          height: calc(100vh - 50px) !important;
          overflow: hidden;
          z-index: 999 !important;
          max-height: calc(100vh - 50px) !important;
          top: 50px;
        }

        &.thinkContentFullSmall {
          position: fixed !important;
          left: 180px;
          width: calc(100vw - 200px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 999 !important;
          max-height: calc(100vh - 150px) !important;
          top: 210px;
        }

        .thinkHeader {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 12px 12px;

          .title {
            color: #323233;
            line-height: 20px;
            display: flex;
            align-items: center;

            img {
              height: 24px;
              width: 24px;
              margin-right: 4px;
            }
          }

          .thinkOpt {
            display: flex;

            .think-btn {
              font-size: 14px;
              margin-left: 4px;
              cursor: pointer;
              width: 24px;
              height: 24px;
              text-align: center;
              line-height: 22px;
              font-weight: bold;

              &.think-btn-blue {
                background-color: #4068d4 !important;
                border-radius: 4px;

                &:hover {
                  background: #3455ad !important;
                }

                &:active {
                  background: #264480;
                }
              }

              &:hover {
                background-color: #ebecf0;
                border-radius: 4px;
              }

              img {
                width: 12px;
                height: 12px;
              }
            }
          }
        }

        .thinkWrap {
          background: #ffffff;
          padding: 0px 12px 12px 36px;
          max-height: calc(100% - 40px);
          overflow-y: auto;

          .thinkItem {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: start;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dcdde0;
            margin-top: 12px;

            &:first-child {
              margin-top: 0px;
            }
          }

          .itemContent {
            color: #646566;
            line-height: 22px;
            flex: 1;
            margin-left: 8px;
          }
        }
      }

      .clearChatBtnBox {
        // position: absolute;
        cursor: pointer;
        bottom: 78px;
        margin-left: 48px;
        // width: 100%;
        background: #ffffff;
        z-index: 2;
        display: flex;
        align-items: center;

        &.clearChatBtnBoxHigh {
          bottom: 100px !important;
        }

        .guess {
          margin-bottom: 5px;
          background: #fff;
          box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
          border-radius: 2px;
          max-width: 500px;
          min-width: 88px;
          padding: 5px;
          max-height: 150px;
          overflow-y: auto;
          position: absolute;
          bottom: 102px;
          left: 45px;
          z-index: 7;

          &.guessHigh {
            bottom: 132px !important;
          }

          .guess-header {
            font-size: 12px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 4px 8px;
            border-bottom: 1px solid #ebecf0;

            .title {
              color: #646566;
              font-size: 12px;
            }

            .huanyihuan {
              font-size: 12px;
              margin-left: 2px;
              color: #4068d4;
              cursor: pointer;

              &:hover {
                color: #3455ad;
              }

              &:active {
                color: #264480;
              }
            }

            .plansearch {
              cursor: pointer;

              &:hover {
                color: #3455ad;
              }

              &:active {
                color: #264480;
              }
            }
          }

          li {
            span {
              display: block;
              line-height: 32px;
              padding: 0 5px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            &:hover {
              background: #eff3ff;
            }

            &:active {
              background: #eff3ff;
            }
          }
        }
      }

      .clearChat {
        cursor: pointer;
        border-radius: 2px;
        border: 1px solid rgba(64, 107, 212, 0.4);
        font-size: 14px;
        display: inline-block;
        padding: 2px 8px;
        font-weight: 500;
        color: #4068d4;
        line-height: 18px;
        margin-right: 8px;
        word-break: break-all;
        margin-right: 8px;

        &:hover {
          background: #f6f7fb;
        }

        &.think-btn-disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .selectRole {
        position: absolute;
        z-index: 99;
        left: 46px;
        bottom: 72px;
        width: 260px;
        max-height: 400px;
        overflow-y: auto;
        background: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 4px;
        display: flex;
        flex-direction: column;

        .selectRoleHigh {
          bottom: 102px !important;
        }

        .roleItem {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 2px 4px;
          cursor: pointer;

          &:hover {
            background: #eff3ff;
          }

          &:active {
            background: #eff3ff;
          }

          img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 4px;
          }

          .name {
            cursor: pointer;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; // 默认不换行；
          }

          .mark-tag {
            background: #eff3ff;
            border-radius: 2px;
            padding: 0px 8px;
            color: #4068d4 !important;
            line-height: 17px;
            font-size: 12px;
            margin-left: 4px;
            display: inline-block;
            max-width: 260px;
            /* 设置文本溢出时的行为为省略号 */
            text-overflow: ellipsis;

            /* 设置超出容器的内容应该被裁剪掉 */
            overflow: hidden;

            /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
            white-space: nowrap;
          }
        }
      }
      .chatFooter {
        padding: 0px 80px;
        bottom: 0px;
        left: 0px;
        .items-tag {
          display: flex;
          margin-bottom: 12px;
          .buttons {
            margin-right: 16px;
            .buttons-item {
              background: #F2F3F5;
              color: #4068D4;
            }
            .buttons-item:hover {
              background: #EBECF0;
            }
            .buttons-item:focus {
              background: #DCDDE0;
            }
          }
        }
        .chatFooterTextInput {
          display: flex;
          position: relative;
          width: 100%;
          background: #ffffff;
          justify-content: flex-start;
          align-items: center;
        }

        .chatFooterImage {
          margin-left: 31px;
        }

        .think-btn-disabled {
          width: 25px;
          height: 25px;
          cursor: pointer;
          line-height: 25px;
          margin-right: 6px;
          text-align: center;
          opacity: 0.5;
          cursor: not-allowed;
        }

        .clear {
          width: 25px;
          height: 25px;
          cursor: pointer;
          line-height: 25px;
          margin-right: 6px;
          text-align: center;

          &:hover {
            background: #ebecf0;
          }

          &:active {
            background: #dcdde0;
          }
        }

        .chatInput {
          flex: 1;
          border-radius: 4px;
          border: 1px solid #DCDDE0;
          box-shadow: 0px 2px 28px 0px #E6E8EF;
          margin-bottom: 32px;
          ::v-deep textarea.el-textarea__inner {
            height: 112px !important;
            padding: 12px 12px 12px 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
          }
          ::v-deep textarea.el-textarea__inner::placeholder {
            color: #969799;
          }
          .roleTag {
            padding: 2px 4px;
            // background: #E6ECFF;
            // color: #3455AD;
            // font-size: 12px;
            // padding: 2px 4px;
            // border-radius: 2px;
          }
        }

        .send-btn {
          position: absolute;
          right: 12px;
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          align-items: center;
          // margin-top: 6px;
          // font-size: 12px;
          bottom: 44px;
          .upload-btn {
            height: 32px;
            width: 32px;
            .expert-upload {
              width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/expert-upload.png");
            }
            .expert-upload:hover {
              width: 32px;
              height: 32px;
              border-radius: 0px;
              // background-size: cover;
              // background-repeat: no-repeat;
              // background-size: 100% ;
              // background-position: center;
              background-image: url("~@/assets/images/planGenerater/hover-upload.png");
            }
            .active-upload {
              width: 32px;
              height: 32px;
              border-radius: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/hover-upload.png");
            }
          }
          img {
            width: 18px;
          }

          .send-desc {
            font-size: 10px;
            color: #969799;
            width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/expert-send.png");
            // }
            img {
              width: 32px;
            }
          }
          .active {
              background-image: url("~@/assets/images/planGenerater/active-send.png");
            img {
              width: 32px;
            }
          }

          .yuyinBtn {
            cursor: pointer;
            width: 32px;
            height: 32px;
            margin-right: 8px;
            margin-left: 8px;
            .expert-voic {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/expert-voice.png");
            }
            .expert-voic:hover {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/hover-voice.png");
            }
            .active-voic {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/hover-voice.png");
            }

            &.yuyinBtnDisabled {
              cursor: not-allowed;
            }
          }

          :deep(.el-button) {
            // width: 20px;
            // height: 20px;
            padding: 0px;
            // background: linear-gradient(180deg, #69a0ff 0%, #375fcb 100%);
            // border-radius: 12px;
            border: none;
            // margin-left: 12px;
            // margin-right: 8px;
            // line-height: 20px;

            i {
              font-size: 12px;
              margin-left: -1px;
            }
          }
        }

        .upload-demo {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .updloadBtn {
          width: 18px;
          height: 16px;
          background: url(@/assets/images/planGenerater/upload-icon.png) no-repeat;
          background-size: contain;
        }

        :deep(.el-input__inner) {
          border-radius: 12px;
          border-color: #c8c9cc;
          color: #323233;

          &:focus {
            border-color: #406bd4;
          }
        }

        :deep(.el-textarea__inner) {
          border-radius: 12px;
          border: none !important;
          color: #323233;
          padding-right: 114px;

          &:focus {
            border-color: #406bd4;
          }
        }
      }
    }

    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      height: calc(100% - 18px);
      max-height: calc(100% - 18px);
      overflow-y: hidden;
      margin-top: 16px;
      margin-right: 16px;
      position: relative;

      &.chatRightFull {
        position: fixed;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;

        .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }

        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }

        .optContentBox {
          height: calc(100vh - 180px) !important;
          max-height: calc(100vh - 180px) !important;
        }
      }

      .optContentBox {
        // height: calc(100vh - 340px);
        // max-height: calc(100vh - 340px);
        max-height: 100%;
        height: 100%;
        overflow-y: auto;
        width: 100%;
        position: relative;
        background: transparent !important;
      }

      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnDisabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none; /* 阻止事件 */
            }

            &.rightBtnBlue {
              background-color: #406bd4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .optScroll {
        position: relative;
        height: calc(100vh - 320px);
        max-height: calc(100vh - 320px);
        overflow-y: hidden;
        overflow-x: hidden;
        padding: 20px;
        display: flex;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optScroll2 {
        position: relative;
        height: calc(100vh - 210px);
        max-height: calc(100vh - 210px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }

      .optContent2 {
        max-height: calc(100% - 0px) !important;
        overflow-y: hidden;
      }

      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }
  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    &:hover {
      background: #e0e6ff;

      .process-icon {
        color: #3455ad !important;
      }
    }

    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;

      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }

    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;

      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }

    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;

      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }
  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }

    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }

    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--mini {
    line-height: 0px !important;
    padding: 8px 6px !important;
    img {
      height: 16px;
      margin-top: -2px;
    }
  }
  ::v-deep .is-wait .el-step__icon.is-text {
    border: none !important;
  }
}
.clear-icon {
  width: 20px;
  height: 20px;
  margin-top: 3px;
}
:deep(.fousetest) {
  border: 1px solid #4068d4;
}
</style>
<style lang="scss">
@import 'github-markdown-css/github-markdown.css';
@import 'highlight.js/styles/stackoverflow-dark.css';
.samllinput {
  .el-input__inner {
    border-radius: 2px;
    border-color: #c8c9cc;
    height: 20px !important;
    line-height: 20px !important;
    font-size: 12px;
    color: #323233;
    &:focus {
      border-color: #406bd4;
    }
  }
}
.chendianfangan {
  padding: 0px !important;
  bottom: 98px;
  top: inherit !important;
  &.chendianfanganHigh {
    bottom: 124px !important;
  }
  .guessPovper {
    // margin-bottom: 5px;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    max-width: 500px;
    min-width: 88px;
    padding: 5px;
    max-height: 150px;
    overflow-y: auto;
    //position: absolute;
    //bottom: 23px;
    //left: 0px;
    //z-index: 7;
    .guess-header {
      font-size: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      border-bottom: 1px solid #ebecf0;
      .title {
        color: #646566;
        font-size: 12px;
      }

      .huanyihuan {
        font-size: 12px;
        margin-left: 2px;
        color: #4068d4;
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }

      .plansearch {
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }
    }

    .otherRole {
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 4px;
      }

      .mark-tag {
        background: #eff3ff;
        border-radius: 2px;
        padding: 0px 8px;
        color: #4068d4 !important;
        line-height: 17px;
        font-size: 12px;
        margin-left: 4px;
        display: inline-block;
        max-width: 260px;
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
      }
    }

    li {
      cursor: pointer;

      span {
        display: block;
        line-height: 32px;
        padding: 0 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
      }

      &:hover {
        background: #eff3ff;
      }

      &:active {
        background: #eff3ff;
      }
    }
  }
}
/* 修改滚动条的轨道 */
.chatScroll::-webkit-scrollbar-track {
  background-color: white;
}

/* 修改滚动条的滑块 */
.chatScroll::-webkit-scrollbar-thumb {
  background-color: white;
  border-radius: 10px;
}

/* 修改滚动条的宽度 */
.chatScroll::-webkit-scrollbar {
  width: 10px;
}
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4068d4; // 你可以选择任何你喜欢的颜色
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@media  (min-height: 0px) and (max-height: 740px) {
        .cardContent {
          .left-contain-main {
            overflow-y: auto ;
          }
        }
      }
.loading {
  padding: 8px 0;
  .loading-text {
    color: #999;
    font-size: 14px;
    position: relative;
    &::after {
      content: '...';
      position: absolute;
      animation: loadingDots 1.5s infinite;
      font-weight: bold;
    }
  }
}

@keyframes loadingDots {
  0% { content: '.'; }
  33% { content: '..'; }
  66% { content: '...'; }
  100% { content: '.'; }
}
      .flex-wrap{
        flex-wrap: wrap;
      }
</style>
