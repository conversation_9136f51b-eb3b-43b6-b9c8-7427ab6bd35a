<template>
    <div class="guide-scene">
            <div class="title">
              <div class="title-welcome">
                <span> {{ `亲爱的${userDetailInfo.title || '安全专家'}，您好` }}</span>
                <span>{{ '以下是根据您的技能标签推荐的能力生产场景，请选择' }}</span>
              </div>
            </div>
            <div class="card">
              <div v-for="(item, index) in sceneList" :key="index" class="card-item">
                <el-checkbox
                  :checked="isChecked(item.id)"
                  @change="handleBoxChange($event, item.id)"
                  > 
                  <el-tag effect="dark" v-if="item.resource_type === 1" type="success" size="small">预置</el-tag>
                  <el-tag effect="dark" v-else-if="item.resource_type === 2" type="primary" size="small">自定义</el-tag>
                  {{ item.name }}
                </el-checkbox>
                <div class="description">
                  <el-tooltip effect="dark" :content="item.description" placement="top">
                    <div class="description-text">{{ item.description }}</div>
                  </el-tooltip>
                </div>
                <div class="sign">
                  <div v-if="item.tags.length > 3" class="sign-item-container">
                    <div v-for="(tag, i) in item.tags.slice(0, 3)" :key="i" class="sign-item">
                      <span>{{ tag.name }}</span>
                    </div>
                    <el-tooltip
                      effect="dark"
                      :content="
                        item.tags
                          .slice(3)
                          .map((tag) => tag.name)
                          .join(', ')
                      "
                      placement="top"
                    >
                      <div class="more-tags">{{ item.tags.length - 3 }}+</div>
                    </el-tooltip>
                  </div>
                  <div v-else v-for="(tag, i) in item.tags" :key="i" class="sign-item">
                    <span>{{ tag.name }}</span>
                  </div>
                </div>
                <div class="icon">
                  <img src="https://res.ennew.com/image/png/ead8baf0c5d02203012f0154ba5a71e8.png" />
                </div>
              </div>
            </div>
            <div class="button">
              <el-button type="primary" :disabled="!canCompeleted" @click="saveSceneUserBindFn"
                >完成并进入经验沉淀
              </el-button>
            </div>
          </div>
</template>
<script>
import {
  getSceneVisibleListWithTop,
  sceneListByUserBind,
  saveSceneUserBind,
} from '@/api/planGenerateApi.js';

const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {};
let title = '';
export default {
  data() {
    return {
      canCompeleted: false,
      searchVal: '',
      activeName: 'first',
      sceneList: [],
      checkedList: [],
      userDetailInfo: {
        name: userInfo.nickName,
        title: title
      },
    };
  },
  computed: {
    // 根据场景id判断当前checkbox是否选中
    isChecked() {
      return (id) => {
        return this.checkedList.indexOf(id) !== -1;
      };
    }
  },
  mounted() {
    this.workspaceId = Number(this.$route.query.workspaceId);
    this.getUserSceneBindList();
    this.getSceneWithTop();
  },
  methods: {
    // checkbox选中状态改变时的处理函数
    getUserSceneBindList() {
      sceneListByUserBind({
        keyword: '',
        user_id: userInfo.userId,
        workspace_id: this.workspaceId
      }).then((res) => {
        const result = res.data || [];
        if (result.length > 0) {
          this.showCardList = false;
          this.checkedList = result.map((item) => {
            return item.id;
          });
          this.canCompeleted = true;
        }
      });
    },
    handleBoxChange(checked, id) {
      // 更新选中的场景id列表
      if (checked) {
        if (this.checkedList.indexOf(id) === -1) {
          this.checkedList.push(id);
        }
      } else {
        if (this.checkedList.indexOf(id) !== -1) {
          this.checkedList = this.checkedList.filter((val) => {
            return val !== id;
          });
        }
      }
      this.canCompeleted = this.hasValue(this.checkedList);
    },
    // 检查是否有值
    hasValue(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== undefined && arr[i] !== null && arr[i] !== false) {
          return true;
        }
      }
      return false;
    },
  
    // 保存用户场景模版绑定配置
    saveSceneUserBindFn() {
      
      saveSceneUserBind({
        sceneIds: this.checkedList
      }).then((res) => {
        this.$emit('show-ability', true);
      });
    },
    // 获取场景模版
    getSceneWithTop() {
      getSceneVisibleListWithTop({
        keyword: '',
        user_id: userInfo.userId,
        workspace_id: this.workspaceId,
        scene_type: ''
      }).then((res) => {
        const result = res.data || [];
        if (result.length > 0) {
          this.sceneList = result;
        }
      });
    },
  }
};
</script>
<style lang="scss" scoped>
.guide-scene {
    background-color: #fff;
    overflow: auto;
    .title {
        background: url('https://res.ennew.com/image/png/53dc32b0db0270a3871352221bc586e8.png'), linear-gradient(180deg, #E7EDF8 0%, #E7EDF8 49%, #FFFFFF 100%);
        background-size: contain, auto; // 背景图片大小为contain，渐变色大小为auto
        background-repeat: no-repeat; // 确保背景图片不重复
        background-position: right; // 背景图片靠右对齐
        width: 100%;
        height: 231px;

        .title-welcome {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 40px;
        }
        span {
        font-weight: 500;
        font-size: 22px;
        color: #264480;
        }
    }

    .card {
        max-height: calc(126px * 2 + 40px);
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        .card-item {
        display: flex;
        position: relative;
        flex-direction: column;
        width: 30%;
        height: 126px;
        margin: 10px;
        border-radius: 4px;
        border: 1px solid #4068d4;
        padding: 16px 16px 13px;
        transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease; // 添加过渡效果
        background-color: #fff;
        &:hover {
            transform: scale(1.05); // 放大效果
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); // 更明显的阴影效果
        }
        :deep(.el-checkbox) {
            display: flex;
            align-items: center;

            .el-checkbox__label {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #1d2129;
            font-style: normal;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            }
        }

    .sign {
        display: flex;
        margin-left: 0px;
        margin-top: 8px;

        .sign-item-container {
        display: flex;
        }

        .more-tags {
        cursor: pointer;
        margin-left: 4px;
        color: #318db8;
        background: #ebf9ff;
        border-radius: 2px;
        padding: 1px 8px;
        }

        .sign-item {
        padding: 1px 8px;
        background: #ebf9ff;
        border-radius: 2px;
        margin-right: 8px;

        span {
            color: #318db8;
            white-space: nowrap;
        }
        }
    }

    .icon {
        position: absolute;
        right: 6px;
        bottom: 6px;

        img {
        width: 46px;
        height: 46px;
        }
    }
    }

    .description-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    }

    .button {
        display: flex;
        justify-content: center;
        margin: 72px 0 106px;
    }
}
</style>