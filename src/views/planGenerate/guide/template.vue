<template>
 <div class="common-drawer">
   <el-drawer
     :visible.sync="drawerVisible"
     :direction="direction"
     :size="computedSize"
     :modal="showModal"
     :custom-class="modalClass"
     :close-on-press-escape="closeOnEsc"
     @close="handleClose"
   >
   <!-- 标题插槽 -->
   <template #title>
        <div class="drawer-header">
          <slot name="title">
            <span class="default-title">{{ title }}</span>
          </slot>
        </div>
      </template>
     <!-- 内容插槽 -->
     <div class="drawer-content" :style="contentStyle">
       <slot></slot>
     </div>

     <!-- 底部操作栏插槽 -->
     <div v-if="$slots.footer" class="drawer-footer">
       <slot name="footer"></slot>
     </div>
     <!-- <div v-else class="drawer-footer default-footer">
       <el-button @click="handleCancel">取消</el-button>
       <el-button type="primary" @click="handleConfirm">确定</el-button>
     </div> -->
   </el-drawer>
 </div>
</template>

<script>
export default {
 name: 'CommonDrawer',
 model: {
   prop: 'visible',
   event: 'change'
 },
 props: {
   // 控制显示
   visible: {
     type: Boolean,
     default: false
   },
   // 标题
   title: {
     type: String,
     default: '基于样版创建能力'
   },
   // 弹出方向
   direction: {
     type: String,
     default: 'btt',
     validator: (value) => ['ltr', 'rtl', 'ttb', 'btt'].includes(value)
   },
   // 尺寸（根据方向自动判断）
   size: {
     type: [String, Number],
     default: null
   },
   // 是否需要遮罩层
   showModal: {
     type: Boolean,
     default: true
   },
   // 自定义遮罩层类名
   modalClass: {
     type: String,
     default: ''
   },
   // 是否支持 ESC 关闭
   closeOnEsc: {
     type: Boolean,
     default: true
   },
   // 内容区域自定义样式
   contentStyle: {
     type: Object,
     default: () => ({})
   }
 },
 data() {
   return {
     drawerVisible: this.visible
   }
 },
 computed: {
   computedSize() {
     if (this.size) return this.size
     return ['ttb', 'btt'].includes(this.direction) ? '50%' : '30%'
   }
 },
 watch: {
   visible(newVal) {
     this.drawerVisible = newVal
     if (newVal) {
       this.lockScroll()
     } else {
       this.unlockScroll()
     }
   }
 },
 methods: {
   lockScroll() {
     document.body.style.overflow = 'hidden'
   },
   unlockScroll() {
     document.body.style.overflow = ''
   },
   handleClose() {
     this.$emit('change', false)
     this.$emit('close')
   },
   handleConfirm() {
     this.$emit('confirm')
     this.handleClose()
   },
   handleCancel() {
     this.$emit('cancel')
     this.handleClose()
   }
 }
}
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
  
  .default-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}
.drawer-footer {
 position: absolute;
 bottom: 0;
 width: 100%;
 border-top: 1px solid #e8e8e8;
 padding: 10px 16px;
 background: #fff;
 text-align: right;

 &.default-footer {
   :deep(.el-button) {
     margin-left: 10px;
   }
 }
}

// 根据方向调整间距
:deep(.el-drawer) {
 &[direction="btt"],
 &[direction="ttb"] {
   .drawer-content {
     padding-bottom: 60px;
   }
 }
}
</style>