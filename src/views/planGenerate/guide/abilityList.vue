<template>
  <div class="ability-list">
    <el-card class="box-card" shadow="never">
      <div class="planSearch">
        <div class="search">
          <hearderContainer title="">
            <template #container>
              <div class="planSearch1">
                <condition-tag label="场景分类" style="margin-bottom: 12px;" ref="conditionTags" uniqueId="1" :checkedTypeTags="checkedTypeTags"
                  @selecType="selectType" :sence-type-list="senceTypeList"></condition-tag>
                <condition-tag :model="false" Icon="el-icon-price-tag" label="标签" ref="conditionTag" uniqueId="2"
                  :checkedTypeTags="checkedTags" @selecType="selectTag" :sence-type-list="tableData.tags">
                </condition-tag>
              </div>
            </template>
            <template #footer>
              <div class="flex_bt">
                <div class="btns">
                <span
                  v-for="(item, index) in tableTypeOptions"
                  :key="index"
                  :class="['btns-item', tableData.process_status === item.value ? 'active' : '']"
                  @click="changeProcessStatus(item.value)"
                >
                  {{ item.label }}
                </span>
              </div>

              <div style="display: flex; justify-content: flex-end; align-items: center;">
                <div class="footer-search">
                  <div class="quick-search">
                        <el-checkbox-group v-model="selectedFilters" @change="handleFilterChange">
                          <el-checkbox label="myCollect">我的收藏</el-checkbox>
                          <el-checkbox label="myAbility">我的沉淀</el-checkbox>
                        </el-checkbox-group>
                      </div>
                  <el-popover v-model="showCustomSearch" popper-class="search" placement="bottom-end" trigger="click"
                    :width="400">
                    <div>
                      <el-form ref="form" :model="formData" label-position="left" label-width="84px"
                        style="max-width: 100%; padding: 8px 16px;">
                        <el-form-item label="使用场景：">
                          <el-cascader v-model="formData.agent_scene" show-all-levels clearable empty-text="暂无数据"
                            :props="props" style="width: 100%">
                            <template #empty> 暂无数据 </template>
                          </el-cascader>
                        </el-form-item>
                        <!-- <el-form-item label="发布状态：">
                          <el-select v-model="formData.process_status" clearable placeholder="请选择状态" style="width: 100%">
                            <el-option v-for="item in statusFbList" :key="item.value" :label="item.name"
                              :value="item.value" />
                          </el-select>
                        </el-form-item> -->
                        <el-form-item label="创建人：">
                          <el-select v-model="formData.createUserId" placeholder="创建人" :remote-method="searchUser"
                            clearable filterable remote style="width: 100%">
                            <el-option v-for="item in userList" :key="item.user_id"
                              :label="`${item.nickName}(${item.username})`" :value="item.user_id" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="贡献专家：">
                          <el-select v-model="formData.contributors" placeholder="请选择贡献专家" :remote-method="searchUser"
                            clearable filterable remote style="width: 100%">
                            <el-option v-for="item in userList" :key="item.user_id"
                              :label="`${item.nickName}(${item.username})`" :value="item.user_id" />
                          </el-select>
                        </el-form-item>
                        <el-form-item>
                          <div class="w-full flex justify-end">
                            <el-button class="button-last" type="primary" @click="handlSearch">查询</el-button>
                            <el-button class="button-last" type="info" @click="handleReset">重置</el-button>
                            <el-button @click="showCustomSearch = false">取消</el-button>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                    <template #reference>
                      <el-button slot="reference" class="button-last" type="text">高级搜索</el-button>
                    </template>
                  </el-popover>
                  <el-input v-model.trim="formData.name" clearable placeholder="请输入名称">
                    <template #append>
                      <el-button class="search-button" @click="handlSearch"
                        icon="el-icon-search"></el-button>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
            </template>
          </hearderContainer>
        </div>
      </div>
    </el-card>
    <div class="containerCardSmall">
      <div v-loading="isLoading" :class="{ 'left-content': true, 'left-content-max': turnFull }">
        <div class="main">
      <div class="tab-content planContainer">
        <div class="container containerCard">
          <div class="cartItem build" :class="turnFull ? 'container-full' : 'container-nofull'">
            <!-- <img src="https://res.ennew.com/image/png/9fbe3742cd7d5c194f0bcf981611a4b0.png" />
            <span>{{ '新建' }}</span> -->
            <div class="create-container">
              <!-- <div class="create-ability">创建能力</div> -->
              <div class="create-methods">
              <div class="create-by-scene" @click="handleIntoBuild"><i class="el-icon-plus"></i>基于场景创建能力</div>
                <div class="create-by-template" @click="handleTemplate"><i class="el-icon-plus"></i>基于样版创建能力</div> 
            </div>
            </div>
          </div>
          <div v-for="(item, index) in tableData.list" :key="item.id_index" class="cartItem" :class="turnFull ? 'cartItem-full' : 'cartItem-nofull'">
            <div class="cartItemBox">
              <div class="itemHeader">
                <div class="itemHeaderTop">
                  <el-popover placement="top-start" trigger="hover" width="240">
                    <div>
                      <div style="display: flex; align-items: center; margin-bottom: 8px">
                        <div style="color: #646566">进度：</div>
                        <div style="flex: 1; max-width: calc(100% - 110px); color: #323233">
                          {{ statusMap(item.agent_scene_code, item.status) }}
                        </div>
                      </div>
                      <div style="display: flex; align-items: center; margin-bottom: 8px">
                        <div style="color: #646566">贡献专家：</div>
                        <div style="flex: 1; max-width: calc(100% - 70px)">
                          <selectItem class="contribution" :array-items="selectUserFn(item.contributors)"
                            :max-length="1">
                          </selectItem>
                        </div>
                      </div>
                      <div style="display: flex; align-items: center">
                        <div style="color: #646566">创建时间：</div>
                        <div style="flex: 1; max-width: calc(100% - 70px); color: #323233">
                          {{ item.update_time || item.create_time }}
                        </div>
                      </div>
                    </div>
                    <i slot="reference" class="el-icon-warning-outline" />
                  </el-popover>
                  <div class="itemHeaderTit" @click="handleGotoDetail(item)">
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                      <div class="title">{{ item.name }}</div>
                    </el-tooltip>
                  </div>
                  <div v-if="$route.name === 'planGenerateFirst'" class="itemHeaderBtn">
                    <el-dropdown style="margin-left: 10px" @command="(type) => handleCommand(type, item)">
                      <el-button type="text" size="mini"><img
                          src="@/assets/images/planGenerater/more.png" /></el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :disabled="
                              (item.visibility === 'private' &&
                                item.username !== userInfo.username &&
                                !isAdmin) ||
                              (item.visibility === 'share' && !item.is_shared && !isAdmin)
                            " command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item v-if="!item.is_publish"
                          :disabled="item.username !== userInfo.username && !isAdmin"
                          command="remove">删除</el-dropdown-item>
                        <el-dropdown-item v-if="item.agent_scene_code === 'digital_twin_assistant_scene'"
                          :disabled="item.username !== userInfo.username && !isAdmin"
                          command="develop">请求平台技术支持</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
                <div style="display: flex; align-items: center; margin-top: 8px">
                  <el-tooltip effect="dark" :content="item.nickName + '(' + item.username + ')'" placement="top">
                    <span class="avator">{{ item.nickName?.slice(-2) }}</span>
                  </el-tooltip>
                  <div class="separate"></div>
                  <div class="sence-tag">ID:{{ item.id }}</div>
                  <div style="margin-left: 8px">
                    <Status :text="statusTypeMap[item.publish_status]?.text"
                      :bg-color="statusTypeMap[item.publish_status]?.bgColor"
                      :dot-color="statusTypeMap[item.publish_status]?.dotColor" />
                  </div>
                  <div style="margin-left: 8px" v-if="item.sort == 1" class="sence-tag">置顶</div>
                </div>
              </div>
              <div class="itemContent">
                <div v-if="$route.name !== 'planGenerateFirst'" class="contentDesc">
                  <div class="descLabel">空间名称：</div>
                  <div class="descValue">
                    {{ item.workspaceName || '--' }}
                  </div>
                </div>
                <div class="contentDesc">
                  <div class="descLabel">使用场景：</div>
                  <div class="descValue">
                    <el-tooltip class="item" effect="dark" :content="
                          item.agent_scene_code_name
                            ? item.agent_scene_code_name +
                              (item.agent_scene_name ? '/' + item.agent_scene_name : '')
                            : item.agent_scene_name
                        " placement="top">
                      <div class="descValueMax">
                        {{
                        item.agent_scene_code_name
                        ? item.agent_scene_code_name +
                        (item.agent_scene_name ? '/' + item.agent_scene_name : '')
                        : item.agent_scene_name
                        }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div class="contentDesc">
                  <div class="descLabel">描述：</div>
                  <div class="descValue">
                    <el-tooltip class="item" effect="dark" :content="item.description ? item.description : '--'"
                      placement="top">
                      <div class="descValueMax">
                        {{ item.description || '--' }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div v-if="item.tag && item.tag.length" class="contentDesc">
                  <div class="descValue" style="max-width: calc(100%); flex: 1">
                    <selectItem :array-items="
                          item.tag?.map((tag) => {
                            return { key: tag.id, label: tag.name }
                          })
                        " :max-length="2" @clickFun="handleTagClick"></selectItem>
                  </div>
                </div>
              </div>
            </div>
            <div class="itemFooter">
              <div class="footerLeft">
                <div class="item">
                  <el-tooltip effect="dark" content="收藏量" placement="top">
                    <img style="cursor: pointer" :src="
                          item.is_collected
                            ? require('@/assets/images/planGenerater/uncollect.png')
                            : require('@/assets/images/planGenerater/collect.png')
                        " @click="collectDebounce(item)" />
                  </el-tooltip>
                  <span>{{ item.collector || 0 }}</span>
                </div>
                <div class="item">
                  <el-tooltip effect="dark" content="调用量" placement="top">
                    <img style="cursor: pointer" :src="require('@/assets/images/planGenerater/callNum2.png')" />
                  </el-tooltip>
                  <span>{{ item.callNum || 0 }}</span>
                </div>
                <div class="item">
                  <el-tooltip effect="dark" content="浏览量" placement="top">
                    <img style="cursor: pointer" :src="require('@/assets/images/planGenerater/preview.png')" />
                  </el-tooltip>
                  <span>{{ item.view || 0 }}</span>
                </div>
              </div>
              <div>
                <el-button type="default" :disabled="
                      (item.visibility === 'private' && item.username !== userInfo.username && !isAdmin) ||
                      (item.visibility === 'share' && !item.is_shared && !isAdmin) "
                      @click="handleGotoDetail(item)">
                  开始生产
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页部分 -->
        <div style="text-align: right; padding-top: 16px; padding-bottom: 16px; background-color: #fff;">
          <el-pagination class="new-paper" layout="prev, pager, next, sizes, jumper" :page-sizes="[12, 24, 36, 48, 60]"
            :current-page.sync="tableData.page" :page-size="tableData.pageSize" :total="tableData.total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
       <div class="icon-content" @click="handleChangeSize">
          <i class="el-icon-caret-right" v-if="!turnFull"></i>
          <i class="el-icon-caret-left" v-else></i>
        </div>
    </div>
      </div>
      <div :class="{ 'right-content': true, 'right-content-min': turnFull }">
        <RoleInfo
          iconSelect="schema"
        ></RoleInfo>
      </div>
    </div>

    <devlopModal :is-visible="devlopModalVisable" :cur-id="curId" :dev-person="devPersonInfo" @close="handleClose" />
    <creat-template v-model="showDrawer" size="90%" modalClass="drawer-template">
      <drawer-list></drawer-list>
    </creat-template>
  </div>
</template>

<script>
// import drawerList from '../sampleAbility/index.vue';
import drawerList from './drawerList.vue';
import creatTemplate from './template.vue';
import RoleInfo from './roleInfo.vue';
import conditionTag from '@/components/conditionTag/index.vue'
 import hearderContainer from '@/components/hearderContainer/index.vue'
import { mapGetters, mapState, mapMutations } from 'vuex';
import { Message } from 'element-ui'
import {
  deleteRel,
  DeleteScheme,
  queryCallCountStatistic,
  expertTagsWithCount,
  saveRel,
  SchemeList,
  schemeListCount,
  updateSchemeSort,
  queryDictConfigNew,
  getSencetVisibleList,
  getUserInfo,
  SchemeStatusList,
  agentSenceList
} from '@/api/planGenerateApi.js'
import selectItem from '@/views/planGenerate/selectItem.vue'
import Status from '@/components/Status/index.vue'
import { debounce } from 'lodash'
import devlopModal from '@/views/planGenerate/wlls/devlopModal.vue'
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {}
  const wid = localStorage.getItem('currentWorkSpace')
  ? JSON.parse(localStorage.getItem('currentWorkSpace')).workspaceId
  : ''
export default {
  components: { devlopModal, Status, selectItem,conditionTag,
    hearderContainer,RoleInfo,creatTemplate,drawerList },
  data() {
    return {
      showDrawer: false,
      isLoading: true,
      turnFull: true,
      statusList: [],
        tableTypeOptions: [
        {
          label: '全部',
          value: '0'
        },
        {
          label: '研发中',
          value: '1'
        },
        {
          label: '待验证',
          value: '2'
        },
        {
          label: '已完成',
          value: '3'
        }
      ],
      userList: [],
      formData: {
        status: '',
        tags: [],
        agent_scene: [],
        name: '',
        createUserId: '',
        contributors: ''
      },
      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node
          if (level === 0) {
            queryDictConfigNew({  "dev_mode": "scheme" }).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                const result = res.data.result?.map((item) => {
                  return {
                    value: item.code,
                    label: item.name
                  }
                })
                resolve(result || [])
              } else {
                Message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
          } else {
            getSencetVisibleList({
              keyword: '',
              user_id: userInfo.userId,
              scene_type: node.data.value,
              workspace_id: wid
            })
              .then((res) => {
                const result = res.data?.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    leaf: true
                  }
                })
                resolve(result || [])
              })
              .catch(() => {
                resolve([])
              })
          }
        }
      },
      checkedTypeTags: [],
      senceTypeList: [],
      showCustomSearch: false,
      checkedTags: [], // 筛选选择的标签
      switchValue: 'AIChat',
      workspaceId: 1,
      canCompeleted: false,
      searchVal: '',
      showCardList: false,
      devlopModalVisable: false,
      activeName: 'first',
      title: '安全专家',
      devPersonInfo: {},
      curId: '',
      tableData: {
        process_status: '0',
        myCollect: '0',
        user_id: '',
        tags: [],
        list: [], // 表格数据
        page: 1,
        pageSize: 12,
        total: 0,
        allist: [],
        allTotal: 0
      },
      sceneList: [],
      checkedList: [],
      statusTypeMap: {
        offline: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
        online: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' },
        un_publish: { bgColor: '#ebeffa', dotColor: '#4068d4', text: '未发布' }
      },
      selectedFilters: [] // 选中的过滤条件
    }
  },
  watch: {
    searchVal: {
      handler(nv) {
        this.queryTableData()
      }
    },
    selectedFilters: {
      handler() {
        this.queryTableData()
      },
      deep: true
    },
    $route: {
      deep:true,
      immediate: true, // 组件加载时触发
      async handler(newRoute,oldRoute) {
        console.log('newrouter-----------',newRoute)
        if(newRoute.query.workspaceId != oldRoute.query.workspaceId){
          this.workspaceId = newRoute.query.workspaceId 
          this.queryTagsWithCount()
          this.queryTableData()
          await this.queryStatusData('')
          this.querySceneList()
        }
      }
    }
  },
//   created() {
//   window.addEventListener('message', this.handleParentMessage);
// },
// beforeDestroy() {
//   window.removeEventListener('message', this.handleParentMessage);
// },
  // created() {
  //  console.log('mounted333334444444444',this.$route)
  //  if (window.top !== window.self) {
  //   console.log('mounted333334444444444')
  //   window.addEventListener('message', (event) => {
  //     try {
  //       const datajson = JSON.parse(event.data)
  //       this.setEmbed(datajson.embed)
  //       console.log('顶部收到空间信息2222', event.data);
  //     } catch (error) {

  //     }
  //   });
  // }
  // },
 async mounted() {
  // if (window.top !== window.self) {
  //   window.addEventListener('message', (event) => {
  //     try {
  //       const datajson = JSON.parse(event.data)
  //       this.setEmbed(datajson.embed)
  //       console.log('顶部收到空间信息', event.data, datajson.embed);
  //     } catch (error) {

  //     }
  //   });
  // }
    this.workspaceId = Number(this.$route.query.workspaceId)
    this.queryTagsWithCount()
    // this.queryTableData()
    await this.queryStatusData('')
    this.querySceneList()
  },
  computed: {
   ...mapState('operations', ['embed']),
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    }),
  },
  methods: {
   ...mapMutations('operations',['setEmbed']),
   ...mapMutations('maco', ['setTreeData']),
  //  handleParentMessage(event) {
  //   if (event.data.type === 'parentRouteUpdate') {
  //     // 同步父级路由参数
  //     this.$router.replace({
  //       query: event.data.query
  //     }).catch(()=>{});
  //   }
  // },
    handleChangeSize() {
      this.turnFull = !this.turnFull
    },
    handleReset() {
      this.tableData.page = 1
      this.justMine = 0
      this.formData.status = ''
      this.formData.agent_scene = ''
      this.formData.name = ''
      this.formData.createUserId = ''
      this.formData.contributors = ''
      this.formData.tags = []
      this.formData.workspaceId = ''
      this.checkedTags = []
      this.checkedTypeTags = []
      this.queryTableData();
    },
    querySceneList() {
      queryDictConfigNew({  "dev_mode": "scheme" }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.senceTypeList = res.data.result|| [];
          this.$nextTick(()=> {
            this.$refs.conditionTags?.observeResize();
          })
        } else {
          this.senceTypeList = []
        }
      })
    },
    async queryStatusData(scene) {
      this.tableLoading = true
      await SchemeStatusList({ scene: scene }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.statusList = res.data.result?.items || []
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
      await agentSenceList({ business_type: 'scene_type' }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.sceneList = res.data.result || []
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
      await this.queryTableData(this.justMine)
    },
    searchUser(userName, callback) {
      getUserInfo({ nickName: userName }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.userList = res.data.result
        }
      })
    },
    handlSearch() {
      this.tableData.page = 1
      this.queryTableData();
    },
    selectType(code) {
      if(Object.prototype.toString.call(code) === '[object Object]') {
        if(code.code) {
          code = code.code
        }else if(code.id) {
          code = code.id
        }
      }
      // 删除
      if (this.checkedTypeTags.indexOf(code) > -1) {
        this.checkedTypeTags = []
      } else {
        // 增加
        this.checkedTypeTags = [code]
      }
      this.tableData.page = 1
      this.queryTableData();
    },
    selectTag(tag) {
      if(Object.prototype.toString.call(tag) === '[object Object]') {
        if(tag.code) {
          tag = tag.code
        }else if(tag.id) {
          tag = tag.id
        }
      }
      // 删除
      if (this.checkedTags.indexOf(tag) > -1) {
        const temp = []
        this.checkedTags.forEach((item) => {
          if (item != tag) {
            temp.push(item)
          }
        })
        this.checkedTags = temp
      } else {
        // 增加
        this.checkedTags.push(tag)
      }
      this.tableData.page = 1
      this.queryTableData();
    },
    handleFilterChange() {
      // this.queryTableData()
    },
    handleIntoBuild() {
      this.$router.push({
        path: '/planGenerate/partnerCreate',
        query: {
          workspaceId: this.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      })
    },
    handleTemplate() {
      this.showDrawer = true
      console.log('handleTemplate')
    },
    queryTagsWithCount() {
      this.tableLoading = true
      expertTagsWithCount({ keyword: '' })
        .then((res) => {
          this.tableLoading = false
          // this.tableData.tags = [{ id: 0, name: '全部' }]
          if (res.data) {
            const results = res.data
            this.tableData.tags.push(...results)
            this.activeName = this.tableData.tags.length > 0 ?  this.tableData.tags[0].name : ''
            this.$nextTick(()=> {
            this.$refs.conditionTag?.observeResize();
          })
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    changeProcessStatus(val) {
      this.tableData.process_status = val
      this.tableData.page = 1
      this.queryTableData()
      // this.queryTableCount()
    },
    queryTableData() {
      this.isLoading = true
      const tags = []
      if (this.activeName) {
        const item = this.tableData.tags.find((tag) => tag.name === this.activeName)
        const tagId = item?.id
        if (tagId && tagId !== 0) {
          tags.push(tagId)
        }
      }
      const param = {
        offset: this.tableData.page,
        limit: this.tableData.pageSize,
        sort_field: 'create_time',
        order: 'desc',
        status: this.formData.status,
        agent_scene: this.formData.agent_scene[1] || '',
        name: this.formData.name,
        agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
        // 我的方案
        user_id: this.formData.createUserId !== '' ? this.formData.createUserId : (userInfo.userId || ''),
        tag_ids: this.checkedTags,
        contributors: this.formData.contributors,
        // 我的收藏
        myCollect: this.tableData.myCollect,
        process_status: this.tableData.process_status,
        is_expert_scene:1,
        dev_mode:'scheme'
      }
      if (this.selectedFilters.includes('myAbility')) {
        this.tableData.user_id = userInfo.userId
        // param.user_id = userInfo.userId
      } else {
        if (!this.formData?.createUserId) {
          this.tableData.user_id = ''
          param.user_id = ''
        }
      }
      if (this.selectedFilters.includes('myCollect')) {
        this.tableData.myCollect = '1'
        param.myCollect = '1'
      } else {
        this.tableData.myCollect = '0'
        param.myCollect = '0'
      }
      SchemeList(param, { workspaceId: this.workspaceId })
        .then(async (res) => {
          const { data = {}, status } = res || {}
          if (status === 200 && data.code === 200) {
            const list = data.result?.items || []
            // list[0].agent_scene_code ='digital_twin_assistant_scene'
            const schemeIds = list?.map((item) => item.id)
            let results = {}
            let statisticsData = {}
            results = await queryCallCountStatistic({ schemeIds })
            statisticsData = results?.data?.data || {}
            this.tableData.list = list?.map((item) => ({
              ...item,
              callNum: statisticsData[item.id] || 0
            }))
            this.tableData.total = data.result.total
            this.$nextTick(()=> {
              this.isLoading = false
            })
          } else {
            this.tableData.list = []
            this.tableData.total = 0
            this.isLoading = false
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.tableData.list = []
          this.tableData.total = 0
          const { statusText = '请求异常' } = _err.request || _err
          this.$message({
              type: 'error',
              message: statusText || '接口异常!'
            })
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleSizeChange() {
      this.queryTableData()
    },
    handleCurrentChange() {
      this.queryTableData()
    },
    handleTabClick() {
      this.tableData.page = 1
      this.queryTableData()
    },
    saveRelHandle(id, status, name, actionName) {
      saveRel({
        biz_id: id,
        biz_type: status
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          if (status === 'collector') {
            this.$message({
              type: 'success',
              message: res.data?.msg || '收藏成功'
            })
          }
          this.queryTableData(this.justMine)
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleGotoDetail(item) {
      this.setTreeData([])
      const sceneCode = item.agent_scene_code
      if (
        (item.visibility === 'private' && item.username !== userInfo.username && !this.isAdmin) ||
        (item.visibility === 'share' && !item.is_shared && !this.isAdmin)
      ) {
        this.$message({
          type: 'warning',
          message: '没有权限查看！'
        })
      } else {
       
        if (sceneCode) {
         let path = '/planGenerate/ConfTaskPlanchat';
  if (sceneCode === 'other_assistant_scene') {
    path = '/planGenerate/planchat';
  } else if (['digital_twin_assistant_scene'].includes(sceneCode)) {
    path = '/planGenerate/partnerchat';
  }

  // 统一通过postMessage进行路由跳转
  if (window.parent !== window.self) {
    window.parent.postMessage({
      type: 'routeChange',
      path,
      query: {
        ...this.$route.query,
        id: item.id,
        status: item.status,
        fromMenu: '1'
      },
    }, '*');
  } 
  // else {
    // 非嵌入情况直接跳转
    this.$router.push({
      path,
      query: {
        ...this.$route.query,
        id: item.id,
        status: item.status,
        fromMenu: '1',
        _t333333: Date.now()
      }
    });
  // }
         
        } else {
          this.$message({
            type: 'warning',
            message: '当前场景已删除！'
          })
        }
      }
    },
    statusMap(code, status) {
      if (
        [
          'device_ops_assistant_scene',
          'device_ops_assistant_scene',
          'artificial_handle_scene',
          'visit_leader_cognition_scene',
          'rule_generation_scene',
          'intelligent_conversation_scene',
          'sop_scene',
          'custom_cognition_assistant_scene'
        ].indexOf(code) > -1
      ) {
        if (status === 1) {
          return '生成方案'
        } else if (status === 2) {
          if (code === 'custom_cognition_assistant_scene') {
            return '思维图生成'
          } else {
            return '思维树生成'
          }
        } else if (status === 3) {
          return '多模态数据对齐'
        } else if (status === 4) {
          return '能力代码生成'
        } else if (status === 5) {
          return '智能能力测试与迭代'
        } else {
          return '--'
        }
      } else if (code === 'other_assistant_scene') {
        if (status === 1) {
          return '生成方案'
        } else {
          return '--'
        }
      } else if (code === 'digital_twin_assistant_scene') {
        // 针对数字孪生
        return status === 1
          ? '生成方案'
          : status === 2
          ? '执行任务'
          : status === 3
          ? '智能能力测试与迭代'
          : '--'
      } else if (code === 'operational_optimization_scene') {
        // 针对数学模型生成
        return status === 1
          ? '生成方案'
          : status >= 2 && status <= 6
          ? '执行任务'
          : status === 7
          ? '智能能力测试与迭代'
          : '--'
      } else {
        if (status === 1) {
          return '生成方案'
        } else if (status === 2) {
          return '生成任务'
        } else if (status === 3) {
          return '执行任务'
        } else {
          return '--'
        }
      }
    },
    collectDebounce: debounce(function (item) {
      this.collectHandle(item)
    }, 1000),
    async collectHandle(item) {
      if (this.loading) {
        return false
      }
      this.loading = true
      if (!item.is_collected) {
        await this.saveRelHandle(item.id, 'collector')
        this.loading = false
      } else {
        await deleteRel({
          biz_id: item.id,
          biz_type: 'collector'
        })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.$message({
                type: 'success',
                message: res.data?.msg || '取消收藏'
              })
              this.queryTableData(this.justMine)
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    selectUserFn(data) {
      let formattedUsers = []
      if (data?.length > 0) {
        formattedUsers = data.map((user, index) => {
          const label = `${user.nickname}(${user.loginName})`
          return { key: index + 1, label }
        })
      }
      return formattedUsers
    },
    async handleCommand(type, item) {
      if (type === 'task') {
        this.$router.push({
          path: '/planGenerate/task',
          query: {
            ...this.$route.query,
            name: item.name,
            id: item.id,
            agent_scene_name: item.agent_scene_name,
            create_time: item.create_time,
            desc: item.description,
            fromMenu:'1',
            agent_scene_code_name: item.agent_scene_code_name
          }
        })
      } else if (type === 'edit') {
        this.editId = item.id
        this.editData = item
        this.$router.push({
          path: '/planGenerate/edit',
          query: { ...this.$route.query, id: item.id, fromMenu: '1', }
        })
      } else if (type === 'remove') {
        this.handleDelete(item)
      } else if (type === 'develop') {
        this.devPersonInfo = item.developer
        this.curId = item.id + ''
        this.devlopModalVisable = true
      } else if (type === 'top') {
        this.tableLoading = true
        await updateSchemeSort(item.id, 1, { workspaceId: this.formData.workspaceId })
        this.queryTableData(this.justMine)
      } else if (type === 'cancelTop') {
        this.tableLoading = true
        await updateSchemeSort(item.id, 0, { workspaceId: this.formData.workspaceId })
        this.queryTableData(this.justMine)
      }
    },
    // async checkInto(item) {
    //   if (
    //     (item.visibility === 'private' && item.username !== userInfo.username && !this.isAdmin) ||
    //     (item.visibility === 'share' && !item.is_shared && !this.isAdmin)
    //   ) {
    //     this.$message({
    //       type: 'warning',
    //       message: '没有权限查看！'
    //     })
    //   } else {
    //     if (item.agent_scene_code) {
    //       if (item.agent_scene_code === 'digital_twin_assistant_scene') {
    //         this.saveRelHandle(item.id, 'view', item.name, '专家进入')
    //         this.$router.push({
    //           path: '/planGenerate/wllsExpertPlanchat',
    //           query: {
    //             ...this.$route.query,
    //             status: item.status,
    //             id: item.id,
    //             enterType: 'expert',
    //             ability_id: item?.ability_id
    //           }
    //         })
    //       } else {
    //         if (item.agent_scene_code === 'operational_optimization_scene') {
    //           this.saveRelHandle(item.id, 'view', item.name, '开始')
    //           this.$router.push({
    //             path: '/planGenerate/hqwPlanchat',
    //             query: {
    //               ...this.$route.query,
    //               id: item.id,
    //               status: item.status,
    //               ability_id: item?.ability_id
    //             }
    //           })
    //         } else {
    //           this.saveRelHandle(item.id, 'view', item.name, '开始')
    //           this.$router.push({
    //             path: '/planGenerate/planchat',
    //             query: {
    //               ...this.$route.query,
    //               id: item.id,
    //               status: item.status,
    //               ability_id: item?.ability_id
    //             }
    //           })
    //         }
    //       }
    //     } else {
    //       this.$message({
    //         type: 'warning',
    //         message: '当前场景已删除！'
    //       })
    //     }
    //   }
    // },
    handleTagClick(tag) {
      this.tableData.tags = [tag]
      this.queryTagsWithCount()
      this.queryTableData()
    },
    developFn(item) {
      this.saveRelHandle(item.id, 'view', item.name, '研发进入')
      this.$router.push({
        path: '/planGenerate/wllsDevPlanchat',
        query: {
          ...this.$route.query,
          status: item.status,
          id: item.id,
          fromMenu:'1',
          enterType: 'develop',
          ability_id: item?.ability_id
        }
      })
    },
    expertFn(item) {
      this.saveRelHandle(item.id, 'view', item.name, '专家进入')
      this.$router.push({
        // path: '/planGenerate/wllsExpertPlanchat',
        path: '/planGenerate/ConfTaskPlanchat',
        query: {
          ...this.$route.query,
          status: item.status,
          id: item.id,
          fromMenu: '1',
          enterType: 'expert',
          ability_id: item?.ability_id
        }
      })
    },
    handleClose(val) {
      if (val) {
        this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'success'
        })
          .then(() => {
            this.queryTableData(this.justMine)
            // this.queryTableCount()
          })
          .catch(() => {
            this.queryTableData(this.justMine)
            // this.queryTableCount()
          })
      }
      this.devlopModalVisable = false
    },
    handleDelete(row) {
      this.$confirm('此操作将删除该方案及任务，是否继续?', '删除', {
        customClass: 'last-dialog',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          DeleteScheme({ id: row.id })
            .then((res) => {
              this.tableLoading = false
              if (res.status === 200 && res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.queryTableData()
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
            .finally(() => {
              this.tableLoading = false
            })
        })
        .catch(() => {
          this.tableLoading = false
          // this.queryTableData()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 16px;
  border: none;
  overflow: unset;
  :deep(.el-card__body) {
    padding: 16px 0px 0px 0px;
  }
  .planSearch {
    background-color: #fff;
    // margin-bottom: 16px;
    // padding: 0px 20px 0px;
    position: relative;
    .quick-search {
      display: flex;
      :deep(.el-checkbox-group) {
        display: flex;

        align-items: center;
        margin-right: 24px;
      }
    }
   .search {
    .planSearch1 {
      padding: 0 16px;
    }
    .footer-search {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-input {
      width: 65%;
      margin: 5px 0px 5px 0px;
    }
   }
   }
    .searchIcon {
      position: absolute;
      left: 50%;
      bottom: -10px;
      transform: translateX(-50%);

      .searchIconTaggle {
        width: 40px;
        height: 20px;
        background: #d9e6f8;
        border-radius: 2px;
        position: relative;
        border: 1px solid #f2f3f5;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4068d4;
        cursor: pointer;

        &:hover {
          background: #a1bbef;
        }
      }
    }

    .headerTitle {
      font-weight: bold;
      color: #323233;
      line-height: 26px;
      font-size: 18px;
      padding: 14px 20px;
    }

    .button-last {
      line-height: 14px;
    }

    .search {
      position: relative;
      overflow: hidden;
    }
   }
}

::v-deep .el-card__header {
  border: none;
  padding: 16px 0px 0px;
}

::v-deep .el-cascader {
  line-height: 30px !important;
  height: 30px;

  .el-input {
    height: 30px;
  }
}

::v-deep .el-tooltip__popper {
  &.is-dark {
    background: rgba($color: #323233, $alpha: 0.8) !important;
  }
}

::v-deep .el-button.is-disabled {
  color: rgba($color: #4068d4, $alpha: 0.4);
}

::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  display: flex;
  align-items: center;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 8px;
  border-radius: 2px;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 7px 6px !important;

  img {
    height: 16px;
    margin-top: -2px;
  }
}

:deep(.el-input__inner) {
  border-radius: 2px;
  height: 30px;
  line-height: 30px;
}
.ability-list {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .containerCardSmall {
    display: flex;
    flex: 1;
    overflow: hidden;
    .containerCard {
      gap: 12px;
    }
    .left-content {
      // background-color: #fff;
      width: 70%;
      min-width: 250px;
      height: 100%;
      overflow-y: auto;
      border-radius: 4px;

      .scene-container {
        width: 100%;
      }
      .show {
        height: 100%;
      }
    }
    .left-content-max {
      width: 100%;
    }
    .right-content {
      padding: 24px 16px 0px 16px;
      background-color: #fff;
      width: 30%;
      // min-width: 450px;
      // height: 100%;
      overflow: auto;
      border-radius: 4px;

      .user {
        display: flex;
        justify-content: flex-start;
        padding: 24px 16px;
        border-bottom: 1px solid #ebecf0;

        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #323233;
          text-align: left;
          font-style: normal;
        }

        img {
          width: 80px;
          height: 80px;
        }

        .user-info {
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;

          .title {
            display: flex;
            align-items: center;

            span {
              background: #e6ecff;
              border-radius: 2px;
              padding: 0 8px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #3455ad;
            }

            i {
              margin-left: 10px;
              cursor: pointer;
              font-size: 14px;
            }
          }
        }
      }

      .knowledge-benefits,
      .knowledge-accumulation {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 22px 16px 0;
        border-bottom: 1px solid #ebecf0;

        .title {
          img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
          span {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
          }
        }

        .info {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 20px 16px;

          .info-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            span {
              font-family: PingFangSC, PingFang SC;
              font-weight: 600;
              font-size: 24px;
              color: #323233;
              line-height: 33px;
              text-align: right;
              font-style: normal;
            }

            .value {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #646566;
            }
          }
        }
      }

      .knowledge-accumulation {
        .info {
          flex-wrap: wrap;
          padding: 20px 0px;
          padding-bottom: 0;

          .info-item {
            width: 33%;
            margin-bottom: 20px;
          }
        }
      }

      .buttons {
        display: flex;
        justify-content: space-between;
        padding: 24px 16px;

        .my-plan,
        .my-collect {
          cursor: pointer;
          display: flex;
          align-items: center;
          padding: 16px 16px 16px 15px;
          width: 162px;
          background: #ebeffa;

          img {
            width: 18px;
            height: 18px;
            margin-right: 7px;
          }
        }

        .my-collect {
          background: linear-gradient(132deg, #fffbd3 0%, #ffe9d9 100%), #fff2eb;
        }
      }
    }
    .right-content-min {
      width: 0px !important;
      margin: 0;
      padding: 0;
    }
  }
  .main {
    height: 100%;
    display: flex;
    overflow: hidden;
    .icon-content {
        cursor: pointer;
        display: flex;
        align-items: center;
        i {
          font-size: 20px;
        }
      }
      .icon-content-card {
        right: 0;
      }
  }
  .search-content {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: space-between;
    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #323233;
      font-style: normal;
    }

    .input-with-select {
      width: 40%;
    }
  }
  .right-content {
    display: flex;
    align-items: center;
  }
  .tab-content {
    flex: 1;
    background-color: #fff;
    // height: calc(100% - 96px);
    // max-height: calc(100vh - 275px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .tabs-list {
      :deep(.el-tabs__nav-wrap) {
        display: flex;
        align-items: center;
      }
      :deep(.el-tabs__nav-wrap)::after,
      :deep(.el-tabs__active-bar) {
        display: none;
      }
      :deep(.el-tabs__item) {
        border-radius: 2px;
        height: auto;
        line-height: 20px;
        padding: 5px 16px;
        margin-right: 8px;
      }
      :deep(.is-active) {
        background: #eff3ff;
      }
    }
        .container {
          overflow-y: auto;
          display: flex;
          padding: 0px 20px;
          flex-wrap: wrap;
          width: 100%;
          flex: 1;
          justify-content: flex-start;
          align-content: flex-start;
          .container-item {
            display: flex;
            flex-direction: column;
            // flex-basis: calc(33.3333% - 15px);
            border: 1px solid #dcdde0;
            border-radius: 4px;
            padding: 16px;
            justify-content: space-between;
            transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease; // 添加过渡效果
            background-color: #fff;
            &:hover {
              box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); // 更柔和的阴影效果
              border-color: #4068d4;
            }
            span {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #646566;
              text-align: left;
              font-style: normal;
            }
            .title {
              font-weight: 500;
              font-size: 18px;
              color: #1d2129;
              font-style: normal;
              cursor: pointer;
              padding: 0 12px;
              transition: color 0.3s ease; // 添加过渡效果
              &:hover {
                color: #4068d4; // 悬浮时的颜色
              }
            }

            .desc {
              margin: 8px 0;
              color: #323233;
              padding: 0 12px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all; // 确保长单词或URL不会破坏布局
            }

            .info {
              display: flex;
              flex-direction: column;
              border-bottom: 1px solid #ebecf0;
              padding: 0 12px;

              .info-item {
                margin-bottom: 8px;

                img {
                  height: 16px;
                  width: 16px;
                  margin-right: 4px;
                }

                span {
                  font-size: 14px;
                  color: #646566;
                }
              }
            }

            .icons {
              display: flex;
              justify-content: space-between;
              padding: 0 12px;

              .icons-item {
                display: flex;
                align-items: center;
                margin-top: 8px;

                img {
                  height: 16px;
                  width: 16px;
                  margin-right: 4px;
                }
              }
            }
          }
          .container-full {
            flex-basis: calc((100% - 3 * 12px) / 4);
            flex-shrink: 0; /* 禁止压缩 */
          }
          .container-nofull {
            flex-basis: calc(33.3333% - 15px);
          }
          .create-container {
            display: flex;
            justify-content: center;
            padding: 60px 16px;
            height: 100%;
            min-height: 180px;
          }
          .build {
            min-height: 100px;
            .create {
              &-ability {}
              &-methods {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
              }
              &-by-scene {
                color: #4068d4;
                cursor: pointer;
                height: 22px;
                line-height: 22px;
                // margin-bottom: 16px;
                i {
                 margin-right: 6px;
                }
              }
              &-by-scene:hover,&-by-template:hover {
               color: #264480;
              }
              &-by-template {
                color: #4068d4;
                cursor: pointer;
                height: 22px;
                line-height: 22px;
                i {
                 margin-right: 6px;
                }
              }
            }
            img {
              width: 60px;
              height: 60px;
            }

            // span {
            //   font-size: 18px;
            //   color: #4068d4;
            //   margin-top: 12px;
            // }
          }
        }
      // }
    // }
  }
}
.cartItem {
  // flex-basis: calc(33.3333% - 12px);
  // width: 25%;
  max-height: 323px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdde0;
  overflow: hidden;
  // cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .cartItemBox {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    flex: 1;
    position: relative;
  }

  &:hover {
    box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
    border: 1px solid #406bd4;
  }

  &:active {
    box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    border: 1px solid #406bd4;
  }

  .itemHeader {
    font-size: 16px;
    color: #323232;
    line-height: 24px;
    padding: 13px 16px;
    font-weight: bold;
    border-bottom: 1px solid #ebecf0;

    .itemHeaderTop {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-icon-warning-outline {
        color: #4068d4;
        padding: 0 8px 0 0px;
        cursor: pointer;
      }
    }

    .itemHeaderTit {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；

      &:hover {
        .title {
          color: #4068d4;
        }
      }

      &.isdisabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }

      .title {
        max-width: calc(100%);
        font-size: 16px;
        font-weight: bold;
        color: #323233;
        cursor: pointer;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; // 默认不换行；
      }
    }

    .itemHeaderBtn {
      display: flex;
      flex-direction: row;
      justify-content: end;
      align-items: center;
    }
  }

  .itemContent {
    padding: 0px 16px 16px 16px;

    .contentDesc {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      margin-top: 8px;
      font-weight: normal;
      color: #646566;
      line-height: 22px;
      font-size: 14px;

      .descLabel {
        color: #646566;
        word-break: keep-all;
      }

      .descValue {
        color: #323233;
        line-height: 22px;
        width: calc(100% - 72px);

        .contribution {
          font-size: 14px;

          ::v-deep .tagslist {
            position: relative;

            .tagslistLast {
              position: relative;

              &::after {
                width: 1px !important;
                content: '';
                position: absolute;
                left: 0px;
                width: 1px;
                height: 12px;
                background: #c8c9cc;
                top: 6px;
              }
            }

            &:last-child {
              &::after {
                width: 0px;
              }
            }

            &::after {
              content: '';
              position: absolute;
              right: 0px;
              width: 1px;
              height: 12px;
              background: #c8c9cc;
              top: 6px;
            }
          }

          ::v-deep .el-tag {
            color: #323233;
            background: transparent;
            font-size: 14px;
            border: none;
            padding: 0 3px;
            position: relative;
            padding-right: 5px;
            margin-right: 0px;
          }

          ::v-deep .el-button {
            background: transparent;
            border: none;
          }
        }

        .descValueMax {
          max-width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          cursor: pointer;
        }
      }
    }

    .itemProcess {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      padding-bottom: 4px;

      .processItem {
        width: 36px !important;
        height: 36px;
        word-break: keep-all;
        border-radius: 4px;
        position: relative;
        background: rgba(235, 236, 240, 0.8);
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        .el-icon-success {
          position: absolute;
          top: -4px;
          right: -4px;
          color: #39ab4c;
          font-size: 14px;
        }

        .process-span {
          margin-top: 0px;
        }

        &.success {
          background: #e6faea;

          .process-icon {
            color: #2ca941 !important;
          }
        }

        &.running {
          background: #e5eaff;

          .process-icon {
            color: #416cde !important;
          }
        }

        .process-icon {
          width: 16px;
          height: 16px;
          color: #7d7e80;
        }
      }

      .processLine {
        flex: 1;
        margin: 0px 8px;
        height: 1px;
        background: #c8c9cc;
      }
    }
  }

  .itemFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-top: 1px solid #ebecf0;
    font-size: 14px;
    color: #646566;

    & .footerLeft {
      display: flex;

      & .item {
        margin-right: 16px;
      }
    }

    > div {
      display: flex;
      align-items: center;

      img {
        height: 16px;
        width: 16px;
        margin-right: 4px;
      }
    }
  }
}
.cartItem-full {
  flex-basis: calc((100% - 3 * 12px) / 4);
  flex-shrink: 0; /* 禁止压缩 */
}
.cartItem-nofull {
  flex-basis: calc((100% - 2 * 12px) / 3);
}
.sence-tag {
  display: inline-flex;
  padding: 0 8px;
  height: 24px;
  border-radius: 2px;
  background: #ebf9ff;
  color: #318db8;
}

.separate {
  width: 1px;
  height: 12px;
  margin: 0 8px;
  background: #c8c9cc;
}

.avator {
  width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  background: #e6ecff;
  color: #4068d4;
  cursor: pointer;
}
// 媒体查询，调整不同屏幕尺寸下的卡片宽度
@media (max-width: 1500px) {
  .cartItem-nofull {
      flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
    }

    .container-nofull {
      flex-basis: calc((100% - 2 * 12px) / 3) !important;
    }
  .cartItem-full {
      flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
    }

    .container-full {
      flex-basis: calc((100% - 2 * 12px) / 3) !important;
    }

}

@media (max-width: 1280px) {
  .cartItem-nofull {
      flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
    }

    .container-nofull {
      flex-basis: calc((100% - 2 * 12px) / 3) !important;
    }
  .cartItem-full {
      flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
    }

    .container-full {
      flex-basis: calc((100% - 2 * 12px) / 3) !important;
    }
}

@media (max-width: 1340px) {
  .cartItem-nofull {
      flex-basis: calc((100% - 1 * 12px) / 2); // 一排显示2个卡片
    }

    .container-nofull {
      flex-basis: calc((100% - 1 * 12px) / 2) !important;
    }
  .cartItem-full {
      flex-basis: calc((100% - 1 * 12px) / 2); // 一排显示2个卡片
    }

    .container-full {
      flex-basis: calc((100% - 1 * 12px) / 2) !important;
    }
    .right-content {
      width: 50% !important;
    }
}

@media (max-width: 1122px) {
  .cartItem-nofull {
      flex-basis: calc(100% - 0px);
    }

    .container-nofull {
      flex-basis: calc(100% - 0px) !important;
    }
  .cartItem-full {
      flex-basis: calc(100% - 0px);
    }

    .container-full {
      flex-basis: calc(100% - 0px) !important;
    }
}
</style>
<style lang="scss">
.drawer-template {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .drawer-content {
    padding: 0px;
    height: 100%;
    overflow-y: hidden;
  }
}
.last-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;

    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-dialog__headerbtn {
      top: 14px;

      .el-dialog__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;

    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }

    .el-message-box__headerbtn {
      top: 14px;

      .el-message-box__close {
        font-size: 18px;
      }
    }
  }

  .el-message-box__content {
    padding: 16px 20px;

    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }

  .el-message-box__btns {
    padding: 0px 20px;

    button {
      width: 60px !important;
    }

    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }

  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }

  .el-dialog__footer {
    padding: 16px 20px;

    .el-button {
      line-height: 20px;
    }
  }

  :deep(.el-input__inner) {
    border-radius: 2px;
    height: 30px;
    line-height: 30px;
  }
}
.btns {
  display: flex;
  flex-shrink: 0;

  .btns-item {
    display: inline-block;
    position: relative;
    padding: 4px 16px;
    // width: 30px;
    // height: 30px;
    line-height: 22px;
    background: #f2f3f5;
    color: #4068d4;
    text-align: center;
    cursor: pointer;

    .view-icon {
      vertical-align: middle;
      color: #3f68d4;
    }

    &:hover {
      background: #eaeaed;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 1px;
      height: 18px;
      background: #dcdde0;
    }

    &:first-child {
      border-radius: 4px 0px 0px 4px;

      &::before {
        content: unset;
      }
    }

    &:last-child {
      border-radius: 0px 4px 4px 0px;
    }

    &.active {
      background: #4068d4;
      color: #ffffff;

      .view-icon {
        color: #ffffff;
      }

      &::before {
        content: unset;
      }
    }
  }
 }
 .flex_bt{
    display: flex;
    justify-content: space-between;
    align-items: center;
 }
</style>
