<template>
 <div class="left-contain-main">
  <div class="main-container">
  <div >
   <div class="main-heard">
    <div class="heard-title">你好，我是专家AI伙伴</div>
    <div class="content">
     <div class="content-lf">作为你的生产智能伙伴，我既能答疑解惑，又能把经验沉淀为知识、能力。</div>
     <div class="content-rg">
      <div class="transfer" @click="changeRefresh">换一换</div>
      <i  @click="changeRefresh" :class="{ rotate: isRotating }" class= "el-icon-refresh-right" id="refresh-icon"></i>
     </div>
    </div>
   </div>
   <div class="main-middle">
    <!-- <div class="middle-lf">
     <div class="lf-heard">
      <div class="lf-title">我的代办任务列表</div>
      <div class="lf-describe">分配给我的所有需要完成的任务</div>
     </div>
     <div class="lf-heard-card">
      <div class="card-items" v-for="(item, index) in todoTasks" :key="index" @click="goRouter(item)">
       <div class="item-name"> <i>{{ index + 1 }}</i>.{{ item.name }}</div>
      </div>
     </div>
    </div> -->
    <div class="middle-mid">
     <div class="mid-heard">
      <div class="mid-title">智能能力场景</div>
      <div class="mid-describe">查看常用的能力场景</div>
     </div>
     <div class="mid-heard-card">
      <div class="card-items" v-for="(item, index) in displayedItems" :key="index" @click="goRouter(item)">
       <div class="item-name">{{ item.name }}</div>
       <div class="item-describe">{{ item.description }}</div>
      </div>
     </div>
    </div>
    <div class="middle-rg">
     <div class="rg-heard" >
      <div class="rg-title">
       能力仓库
      </div>
      <span class="item" v-for="(item, index) in pageItems" :key="index">
        <span @click="() => $router.push({path:'/abilityCenter/targetList',query: { ...item }})" class="rg-describe">
         {{ item.name }}
        </span>
      </span>
     </div>
     <div class="rg-heard knowledge" >
      <div class="rg-title">
       知识库
      </div>
      <span class="item" v-for="(item, index) in KnowledgeItems" :key="index" @click="toKnowledge(item)">
        <span class="rg-describe">
         {{ item.name }}
        </span>
      </span>
     </div>
    </div>
   </div>
  </div>
   <div class="middle-bottom">
    <div class="floating-button" @mouseover="showCards = true" @mouseleave="hideCards">
      <i class="el-icon-menu"></i>
    </div>
    <transition name="fade">
      <div class="card-container" v-if="showCards" @mouseover="showCards = true" @mouseleave="hideCards">
        <div class="card" v-for="(card, index) in cards" :key="index" @click="navigateTo(card.path)">
          <div class="card-icon">
            <i :class="card.icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">{{ card.title }}</div>
          </div>
        </div>
      </div>
    </transition>
    <el-drawer
    :visible.sync="drawerVisible"
    size="50%"
    direction="rtl"
    :with-header="false"
  >
    <First v-if="drawerVisible && drawerUrl === '/planGenerate/first'" />
    <Index v-if="drawerVisible && drawerUrl === '/planGenerate/index'" /> 
  </el-drawer>
  </div>
   </div>
  </div>
</template>
<script>
import {
 queryDictConfig
} from '@/api/planGenerateApi.js';
import First from '@/views/planGenerate/First.vue';
import Index from '@/views/planGenerate/index.vue';
import { recommendedSceneList,queryAbilityMarket,getKnowledgeList} from '@/api/planGenerateApi.js';
export default {
 name: 'homePage',
 components: {
    First,
    Index,
},
 data() {
  return {
    KnowledgeList: [],
    pageList: [],
    sceneList: [],
    currentIndex: 0, // 当前展示的数据索引
    currentPageListIndex: 0,
    currentKnowledgeListIndex: 0,
    itemsPerPage: 2, // 每页展示的数据条数
    isRotating:false,
    showCards: false,
    drawerVisible: false,
    drawerUrl: '',
    cards: [
        { icon: 'el-icon-setting', title: '仿真生产',  path: 'http://fnfz-sim-web.fat.fnwintranet.com/' },
        { icon: 'el-icon-s-grid', title: '能力组装',  path: 'https://ioc-workbench-front.ennew.com/' },
        { icon: 'el-icon-postcard', title: '卡片生产', path: 'https://qi.ennew.com/cards' },
    ],
    todoTasks: [] // 初始化待办任务列表为空数组

  }
 },
 computed: {
  displayedItems() {
    // 获取当前要展示的两条数据
    const itemsToShow = this.sceneList.slice(this.currentIndex, this.currentIndex + this.itemsPerPage);
    // 如果数据不足两条，从列表开头继续取
    if (itemsToShow.length < 2 && this.sceneList.length>2) {
        itemsToShow.push(...this.sceneList.slice(0, this.itemsPerPage - itemsToShow.length));
    }
    this.isRotating = false
    return itemsToShow
  },
  pageItems() {
    // 获取当前要展示的两条数据
    const itemsToShow = this.pageList.slice(this.currentPageListIndex, this.currentPageListIndex + this.itemsPerPage);

    // 如果数据不足两条，从列表开头继续取
    if (itemsToShow.length < 2 && this.pageList.length>2) {
        itemsToShow.push(...this.pageList.slice(0, this.itemsPerPage - itemsToShow.length));
    }
    return itemsToShow
  },
  KnowledgeItems() {
    // 获取当前要展示的两条数据
    const itemsToShow = this.KnowledgeList.slice(this.currentKnowledgeListIndex, this.currentKnowledgeListIndex + this.itemsPerPage);

    // 如果数据不足两条，从列表开头继续取
    if (itemsToShow.length < 2 && this.KnowledgeList.length>2) {
        itemsToShow.push(...this.KnowledgeList.slice(0, this.itemsPerPage - itemsToShow.length));
    }
    return itemsToShow
  },
},
  mounted() {
    this.getSceneList()
    this.getQueryPageList()
    this.getKnowledge()
    this.fetchTodoTasks(); // 在组件挂载时调用函数以填充待办任务列表
  },
 methods: {
  goRouter(item){
    
    queryDictConfig({ business_type: 'scene_type' }).then((res) => {
      if (res.status === 200 && res.data.code === 200) {
        const result = res.data.result?.config.filter(element => element.code == item.scene_type)[0]
        // console.log('eeeeeeeeeeeeeeeeee',result)
        if(result){
          if(result.dev_mode == 'scheme,code'){
            this.$router.push({path:'/planGenerate/create',query: { ...item }})
          }else if(result.dev_mode == 'scheme'){
            this.$router.push({path:'/planGenerate/partnerCreate',query: { ...item }})
          }else if(result.dev_mode == 'task' ) {
            this.$router.push({path: '/planGenerate/createTask',query: { ...item }})
          }
        }else{
          this.$router.push({path:'/planGenerate/create',query: { ...item }})
        }
      }
    })
  },
  toKnowledge(item) {
    const route = this.$router.history.current
    this.$router.push({
      path: '/knowledgeBase/portal',
      query: { 
        workspaceId: route.query.workspaceId,
        workspaceName: route.query.workspaceName,
         ...item }}
    )
  },
  navigateTo(path) {
      if (path === '/planGenerate/first' || path === '/planGenerate/index' || path === '/blockly-project-new/ViewWrite') {
        this.drawerUrl = path;
        this.drawerVisible = true;
      } else if (path.startsWith('http')) {
        window.open(path, '_blank');
      } else {
        this.$router.push(path);
      }
    },
  hideCards() {
      this.showCards = false;
  },
  changeRefresh() {
    this.isRotating = true
    // 更新索引
    this.currentIndex = (this.currentIndex + this.itemsPerPage) % this.sceneList.length;
    this.currentPageListIndex = (this.currentPageListIndex + this.itemsPerPage) % this.pageList.length;
    this.currentKnowledgeListIndex = (this.currentKnowledgeListIndex + this.itemsPerPage) % this.KnowledgeList.length;
  },
  getSceneList() {
    recommendedSceneList().then(res =>{
      if(res.data?.code === 200 && res.status == 200) {
        console.log('res----',res.data.result)
        this.sceneList = res.data.result
      }
    }).catch(err =>{
      this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
    })
  },
  async getQueryPageList() {
      const params = {
        ability_name: "",
        updated_id: "",
        scene_id: "",
        owner_id: "",
        page_num: 1,
        page_size: 10 ,
        // agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
        tag_ids: [],
        sorted_scheme_ids: [],
      };
      queryAbilityMarket(params)
        .then(async (res) => {
          const { data = {}, status } = res;
          if (status === 200 && data.code === 200) {
            console.log('能力仓库卡片信息-----------',data)
            if(data.result?.items) {
              this.pageList = data.result.items
            }
          } else {
            this.$message.error(res.msg || '失败!');
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        })
    },
    getKnowledge() {
      getKnowledgeList().then(res=> {
        const { data = {}, status } = res;
        if (status === 200 ) {
          this.KnowledgeList = data.data
        }
      })
    },
    fetchTodoTasks() {
   // 直接返回一个静态的待办任务列表
      this.todoTasks = [
        { name: '染缸液位/温度联控的专家模型生产'},
        { name: '物联基础物模型生产'  },
        { name: '物联部署&数据采集' },
        { name: '数据指标服务' }
      ];
    },
    handleTaskClick(task) {
      // 处理任务点击事件，例如导航到任务详情页面
      console.log('Clicked task:', task);
    // 在这里可以添加导航到任务详情页面的逻辑
    }
 },
 beforeDestroy() {

  }
}
</script>
<style lang="scss" scoped>
.left-contain {
 &-main {
  height: 100%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;

  .main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 100%;   
   .main-heard {
    // height: 184px;
    // padding: 56px 0px;

    .heard-title {
     font-weight: 600;
     font-size: 28px;
     color: #323233;
     line-height: 40px;
     text-align: center;
     font-style: normal;
     margin-bottom: 12px;
    }

    .content {
     display: flex;
     justify-content: center;

     #refresh-icon {
      cursor: pointer;
      // transition: transform 0.5s linear;
      /* 添加过渡效果 */
     }

     #refresh-icon.rotate {
      animation: rotate .6s infinite linear; /* 旋转动画 */
      // animation: transform 0.1s linear; /* 旋转动画 */
     }
     .rotate {
      // transition: transform 0.1s linear;
     }
     // @keyframes rotate {
      
     // }
     &-lf {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: center;
      font-style: normal;
     }

     &-rg {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #4068D4;
      .transfer {
       cursor: pointer;
      }
     }

    }
   }

   .main-middle {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    .middle-lf {
     padding: 16px;
     width: 300px;
     background: linear-gradient(135deg, #ece0fa 180%, #f5effb 80%);
     border-radius: 4px;

     .lf-heard {
      margin-bottom: 20px;

      .lf-title {
       font-weight: 600;
       font-size: 16px;
       color: #323233;
       line-height: 22px;
       margin-bottom: 4px;
      }

      .lf-describe {
       color: #969799;
       font-size: 12px;
       line-height: 17px;
      }
     }

     .lf-heard-card {
      .card-items {
       cursor: pointer;
       border: 1px solid transparent;
       border-radius: 4px;
       margin-bottom: 12px;
       .item-name {
        font-weight: 600;
        color: #323233;
        line-height: 20px;
        margin-bottom: 2px;
        white-space: nowrap;
       /* 防止文本换行 */
       overflow: hidden;
       /* 隐藏溢出的文本 */
       text-overflow: ellipsis;
       /* 显示省略号 */
       }

       .item-describe {
        font-size: 12px;
        color: #969799;
        line-height: 18px;
        white-space: nowrap;
       /* 防止文本换行 */
       overflow: hidden;
       /* 隐藏溢出的文本 */
       text-overflow: ellipsis;
       /* 显示省略号 */
       }
      }
      .card-items:hover {
        border: 1px solid #615ced;
      }
      .card-items:last-of-type {
       margin-bottom: 0px;
      }
     }
    }
    .middle-mid {
     margin-left: 16px;
     padding: 16px;
     width: 300px;
     background: linear-gradient(135deg, #F8E9EC 0%, #EFF3FF 100%);
     border-radius: 4px;

     .mid-heard {
      margin-bottom: 20px;

      .mid-title {
       font-weight: 600;
       font-size: 16px;
       color: #323233;
       line-height: 22px;
       margin-bottom: 4px;
      }

      .mid-describe {
       color: #969799;
       font-size: 12px;
       line-height: 17px;
      }
     }

     .mid-heard-card {
      .card-items {
       cursor: pointer;
       border: 1px solid transparent;
       background: #FFFFFF;
       padding: 12px 16px;
       border-radius: 4px;
       margin-bottom: 12px;

       .item-name {
        font-weight: 600;
        color: #323233;
        line-height: 20px;
        margin-bottom: 2px;
        white-space: nowrap;
       /* 防止文本换行 */
       overflow: hidden;
       /* 隐藏溢出的文本 */
       text-overflow: ellipsis;
       /* 显示省略号 */
       }

       .item-describe {
        font-size: 12px;
        color: #969799;
        line-height: 18px;
        white-space: nowrap;
       /* 防止文本换行 */
       overflow: hidden;
       /* 隐藏溢出的文本 */
       text-overflow: ellipsis;
       /* 显示省略号 */
       }
      }
      .card-items:hover {
        border: 1px solid #615ced;
      }
      .card-items:last-of-type {
       margin-bottom: 0px;
      }
     }
    }

    .middle-rg {
     display: flex;
     width: 300px;
     justify-content: center;
     flex-direction: column;
     margin-left: 16px;

     .rg-heard {
      // cursor: pointer;
      flex: 1;
      // width: 288px;
      background: linear-gradient(135deg, #EFFDFF 0%, #F7F1FF 100%);
      border-radius: 4px;
      margin-bottom: 16px;
      padding: 16px;
      .item {
        display: flex
      }
      .rg-title {
       font-weight: 600;
       font-size: 16px;
       margin-bottom: 8px;
       color: #323233;
       line-height: 22px;
      }

      .rg-describe {
       cursor: pointer;
       border: 1px solid transparent;
       font-size: 12px;
       color: #323233;
       line-height: 18px;
       font-weight: 400;
       white-space: nowrap;
       /* 防止文本换行 */
       overflow: hidden;
       /* 隐藏溢出的文本 */
       text-overflow: ellipsis;
       /* 显示省略号 */
      }
      .rg-describe:hover {
        // border: 1px solid #615ced;
        color: #409eff;
      }
     }
     .knowledge {
      background: linear-gradient( 135deg, #EFECFF 0%, #FFF7FD 100%);
     }
     .rg-heard:last-of-type {
      margin-bottom: 0px;
     }

     .rg-bottom {
      background: linear-gradient(135deg, #EFECFF 0%, #FFF7FD 100%);
      border-radius: 4px;
     }
    }

   }

   .middle-bottom {

        .floating-button {
          position: fixed;
          bottom: 20px;
          right: 20px;
          width: 60px;
          height: 60px;
          background-color: #409eff;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 30px;
          cursor: pointer;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          transition: transform 0.3s ease;
        }

        .floating-button:hover {
          transform: scale(1.1);
        }

        .card-container {
          position: fixed;
          bottom: 100px; /* Adjust this value to control the distance from the floating button */
          right: 20px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: center;
        }

        .card {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          padding: 2px;
          text-align: center;
          min-width: 120px;
          transition: transform 0.3s ease;
          cursor: pointer;

          &:hover {
            transform: translateY(-5px);
          }

          .card-icon {
            margin-bottom: 5px;
            color: #409eff;
          }

          .card-content {
            .card-title {
              font-size: 14px;
              font-weight: 600;
              color: #323233;
              margin-bottom: 4px;
            }

            .card-description {
              font-size: 12px;
              color: #969799;
            }
          }
        }
      }
  }
 }
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
</style>