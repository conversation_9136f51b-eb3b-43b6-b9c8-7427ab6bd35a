<template>
  <div class="role-info">
    <div class="user">
      <el-avatar :size="80" :src="circleUrl"></el-avatar>
      <div class="user-info">
        <span class="name">{{ userDetailInfo.name }}</span>
        <div class="title">
          <span>{{ userDetailInfo.title || '安全专家' }}</span>
        </div>
      </div>
    </div>
    <div class="knowledge-benefits">
      <div class="title">
        <img src="https://res.ennew.com/image/png/3184529ce7ccd32664c18d88aaec8315.png" />
        <span class="profit">知识收益情况</span>
      </div>
      <div class="info">
        <div v-for="(item,index) in knowledgeBenefitsInfo" class="info-item" :key="index">
          <span :class="(!item.value || item.value == 0) ? 'bill' : 'num' ">{{ item.type === 'share' ? '¥' + item.value : item.value }}</span>
          <span class="value">{{ item.title }}</span>
        </div>
      </div>
    </div>
    <div class="knowledge-accumulation">
      <div class="title">
        <img src="https://res.ennew.com/image/png/b79358d2c1cec9c49326dfb6085034c3.png" />
        <span>知识收益情况</span>
      </div>
      <div class="info">
        <div v-for="item in knowledgeAccumulationInfo" class="info-item">
          <span :class="(!item.value || item.value == 0) ? 'bill' : 'num' ">{{ item.value }}</span>
          <span class="value">{{ item.title }}</span>
        </div>
      </div>
    </div>
    <!-- <div class="guide-select"  >
      <div class="icon-btn" @click="handleIconClick('chat')" :class="{ selected: selectedIcon == 'chat' }">
        <i class="el-icon-chat-dot-round"></i>
        <span style="font-weight: 900">专家引导</span>
      </div> -->
      <!-- <div class="icon-btn" @click="handleIconClick('scene')" :class="{ selected: selectedIcon == 'scene' }">
        <i class="el-icon-setting"></i>
        <span style="font-weight: 900">场景设置</span>
      </div> -->
      <!-- <div class="icon-btn" @click="handleIconClick('schema')" :class="{ selected: selectedIcon == 'schema' }">
        <i class="el-icon-notebook-1"></i>
        <span style="font-weight: 900">沉淀看板</span>
      </div>
    </div> -->
  </div>
</template>

<script>
import { userIndexCount } from '@/api/planGenerateApi.js';
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {};
let title = '';
if (sessionStorage.getItem('ACCOUNT_INFO')) {
  const account = JSON.parse(sessionStorage.getItem('ACCOUNT_INFO'));
  const titleParts = account.postName.split('/');
  title = titleParts[titleParts.length - 2];
} else {
  title = '';
}
export default {
  props: {
    iconSelect: {
      type: String,
      required: true
    }
  },
  watch: {
    iconSelect(newValue, oldValue) {
      this.handleIconClick(newValue); // 假设 queryTableData 是组件的方法
    }
  },
  data() {
    return {
      workspaceId: 1,
      canCompeleted: false,
      searchVal: '',
      showCardList: false,
      activeName: 'first',
      title: '安全专家',
      tableData: {
        myCollect: '0',
        user_id: '',
        tags: [],
        list: [], // 表格数据
        page: 1,
        pageSize: 12,
        total: 0,
        allist: [],
        allTotal: 0
      },
      sceneList: [],
      checkedList: [],
      circleUrl: userInfo.headImageUrl,
      userDetailInfo: {
        name: userInfo.nickName,
        title: title
      },
      knowledgeBenefitsInfo: [
        {
          title: '创值分享',
          value: 889,
          type: 'share'
        },
        {
          title: '被调用次数量',
          value: 1789,
          type: ''
        },
        {
          title: '已生成智能能力数量',
          value: 9,
          type: ''
        }
      ],
      knowledgeAccumulationInfo: [
        {
          title: '提交总数',
          value: 8889
        },
        {
          title: '处理中总量',
          value: 1789
        },
        {
          title: '待处理消息量',
          value: 9,
          type: ''
        },
        {
          title: '被收藏次数',
          value: 8889,
          type: 'share'
        },
        {
          title: '被浏览次数',
          value: 1789,
          type: ''
        },
        {
          title: '被分享次数',
          value: 9,
          type: ''
        }
      ],
      showMyAbility: false,
      showAllAbility: true,
      showMyCollect: false, // 新增状态变量
      selectedIcon: 'chat' // 新增状态变量
    };
  },
  mounted() {
    this.workspaceId = Number(this.$route.query.workspaceId);
    this.userIndexCountFn();
  },
  methods: {
    userIndexCountFn() {
      userIndexCount().then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          const result = res.data.result;
          // 创值分享
          this.knowledgeBenefitsInfo[0].value = result.get_value;
          // 被调用次数量
          this.knowledgeBenefitsInfo[1].value = result.call_size;
          // 已生成能力数量
          this.knowledgeBenefitsInfo[2].value = result.ability_size;

          // 提交总数
          this.knowledgeAccumulationInfo[0].value = result.scheme_commit_size;
          // 处理中总量
          this.knowledgeAccumulationInfo[1].value = result.scheme_doing_size;
          // 待处理消息量
          this.knowledgeAccumulationInfo[2].value = result.scheme_todo_size;
          // 收藏次数
          this.knowledgeAccumulationInfo[3].value = result.scheme_collect_size;
          // 浏览量
          this.knowledgeAccumulationInfo[4].value = result.scheme_views_size;
          // TODO 分享，后端暂无统计值
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    handleIntoList(target) {
      this.$emit('show-schema', target);
    },
    handleIconClick(icon) {
      console.log(icon)
      this.selectedIcon = icon;
      this.handleIntoList(icon);
    },
  }
};
</script>

<style lang="scss" scoped>
.role-info {
  height: 100%;
  flex-shrink: 0;
}
.right-content {
  background-color: #fff;
  overflow: auto;

  .user {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0px 0px 24px 0px;
    border-bottom: 1px solid #ebecf0;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #323233;
      text-align: left;
      font-style: normal;
    }
    .title {
      display: flex;
      align-items: center;
    }

    .title span {
      margin-right: 10px; /* 调整文本和图标之间的间距 */
    }

    .title i {
      margin-left: 10px; /* 调整图标之间的间距 */
      vertical-align: middle; /* 垂直对齐方式 */
      color: #333; /* 图标颜色 */
    }

    .title i:hover {
      color: #409eff; /* 鼠标悬停时的颜色 */
    }
    .title i.selected {
      color: #409eff; // 选中时的颜色
    }
    img {
      width: 80px;
      height: 80px;
    }

    .user-info {
      margin-left: 16px;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
      align-items: left;
      .name {
        line-height: 40px;
        margin-bottom: 6px;
      }
      .title {
        display: flex;
        border-radius: 2px;
        align-items: center;

        span {
          background: #e6ecff;
          
          padding: 0.5px 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #3455ad;
        }

        i {
          margin-left: 10px;
          cursor: pointer;
          font-size: 14px;
        }
      }
    }
  }

  .knowledge-benefits,
  .knowledge-accumulation {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 24px 0px 0px 0px;
    border-bottom: 1px solid #ebecf0;

    .title {
      .profit {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #323233;
        line-height: 18px;
      }
      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
    }

    .info {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 22px 16px 24px 16px;

      .info-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 24px;
          color: #323233;
          line-height: 33px;
          text-align: right;
          font-style: normal;
        }
        .bill {
          color: #DCDDE0;
          line-height: 33px;
          margin-bottom: 4px;
        }
        .num {
          margin-bottom: 4px;
        }
        .value {
          font-family: PingFangSC, PingFang SC;
          line-height: 20px;
          font-weight: 400;
          font-size: 14px;
          color: #646566;
        }
      }
    }
  }

  .knowledge-accumulation {
    .info {
      flex-wrap: wrap;
      padding: 22px 16px 0px 16px;
      padding-bottom: 0;

      .info-item {
        width: 33%;
        margin-bottom: 24px;
      }
    }
  }
  .knowledge-benefits {
    .info-item {
      flex: 1;
    }
  }
  .guide-select {
    display: flex;
    justify-content: space-between;
    padding: 32px 0px 24px 0px;

    .icon-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 16px 16px 15px;
      flex: 1; // 使每个按钮占据相同的空间
      min-width: 150px; // 去除默认的宽度
      margin-right: 10px; // 添加右侧间距
      background: #ebeffa;
      transition: background-color 0.3s ease; // 添加过渡效果

      i {
        width: 18px;
        height: 18px;
        margin-right: 7px;
        font-size: 20px;
      }
      &:hover {
        background: linear-gradient(132deg, #cfcddc 0%, #a7b5fd 100%), #d7cef0; // 选中时的背景颜色，可以根据设计调整
      }
    }
    .icon-btn.selected {
      background: linear-gradient(
        132deg,
        #5a29e0 0%,
        #a58aee 100%
      ); // 调整渐变色的起始和结束颜色，使其过渡更加平滑
      color: #fff; // 选中时的文字颜色
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); // 选中时的阴影
    }
  }
}
</style>
