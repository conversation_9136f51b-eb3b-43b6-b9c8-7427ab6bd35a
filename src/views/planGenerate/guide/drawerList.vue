<template>
 <div class="ability-list">
  <el-card class="box-card" shadow="never">
   <div class="planSearch">
    <div class="search">
     <div class="flex_bt">
      <div class="optHeader2 center_flex">
       <el-tabs v-model="activeTag" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in tableTypeOptions" :key="index" :label="item.name" :name="item.code">
         <template slot="label">
          <div class="tabTitle">{{ item.name }}</div>
         </template>
        </el-tab-pane>
       </el-tabs>
      </div>
      <div class="rightTitleOpt-tab">
       <el-dropdown @command="changeProcessStatus">
        <div class="solid_class"><i class="el-icon-more"></i> </div>
        <el-dropdown-menu slot="dropdown">
         <!-- 直接渲染所有标签 -->
         <el-dropdown-item v-for="tag in tableTypeOptions" :key="tag.code" :command="tag.code"
          :class="{ 'tag-selected': activeTag === tag.code }">
          {{ tag.name }}
         </el-dropdown-item>
        </el-dropdown-menu>
       </el-dropdown>
      </div>
      <div class="footer-search">
       <el-input v-model.trim="formData.name" clearable @input="handleSearchList" suffix-icon="el-icon-search" 
        placeholder="请输入名称">
       </el-input>
       <!-- <el-button style="margin-left: 12px;" type="primary" @click="handleIntoBuild">新建</el-button> -->
      </div>
     </div>
    </div>
   </div>
  </el-card>
  <div v-if="tableData.process_status === '0'" class="containerCardSmall">
   <div v-loading="isLoading" :class="{ 'left-content': true, 'left-content-max': turnFull }">
    <div v-if="tableData.list.length > 0" class="main">
     <div class="tab-content planContainer">
      <div class="container containerCard">
       <div v-for="(item, index) in tableData.list" :key="item.id_index" class="cartItem"
        :class="turnFull ? 'cartItem-full' : 'cartItem-nofull'">
        <div class="cartItemBox">
         <div class="itemHeader">
          <div class="itemHeaderTop">
           <el-popover placement="top-start" trigger="hover" width="240">
            <div>
             <div style="display: flex; align-items: center; margin-bottom: 8px">
              <div style="color: #646566">进度：</div>
              <div style="flex: 1; max-width: calc(100% - 110px); color: #323233">
               {{ statusMap(item.agent_scene_code, item.status) }}
              </div>
             </div>
             <div style="display: flex; align-items: center; margin-bottom: 8px">
              <div style="color: #646566">贡献专家：</div>
              <div style="flex: 1; max-width: calc(100% - 70px)">
               <selectItem class="contribution" :array-items="selectUserFn(item.contributors)" :max-length="1">
               </selectItem>
              </div>
             </div>
             <div style="display: flex; align-items: center">
              <div style="color: #646566">创建时间：</div>
              <div style="flex: 1; max-width: calc(100% - 70px); color: #323233">
               {{ item.update_time || item.create_time }}
              </div>
             </div>
            </div>
            <i slot="reference" style="transform: rotate(180deg);" class="el-icon-warning-outline" />
           </el-popover>
           <div class="itemHeaderTit" @click="handleGotoDetail(item)">
            <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
             <div class="title">{{ item.name }}</div>
            </el-tooltip>
           </div>
           <!-- <div class="itemHeaderBtn">
            <el-dropdown style="margin-left: 10px" @command="(type) => handleCommand(type, item)">
             <el-button type="text" size="mini"><img src="@/assets/images/planGenerater/more.png" /></el-button>
             <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="(item.visibility === 'private' &&
                   item.username !== userInfo.username &&
                   !isAdmin) ||
                 (item.visibility === 'share' && !item.is_shared && !isAdmin)
                 " command="edit">编辑</el-dropdown-item>
              <el-dropdown-item command="remove">删除</el-dropdown-item>
             </el-dropdown-menu>
            </el-dropdown>
           </div> -->
          </div>
          <div style="display: flex; align-items: center; margin-top: 8px">
           <el-tooltip effect="dark" :content="item.nickName + '(' + item.username + ')'" placement="top">
            <span class="avator">{{ item.nickName?.slice(-2) }}</span>
           </el-tooltip>
           <div class="separate"></div>
           <div class="sence-tag">ID:{{ item.id }}</div>
           <div style="margin-left: 8px" v-if="item.template_code">
            <Status :isStatus="false" bgColor="#E6ECFF" :text="codeToNameMap[item.template_code]" />
           </div>
           <div style="margin-left: 8px" v-if="item.sort == 1" class="sence-tag">置顶</div>
          </div>
         </div>
         <div class="itemContent">
          <div class="contentDesc">
           <div class="descValue">
            <el-tooltip class="item" effect="dark" :content="item.description ? item.description : '--'"
             placement="top">
             <div class="descValueMax">
              {{ item.description || '--' }}
             </div>
            </el-tooltip>
           </div>
          </div>
         </div>
        </div>
        <div class="itemFooter">
         <el-button type="text" @click="handleGotoDetail(item)">
          <SvgIcon name="right-rect" style="margin-right: 4px;" />立即使用
         </el-button>
        </div>
       </div>
      </div>
      <!-- 分页部分 -->
      <div style="text-align: right; padding-top: 16px; padding-bottom: 16px; background-color: #fff;">
       <el-pagination class="new-paper" layout="prev, pager, next, sizes, jumper" :page-sizes="[12, 24, 36, 48, 60]"
        :current-page.sync="tableData.page" :page-size="tableData.pageSize" :total="tableData.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange">
       </el-pagination>
      </div>
     </div>
    </div>
    <div v-else class="cardEmpty">
     <el-empty description="暂无能力"></el-empty>
    </div>
   </div>
  </div>
  <div v-else>
   <el-empty description="暂无内容">
    <img slot="image" src="@/assets/images/ar/pic_暂无内容.png" />
   </el-empty>
  </div>
 </div>
</template>

<script>
import sampleAbility from '../sampleAbility/index.vue';
import conditionTag from '@/components/conditionTag/index.vue'
import hearderContainer from '@/components/hearderContainer/index.vue'
import { mapGetters } from 'vuex';
import { Message } from 'element-ui'
import {
 queryTemplate,
 schemeCreateFromTemplate,
 deleteRel,
 DeleteScheme,
 queryCallCountStatistic,
 expertTagsWithCount,
 saveRel,
 SchemeList,
 schemeListCount,
 updateSchemeSort,
 queryDictConfigNew,
 getSencetVisibleList,
 getUserInfo,
 SchemeStatusList,
 agentSenceList,
 getInitWorkBenchDich,
 AddScheme,
 bindTag,
 schemeTemplateList
} from '@/api/planGenerateApi.js'
import selectItem from '@/views/planGenerate/selectItem.vue'
import Status from '@/components/Status/index.vue'
import { debounce } from 'lodash'
import { template } from 'lodash';
const userInfo = sessionStorage.getItem('USER_INFO')
 ? JSON.parse(sessionStorage.getItem('USER_INFO'))
 : {}
const wid = localStorage.getItem('currentWorkSpace')
 ? JSON.parse(localStorage.getItem('currentWorkSpace')).workspaceId
 : ''
export default {
 name: 'SampleAbility',
 components: {
   Status, selectItem, conditionTag,
  hearderContainer, sampleAbility
 },
 data() {
  return {
   requirement: false,
   dialogVisible: false,
   isLoading: true,
   turnFull: true,
   statusList: [],
   tableTypeOptions: [
   {
     name: '全部',
     code: ''
    }
   ],
   activeTag: '0',
   userList: [],
   formData: {
    description: '',
    status: '',
    tags: [],
    agent_scene: [],
    name: '',
    createUserId: '',
    contributors: ''
   },
   props: {
    lazy: true,
    lazyLoad(node, resolve) {
     const { level } = node
     if (level === 0) {
      queryDictConfigNew({ "dev_mode": "scheme" }).then((res) => {
       if (res.status === 200 && res.data.code === 200) {
        const result = res.data.result?.map((item) => {
         return {
          value: item.code,
          label: item.name
         }
        })
        resolve(result || [])
       } else {
        Message({
         type: 'error',
         message: res.data?.msg || '接口异常!'
        })
       }
      })
     } else {
      getSencetVisibleList({
       keyword: '',
       user_id: userInfo.userId,
       scene_type: node.data.value,
       workspace_id: wid
      })
       .then((res) => {
        const result = res.data?.map((item) => {
         return {
          value: item.id,
          label: item.name,
          leaf: true
         }
        })
        resolve(result || [])
       })
       .catch(() => {
        resolve([])
       })
     }
    }
   },
   checkedTypeTags: [],
   senceTypeList: [],
   showCustomSearch: false,
   checkedTags: [], // 筛选选择的标签
   switchValue: 'AIChat',
   workspaceId: 1,
   canCompeleted: false,
   searchVal: '',
   showCardList: false,
   activeName: 'first',
   title: '安全专家',
   devPersonInfo: {},
   curId: '',
   tableData: {
    process_status: '0',
    myCollect: '0',
    user_id: '',
    tags: [],
    list: [], // 表格数据
    page: 1,
    pageSize: 12,
    total: 0,
    allist: [],
    allTotal: 0
   },
   sceneList: [],
   checkedList: [],
   selectedFilters: [] // 选中的过滤条件
  }
 },
 watch: {
  searchVal: {
   handler(nv) {
    this.queryTableData()
   }
  },
  selectedFilters: {
   handler() {
    this.queryTableData()
   },
   deep: true
  }
 },
 created() {
  this.getQueryTemplate()
  this.handleSearchList = debounce(this.handleSearchList, 300)
 },
 async mounted() {
  this.workspaceId = Number(this.$route.query.workspaceId)
  // this.queryTableData()
  await this.queryStatusData('')
 },
 computed: {
  ...mapGetters({
   isAdmin: 'common/getIsAdminGetter'
  }),
  codeToNameMap() {
      return this.tableTypeOptions?.reduce((map, item) => {
        map[item.code] = item.name;
        return map;
      }, {});
    }
 },
 methods: {
  templateStatus(item) {
   console.log('item-----1111',item)
  },
  async getQueryTemplate() {
  try {
   const res = await queryTemplate({})
   if (res.status === 200 || res.data?.status === 200) {
    this.tableTypeOptions = [
    {
     name: '全部',
     code: '0'
    },
    ...res.data
    ]
          }
  } catch (error) {
   
  }
    },
  async getFromTemplate(item) {
   try {
    const params = {
       scheme_id: Number(item?.id),
       name: item?.name,
       description: item?.description
     }
     const res = await schemeCreateFromTemplate(params)
     if (res.status === 200 && res.data.code === 200) {
     
     } else {
       this.$message({
         type: 'error',
         message: res.data?.msg || '接口异常!'
       })
     }
     return res.data?.result
   } catch (error) {
     console.error(error)
   }
  },
  handleClick() {
   this.changeProcessStatus(this.activeTag)
  },
  changeoptHeader2Tab(tag) {
   this.activeTag = tag
  },
  handleSearchList() {
   this.handlSearch()
  },
  handleChangeSize() {
   this.turnFull = !this.turnFull
   console.log('222222222222-----', this.turnFull)
  },
  async queryStatusData(scene) {
   this.tableLoading = true
   await this.queryTableData(this.justMine)
  },
  searchUser(userName, callback) {
   getUserInfo({ nickName: userName }).then((res) => {
    if (res.status === 200 && res.data.code === 200) {
     this.userList = res.data.result
    }
   })
  },
  handlSearch() {
   this.tableData.page = 1
   this.queryTableData();
  },
  selectType(code) {
   if (Object.prototype.toString.call(code) === '[object Object]') {
    if (code.code) {
     code = code.code
    } else if (code.id) {
     code = code.id
    }
   }
   // 删除
   if (this.checkedTypeTags.indexOf(code) > -1) {
    this.checkedTypeTags = []
   } else {
    // 增加
    this.checkedTypeTags = [code]
   }
   this.tableData.page = 1
   this.queryTableData();
  },
  selectTag(tag) {
   if (Object.prototype.toString.call(tag) === '[object Object]') {
    if (tag.code) {
     tag = tag.code
    } else if (tag.id) {
     tag = tag.id
    }
   }
   // 删除
   if (this.checkedTags.indexOf(tag) > -1) {
    const temp = []
    this.checkedTags.forEach((item) => {
     if (item != tag) {
      temp.push(item)
     }
    })
    this.checkedTags = temp
   } else {
    // 增加
    this.checkedTags.push(tag)
   }
   this.tableData.page = 1
   this.queryTableData();
  },
  handleFilterChange() {
   // this.queryTableData()
  },
  handleIntoBuild() {
   this.dialogVisible = true
   // this.$router.push({
   //   path: '/planGenerate/partnerCreate',
   //   query: {
   //     workspaceId: this.workspaceId,
   //     workspaceName: this.$route.query.workspaceName
   //   }
   // })
  },
  changeProcessStatus(val) {
   this.activeTag = val
   this.tableData.page = 1
   this.queryTableData()
   // this.queryTableCount()
  },
  queryTableData() {
   this.isLoading = true
   const tags = []
   if (this.activeName) {
    const item = this.tableData.tags.find((tag) => tag.name === this.activeName)
    const tagId = item?.id
    if (tagId && tagId !== 0) {
     tags.push(tagId)
    }
   }
   const param = {
    template_code: this.activeTag === '0' ? '' : this.activeTag,
    offset: this.tableData.page,
    limit: this.tableData.pageSize,
    sort_field: 'create_time',
    order: 'desc',
    status: this.formData.status,
    agent_scene: this.formData.agent_scene[1] || '',
    name: this.formData.name,
    agent_scene_code: this.checkedTypeTags.length ? this.checkedTypeTags : '',
    // 我的方案
    user_id: this.formData.createUserId !== '' ? this.formData.createUserId : (userInfo.userId || ''),
    tag_ids: this.checkedTags,
    contributors: this.formData.contributors,
    // 我的收藏
    myCollect: this.tableData.myCollect,
    is_expert_scene: 1,
    dev_mode: 'scheme'
   }
   if (this.selectedFilters.includes('myAbility')) {
    this.tableData.user_id = userInfo.userId
    // param.user_id = userInfo.userId
   } else {
    if (!this.formData?.createUserId) {
     this.tableData.user_id = ''
     param.user_id = ''
    }
   }
   if (this.selectedFilters.includes('myCollect')) {
    this.tableData.myCollect = '1'
    param.myCollect = '1'
   } else {
    this.tableData.myCollect = '0'
    param.myCollect = '0'
   }
   schemeTemplateList(param, { workspaceId: this.workspaceId })
    .then(async (res) => {
     const { data = {}, status } = res || {}
     if (status === 200 && data.code === 200) {
      const list = data.result?.items || []
      let results = {}
      let statisticsData = {}
      console.log('list---1',list)
      this.tableData.list = list?.map((item) => ({
       ...item,
      }))
      this.tableData.total = data.result.total
      this.$nextTick(() => {
       this.isLoading = false
      })
     } else {
      this.tableData.list = []
      this.tableData.total = 0
      this.isLoading = false
      this.$message({
       type: 'error',
       message: res.data?.msg || '接口异常!'
      })
     }
    })
    .catch((_err) => {
     this.tableData.list = []
     this.tableData.total = 0
     const { statusText = '请求异常' } = _err.request || _err
     this.$message({
      type: 'error',
      message: statusText || '接口异常!'
     })
    })
    .finally(() => {
     this.tableLoading = false
    })
  },
  handleSizeChange() {
   this.queryTableData()
  },
  handleCurrentChange() {
   this.queryTableData()
  },
  handleTabClick() {
   this.tableData.page = 1
   this.queryTableData()
  },
  saveRelHandle(id, status, name, actionName) {
   saveRel({
    biz_id: id,
    biz_type: status
   }).then((res) => {
    if (res.status === 200 && res.data.code === 200 && res.data.result) {
     if (status === 'collector') {
      this.$message({
       type: 'success',
       message: res.data?.msg || '收藏成功'
      })
     }
     this.queryTableData(this.justMine)
    } else {
     this.$message({
      type: 'error',
      message: res.data?.msg || '接口异常!'
     })
    }
   })
  },
  async handleGotoDetail(item) {
   console.log('item---1-1-1-22--', item)
   const sceneCode = item.agent_scene_code
   if (
    (item.visibility === 'private' && item.username !== userInfo.username && !this.isAdmin) ||
    (item.visibility === 'share' && !item.is_shared && !this.isAdmin)
   ) {
    this.$message({
     type: 'warning',
     message: '没有权限查看！'
    })
   } else {
    if (sceneCode) {
     try {
      const res = await this.getFromTemplate(item)
      if (res.id) {
       if (
        ['digital_twin_assistant_scene'].includes(sceneCode)
       ) {
        this.$router.push({
         path: '/planGenerate/partnerchat',
         query: {
          ...this.$route.query,
          status: item.status,
          id: res.id,
          fromMenu: '1',
         }
        })
       }
       else if (sceneCode === 'other_assistant_scene') {
        this.saveRelHandle(item.id, 'view', item.name, '开始')
        this.$router.push({
         path: '/planGenerate/planchat',
         query: {
          ...this.$route.query,
          id: res.id,
          status: item.status,
          fromMenu: '1',
         }
        })
       } else {
        // 其他场景类型进入原方案明细页面
        this.saveRelHandle(item.id, 'view', item.name, '开始')
        this.$router.push({
         path: '/planGenerate/ConfTaskPlanchat',
         query: {
          ...this.$route.query,
          id: res.id,
          fromMenu: '1',
          status: item.status
         }
        })
       }
      }
     } catch (e) {
      console.log('保存关系失败', e)
     }

    } else {
     this.$message({
      type: 'warning',
      message: '当前场景已删除！'
     })
    }
   }
  },
  statusMap(code, status) {
   if (
    [
     'device_ops_assistant_scene',
     'device_ops_assistant_scene',
     'artificial_handle_scene',
     'visit_leader_cognition_scene',
     'rule_generation_scene',
     'intelligent_conversation_scene',
     'sop_scene',
     'custom_cognition_assistant_scene'
    ].indexOf(code) > -1
   ) {
    if (status === 1) {
     return '生成方案'
    } else if (status === 2) {
     if (code === 'custom_cognition_assistant_scene') {
      return '思维图生成'
     } else {
      return '思维树生成'
     }
    } else if (status === 3) {
     return '多模态数据对齐'
    } else if (status === 4) {
     return '能力代码生成'
    } else if (status === 5) {
     return '智能能力测试与迭代'
    } else {
     return '--'
    }
   } else if (code === 'other_assistant_scene') {
    if (status === 1) {
     return '生成方案'
    } else {
     return '--'
    }
   } else if (code === 'digital_twin_assistant_scene') {
    // 针对数字孪生
    return status === 1
     ? '生成方案'
     : status === 2
      ? '执行任务'
      : status === 3
       ? '智能能力测试与迭代'
       : '--'
   } else if (code === 'operational_optimization_scene') {
    // 针对数学模型生成
    return status === 1
     ? '生成方案'
     : status >= 2 && status <= 6
      ? '执行任务'
      : status === 7
       ? '智能能力测试与迭代'
       : '--'
   } else {
    if (status === 1) {
     return '生成方案'
    } else if (status === 2) {
     return '生成任务'
    } else if (status === 3) {
     return '执行任务'
    } else {
     return '--'
    }
   }
  },
  collectDebounce: debounce(function (item) {
   this.collectHandle(item)
  }, 1000),
  async collectHandle(item) {
   if (this.loading) {
    return false
   }
   this.loading = true
   if (!item.is_collected) {
    await this.saveRelHandle(item.id, 'collector')
    this.loading = false
   } else {
    await deleteRel({
     biz_id: item.id,
     biz_type: 'collector'
    })
     .then((res) => {
      if (res.status === 200 && res.data.code === 200 && res.data.result) {
       this.$message({
        type: 'success',
        message: res.data?.msg || '取消收藏'
       })
       this.queryTableData(this.justMine)
      } else {
       this.$message({
        type: 'error',
        message: res.data?.msg || '接口异常!'
       })
      }
     })
     .finally(() => {
      this.loading = false
     })
   }
  },
  selectUserFn(data) {
   let formattedUsers = []
   if (data?.length > 0) {
    formattedUsers = data.map((user, index) => {
     const label = `${user.nickname}(${user.loginName})`
     return { key: index + 1, label }
    })
   }
   return formattedUsers
  },
  async handleCommand(type, item) {
   if (type === 'task') {
    this.$router.push({
     path: '/planGenerate/task',
     query: {
      ...this.$route.query,
      name: item.name,
      id: item.id,
      agent_scene_name: item.agent_scene_name,
      create_time: item.create_time,
      desc: item.description,
      fromMenu: '1',
      agent_scene_code_name: item.agent_scene_code_name
     }
    })
   } else if (type === 'edit') {
    this.editId = item.id
    this.editData = item
    this.$router.push({
     path: '/planGenerate/edit',
     query: { ...this.$route.query, id: item.id, fromMenu: '1', }
    })
   } else if (type === 'remove') {
    this.handleDelete(item)
   } else if (type === 'develop') {
    this.devPersonInfo = item.developer
    this.curId = item.id + ''
   } else if (type === 'top') {
    this.tableLoading = true
    await updateSchemeSort(item.id, 1, { workspaceId: this.formData.workspaceId })
    this.queryTableData(this.justMine)
   } else if (type === 'cancelTop') {
    this.tableLoading = true
    await updateSchemeSort(item.id, 0, { workspaceId: this.formData.workspaceId })
    this.queryTableData(this.justMine)
   }
  },
  // async checkInto(item) {
  //   if (
  //     (item.visibility === 'private' && item.username !== userInfo.username && !this.isAdmin) ||
  //     (item.visibility === 'share' && !item.is_shared && !this.isAdmin)
  //   ) {
  //     this.$message({
  //       type: 'warning',
  //       message: '没有权限查看！'
  //     })
  //   } else {
  //     if (item.agent_scene_code) {
  //       if (item.agent_scene_code === 'digital_twin_assistant_scene') {
  //         this.saveRelHandle(item.id, 'view', item.name, '专家进入')
  //         this.$router.push({
  //           path: '/planGenerate/wllsExpertPlanchat',
  //           query: {
  //             ...this.$route.query,
  //             status: item.status,
  //             id: item.id,
  //             enterType: 'expert',
  //             ability_id: item?.ability_id
  //           }
  //         })
  //       } else {
  //         if (item.agent_scene_code === 'operational_optimization_scene') {
  //           this.saveRelHandle(item.id, 'view', item.name, '开始')
  //           this.$router.push({
  //             path: '/planGenerate/hqwPlanchat',
  //             query: {
  //               ...this.$route.query,
  //               id: item.id,
  //               status: item.status,
  //               ability_id: item?.ability_id
  //             }
  //           })
  //         } else {
  //           this.saveRelHandle(item.id, 'view', item.name, '开始')
  //           this.$router.push({
  //             path: '/planGenerate/planchat',
  //             query: {
  //               ...this.$route.query,
  //               id: item.id,
  //               status: item.status,
  //               ability_id: item?.ability_id
  //             }
  //           })
  //         }
  //       }
  //     } else {
  //       this.$message({
  //         type: 'warning',
  //         message: '当前场景已删除！'
  //       })
  //     }
  //   }
  // },
  developFn(item) {
   this.saveRelHandle(item.id, 'view', item.name, '研发进入')
   this.$router.push({
    path: '/planGenerate/wllsDevPlanchat',
    query: {
     ...this.$route.query,
     status: item.status,
     id: item.id,
     fromMenu: '1',
     enterType: 'develop',
     ability_id: item?.ability_id
    }
   })
  },
  expertFn(item) {
   this.saveRelHandle(item.id, 'view', item.name, '专家进入')
   this.$router.push({
    // path: '/planGenerate/wllsExpertPlanchat',
    path: '/planGenerate/ConfTaskPlanchat',
    query: {
     ...this.$route.query,
     status: item.status,
     id: item.id,
     fromMenu: '1',
     enterType: 'expert',
     ability_id: item?.ability_id
    }
   })
  },
  handleClose(val) {
   if (val) {
    this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
     confirmButtonText: '确定',
     cancelButtonText: '取消',
     showCancelButton: false,
     type: 'success'
    })
     .then(() => {
      this.queryTableData(this.justMine)
      // this.queryTableCount()
     })
     .catch(() => {
      this.queryTableData(this.justMine)
      // this.queryTableCount()
     })
   }
  },
  handleDelete(row) {
   this.$confirm('此操作将删除该方案及任务，是否继续?', '删除', {
    customClass: 'last-dialog',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
   })
    .then(() => {
     this.tableLoading = true
     DeleteScheme({ id: row.id })
      .then((res) => {
       this.tableLoading = false
       if (res.status === 200 && res.data.code === 200) {
        this.$message({
         message: '删除成功',
         type: 'success'
        })
        this.queryTableData()
       } else {
        this.$message({
         type: 'error',
         message: res.data?.msg || '接口异常!'
        })
       }
      })
      .finally(() => {
       this.tableLoading = false
      })
    })
    .catch(() => {
     this.tableLoading = false
     // this.queryTableData()
    })
  }
 }
}
</script>

<style lang="scss" scoped>
.solid_class {
 cursor: pointer;
 padding: 4px 8px;
 margin: 0 12px;
 background: #F2F3F5;
 border-radius: 2px;
 height: 30px;
 width: 30px;
 display: flex;
 justify-content: center;
 align-items: center;

 i {
  color: #4068D4;
 }
}

.el-dropdown-menu__item.tag-selected {
 color: #409eff !important;
 background-color: #ecf5ff !important;
}

.center_flex {
 flex: 1;

 :deep(.el-tabs__nav-wrap::after) {
  //不要下面那条杠
  content: none;
  /* 移除伪元素内容 */
  background: none;
  /* 移除背景 */
 }

 :deep(.el-tabs--top) {
  line-height: 40px;
 }

 :deep(.el-tabs__item) {
  border-radius: 16px;
  background: #F6F7FB;
  height: 30px;
  line-height: 30px;
  padding: 4px 12px !important;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  margin-right: 8px;
 }

 :deep(.el-tabs__item.is-top:last-child) {
  margin-right: 0px;
 }

 :deep(.is-active) {
  background: #4068D4;

  .tabTitle {
   color: #FFFFFF;
  }
 }

 :deep(.el-tabs__active-bar) {
  display: none;
 }

 :deep(.el-tabs__nav-prev) {
  line-height: inherit;
 }

 :deep(.el-tabs__nav-next) {
  line-height: inherit;
 }

 :deep(.el-tabs__header) {
  margin-bottom: 0px;
  width: -webkit-fill-available;
 }
}

.cardEmpty {
 height: calc(100% - 96px);
 margin-top: 16px;
}

.flex_bt {
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 0px 40px 0 20px;
}

.box-card {
 border: none;
 overflow: unset;
 margin-bottom: 16px;

 :deep(.el-card__body) {
  padding: 16px 0px 16px 0px;
  border-bottom: 1px solid #e8e8e8;
 }

 .planSearch {
  background-color: #fff;
  // margin-bottom: 16px;
  // padding: 0px 20px 0px;
  position: relative;

  .quick-search {
   display: flex;

   :deep(.el-checkbox-group) {
    display: flex;

    align-items: center;
    margin-right: 24px;
   }
  }

  .search {

   // margin-top: 10px;
   .planSearch1 {
    padding: 0 20px;
   }

   .footer-search {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-input {
     margin: 5px 0px 5px 0px;
    }
   }
  }

  .searchIcon {
   position: absolute;
   left: 50%;
   bottom: -10px;
   transform: translateX(-50%);

   .searchIconTaggle {
    width: 40px;
    height: 20px;
    background: #d9e6f8;
    border-radius: 2px;
    position: relative;
    border: 1px solid #f2f3f5;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4068d4;
    cursor: pointer;

    &:hover {
     background: #a1bbef;
    }
   }
  }

  .headerTitle {
   font-weight: bold;
   color: #323233;
   line-height: 26px;
   font-size: 18px;
   padding: 14px 20px;
  }

  .button-last {
   line-height: 14px;
  }

  .search {
   position: relative;
   overflow: hidden;
  }
 }
}

::v-deep .el-card__header {
 border: none;
 padding: 16px 0px 0px;
}

::v-deep .el-cascader {
 line-height: 30px !important;
 height: 30px;

 .el-input {
  height: 30px;
 }
}

::v-deep .el-tooltip__popper {
 &.is-dark {
  background: rgba($color: #323233, $alpha: 0.8) !important;
 }
}

::v-deep .el-button.is-disabled {
 color: rgba($color: #4068d4, $alpha: 0.4);
}

::v-deep .el-button--info {
 background-color: #f2f3f5;
 color: #4068d4;
 border-color: #f2f3f5;
 display: flex;
 align-items: center;

 &.is-disabled {
  opacity: 0.4;
  background-color: #f2f3f5 !important;
  color: #4068d4;
  border-color: #f2f3f5 !important;
 }

 &:hover {
  background-color: #ebecf0;
  border-color: #ebecf0;
  color: #4068d4;
 }

 &:active {
  background-color: #dcdde0;
  border-color: #dcdde0;
 }
}

::v-deep .el-button--text {
 background-color: #fff;
 color: #4068d4;
 border-color: #fff;
 padding: 6px 8px;
 border-radius: 2px;

 &.is-disabled {
  opacity: 0.4;
  background-color: #f2f3f5 !important;
  color: #4068d4;
  border-color: #f2f3f5 !important;
 }

 &:hover {
  background-color: #ebecf0;
  border-color: #ebecf0;
  color: #4068d4;
 }

 &:active {
  background-color: #dcdde0;
  border-color: #dcdde0;
 }
}

::v-deep .el-button--mini {
 line-height: 0px !important;
 padding: 7px 6px !important;

 img {
  height: 16px;
  margin-top: -2px;
 }
}

:deep(.el-input__inner) {
 border-radius: 2px;
 height: 30px;
 line-height: 30px;
}

.ability-list {
 height: 100%;
 width: 100%;
 display: flex;
 flex-direction: column;

 .containerCardSmall {
  display: flex;
  flex: 1;
  overflow: hidden;

  .containerCard {
   gap: 12px;
  }

  .left-content {
   background-color: #fff;
   width: 70%;
   min-width: 250px;
   height: 100%;
   overflow-y: auto;
   border-radius: 4px;

   .scene-container {
    width: 100%;
   }

   .show {
    height: 100%;
   }
  }

  .left-content-max {
   width: 100%;
  }

  .right-content {
   padding: 24px 16px 0px 16px;
   background-color: #fff;
   width: 30%;
   // min-width: 450px;
   // height: 100%;
   overflow: auto;
   border-radius: 4px;

   .user {
    display: flex;
    justify-content: flex-start;
    padding: 24px 16px;
    border-bottom: 1px solid #ebecf0;

    span {
     font-family: PingFangSC, PingFang SC;
     font-weight: 500;
     font-size: 28px;
     color: #323233;
     text-align: left;
     font-style: normal;
    }

    img {
     width: 80px;
     height: 80px;
    }

    .user-info {
     margin-left: 20px;
     display: flex;
     flex-direction: column;
     justify-content: space-around;
     align-items: center;

     .title {
      display: flex;
      align-items: center;

      span {
       background: #e6ecff;
       border-radius: 2px;
       padding: 0 8px;
       font-family: PingFangSC, PingFang SC;
       font-weight: 400;
       font-size: 14px;
       color: #3455ad;
      }

      i {
       margin-left: 10px;
       cursor: pointer;
       font-size: 14px;
      }
     }
    }
   }

   .knowledge-benefits,
   .knowledge-accumulation {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 22px 16px 0;
    border-bottom: 1px solid #ebecf0;

    .title {
     img {
      width: 24px;
      height: 24px;
      margin-right: 8px;
     }

     span {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
     }
    }

    .info {
     display: flex;
     justify-content: space-between;
     width: 100%;
     padding: 20px 16px;

     .info-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      span {
       font-family: PingFangSC, PingFang SC;
       font-weight: 600;
       font-size: 24px;
       color: #323233;
       line-height: 33px;
       text-align: right;
       font-style: normal;
      }

      .value {
       font-family: PingFangSC, PingFang SC;
       font-weight: 400;
       font-size: 14px;
       color: #646566;
      }
     }
    }
   }

   .knowledge-accumulation {
    .info {
     flex-wrap: wrap;
     padding: 20px 0px;
     padding-bottom: 0;

     .info-item {
      width: 33%;
      margin-bottom: 20px;
     }
    }
   }

   .buttons {
    display: flex;
    justify-content: space-between;
    padding: 24px 16px;

    .my-plan,
    .my-collect {
     cursor: pointer;
     display: flex;
     align-items: center;
     padding: 16px 16px 16px 15px;
     width: 162px;
     background: #ebeffa;

     img {
      width: 18px;
      height: 18px;
      margin-right: 7px;
     }
    }

    .my-collect {
     background: linear-gradient(132deg, #fffbd3 0%, #ffe9d9 100%), #fff2eb;
    }
   }
  }

  .right-content-min {
   width: 0px !important;
   margin: 0;
   padding: 0;
  }
 }

 .main {
  height: 100%;
  display: flex;
  overflow: hidden;

  .icon-content {
   cursor: pointer;
   display: flex;
   align-items: center;

   i {
    font-size: 20px;
   }
  }

  .icon-content-card {
   right: 0;
  }
 }

 .search-content {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  justify-content: space-between;

  span {
   font-family: PingFangSC, PingFang SC;
   font-weight: 500;
   font-size: 16px;
   color: #323233;
   font-style: normal;
  }

  .input-with-select {
   width: 40%;
  }
 }

 .right-content {
  display: flex;
  align-items: center;
 }

 .tab-content {
  flex: 1;
  background-color: #fff;
  // height: calc(100% - 96px);
  max-height: calc(100vh - 275px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .tabs-list {
   :deep(.el-tabs__nav-wrap) {
    display: flex;
    align-items: center;
   }

   :deep(.el-tabs__nav-wrap)::after,
   :deep(.el-tabs__active-bar) {
    display: none;
   }

   :deep(.el-tabs__item) {
    border-radius: 2px;
    height: auto;
    line-height: 20px;
    padding: 5px 16px;
    margin-right: 8px;
   }

   :deep(.is-active) {
    background: #eff3ff;
   }
  }

  .container {
   overflow-y: auto;
   display: flex;
   padding: 0px 20px;
   flex-wrap: wrap;
   width: 100%;
   flex: 1;
   justify-content: flex-start;
   align-content: flex-start;

   .container-item {
    display: flex;
    flex-direction: column;
    // flex-basis: calc(33.3333% - 15px);
    border: 1px solid #dcdde0;
    border-radius: 4px;
    padding: 16px;
    justify-content: space-between;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease; // 添加过渡效果
    background-color: #fff;

    &:hover {
     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); // 更柔和的阴影效果
     border-color: #4068d4;
    }

    span {
     font-family: PingFangSC, PingFang SC;
     font-weight: 400;
     font-size: 14px;
     color: #646566;
     text-align: left;
     font-style: normal;
    }

    .title {
     font-weight: 500;
     font-size: 18px;
     color: #1d2129;
     font-style: normal;
     cursor: pointer;
     padding: 0 12px;
     transition: color 0.3s ease; // 添加过渡效果

     &:hover {
      color: #4068d4; // 悬浮时的颜色
     }
    }

    .desc {
     margin: 8px 0;
     color: #323233;
     padding: 0 12px;
     display: -webkit-box;
     -webkit-box-orient: vertical;
     -webkit-line-clamp: 2;
     overflow: hidden;
     text-overflow: ellipsis;
     word-break: break-all; // 确保长单词或URL不会破坏布局
    }

    .info {
     display: flex;
     flex-direction: column;
     border-bottom: 1px solid #ebecf0;
     padding: 0 12px;

     .info-item {
      margin-bottom: 8px;

      img {
       height: 16px;
       width: 16px;
       margin-right: 4px;
      }

      span {
       font-size: 14px;
       color: #646566;
      }
     }
    }

    .icons {
     display: flex;
     justify-content: space-between;
     padding: 0 12px;

     .icons-item {
      display: flex;
      align-items: center;
      margin-top: 8px;

      img {
       height: 16px;
       width: 16px;
       margin-right: 4px;
      }
     }
    }
   }

   .container-full {
    flex-basis: calc((100% - 3 * 12px) / 4);
    flex-shrink: 0;
    /* 禁止压缩 */
   }

   .container-nofull {
    flex-basis: calc(33.3333% - 15px);
   }

   .build {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
     width: 60px;
     height: 60px;
    }

    span {
     font-size: 18px;
     color: #4068d4;
     margin-top: 12px;
    }
   }
  }

  // }
  // }
 }
}

.cartItem {
 // flex-basis: calc(33.3333% - 12px);
 // width: 25%;
 max-height: 323px;
 background: #ffffff;
 border-radius: 4px;
 border: 1px solid #dcdde0;
 overflow: hidden;
 cursor: pointer;
 display: flex;
 flex-direction: column;
 justify-content: space-between;

 .cartItemBox {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
  position: relative;
 }

 &:hover {
  box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
  border: 1px solid #406bd4;
 }

 &:active {
  box-shadow: 0px 8px 16px 1px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  border: 1px solid #406bd4;
 }

 .itemHeader {
  font-size: 16px;
  color: #323232;
  line-height: 24px;
  padding: 13px 16px;
  font-weight: bold;
  border-bottom: 1px solid #ebecf0;

  .itemHeaderTop {
   display: flex;
   justify-content: space-between;
   align-items: center;

   .el-icon-warning-outline {
    color: #4068d4;
    padding: 0 0px 0 8px;
    cursor: pointer;
   }
  }

  .itemHeaderTit {
   width: 100%;
   flex: 1;
   display: flex;
   flex-direction: row;
   align-items: center;
   cursor: pointer;
   overflow: hidden; //超出的文本隐藏
   text-overflow: ellipsis; //溢出用省略号显示
   white-space: nowrap; // 默认不换行；

   &:hover {
    .title {
     color: #4068d4;
    }
   }

   &.isdisabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
   }

   .title {
    max-width: calc(100%);
    font-size: 16px;
    font-weight: bold;
    color: #323233;
    cursor: pointer;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; // 默认不换行；
   }
  }

  .itemHeaderBtn {
   display: flex;
   flex-direction: row;
   justify-content: end;
   align-items: center;
  }
 }

 .itemContent {
  padding: 12px 16px;

  .contentDesc {
   display: flex;
   flex-direction: row;
   justify-content: flex-start;
   align-items: center;
   margin-top: 8px;
   font-weight: normal;
   color: #646566;
   line-height: 22px;
   font-size: 14px;

   .descLabel {
    color: #646566;
    word-break: keep-all;
   }

   .descValue {
    color: #323233;
    line-height: 22px;

    .contribution {
     font-size: 14px;

     ::v-deep .tagslist {
      position: relative;

      .tagslistLast {
       position: relative;

       &::after {
        width: 1px !important;
        content: '';
        position: absolute;
        left: 0px;
        width: 1px;
        height: 12px;
        background: #c8c9cc;
        top: 6px;
       }
      }

      &:last-child {
       &::after {
        width: 0px;
       }
      }

      &::after {
       content: '';
       position: absolute;
       right: 0px;
       width: 1px;
       height: 12px;
       background: #c8c9cc;
       top: 6px;
      }
     }

     ::v-deep .el-tag {
      color: #323233;
      background: transparent;
      font-size: 14px;
      border: none;
      padding: 0 3px;
      position: relative;
      padding-right: 5px;
      margin-right: 0px;
     }

     ::v-deep .el-button {
      background: transparent;
      border: none;
     }
    }

    .descValueMax {
     max-width: 100%;
     display: -webkit-box;
     -webkit-box-orient: vertical;
     -webkit-line-clamp: 2;
     line-clamp: 2;
     overflow: hidden;
     text-overflow: ellipsis;
     word-break: break-all;
    }
   }
  }

  .itemProcess {
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   align-items: center;
   padding-top: 12px;
   padding-bottom: 4px;

   .processItem {
    width: 36px !important;
    height: 36px;
    word-break: keep-all;
    border-radius: 4px;
    position: relative;
    background: rgba(235, 236, 240, 0.8);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .el-icon-success {
     position: absolute;
     top: -4px;
     right: -4px;
     color: #39ab4c;
     font-size: 14px;
    }

    .process-span {
     margin-top: 0px;
    }

    &.success {
     background: #e6faea;

     .process-icon {
      color: #2ca941 !important;
     }
    }

    &.running {
     background: #e5eaff;

     .process-icon {
      color: #416cde !important;
     }
    }

    .process-icon {
     width: 16px;
     height: 16px;
     color: #7d7e80;
    }
   }

   .processLine {
    flex: 1;
    margin: 0px 8px;
    height: 1px;
    background: #c8c9cc;
   }
  }
 }

 .itemFooter {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #ebecf0;
  font-size: 14px;
  color: #646566;

  .create-template,
  .template-button {
   width: 100%;
   border-radius: 8px;
  }

  & .footerLeft {
   display: flex;

   & .item {
    margin-right: 16px;
   }
  }

  >div {
   display: flex;
   align-items: center;

   img {
    height: 16px;
    width: 16px;
    margin-right: 4px;
   }
  }
 }
}

.cartItem-full {
 flex-basis: calc((100% - 3 * 12px) / 4);
 flex-shrink: 0;
 /* 禁止压缩 */
}

.cartItem-nofull {
 flex-basis: calc((100% - 2 * 12px) / 3);
}

.sence-tag {
 display: inline-flex;
 padding: 0 8px;
 height: 24px;
 border-radius: 2px;
 background: #ebf9ff;
 color: #318db8;
}

.separate {
 width: 1px;
 height: 12px;
 margin: 0 8px;
 background: #c8c9cc;
}

.avator {
 width: 28px;
 height: 28px;
 line-height: 28px;
 border-radius: 50%;
 font-size: 10px;
 font-weight: 600;
 text-align: center;
 background: #e6ecff;
 color: #4068d4;
}

// 媒体查询，调整不同屏幕尺寸下的卡片宽度
@media (max-width: 1500px) {
 .cartItem-nofull {
  flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
 }

 .container-nofull {
  flex-basis: calc((100% - 2 * 12px) / 3) !important;
 }

 .cartItem-full {
  flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
 }

 .container-full {
  flex-basis: calc((100% - 2 * 12px) / 3) !important;
 }

}

@media (max-width: 1280px) {
 .cartItem-nofull {
  flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
 }

 .container-nofull {
  flex-basis: calc((100% - 2 * 12px) / 3) !important;
 }

 .cartItem-full {
  flex-basis: calc((100% - 2 * 12px) / 3); // 一排显示3个卡片
 }

 .container-full {
  flex-basis: calc((100% - 2 * 12px) / 3) !important;
 }
}

@media (max-width: 1340px) {
 .cartItem-nofull {
  flex-basis: calc((100% - 1 * 12px) / 2); // 一排显示2个卡片
 }

 .container-nofull {
  flex-basis: calc((100% - 1 * 12px) / 2) !important;
 }

 .cartItem-full {
  flex-basis: calc((100% - 1 * 12px) / 2); // 一排显示2个卡片
 }

 .container-full {
  flex-basis: calc((100% - 1 * 12px) / 2) !important;
 }

 .right-content {
  width: 50% !important;
 }
}

@media (max-width: 1122px) {
 .cartItem-nofull {
  flex-basis: calc(100% - 0px);
 }

 .container-nofull {
  flex-basis: calc(100% - 0px) !important;
 }

 .cartItem-full {
  flex-basis: calc(100% - 0px);
 }

 .container-full {
  flex-basis: calc(100% - 0px) !important;
 }
}
</style>
<style lang="scss"></style>
