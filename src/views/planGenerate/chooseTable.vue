<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="对齐方式配置"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="40%"
    >
      <div
        v-if="
          $route.query.agent_scene_code === 'device_ops_assistant_scene' ||
          $route.query.agent_scene_code === 'visit_leader_cognition_scene'
        "
      >
        <!-- <div style="color:#323232;">请选择和方案内容进行对齐的目标源</div> -->
        <el-alert title="请选择和方案内容进行对齐的目标源" type="success"></el-alert>
        <div style="margin-top: 16px">
          <div
            v-if="$route.query.agent_scene_code !== 'visit_leader_cognition_scene'"
            style="display: flex; flex-direction: row; align-items: center; margin-bottom: 16px"
          >
            <div style="width: 120px">
              <el-checkbox v-model="formData.cangku" @change="changeCangku">数据仓库：</el-checkbox>
            </div>
            <el-select
              v-model="formData.chooseData"
              clearable
              :multiple="conSelect === 'multiple' ? true : false"
              style="width: 100%; flex: 1"
              placeholder="请选择"
            >
              <el-option
                v-for="item in tablesList"
                :key="item.table"
                :label="item.table"
                :value="item.table"
              >
                <span style="float: left">{{ item.table }}</span>
                <span style="float: right; color: #8492a6; font-size: 12px">{{
                  item.description
                }}</span>
              </el-option>
            </el-select>
          </div>
          <div style="display: flex; flex-direction: row; align-items: center; margin-bottom: 16px">
            <div style="width: 120px">
              <el-checkbox v-model="formData.api" @change="changeAPI">算法/系统api：</el-checkbox>
            </div>
            <el-select
              ref="tagsSelect"
              v-model="formData.tag_ids"
              style="width: 100%; flex: 1"
              multiple
              filterable
              remote
              placeholder="请选择"
              :remote-method="searchTags"
              clearable
              :popper-append-to-body="false"
            >
              <!-- <el-option
                  key="55555"
                  label="我是长发我是长发还是剪短发后的世界复活节岛上我是长发还是剪短发后的世界复活节岛上我是长发还是剪短发后的世界复活节岛上我是长发还是剪短发后的世界复活节岛上还是剪短发后的世界复活节岛上"
                  value="898"
                /> -->
              <el-option
                v-for="item in tagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div style="display: flex; flex-direction: row; align-items: center; margin-bottom: 16px">
            <div style="width: 120px">
              <el-checkbox v-model="formData.manual_input">人工输入</el-checkbox>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <el-form
          ref="form"
          label-position="right"
          label-width="99px"
          :model="formData"
          :rules="rules"
        >
          <el-form-item label="数据表:" prop="chooseData">
            <el-select
              v-model="formData.chooseData"
              :multiple="conSelect === 'multiple' ? true : false"
              style="width: 100%"
              placeholder="请选择"
            >
              <el-option
                v-for="item in tablesList"
                :key="item.table"
                :label="item.table"
                :value="item.table"
              >
                <span style="float: left">{{ item.table }}</span>
                <span style="float: right; color: #8492a6; font-size: 12px">{{
                  item.description
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="createSubmit"
          >确定</el-button
        >
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTablesList, queryDuiqiTags } from '@/api/planGenerateApi.js';

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    conSelect: {
      type: String,
      default: ''
    },
    editData: {
      type: Object,
      default: () => {
        return { table: '', tag_ids: [] };
      }
    }
  },
  data() {
    return {
      showFlag: false,
      formData: {
        chooseData: this.conSelect === 'multiple' ? [] : '',
        cangku: false,
        api: false,
        manual_input: false
      },
      tablesList: [],
      rules: {
        chooseData: [{ required: true, message: '请选择数据表', trigger: 'blur' }]
      },
      loading: false,
      tagList: [],
      allTagList: []
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val;
          console.log('回显数据', this.editData);
          if (this.editData.manual_input) {
            this.formData.manual_input = true;
          } else {
            this.formData.manual_input = false;
          }
          this.queryDataInfo();
          this.searchTags2('');
        } else {
          this.showFlag = false;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    queryDataInfo() {
      this.loading = true;
      const params = { dbType: '' };
      if (this.$route.query.agent_scene_code === 'custom_cognition_assistant_scene') {
        params.dbType = 'got';
      }
      if (
        this.$route.query.agent_scene_code === 'device_ops_assistant_scene' ||
        this.$route.query.agent_scene_code === 'visit_leader_cognition_scene'
      ) {
        params.dbType = 'static';
      }
      if (this.$route.query.agent_scene_code === 'device_ops_assistant_scene-v1') {
        params.dbType = 'run';
      }
      console.log('选择表数据', params);
      getTablesList(params)
        .then((res) => {
          this.loading = false;
          if (res.status === 200 && res.data.success) {
            this.tablesList = res.data.data?.list || [];
            console.log('0000', this.editData);
            if (this.editData && this.editData.table) {
              this.formData.cangku = true;
              this.formData.chooseData = this.editData.table;
            } else {
              this.formData.cangku = false;
              this.formData.chooseData = this.conSelect === 'multiple' ? [] : '';
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchTags(keyword) {
      queryDuiqiTags({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
          }
        } else {
          this.tagList = [];
        }
      });
    },
    searchTags2(keyword) {
      queryDuiqiTags({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
            if (this.editData && this.editData.tag_ids?.length) {
              this.formData.api = true;
              this.formData.tag_ids = this.editData.tag_ids || [];
            } else {
              this.formData.api = false;
              this.formData.tag_ids = [];
            }
          }
        } else {
          this.tagList = [];
        }
      });
    },
    changeCangku(val) {
      if (!val) {
        this.formData.chooseData = this.conSelect === 'multiple' ? [] : '';
      }
    },
    changeAPI(val) {
      if (!val) {
        this.formData.tag_ids = [];
      }
    },
    onClose() {
      this.formData.chooseData = this.conSelect === 'multiple' ? [] : '';
      this.formData.tag_ids = [];
      this.formData.cangku = false;
      this.formData.api = false;
      this.formData.manual_input = false;
      this.loading = false;
      this.showFlag = false;
      if (
        this.$route.query.agent_scene_code !== 'device_ops_assistant_scene' &&
        this.$route.query.agent_scene_code !== 'visit_leader_cognition_scene'
      ) {
        this.$refs.form.clearValidate();
      }
      this.$emit('close');
    },
    createSubmit() {
      if (
        this.$route.query.agent_scene_code === 'device_ops_assistant_scene' ||
        this.$route.query.agent_scene_code === 'visit_leader_cognition_scene'
      ) {
        console.log(this.formData);
        if (this.formData.api || this.formData.cangku) {
          if (
            (this.formData.api && this.formData.tag_ids.length) ||
            (this.formData.cangku && this.formData.chooseData)
          ) {
            const filters = this.tablesList.filter(
              (table) => table.table === this.formData.chooseData
            );
            if (filters.length) {
              this.$emit(
                'close',
                filters[0]?.ddl,
                this.formData.chooseData,
                this.formData.tag_ids,
                this.formData.manual_input
              );
            } else {
              this.$emit(
                'close',
                'close',
                this.formData.chooseData,
                this.formData.tag_ids,
                this.formData.manual_input
              );
            }
          } else {
            this.$message({
              type: 'error',
              message: '请选择配置并配置数据源后再进行对齐!'
            });
          }
        } else {
          if (this.formData.manual_input) {
            this.$emit(
              'close',
              'close',
              this.formData.chooseData,
              this.formData.tag_ids,
              this.formData.manual_input
            );
          } else {
            this.$message({
              type: 'error',
              message: '请选择任意一个配置后再进行对齐!'
            });
          }
        }
      } else {
        this.$refs.form.validate((validate) => {
          if (validate) {
            if (this.conSelect === 'multiple') {
              // console.log(this.formData.chooseData)
              // console.log(this.tablesList)
              const filtered = this.tablesList
                .filter((item) => this.formData.chooseData.includes(item.table))
                .map((item) => item.ddl);
              if (filtered.length) {
                this.formData.chooseData = '';
                this.$refs.form.clearValidate();
                this.$emit('close', JSON.stringify(filtered));
              }
            } else {
              const filters = this.tablesList.filter(
                (table) => table.table === this.formData.chooseData
              );
              console.log('filters[0].ddl', filters[0].ddl);
              if (filters.length) {
                this.formData.chooseData = '';
                this.$refs.form.clearValidate();
                this.$emit('close', filters[0].ddl);
              }
            }
          } else {
            this.loading = false;
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-select-dropdown__item) {
  max-width: 700px;
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;

  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;

  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;
}
</style>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
