<template>
  <div class="body_flex">
    <div class="optHeader2 center_flex">
      <el-tabs v-model="optHeaderTab">
        <el-tab-pane
          v-for="item in optHeaderList"
          :key="item.name"
          :label="item.label"
          :name="item.name"
        >
          <template slot="label">
            <div class="tabTitle">{{ item.label }}</div>
          </template>
        </el-tab-pane>
      </el-tabs>
      <div class="rightTitleOpt-tab">
        <el-dropdown>
          <div class="solid_class">
            <i class="el-icon-more"></i>
          </div>
          <el-dropdown-menu slot="dropdown">
            <!-- 直接渲染所有标签 -->
            <el-dropdown-item
              v-for="tag in optHeaderList"
              :key="tag.name"
              :command="tag.name"
              :class="{ 'tag-selected': optHeaderTab === tag.name }"
              @click.native="()=>{changeoptHeader2Tab(tag)}"
            >{{ tag.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="flex">
      <div class="flex_info">
        <div v-for="(item, index) in demand" :key="index" class="demand">
         <div class="flex_sp" v-show="optHeaderTab == '1'">
          <div class="flex_div">{{ item.title }}</div>
          <div>
            <i
              v-if="!item.checkFlag"
              class="el-icon-check cur"
              style="margin-right: 20px;"
              @click="save(item)"
            ></i>
            <i v-if="!item.checkFlag" class="el-icon-close cur" @click="reset(item)"></i>
            <i
              v-if="item.checkFlag"
              :class="{ 'cust-disabled': isReadOnly }"
              class="el-icon-edit cur"
              @click="editRequirement(item)"
            ></i>
          </div>
        </div>
        <div v-if="item.checkFlag && optHeaderTab == '1'" class="bgFont">
          <MyEditorPreview
            id="MyFanganEditorSikao"
            ref="MyFanganEditorSikao"
            :md-content="item[item.fieldKey]"
          ></MyEditorPreview>
        </div>
        <el-input
          v-else-if="optHeaderTab == '1'"
          type="textarea"
          maxlength="8000"
          show-word-limit
          :placeholder="index ?  '请输入需求说明':'请输入需求背景'"
          rows="3"
          class="input_class"
          v-model="item[item.fieldKey]"></el-input>
        </div>
        <div class="flex_sp" v-show="optHeaderTab == '2'">
          <div class="flex_div">关联知识库</div>
          <div class="adddiv cur" :class="{ 'cust-disabled': isReadOnly }" @click="addzs">
            <span class="addicon">+</span>
            <span class="addfont">添加</span>
          </div>
        </div>
        <div class="zslist-container" v-show="optHeaderTab == '2'">
          <div class="zslist" v-for="(item, index) in content.knowledges" :key="item.id">
            <div class="card">
              <div class="card-content">
                <i class="el-icon-reading"></i>
                <span class="card-text" :title="item.name">{{ item.name }}</span>
              </div>
              <div class="card-actions">
                <el-button size="mini" class="preview-btn" @click="showZs()">
                  <i class="el-icon-view"></i>预览
                </el-button>
                <el-button size="mini" class="delete-btn" @click="delZs(index)" :disabled="isReadOnly">
                  <i class="el-icon-delete"></i>删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex_sp" v-show="optHeaderTab == '3'">
          <div class="flex_div">关联文件</div>
          <div class="adddiv cur" :class="{ 'cust-disabled': isReadOnly }" @click="up">
            <span class="addicon">+</span>
            <span class="addfont">添加</span>
          </div>
        </div>
        <div class="zslist-container" v-show="optHeaderTab == '3'">
          <div class="zslist" v-for="(item, index) in content.files" :key="item.fileId">
            <div class="card">
              <div class="card-content">
                <i class="el-icon-paperclip"></i>
                <span class="card-text" :title="item?.name">{{ item?.name }}</span>
              </div>
              <div class="card-actions">
                <el-button size="mini" class="preview-btn" @click="downfile(item?.url || item.path)">
                  <i class="el-icon-view"></i>预览
                </el-button>
                <el-button size="mini" class="delete-btn" @click="delUp(index)" :disabled="isReadOnly">
                  <i class="el-icon-delete"></i>删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex_sp" v-show="optHeaderTab == '4'">
          <div class="flex_div">关联能力</div>
          <div class="adddiv cur" :class="{ 'cust-disabled': isReadOnly }" @click="addnl">
            <span class="addicon">+</span>
            <span class="addfont">添加</span>
          </div>
        </div>
        <div class="zslist-container" style="width: 100%;" v-show="optHeaderTab == '4'">
          <AbilitysFile :fileData="content.abilitys" @delnlFun="delnl" @showFileFun="showFileFun"></AbilitysFile>
        </div>
        <div class="flex_sp" v-show="optHeaderTab == '5'">
          <div class="flex_div">关联URL</div>
          <div class="adddiv cur" :class="{ 'cust-disabled': isReadOnly }" @click="addInput">
            <span class="addicon">+</span>
            <span class="addfont">添加</span>
          </div>
        </div>
        <div class="zslist-container" v-show="optHeaderTab == '5'">
          <div class="zslist" v-for="(item, index) in content.relatedUrls" :key="index">
            <div class="card">
              <div class="card-content">
                <i class="el-icon-link"></i>
                <span class="card-text" :title="item.name">{{ item.name }}</span>
              </div>
              <div class="card-actions">
                <el-button size="mini" class="preview-btn" @click="openUrlInNewTab(item.url)">
                  <i class="el-icon-top-right"></i>打开
                </el-button>
                <el-button size="mini" class="preview-btn" @click="glShowfun(item.url)">
                  <i class="el-icon-document"></i>预览
                </el-button>
                <el-button size="mini" type="primary" @click="edidInput(index)" :disabled="isReadOnly">
                  <i class="el-icon-edit"></i>编辑
                </el-button>
                <el-button size="mini" class="delete-btn" @click="delInput(index)" :disabled="isReadOnly">
                  <i class="el-icon-delete"></i>删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <Note v-show="optHeaderTab == '6'" ref="NoteRef" :firstUrl="firstUrl" :abilitys="abilitys"></Note>
      </div>
      <ShowFile
        v-show="showFileStatus"
        :filePath="filePath"
        @close="showFileStatus = !showFileStatus"
      ></ShowFile>
      <ShowUrl v-show="glShowUrl" :urlPath="urlData" @close="glShowUrl = !glShowUrl"></ShowUrl>
      <ShowUrl v-show="zsShowUrl" :urlPath="zsUrlData" @close="zsShowUrl = !zsShowUrl"></ShowUrl>
      <el-dialog
        :visible.sync="glShow"
        title="添加关联URL"
        width="700px"
        @closed="glShow = false"
        append-to-body
      >
        <div>
          <el-form label-width="100px" :model="form" :rules="rules" ref="ruleForm">
            <el-form-item prop="name" label="名称">
              <el-input
                style="width:536px;"
                placeholder="请输入名称"
                rows="3"
                v-model="form.name"
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
            <el-form-item prop="input" label="关联URL">
              <el-input
                style="width:536px;"
                type="textarea"
                placeholder="请输入Url"
                rows="3"
                v-model="form.input"
                show-word-limit
                maxlength="100"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <div>
            <el-button type="primary" @click="upInput">确定</el-button>
            <el-button @click="glShow = false">取消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
        :visible.sync="zsShow"
        title="添加知识库"
        width="700px"
        @closed="zsShow = false"
        append-to-body
      >
        <div>
          <div class="flex-between header-content">
            <div class="flex-center">
              <el-checkbox v-model="isSelectedAll" label="全选" @change="selectAll"></el-checkbox>
              <el-divider direction="vertical"></el-divider>
              <span>
                <span>已选</span>
                <span class="primary-color">
                  {{
                  tableDataListDialog.length
                  }}
                </span>
                <span>项</span>
              </span>
            </div>
            <el-input
              v-model="searchVal"
              style="width: 300px"
              placeholder="请输入知识库名称"
              clearable
              @input="handleSetListChecked"
            />
          </div>
          <div class="overflow-y-auto" v-if="(tableData && tableData.length)">
            <div
              class="knowledge-list flex justify-between items-center cursor-pointer"
              v-for="item in tableData"
              :key="item.id"
              :class="{ selected: item.checked }"
              :style="item.checked ? 'background: #EFF3FF' : 'background: #ffffff'"
            >
              <div class="margin-right-12">
                <el-checkbox v-model="item.checked" @change="selectedChanged(item)"></el-checkbox>
              </div>
              <div class="knowledge-item">
                <img src="@/assets/images/knowledge.png" alt />
                <div class="knowledge-message">
                  <div class="title">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <el-empty description="暂无数据" v-else />

          <div style="text-align: right; padding-top: 16px">
            <el-pagination
              class="new-paper"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="[5, 10, 20, 50, 100]"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              :total="totalPage"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
        <template #footer>
          <div>
            <el-button type="primary" @click="pushData">确定</el-button>
            <el-button @click="zsShow = false">取消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
        :visible.sync="nlShow"
        title="添加能力"
        width="700px"
        @closed="nlShow = false"
        append-to-body
      >
        <div>
          <div class="flex-between header-content">
            <div class="flex-center">
              <el-checkbox v-model="isSelectedAllNl" label="全选" @change="selectAllNl"></el-checkbox>
              <el-divider direction="vertical"></el-divider>
              <span>
                <span>已选</span>
                <span class="primary-color">
                  {{
                  tableDataListNlDialog.length
                  }}
                </span>
                <span>项</span>
              </span>
            </div>
            <el-input
              v-model="searchValNl"
              style="width: 300px"
              placeholder="请输入能力名称"
              clearable
              @input="handleSetListCheckedNl"
            />
          </div>
          <div class="overflow-y-auto" v-if="(tableDataNl && tableDataNl.length)">
            <div
              class="knowledge-list flex justify-between items-center cursor-pointer"
              v-for="item in tableDataNl"
              :key="item.id"
              :class="{ selected: item.checked }"
              :style="item.checked ? 'background: #EFF3FF' : 'background: #ffffff'"
            >
              <div class="margin-right-12">
                <el-checkbox v-model="item.checked" @change="selectedChangedNl(item)"></el-checkbox>
              </div>
              <div class="knowledge-item">
                <img :src="item.ext_info.abilityIcon" alt />
                <div class="knowledge-message">
                  <div class="title">{{ item.name }}</div>
                  <el-tag size="mini">{{ item.agent_scene_name }}</el-tag>
                </div>
              </div>
            </div>
          </div>
          <el-empty description="暂无数据" v-else />
          <div style="text-align: right; padding-top: 16px">
            <el-pagination
              class="new-paper"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="[5, 10, 20, 50, 100]"
              :current-page.sync="currentPageNl"
              :page-size="pageSizeNl"
              :total="totalPageNl"
              @size-change="handleSizeChangeNl"
              @current-change="handleCurrentChangeNl"
            ></el-pagination>
          </div>
        </div>
        <template #footer>
          <div>
            <el-button type="primary" @click="pushDataNl()">确定</el-button>
            <el-button @click="nlShow = false">取消</el-button>
          </div>
        </template>
      </el-dialog>
      <input type="file" ref="fileInput" style="display:none" @change="inputChange" />
      <!-- <el-dialog :visible.sync="glShowUrl" title="关联URL" custom-class="glShowUrlclass" :style="{ height: '100vh' }" width="80%"  @closed="glShowUrl = false">
            <iframe :src="urlData" frameborder="0"  class="iframeClass" sandbox="allow-scripts allow-same-origin" ></iframe>
      </el-dialog>-->
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import MyEditorPreview from './mdEditorPreview.vue';
import {
  getKnowledgeList,
  queryAbilityMarket,
  update_scheme_materialsFetch,
  get_scheme_materialsFetch,
  create_scheme_materialsFetch
} from '@/api/planGenerateApi.js';
import ShowFile from './showFile.vue';
import ShowUrl from './showUrl.vue';
import AbilitysFile from './abilitysFile.vue';
import Note from './ConfTask/components/note.vue';
import { iframeKeyMap } from '../iframeView/config';
import { debounce } from 'lodash'
export default {
  components: {
    ShowFile,
    Note,
    ShowUrl,
    AbilitysFile,
    MyEditorPreview
  },
  computed: {
    ...mapGetters({
      getAuthorization: 'common/getMenuCollapseGetter'
    })
  },
  props: {
    isReadOnly: {
      type: Boolean,
      default: false
    },
    firstUrl:{
        type: String,
        default: ''
    },
    abilitys:{
        type: Object,
        default: () => {}
    }
  },
  data() {
    return {
     demand:[
      {
       title:'需求背景',
       fieldKey: 'requirement',
       requirement: '',
       checkFlag:true
      },
      {
       title:'需求目标',
       fieldKey: 'scheme_target',
       scheme_target: '',
       checkFlag:true
      },
     ],
      optHeaderTab: '1',
      optHeaderList: [
        {
          name: '1',
          label: '需求说明'
        },
        {
          name: '2',
          label: '关联知识库'
        },
        {
          name: '3',
          label: '关联文件'
        },
        {
          name: '4',
          label: '关联能力'
        },
        {
          name: '5',
          label: '关联URL'
        },
        {
          name: '6',
          label: '便签'
        }
      ],
      zsShowUrl: false,
      zsUrlData: '',
      filePath: '',
      showUrltatus: false,
      showFileStatus: false,
      urlData: '',
      glShowUrl: false,
      checkFlag: true,
      content: {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      },
      form: {
        name: '',
        input: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        input: [{ required: true, message: '请输入Url', trigger: 'blur' }]
      },
      glShow: false,
      requirement: '',
      id: '',
      zsShow: false,
      isSelectedAll: false,
      searchVal: '',
      tableData: [],
      tableDataListDialog: [],
      currentPage: 1,
      pageSize: 5,
      totalPage: 0,
      tableDataStroe: [],
      nlShow: false,
      isSelectedAllNl: false,
      searchValNl: '',
      tableDataNl: [],
      tableDataListNlDialog: [],
      currentPageNl: 1,
      pageSizeNl: 5,
      totalPageNl: 0,
      tableDataStroeNl: [],
      requirement: '',
      index: undefined
    };
  },
  created() {
    this.handleSetListCheckedNl = debounce(this.handleSetListCheckedNl, 300);
  },
  mounted() {
    this.getData();
  },
  beforeDestroy() {
    // 组件销毁时取消未执行的防抖任务
    this.debouncedInputHandler.cancel();
  },
  methods: {
    updateData(){
        this.$refs.MemoryComRef.setMor_ac()
        this.$refs.MemoryComRef.queryfun()
    },
    changeoptHeader2Tab(tag){
        console.log(tag,1111)
      this.optHeaderTab = tag.name
    },
    edidInput(index) {
      this.index = index;
      this.form.input = this.content.relatedUrls[index].url;
      this.form.name = this.content.relatedUrls[index].name;
      this.glShow = true;
    },
    glShowfun(url) {
      this.glShowUrl = true;
      let goUrl = url;
      if (!this.hasProtocol(goUrl)) {
        goUrl = 'https://' + url;
      }
      this.urlData = goUrl;
    },
    showZs() {
      this.zsShowUrl = true;
      let path =
        iframeKeyMap('knowledgeBasePortal', this.$route.query?.id) +
        '?showHeader=0&showLeftMenu=0&showBreadcrumb=0' +
        '&workspaceId=' +
        this.$route.query?.workspaceId;
      this.zsUrlData = this.getQueryToken(path);
    },
    getQueryToken(url) {
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    hasProtocol(url) {
      return url.startsWith('http://') || url.startsWith('https://');
    },
    openUrlInNewTab(url) {
      let goUrl = url;
      if (!this.hasProtocol(goUrl)) {
        goUrl = 'https://' + url;
      }
      window.open(goUrl, '_blank');
    },
    goRouter(val) {
      if (this.isReadOnly) return;
      this.$router.push({
        path: val,
        query: this.$route.query
      });
    },
    // 获取文件名
    getFileNameFromUrl(url) {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      return pathname.split('/').pop() || '';
    },
    // 获取文件扩展名
    getFileExtension(filename) {
      const parts = filename.split('.');
      return parts.pop() || '';
    },
    showFileFun(url){
      this.showFileStatus = true;
      this.filePath = url;
    },
    downfile(val) {
      if (this.isReadOnly) return;
      const fileName = this.getFileNameFromUrl(val);
      let hzmFile = this.getFileExtension(fileName);

      if (
        hzmFile == 'xls' ||
        hzmFile == 'xlsx' ||
        hzmFile == 'docx' ||
        hzmFile == 'doc' ||
        hzmFile == 'png' ||
        hzmFile == 'jpg' ||
        hzmFile == 'jpeg' ||
        hzmFile == 'pdf' ||
        hzmFile == 'txt'
      ) {
        this.showFileStatus = true;
        this.filePath = val;
      } else {
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = val;
        link.target = '_blank';
        link.setAttribute('download', name);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    async getData() {
      const res = await get_scheme_materialsFetch({
        scheme_id: Number(this.$route.query.id)
      });
      this.demand[0].requirement = res.data.result?.requirement || '';
      this.demand[1].scheme_target = res.data.result?.content?.scheme_target || '';
      this.id = res.data.result?.id || '';
      const content = res.data?.result?.content || {}
      this.content = {
        abilitys: content.abilitys || [],
        files: content.files || [],
        knowledges: content.knowledges || [],
        relatedUrls: content.relatedUrls || [],
        scheme_target: content.scheme_target || ''
      };
    },
    upInput() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.glShow = false;
          if (this.index === undefined) {
            this.content.relatedUrls.push({
              url: this.form.input,
              name: this.form.name
            });
          } else {
            this.content.relatedUrls[this.index].name = this.form.name;
            this.content.relatedUrls[this.index].url = this.form.input;
          }
          this.save();
        }
      });
    },
    reset(item) {
      // this.requirementEdit = this.requirement;
      item.checkFlag = true;
    },
    editRequirement(item) {
      if (this.isReadOnly) return;
      // this.requirementEdit = this.requirement;
      item.checkFlag = false;
    },
    async save(val=null) {
      // this.requirement = this.requirementEdit;
      this.content.knowledges = this.content?.knowledges?.map((item) => {
        return {
          id: Number(item.id),
          name: item.name
        };
      });
      this.content.abilitys = this.content?.abilitys?.map((item) => {
        return {
          id: Number(item.id),
          scheme_id: item.scheme_id,
          name: item.name,
          agent_scene_name: item.agent_scene_name,
          description: item.description,
          capability_type: item.capability_type,
          ext_info: {
            abilityIcon: item?.ext_info?.abilityIcon
          }
        };
      });
      this.content.scheme_target = this.demand[1].scheme_target
      //
      let res = {};
      if (this.id == '') {
        res = await create_scheme_materialsFetch({
          content: this.content,
          requirement: this.demand[0].requirement,
          schemeId: Number(this.$route.query.id)
        });
      } else {
        res = await update_scheme_materialsFetch({
          content: this.content,
          requirement: this.demand[0].requirement,
          id: this.id,
          scheme_id: Number(this.$route.query.id)

        });
      }
      if (res.data.result) {
        await this.getData();
      } else {
        this.$message({
          type: 'error',
          message: '保存失败!'
        });
      }
      this.nlShow = false;
      this.zsShow = false;
      if(val) {
       val.checkFlag = true;
      }else {
       this.demand[0].checkFlag = true
       this.demand[1].checkFlag = true
      }
    },
    delZs(index) {
      this.content.knowledges.splice(index, 1);
      this.save();
    },
    async delUp(index) {
      if (this.isReadOnly) return;
      this.content.files.splice(index, 1);
      await this.save();
      this.$emit('getDataRef');
    },
    delnl(index) {
      if (this.isReadOnly) return;
      this.content.abilitys.splice(index, 1);
      this.save();
    },
    delInput(index) {
      this.content.relatedUrls.splice(index, 1);
      this.save();
    },
    addInput() {
      if (this.isReadOnly) return;
      this.form.name = '';
      this.form.input = '';
      this.index = undefined;
      this.glShow = true;
    },
    pushData() {
      this.zsShow = false;
      this.content.knowledges = this.getUnionWithMap(
        this.content.knowledges,
        this.tableDataListDialog
      );
      this.save();
    },
    pushDataNl() {
      this.nlShow = false;
      this.content.abilitys = this.getUnionWithMap(
        this.content.abilitys,
        this.tableDataListNlDialog
      );
      this.save();
    },
    up() {
      if (this.isReadOnly) return;
      this.$refs.fileInput.click();
    },
    async getKnowledgeFun() {
      const res = await getKnowledgeList();
      this.tableData = res.data.data;
      this.tableDataStroe = res.data.data;
      this.totalPage = res.data.data.length || 0;
      if (res.data.data.length > this.pageSize) {
        this.tableData = res.data.data.slice(0, this.pageSize);
      } else {
        this.tableData = res.data.data;
      }
      this.tableData = this.tableData.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
    },
    async addzs() {
      if (this.isReadOnly) return;
      this.tableDataListDialog = [];
      this.isSelectedAll = false;
      await this.getKnowledgeFun();
      this.currentPage = 1;
      this.pageSize = 5;
      this.zsShow = true;
    },
    handleClose() {
      this.zsShow = false;
    },
    selectAll() {
      if (this.isSelectedAll) {
        this.tableData = this.tableData.map((item) => {
          return Object.assign({
            ...item,
            checked: true
          });
        });
        this.isSelectedAll = true;
        // this.content.knowledges = this.getUnionWithMap(this.content.knowledges, this.tableData)
        this.tableDataListDialog = this.tableData;
      } else {
        this.isSelectedAll = false;
        // this.content.knowledges = this.content.knowledges.filter(itemA => !this.tableData.some(itemB => itemB.id === itemA.id))
        this.tableData = this.tableData.map((item) => {
          return {
            ...item,
            checked: false
          };
        });
        this.tableDataListDialog = [];
      }
    },
    selectedChanged(data) {
      const isSelected = this.tableDataListDialog.find((obj) => obj.id === data.id);
      if (data.checked && !isSelected) {
        this.tableDataListDialog.push(data);
      }
      if (!data.checked && isSelected) {
        const currentIndex = this.tableDataListDialog.findIndex((obj) => obj.id === data.id);
        this.tableDataListDialog.splice(currentIndex, 1);
      }
    },
    handleSetListChecked() {
      const list = this.tableDataStroe.filter((obj) => obj.name.indexOf(this.searchVal) > -1);
      this.totalPage = list.length;
      // 前端手动分页
      this.tableData = list.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
      this.tableData.forEach((res) => {
        const data = this.tableDataListDialog.find((obj) => obj.id === res.id);
        res.checked = !!data;
      });
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.tableData = this.tableDataStroe.slice(0, this.pageSize);
      this.tableData = this.tableData.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
      this.isSelectedAll = false;
      this.tableDataListDialog = [];
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.tableData = this.tableDataStroe.slice(
        this.pageSize * (this.currentPage - 1),
        this.pageSize * this.currentPage
      );
      this.tableData = this.tableData.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
      this.isSelectedAll = false;
      this.tableDataListDialog = [];
    },
    async inputChange(event) {
      const files = event.target.files;
      const maxSize = 100 * 1024 * 1024; // 10MB
      let breakFlag = false;
      const allowedTypes = [
        'application/xlsx',
        'application/xls',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      for (let i = 0; i < files.length; i++) {
        // if (!allowedTypes.includes(files[i].type)) {
        //     this.$refs.fileInput.value = '';
        //     this.$message({
        //         type: 'error',
        //         message: '只能上传.pdf,.doc,.docx,.tet,.excel格式的文件!'
        //     });
        //     breakFlag = true
        //     break
        // }
        if (files[i].size > maxSize) {
          this.$refs.fileInput.value = '';
          this.$message({
            type: 'error',
            message: '文件大小不能超过100MB!'
          });
          breakFlag = true;
          break;
        }
      }
      if (breakFlag) {
        return false;
      }
      console.log(files, 'files');
      if (files) {
        for (let i = 0; i < files.length; i++) {
          const formData = new FormData();
          try {
            const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
              fileType: this.$fileUtil.getFileSuffix(files[i].name)
            });
            if (res.data.status === 200) {
              formData.append('key', res.data.data.key);
              formData.append('accessKeyId', res.data.data.accessKeyId);
              formData.append('signature', res.data.data.signature);
              formData.append('policy', res.data.data.policy);
              formData.append('file', files[i]);
            }
            const res1 = await this.$axios.post(res.data.data.obsUrl, formData);
            const fileName = this.$fileUtil.getFileName(files[i].name);
            const fileSize = files[i].size / 1024;
            const fileType = this.$fileUtil.getFileSuffixWithSpot(files[i].name);
            const fileKey = res.data.data.key;
            await this.$axios
              .post('/file/add', {
                fileKey: fileKey,
                fileName: fileName,
                fileSize: fileSize,
                fileType: fileType,
                storagePlatform: 'Obs'
              })
              .then((res) => {
                if (res.data.status === 200) {
                  console.log(res.data.data, '文件id');
                  this.content.files.push(
                    Object.assign(
                      {},
                      { ...res.data.data },
                      { name: fileName, url: res.data.data.path }
                    )
                  );
                }
              });
            await this.save();
            this.$emit('getDataRef');
          } catch (e) {
            console.log(e);
            this.$message.error('获取签名出错！');
          }
        }
      }
      this.$refs.fileInput.value = '';
    },
    async addnl() {
      if (this.isReadOnly) return;
      this.isSelectedAllNl = false;
      this.tableDataListDialog = [];
      this.getQueryPage();
      this.currentPageNl = 1;
      this.pageSizeNl = 5;
      this.nlShow = true;
    },
    handleCloseNl() {
      this.nlShow = false;
    },
    selectAllNl() {
      if (this.isSelectedAllNl) {
        this.tableDataNl = this.tableDataNl.map((item) => {
          return Object.assign({
            ...item,
            checked: true
          });
        });
        this.isSelectedAllNl = true;
        // this.content.abilitys = this.getUnionWithMap(this.content.abilitys, this.tableDataNl)
        this.tableDataListNlDialog = this.tableDataNl;
      } else {
        this.isSelectedAllNl = false;
        // this.content.abilitys = this.content.abilitys.filter(itemA => !this.tableDataNl.some(itemB => itemB.id === itemA.id))
        this.tableDataListNlDialog = [];
        this.tableDataNl = this.tableDataNl.map((item) => {
          return {
            ...item,
            checked: false
          };
        });
      }
    },
    getUnionWithMap(arr1, arr2) {
      // 创建一个 Map 来存储唯一的对象
      let map = new Map();
      let newarr1 = arr1.map((it) => {
        return {
          ...it,
          id: Number(it.id),
          name: it.name
        };
      });
      let newarr2 = arr2.map((it) => {
        return {
          ...it,
          id: Number(it.id),
          name: it.name
        };
      });
      // 将 arr1 和 arr2 的元素添加到 Map 中
      [...newarr1, ...newarr2].forEach((item) => {
        if (!map.has(item.id)) {
          map.set(item.id, item);
        } else {
          // 如果需要合并具有相同 id 的对象，可以在这里进行处理
          // 例如，可以合并属性或选择保留最新的对象
          // 这里我们选择保留 b 中的对象
          map.set(item.id, item);
        }
      });

      // 将 Map 的值转换为数组
      return Array.from(map.values());
    },
    selectedChangedNl(data) {
      const isSelected = this.tableDataListNlDialog.find((obj) => obj.id === data.id);
      if (data.checked && !isSelected) {
        this.tableDataListNlDialog.push(data);
      }
      if (!data.checked && isSelected) {
        const currentIndex = this.tableDataListNlDialog.findIndex((obj) => obj.id === data.id);
        this.tableDataListNlDialog.splice(currentIndex, 1);
      }
    },
    handleSetListCheckedNl() {
      this.getQueryPage(this.searchValNl)
    },
    handleSizeChangeNl(val) {
      this.currentPageNl = 1;
      this.pageSizeNl = val;
      this.getQueryPage(this.searchValNl);
      this.tableDataNl = this.tableDataNl.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
      this.tableDataListNlDialog = [];
    },
    handleCurrentChangeNl(val) {
      this.currentPageNl = val;
      this.getQueryPage(this.searchValNl);
      this.tableDataNl = this.tableDataNl.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
      this.tableDataListNlDialog = [];
    },
    async getQueryPage(abilityName) {
      const res = await queryAbilityMarket({
        ability_name: abilityName || '',
        updated_id: '',
        scene_id: '',
        owner_id: '',
        page_num: this.currentPageNl,
        page_size: this.pageSizeNl,
        agent_scene_code: '',
        tag_ids: [],
        sorted_scheme_ids: []
      });
      this.tableDataNl = res.data.result?.items || [];
      this.tableDataStroeNl = res.data.result?.items || [];
      this.totalPageNl = res.data.result.total || 0;
      this.isSelectedAllNl = false;
      this.tableDataNl = this.tableDataNl.map((item) => {
        return Object.assign({
          ...item,
          checked: false
        });
      });
    }
  }
};
</script>
<style lang="postcss" scoped>
.cust-disabled {
  cursor: not-allowed !important;
  opacity: 0.6;
}
.addicon {
  font-size: 27px;
  color: #4068d4;
  padding-right: 5px;
}

.span_font {
  width: 400px;
  /* 固定宽度 */
  white-space: nowrap;
  /* 防止换行 */
  overflow: hidden;
  /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}

.adddiv {
  display: flex;
  align-items: center;
}

.addfont {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4068d4;
  line-height: 20px;
  text-align: center;
  font-style: normal;
}

.flex_sp {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .flex_div {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #323233;
    line-height: 22px;
    text-align: left;
    padding-left: 10px;
    font-style: normal;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      /* z-index: 10; */
      left: -1.5px;
      width: 4px;
      height: 12px;
      background-color: #4068d4;
      box-sizing: border-box;
      top: 6px;
    }
  }
}

.flex {
  display: flex;
  justify-content: center;
  flex: 1;
  padding: 20px 20px 70px 20px;
  position: relative;
  /* max-height: calc(100% - 70px); */
}

.flex_info {
  width: 100%;
  height: auto;
  background-color: #ffffff;
  max-height: calc(100vh - 250px);
  overflow-y: auto;

  :deep(.el-form-item__label) {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #646566;
    text-align: center;
    font-style: normal;
  }
}

.btn {
  width: 102px;
  height: 30px;
  background: #f2f3f5;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4068d4;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  line-height: 30px;
  text-align: center;
}

.flex_zs {
  display: flex;
  align-items: center;

  img {
    padding: 8px;
  }

  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #4068d4;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}

:deep(.el-form-item__content) {
  height: auto !important;
  width: 500px !important;
}

:deep(.el-textarea__inner) {
  width: 100%;
}

:deep(.el-textarea) {
  width: 100%;
}

.zslist {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  background: #f6f7fb;
  width: 100%;
  margin-bottom: 8px;
}

.lr14 {
  padding-right: 14px;
}
</style>
<style lang="postcss" scoped>
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
}

.primary-color {
  color: var(--el-color-primary);
}

.margin-right-12 {
  margin-right: 12px;
}

.margin-top-12 {
  margin-top: 12px;
}

.margin-bottom-12 {
  margin-bottom: 12px;
}

.text-overFlow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-height-30 {
  line-height: 30px;
}

.label {
  width: 50px;
  color: #646566;
}

.value {
  max-width: calc(100% - 50px);

  .role-tag {
    max-width: 100%;
  }
}

.header-content {
  margin-bottom: 12px;
}

.knowledge-list {
  padding-left: 0;
  height: 74px;
  margin: 4px auto;
  display: flex;
  align-items: center;

  .knowledge-item {
    width: calc(100% - 26px);
  }
}

.knowledge-item {
  display: flex;
  padding-left: 0;

  img {
    width: 45px;
    height: 45px;
    margin-right: 12px;
  }

  .knowledge-message {
    width: calc(100% - 45px - 12px);
    display: flex;
    align-items: center;

    .title {
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 4px;
      color: #1d2129;
      line-height: 22px;
      width: calc(100% - 40px);
    }

    .sub-title {
      font-size: 14px;
      color: #646566;
      margin-bottom: 8px;
      line-height: 22px;
      width: calc(100% - 40px);
    }

    .operate {
      position: absolute;
      right: 12px;
      top: 20px;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-y-auto {
  height: 396px;
  overflow: auto;
}

.bgFont {
  min-height: 30px;
  background: #f6f7fb;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 4px 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-line;
}

.cur {
  cursor: pointer;
}

:deep(.el-input__count) {
  height: 33px;
}

.glShowUrlclass {
  height: 70vh;
}

.iframeClass {
  width: calc(100% + 20px);
  height: 70vh;
}

.zslist-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  padding: 2px 0;
}

.zslist {
  width: auto;
  box-sizing: border-box;
  max-width: calc(33.333% - 8px);
  min-width: 260px;
  margin-bottom: 0;
}

@media screen and (max-width: 1200px) {
  .zslist {
    max-width: calc(50% - 6px);
  }
}

@media screen and (max-width: 768px) {
  .zslist {
    max-width: 100%;
  }
}

.card {
  display: flex;
  padding: 10px;
  flex-direction: column;
  background: #f6f7fb;
  border-radius: 4px;
  width: 100%;
  border: 1px solid #ebeef5;
  position: relative;
  transition: all 0.2s ease;
}

.card:hover {
  background: #f0f2f5;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  width: 100%;
}

.card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.card-actions .el-button {
  padding: 3px 8px;
  height: 22px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border-radius: 2px;
}

.card-actions .el-button i {
  margin-right: 2px;
  font-size: 12px;
}

.card-actions .preview-btn {
  background-color: #f2f6ff;
  color: #4068d4;
  border: 1px solid #d9e3ff;
}

.card-actions .delete-btn {
  background-color: #fff0f0;
  color: #f56c6c;
  border: 1px solid #fcd3d3;
}

.card-actions .preview-btn:hover {
  background-color: #e6edff;
}

.card-actions .delete-btn:hover {
  background-color: #ffe6e6;
}

.card-actions .el-button[disabled] {
  background-color: #f5f7fa;
  color: #c0c4cc;
  border: 1px solid #e4e7ed;
  cursor: not-allowed;
}

.card-actions .el-button[type="primary"] {
  background-color: #4068d4;
  color: white;
  border: none;
}

.card-actions .el-button[type="primary"]:hover {
  background-color: #3458b3;
}

.card-actions .el-button[type="primary"][disabled] {
  background-color: #a0cfff;
  border: none;
}

.card-content {
  i {
    font-size: 14px;
    color: #909399;
    flex-shrink: 0;
  }

  img {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }
}

.card-text {
  flex: 1;
  font-size: 13px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;

  &[title] {
    cursor: help;
  }
}

.zslist {
  height: auto;
  min-height: auto;
}

:deep(.input_class .el-input__count) {
  height: 18px !important;
}
.center_flex {
  :deep(.el-tabs--top) {
    line-height: 40px;
  }
  :deep(.el-tabs__item) {
    border-radius: 16px;
    background: #f6f7fb;
    height: 30px;
    line-height: 30px;
    padding: 4px 12px;
    font-weight: 400;
    font-size: 14px;
    color: #323233;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    margin-right: 8px;
  }
  :deep(.is-active) {
    background: #4068d4;
    .tabTitle {
      color: #ffffff;
    }
  }
  :deep(.el-tabs__active-bar) {
    display: none;
  }
  :deep(.el-tabs__nav-prev) {
    line-height: inherit;
  }
  :deep(.el-tabs__nav-next) {
    line-height: inherit;
  }
  :deep(.el-tabs__header) {
    width: -webkit-fill-available;
  }
}
.solid_class {
  padding: 4px 8px;
  margin-left: 10px;
  background: #f2f3f5;
  border-radius: 2px;
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  i {
    color: #4068d4;
  }
}
</style>
<style lang="postcss">
.center_flex {
  .el-tabs__nav {
    display: flex;
    justify-content: center;
  }
}
.optHeader2 {
  padding: 0px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .el-tabs {
    flex: 1;
    height: 40px;
    max-width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    .el-tabs__header {
      display: flex;
      align-items: center;
    }
  }

  .rightTitleOpt-tab {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-shrink: 0;
    &:empty {
      margin-left: 0;
    }

    .item {
      margin-left: 8px;
    }

    img {
      width: 16px;
      height: auto;
    }
  }

  .el-tabs__header {
    margin: 0px;
  }
  .el-tabs__nav-wrap::after {
    content: none;
    background: none;
  }

  .tabTitle {
    font-weight: 500;
    font-size: 14px;
  }
}
.rightTitleOpt-tab .tag-item {
  min-width: auto !important;
}
.body_flex{
    height: 100%;
    overflow: hidden;
    padding-bottom: 12px;
    padding-top: 10px;
}
</style>
