<template>
  <div class="marketTest">
    <div class="testHeader">
      <div class="headerTitle">
        <!-- <div class="title">空压机高温故障诊断</div> -->
        <el-popover
          v-model="visible"
          placement="bottom-start"
          width="330"
          :visible-arrow="false"
          @show="showPop"
        >
          <div>
            <div class="guess-header">
              <el-input
                v-model.trim="searchName"
                class="samllinput"
                style="width: 300px"
                placeholder="请输入"
                @input="getQueryPageList"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="getQueryPageList"
                ></el-button>
              </el-input>
            </div>
            <div class="abilityList">
              <div
                v-for="(el, index) in nengliList"
                :key="el.id"
                :class="
                  Number(activeId) === el.id ? 'abilityItem abilityItemActive' : 'abilityItem'
                "
                @click="changeAbilityFun(el)"
              >
                <!-- <el-tooltip class="item" :content="el.name"  effect="dark" placement="bottom"> -->
                {{ el.name }}
                <!-- </el-tooltip> -->
              </div>
            </div>
            <div v-if="nengliList.length === 0">
              <el-empty description="暂无"></el-empty>
            </div>
          </div>
          <div style="text-align: right; margin: 0">
            <el-button type="primary" size="mini" @click="handleAbilityFun">切换</el-button>
          </div>
          <div slot="reference">
            <div class="title">{{ currentName }}<i class="el-icon-caret-bottom"></i></div>
          </div>
        </el-popover>
      </div>
      <div style="display: flex; align-items: center">
        <div class="btns" style="margin-right: 16px">
          <span
            v-if="specialScene.includes(agent_scene_code)"
            :class="['btns-item', showType === 'business' ? 'active' : '']"
            @click="changeShowType('business')"
          >
            <el-tooltip class="item" effect="dark" content="业务验证" placement="top">
              <SvgIcon name="dragborder" class="view-icon" />
            </el-tooltip>
          </span>
          <span
            v-if="agent_scene_code !== 'device_ops_assistant_scene-v1'"
            :class="['btns-item', showType === 'task' ? 'active' : '']"
            @click="changeShowType('task')"
          >
            <el-tooltip class="item" effect="dark" content="任务模式" placement="top">
              <SvgIcon name="task-model" class="view-icon" />
            </el-tooltip>
          </span>
          <span
            :class="[
              'btns-item',
              showType === 'more' ? 'active' : '',
              agent_scene_code === 'device_ops_assistant_scene-v1' ? 'one-btn' : ''
            ]"
            @click="changeShowType('more')"
          >
            <el-tooltip class="item" effect="dark" content="高级模式" placement="top">
              <SvgIcon name="code" class="view-icon" />
            </el-tooltip>
          </span>
          <span
            v-if="agent_scene_code == 'intelligent_conversation_scene'"
            :class="['btns-item', showType === 'chat' ? 'active' : '']"
            @click="changeShowType('chat')"
          >
            <el-tooltip class="item" effect="dark" content="会话模式" placement="top">
              <SvgIcon name="chat" class="view-icon" />
            </el-tooltip>
          </span>
        </div>
        <!-- <div class="more" @click="showType = 'more'">高级</div> -->
        <el-button
          type="info"
          @click="
            $router.push({
              path: '/abilityCenter/targetList',
              query: {
                workspaceId: $route.query.workspaceId,
                workspaceName: $route.query.workspaceName
              }
            })
          "
          >返回能力仓库</el-button
        >
      </div>
    </div>
    <div v-if="showType === 'business'" class="testContent testContent-business">
      <div class="business">
        <BusinessValidation></BusinessValidation>
      </div>
    </div>
    <div v-if="showType === 'task'" class="testContent">
      <div v-if="tableData.length > 0">
        <!-- <div class="qsBox">
          <div class="qs">你好！输入参数可以测试能力</div>
        </div> -->
        <div class="asBox">
          <div class="asContent">
            <div class="as">
              <div style="font-weight: bold">请输入以下参数：</div>
              <div>
                <el-button :disabled="loading || completeLoading" type="primary" @click="handleTest"
                  >测试</el-button
                >
                <el-button :disabled="loading || completeLoading" type="info" @click="handleReset"
                  >重置</el-button
                >
              </div>
            </div>
            <div v-for="(item, index) in tableData" class="as">
              <div>{{ item.param_desc }}：</div>
              <div style="flex: 1">
                <el-select
                  v-if="item.param_name === 'deviceId'"
                  v-model="item.param_value"
                  placeholder="请选择"
                  filterable
                  remote
                  :remote-method="filterEquip"
                  :loading="loading2"
                  style="width: 100%"
                  @change="(val) => changeEquip(val, index)"
                >
                  <el-option
                    v-for="fitem in equipList"
                    :key="fitem.deviceId + ''"
                    :label="fitem.deviceName"
                    :value="fitem.deviceId + ''"
                  ></el-option>
                </el-select>
                <div v-else-if="item.data_type === 'File'">
                  <div class="fileBox">
                    <shardUploaderTool
                      :accept="''"
                      upload-tip="小于20M"
                      :limit="20"
                      :multiple="false"
                      :cover="true"
                      :index-ref="index"
                      :shard-limit="1"
                      @onFilesChange="uploadScriptCallback"
                      @onFilesStatus="uploadShardStatusCb"
                      @on-remove="removeFile"
                    />
                  </div>
                  <div v-if="previewFlag" class="image">
                    <img v-if="curImageUrl" :src="curImageUrl" />
                    <span v-else>暂不支持预览</span>
                  </div>
                </div>
                <el-select
                  v-else-if="item.data_type === 'bool' || item.data_type === 'Boolean'"
                  v-model="item.param_value"
                  placeholder="请选择"
                >
                  <el-option key="1" value="true">true</el-option>
                  <el-option key="2" value="false">false</el-option>
                </el-select>
                <el-date-picker
                  v-else-if="item.data_type === 'dateTime'"
                  v-model="item.param_value"
                  :picker-options="pickerOptions1"
                  style="width: 100%"
                  type="datetime"
                  popper-class="mytest"
                  placeholder="选择日期时间"
                >
                </el-date-picker>
                <el-input v-else v-model="item.param_value" />
              </div>
            </div>
          </div>
        </div>
        <div v-if="testloading" class="qa-loading-spinner3"></div>
        <div v-if="Object.keys(jsonData).length" class="qsBox" :loading="testloading">
          <div class="qs">已为您生成结果</div>
        </div>
        <div v-if="Object.keys(jsonData).length" class="qsResult" :loading="testloading">
          <div v-if="jsonData.success" class="reTitle">
            <!-- <img src="@/assets/images/planGenerater/task-success.png"/><div class="title">{{jsonData.message}}</div> -->
            <img src="@/assets/images/planGenerater/task-success.png" />
            <div class="title">
              <MyEditorPreview
                id="MyJsonEditor"
                ref="MyJsonEditor"
                :md-content="jsonData.message"
              ></MyEditorPreview>
            </div>
          </div>
          <div v-if="!jsonData.success" class="reTitle">
            <!-- <img src="@/assets/images/planGenerater/task-error.png"/><div class="title">{{jsonData.message}}</div> -->
            <img src="@/assets/images/planGenerater/task-error.png" />
            <div class="title">
              <MyEditorPreview
                id="MyJsonEditor"
                ref="MyJsonEditor"
                :md-content="jsonData.message"
              ></MyEditorPreview>
            </div>
          </div>
          <div class="reContent">
            <MyEditorPreview
              id="MyJsonEditorResult"
              ref="MyJsonEditorResult"
              :md-content="jsonData.result"
            ></MyEditorPreview>
          </div>
        </div>
        <div
          v-if="Object.keys(jsonData).length && !completeLoading && !loading"
          :disabled="completeLoading"
          class="redo"
          @click="
            () => {
              jsonData = {};
              handleTest();
            }
          "
        >
          <i class="el-icon-refresh-left"></i>重新生成
        </div>
      </div>
      <el-empty
        v-else
        :image="require('@/assets/images/planGenerater/emptyParams.png')"
        description="暂无参数，无法使用任务模式，请使用其他模式"
      >
      </el-empty>
    </div>
    <div v-if="showType === 'more'" class="testContent">
      <MoreTest :id="nengliId" :agent-scene-code="agent_scene_code" :scheme-test-id="scheme_id" />
    </div>
    <div v-if="showType === 'chat'" class="testContent">
      <ChatTest :id="nengliId" :scheme_id="scheme_id" />
    </div>
  </div>
</template>
<script>
import {
  queryAbilityApi,
  allEquipList,
  queryOneEquipDetail,
  queryAbilityMarket
} from '@/api/planGenerateApi.js';
import dayjs from 'dayjs';
import BusinessValidation from './marketDetail/BusinessValidation.vue';
import MoreTest from './marketDetail/testGaoji.vue';
import ChatTest from './marketDetail/testChat.vue';
import MyEditorPreview from './mdEditorPreview.vue';
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue';
import axios from 'axios';
const cancelToken = axios.CancelToken;
let source = cancelToken.source();
export default {
  name: 'MarketTest',
  components: { MoreTest, ChatTest, shardUploaderTool, MyEditorPreview, BusinessValidation },
  data() {
    return {
      testloading: false,
      loading: false,
      newTableData: [],
      tableData: [],
      filesList: [],
      curImageUrl: '',
      previewFlag: false,
      jsonData: {},
      equipList: [],
      loading2: false,
      showType: 'task',
      visible: false,
      nengliList: [],
      searchName: '',
      currentName: this.$route.query.name || '--',
      nengliId: this.$route.query.id || '',
      agent_scene_code: this.$route.query.agent_scene_code || '',
      scheme_id: this.$route.query.scheme_id || '',
      copyRow: {},
      activeId: this.$route.query.id || '',
      copyId: this.$route.query.id || '',
      completeLoading: false,
      specialScene: ['operational_optimization_scene']
    };
  },
  computed: {
    pickerOptions1() {
      return {
        disabledDate(time) {
          const maxDate = Date.parse(
            dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
          );
          return time.getTime() > maxDate;
        }
      };
    }
  },
  mounted() {
    if (this.agent_scene_code === 'device_ops_assistant_scene-v1') {
      this.showType = 'more';
    } else {
      if (this.specialScene.includes(this.agent_scene_code)) {
        this.showType = 'business';
      } else {
        this.showType = 'task';
      }
    }
    this.getAllEquip();
    this.getCodeParams();
    this.getAllQueryPageList('');
  },
  methods: {
    changeShowType(type) {
      source.cancel();
      source = cancelToken.source();
      this.showType = type;
    },
    showPop() {
      this.activeId = this.copyId;
      this.searchName = '';
      this.jsonData = {};
      this.getAllQueryPageList();
    },
    changeAbilityFun(row) {
      this.copyId = this.activeId;
      this.activeId = row.id;
      this.copyRow = {
        agent_scene_code: row.agent_scene_code,
        id: row.id,
        scheme_id: row.scheme_id,
        name: row.name
      };
    },
    handleAbilityFun() {
      if (this.completeLoading) {
        this.visible = false;
        this.$message({
          type: 'warning',
          message: '请等待本次测试结束后再切换！'
        });
      } else {
        console.log('切换能力仓库', this.copyRow);
        if (Object.keys(this.copyRow).length) {
          this.agent_scene_code = this.copyRow.agent_scene_code;
          this.nengliId = Number(this.copyRow.id);
          this.scheme_id = Number(this.copyRow.scheme_id);
          this.currentName = this.copyRow.name;
          this.copyId = this.activeId;
          this.visible = false;
          this.showType = 'task';
          if (this.copyRow.agent_scene_code === 'device_ops_assistant_scene-v1') {
            // 研发版物联场景没有任务模式
            this.showType = 'more';
          } // 切换数学场景模型的时候
          if (this.specialScene.includes(this.agent_scene_code)) {
            this.showType = 'business';
          } else {
            this.$nextTick(() => {
              this.getAllEquip();
              this.getCodeParams();
              this.getAllQueryPageList('');
            });
          }
        } else {
          this.visible = false;
          this.showType = 'task';
          if (this.agent_scene_code === 'device_ops_assistant_scene-v1') {
            // 研发版物联场景没有任务模式
            this.showType = 'more';
          } else {
            this.getAllEquip();
            this.getCodeParams();
            this.getAllQueryPageList('');
          }
        }
      }
    },
    async handleTest() {
      this.testloading = true;
      this.completeLoading = true;
      let url = '';
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      let params = {};
      const datas = {};
      let deviceId = '';
      let detectionTime = '';
      this.tableData.forEach((item, index) => {
        if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
          if (item.data_type !== 'File') {
            if (item.data_type == 'Boolean' || item.data_type == 'bool') {
              const temp = item.param_value === 'true';
              datas[item.param_name] = temp;
            } else if (item.data_type == 'Array' || item.data_type == 'Object') {
              try {
                const temp = JSON.parse(item.param_value);
                datas[item.param_name] = temp;
              } catch (error) {
                const temp = item.param_value;
                datas[item.param_name] = temp;
              }
            } else if (item.data_type == 'String' || item.data_type == 'string') {
              datas[item.param_name] = item.param_value;
            } else if (item.data_type == 'Number' || item.data_type === 'decimal') {
              const temp = Number(item.param_value);
              if (isNaN(temp)) {
                datas[item.param_name] = item.param_value;
              } else {
                datas[item.param_name] = temp;
              }
            } else {
              if (item.param_value !== '') {
                datas[item.param_name] = item.param_value;
              } else {
                console.log('不传');
                // datas[item.name] = item.value
              }
            }
          } else {
            datas[item.param_name] = this.filesList[index];
          }
        } else {
          if (item.param_name === 'deviceId') {
            deviceId = item.param_value;
          }
          if (item.param_name === 'detectionTime') {
            detectionTime = item.param_value;
          }
        }
      });
      if (this.agent_scene_code === 'device_ops_assistant_scene') {
        url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/executev1'
          : process.env.VUE_APP_PLAN_API + '/code/executev1';

        params = {
          scheme_id: this.scheme_id,
          deviceId: deviceId,
          detectionTime: dayjs(detectionTime).format('YYYY-MM-DD HH:mm:ss'),
          params: datas,
          oper_type: 'ability'
        };
      } else {
        url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute'
          : process.env.VUE_APP_PLAN_API + '/code/execute';
        params = {
          scheme_id: this.scheme_id,
          scene_type: this.agent_scene_code || '',
          params: datas,
          oper_type: 'online_ability'
          // oper_type: 'ability'
        };
      }
      this.$axios
        .post(
          url,
          {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: params || {}
          },
          {
            timeout: 280000,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            maxContentLength: 1024 * 1024 * 1024, // 增加请求体的大小限制为1GB
            headers: {
              affinitycode: userInfo.userId || '',
              'Content-Type': 'application/octet-stream'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              this.testloading = false;
              const xhr = event.target;
              const { responseText } = xhr;
              const newStr = responseText.replace(/[\r\n]/g, '');
              this.$nextTick(() => {
                console.log('----流----', newStr);
                this.jsonData = {
                  message: '执行成功',
                  result: newStr,
                  success: true
                };
              });
            },
            onError: function (error) {
              // 处理流错误
              console.error(error);
              this.testloading = false;
            }
          }
        )
        .then(async (res) => {
          // 关闭数据流
          console.log('数据流', res);
          this.testloading = false;
          this.completeLoading = false;
          if (res.status === 200 && res.data.code === 200) {
            console.log('测试结果', res.data.result);
            if (res.data.result) {
              this.jsonData = res.data.result?.resp || {
                message: '执行成功',
                result: '',
                success: true
              };
            } else {
              this.jsonData = {
                message: res.data.msg || '执行失败',
                result: '',
                success: false
              };
            }
          } else {
            if (res.status === 200) {
              this.$nextTick(() => {
                const newStr = res.data.replace(/[\r\n]/g, '');
                console.log('----流----', res.data, newStr);
                this.jsonData = {
                  message: '执行成功',
                  result: newStr,
                  success: true
                };
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          }
        })
        .catch(() => {
          this.testloading = false;
          this.completeLoading = false;
        });
    },
    handleReset() {
      const files = [];
      const temp = [];
      this.tableData.forEach((item) => {
        temp.push({ ...item, param_value: '' });
        files.push('');
      });
      this.filesList = files;
      this.tableData = temp;
      this.jsonData = {};
    },
    // 设备选择框
    async filterEquip(val) {
      this.loading2 = true;
      if (val) {
        await allEquipList({ deviceId: val, scheme_id: this.scheme_id }).then((res) => {
          this.loading2 = false;
          if (res.status === 200 && res.data.code === 200) {
            this.equipList = res.data.result || [];
          }
        });
      } else {
        this.loading2 = false;
        this.equipList = this.globalList;
      }
    },
    changeEquip(val, index) {
      if (val) {
        const filtes = this.equipList.filter((item) => item.deviceId == val);
        const curRow = filtes[0];
        console.log('当前行', this.tableData[index]);
        this.equipName = curRow.deviceName || '';
        const timeFilter = this.tableData.filter((item) => item.param_name === 'detectionTime');
        queryOneEquipDetail({
          scheme_id: this.scheme_id,
          detectionTime: timeFilter.length
            ? timeFilter[0].param_value
            : dayjs().format('YYYY-MM-DD HH:mm:ss'),
          device_type_code: curRow.device_type_code,
          deviceId: val
        }).then((res) => {
          const temp = [...this.tableData];
          const temp3 = [];
          temp.forEach((item) => {
            if (item.param_name !== 'deviceId' && item.param_name !== 'detectionTime') {
              const filters = res.data?.result.filter(
                (ritem) => ritem.device_attribute_code === item.param_name
              );
              if (filters.length) {
                temp3.push({ ...item, param_value: filters[0].device_attribute_value });
              } else {
                temp3.push({ ...item });
              }
            } else {
              temp3.push({ ...item });
            }
          });
          this.tableData = temp3;
          console.log('更新', temp3);
        });
      }
    },
    // 查询所有设备
    getAllEquip() {
      allEquipList({ scheme_id: this.scheme_id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.equipList = res.data.result || [];
          this.globalList = res.data.result || [];
        }
      });
    },
    getCodeParams() {
      queryAbilityApi({ ability_id: Number(this.nengliId) }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('测试参数', res.data.result);
          this.newTableData = res?.data?.result?.req_body || [];
        } else {
          this.newTableData = [];
        }
        const files = [];
        const temp = [];
        if (this.agent_scene_code === 'device_ops_assistant_scene') {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name === 'detectionTime') {
              item.param_value = dayjs()
                .subtract(2, 'day')
                .startOf('day')
                .format('YYYY-MM-DD HH:mm:ss');
              temp.push({ ...item });
            } else {
              temp.push({ ...item, param_value: '' });
            }
            files.push('');
          });
        } else {
          res.data.result.req_body?.forEach((item) => {
            if (item.param_name !== 'detectionTime' && item.param_name !== 'deviceId') {
              temp.push({ ...item, param_value: '' });
            }
            files.push('');
          });
        }

        this.filesList = files;
        this.tableData = temp;
      });
    },
    uploadScriptCallback(fileData, fileList, index) {
      console.log('文件上传成功过', fileData, index);
      const fileType = this.$fileUtil.getFileSuffix(fileData.name);
      if (fileData.path) {
        this.previewFlag = true;
      } else {
        this.previewFlag = false;
      }
      if (
        fileType.toLowerCase() === 'jpg' ||
        fileType.toLowerCase() === 'png' ||
        fileType.toLowerCase() === 'jpeg'
      ) {
        this.curImageUrl = fileData.path;
      } else {
        this.curImageUrl = '';
      }
      this.filesList[index] = fileData.path;
      console.log(this.filesList);
    },
    uploadShardStatusCb(status) {},
    removeFile(fileId, filelist, index) {
      console.log('删除的行', index);
      this.filesList[index] = '';
    },
    async getQueryPageList(val) {
      const params = {
        ability_name: val || this.searchName,
        updated_id: '',
        scene_id: '',
        owner_id: '',
        page_num: 1,
        page_size: 20,
        tag_ids: [],
        sorted_scheme_ids: []
      };
      queryAbilityMarket(params)
        .then((res) => {
          const { data = {}, status } = res;
          if (status === 200 && data.code === 200) {
            const list = [];
            data.result?.items?.forEach((item) => {
              if (item.is_online) {
                list.push(item);
              }
            });
            if (val) {
              this.nengliList = list;
            } else {
              this.firstList?.forEach((item) => {
                if (item.is_online) {
                  list.push(item);
                }
              });
              this.nengliList = list;
            }
          }
        })
        .catch((_err) => {
          this.isLoading = false;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    async getAllQueryPageList() {
      const params = {
        ability_name: '',
        updated_id: '',
        scene_id: '',
        owner_id: '',
        page_num: 1,
        page_size: 20,
        tag_ids: [],
        sorted_scheme_ids: []
      };
      queryAbilityMarket(params)
        .then((res) => {
          const { data = {}, status } = res;
          if (status === 200 && data.code === 200) {
            const list = [];
            data.result?.items?.forEach((item) => {
              if (item.is_online) {
                list.push(item);
              }
            });
            this.firstList = list;
            this.nengliList = list;
          }
        })
        .catch((_err) => {
          this.isLoading = false;
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}
.abilityList {
  max-height: 300px;
  overflow: auto;
  padding: 8px 0px;
  .abilityItem {
    cursor: pointer;
    max-width: 300px;
    /* 设置文本溢出时的行为为省略号 */
    text-overflow: ellipsis;
    /* 设置超出容器的内容应该被裁剪掉 */
    overflow: hidden;
    /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
    white-space: nowrap;
    line-height: 32px;
    padding: 0 5px;
    &:hover {
      background: #eff3ff;
    }
    &:active {
      background: #eff3ff;
    }
    &.abilityItemActive {
      background: #eff3ff;
      color: #4068d4;
    }
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  line-height: 20px !important;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
::v-deep .el-button.el-button--primary {
  line-height: 20px;
}
.imgBox {
  display: flex;
  justify-content: center;
}
.mytest {
  font-size: 14px;
  .el-button--text {
    display: none !important;
  }
}
.marketTest {
  display: flex;
  flex-direction: column;
  .testHeader {
    background-color: #fff;
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .headerTitle {
      // flex: 1;
      cursor: pointer;
      .title {
        font-size: 18px;
        color: #323233;
        font-weight: bold;
        margin-right: 8px;
      }
    }
  }
  .testContent {
    flex: 1;
    margin: 16px 20px;
    background: url('@/assets/images/planGenerater/market-bg.png') no-repeat;
    background-size: cover;
    max-height: calc(100% - 84px);
    overflow-y: auto;
    position: relative;
    .qsBox {
      display: flex;
      justify-content: flex-start;
      .qs {
        font-size: 14px;
        color: #323233;
        line-height: 20px;
        margin: 20px;
        background: #ffffff;
        border-radius: 4px;
        padding: 8px 16px;
      }
    }

    .asBox {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin: 56px 20px 16px;
      .asContent {
        background: #ffffff;
        border-radius: 4px;
        padding: 8px 16px;
        // max-width: 50%;
        min-width: 50%;
        .as {
          display: flex;
          justify-content: space-between;
          align-items: top;
          margin-bottom: 16px;
          .fileBox {
            display: flex;
            :deep(.el-button) {
              height: 32px;
            }
            :deep(.el-upload-list__item:first-child) {
              margin-top: 5px;
            }
          }
          .image {
            width: 100%;
            height: 200px;
            overflow: hidden;
            margin-top: 10px;
            color: rgb(150, 151, 153);
            img {
              max-width: 100%;
              max-height: 100%;
              display: block;
              margin: auto;
            }
          }
        }
      }
    }
    .qsResult {
      background: #ffffff;
      border-radius: 4px;
      padding: 8px 16px;
      border: 1px solid #dcdde0;
      margin: 0px 20px 0px;
      .reContent {
        color: #646566;
        line-height: 22px;
      }
      .reTitle {
        display: flex;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
          margin-right: 8px;
        }
        .title {
          color: #323233;
          font-weight: bold;
        }
      }
    }
    .redo {
      margin-top: 8px;
      margin-left: 20px;
      color: #4068d4;
      cursor: pointer;
      &:hover {
        color: #2d4b9d;
      }
    }
    .el-empty {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .testContent-business {
    display: flex;
    justify-content: center;
    padding: 8px 16px;
    .business {
      border-radius: 4px;
      min-width: 50%;
    }
  }
  .more {
    margin-left: 16px;
    margin-right: 16px;
    color: #4068d4;
    cursor: pointer;
    &:hover {
      color: #2d4b9d;
    }
  }
  .btns {
    display: flex;
    .btns-item {
      display: inline-block;
      position: relative;
      width: 30px;
      height: 30px;
      line-height: 28px;
      background: #f2f3f5;
      color: #4068d4;
      text-align: center;
      cursor: pointer;
      &.one-btn {
        border-radius: 4px 4px 4px 4px !important;
      }
      .view-icon {
        vertical-align: middle;
        color: #3f68d4;
      }
      &:hover {
        background: #eaeaed;
      }
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 6px;
        width: 1px;
        height: 18px;
        background: #dcdde0;
      }
      &:first-child {
        border-radius: 4px 0px 0px 4px;
        &::before {
          content: unset;
        }
      }
      &:last-child {
        border-radius: 0px 4px 4px 0px;
      }
      &.active {
        background: #4068d4;
        color: #ffffff;
        .view-icon {
          color: #ffffff;
        }
        &::before {
          content: unset;
        }
      }
    }
  }
}
</style>
