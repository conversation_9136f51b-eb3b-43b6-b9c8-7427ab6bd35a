<template>
  <div class="editZhushou">
    <div v-loading="loading" class="editBox">
      <div class="pack-heard">
      <el-page-header @back="() => $router.push({ path: '/planGenerate/taskRd', query: { ...$route.query, fromMenu:'2'} })" content="">
      </el-page-header>
     </div>
     <div class="contain">
       <div class="headerTip">
         <!-- <div class="tipIcon"><img slot="icon" src="@/assets/images/planGenerater/jiqiren.png"></div>
         <div class="tipDesc">请先选择使用场景并输入名称和描述，将为您自动生成场景方案明细</div> -->
         专家知识的起点
       </div>
       <el-form ref="form" label-position="right" :model="formData" :rules="rules">
         <el-form-item prop="description" class="textBox skin">
           <div class="input-outer">
             <el-input
               id="saveImgProd"
               v-model="formData.description"
               @input="handleInput"
               :placeholder=" requirement ? '请输入您的需求明细' : placeholder"
               type="textarea"
               :rows="7"
               resize="none"
             />
             <div class="choose-btn">
               <div class="scene">
                 <el-popover
                   v-model="chooseModelFlag"
                   placement="bottom-start"
                   :width="300"
                   trigger="click"
                 >
                   <div>
                     <el-cascader
                       v-if="showFlag"
                       ref="cascaderSelect"
                       v-model="formData.agent_scene"
                       show-all-levels
                       empty-text="暂无数据"
                       :props="props"
                       style="width: 100%"
                       @change="changeScene"
                       @expand-change="handleChangeExpand"
                     >
                       <template #default="{ node, data }">
                         <span>
                           <el-tag  v-if="data.resource_type === 1" type="success" size="small" effect="light" plain >预置</el-tag>
                           <el-tag  v-else-if="data.resource_type === 2" type="primary" size="small" effect="light" plain>自定义</el-tag>
                         </span>&nbsp;
                         <span>{{ data.label }}</span>
                       </template>
                       <template #empty> 暂无数据 </template>
                     </el-cascader>
                     <div style="float: right; margin-top: 16px">
                       <el-button
                         type="primary"
                         :disabled="ensureBtnDisabled"
                         @click="handleCloseClick('sure')"
                         >确定</el-button
                       >
                       <el-button type="secondary" @click="handleCloseClick('cancel')"
                         >取消</el-button
                       >
                     </div>
                   </div>
                   <template #reference>
                    <div class="flex_info">
                      <SvgIcon
                         :name="
                           !noneList.includes(selectedValues[0]) || chooseModelFlag
                             ? 'scene-active'
                             : 'scene'
                         "
                         class="menu-icon"
                       />
                       <el-tooltip class="item" effect="dark" :content="findNameById(selectedValues)" placement="top-start">
                       <p
                         :class="{
                           'scene-name ellipsis-text': true,
                           'scene-name__active ellipsis-text': !noneList.includes(selectedValues[0])
                         }"
                       >
                         {{ findNameById(selectedValues) }}
                       </p>
                      </el-tooltip>
                    </div>
                   </template>
                 </el-popover>
               </div>
               <!-- <div class="upload">
               <SvgIcon name="upload" class="menu-icon" />
               <span>文件</span>
             </div> -->
             <div class="flex_text">
              <!-- <span class="text-sm" style="margin-right: 10px;">专家生产</span>
              <el-select v-model="dev_product_scheme_id" filterable clearable   placeholder="请选择专家生产" :style="{marginRight: '20px',width:'160px'}">
                      <el-option
                      v-for="item in typeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      />
                  </el-select> -->
              <!-- <span class="text-sm" >生产资料</span><el-switch v-model="productionShow" /> -->
              <span class="text-sm" :style="{marginRight: '20px'}">生产上下文</span>
              <el-switch v-model="productionShow" />
              <span class="text-sm" style="margin-left: 12px">{{ requireTitle }}</span>
              <el-switch style="margin-left: 16px;" v-model="requirement" />
             </div>
             </div>

             <div class="send-btn">
                <div class="upload-btn">
                    <el-dropdown
                        :disabled="!((this.sceneDetailList[this.formData.scene_version_id]?.enable_image_text_msg || false) && requirement)">
                        <el-button :class="(inputDisabled) ? 'active-upload': 'expert-upload'" :disabled="!((this.sceneDetailList[this.formData.scene_version_id]?.enable_image_text_msg || false) && requirement)">
                        </el-button>
                       <el-dropdown-menu slot="dropdown">
                         <el-dropdown-item command="personInfo">
                          <el-upload
                              ref="uploadBtn"
                              :action="uploadUrl"
                              :show-file-list="false"
                              :data="uploadParam"
                              :limit="1"
                              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                              :before-upload="beforeUpload"
                              :on-success="modelUploadSuccess"
                            >图片上传
                          </el-upload>
                          </el-dropdown-item>
                         <el-dropdown-item :disabled="true" command="changePWD">文件上传</el-dropdown-item>
                       </el-dropdown-menu>
                     </el-dropdown>
                 </div>
                <el-tooltip class="item" effect="dark" content="请输入你的问题" placement="top">
                  <el-button class="send-desc" :disabled="loading || echoLoading" :class="(active && formData.description) ? 'active': ''" @click="createSubmit">
                  </el-button>
                </el-tooltip>
              </div>
           </div>
           <div v-if="this.formData.image_path !=''" style="width: 100%;height: 138px;background-color: #E5E5E5;position: relative; display: inline-block;">
               <div style="display: flex; flex-direction: column; ">
                 <el-image style="width: 100px; height: 100px;margin-left: 10px;margin-top: 10px"
                       :src="this.formData.image_path"
                       :fit="fit" :preview-src-list="[this.formData.image_path]">
                 </el-image>
                 <div style="width: 120px;display: flex; justify-content: center; align-items: center;" >
                   <el-button type="text" icon="el-icon-delete" style="color: red;padding-top: 5px;bckground-color: #E5E5E5;" @click="clearImage"></el-button>
                 </div>
               </div>
           </div>
         </el-form-item>
       </el-form>
       <ProductionCom ref="ProductionComRef" v-show="productionShow"></ProductionCom>
     </div>
    </div>
    <shareMember
      ref="shareMemberRef"
      :share-users="shareUsers"
      :modal-title="modalTitle"
      @shareMemberList="shareMemberList"
    />
  </div>
</template>
<script>
import ProductionCom from './productionCom'
import {
  AddScheme,
  queryDictConfig,
  agentSenceList,
  queryTags,
  addTag,
  bindTag,
  queryUseTags,
  getInitWorkBenchDich,
  getExecuteSync,
  getSencetVisibleList,
  getUserLastSceneList,
  SchemeList,
  saveSimpleSchemeGenerate
} from '@/api/planGenerateApi.js'
import { cloneDeep } from 'lodash'
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {}
const currentWorkspace = localStorage.getItem('currentWorkSpace')
  ? JSON.parse(localStorage.getItem('currentWorkSpace'))
  : {}
export default {
  name: 'ModelDialog',
  components:{
    ProductionCom,
  },
  data() {
    const checkShare = (rule, value, callback) => {
      if (value.trim() === '') {
        return callback(new Error('可见范围不能为空'))
      } else if (value === 'share' && this.shareUsers.length === 0) {
        return callback(new Error('分享人员不能为空'))
      } else {
        callback()
      }
    }
    return {
      typeList:[],
      dev_product_scheme_id:'',
      requireTitle: '直接创建',
      placeholder: '请输入您的对话名称',
      requirement: false,
      productionShow: false,
      inputDisabled:false,
      active:false,
      noneList: [null, '', undefined],
      specialSceneType: ['digital_twin_assistant_scene'],
      sceneName: '场景',
      firstSceneList: [],
      secondSceneList: [],
      firstSceneChange: false,
      chooseModelFlag: false,
      sceneDetailList: {},
      IsSenior: false,
      showFlag: false,
      highFlag: true, // 高级配置
      showDisplayType: false, // 是否展示模式
      selectedValues: [],
      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          if (node?.data?.is_display === 1) {
            this.showDisplayType = true
          } else {
            this.showDisplayType = false
          }
          const { level } = node
          if (level === 0) {
            queryDictConfig({ business_type: 'scene_type' }).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                // const result = res.data.result?.config.map((item) => {
                //   return {
                //     value: item.code,
                //     label: item.name,
                //     is_display: item.is_display
                //   }
                // })
                const result = res.data.result?.config?.filter(item => item.dev_mode === 'task').map((item) => {
                  return {
                    value: item.code,
                    label: item.name,
                    is_display: item.is_display
                  }
                })
                resolve(result || [])
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
          } else {
            getSencetVisibleList({
              keyword: '',
              user_id: userInfo.userId,
              workspace_id: currentWorkspace.workspaceId,
              scene_type: node.data.value
            }).then((res) => {
              console.log(res, 'opppp')
              const result = res.data || []
              if (result && result.length > 0) {
                const resultData = result
                  .filter(
                    (item) =>
                      item.scene_visibility !== 'unpublish' &&
                      item.scene_visibility !== 'invisiable'
                  )
                  .map((item) => {
                    return {
                      value: item.scene_version_id,
                      label: item.name,
                      scene_visibility: item.scene_visibility,
                      leaf: true,
                      resource_type: item.resource_type // 添加 resource_type 属性
                    }
                  })
                resolve(resultData || [])
              }
              // else {
              //   this.$message({
              //     type: 'error',
              //     message: res.data?.msg || '接口异常!'
              //   })
              // }
            })
          }
        }
      },
      formData: {
        name: '',
        description: '',
        agent_scene_code: '',
        agent_scene: '',
        agent_id: '',
        ownerId: [],
        share_userids: [], // 分享的用户
        contributors: [],
        tag_ids: [],
        scene_version_id: '',
        scheme_detail_name: '',
        domains: [],
        visibility: 'public',
        display_type: '1',
        image_key: '',
        image_path: ''
      },
      userList: [],
      agentList: [{ id: '001', description: 'ddd' }],
      tagList: [],
      allTagList: [],
      senceOptions: [],
      dictList: [],
      allTags: [],
      rules: {
        // scheme_detail_name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        agent_scene: [{ required: true, message: '请选择使用场景', trigger: 'blur' }],
        visibility: [
          { required: true, message: '请选择可见范围', trigger: 'blur' },
          { validator: checkShare, trigger: 'blur' }
        ],
        description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
        display_type: [{ required: true, message: '请选择展示模式', trigger: 'blur' }]
      },
      loading: false,
      echoLoading: false,
      schemeId: '',
      editData: {},
      shareUsers: [],
      modalTitle: '请选择分享用户',
      shareToolTip: '',
      ensureBtnDisabled: true,
      oldSelectedValues: [],
      uploadUrl: '',
      uploadParam: {}
    }
  },
  watch: {
    requirement: {
      immediate: true,
      handler(newVal) {
      const newRule = { max: 20, message: '描述内容不能超过20个字符', trigger: 'blur' };
      if(!newVal) {
        this.requireTitle = '直接创建'
        // 添加新的规则，确保不重复添加
      if (!this.rules.description.some(rule => rule.max === 20)) {
        this.rules.description.push(newRule);
      }
      }else {
        this.requireTitle = '自动聊天'
        // �移除新增的规则
      this.rules.description = this.rules.description.filter(rule => rule.max !== 20);
      this.$refs.form.validateField('description');
      }
      }
    },
    chooseModelFlag(newVal, oldVal) {
      if (newVal) {
        this.ensureBtnDisabled = true
        this.oldSelectedValues = this.selectedValues.slice()
      } else {
        if (this.firstSceneChange) {
          this.handleGetSecondScene(this.formData.agent_scene[0])
          this.firstSceneChange = false
        }
      }
    },
    selectedValues(newVal, oldVal) {
      if (newVal.length > 0 && oldVal?.length > 0) {
        const instanceVersionId = newVal[1]
        const enableImage = this.sceneDetailList[instanceVersionId]?.enable_image_text_msg || false
        if (enableImage === false) {
          this.formData.image_path = ''
          this.formData.image_key = ''
        }
      }
    }
  },
  async mounted() {
    console.log('query---.',this.$route.query)
    this.saveImg()
    this.queryLastSence()
    this.getSchemeList()
    this.loading = true
    await this.queryAllTag()
    this.loading = false
    const _this = this
    document.addEventListener('keydown', function (event) {
      if (event.keyCode === 13 || event.which === 13) {
        console.log('按下了回车键')
        _this.createSubmit()
      }
    })
    await this.handleGetFirstScene()
    this.$forceUpdate()
  },
  methods: {
    async getSchemeList(){
      const res = await  SchemeList(
        {
            "offset": 1,
            "limit": 999,
            "name": "",
            "agent_scene": "",
            "status": "",
            "process_status": "3",
            "sort_field": "create_time",
            "order": "desc",
            "dev_mode": "scheme"
        })
        this.typeList = res.data.result.items
    },
    saveImg() {
      const tath = this
      document.getElementById('saveImgProd').addEventListener('paste', async function (event) {
        event.preventDefault();

        // 检查是否启用了图像和文本消息功能
        const enableImageTextMsg = tath.sceneDetailList[tath.formData?.scene_version_id]?.enable_image_text_msg;

        // 获取剪切板数据
        const clipboardData = event.clipboardData || window.clipboardData;

        if (!clipboardData || !clipboardData.items) {
          console.warn('No clipboard data available.');
          return;
        }

        let hasImage = false;
        let textContent = '';
        const promises = [];

        console.log('Clipboard items:', clipboardData.items);

        for (let i = 0; i < clipboardData.items.length; i++) {
          const item = clipboardData.items[i];

          if (item && item.type) {
            console.log('Item type:', item.type, 'Item kind:', item.kind);

            if (item.type.startsWith("image/") && tath.sceneDetailList[tath.formData?.scene_version_id]?.enable_image_text_msg) {
              // 处理图像
              hasImage = true;
              const blob = item.getAsFile();
              if (blob) {
                await tath.newUpdown(blob, blob.type, blob.size, blob.name);
              }
            } else if (item.kind === 'string' && item.type === 'text/plain') {
              // 处理文本
              console.log('Found string item:', item);
              promises.push(new Promise((resolve) => {
                item.getAsString(function (str) {
                  console.log('getAsString callback triggered with:', str);
                  resolve(str || ''); // 如果 str 为空，返回空字符串
                });
              }));
            }
          }
        }

        // 等待所有文本 Promise 完成
        try {
          const allTexts = await Promise.all(promises);
          textContent = allTexts.join('');
          console.log('All texts:', allTexts);
        } catch (error) {
          console.error('Error processing clipboard text:', error);
        }

        // 如果有文本内容，插入到文档中
        if (textContent) {
          console.log('Inserting text:', textContent);
          document.execCommand('insertText', false, textContent);
        }

        // 调试信息
        console.log('Images:', hasImage ? 'Yes' : 'No');
        console.log('Text Content:', textContent);

        // 如果未启用图像和文本消息功能，仅处理文本
        if (!enableImageTextMsg && textContent) {
          console.warn('Image and text message feature is not enabled. Only text will be processed.');
        }
      });
    },
    async newUpdown(files) {
      try {
        const formData = new FormData();
        const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
          fileType: 'png'
        })
        if (res.data.status === 200) {
          formData.append('key', res.data.data.key)
          formData.append('accessKeyId', res.data.data.accessKeyId)
          formData.append('signature', res.data.data.signature)
          formData.append('policy', res.data.data.policy)
          formData.append('file', files)
        }
        const res1 = await this.$axios.post(res.data.data.obsUrl, formData)
        const fileName = this.$fileUtil.getFileName(files.name)
        const fileSize = files.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(files.name)
        const fileKey = res.data.data.key
        this.uploadParam = {
          key: res.data.data.key,
          accessKeyId: res.data.data.accessKeyId,
          signature: res.data.data.signature,
          policy: res.data.data.policy
        }
        this.formData.image_key = res.data.data.key
        await this.$axios
          .post('/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs',

          })
          .then((res1) => {
            if (res1.data.status === 200) {
              console.log(res.data.data, '文件id')
              this.formData.image_path = res1.data.data.path
            }
          })
        // await this.save()
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    handleInput(val) {
      this.active = true
      if(!val) {
        this.active = false
      }
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.formData.image_path = ''
          this.formData.image_key = ''
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取第一层场景数据
    handleGetFirstScene() {
      queryDictConfig({ business_type: 'scene_type' }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.firstSceneList = res.data.result?.config.map((item) => {
            return {
              value: item.code,
              label: item.name,
              is_display: item.is_display
            }
          })
          if (this.formData.agent_scene.length) {
            this.handleGetSecondScene(this.formData.agent_scene[0])
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    // 获取第二层场景数据
    handleGetSecondScene(val) {
      getSencetVisibleList({
        keyword: '',
        user_id: userInfo.userId,
        workspace_id: currentWorkspace.workspaceId,
        scene_type: val
      }).then((res) => {
        const result = res.data || []

        if (result.length > 0) {
          console.log('this.sceneDetailList   res.data', result)
          result.forEach((item) => {
            this.sceneDetailList[item.scene_version_id] = item
          })
          console.log('this.sceneDetailList1---', this.sceneDetailList)
          this.secondSceneList = result.map((item) => {
              return {
                value: item.scene_version_id,
                label: item.name,
                id: item.id
              };
          })
          this.$forceUpdate()
        }

        // else {
        //   this.$message({
        //     type: 'error',
        //     message: res.data?.msg || '接口异常!'
        //   })
        // }
      })
    },
    // 返回场景name
    findNameById(idArray) {

      const nameArray = idArray
        .map((id) => {
          const firstScene = this.firstSceneList.find((item) => item.value === id)
          if (firstScene) {
            return firstScene.label
          }
          console.log('this.sceneDetailList2',this.secondSceneList,idArray)
          const secondScene = this.secondSceneList.find((item) => item.value === id);
          if (secondScene) {
            return secondScene.label
          }
          return ''
        })
        .filter((name) => name !== '');
        // console.log('1122233',secondScene)
      return nameArray.join('/') || '场景';
    },
    handleChangeExpand(val) {
      console.log('query1',this.formData.agent_scene,val)
      this.ensureBtnDisabled = true;
      this.firstSceneChange = true;
      this.handleGetSecondScene(val[0]);
    },
    handleCloseClick(val) {
      this.chooseModelFlag = false
      if (this.firstSceneChange) {
        this.firstSceneChange = false
      }
      if (val === 'cancel') {
        this.selectedValues = this.oldSelectedValues.slice()
        this.formData.agent_scene = this.oldSelectedValues.slice()
        this.formData.agent_scene_code = this.oldSelectedValues[0]
        this.formData.scene_version_id = this.oldSelectedValues[1]
        this.handleGetSecondScene(this.selectedValues[0])
      }
    },
    changeScene(val) {
      this.ensureBtnDisabled = false
      this.formData.agent_scene_code = val[0]
      this.formData.scene_version_id = val[1]
      this.selectedValues = this.formData.agent_scene
      if (this.formData.agent_scene_code) {
        const filters = this.dictList.filter(
          (item) => item.value === this.formData.agent_scene_code
        )
        if (filters.length && Number(filters[0].is_display) === 1) {
          this.showDisplayType = true
        } else {
          this.showDisplayType = false
        }
      } else {
        this.showDisplayType = false
      }
      this.queryEqu(val[1])
    },
    shareFrame() {
      this.$refs.shareMemberRef.handleOpen()
    },
    shareMemberList(Arr) {
      this.shareUsers = Arr || []
      this.formData.share_userids = Arr.map((el) => {
        return el.id
      })
      this.shareToolTip = Arr.map((el) => {
        return el.nickname
      })
    },
    queryLastSence() {
      getUserLastSceneList({"dev_mode": "task"})
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data.result;
            if(this.$route.query && this.$route.query.scene_version_id && this.$route.query.scene_type) {
              this.selectedValues = cloneDeep([this.$route.query.scene_type, this.$route.query.scene_version_id])
            }else {
              this.selectedValues = cloneDeep([result.scene_type, result.scene_version_id]);
            }

          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.showFlag = true
          this.$nextTick(() => {
            this.$refs.cascaderSelect.checkedValue = this.selectedValues;
            this.$forceUpdate();
            console.log(this.selectedValues, this.$refs.cascaderSelect, 'this.selectedValues');
          });
        });
    },
    queryEqu(val) {
      getInitWorkBenchDich({ scene_version_id: val })
        .then(async (res) => {
          this.echoLoading = true
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data?.result || []
            this.formData.domains = []
            if (result.length > 0) {
              for (const item of result) {
                const domainItem = {
                  label: item.field_name,
                  key: item.field_code,
                  value: '',
                  type: item.field_val.type || '',
                  domainsOptions: []
                }
                if (item.field_val.type === 'ability') {
                  const res = await getExecuteSync({
                    ability_id: item.field_val.ability_id,
                    name: item.field_val.ability_id,
                    goal: ''
                  })
                  if (res.data && res.data.length > 0) {
                    const resultList = res.data
                    if (resultList && resultList.length > 0) {
                      resultList.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                          item.children.forEach((it) => {
                            if (it.children && it.children.length === 0) {
                              delete it.children
                            }
                          })
                        }
                      })
                      domainItem.domainsOptions = resultList
                    }
                    this.formData.domains.push(domainItem)
                  }
                } else {
                  this.formData.domains.push(domainItem)
                }
              }
              if (this.$route.query.id) {
                if (this.formData.domains.length > 0) {
                  this.formData.domains.forEach((item, index) => {
                    if (item.type === 'ability') {
                      if (this.formData.ext_data_info[index]?.field_val?.parentValue) {
                        // 二层级
                        item.value = [
                          this.formData.ext_data_info[index]?.field_val?.parentValue,
                          this.formData.ext_data_info[index]?.field_val?.value
                        ]
                      } else {
                        // 一层级
                        item.value = [this.formData.ext_data_info[index]?.field_val?.value]
                      }
                    } else {
                      item.value = this.formData.ext_data_info[index]?.field_val?.value
                    }
                  })
                }
              }
            }
          }
          this.echoLoading = false
        })
        .finally(() => {
          this.echoLoading = false
        })
    },
    changeList(val) {
      this.$nextTick(() => {
        this.formData.contributors = this.$refs.onwerSelect.selected.map((item) => {
          return {
            id: item.currentValue,
            nickname: item.currentLabel.slice(0, item.currentLabel.indexOf('(')),
            loginName: item.currentLabel.match(/\((.+)\)/)[1]
          }
        })
      })
    },
    async changeTags(val) {
      const temp = []
      val.forEach(async (tagid) => {
        const tagTotal = [...this.allTags, ...this.allTagList]
        const filters = tagTotal.filter((item) => item.id === tagid)
        if (filters.length === 0) {
          console.log('标签长度', tagid.length)
          if (tagid.length && tagid.length <= 15) {
            await addTag({
              name: tagid
            }).then(async (res) => {
              if (res.data) {
                console.log('添加成功', res.data)
                temp.push(res.data)
                await queryTags({
                  keyword: ''
                }).then((res) => {
                  if (res.data) {
                    const mergedArr = [...res.data, ...this.allTags]
                    const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(
                      JSON.parse
                    )
                    this.tagList = uniqueArr
                    this.allTagList = uniqueArr
                    this.$refs.tagsSelect.selected[
                      this.$refs.tagsSelect.selected.length - 1
                    ].currentLabel = tagid
                    this.formData.tag_ids = temp
                    console.log('this.formData.tag_ids', this.formData.tag_ids)
                    this.$nextTick(() => {
                      this.$refs.tagsSelect.selected[
                        this.$refs.tagsSelect.selected.length - 1
                      ].currentLabel = tagid
                    })
                  } else {
                    this.tagList = []
                  }
                })
              }
            })
          } else {
            this.$message({
              type: 'warning',
              message: '标签最长为15个字符，请修改!'
            })
          }
        } else {
          const mergedArr = [...this.allTags, ...this.allTagList]
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse)
          this.tagList = uniqueArr
          this.allTagList = uniqueArr
          const filters = tagTotal.filter((item) => item.id === tagid)
          if (filters.length) {
            // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            temp.push(tagid)
            this.formData.tag_ids = temp
            console.log('this.formData.tag_ids', this.formData.tag_ids)
            this.$nextTick(() => {
              // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = filters[0].name
            })
          } else {
            temp.push(tagid)
            this.formData.tag_ids = temp
            console.log('this.formData.tag_ids', this.formData.tag_ids)
          }
        }
      })
    },
    searchUser(userName) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data
      })
    },
    searchTags(keyword) {
      queryTags({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data
          if (keyword === '') {
            this.allTagList = res.data
          }
        } else {
          this.tagList = []
        }
      })
    },
    queryAllTag() {
      queryUseTags({ keyword: '' })
        .then((res) => {
          if (res.data) {
            console.log(res.data)
            this.allTags = res.data
            this.$nextTick(async () => {
              await this.searchTags2()
            })
          }
        })
        .finally(() => {})
    },
    searchTags2() {
      queryTags({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          const mergedArr = [...res.data, ...this.allTags]
          const uniqueArr = Array.from(new Set(mergedArr.map(JSON.stringify))).map(JSON.parse)
          this.allTagList = uniqueArr
          this.tagList = uniqueArr
          const temp = []
          // console.log('回显', this.editData.tags, this.tagList);
          this.editData.tags?.forEach((titem) => {
            const filter = uniqueArr.filter((item) => item.id === titem.id)
            if (filter.length) {
              temp.push(titem.id)
            }
          })
          this.formData.tag_ids = temp
        } else {
          this.allTagList = [...this.allTags]
          this.tagList = [...this.allTags]
        }
      })
    },

    onClose(item, scene) {
      this.formData.name = ''
      this.formData.agent_scene = []
      this.formData.contributors = []
      this.formData.visibility = 'public'
      this.formData.ownerId = []
      this.formData.agent_id = ''
      this.formData.tag_ids = []
      this.formData.display_type = '1'
      this.$refs.form.clearValidate()
      // let url = '/planGenerate/wllsExpertPlanchat'
      let url = '/planGenerate/ConfTaskPlanchat'
      let params = {
            ...this.$route.query,
            status: item.status,
            id: item.id,
            isTask: true,
            fromMenu:'2'
          }
      if(this.$route.query && this.$route.query.scene_version_id && this.$route.query.scene_type) {
        window.customeDescription = this.formData.description
        // url = '/planGenerate/hqwPlanchat'
        url = '/planGenerate/ConfTaskPlanchat'
      }else {
        params.enterType = 'expert'
      }
      if (scene === 'digital_twin_assistant_scene'){
        window.customeDescription = this.formData.description
          this.$router.push({
            path: '/planGenerate/ConfTaskPlanchat',
            // path: '/planGenerate/wllsExpertPlanchat',
            query: params
          })
        }
      else if(scene === 'operational_optimization_scene' && this.$route.query.scene_version_id && this.$route.query.scene_type) {
        window.customeDescription = this.formData.description
        this.$router.push({
          path: url,
          query: params
        })
      } else if (scene === 'operational_optimization_scene'&& !this.$route.query.scene_version_id && !this.$route.query.scene_type) {
        window.customeDescription = this.formData.description
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          // path: '/planGenerate/hqwPlanchat',
          query: {
            ...this.$route.query,
            status: item.status,
            id: item.id,
            isTask: true,
            fromMenu:'2'
          }
        })
      } else if (
        [
          'device_ops_assistant_scene',
          'device_ops_assistant_scene-v1',
          'sop_scene',
          'visit_leader_cognition_scene',
          'intelligent_conversation_scene',
          'artificial_handle_scene',
          'rule_generation_scene',
          'custom_cognition_assistant_scene'
        ].includes(scene)
      ) {
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          query: {
            ...this.$route.query,
            status: item.status,
            isTask: true,
            fromMenu:'2',
            id: item.id
          }
        })
        // window.customeDescription = this.formData.description
        // window.message = {
        //   content: this.formData.description,
        //   image_key: this.formData.image_key || '',
        //   image_path: this.formData.image_path || ''
        // }
      } else {
        this.$router.push({
          path: '/planGenerate/ConfTaskPlanchat',
          query: {
            ...this.$route.query,
            status: item.status,
            fromMenu:'2',
            isTask: true,
            id: item.id
          }
        })
        // window.customeDescription = this.formData.description
        // window.message = {
        //   content: this.formData.description,
        //   image_key: this.formData.image_key || '',
        //   image_path: this.formData.image_path || ''
        // }
      }
      if(this.requirement) {
        window.customeDescription = this.formData.description
        window.message = {
          content: this.formData.description,
          image_key: this.formData.image_key || '',
          image_path: this.formData.image_path || ''
        }
      }
      this.formData.description = ''
    },
    createSubmit() {
      this.$refs.form.validate((validate) => {
        if (validate) {
          const noneList = [null, '', undefined]
          const isEmpty = this.selectedValues.every(function (element) {
            return noneList.includes(element)
          })
          console.log('this.selectedValues.length', this.selectedValues, isEmpty)

          if (isEmpty) {
            console.log('this.selectedValues.length', !this.selectedValues.length)

            this.$message({
              type: 'error',
              message: '您还没有选择具体的场景!'
            })
            return
          }
          this.loading = true
          const outputFields = this.formData.domains.map((field) => {
            const obj = {}
            obj.field_name = field.label
            obj.field_code = field.key
            if (field.type === 'input') {
              obj.field_val = { value: field.value, label: field.value }
            } else {
              console.log(field, 'filed')
              if (field.value && field.value.length === 1) {
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(field.value[0])
                )
                obj.field_val = {
                  value: selectedOptions.value,
                  label: selectedOptions.label,
                  parentValue: selectedOptions.parentValue
                }
              } else if (field.value && field.value.length === 2) {
                const selectedValues = field.value
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(selectedValues[0])
                )
                const selectedSubOptions = selectedOptions?.children?.find(
                  (child) => child.value === selectedValues[1]
                )
                obj.field_val = {
                  value: selectedSubOptions.value,
                  label: selectedSubOptions.label,
                  parentValue: selectedValues[0]
                }
              }
            }
            return obj
          })

          let param = {
            name: this.formData.description.slice(0, 6),
            scheme_detail_name: this.formData.name || this.formData.scheme_detail_name,
            description: this.formData.description,
            scene_version_id: this.formData.scene_version_id,
            agent_scene_code: this.formData.agent_scene_code,
            agent_id: this.formData.agent_id,
            contributors: this.formData.contributors,
            tag_ids: this.formData.tag_ids,
            ext_data_info: outputFields,
            visibility: this.formData.visibility,
            image_key: this.formData.image_key,
            name_analysis: this.requirement ? 1 : 0,
            materials:{
              dev_product_scheme_id:this.dev_product_scheme_id
            }
          }
          if( this.productionShow == true){
            param = Object.assign({...param},{materials: this.$refs.ProductionComRef.getDataAll()} )
          }
          if (this.showDisplayType) {
            param.display_type = this.formData.display_type
          }
          if (this.formData.visibility === 'share') {
            param.share_userids = this.shareUsers.map((el) => {
              return el.id
            })
          }
          AddScheme(param)
            .then(async (res) => {
              this.loading = false
              console.log('创建结果', res)
              if (res.status === 200 && res.data.code * 1 === 200) {
                await bindTag({
                  tag_ids: this.formData.tag_ids,
                  biz_id: res.data.result.id
                }).then((ress) => {
                  console.log('绑定成功', ress.data)
                })
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                })
                if (
                  [
                    'digital_twin_assistant_scene',
                    'operational_optimization_scene',
                    'device_ops_assistant_scene',
                    'sop_scene',
                    'visit_leader_cognition_scene',
                    'intelligent_conversation_scene',
                    'artificial_handle_scene',
                    'rule_generation_scene',
                    'custom_cognition_assistant_scene'
                  ].includes(this.formData.agent_scene_code)
                ) {
                  this.$store.commit('planGenerate/setInitSengMsg', this.formData.description)
                }
                this.onClose(res.data.result, this.formData.agent_scene_code)
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    async beforeUpload(file) {
      try {
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        })
        if (res.data.status === 200) {
          this.uploadUrl = res.data.data.obsUrl
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        }
      } catch (e) {
        this.$message.error('获取签名出错！')
      }
    },
    modelUploadSuccess(response, file) {
      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        this.$refs.uploadBtn.clearFiles()
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key
        this.formData.image_key = fileKey
        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.formData.image_path = res.data.data.path
            }
          })
      } else {
        this.$message.warning(`模型上传状态为:${this.uploadStatus}`)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  :deep(.el-form-item__content) {
    flex: 1;
  }
}
:deep(.skin .el-input__inner) {
  border: 2px solid;
  border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;
}
:deep(.skin .el-textarea__inner) {
  border: 2px solid;
  border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;
}
.editZhushou {
  display: flex;
  flex-direction: column;
  .headerBox {
    background: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    .headerTitle {
      font-size: 18px;
      color: #323233;
    }
  }
  .editBox {
    background-image: url('~@/assets/images/planGenerater/jianbian.png') ;
    background-repeat: no-repeat;
    background-size: 100% auto; /* 或者使用 100% 100% 来铺满 */
    background-position: left top;
    flex: 1;
    .pack-heard {
      // border-bottom: 1px solid #fff;
      padding-inline: 0 24px;
      ::v-deep .el-page-header {
        height: 60px;
        align-items: center;
        justify-content: flex-start;
        margin-left: 20px;
      }
      ::v-deep .el-page-header__left::after {
        background-color: #409eff;
      }
    }
    .contain {
      padding: 16px 100px;
    }
    .textBox {
      position: relative;
      display: flex;
      justify-content: center;

      .input-outer {
        // border-radius: 6px;
        overflow: hidden;
      }
      .choose-btn {
        position: absolute;
        display: flex;
        flex-direction: row;
        align-items: center;
        bottom: 0px;
        left: 8px;
        cursor: pointer;
        .scene {
          margin-right: 12px;
        }
      }
      .send-btn {
          position: absolute;
          right: 12px;
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          align-items: center;
          bottom: 0;
          .upload-btn {
            height: 32px;
            width: 32px;
            .expert-upload {
              width: 32px;
              height: 32px;
              // margin-right: 8px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/expert-upload.png");
            }
            .expert-upload:hover {
              width: 32px;
              height: 32px;
              border-radius: 0px;
              background-image: url("~@/assets/images/planGenerater/hover-upload.png");
            }
            .active-upload {
              width: 32px;
              height: 32px;
              border-radius: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/hover-upload.png");
            }
          }
          img {
            width: 18px;
          }

          .send-desc {
            font-size: 10px;
            margin-left: 8px !important;
            color: #969799;
            width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
              background-image: url("~@/assets/images/planGenerater/expert-send.png");
            // }
            img {
              width: 32px;
            }
          }
          .active {
              background-image: url("~@/assets/images/planGenerater/active-send.png");
            img {
              width: 32px;
            }
          }

          .yuyinBtn {
            cursor: pointer;
            width: 32px;
            height: 32px;
            margin-right: 8px;
            margin-left: 8px;
            .expert-voic {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/expert-voice.png");
            }
            .expert-voic:hover {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/hover-voice.png");
            }
            .active-voic {
             width: 32px;
              height: 32px;
              border-radius: 0;
              margin-left: 0px;
              background-size: cover;
              background-repeat: no-repeat;
              background-size: 100% ;
              background-position: center;
             background-image: url("~@/assets/images/planGenerater/hover-voice.png");
            }

            &.yuyinBtnDisabled {
              cursor: not-allowed;
            }
          }
          :deep(.el-button) {
            padding: 0px;
            border: none;
            i {
              font-size: 12px;
              margin-left: -1px;
            }
          }
        }
    }
    .headerTip {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 48px 0 16px;
      font-size: 24px;
      font-weight: 500;
      font-family: fangsong;
      color: #4a77ff;
      .tipIcon {
        width: 48px;
        height: 48px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .tipDesc {
        flex: 1;
        background: #eff3ff;
        margin-left: 13px;
        position: relative;
        padding: 8px 16px;
        font-size: 14px;
        line-height: 20px;
        color: #323233;
        border-radius: 6px;
        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          border-top: 5px solid transparent; /*左边透明*/
          border-bottom: 5px solid transparent; /*右边透明*/
          border-right: 8px solid #eff3ff; /*底部为黑色线条*/
        }
      }
    }
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.custom-content {
  display: flex;
  align-items: center;
  padding: 4px;
  .menu-icon__active {
    color: var(--slide-menu-active-color);
  }

  .scene-name {
    margin-left: 6px;
  }
  .scene-name__active {
    color: #4a77ff;
  }
}
.custom-content__active {
  background-color: #f5f7fa;
  border-radius: 20px;
}
:deep(.el-form-item__content) {
  line-height: 0;
  width: 650px;
  height: 150px;
}
:deep(.el-form-item__error) {
  padding-top: 14px;
}
.flex_text{
  display: flex;
  align-items: center;
  span{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #4068D4;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
.flex_info{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 32px;
}
.ellipsis-text {
  max-width: 160px; /* 使用视口宽度的 20% */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  height: 22px;
  line-height: 22px;
}
.scene-name__active{
  color: #4a77ff;
}
</style>
