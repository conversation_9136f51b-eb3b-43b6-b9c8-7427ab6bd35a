<template>
  <div class="marketTest">
    <div class="testHeader">
      <div class="headerTitle">
        <div class="title">{{ currentName }}</div>
      </div>
      <div style="display: flex; align-items: center">
        <el-button type="info" @click="
          $router.push({
            path: '/abilityCenter/targetList',
            query: {
              workspaceId: $route.query.workspaceId,
              workspaceName: $route.query.workspaceName
            }
          })
          ">返回能力仓库</el-button>
      </div>
    </div>
    <div class="task_model">
      <!-- <div class="header">
        <img alt="" />
        <img src="@/assets/images/image/nl_1.png" alt="" />
        <div class="title">{{ diy }}</div>
      </div> -->
      <div class="main">
        <div class="req_container">
          <div class="s_box">
            <div class="param">
              <div class="param_title">输入信息</div>
              <div class="headerContent">
                <el-row class="service-other-subtitle-row">
                  <el-col class="service-other-subtitle-col">
                    <span class="service-other-subtitle">请求头参数：</span>
                  </el-col>
                  <el-col :span="24">
                    <el-table :data="headerParamList" border>
                      <el-table-column label="参数" prop="label" />
                      <el-table-column label="值" prop="desc">
                        <template slot-scope="scope">
                          <!-- <span v-if="!testCallAvailable">{{ scope.row.desc }}</span> -->
                          <div>
                            <!-- <el-select v-if="scope.row.label == 'Content-Type'" v-model="format" style="width: 100%"
                            :placeholder="scope.row.placeholder" @change="selectFormat">
                            <el-option v-for="item in serviceBasicInfo.devType !== 1
                              ? scope.row.value.slice(0, 1)
                              : scope.row.value" :key="item" :label="item" :value="item" />
                          </el-select> -->
                            <span v-if="scope.row.label == 'Content-Type'">{{ scope.row.desc }}</span>
                            <el-input v-if="scope.row.label == 'X-GW-Authorization'" v-model="headerParamList[1].value"
                              :placeholder="scope.row.placeholder">
                              <el-popover slot="append" v-model="appModule.visible" trigger="click" placement="bottom">
                                <el-select v-model="appModule.selectApp" :popper-append-to-body="false" clearable
                                  placeholder="选择一个应用授权" value-key="id" @change="initToken">
                                  <div style="max-height: 170px">
                                    <el-option v-for="item in appModule.appList" :key="item.id" :label="item.appName"
                                      :value="item" />
                                    <!-- <el-option :key="appModule.guideApp.id" :label="appModule.guideApp.appName"
                                    style="text-align: center; background-color: #0f55fa; color: #fff"
                                    :value="appModule.guideApp" /> -->
                                  </div>
                                </el-select>
                                <el-button slot="reference">生成token</el-button>
                              </el-popover>
                            </el-input>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>
              </div>
              <div class="inputContent">
                <el-row :gutter="30">
                  <el-col :span="24">
                    <el-row class="service-other-subtitle-row">
                      <el-col class="service-other-subtitle-col">
                        <span class="service-other-subtitle">请求参数：</span>
                      </el-col>
                      <el-col>
                        <div class="div-textarea">
                          <div class="div-box">
                            <codemirror v-model="inParam" :disabled="!testCallAvailable" :options="cmOptions"
                              :placeholder="JSON.stringify(jsonSample, null, 4)" @blur="checkJSON('1')" />
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="foot">
              <el-button class="check-button" type="primary" @click="startCall">开始</el-button>
            </div>
          </div>
        </div>
        <div class="result">
          <div class="title">
            输出结果
          </div>
          <div class="content">
            <div class="inputContent">
              <div class="div-textarea" style="height: 307px">
                <div class="div-box">
                  <el-input v-model.trim="outParam" type="textarea" :rows="8" :readonly="true" resize="none"
                    :placeholder="JSON.stringify(jsonSample, null, 4)" @blur="checkJSON('2')" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-if="showType === 'task'" class="testContent">
      <div>
        <div class="asBox">
          <div class="asContent">

            <div class="as">
              <div style="font-weight: bold">请输入以下参数：</div>
              <div>
                <el-button :disabled="loading || completeLoading" type="primary" @click="startCall">测试</el-button>
                <el-button :disabled="loading || completeLoading" type="info" @click="handleReset">重置</el-button>
              </div>
            </div>
            <div class="headerContent">
              <el-row class="service-other-subtitle-row">
                <el-col class="service-other-subtitle-col">
                  <span class="service-other-subtitle">请求头参数：</span>
                </el-col>
                <el-col :span="18">
                  <el-table :data="headerParamList" border>
                    <el-table-column label="参数" prop="label" />
                    <el-table-column label="值" prop="desc">
                      <template slot-scope="scope">
                        <div>
                          <span v-if="scope.row.label == 'Content-Type'">{{ scope.row.desc }}</span>
                          <el-input v-if="scope.row.label == 'X-GW-Authorization'" v-model="headerParamList[1].value"
                            :placeholder="scope.row.placeholder">
                            <el-popover slot="append" v-model="appModule.visible" trigger="click" placement="bottom">
                              <el-select v-model="appModule.selectApp" :popper-append-to-body="false" clearable
                                placeholder="选择一个应用授权" value-key="id" @change="initToken">
                                <div style="max-height: 170px">
                                  <el-option v-for="item in appModule.appList" :key="item.id" :label="item.appName"
                                    :value="item" />
                                </div>
                              </el-select>
                              <el-button slot="reference">生成token</el-button>
                            </el-popover>
                          </el-input>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
            <div class="inputContent">
              <el-row :gutter="30">
                <el-col :span="12">
                  <el-row class="service-other-subtitle-row">
                    <el-col class="service-other-subtitle-col">
                      <span class="service-other-subtitle">请求参数：</span>
                    </el-col>
                    <el-col>
                      <div class="div-textarea">
                        <div class="div-box">
                          <codemirror v-model="inParam" :disabled="!testCallAvailable" :options="cmOptions"
                            :placeholder="JSON.stringify(jsonSample, null, 4)" @blur="checkJSON('1')" />
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <el-row class="service-other-subtitle-row">
                    <el-col class="service-other-subtitle-col">
                      <span class="service-other-subtitle">请求结果：</span>
                    </el-col>
                    <el-col>
                      <div class="div-textarea" style="height: 307px">
                        <div class="div-box">
                          <el-input v-model.trim="outParam" type="textarea" :rows="8" :readonly="true" resize="none"
                            :placeholder="JSON.stringify(jsonSample, null, 4)" @blur="checkJSON('2')" />
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>

        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import {
  getAppListByServiceId,
  queryAlgoConfigInfo,
} from '@/api/aiServiceDeploy.js';
import dayjs from 'dayjs';
import MyEditorPreview from '../mdEditorPreview.vue';
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue';
import axios from 'axios';
import { codemirror } from 'vue-codemirror';

// require styles
import 'codemirror/lib/codemirror.css';

// require active-line.js
import 'codemirror/addon/selection/active-line.js';
// styleSelectedText
import 'codemirror/addon/selection/mark-selection.js';
// hint
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/javascript-hint.js';
const cancelToken = axios.CancelToken;
let source = cancelToken.source();
export default {
  name: 'MarketTest',
  components: { codemirror },
  data() {
    return {
      showType: 'task',
      currentName: this.$route.query.name || '--',
      testCallAvailable: false,
      loading: false,
      completeLoading: false,
      cmOptions: {
        // codemirror options
        tabSize: 4,
        mode: {
          name: 'application/json',
          json: true
        },
        theme: 'base16-dark',
        // lineNumbers: true,
        line: true,
        indentWithTab: true,
        readOnly: false,
        maxScanLineLength: 500
        // lineWrapping: true
        // more codemirror options, 更多 codemirror 的高级配置...
      },
      jsonSample: {},
      inParam: '',
      outParam: '',
      headerParamList: [
        {
          label: 'Content-Type',
          value: ['application/json', 'multipart/form-data'],
          desc: 'application/json'
        },
        {
          label: 'X-GW-Authorization',
          value: '',
          desc: '用于权限认证，详见说明文档',
          placeholder: '请输入token'
        }
      ],
      appModule: {
        visible: false,
        token: '',
        appList: [],
        selectApp: null,
        guideApp: {
          id: -1,
          appName: '去绑定应用授权'
        }
      },
      requestUrl: ''
    };
  },
  computed: {
  },
  async mounted() {
    await this.getDeviceBindList()
    await this.getBeBindApp()
  },
  methods: {
    async getDeviceBindList() {
      this.tableLoading = true
      await queryAlgoConfigInfo({
        aiServiceId: +this.$route.query?.aiServiceId || '',
        // version_id: this.versionId,
      }).then(res => {
        console.log('interface res', res);
        this.tableLoading = false
        if (res?.data?.status === 200) {
          const data = res?.data?.data
          this.requestUrl =  data.requestUrlPrefix ? data.requestUrlPrefix + this.$route.query?.pathName : ''
          this.serviceId = data.serviceId;
        } else {
          this.$message.error('获取失败')
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    async getBeBindApp() {
      const _this = this;
      // const serviceId = this.serviceId;
      this.$post('/aiApp/queryAppListByServiceId', {
        serviceId: this.serviceId || ''
      }).then((res) => {
        console.log("res queryAppListByServiceId", res);

        _this.appModule.appList = res;
        if (res?.length) {
          this.appModule.selectApp = res[0] || {};
          this.initToken();
        }
      }).catch((error)=>{
        this.$message.error('获取失败')
      });
    },
    async initToken() {
      // 应用授权不属于工程内 所以需要跳出2级菜单
      // const { algorithmProjectId, type, projectName } = this.$route.query
      const app = this.appModule.selectApp;

        const res = await getAppListByServiceId({
          serviceId: +this.$route.query?.aiServiceId || ''
        })
        this.appModule.token = res.token
        this.$post('/aiApp/getToken', {
          appKey: app.userAppKey,
          appSecret: app.userAppSecret
        }).then((res) => {
          this.appModule.token = res.token;
          this.headerParamList[1].value = res.token;
          // 选择后情况下拉框
          // this.appModule.selectApp = null;
        });
    },
    startCall() {
      if (!this.requestUrl) {
        this.$message({
          message: '请先输入接口地址',
          type: 'warning'
        });
        return;
      }
      this.uniqueCallCtrl = true;

      const headers = {};
      this.headerParamList.forEach(function (item) {
        headers[item.label] = item.value;
      });
      let input = {};
      headers['Content-Type'] = 'application/json';
      input = this.inParam ? this.inParam : {};
      const status = {
        bool: true,
        id: this.serviceId
      };
      this.$store.commit('common/setInvokingLoading', status);
      let _url = this.requestUrl;
      this.$axios
        .post(_url, input, {
          timeout: 50 * 1000,
          headers: headers
        })
        .then((res) => {
          this.uniqueCallCtrl = false;
          status.bool = false;
          this.$store.commit('common/setInvokingLoading', status);
          if (res.status === 200) {
            this.alreadyCall = true;
            this.outParam = JSON.stringify(res.data, null, 4);
            if (res.data.code === 704 || res.data.code === 703) {
              this.$message.error('接口调用失败，没有token或token错误');
              return;
            }
            if (this.visualMarkFlag) {
              // 从json里将url或base64 取出来用于可视化展示
              if (Object.keys(input).length === 0) {
                this.resultType = 1;
                this.testCallDisabled = true;
                return;
              } else if (res.data.result === null || !res.data.result.data) {
                this.resultType = 1;
                this.testCallDisabled = true;
                return;
              }

              this.resultType = 2;
              this.testCallDisabled = false;
              const objJson = JSON.parse(input);
              const value = this.imageParams(objJson);
              const imgValue = Object.keys(value).includes('url') ? value.url : '';
              const base64Value = Object.keys(value).includes('image') ? value.image : '';
              if (imgValue) {
                // 创建一个Image对象
                let img = new Image();
                img.crossOrigin = 'Anonymous'; // 设置跨域属性
                img.src = imgValue; // 图片地址
                // 等待图片加载完成
                img.onload = () => {
                  // 创建一个canvas元素
                  let canvas = document.createElement('canvas');
                  canvas.width = img.width;
                  canvas.height = img.height;

                  // 将图片绘制到canvas中
                  let ctx = canvas.getContext('2d');
                  ctx.drawImage(img, 0, 0);

                  // 将canvas转换成Base64编码格式
                  let base64 = canvas.toDataURL('image/jpg');
                  // console.log('图片转换完成', base64)
                  this.visualMarkFormData_buff = {
                    imgUrl: '',
                    imgBase64: base64.split(',')[1],
                    imgJson: res.data.result.data
                  };
                  // 删除canvas
                  canvas = null;
                }
              } else {
                this.visualMarkFormData_buff = {
                  imgUrl: imgValue,
                  imgBase64: base64Value,
                  imgJson: res.data.result.data
                };
              }
            }
            this.$message.success('接口调用完成');
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((resErr) => {
          if (resErr.response?.status === 413) {
            this.$message.error({
              message: '调用失败，请求body大小不能超过20M'
            });
          }
          status.bool = false;
          this.$store.commit('common/setInvokingLoading', status);
          this.uniqueCallCtrl = false;
          if (resErr) {
            this.alreadyCall = true;
            try {
              this.outParam = JSON.stringify(resErr.response.data, null, 4);
            } catch (error) {
              try {
                this.outParam = JSON.stringify(resErr.response, null, 4);
              } catch (error) {
                try {
                  this.outParam = JSON.stringify(resErr, null, 4);
                } catch (error) { }
              }
            }
          }

          // this.$message.error(err)
        });
    },
    handleReset() {
      this.inParam = ''
    },
    checkJSON(type) {
      let finalData = this.inParam;
      if (type === '2') {
        finalData = this.outParam;
      }
      if (!finalData) {
        return;
      }
      if (typeof finalData === 'string') {
        try {
          const obj = type === '1' ? JSON.parse(this.inParam) : JSON.parse(this.outParam);
          if (typeof obj === 'object' && obj) {
            if (type === '1') {
              this.inParam = JSON.stringify(obj, null, 4);
            } else {
              this.outParam = JSON.stringify(obj, null, 4);
            }
          } else {
            this.$message.error('参数样例不是合法json，请检查并修改1。');
          }
          return true;
        } catch (e) {
          console.error(e);
          this.$message.error('参数样例不是合法json，请检查并修改2。');
          return false;
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.qa-loading-spinner3 {
  width: 42px;
  height: 36px;
  margin-left: 40px;
  background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
  background-size: 100% 100%;
  position: relative;
  border-radius: 6px;
}

.abilityList {
  max-height: 300px;
  overflow: auto;
  padding: 8px 0px;

  .abilityItem {
    cursor: pointer;
    max-width: 300px;
    /* 设置文本溢出时的行为为省略号 */
    text-overflow: ellipsis;
    /* 设置超出容器的内容应该被裁剪掉 */
    overflow: hidden;
    /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
    white-space: nowrap;
    line-height: 32px;
    padding: 0 5px;

    &:hover {
      background: #eff3ff;
    }

    &:active {
      background: #eff3ff;
    }

    &.abilityItemActive {
      background: #eff3ff;
      color: #4068d4;
    }
  }
}

::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  line-height: 20px !important;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

::v-deep .el-button.el-button--primary {
  line-height: 20px;
}

.imgBox {
  display: flex;
  justify-content: center;
}

.mytest {
  font-size: 14px;

  .el-button--text {
    display: none !important;
  }
}

.marketTest {
  display: flex;
  flex-direction: column;


  .testHeader {
    background-color: #fff;
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .headerTitle {
      // flex: 1;
      // cursor: pointer;

      .title {
        font-size: 18px;
        color: #323233;
        font-weight: bold;
        margin-right: 8px;
      }
    }
  }

  .testContent {
    flex: 1;
    margin: 16px 20px;
    background: url('@/assets/images/planGenerater/market-bg.png') no-repeat;
    background-size: cover;
    max-height: calc(100% - 84px);
    overflow-y: auto;
    position: relative;

    .qsBox {
      display: flex;
      justify-content: flex-start;

      .qs {
        font-size: 14px;
        color: #323233;
        line-height: 20px;
        margin: 20px;
        background: #ffffff;
        border-radius: 4px;
        padding: 8px 16px;
      }
    }

    .asBox {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin: 56px 20px 16px;

      .asContent {
        background: #ffffff;
        border-radius: 4px;
        padding: 8px 16px;
        // max-width: 50%;
        min-width: 80%;

        .service-other-subtitle-row {
          margin-bottom: 15px;

          .service-other-subtitle-col {
            margin-bottom: 15px;

            .service-other-subtitle {
              color: lightgray;
            }
          }
        }

        .headerContent {}

        .as {
          display: flex;
          justify-content: space-between;
          align-items: top;
          margin-bottom: 16px;

          .fileBox {
            display: flex;

            :deep(.el-button) {
              height: 32px;
            }

            :deep(.el-upload-list__item:first-child) {
              margin-top: 5px;
            }
          }

          .image {
            width: 100%;
            height: 200px;
            overflow: hidden;
            margin-top: 10px;
            color: rgb(150, 151, 153);

            img {
              max-width: 100%;
              max-height: 100%;
              display: block;
              margin: auto;
            }
          }
        }
      }
    }

    .qsResult {
      background: #ffffff;
      border-radius: 4px;
      padding: 8px 16px;
      border: 1px solid #dcdde0;
      margin: 0px 20px 0px;

      .reContent {
        color: #646566;
        line-height: 22px;
      }

      .reTitle {
        display: flex;
        align-items: center;

        img {
          width: 14px;
          height: 14px;
          margin-right: 8px;
        }

        .title {
          color: #323233;
          font-weight: bold;
        }
      }
    }

    .redo {
      margin-top: 8px;
      margin-left: 20px;
      color: #4068d4;
      cursor: pointer;

      &:hover {
        color: #2d4b9d;
      }
    }

    .el-empty {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

}

.inputContent {
  .div-textarea {
    border: 1px solid #dcdfe6;

    .div-box {
      display: inline-block;
      min-width: 100%;
    }

    ::v-deep .el-textarea__inner {
      border: none;
      height: 305px;
    }
  }
}


.task_model {
  // width: 60%;
  // width: 50%;
  background-color: #f3f6ff;
  border-radius: 5px;
  // margin: 10px auto;
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  // box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  min-width: 600px;
  height: calc(100vh - 88px);

  .header {
    display: flex;
    justify-content: flex-start;

    img {
      width: 48px;
      height: 48px;
      border-radius: 24px;
    }

    .title {
      font-weight: bold;
      font-size: 16px;
      margin: auto 5px;
    }
  }

  .main {
    margin-top: 15px;
    flex-grow: 1;
    display: flex;
    gap: 10px;
    overflow: auto;
    background-color: #f3f6ff;

    .req_container {
      background-color: #f3f6ff;
      border-radius: 5px;
      overflow: hidden;
      height: 98%;
      width: 50%;
      display: flex;
      flex-direction: column;

      .title {
        background-color: #fff;
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        border: 1px solid #dcdde0;
        border-radius: 5px;
        margin-bottom: 10px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

        .title_title {
          font-weight: bold;
          font-size: 16px; // 增大字体大小
          color: #333; // 使用深色以突出标题
          padding: 10px; // 增加内边距
          margin-bottom: 20px; // 增加下边距
          border-bottom: 2px solid #ccc; // 添加下划线
        }
      }

      .s_box {
        padding: 10px;
        background-color: #fff;
        border: 1px solid #dcdde0;
        border-radius: 5px;
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        // overflow-y: scroll;

        .param {
          flex-grow: 1;
          // overflow-y: scroll;

          .param_title {
            font-weight: bold;
            font-size: 16px; // 增大字体大小
            color: #333; // 使用深色以突出标题
            padding: 10px; // 增加内边距
            margin-bottom: 20px; // 增加下边距
            border-bottom: 2px solid #ccc; // 添加下划线
          }

          div {
            margin-bottom: 5px;
          }

          .label {
            font-size: 14px;
            font-weight: bold;
          }
        }

        .foot {
          background-color: #fff;
          display: flex;
          justify-content: flex-end;
          align-items: flex-end;

          .check-button {
            font-weight: 500;

            /* 按钮文字粗细 */
            &:hover {
              background-color: #66b1ff;
              /* 按钮悬停背景色 */
              border-color: #66b1ff;
              /* 按钮悬停边框色 */
            }

            &:active {
              background-color: #3a8ee6;
              /* 按钮激活背景色 */
              border-color: #3a8ee6;
              /* 按钮激活边框色 */
            }
          }
        }
      }
    }

    .result {
      background-color: #fff;
      border: 1px solid #dcdde0;
      border-radius: 5px;
      height: 98%;
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: bold;
        font-size: 16px; // 增大字体大小
        color: #333; // 使用深色以突出标题
        padding: 10px; // 增加内边距
        margin-bottom: 20px; // 增加下边距
        border-bottom: 2px solid #ccc; // 添加下划线
      }

      .content {
        word-wrap: break-word;
        margin-top: 10px;
      }
    }
  }
}

.service-other-subtitle-row {
  margin-bottom: 15px;

  .service-other-subtitle-col {
    margin-bottom: 15px;

    .service-other-subtitle {
      color: lightgray;
    }
  }
}
</style>
