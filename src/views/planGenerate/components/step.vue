<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="el-step-head_new is-process" :style="index === 0 ? 'margin-top: 30px;' : 'padding-top:30px'">
    <div class="el-step__line" :style="last ? 'display: none' :''"><i class="el-step__line-inner"
        style="transition-delay: 0ms; border-width: 0px; height: 0%;"></i></div>
    <div class="el-step__icon"><!---->
      <div class="el-step__icon-inner"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    index: {
      type: Number
    },
    last: {
      type: Boolean
    },
  },
  data() {
    return {

    }
  }
}
</script>

<style scoped lang="postcss">
.el-step__line {
    width: 2px;
    top: 0;
    bottom: -28px;
    left: 11px;
    margin-right: 0px;
}
.el-step-head_new {
  position: relative;
  line-height: 0;
}
.el-step__icon{
  height: 6px;
}
.el-step__icon-inner{
  width: 8px;
  height: 8px;
  background: #FFFFFF;
  border: 2px solid #4068D4;
  border-radius: 50%;
}
</style>
