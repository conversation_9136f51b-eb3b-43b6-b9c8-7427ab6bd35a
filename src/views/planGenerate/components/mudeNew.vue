<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="mude">
    <div class="mude_bg">
      <!--专业模型 -->
      <div class="flex_info">
        <div class="flex_info_title" style="margin-right:20px;">{{ childData.task_name }}</div>
        <Status :status="childData.task_status" ></Status>
        <!-- <el-dropdown>
          <Status :status="childData.status" showIcon></Status>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(items, index) in List" :key="index" style="padding: 5px 5px;" @click.native="changeStatus(childData,items.status)">
              <Status :status="items.status" style="margin-left:20px;"></Status>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
      </div>
      <!-- 描述信息 -->
      <div class="flex_body">
        <div class="flex_body_mx">
          <img src="@/assets/images/mx.png" alt="">
          描述信息:
        </div>
        <div class="flex_body_wz" v-html="childData.task_desc"></div>
      </div>
      <!-- <div class="flex_body btwin">
        <div class="flex_body_mx" style="width: 200px;text-align:left;margin-left:6px">
          <i class="el-icon-user"></i>
          建议角色:   <span style="margin-left: 11px;">{{ }}</span>
        </div>
        <div class="xiangq" @click="goRouter(childData.id)">查看详情</div>
      </div> -->
      <!-- <div class="flex_bottom" >
        <div></div>
        查看详情
      </div> -->
    </div>
  </div>
</template>

<script>
import Status from './statusNew.vue'
import service from '@/axios'
export default {
  components: {
    Status,
  },
  props: {
    childData: {
      type: Object
    }
  },
  data() {
    return {
      List: [{
        status: '待验证'
      }, {
        status: '研发中'
      }, {
        status: '已完成'
      },
      ],
    }
  },
  methods: {
    async changeStatus(items,status){
      const res = await service({
        method: 'post',
        url: process.env.VUE_APP_AI_CHAT + '/api/knowledgeBase/prepareTask/updateTaskStatus',
        headers: { 'appid': 'ai-chat-backend' },
        data: { 
          taskType:2,
          taskId:items.id,
          status:status,
         },
      })
      if (res.data.success) {
        this.$emit('showchildFun')
      }
    },
    goRouter(id) {
      this.$router.push({
        path: '/abilityReady/subTaskDetail',
        query: {
          ...this.$route.query,
          subTaskId: id,
          taskId: this.task.id
        }
      })
    },
  }
}
</script>

<style scoped lang="postcss">
.xiangq{
  margin-right: 20px;
  cursor: pointer;
font-family: PingFangSC, PingFang SC;
font-weight: 400;
font-size: 14px;
color: #4068D4;
line-height: 20px;
text-align: left;
font-style: normal;
}
.btwin{
  justify-content: space-between;
  align-items: center;
}
.flex_bottom {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4068D4;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
}

.mude {
  display: flex;
  align-items: center;
  /* padding: 20px; */
  flex-direction: column;
  /* height: calc(100vh - 432px); */
  overflow: auto;
  background: #ffffff;
  margin: 8px 16px 8px 0;
  flex: 1;
}

.mude_bg {
  width: 100%;
  margin-bottom: 10px;
}

.flex_info {
  display: flex;
  height: 43px;
  width: 100%;
  align-items: center;
  border-top: 1px solid #DCDDE0;
  padding-left: 27px;
}

.flex_info_title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #323233;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.flex_body {
  display: flex;

}

.flex_body_mx {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #646566;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  padding: 20px 0 20px 10px;
  width: 100px;
}

.flex_body_wz {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 20px 20px 0px 10px;
  flex: 1;
}
</style>
