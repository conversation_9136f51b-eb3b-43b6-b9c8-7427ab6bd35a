<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="goal">
    <el-tooltip effect="dark" :content="nickName + '(' + username + ')'" placement="top">
      <span class="avator" style="word-break: keep-all">{{
        nickName?.slice(-2)
      }}</span>
    </el-tooltip>
    <span style="margin-left: 10px;">{{ taskDesc }}</span>
  </div>
</template>

<script>
export default {
  props: {
    taskDesc: {
      type: String
    },
    username: {
      type: String
    },
    nickName: {
      type: String
    },
  },
  data() {
    return {
    }
  }
}
</script>

<style scoped lang="postcss">
.goal {
  padding: 20px;
}

.avator {
  width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  background: #e6ecff;
  color: #4068d4;
  word-break: keep-all;
  display: inline-block;
}
</style>
