<!-- CardList.vue -->
<template>
  <div :class="$store.state.planGenerate.isIframeHide ? 'page-container planContainerFrame' : 'page-container'">
    <div class="containerCard">
      <div class="page-header">
        <div style="flex: 1; min-width: 120px">
          <el-radio-group v-model="tabPosition" size="small" @input="handleChange">
            <el-radio-button label="1">全部</el-radio-button>
            <el-radio-button label="2">智能</el-radio-button>
            <el-radio-button label="3">传统</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="page-table-items" v-loading="loading">
        <div class="card-main">
          <el-row :gutter="16" type="flex" class="row-flex">
            <el-col v-for="(item, index) in tableData" :key="index" class="col-flex">
              <div class="gird-card">
                <div class="gird-card-content">
                  <div class="card-top">
                    <div class="card-img">
                      <img v-if="item.logoUrl" :src="item.logoUrl" alt="">
                      <img v-else src="@/assets/images/nl_1.png" alt="">
                      <div class="img-overlay"></div>
                    </div>
                    <div class="right">
                      <div style="display: flex;">
                        <div class="title-left">{{ item.appName }}</div>
                        <Status :text="statusTypeMap[item.deployStatus]?.text"
                                :bg-color="statusTypeMap[item.deployStatus]?.bgColor"
                                :dot-color="statusTypeMap[item.deployStatus]?.dotColor" />
                      </div>
                      <div class="card-tab">
                        <div class="sence-tag">ID:{{ item.id }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="card-detail">
                    <el-tooltip effect="dark" :content="item.appDesc || ''" placement="top">
                      <div>{{ item.appDesc || '' }}</div>
                    </el-tooltip>
                  </div>
                  <div class="creator">更新人: {{ item.updateByName }}</div>
                  <div class="creator">更新时间: {{ item.updateTime }}</div>
                  <div class="creator">发布版本: {{ item.version.version }}</div>
                </div>
                <div :class="['itemFooter', { 'show-before': item.deployStatus===2 }]">
                  <!--使用Popover 弹出框 里面有两个按钮 手机端链接 pc端链接-->
                  <el-popover placement="bottom" trigger="click" width="150">
                    <div class="popover-content">
                      <div class="popover-item">
                        <SvgIcon name="mobile-link" style="margin-right: 2px;" />
                        <el-button class="button-last" type="text" @click="handleMobile(item)">手机端链接</el-button>
                      </div>
                      <div class="popover-item">
                        <SvgIcon name="pc-link" style="margin-right: 2px;" />
                        <el-button class="button-last" type="text" @click="handlePC(item)">电脑端链接</el-button>
                      </div>
                    </div>
                    <el-button slot="reference" class="button-last" type="text">
                      <SvgIcon name="link" style="margin-right: 2px;" />
                      复制链接
                    </el-button>
                  </el-popover>
                  <el-button v-if="item.deployStatus===2" class="button-last" type="text" @click="handleShow(item)">
                    <SvgIcon name="right-rect" style="margin-right: 2px;" />
                    试一试
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div
          v-if="!tableData.length" style="
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: row;
            justify-content: center;
          ">
          <el-empty description="暂无数据"></el-empty>
        </div>
      </div>
      <div class="page-footer">
        <el-pagination prev-text="上一页" next-text="下一页" background style="float: right"
                       :current-page="pageParams.pageNum" :page-size="pageParams.pageSize"
                       :page-sizes="[12, 24, 36, 48, 60]"
                       layout="total, prev, pager, next" :total="pageParams.total" @size-change="getQueryPageList"
                       @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAppListService // 假设这是获取卡片能力列表的API
} from '@/api/cardService.js';
import Status from '@/components/Status/index.vue';
import { getWsID } from '@/api/planGenerateApi';

export default {
  name: 'CardList',
  components: {
    Status
  },
  data() {
    return {
      loading: false,
      tabPosition: '1',
      tableData: [],
      pageParams: {
        pageNum: 1,
        pageSize: 12,
        spaceId: '',
        appClass: '',
        appName: '',
        tenantId: '',
        total: 0
      },
      statusTypeMap: {
        1: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '未发布' },
        2: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已发布' }
      }
    };
  },
  created() {
    this.getSpaceInfoDetail();
  },
  mounted() {
    this.getQueryPageList();
  },
  methods: {
    handlSearch(val) {
      console.log('val', val);
      this.pageParams.appName = val;
      this.getQueryPageList();
    },
    handleShow(row) {
      // 打开新窗口访pcUrl
      window.open(row.pcUrl);
    },
    handlePC(row) {
      // 复制到剪切板
      navigator.clipboard.writeText(row.pcUrl);
      this.$message({
        type: 'success',
        message: '电脑端链接复制成功！'
      });
    },
    handleMobile(row) {
      // 复制到剪切板
      navigator.clipboard.writeText(row.h5Url);
      this.$message({
        type: 'success',
        message: '手机端链接复制成功！'
      });
    },
    async getQueryPageList() {
      try {
        this.loading = true; // 开始加载
        if (this.tabPosition === '1') {
          this.pageParams.appClass = '';
        }
        if (this.tabPosition === '2') {
          this.pageParams.appClass = 1;
        }
        if (this.tabPosition === '3') {
          this.pageParams.appClass = 0;
        }
        const params = {
          spaceId: this.pageParams.spaceId,
          appClass: this.pageParams.appClass,
          appName: this.pageParams.appName,
          tenantId: this.pageParams.tenantId,
          pageNum: this.pageParams.pageNum,
          pageSize: this.pageParams.pageSize
        };
        console.log('params', params);
        const res = await getAppListService(params);
        console.log('app list res ', res);
        this.tableData = res.data.data.data.data;
        this.pageParams.total = res.data.data.data.total;
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },
    handleCurrentChange(event) {
      this.pageParams.pageNum = event;
      this.getQueryPageList();
    },
    handleChange(event) {
      this.getQueryPageList();
    },
    // 查询空间详情
    async getSpaceInfoDetail() {
      try {
        const res = await this.$post(`${this.baseUrl}/workspace/detail`, {
          id: getWsID()
        });
        // res.cardSpaceId如果值存在则转成Number不存在默认0
        this.pageParams.spaceId = res.cardSpaceId || 0;
        console.log('获取空间详情成功:', res);
      } catch (error) {
        console.error('获取空间详情失败:', error);
        // 处理错误情况
      }
    }
  }
};
</script>

<style scoped lang="scss">
.page-container {
  max-height: calc(100vh - 90px);
  height: calc(100vh - 90px);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;

  &.planContainerFrame {
    max-height: calc(100vh) !important;
  }

  .planSearch {
    background-color: #fff;
    position: relative;

    .search {
      // margin-top: 10px;
      position: relative;
      overflow: hidden;

      :deep(.header-container__container-border) {
        padding-bottom: 0px;
        margin-bottom: 0px;
        border-bottom: none;
      }

      .planSearch1 {
        margin-top: 10px;
        padding: 0 20px;
      }

      .footer-search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .el-input {
          // width: 65%;
          margin: 5px 0px 5px 0px;
        }

        // .search-button {
        //     background-color: #4068d4; /* 按钮背景色 */
        //     border-color: #4068d4; /* 按钮边框色 */
        //     color: #fff; /* 按钮文字颜色 */
        //     font-weight: 500; /* 按钮文字粗细 */
        //   }

        // .search-button:hover {
        //   background-color: #66B1FF; /* 按钮悬停背景色 */
        //   border-color: #66B1FF; /* 按钮悬停边框色 */
        // }

        // .search-button:active {
        //   background-color: #3A8EE6; /* 按钮激活背景色 */
        //   border-color: #3A8EE6; /* 按钮激活边框色 */
        // }
      }
    }

    .searchIcon {
      position: absolute;
      left: 50%;
      bottom: -10px;
      transform: translateX(-50%);

      .searchIconTaggle {
        width: 40px;
        height: 20px;
        background: #d9e6f8;
        border-radius: 2px;
        position: relative;
        border: 1px solid #f2f3f5;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4068d4;
        cursor: pointer;

        &:hover {
          background: #a1bbef;
        }
      }
    }

    .headerTitle {
      font-weight: bold;
      color: #323233;
      line-height: 26px;
      font-size: 18px;
      padding: 14px 20px;
    }

    .button-last {
      line-height: 14px;
    }
  }

  .containerCard {
    height: calc(100vh - 149px);
    max-height: calc(100vh - 149px);
    overflow: hidden;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: #fff;
    padding: 16px 20px;

    & .page-header {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;

      ::v-deep .el-radio-button__inner {
        height: 30px;
        line-height: 19px;
        padding: 4px 16px;
        font-size: 14px;
        border-radius: 2px 0 0 2px;
      }

      .rg {
        display: flex;
        align-items: center;

        .sortedScheme {
          display: flex;
          align-items: center;
          margin-right: 24px;

          > span {
            margin-right: 8px;
            font-size: 14px;
            color: #646566;
          }
        }
      }
    }

    & .page-table-items {
      height: calc(100% - 66px);
      margin-top: 16px;
      width: 100%;
      // width: calc(100% + 12px);
      overflow-x: hidden;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-content: flex-start;

      & .table-no-header {
        border-top: 1px solid #ebecf0;

        ::v-deep .el-table__header {
          display: none;
        }
      }

      & .page-table-item2 {
        width: calc(33.3333% - 12px);
        max-width: calc(33.3333% - 12px);
        margin-right: 12px;
        border-radius: 4px;
        // min-width: 275px;
        margin-bottom: 15px;
        border: 1px solid #dcdde0;

        .headerBox {
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          width: 100%;
          border-bottom: 1px solid rgb(220, 221, 224);
        }

        // .sence-tag {
        //   display: inline-flex;
        //   padding: 0 8px;
        //   margin-right: 10px;
        //   height: 24px;
        //   border-radius: 2px;
        //   background: #ebf9ff;
        //   color: #318db8;
        // }

        .el-status {
          margin-left: 16px;
          margin-bottom: 14px;
          display: flex;
          justify-content: left;
          align-items: center;
          border-radius: 2px;
          line-height: initial;
          font-size: 14px;

          .point {
            width: 5px;
            height: 5px;
            margin-right: 5px;
            background: #7d7e80;
            vertical-align: middle;
            display: inline-flex;
            align-items: center;
          }

          &.success {
            background: #d7eedb;

            .point {
              background: #39ab4c;
            }
          }
        }

        .header {
          flex: 1;
          display: flex;
          align-items: center;
          line-height: 24px;
          width: 100%;

          .el-icon-warning-outline {
            color: #4068d4;
            padding: 0 8px 0 16px;
            cursor: pointer;
          }
        }

        &:hover {
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 16px;
  border-radius: 2px;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.itemFooter {
  border-top: 1px solid #c8c9cc;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  max-width: 100%;
  overflow: hidden;
}

.hidden-sc {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.scene-status {
  padding: 0 8px;
  height: 24px;
  border-radius: 2px;
  max-width: calc(100% - 80px);
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;

  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;

  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;

  &.equi {
    background: #ebf9ff;
    color: #318db8;
  }

  &.client {
    background: #fff2eb;
    color: #cc7846;
  }

  &.other {
    background: #f3eafe;
    color: #7a4db8;
  }

  &.lingdao {
    background: #e6ecff;
    color: #3455ad;
  }
}

::v-deep .el-dialog__body {
  border-top: 1px solid #ebecf0;
  padding: 10px 20px;
}

.statistics {
  display: flex;
  align-items: center;
  background: #f6f7fb;

  > div {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    cursor: pointer;

    &:hover {
      background: #ebecf0;
    }

    > img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }
}

.line-chart {
  width: 100%;
  height: 162px;
  margin: 16px 0;
}

.contentDesc {
  margin: 16px 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  font-weight: normal;
  color: #646566;
  line-height: 22px;
  font-size: 14px;

  .descLabel {
    color: #646566;
    word-break: keep-all;
  }

  .descValue {
    color: #323233;
    line-height: 22px;
  }
}

</style>
<style lang="scss" scoped>
.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.card-main {
  width: 100%;
}

.itemFooter {
  position: relative;
  border-top: 1px solid #c8c9cc;
  display: flex;
  flex-direction: row;
  justify-content: space-around; // 修改为 space-between
  align-items: center;
  padding: 12px 16px;
  max-width: 100%;
  overflow: hidden;


  .el-button:hover {
    // background-color: #4068d4; // 或者您希望的悬停颜色
    // border-color: #4068d4;

    // ::after {
    //   content: ">>";
    // }
  }

}

.itemFooter::before {
  content: "";
  /* 创建伪元素内容为空 */
  position: absolute;
  /* 伪元素使用绝对定位 */
  top: 25%;
  /* 伪元素从顶部开始 */
  left: 50%;
  /* 将伪元素水平放在父元素中间 */
  width: 1px;
  /* 设置伪元素的原始宽度为1px */
  height: calc(100% - 24px);
  /* 伪元素的高度与父元素保持一致 */
  background-color: #ccc;
  /* 分割线颜色 */
  transform: scaleX(0.5);
  /* 将宽度缩小一半，达到更清晰的1px效果 */
  transform-origin: center;
  /* 缩放中心为伪元素的中心位置 */
  display: none;
}

.itemFooter.show-before::before {
  display: block;
  /* 当类存在时显示伪元素 */
}

.row-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.col-flex {
  // flex: 1; /* 确保均匀分布且不会被内容撑大 */
  min-width: 0;
  /* 防止内容撑大 */
  overflow: hidden;
  /* 隐藏超出部分 */
  flex-basis: calc(100% / 4); // 默认一排显示4个卡片
  margin-bottom: 16px;
  box-sizing: border-box;
}

.gird-card:hover {
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.16);
}

.gird-card {
  height: 273.3px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #DCDDE0;
  box-sizing: border-box;

  &-content {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .creator {
      font-weight: 400;
      font-size: 14px;
      color: #969799;
      line-height: 10px;
      text-align: left; // 确保靠左对齐
      font-style: normal;
    }

    .card-top {
      display: flex;
      flex-direction: row;
      // justify-content: space-between;
      align-items: center;


      .right {
        align-self: flex-start;
        max-width: calc(100% - 76px)
      }
    }


    .card-img {
      position: relative;
      margin-right: 16px;
      overflow: hidden;
      border-radius: 5px;
      /* 确保图片超出部分被裁剪 */

      img {
        height: 60px;
        width: 60px;
        /* 或者您希望的圆角值 */
        object-fit: cover;
        transition: transform 0.3s ease;
        /* 平滑过渡效果 */
      }

      // .img-overlay {
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   width: 100%;
      //   height: 100%;
      //   background: rgba(0, 0, 0, 0.5);
      //   opacity: 0;
      //   transition: opacity 0.3s ease;
      // }

      &:hover img {
        transform: scale(1.3);
        /* 放大图片 */
      }

      // &:hover .img-overlay {
      //   opacity: 1;
      // }
    }

    .title-left {
      white-space: nowrap;
      /* 防止文本换行 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      /* 显示省略号 */
      height: 24px;
      margin: 0px 8px 8px 0px;
      line-height: 24px;
      font-weight: 500;
      font-size: 18px;
      color: #1D2129;
      font-family: PingFangSC, PingFang SC;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .card-detail {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 限制为三行 */
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
      font-weight: 200;
      font-size: 14px;
      color: #646566;
      line-height: 20px;
      font-style: normal;
      min-height: 30px;
    }

    .creat-tab {
      display: flex;
      margin: 8px 0px;

      .name {
        margin-right: 12px;
      }

      .tab-item {
        font-weight: 400;
        font-size: 14px;
        color: #969799;
        line-height: 20px;
        text-align: justify;
        font-style: normal;
      }
    }

    .card-tab {
      display: flex;

      .sence-tag {
        display: inline-flex;
        padding: 0 8px;
        margin-right: 10px;
        height: 24px;
        border-radius: 2px;
        background: #EBF9FF;
        color: #318db8;
      }

      .tab-title {
      }
    }
  }

  .itemFooter {
    border-top: 1px solid #c8c9cc;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 16px;
    max-width: 100%;
    overflow: hidden;
  }
}

// 媒体查询，调整不同屏幕尺寸下的卡片宽度
@media (max-width: 1500px) {
  .col-flex {
    flex-basis: calc(100% / 3); // 一排显示3个卡片
  }
}

@media (max-width: 1280px) {
  .col-flex {
    flex-basis: calc(100% / 3); // 一排显示3个卡片
  }
}

@media (max-width: 1080px) {
  .col-flex {
    flex-basis: calc(100% / 2); // 一排显示2个卡片
  }
}

@media (max-width: 768px) {
  .col-flex {
    flex-basis: 100%; // 一排显示1个卡片
  }
}
</style>
