<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="tag" :style="{ backgroundColor: bg }">
    <div class="tag_color" :style="{ backgroundColor: color }"></div>
    <div class="tag_font">{{ font }}</div>
    <i class="el-icon-arrow-down padding" v-if="showIcon"></i>
  </div>
</template>

<script>
export default {
  props: {
    status: {
      type: String | Number
    },
    showIcon:{
      type: Boolean
    }
  },
  data() {
    return {
      font: '',
      color: '',
      bg: ''
    }
  },
  watch: {
    status: {
      // status|状态 0-待生产/1-生产中/2-生产完成/3-生产失败|string||
      handler(newVal, oldVal) {
        switch (newVal) {
          case 2:
            this.font = '已完成',
              this.color = '#39AB4C',
              this.bg = '#EBF6ED'
            break;
          case 1:
            this.font = '研发中',
              this.color = '#4068D4',
              this.bg = '#EBEFFA'
            break
          case 0:
            this.font = '未开始',
              this.color = '#EFA900',
              this.bg = '#FDF6E5'
            break
        }
      },
      immediate: true,
      deep: true
    }

  }
}
</script>

<style scoped lang="postcss">
.padding{
  padding: 0 5px;
}
.tag {
  width: auto;
  height: 24px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 0px!important;
  padding: 0 10px;
}

.tag_color {
  width: 7px;
  height: 7px;
  border-radius: 2px;
}

.tag_font {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-left: 8px;
}
</style>
