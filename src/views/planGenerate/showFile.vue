<template>
    <div class="full">
        <div class=" icon">
            <i class="el-icon-close font" @click="close"></i>
        </div>
        <div v-if="hzmFile == 'jpg' || hzmFile == 'png' || hzmFile == 'jpeg'">
            <ShowImg :src="filePath" />
        </div>
        <iframe :src="filePath" frameborder="0" v-if="hzmFile == 'pdf' || this.hzmFile === 'txt'"></iframe>
        <iframe :src="wordUrl" frameborder="0"
            v-if="hzmFile == 'xls' || hzmFile == 'xlsx' || hzmFile == 'docx' || hzmFile == 'doc'"></iframe>
    </div>
</template>

<script>
import ShowImg from './showImg'
export default {
    components: {
        ShowImg
    },
    props: {
        filePath: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            sheetNames: null, // 从数据中获取到的sheet页数组
            currentSheet: '',
            columns: [], // 表头
            excelData: null, // Excel 数据
            wordUrl: '',
            docxOptions: {
                className: 'kaimo-docx-666', // string：默认和文档样式类的类名/前缀
                inWrapper: true, // boolean：启用围绕文档内容的包装器渲染
                ignoreWidth: false, // boolean：禁用页面的渲染宽度
                ignoreHeight: false, // boolean：禁止渲染页面高度
                ignoreFonts: false, // boolean：禁用字体渲染
                breakPages: true, // boolean：在分页符上启用分页
                ignoreLastRenderedPageBreak: true, // boolean：在 lastRenderedPageBreak 元素上禁用分页
                experimental: false, // boolean：启用实验功能（制表符停止计算）
                trimXmlDeclaration: false, // boolean：如果为true，解析前会从文档中移除 xml 声明
                useBase64URL: true, // boolean：如果为true，图片、字体等会转为base 64 URL，否则使用URL.createObjectURL
                useMathMLPolyfill: false, // boolean：包括用于 chrome、edge 等的 MathML polyfill。
                showChanges: false, // boolean：启用文档更改的实验性渲染（插入/删除）
                debug: false // boolean：启用额外的日志记录
            },
            hzmFile: '',
            error: null
        };
    },
    methods: {
        close() {
            this.$emit('close')
        },
        // 获取文件名
        getFileNameFromUrl(url) {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            return pathname.split('/').pop() || '';
        },
        // 获取文件扩展名
        getFileExtension(filename) {
            const parts = filename.split('.');
            return parts.pop() || '';
        },
        // 更新文件扩展名并根据类型加载文件
        updateFileTypeAndLoad() {
            if (!this.filePath) return;
            const fileName = this.getFileNameFromUrl(this.filePath);
            this.hzmFile = this.getFileExtension(fileName);
            if (this.hzmFile === 'xls' || this.hzmFile === 'xlsx' || this.hzmFile === 'docx' || this.hzmFile === 'doc') {
                let val = encodeURIComponent(this.filePath)
                this.wordUrl = `https://view.officeapps.live.com/op/view.aspx?src=${val}`
            }
        }
    },
    watch: {
        filePath: {
            handler() {
                this.updateFileTypeAndLoad();
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.updateFileTypeAndLoad();
    }
}
</script>

<style lang="postcss" scoped>
.full {
    width: 100%;
    height: calc(100vh - 261px);
    position: absolute;
    overflow: auto;
    /* background: rgba(0, 0, 0, 0.5); */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1111;
}

iframe {
    position: absolute;
    /* top: 20px; */
    width: 100%;
    /* 撑满父容器的宽度 */
    height: 100%;
    /* 撑满父容器的高度 */
    border: none;
    background: #fff;
    /* 去掉边框 */
}

.icon {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 11;
    font-size: 22px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
}

.font {
    color: #fff;
    font-size: 20px;
}
.screenshot-container {
  text-align: center;
}
.cropper-wrap img {
  max-width: 100%;
  height: auto;
}
</style>