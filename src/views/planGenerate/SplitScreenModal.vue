<template>
    <div v-if="showSplitScreenModal" class="split-screen-modal" @click.self="closeSplitScreenModal">
      <div class="modal-content">
        <div class="modal-header">
          <span>分屏视图</span>
          <button @click="closeSplitScreenModal" class="close-button">×</button>
        </div>
        <div class="modal-body">
          <!-- 左右布局 -->
          <div class="split-container">
            <!-- 左侧：聊天区域 -->
            <ChatComponent :chatMessages="chatMessages" @send-message="handleSendMessage" />
            <!-- 右侧：代码和预览区域 -->
            <div class="code-container">
              <!-- 切换按钮 -->
              <div class="mode-switcher">
                <el-switch
                  v-model="isPreviewMode"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-text="预览"
                  inactive-text="代码"
                >
                </el-switch>
              </div>
              <!-- 代码模式 -->
              <div v-if="!isPreviewMode" class="code-mode">
                <pre>{{ splitScreenContent }}</pre>
              </div>
              <!-- 预览模式 -->
              <div v-if="isPreviewMode" class="preview-mode">
                <EnnHTML
                  :executeResult="item.htmlContent"
                  :isLoading="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script>
  import EnnHTML from './ConfTask/toolComponents/EnnHTML.vue';
  import ChatComponent from './chatComponent.vue';

  export default {
    components: {
      ChatComponent,
      EnnHTML
    },
    props: {
      showSplitScreenModal: {
        type: Boolean,
        required: true
      },
      splitScreenContent: {
        type: String,
        required: true
      },
      chatMessages: {
        type: Array,
        required: true
      },
      isPreviewMode: {
        type: Boolean,
        required: true
      },
      item: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        newMessage: '' // 用户输入的新消息
      };
    },
    methods: {
      closeSplitScreenModal() {
        this.$emit('close-split-screen-modal');
      },
      sendMessage() {
        if (this.newMessage.trim() === '') return; // 如果消息为空，则不发送
        const message = {
          text: this.newMessage,
          time: new Date().toLocaleTimeString() // 获取当前时间
        };
        this.$emit('send-message', message); // 触发发送消息事件
        this.newMessage = ''; // 清空输入框
      }
    }
  }
  </script>

  <style lang="scss" scoped>
  .split-screen-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* 半透明背景 */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000; /* 确保弹窗在最上层 */
  }

  .modal-content {
    background: #fff;
    border-radius: 8px;
    width: 100%; /* 宽度占用 100% */
    height: 95%; /* 高度占用 95% */
    overflow: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .modal-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: bold;
  }

  .modal-body {
    padding: 16px;
    height: calc(100% - 64px); /* 减去 header 的高度 */
    overflow: auto;
  }

  /* 左右布局容器 */
  .split-container {
    display: flex;
    height: 100%;
  }

  /* 左侧：聊天区域 */
  .chat-container {
    width: 50%; /* 左侧占 50% */
    height: 100%;
    overflow-y: auto; /* 支持滚动 */
    border-right: 1px solid #eee; /* 分割线 */
    padding-right: 16px; /* 右侧内边距 */
    display: flex;
    flex-direction: column;
  }

  /* 聊天消息显示区域 */
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 16px;
  }

  /* 聊天消息样式 */
  .chat-message {
    margin-bottom: 16px;
  }

  .message-text {
    font-size: 14px;
    color: #333;
  }

  .message-time {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  /* 用户输入区域 */
  .chat-input {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-top: 1px solid #eee;
  }

  .chat-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
  }

  .chat-input button {
    padding: 8px 16px;
    background: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #0056b3;
    }
  }

  /* 右侧：代码和预览内容 */
  .code-container {
    width: 50%; /* 右侧占 50% */
    height: 100%;
    overflow-y: auto; /* 支持滚动 */
    padding-left: 16px; /* 左侧内边距 */
    display: flex;
    flex-direction: column;
  }

  /* 模式切换按钮 */
  .mode-switcher {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  /* 代码模式 */
  .code-mode {
    flex: 1;
    overflow-y: auto;
  }

  .code-mode pre {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow: auto;
    height: 100%; /* 代码块高度占满右侧区域 */
    max-height: 100% !important;
  }

  /* 预览模式 */
  .preview-mode {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;

    &:hover {
      color: #333;
    }
  }
  </style>
