<template>
  <div class="chatContainerTest" :class="{'chatContainerTestFrame': isEmbedMode}" :style="!isHideIframe ?  getJujiaoStatus.status ? 'max-height: calc(100vh - 48px);': 'max-height: calc(100vh - 88px);':'max-height: 100vh;'">
    <div class="containerBox">
      <div v-show="phoneFlag" id="left-phone" class="phoneContent">
        <el-tooltip class="item" effect="dark" :content="currentRoleInfo?.name
            ? `${currentRoleInfo?.name}（${currentRoleInfo.alias}）`
            : '专家工作台'
          " placement="top">
          <div v-if="currentRoleInfo?.name" class="phoneTitle">
            {{ currentRoleInfo?.name }}（{{ currentRoleInfo.alias }}）
          </div>
          <div v-else class="phoneTitle">专家工作台</div>
        </el-tooltip>
        <div class="phonePhoto">
          <div v-show="([
              'process stream',
              'process stream running',
              'process running',
              'processing',
              'scheme generating',
              'clear_history',
              'process_stream_message'
            ].indexOf(systemMessages) > -1 &&
              !historyChat.messages[historyChat.messages.length - 1].id) ||
  hasChatingName !== '' || isOccupied ||
            qaBoxLoading
            " class="bubble">
            <img src="@/assets/images/planGenerater/voice.gif" />
          </div>
          <div class="phonePh">
            <img v-if="currentRoleInfo?.icon" :src="currentRoleInfo.icon" />
            <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
          </div>
        </div>
        <div class="phoneStatus">
          <div v-if="phoneStatus === 'start'" @click="startWeb">
            <img src="@/assets/images/planGenerater/call.png" width="40px" />
          </div>
          <div v-if="phoneStatus === 'running'" @click="() => {
              stopWeb()
              phoneStatus = 'start'
            }
            ">
            <img src="@/assets/images/planGenerater/voice.gif" width="60px" />
            <div>正在听...</div>
          </div>
          <div v-if="phoneStatus === 'shibie'">
            <img src="@/assets/images/planGenerater/loading.gif" width="60px" />
            <div>语音识别中...</div>
          </div>
        </div>
        <div class="phoneClose">
          <div :class="!(
              ([
                'process stream',
                'process stream running',
                'process running',
                'processing',
                'scheme generating',
                'clear_history',
                'process_stream_message'
              ].indexOf(systemMessages) > -1 &&
                !historyChat.messages[historyChat.messages.length - 1].id) ||
  hasChatingName !== '' || isOccupied ||
              shibieLoading ||
              qaBoxLoading
            )
              ? 'phoneStop phoneStopDisabled'
              : 'phoneStop'
            " @click="stopThink">
            <img src="@/assets/images/planGenerater/stop.png" />
            <!-- <el-button type="danger" icon="el-icon-video-pause" :disabled="!(   )" circle ></el-button> -->
          </div>
          <div class="phoneClone" @click="closePhone">
            <img src="@/assets/images/planGenerater/close.png" />
          </div>
        </div>
      </div>
      <div class="new_flex">
        <div class="new_flex new_flex_width">
          <div v-show="planDetailShow && !shrinkLeft" id="left-content" :style="{
        width: leftWidth,
        maxWidth: leftWidth,
        marginRight: planDetailShow && !phoneFlag ? '0px' : '0px',
        userSelect: isDragging ? 'none' : 'auto',
        transition: isDragging ? 'none' : 'width 0.2s',
        position: thinkFullFlag ? '' : 'relative',
        marginLeft: phoneFlag ? '0px' : '0px',
        marginTop:'0px!important',
        minWidth: minWidth+'px',
        height: (rightFullFlag && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
        maxHeight: (rightFullFlag && !isHideIframe) ?  'calc(100% - 48px)':  '100%',
        top: (rightFullFlag && !isHideIframe) ?  '48px':  '0px',
      }" :class="rightFullFlag ? 'containerCard customMd containerCardFull' : 'customMd containerCard'
          " v-loading="kickLoading" element-loading-spinner="el-icon-loading">
        <div class="tab-switch">
          <div
            :class="['tab-btn', { activetab: activeTab === 'chat' }]"
            @click="ChangeactiveTab('chat')"
          >
            Chat
          </div>
          <div
            :class="['tab-btn', { activetab: activeTab === 'builder' }]"
            @click="ChangeactiveTab('builder')"
          >
            Builder
          </div>
        </div>
        <Model v-show="activeTab === 'builder' && !ModalType"v-loading="ModalType" ref="chat" :ModalType="ModalType" :isReadOnly="isReadOnly" :leftWidth="leftWidth"
          :choseClick2="choseClick2" :choseClick3="choseClick3" :onClickOutside2="onClickOutside2"  :onClickOutside3="onClickOutside3"  :selectAgentRoleNew="selectAgentRoleNew"
          :adminJueSe="adminJueSe" :showSelectVisable2="showSelectVisable2" :noKickFeat="noKickFeat" :getRoleList="getRoleList"
          :handleModalCodeGenerate="handleModalCodeGenerate" :realTime="realTime" :clearImage="clearImage"
          :uploadUrl="uploadUrl" :uploadParam="uploadParam" :stopRecording="stopRecording" :yuyinText="yuyinText" :abilitys="abilitys" :firstUrl="firstUrl"
          :handleChangeRole="handleChangeRole" :clearHistory="clearHistory" :choseClick="choseClick" :shanchu="shanchu"
          :currentRoleInfo="currentRoleInfo" :agentAvatorInfo="agentAvatorInfo" :startWeb="startWeb" :jjRole="jjRole"
          :thinkFullFlag="thinkFullFlag" :closechangeThinkWrap="closechangeThinkWrap" :thinkFlag="thinkFlag"
          :changeThinkFull="changeThinkFull" :changeThinkWrap="changeThinkWrap" :agentAvatorInfoMap="agentAvatorInfoMap"
          :startPolling="startPolling" :handleUpdateKickedLoading="handleUpdateKickedLoading" :kickLoading="kickLoading"
          :isEmbedTool="isEmbedTool" :carriageReturn="carriageReturn" :checkRole="checkRole"
          :chendianVisable2="chendianVisable2" :startRecording="startRecording" :selectAgentRole="selectAgentRole"
          :adminRole="adminRole" :instanceInfo="instanceInfo" :modelUploadSuccess="modelUploadSuccess"
          :beforeUpload="beforeUpload" :agentRoleList="agentRoleList" :handleInput="handleInput"
          :speakingFlag="speakingFlag" :toMessage="toMessage" :clearChose="clearChose" :choseData="choseData"
          :addChose="addChose" :closeCz="closeCz" :openCz="openCz" :ischuanzuoShow="ischuanzuoShow" :showSelectVisable3="showSelectVisable3"
          :showSelectVisable="showSelectVisable" :currentRoleInfoTemp="currentRoleInfoTemp" :agentError="agentError"
          :handleAsk="handleAsk" :qaList="qaList" :phoneFlag="phoneFlag" :rightFullFlag="rightFullFlag"
          :systemMessages="systemMessages" :historyChat2="historyChat2" :isOccupied="isOccupied"
          :hasChatingName="hasChatingName" :shibieLoading="shibieLoading" :qaBoxLoading="qaBoxLoading"
          :isDragging="isDragging" :showButton="showButton" :openModal="openModal" :sendMessage2="sendMessage2"
          :stopThink="stopThink" :processContent="processContent" :onClickOutside="onClickOutside" :doneSend="doneSend"
          :handleSave="handleSave" :handleCreate="handleCreate" :taskStatusText="taskStatusText" :saveImg="saveImg"
          :changeShowFull="changeShowFull" :localCommands="localCommands" :localTags="localTags" :tagSelectedChange="tagSelectedChange"  :tagUpdate="tagUpdate"></Model>
        <Chat v-show="activeTab === 'chat' && !ModalType" v-loading="ModalType" ref="chat" :ModalType="ModalType" :isReadOnly="isReadOnly" :leftWidth="leftWidth"
          :choseClick2="choseClick2" :choseClick3="choseClick3" :onClickOutside2="onClickOutside2"  :onClickOutside3="onClickOutside3"  :selectAgentRoleNew="selectAgentRoleNew"
          :adminJueSe="adminJueSe" :showSelectVisable2="showSelectVisable2" :noKickFeat="noKickFeat" :getRoleList="getRoleList"
          :handleModalCodeGenerate="handleModalCodeGenerate" :realTime="realTime" :clearImage="clearImage"
          :uploadUrl="uploadUrl" :uploadParam="uploadParam" :stopRecording="stopRecording" :yuyinText="yuyinText" :abilitys="abilitys" :firstUrl="firstUrl"
          :handleChangeRole="handleChangeRole" :clearHistory="clearHistory" :choseClick="choseClick" :shanchu="shanchu"
          :currentRoleInfo="currentRoleInfo" :agentAvatorInfo="agentAvatorInfo" :startWeb="startWeb" :jjRole="jjRole"
          :thinkFullFlag="thinkFullFlag" :closechangeThinkWrap="closechangeThinkWrap" :thinkFlag="thinkFlag"
          :changeThinkFull="changeThinkFull" :changeThinkWrap="changeThinkWrap" :agentAvatorInfoMap="agentAvatorInfoMap"
          :startPolling="startPolling" :handleUpdateKickedLoading="handleUpdateKickedLoading" :kickLoading="kickLoading"
          :isEmbedTool="isEmbedTool" :carriageReturn="carriageReturn" :checkRole="checkRole"
          :chendianVisable2="chendianVisable2" :startRecording="startRecording" :selectAgentRole="selectAgentRole"
          :adminRole="adminRole" :instanceInfo="instanceInfo" :modelUploadSuccess="modelUploadSuccess"
          :beforeUpload="beforeUpload" :agentRoleList="agentRoleList" :handleInput="handleInput"
          :speakingFlag="speakingFlag" :toMessage="toMessage" :clearChose="clearChose" :choseData="choseData"
          :addChose="addChose" :closeCz="closeCz" :openCz="openCz" :ischuanzuoShow="ischuanzuoShow" :showSelectVisable3="showSelectVisable3"
          :showSelectVisable="showSelectVisable" :currentRoleInfoTemp="currentRoleInfoTemp" :agentError="agentError"
          :handleAsk="handleAsk" :qaList="qaList" :phoneFlag="phoneFlag" :rightFullFlag="rightFullFlag"
          :systemMessages="systemMessages" :historyChat="historyChat" :isOccupied="isOccupied"
          :hasChatingName="hasChatingName" :shibieLoading="shibieLoading" :qaBoxLoading="qaBoxLoading"
          :isDragging="isDragging" :showButton="showButton" :openModal="openModal" :sendMessage="sendMessage"
          :stopThink="stopThink" :processContent="processContent" :onClickOutside="onClickOutside" :doneSend="doneSend"
          :handleSave="handleSave" :handleCreate="handleCreate" :taskStatusText="taskStatusText" :saveImg="saveImg"
          :changeShowFull="changeShowFull" :localCommands="localCommands" :localTags="localTags" :tagSelectedChange="tagSelectedChange"  :tagUpdate="tagUpdate" />
      </div>
      <div v-if="planDetailShow && !rightFullFlag" v-show="!phoneFlag" id="resize" class="resize" title="收缩侧边栏"
        @mousedown="startDrag">
        <div class="el-two-column__icon-top">
          <div class="el-two-column__icon-top-bar"></div>
        </div>
        <div class="el-two-column__trigger-icon">
          <SvgIcon name="dragborder" class="process-icon" />
        </div>
        <div class="el-two-column__icon-bottom">
          <div class="el-two-column__icon-bottom-bar"></div>
        </div>
      </div>
      <div v-show="!phoneFlag" id="right-content" :style="{
        width: rightWidth,
        minWidth: minWidth+'px',
        marginLeft: planDetailShow ? '0px' : '16px',
        transition: isDragging ? 'none' : 'width 0.2s',
        userSelect: isDragging ? 'none' : 'auto',
        height: (!planDetailShow && !isHideIframe)  ? 'calc(100% - 48px)':'100%',
        maxHeight:(!planDetailShow && !isHideIframe) ? 'calc(100% - 48px)':'100%',
        top: (!planDetailShow && !isHideIframe)  ?  '48px': '0px',
      }" :class="!planDetailShow ? 'chatRight chatRightFull customMd' : 'chatRight customMd'">
            <div class="optContent newClass" v-if="schemeInfo.agent_scene_code !== 'other_assistant_scene' && !getJujiaoStatus.status">

          <div class="optHeader">
            <div class="leftTitleOpt modern-button-container">
              <el-tabs v-model="activeZl" @tab-click="changeActiveZl">
                <el-tab-pane  label="生产上下文" name="second">
                  <template slot="label">
                    <div class="tabTitle">生产上下文</div>
                  </template>
                </el-tab-pane>
                <el-tab-pane  label="生产产物" name="first">
                  <template slot="label">
                    <div class="tabTitle">生产产物</div>
                  </template>
                </el-tab-pane>
                <el-tab-pane  label="版本信息" name="version" v-if="!$route.query.isTask">
                  <template slot="label">
                    <div class="tabTitle">版本信息</div>
                  </template>
                </el-tab-pane>
                <el-tab-pane  label="任务" name="task" v-if="$route.query.isTask">
                  <template slot="label">
                    <div class="tabTitle">任务</div>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </div>
            <div class="rightTitleOpt">
             <div class="rightTitleOpt-lf" v-show="!getJujiaoStatus.status">
               <template>
                 <el-button v-if="!developFlag && runTaskStatus && !$route.query.isTask" class="button-last"
                   :disabled="hasChatingName !== '' || isOccupied || nextLoading || isReadOnly" type="info"
                   @click="showTaskNew()">查看任务进度</el-button>
                   <el-button v-if="!developFlag && !$route.query.isTask" class="button-last" type="primary"
                   :disabled="isEdit || hasChatingName !== ''  || isOccupied || nextLoading || ploading || disTree || disfangan || isReadOnly"
                   @click="showTaskDialog">任务执行</el-button>
                 <el-button v-if="!$route.query.isTask && !developFlag && capability_type=='api_service_development' && flowData[flowData.length - 1].status === 1 &&
                 flowData.filter((item) => item.status === 1).length === flowData.length" class="button-last" type="primary"
                   :disabled="isEdit || hasChatingName !== ''  || isOccupied || nextLoading || ploading || disTree || disfangan || isReadOnly"
                   @click="changeViews(1)">下一步</el-button>
                 <el-button v-if="isEmbedMode && capability_type=='api_service_development'" class="button-last" type="info">完成</el-button>
               </template>
             </div>
              <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top">
                <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowRight">
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png" />
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div v-show="activeZl == 'first'" style="height: 100%; overflow: hidden; padding-top: 10px;"
            :class="{ 'headerScroll': isEmbedTool }">
            <div class="optHeader2 center_flex" v-show="activeZl == 'first'">
              <el-tabs v-model="optHeader2Tab" @tab-click="handleOptHeader2TabClick">
                <el-tab-pane v-for="item in optHeader2TabsList" :key="item.name" :label="item.label" :name="item.name">
                  <template slot="label">
                    <div class="tabTitle">{{ item.label }}</div>
                  </template>
                </el-tab-pane>
              </el-tabs>
              <el-tooltip class="item" effect="dark" content="进入聚焦模式" placement="top" v-if="optHeader2TabsList.length > 0  && optHeader2Tab != 'api_generate_ability.代码生成' && optHeader2Tab != '任务key'">
              <div class="chuanzuozhe" @click="openJJ" >
                <SvgIcon name="focus" class="menu-icon" style="height: 24px;width: 24px;"></SvgIcon>
              </div>
            </el-tooltip>
              <div class="rightTitleOpt-tab" v-if="optHeader2TabsList.length > 0">
                <el-dropdown >
                  <div class="solid_class"><i class="el-icon-more"></i> </div>
                      <el-dropdown-menu slot="dropdown">
                        <!-- 直接渲染所有标签 -->
                        <el-dropdown-item
                          v-for="tag in optHeader2TabsList"
                          :key="tag.name"
                          :command="tag.name"
                          :class="{ 'tag-selected': optHeader2Tab === tag.name }"
                          @click.native="()=>{changeoptHeader2Tab(tag)}"
                        >
                          {{ tag.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
              </div>
            </div>
            <div :class="isEnnObjec  ? 'optScroll' : 'optScroll optEdit'" v-show="activeZl == 'first'">
              <template v-for="item in optHeader2TabsList">
                <div style="width: 100%;" v-if="optHeader2Tab === item.name && item.name === '方案详情key'"
                  :key="item.name">
                  <div v-show="!dagangFlag" id="detail-content" class="optContentBox h-full" @mouseenter="fangda"
                    @mouseup="fangda2">
                    <MyEditor id="MyEditor" ref="MyEditor" :md-content="detailContent.text" :is-edit="isEdit"  @updateContent="handleUpdateContent"></MyEditor>
                  </div>
                  <div v-show="dagangFlag && !isEdit" style="width: 100%; height: 100%;position: absolute;" @mouseenter="fangda">
                      <svg id="markmap" class="mark-map"></svg>
                  </div>
                  <div class="enn-markdown-div" v-if="optHeader2Tab === '方案详情key'">
                    <el-tooltip v-if="!dagangFlag && !isEdit && !isEmbedTool" class="item" effect="dark" content="方案优化"
                    placement="top">
                    <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                      type="info" size="mini" @click="() => { SOdialogVisible = true }">
                      <img src="@/assets/images/planGenerater/ic_fangan.png" />
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-if="!dagangFlag && !isEdit" class="item" effect="dark" content="脑图" placement="top">
                    <el-button
                      :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                      type="info" size="mini" @click="changeDagang">
                      <img src="@/assets/images/planGenerater/ic_naotu.png" />
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-if="dagangFlag && !isEdit" class="item" effect="dark" content="大纲" placement="top">
                    <el-button
                      :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                      type="info" size="mini" @click="changeDagang"><img
                        src="@/assets/images/planGenerater/ic_dagang.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip v-if="!isEdit && !isEmbedTool" class="item" effect="dark" content="编辑" placement="top">
                    <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                      type="info" size="mini" @click="() => {
                        hisDetail = detailContent.text
                        isEdit = true
                      }
                        "><img src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
                      <el-button type="info" size="mini" @click="handleDetailSave"><img
                          src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
                      <el-button type="info" size="mini" @click="handleDetailSaveClose"><img
                          src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="复制" placement="top" v-if='!isEmbedTool'
                  :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                      <el-button :disabled="isReadOnly" type="info" size="mini" @click="copyTextNew(detailContent.text)"><img
                          src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="生成过程" placement="top" v-if='!isEmbedTool'
                    :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                      <el-button :disabled="isReadOnly" type="info" size="mini" @click="showSikao()"><img
                          src="@/assets/images/planGenerater/ic_guocheng.png" /></el-button>
                  </el-tooltip>
                  </div>
                  <el-link type="primary">{{ detailContent.file_url }}</el-link>
                </div>
                  <!-- <AbilitysFile v-if="optHeader2Tab === item.name && item.name === '文件key'" :firstUrl="firstUrl"  :fileData="abilitys"></AbilitysFile> -->
                <template v-else-if="optHeader2Tab === item.name && item.name === '设备匹配key'">
                  <DeviceMatchingComp />
                </template>
                <template v-else-if="optHeader2Tab === item.name && getComponent(item.display_component)">
                  <div v-if="!dagangFlagNew && activeZl == 'first'" style="width:100%">
                    <EnnEmptyContent v-if="!item.execute_result && !isEmpty " :key="`empty-${item.name}`" />
                  <component v-else :ref="`tab-${item.name}`" :is="getComponent(item.display_component)"
                    :optHeader2Tab="optHeader2Tab"
                    :key="item.name" :item="item" :optHeader2TabsList="optHeader2TabsList" @handleSave="handleSave"
                    @handleTask="getQueryTaskContent"
                    v-bind="getComponentProps(item)" v-on="getComponentEmitHandlers(item)" v-loading="item.isLoading"
                    element-loading-text="生成中..." element-loading-spinner="el-icon-loading" :isEdit="isEdit" :flow-data="flowData" :firstUrl="firstUrl"/>
                  </div>
                <div v-if="optHeader2Tab === item.name && item.name !== '方案详情key' && !item.isLoading && !item.isLoadingNew && isEnnObjec">
                  <div class="enn-markdown-div">
                    <el-tooltip v-if="!dagangFlagNew && !isEdit && item.display_component == 'EnnMarkdown' && item.name !== '任务key'" class="item" effect="dark" content="脑图" placement="top">
                    <el-button
                      :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                      type="info" size="mini" @click="changeDagangNew">
                      <img src="@/assets/images/planGenerater/ic_naotu.png" />
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-if="dagangFlagNew && !isEdit && item.display_component == 'EnnMarkdown'" class="item" effect="dark" content="大纲" placement="top">
                    <el-button
                      :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                      type="info" size="mini" @click="changeDagangNew"><img
                        src="@/assets/images/planGenerater/ic_dagang.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip v-if="!isEdit && !isEmbedTool && item.display_component == 'EnnMarkdown' && item.name !== '任务key'" class="item" effect="dark" content="编辑" placement="top">
                    <el-button :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                      type="info" size="mini" @click="() => {
                        isEdit = true
                      }
                        "><img src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
                      <el-button type="info" size="mini" @click="()=>{newHanderSave(item.name)}"><img
                          src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
                      <el-button type="info" size="mini" @click="()=>{closeCaiNaFun()}"><img
                          src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="复制" placement="top" v-if='!isEmbedTool'
                  :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                      <el-button :disabled="isReadOnly" type="info" size="mini" @click="copyTextNewEnn()"><img
                          src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="生成过程" placement="top" v-if="!isEmbedTool&& item.name !== '任务key'"
                    :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                      <el-button type="info" size="mini" @click="showSikao()"><img
                          src="@/assets/images/planGenerater/ic_guocheng.png" /></el-button>
                  </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="JSON美化" placement="top" v-if="item.display_component == 'EnnJson'">
                    <el-button type="info" size="mini" @click="JSONEnn(item.name)"><img
                      src="@/assets/images/planGenerater/JSON.png" /></el-button>
                  </el-tooltip>
                  </div>
                </div>
                </template>
                <div v-else-if="dagangFlagNew" style="width: 100%; height: 100%;position: absolute;" @mouseenter="fangda">
                      <svg id="markmapenn" class="mark-map"></svg>
                  </div>
              </template>
            </div>
            <treeProcess class="test1" :is-visible="processVisable" :tree-process-val="optionDataProcess"
              @close="closeSikaoRizhi" />
            <planOptimize class="test2" :is-visible="youhuaVisable" :tree-status="planStatus" :sql-data="optimizeData"
              :agent-scene="schemeInfo.agent_scene" @close="closeYouhuaEdit" @updateOptimize="emitOptimize" />
          </div>
          <ProductionComDetail  :firstUrl="firstUrl" :abilitys="abilitys" :isReadOnly="isReadOnly" ref="ProductionComDetailRef" @getDataRef="getDataRef" v-show="activeZl == 'second'"></ProductionComDetail>
          <!-- <MemoryCom :isReadOnly="isReadOnly" v-show="activeZl == 'third'" ref="MemoryComRef"></MemoryCom> -->
          <Version v-show="activeZl == 'version'"></Version>
          <taskNew v-show="activeZl == 'task'" ref="taskNewRef"></taskNew>
        </div>
      <!-- 聚焦模式。单独抽出来做组件 -->
      <JujiaoCom :copyTextNew="copyTextNew" @closeCaiNaFunc="closeCaiNaFunc" :isDisabled="(systemMessages == 'process running' ||
          systemMessages == 'process stream' ||
          systemMessages == 'process_stream_message' ||
          systemMessages.indexOf('received feedback') >-1 ||
          systemMessages == 'scheme generating' ||
          systemMessages == 'process stream running' ||
          systemMessages == 'clear_history' ||
          systemMessages == 'clear history completed')"
      ref="JujiaoComRef" @closeJJ="closeJJ" @ChangedetailContent="ChangedetailContent"  @changeEdit="changeEdit"
      :firstUrl="firstUrl"
      :hisDetail="hisDetail"  v-show="getJujiaoStatus.status" :getComponent="getComponent" :getComponentProps="getComponentProps" :optHeader2TabsList="optHeader2TabsList.filter(item =>  item.name != 'api_generate_ability.代码生成' && item.name != '任务key')" :isEdit="isEdit"
      :getComponentEmitHandlers="getComponentEmitHandlers" :isEmbedTool="isEmbedTool" :detailContent="detailContent" :dagangFlag="dagangFlag" :isReadOnly="isReadOnly"
      :dagangFlagNew="dagangFlagNew" :isOccupied="isOccupied" :fangda2="fangda2" :fangda="fangda" :handleUpdateContent="handleUpdateContent" :handleSave="handleSave"
      :handleDetailSave="handleDetailSave"  :showSikao="showSikao" @handleDetailSaveCloseNew="handleDetailSaveCloseNew" :getInstructionParam="getInstructionParam"
      :changeDagang="changeDagang" :changeDagangNew="changeDagangNew" @handleSave="handleSave"  :hasChatingName="hasChatingName" :systemMessages="systemMessages"
      ></JujiaoCom>
        <treeProcess class="test1" :is-visible="processVisable" :tree-process-val="optionDataProcess"
          @close="closeSikaoRizhi" />
        <planOptimize class="test2" :is-visible="youhuaVisable" :tree-status="planStatus" :sql-data="optimizeData"
          :agent-scene="schemeInfo.agent_scene" @close="closeYouhuaEdit" @updateOptimize="emitOptimize" />
      </div>
      </div>
      <div class="right_gongju" ref="right_gongju" v-show="!getJujiaoStatus.status">
          <img  v-if="!shrinkRight" class="op_i" :style="isHideIframe ? 'top: 8px' : 'top: 56px'" @click="changeWidthRight" src="@/assets/images/planGenerater/ic_shouqi.png"  width="24" height="24"/>
          <img   v-else class="op_i" :style="isHideIframe ? 'top: 8px' : 'top: 56px'" @click="changeWidthRight" src="@/assets/images/planGenerater/ic_zhankai.png" width="24" height="24" />
          <img  v-if="!shrinkLeft" class="op_i2" :style="isHideIframe ? 'top: 8px' : 'top: 56px'" @click="changeWidthLeft" src="@/assets/images/planGenerater/ic_shouqi.png"  width="24" height="24"/>
          <img   v-else class="op_i2" :style="isHideIframe ? 'top: 8px' : 'top: 56px'" @click="changeWidthLeft" src="@/assets/images/planGenerater/ic_zhankai.png" width="24" height="24" />
          <MagicToolsNew  :tools="tools"
                :sessionId="sessionId"
                :isReadOnly="isReadOnly"
                @setActiveZl="setActiveZl"
                @tab-update="handleTabUpdate"
                :optHeader2TabsList="optHeader2TabsList"
                :endStatus="endStatus"
                :firstUrl="firstUrl"
                :abilitys="abilitys"
                @ChangeEndStatus="ChangeEndStatus"
                :userAbility="AllAbility?.user_ability || []"
                @getAbilitiesAndResultNew="getAbilitiesAndResultNew"
                @getShenCData="getShenCData" ref="MagicToolsNewRef" v-show="!shrinkRight"></MagicToolsNew>
        </div>
      </div>
    </div>


    <treeProcess :is-visible="processTreeVisable" :tree-process-val="treeDataProcess" @close="closeSikaoRizhi" />
    <codeAnalysis ref="chatScrollBox2" :is-visible="codeAnalysisProcessStatus" :tree-process-val="codeAnalysisProcess"
      :tree-process-val-first="codeAnalysisProcessFirst" :title-val="'代码分析日志'" @close="handleNext" />
    <!-- <MyModal ref="myModal" :visible.sync="myModalVisible"></MyModal> -->
    <SchemeOptimization :visible.sync="SOdialogVisible" @confirm="SOhandleConfirm" />
    <transition name="fade">
      <div v-if="(hasChatingName !== '' || isOccupied) && !isEmbedTool && !isReadOnly" class="overlay" v-loading="kickLoading"
        element-loading-spinner="el-icon-loading"></div>
    </transition>
    <div v-if="isEmbedMode && !getJujiaoStatus.status" type="info" class="EmbedModeWrap">
      <el-button @click="handleFinished" :disabled="embedFinishclicked">完成专家方案</el-button>
    </div>
    <PopModal v-model="showSplitScreenModal" :codeState="codeState" @ModalClosed="handleModalClosed"
      @clickShowFlase="handleclickShowFlase">
      <Chat ref="chat" :isReadOnly="isReadOnly" :ModalType="ModalType" :leftWidth="leftWidth" :choseClick2="choseClick2"
        :onClickOutside2="onClickOutside2" :onClickOutside3="onClickOutside3" :selectAgentRoleNew="selectAgentRoleNew" :adminJueSe="adminJueSe" :noKickFeat="noKickFeat"
        :showSelectVisable2="showSelectVisable2" :handleModalCodeGenerate="handleModalCodeGenerate" :realTime="realTime"
        :clearImage="clearImage" :uploadUrl="uploadUrl" :uploadParam="uploadParam" :stopRecording="stopRecording"
        :yuyinText="yuyinText" :handleChangeRole="handleChangeRole" :clearHistory="clearHistory" :getRoleList="getRoleList"
        :choseClick="choseClick" :choseClick3="choseClick3" :currentRoleInfo="currentRoleInfo" :agentAvatorInfo="agentAvatorInfo"
        :startWeb="startWeb" :thinkFullFlag="thinkFullFlag" :closechangeThinkWrap="closechangeThinkWrap"
        :thinkFlag="thinkFlag" :changeThinkFull="changeThinkFull" :changeThinkWrap="changeThinkWrap" :jjRole="jjRole"
        :agentAvatorInfoMap="agentAvatorInfoMap" :startPolling="startPolling" :shanchu="shanchu"
        :handleUpdateKickedLoading="handleUpdateKickedLoading" :kickLoading="kickLoading" :isEmbedTool="isEmbedTool"
        :carriageReturn="carriageReturn" :checkRole="checkRole" :chendianVisable2="chendianVisable2"
        :startRecording="startRecording" :selectAgentRole="selectAgentRole" :adminRole="adminRole"
        :instanceInfo="instanceInfo" :modelUploadSuccess="modelUploadSuccess" :beforeUpload="beforeUpload"
        :agentRoleList="agentRoleList" :handleInput="handleInput" :speakingFlag="speakingFlag" :toMessage="toMessage"
        :clearChose="clearChose" :choseData="choseData" :addChose="addChose" :closeCz="closeCz" :openCz="openCz"
        :ischuanzuoShow="ischuanzuoShow" :showSelectVisable="showSelectVisable" :showSelectVisable3="showSelectVisable3"
        :currentRoleInfoTemp="currentRoleInfoTemp" :agentError="agentError" :handleAsk="handleAsk" :qaList="qaList"
        :phoneFlag="phoneFlag" :rightFullFlag="rightFullFlag" :systemMessages="systemMessages"
        :historyChat="historyChat" :isOccupied="isOccupied" :hasChatingName="hasChatingName"
        :shibieLoading="shibieLoading" :qaBoxLoading="qaBoxLoading" :isDragging="isDragging" :showButton="false"
        :openModal="openModal" :sendMessage="sendMessage" :stopThink="stopThink" :processContent="processContent"
        :onClickOutside="onClickOutside" :doneSend="doneSend" :handleSave="handleSave" :handleCreate="handleCreate"
        :taskStatusText="taskStatusText" :saveImg="saveImg" :changeShowFull="changeShowFull" :abilitys="abilitys" :firstUrl="firstUrl"
        :localCommands="localCommands" :localTags="localTags" :tagSelectedChange="tagSelectedChange"  :tagUpdate="tagUpdate" />
    </PopModal>
    <Task
    :tools="tools"
    :optHeader2TabsList="optHeader2TabsList"
    @setVisible="setVisible"
    :visible="showStatus"
    :abilitys="abilitys" :firstUrl="firstUrl"
    ></Task>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex'
import Task from './components/Task.vue'
import taskNew from '../taskNew.vue'
import Model from './components/Model.vue'
import JujiaoCom from './components/jujiaoCom.vue'
import AbilitysFile from './abilitysFile.vue';
import AbilitysCode from './abilitysCode.vue';
import { v4 as uuidv4 } from 'uuid';
import { RunCode } from './constants.js'
// modify from wlls component
import CryptoJs from 'crypto-js'
// above component import from yeahz
import { mapGetters } from 'vuex'
import planOptimize from './fanganyouhua.vue'
import { handleThrottleLog,log } from '@/utils/ThrottledLogger.js';
import {
 queryTaskContent,
  getRoleList,
  reexecuteTask,
  accepCodeApi,
  shouldShowSchemeOptAPI,
  queryQuickReply,
  chatDisconnect,
  startSchemeOptimize,
  queryAbilityList,
  startConversationWithRole,
  startAppendHistory,
  getBaidu,
  getInstanceInfo,
  startStopThinking,
  queryGreeting,
  startClearHistory,
  getSuggestionList,
  queryAgentInfoDetail,
  UpdateScheme,
  queryAnsRole,
  startConversation,
  queryQuestionList,
  // // above api import from yeahz
  updateAbilityMapping,
  SchemeDetail,
  GetDecision,
  queryAbilityMapping,
  PlanTaskEdit,
  queryCache,
  addCache,
  removeCache,
  querySchemeDetailById,
  SchemeSaveKnow,
  updateByDeviceId,
  checkByDeviceId,
  saveSimpleSchemeGenerate,
  getTaskStatus,
  getExecuteSync,
  updateSchemeDescription,
  getknowledgeRetrievalCapabilityId,
  getKonwledgeSource,
  getAssociationGeneration,
  getRefpromptsList,
  queryRefpromptUseTags,
  get_instance_abilities_with_result,
  ScheManaioc_save_data,
  scheme_memory_create,
  notes_file_add,
  get_preset_question,
  chain_variable_session_upd,
  getRoleInfo,
  clear_this_message,
  update_ability_result
} from '@/api/planGenerateApi.js'
import { Transformer } from 'markmap-lib'
import dayjs from 'dayjs'
import { Markmap } from 'markmap-view'
import treeProcess from '../treeProcess.vue'
import historyProcess from './historyProcess.vue'
import panzoom from 'panzoom'
import MyEditor from '../mdEditor.vue'
import MyEditorPreview from '../mdEditorPreview.vue'
import codeAnalysis from '../codeAnalysis.vue'
import Recorder from 'js-audio-recorder'
import MyModal from './components/MyModal.vue'
import MagicTools from './components/MagicTools.vue'
import MagicToolsNew from './components/MagicToolsNew.vue'
import SchemeOptimization from './components/SchemeOptimization.vue'
import DeviceMatchingComp from './components/DeviceMatchingComp.vue'
import { ComponentRegistry, ComponentAliasMap } from './toolComponents/ComponentRegistry';
import EnnEmptyContent from './toolComponents/EnnEmptyContent.vue';
import { ButtonConfig } from './toolComponents/ButtonConfig.js';
import ProductionComDetail from '../productionComDetail.vue'
import MemoryCom from './components/memoryCom.vue'
import Version from './components/version.vue'
import { getList } from '@/api/labelMange.js';
import PopModal from './components/PopModal.vue';
import Chat from './components/Chat.vue';
import { mode } from 'crypto-js'
const {
  KNOWLEDGE_BASE_SEARCH,
  CREATE_QUESTIONS_RESULT,
  SCHEME_OPTIMIZE,
  DECISION_TREE,
  ALIGN_ANALYSIS,
  MODEL_PARAM_EXTRACTION,
  MODELING_INFO_STRUCTURE,
  MATH_MODEL_GENERATE,
  ALIGN_DATA_GENERATE,
  DECISION_MAKING_GENERATE,
  CODE_DEPLOY,
  CODE_TEST,
  CODE_ANALYSIS,
  API_PARAM_ANALYSIS
} = RunCode

const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
}
const userInfo = sessionStorage.getItem('USER_INFO')
  ? JSON.parse(sessionStorage.getItem('USER_INFO'))
  : {}

  let accumulatedMessages = [];
let timer;
export default {
  name: 'Planchat',

  components: {
    Model,
    AbilitysFile,
    AbilitysCode,
    JujiaoCom,
    Chat,
    taskNew,
    PopModal,
    DeviceMatchingComp,
    EnnEmptyContent,
    SchemeOptimization,
    ProductionComDetail,
    MyModal,
    MagicTools,
    MagicToolsNew,
    // above import by yeahz
    planOptimize,
    treeProcess,
    MyEditor,
    MyEditorPreview,
    historyProcess,
    codeAnalysis,
    Task,
    Version,
    MemoryCom
  },
  props: {
    noKickFeat:{
      type: Boolean,
      default: false
    },
    startPolling: {
      type: Function,
    },
    getNextTask:{
      type: Function,
    },
    queryTaskList:{
      type: Function,
    },
    queryBatchTasksResult:{
      type: Function,
    },
    startPolling:{
      type: Function,
    },
    handleUpdateKickedLoading: {
      type: Function,
    },
    kickLoading:{
      type: Boolean,
      default: false
    },
    isOccupied: {
      type: Boolean,
      default: false
    },
    hasChatingName: {
      type: String
    },
    processContent: {
      type: Object
    },
    processRunningContent: {
      type: String
    },
    schemeInfo: {
      type: Object,
      default: () => { }
    },

    replaceWriteFlag: {
      type: Boolean,
      default: true
    },

    historyChat: {
      type: Object
    },
    historyChat2: {
      type: Object
    },
    // above from yeahz
    agentSenceCode: {
      type: String,
      default: ''
    },
    planDataVal: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    planProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    planStatus: {
      type: [String, Number],
      default: -1
    },
    sessionId: {
      type: String,
      default: '1'
    },
    usingSession_id: {
      type: String,
      default: ''
    },
    displayType: {
      type: Number,
      default: 1
    },
    miniFlag: {
      type: Boolean,
      default: false
    },
    agentError: {
      type: Boolean,
      default: false
    },
    developFlag: {
      type: Boolean,
      default: false
    },
    isExpert: {
      type: Boolean,
      default: false
    },
    flowData: {
      type: Array,
      required: true
    },
    systemMessages: {
      type: String
    },
    changeRunTaskStatus: {
      type: [Number, String],
      default: ''
    },
    codeAnalysisData: {
      type: String,
      default: ''
    },
    codeAnalysisDataStatus: {
      type: String | Number,
      default: ''
    },
    codeAnalysisProcessData: {
      type: String,
      default: ''
    },
    emergentScene: {
      type: String
    },
    isReadOnly: {
   type: Boolean,
   default: false
 }
  },
  data() {
    return {
      activeTab: 'chat',
      routerFile:true,
      routerFileCode:'',
      firstUrl:'',
      abilitys:{},
      qieStatus: false,
      schimeId: this.$route.query.id,
      isFirstLoad:true,
      isLoadPage:false,
      showSelectVisable3: false,
      jjRole:'Chat',
      getRoleList:[{
        label: 'Agent'
      },{
        label: 'Chat'
      }
      ],
      capability_type: '',
      showStatus:false,
      endStatus:false,
      dataProcess: {},
      ModalType: false,
      minWidth:'300',
      shrinkRight: false, // 控制是否收缩右边
      shrinkLeft:false,// 控制是否收缩左边
      codeState: {
        Modalcodetype: '',
        currentModalCode: '',
        generateing: false,
        clickShow: false,
      },
      splitScreenCode: '',
      thinkFlag: false,
      thinkFullFlag: false,
      showSplitScreenModal: false,
      showButton: true,
      selectAgentRoleNew:{},
      adminJueSe: true,
      doneSend: true,
      chatStatus: false,
      lastChatShow:true,
      ischuanzuoShow: false,
      lastMessage:'',
      choseData:{label:'',labelChanWu:''},
      roleList: [],
      adminRole: [],
      checkRole: '',
      isdown: false,
      managerList: [],
      selectAgentRole: {},
      embedFinishclicked: false,
      roleName:'',
      localCommands: [], // 添加指令数据
      localTags: [], // 添加指令数据
      isEmpty: true,
      activeZl:'second',
      shouldShowSchemeOpt: false,
      tools: [],
      SOdialogVisible: false,
      AllAbility: null,
      optHeader2TabsList: [],//{ label: '能力方案', name: '方案详情key'}
      optHeader2Tab: '方案详情key',
      chatActiveTab: 'first',
      MyAbilityId: null,
      myModalVisible: false, // 控制弹框显示或隐藏
      planSearch: '',
      insertWriteFlag: true,
      websocket: null,
      audioContext: null,
      websocketPara: {
        URI: 'wss://vop.baidu.com/realtime_asr',
        APPKEY: 'zrhz2KGQLCxgVrIPdvcUa9c2',
        DEV_PID: 15372, // 声道，支持 1 或 2， 默认是1,
        sample: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        CHUNK_SIZE: 1024, // 每个音频帧的大小，单位为字节
        APPID: 60519323
      },
      phoneFlag: false,
      treeData: '', // 思维图、思维树内容
      agentAvatorInfo: {}, // 角色基本信息
      agentRoleList: [], // 角色列表
      agentAvatorInfoMap: {}, // 角色信息map
      currentRoleInfo: {}, // 当前角色id
      youhuaVisable: false,
      activeStep: 0,
      abilityList: [], // 方案优化可选能力列表，用来判断方案优化按钮是否可用
      dagangFlag: false, // 大纲视图
      dagangFlagNew: false, // 大纲视图
      instanceInfo: {user_ability_config:[],system_ability_config:[]},
      uploadUrl: '',
      uploadParam: {},
      toMessage: { content: '', image_key: '', image_path: '' },
      currentRoleInfoTemp: {}, // 临时
      showSelectVisable: false,
      showSelectVisable2: false,
      showSelectRoleId: '',
      globalChendianList: [],
      chendianList: [
        // {
        //     "question": "分析",
        //     "answer": "# 方案明细\n\n根据您遇到的空压机排气高温故障，以下是我为您准备的解决方案：\n\n1. **检查压缩机排气温度**\n\n   - 确保压缩机排气温度不超过厂家规定的最高限制值；\n   - 如果温度过高，可能是由于压缩机工作不正常导致的，需要进一步诊断原因。\n\n2. **检查压缩机冷却系统**\n\n   - 检查冷却系统的运行情况；\n   - 清洁冷却器，确保散热良好；\n   - 检查冷却系统的冷却剂水平及质量。\n\n3. **检查冷却风扇**\n\n   - 确保冷却风扇正常工作；\n   - 检查风扇叶片是否有损坏或堵塞。\n\n4. **检查排气系统管道**\n\n   - 检查排气系统管道是否存在堵塞或破损；\n   - 清除堵塞物，修复破损部分。\n\n请根据上述方案逐步进行排查和修复，如有需要，请随时告知进展情况，我将为您提供进一步的支持和建议。\n"
        // },
        // {
        //     "question": "空压机排气",
        //     "answer": "# 高温故障检测流程\n\n- 排气管温度检查\n  - 50 摄氏度：排气温度高告警\n  - <=50摄氏度：end\n\n- 排气温度高告警\n  - 检测环境温度\n\n- 检测环境温度\n  - 40 摄氏度：请加大站房通风，降低环境温度\n  - <=40摄氏度：检测喷油温度\n- 检测喷油温度\n  - 70 摄氏度：检测冷却器进油和出油温度\n  - <=70摄氏度：检测喷油压力\n- 检测喷油压力\n  - 2.5bar: 检测断油电磁阀阀位\n  - <= 2.5bar: 请检查供油软管是否爆裂、是否有漏油点\n\n- 检测断油电磁阀阀位\n  - 不正常：检修断油阀\n\n- 检测冷却器进油和出油温度\n\n  - 温差<=5摄氏度：检查温控阀是否损坏\n\n  - 温差>5摄氏度：\n    - 【风冷机型】检查冷却风扇电流\n    - 【水冷机型】检查冷却水进水压力\n\n- 检查冷却风扇电流\n\n  - 电流>0：请清洗冷却器\n  - 电流<=0：请检修冷却风扇\n\n- 检查冷却水进水压力\n\n  - 小于或等于1bar：检查冷却水泵\n  - 大于1bar：检查冷却水进水温度\n\n- 检查冷却水进水温度\n\n  - 大于或等于32摄氏度：请检查冷却塔风扇"
        // }
      ], // 沉淀方案列表
      planSearchFlag: false,
      chendianVisable: false, // 参考已沉淀方案弹窗标志
      chendianVisable2: false,
      guessList: [], // 参考提示词的内容列表
      showGuess: false, // 是否显示参考提示词列表标识
      taskStatusText: '',
      ansBoxLoading: false,
      qaList: [],
      qaBoxLoading: false,
      phoneStatus: 'start',
      // all above from yeahz
      // isKnowledge:false,
      knowledgeId: '',
      sourceList: [],
      relevanceList: [],
      topologyData: {
        title: '找不到拓扑关系？'
      },
      isReplay: false, // 重新执行任务
      deviceObj: {},
      bottomHeightSpecial: '100%',
      specialText: '锅炉',
      iframeSrc: '',
      optimizeData: '', // 方案优化流数据
      currentText: '',
      optionDataProcess: '',
      speakingFlag: 'start',
      yuyinText: '',
      shibieLoading: false,
      recorderEx: new Recorder(parameter),
      recorderPhoneEx: new Recorder(parameter),
      startY: '',
      startHeight: '',
      contentEditor: '',
      saveLoading: false,
      isSuperAdmin: false, // 是否超级管理员
      dialogTableVisible: false,
      gridData: [],
      tableLoading: false, // 加载状态
      detailContent: { id: '', text: '', file_url: '' },
      hisDetail: '',
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      timer: null,
      isDragging: false,
      leftWidth: 'calc( (100vw - 364px) *0.4)',
      topHeight: '50%',
      rightWidth: '',
      bottomHeight: '50%',
      totalWidth: 1000,
      totalHeight: 1000,
      isEdit: false,
      planDetailShow: true,
      planDetailTopShow: true,
      planDetailBottomShow: true,
      rightFullFlag: false,
      taskLoading: false,
      disfangan: false,
      deviceLoading: false,
      planLoading: false,
      writeFlag: true,
      writeText: '',
      treeData: '',
      treeDataProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      processTreeVisable: false, // 思维树生成过程弹窗标志
      panZoomRef: null,
      dataThDatas: [],
      dataStatus: '',
      checkNum: 0,
      isCache: false,
      isCacheDisabled: true,
      historyVisable: false,
      historyData: [],
      runTaskStatus: 0,
      env: {
        dev: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=5197&paperId=14649&sceneCode=1666686413389905975&editorMode=function-energy-topology&isFromIMP=true`,
        fat: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=396&paperId=1066&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`,
        uat: '',
        production: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=129&paperId=4201&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`
      },
      nextLoading: false,
      disTree: false,
      codeAnalysisProcessStatus: false,
      codeAnalysisProcess: '',
      codeAnalysisProcessFirst: '',
      ploading: false,
      reTree: false,
      reTreeTimes: 0,
      ability_id: '',
      schemeDescription: '',
      modelObj: {
        modelType: '',
        modelCode: ''
      },
      isMaximize: false,
      emergentSceneList: [
        '9552327c-0266-4258-8350-19fbb4fb0ea4',
        '6bce1509-aa55-4bdd-9e9e-7558fb2f542b',
        '6c54877d-2973-4c70-829c-7251444dea5f',
        '40ed9f63-2ec1-48ef-a77f-5930c437452f',
        'dd06dc53-42ab-43bb-a4a8-9902a67cd3bc',
        '3c719a8d-6b6d-4213-bce7-aed4813526d2'
      ]
    }
  },
  computed: {
   ...mapState('maco', ['taskContent']),
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter',
      globalNavigatorStatus: 'common/getMenuCollapseGetter',
      getJujiaoStatus: 'common/getJujiaoStatus',
      gettaskJujiaoStatus: 'common/gettaskJujiaoStatus',
      getJujiaoRole: 'common/getJujiaoRole',
      getList:'production/getList'
    }),
    isEnnObjec() {
      const result = this.optHeader2TabsList.find(item => item.name === this.optHeader2Tab)
      if(result && result.display_component === 'EnnObjectFiles') {
       return false
      }else {
       return true
      }
    },
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },
    taskAllComplete() {
      const successTask = []
      this.flowData.forEach((item) => {
        console.log(item.title + '-hello--' + item.status)
        if (item.status === 1) {
          successTask.push(1)
        }
      })
      console.log('任务进度', successTask)
      return this.flowData.length > 0 && successTask.length === this.flowData.length
    },
    isEmbedMode() {
      return this.$route.query.embed === "true"; // URL 参数控制
    },
    isEmbedTool(){
      // return true
      return this.$route.query.artifactOnly === "true";
    }
  },
  watch: {
    activeZl: {
      handler(val) {
        if(val == 'first' && this.qieStatus){
          this.isLoadPage = false
          this.optHeader2Tab =  this.optHeader2TabsList.length  > 0 ?  this.optHeader2TabsList[0].name : '';
        }
      },
    },
    capability_type:{
      handler(val,oldVal) {
        if(val != oldVal){
          if(this.capability_type && this.capability_type != 'api_service_development'){
            this.optHeader2TabsList = this.optHeader2TabsList.filter(item => item.name != '方案详情key' );
          }
          let intersection =this.optHeader2TabsList.filter(itemA =>
              [...this.instanceInfo.user_ability_config,...this.instanceInfo.system_ability_config].some(itemB => itemB.value === itemA.value)
          );

          for(let i = this.optHeader2TabsList.length - 1; i >= 0; i--) {
            const item = this.optHeader2TabsList[i];
            if(item.isTaskStatus) {
              intersection.unshift(item);
            }
          }
          if(this.capability_type && this.capability_type == 'api_service_development'){
          //   intersection.unshift({
          //     "label": "能力方案",
          //     "name": "方案详情key",
          //     "execute_result": ""
          // })
          }
          this.optHeader2TabsList = this.optHeader2TabsList.reduce((acc, current) => {
            const isDuplicate = acc.find(item => item.label === current.label)
            if (!isDuplicate) {
              acc.push(current)
            }
            return acc
          }, [])
          // this.optHeader2TabsList = this.optHeader2TabsList.filter(item => !item.isTaskStatus);
          intersection = intersection.filter(item =>  item.name != '文件key' &&  item.name != 'api_generate_ability.代码生成');
          this.$store.commit('production/setList',intersection)
        }
      },
    },
    systemMessages:{
      handler(val,oldVal) {
        if(val == 'process error'){
          this.doneSend = true
        }
      },
      deep:true,
      immediate:true
    },
    'historyChat.messages':{
      handler(val,oldVal) {
        if (val.length > 1){
          this.chatStatus = true
        } else {
          this.chatStatus = false
        }
        handleThrottleLog('historyChat.messages val',val);
      },
      immediate: true,
      deep:true
    },
    chendianVisable2(val) {
      val ? this.isdown = true : this.isdown = false
    },
    adminRole: {
      deep: true,
      handler(val) {
        // if(val.length > 0) {
        //   this.agentRoleList = []
        //   this.checkRole = val[0].name
        // }
        console.log('为什么编辑后，展示任务进度的按钮不见了？', this.adminRole,val)
      }
    },
    '$route.query.artifactOnly': {
      immediate: true, // 初始化时立即触发一次
      handler(newVal) {
        if (newVal === "true") {
          this.activeZl = "first"; // 切换到具体的 tab
        }else{
          this.activeZl = "second";
        }
      }
    },
    optHeader2TabsList: {
      immediate: true, // 初始化时立即触发一次
      deep:true,
      async  handler(newVal,oldVal) {
        if(newVal != oldVal){
        if(!this.isLoadPage || this.qieStatus){
          this.optHeader2Tab = newVal.length  > 0 ? newVal[0].name : '';
        }
        if (newVal.length > 0 && this.gettaskJujiaoStatus.status)  {
          this.ischuanzuoShow = true
          let data =  this.optHeader2TabsList.find((item) => item.label === this.gettaskJujiaoStatus.data.title)
          if(data?.default_agent_role){
            const res = await getRoleInfo({
              RoleId:data?.default_agent_role
            })
            const avatorInfo = {
                name: res.data?.name,
                alias: res.data?.alias,
                icon: res.data?.icon,
                id: res.data?.id,
                enable_image_text_msg: res.data?.enable_image_text_msg
            }
            this.$store.commit('common/setJujiaoRole', {
              PTData: {
                currentRoleInfo: this.getJujiaoRole.PTData.currentRoleInfo,
            selectAgentRoleNew: this.getJujiaoRole.PTData.selectAgentRoleNew,
            showSelectRoleId: this.getJujiaoRole.PTData.showSelectRoleId,
              },
              JJData:{
                currentRoleInfo: data?.default_agent_role,
                selectAgentRoleNew: avatorInfo,
                showSelectRoleId: data?.default_agent_role
              }
            })
            // this.currentRoleInfo.id = data?.default_agent_role
            // this.selectAgentRoleNew = avatorInfo
            // this.showSelectRoleId = data?.default_agent_role
          }
          this.$store.commit('common/setJujiaoStatus', {
            data: data,
            status:true
          })
          this.choseData.labelChanWu = data?.label
        }
      }
      }
    },
    selectAgentRoleNew:{
      handler(val,oldval) {
        console.log('selectAgentRoleNew',val)
        if(val.name && val != oldval){
          if(this.isFirstLoad){
          this.isFirstLoad = false
          this.$store.commit('common/setJujiaoRole', {
              PTData: {
                currentRoleInfo: this.currentRoleInfo.id,
                selectAgentRoleNew: this.selectAgentRoleNew,
                showSelectRoleId: this.showSelectRoleId,
              },
              JJData:{
                currentRoleInfo: this.currentRoleInfo.id,
                selectAgentRoleNew: this.selectAgentRoleNew,
                showSelectRoleId: this.showSelectRoleId,
              }
            })
        }
        }
      },
      immediate: true, // 初始化时立即触发一次
      deep:true,
    },
    'getJujiaoStatus': {
      async handler(newVal, oldVal) {
        if (newVal === oldVal) return;

        try {
          if (!newVal.status) {
            const ptData = this.getJujiaoRole.PTData;
            Object.assign(this, {
              'currentRoleInfo.id': ptData.currentRoleInfo || this.currentRoleInfo.id,
              'selectAgentRoleNew': ptData.selectAgentRoleNew || this.selectAgentRoleNew,
              'showSelectRoleId': ptData.showSelectRoleId || this.showSelectRoleId
            });
          } else if (newVal.data?.default_agent_role) {
            const res = await getRoleInfo({
              RoleId: newVal.data.default_agent_role
            });

            const avatorInfo = {
              name: res.data?.name,
              alias: res.data?.alias,
              icon: res.data?.icon,
              id: res.data?.id,
              enable_image_text_msg: res.data?.enable_image_text_msg
            };

            const updates = {
              'currentRoleInfo.id': newVal.data.default_agent_role,
              'selectAgentRoleNew': avatorInfo,
              'showSelectRoleId': newVal.data.default_agent_role
            };

            Object.assign(this, updates);

            const { PTData } = this.getJujiaoRole;
            this.$store.commit('common/setJujiaoRole', {
              PTData: {
                currentRoleInfo: PTData.currentRoleInfo,
                selectAgentRoleNew: PTData.selectAgentRoleNew,
                showSelectRoleId: PTData.showSelectRoleId
              },
              JJData: {
                currentRoleInfo: newVal.data.default_agent_role,
                selectAgentRoleNew: avatorInfo,
                showSelectRoleId: newVal.data.default_agent_role
              }
            });
          } else {
            const { currentRoleInfo, selectAgentRoleNew, showSelectRoleId } = this.getJujiaoRole.PTData;
            Object.assign(this, {
              'currentRoleInfo.id': currentRoleInfo,
              'selectAgentRoleNew': selectAgentRoleNew,
              'showSelectRoleId': showSelectRoleId
            });
          }
        } catch (error) {
          console.error('角色信息更新失败:', error);
        }
      },
      immediate: true,
      deep: true
    },
    runTaskStatus: {
      handler(val) {
        console.log('为什么编辑后，展示任务进度的按钮不见了？', val)
      }
    },
    'schemeInfo.agent_scene_code':{
      handler(val) {
        console.log('哥哥 val',val);
        if (val === 'digital_twin_assistant_scene'){
          this.optHeader2TabsList.splice(1, 0, {
            label: '设备匹配',
            name: '设备匹配key'
          });
        }
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成PlanChat')
          this.taskLoading = false
          this.disfangan = false
          setTimeout(() => {
            this.queryCacheHandle()
          }, 2000)
        } else if (val === 1) {
          this.taskLoading = true
          this.disfangan = true
        } else if (val === 0) {
          this.taskLoading = false
          this.disfangan = true
        } else if (val === 3) {
          // 生成失败
          this.taskLoading = false
          this.disfangan = false
        }
      },
      immediate: true
    },
    planStatus: {
      handler(val) {
        if (val === 0) {
          console.log('准备接收完方案信息')
          this.planLoading = true
          this.disTree = true
          this.reTree = true
          console.log('planStatus : ' + val + ',this.reTree: ' + this.reTree)
        } else if (val === 1) {
          this.disTree = true
          this.planLoading = false
          this.reTree = true
          console.log('planStatus : ' + val + ',this.reTree: ' + this.reTree)
        } else {
          if (val === 2 && (!this.developFlag || !this.isExpert)) {
            // 输出完方案 触发任务流程
            console.log('输出完方案触发任务流程111')
            this.$emit('handleUpdateScheme', this.detailContent.text)
            if (this.reTree) {
              this.regenerate()
              this.reTree = false
              this.reTreeTimes++
            }
          }
          console.log('方案信息接收', val)
          this.planLoading = false
          if (Number(val) === 2) {
            this.disTree = false
          }
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.treeData = val
      },
      immediate: true
    },
    planDataVal: {
      handler(val) {
        this.detailContent.text = val
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.treeDataProcess = val
      },
      immediate: true
    },
    planProcessVal: {
      handler(val) {
        this.dataProcess[this.optHeader2Tab] = val
        this.optionDataProcess = val
      },
      immediate: true
    },
    changeRunTaskStatus: {
      handler(val) {
        this.runTaskStatus = val
        console.log('runTaskStatus 0', val)
      },
      immediate: true
    },
    codeAnalysisData: {
      handler(val) {
        console.log('代码分析流', val)
        this.codeAnalysisProcess = val
      },
      immediate: true
    },
    codeAnalysisProcessData: {
      handler(val) {
        this.codeAnalysisProcessFirst = val
      },
      immediate: true
    },
    codeAnalysisDataStatus: {
      handler(val) {
        console.log('代码分析状态状变化', val)
        if (Number(val) === 1) {
          this.ploading = false
          this.codeAnalysisProcessStatus = true
        } else {
          this.codeAnalysisProcessStatus = false
        }
        if (Number(val) === 2 || Number(val) === 3) {
          this.ploading = false
          this.handleNext()
        }
      },
      immediate: true
    },
    // flowData:{
    //   hander(val){

    //   },
    //   immediate: true,
    //   deep:true
    // }
  },
  created() {
    this.getShouldShowSchemeOpt()
    this.updateCommandAndTags()
  },
  beforeDestroy() {
   this.setTreeData([])
   this.setIsRunning(false)
    this.$store.commit('common/setTaskJujiaoStatus', false)
    this.$store.commit('common/setJujiaoStatus', {
      status: false,
      data: {}
    })
    this.$root.$off('command-selected', this.handleCommandSelected); // 移除事件监听
    clearInterval()
    clearInterval(this.timer)
    this.timer = null
    // 组件卸载时重置聚焦状态
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
   this.setIsStop(false)
    this.$root.$on('command-selected', this.handleCommandSelected); // 监听事件
    const nodeMarkmap = document.getElementById('markmap')
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = ''
    }
    window.addEventListener('message', (e) => {
      // 真实物联孪生项目
      const typeList = [
        'IOTtoIMPClick',
        'virtualStandardDevice',
        'modeTypeChange',
        'IOTtoIMPSelectList'
      ]
      if (typeList.includes(e.data.type)) {
        console.log('--数字孪生透传数据--', e.data)
        this.deviceObj = e.data
        this.updateDeviceId(e.data)
      }
    })
    // above from yeahz
    await this.queryDetailFn()
    await this.getBaiduFn()
    await this.initTaskStatus()
    // await this.getInstance()
    await this.queryDecision()
    // await this.queryAbilityId(); // 获取知识检索能力

    await this.handleAbility()
    await this.queryAgentAvator()
    await this.queryPlanDetail() // 查询方案明细
    this.$emit('queryDetailTrigger')
    // await this.getChatIsUseing();
    await this.handleGetAbilityId() // 最终获取数字孪生的modelCode和modelType
    this.getQueryTaskContent()
    // below form yeahz
    console.log('this.historyChat', this.historyChat)
    await this.getQuickReply()
    // console.log('this.$parent', this.$parent.eventSource)
    // if (this.$parent.eventSource) {
    //   this.$parent.eventSource.addEventListener('stream_message', (event) => {
    //     const message = JSON.parse(event.data)
    //     console.log('stream_message收到消息：', event.data)
    //     requestIdleCallback(()=>{
    //     this.handleMessageStream(JSON.parse(event.data))
    //     if (message.action === 'end') {
    //       if (this.phoneFlag) {
    //         this.startWeb()
    //       }
    //     }
    //   })
    //   })

    //   this.$parent.eventSource.addEventListener('sure_template_stream_message', (event) => {
    //     const message = JSON.parse(event.data)
    //     handleThrottleLog('sure_template_stream_message收到消息：', message)
    //     this.handleSureTemplateStream(message)
    //   })
    // }
    // this.listenSteam()
    console.log('window.message child 0', window.message)
    setTimeout(() => {
      if (window.message) {
        console.log('window.message plan chat setTimeout', window.message)
        this.toMessage = window.message
        // this.currentText = window.customeDescription;
        this.sendMessage()
        window.message = null
      }
    }, 1000)
    // this.throttledScrollToBottom()
    this.$nextTick(() => {
      this.throttledScrollToBottom = _.throttle((data) => {
        this.scrollToBottom();
      }, 500, { leading: true, trailing: false });
    })
    //     this.throttledScrollToBottom = _.throttle((data) => {
    //     this.scrollToBottom();
    // }, 500, { leading: true, trailing: false });
    if(this.$route.params.startrun){
      this.$emit('handleReStart',true)
    }
    this.$nextTick(() => {
      this.isLoadPage = true
    })
  },
  methods: {
    JSONEnn(name){
      let data = `tab-${name}`
      this.$refs[data][0].JSONEnn()
    },
    ChangeactiveTab(tab){
      if(!this.doneSend){
        return false
      }
      this.activeTab = tab
    },
   ...mapMutations('maco', ['setIsRunning', 'setIsStop', 'setTaskContent','setTreeData']),
  async getQueryTaskContent() {
    try {
     const res = await queryTaskContent({
      scheme_id:this.$route.query.id
     })
     if(res.data?.code === 200) {
      this.setTaskContent(res.data?.result?.task_content || '')
      if(this.taskContent) {
        const task = {
          "label": "任务",
          "name": "任务key",
          display_component: 'EnnMarkdown',
          execute_result: this.taskContent
        }
      if(!this.optHeader2TabsList.some(i => i.name === '任务key')) {
       this.optHeader2TabsList?.splice(1,0, task)

      }
     }else {
      if (this.optHeader2TabsList.length > 0) {
        const taskIndex = this.optHeader2TabsList.findIndex(item => item.name === '任务key');
        if (taskIndex !== -1) {
          this.optHeader2TabsList.splice(taskIndex, 1);
        }
      }
     }
     }
    } catch (error) {

    }
   },
    goback() {
      this.routerFile = true
    },
    editFile(data,objectKey){
      console.log(data,'data')
      this.routerFileCode = data
      this.objectKey = objectKey
      this.routerFile = false
      this.$forceUpdate()
    },
    async  shanchu(index){
      let negativeIndex = -(this.historyChat.messages.length - index)
      console.log(negativeIndex,111)
      const res = await clear_this_message({
        "scheme_id": this.$route.query.id,
        "message_index": negativeIndex
      })
      if(res.data.status == 'success'){
        this.$message.success('删除成功')
        this.historyChat.messages.splice(index,1)
      }else{
        this.$message.error('删除失败')
      }
    },
    changeEdit(){
      this.isEdit = !this.isEdit
    },
    ChangedetailContent(data){
      this.hisDetail = data
      this.isEdit = true
    },
    closeJJ(){
      this.ischuanzuoShow = false
    },
    async  openJJ(){
      this.ischuanzuoShow = true
      let data =  this.optHeader2TabsList.find((item) => item.name === this.optHeader2Tab)
      if(data?.default_agent_role){
        const res = await getRoleInfo({
          RoleId:data?.default_agent_role
        })
        const avatorInfo = {
            name: res.data?.name,
            alias: res.data?.alias,
            icon: res.data?.icon,
            id: res.data?.id,
            enable_image_text_msg: res.data?.enable_image_text_msg
        }
        this.$store.commit('common/setJujiaoRole', {
          PTData: {
            currentRoleInfo: this.currentRoleInfo.id,
            selectAgentRoleNew: {...this.selectAgentRoleNew},
            showSelectRoleId: this.showSelectRoleId
          },
          JJData:{
            currentRoleInfo: data?.default_agent_role,
            selectAgentRoleNew: avatorInfo,
            showSelectRoleId: data?.default_agent_role
          }
        })
      }else {
        this.$store.commit('common/setJujiaoRole', {
              PTData: {
                currentRoleInfo: this.currentRoleInfo,
                selectAgentRoleNew: this.selectAgentRoleNew,
                showSelectRoleId: this.showSelectRoleId,
              },
              JJData:{
                currentRoleInfo: '',
                selectAgentRoleNew: {},
                showSelectRoleId: ''
              }
            })
      }
      this.$store.commit('common/setJujiaoStatus', {
        data: data,
        status:true
      })
      this.choseData.labelChanWu = data.label
    },
    setVisible(val){
      this.showStatus = val
    },
    showTaskDialog(){
      this.showStatus = true
    },
    getInstructionParam(){
      return  this.$refs.MagicToolsNewRef.getInstructionParam()
    },
    newHanderSave(name){
      this.isEdit = false;
      let data = `tab-${name}`
      this.$refs[data][0].handleSave()
    },
    changeoptHeader2Tab(tag){
      this.optHeader2Tab = tag.name
    },
    extractAfterThinkTag(text) {
      // 查找 </think> 标签的索引位置
      const thinkEndIndex = text.indexOf('</think>');
      console.log('text  0', thinkEndIndex);
      // 如果找到了 </think> 标签，返回其后的文本
      if (thinkEndIndex !== -1) {
        console.log('text  1', text.slice(thinkEndIndex + '</think>'.length).trim());
        return text.slice(thinkEndIndex + '</think>'.length).trim();
      }

      // 如果没有找到 </think> 标签，返回空字符串或原始文本
      console.log('text  2',text);
      return text;
    },
    // throttledScrollToBottom: _.throttle(function (data) {
    //   this.scrollToBottom();
    // }.bind(this), 500, { leading: true, trailing: false }),
    handleModalClosed(){
      console.log('handleModalClosed');
      this.ModalType = false
      this.codeState.clickShow = false
    },
    handleclickShowFlase(){
      this.codeState.clickShow = false
    },
    changeWidthRight(){
      this.$refs.right_gongju.style = this.shrinkRight ? 'width: 364px':  'width: 0px';
      this.shrinkRight = !this.shrinkRight
    },
    changeWidthLeft(){
      // this.leftWidth = '0px'
      this.shrinkLeft = !this.shrinkLeft
    },
    realTime() {
      if(this.isReadOnly) return
      this.phoneFlag = true
      this.leftWidth = '100%'
      this.startWeb()
    },
    closechangeThinkWrap() {
      this.thinkFlag = !this.thinkFlag
      this.thinkFullFlag = false
      if (this.thinkFlag) {
        this.$refs.chat.$refs.chatBox.style.height = 'calc(100vh - 530px)'
      } else {
        this.$refs.chat.$refs.chatBox.style.height = 'calc(100vh - 300px)'
      }
    },
    changeThinkWrap(data) {
     if(this.isReadOnly) return
      console.log('data 00', data, this.historyChat.messages)

      this.thinkFlag = !this.thinkFlag
      this.thinkFullFlag = false
      if (this.thinkFlag) {
        this.$refs.chat.$refs.chatBox.style.height = 'calc(100vh - 530px)'
      } else {
        this.$refs.chat.$refs.chatBox.style.height = 'calc(100vh - 300px)'
      }
      if (data) {
        this.processContent.text = data
      } else {
        this.processContent.text = this.processContent.text || ''
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag
    },
    handleModalCodeGenerate(val){
      if (val && val.generateing === true){
        this.codeState.Modalcodetype = val?.codetype
        this.codeState.currentModalCode = val?.content
        this.codeState.generateing = val?.generateing
      }
      if(val && val.generateing === false){
        this.codeState.generateing = val?.generateing
      }
    },
    openModal(val) {
      this.showSplitScreenModal = true;
      this.ModalType= true// 显示 Modal
      console.log('ModalType openModal', this.ModalType);
      if(val){
        this.codeState.Modalcodetype = val?.codetype
        this.codeState.currentModalCode = val?.codeContent
        this.codeState.clickShow = val?.clickShow || false
      }
    },
    closeModal() {
      this.showSplitScreenModal = false; // 隐藏 Modal
    },
    openSplitScreen(codeContent) {
      this.showSplitScreenModal = true; // 显示弹窗
    },
    getShenCData(){
      this.$refs.ProductionComDetailRef.getData()
    },
    async getDataRef(data){
      await this.$refs.MagicToolsNewRef.getData(false,data)
      // this.$refs.MagicToolsNewRef.setRef(data)
    },
    transformItem(item) {
      const transformed = {
        ...item,
        value: item.name,
        label: item.label
      };
      return transformed;
    },
    onClickOutside(){
      this.showSelectVisable = false
    },
    onClickOutside2(){
      this.showSelectVisable2 = false
    },
    onClickOutside3(){
      this.showSelectVisable3 = false
    },
    copy(data){
      const vm = this;
      vm.$copyText(data).then(
        function (e) {
          vm.$message({
            showClose: true,
            message: '复制成功',
            type: 'success'
          });
        },
        function (e) {
          vm.$message({
            showClose: true,
            message: '复制失败，请手动复制',
            type: 'error'
          });
        }
      );
    },
    openCz(){
      if(this.isReadOnly) return
      if (!this.doneSend){
        return false
      }
      // this.choseData.label = '方案详情'
      this.showSelectVisable2 = true
    },
    closeCz(){
      if (!this.doneSend){
        return false
      }
      this.choseData.labelChanWu = ''
      this.choseData.label = ''
      this.showSelectRoleId = ''
      this.selectAgentRoleNew = _.cloneDeep(this.selectAgentRole)
      this.checkRole = this.selectAgentRole.name
      this.ischuanzuoShow = false
    },
    clearChose(){
      if (!this.doneSend){
        return false
      }
      this.choseData.label = ''
    },
    addChose(){
     if(this.isReadOnly) return
      if (!this.doneSend){
        return false
      }
      this.showSelectVisable = true
    },
    choseClick(val){
      this.showSelectVisable = false
      this.choseData.label = val.label
    },
    choseClick3(val){
      this.showSelectVisable3 = false
      this.jjRole = val.label
      this.$forceUpdate()
    },
    getagent_role_id(){
      if(this.ischuanzuoShow){
        return this.showSelectRoleId
      }else{
        return this.currentRoleInfo?.id
      }
    },
    choseClick2(val){
      console.log(val,'cesss')
      this.showSelectVisable2 = false
      this.showSelectRoleId = val?.default_agent_role || ''
      // this.selectAgentRole =
      const adminAgen = this.agentRoleList.find(item => item.id === val?.default_agent_role)
      if(adminAgen){
        const avatorInfo = {
              name: adminAgen?.name,
              alias: adminAgen?.alias,
              icon: adminAgen?.icon,
              id: adminAgen?.id,
              enable_image_text_msg: adminAgen?.enable_image_text_msg
            }
          this.selectAgentRoleNew = avatorInfo
          this.checkRole = this.selectAgentRoleNew.name
      }
      this.ischuanzuoShow = true
      this.choseData.labelChanWu = val.label
      this.choseData.label = val.label
    },
    listenSteam(){
      if (this.$parent.eventSource) {
        this.setIsRunning(false)
        this.setIsStop(false)
        this.$parent.eventSource.addEventListener('stream_message', (event) => {
          const message = JSON.parse(event.data)
          handleThrottleLog('stream_message收到消息：', event.data)
          const allowedIds = [
            '841428e0-37c4-4030-a148-8bc007068cbe',
            '690c3d26-c347-4978-a6a1-abd2fbe4f65a',
            'fdd23527-4a75-48b1-a8d8-69c315ada6af'
          ];
          if(this.selectAgentRoleNew && allowedIds.includes(this.selectAgentRoleNew.id)) {
           if (this.activeZl === 'first' &&
           (this.optHeader2Tab === '任务key' || this.optHeader2Tab === '文件key') &&
           (message.action === 'running')) {
               console.log('running-----------1111',message.action,this.optHeader2Tab,this.activeZl)
            this.setIsRunning(true)
           }
           if (message.action === 'end' ||
              message.data === 'process completed' ||
              message.data === 'process error' ||
              message.data === 'scheme generating error') {
            this.setIsRunning(false)
            this.setIsStop(true)
           }

          }
          requestIdleCallback(() => {
            this.handleMessageStream(JSON.parse(event.data))
            if (message.action === 'end') {
              if (this.phoneFlag) {
                this.startWeb()
              }
            }
          })
        })

        this.$parent.eventSource.addEventListener('sure_template_stream_message', (event) => {
          const message = JSON.parse(event.data)
          handleThrottleLog('sure_template_stream_message收到消息：', message)
          this.handleSureTemplateStream(message)
        })
      }
    },
    async searchManagerData() {
      const res = await getRoleList({
       keyword: '',
       type: 'manager'
         });
      if (res.status === 200) {
        this.managerList = res.data?.map((item) => ({ id: item.id, icon: item.icon, name: item.name, alias: item.alias, enable_image_text_msg: item.enable_image_text_msg}));
        return res.data || []; // 直接返回 res.data
      }
    },
    async handleFinished() {

      const data_scheme_tab = this.optHeader2TabsList.find((item) => item.label === '数据方案')
      if (!data_scheme_tab?.execute_result) {
        this.$message.warning('数据方案未生成')
        return
      }
      log('data_scheme_tab', data_scheme_tab)
      log('window.location.href', window.location.href)
      log('this.$route.query.businessKey', this.$route.query.businessKey)
      try {
        this.embedFinishclicked = true
        const res = await ScheManaioc_save_data({
          scheme_id: this.$route.query.id,
          // scheme_detail: this.detailContent.text,
          data_scheme_detail: data_scheme_tab?.execute_result,
          businessKey: this.$route.query.businessKey || '',
          scheme_url: window.location.href
        })
        log('？？？', res)
        if (res.data.code === 200) {
          this.$message.success('数据方案已提交');
        } else {
          this.$message.error(res.data.msg)
        }
      } catch (error) {
        this.$message.error(error)
      } finally {
        this.embedFinishclicked = false
      }
    },
    async handleCreate(val) {
      const now = new Date();
      const year = now.getFullYear();           // 年份 (2025)
      const month = String(now.getMonth() + 1).padStart(2, '0');  // 月份 (06)，注意月份从0开始
      const day = String(now.getDate()).padStart(2, '0');         // 日期 (16)
      const hours = String(now.getHours()).padStart(2, '0');      // 小时 (14)
      const minutes = String(now.getMinutes()).padStart(2, '0');  // 分钟 (16)
      const seconds = String(now.getSeconds()).padStart(2, '0');  // 秒 (00)
      const milliseconds = String(now.getMilliseconds()).padStart(3, '0'); // 毫秒 (501)
      let date = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
      const res = await  notes_file_add({
        "scheme_id": this.$route.query.id,
        "file_content": val,
        "obs_suffix_file_key": `${date}.md`
      })
      if (res.status == 200) {
        this.activeZl = 'second'
        this.$refs.ProductionComDetailRef.optHeaderTab = '6'
        this.$refs.ProductionComDetailRef.$refs.NoteRef.getData()
        this.$message.success('便签成功')
      } else {
        this.$message.error('便签失败')
      }
    },
    async tagSelectedChange(tagId,keyword){
      this.fetchCommands(tagId,keyword)
    },
    async tagUpdate(tagId){
      await this.updateCommandAndTags(tagId)
    },
    async fetchCommands(tagId,keyword) {
      try {
        const request={
          page: 1,
          page_size: 1000
        }
        if(tagId){
          request["tag_ids"] =[tagId]
          if (tagId == '-1') {
            request.user_id = userInfo.userId
          }
        }
        if(keyword){
          request["keyword"] =keyword
        }
        const response = await getRefpromptsList(request);
        if (response.status === 200) {
          const commandList = response.data.data ? response.data.data : [];
          this.localCommands = commandList
          .filter(command => !command.tags.some(tag => tag.id === '-1' && command.user_id !==userInfo.userId))
          .map((command, index) => ({
            id: command.id,
            title: command.title,
            content: command.content
          }));
        } else {
          console.error(response.data?.msg || 'getRefpromptsList 接口异常!');
        }
      } catch (error) {
        console.error('fetchCommands error:', error);
      }
    },
    async updateCommandAndTags(tagId){
      await this.fetchCommands(tagId)
      await this.fetchTags()
    },
    async fetchTags() {
      try {
        const response = await queryRefpromptUseTags({ scheme_id: this.$route.query.id });
        if (response.status === 200) {
          const tagList = response.data ? response.data : [];
          const defaultIndex = tagList.findIndex((tag) => tag.id === -1)
          if (defaultIndex !== -1) {
            const defaultTag = tagList.splice(defaultIndex, 1)[0]
            tagList.unshift(defaultTag)
          }

          // Add "All" tag at start
          tagList.unshift({ id: 0, name: '全部' })

          // Sort tags
          const sortedTags = tagList.sort((a, b) => {
            if (a.id === 0) return -1
            if (b.id === 0) return 1
            if (a.id === '-1') return -1
            if (b.id === '-1') return 1
            return 0
          })
          tagList[0]= {id:0,name:'全部'}
          this.localTags = sortedTags;
        } else {
          console.error( 'queryRefpromptUseTags 接口异常!');
        }
      } catch (error) {
        console.error('queryRefpromptUseTags error:', error);
      }
    },
    async handleDialogCommand(command){
      if (command.action === 'apply_code') {
        try {
          const res = await accepCodeApi({
            session_id: this.sessionId,
            scheme_id: Number(this.$route.query.id),
            message: command.data
          })
          console.log('handleDialogCommand',res);
          console.log('bro?', res.data.code);
          if (res.data.code === 200){
            console.log('nextTask1111');
            const nextTask = this.getNextTask('decision_making_generate')
            console.log('nextTask2222');
            console.log('nextTask1111', nextTask);
            if (nextTask) {
              await reexecuteTask({ scheme_id: this.$route.query.id, task_name: nextTask.runCode })
            }
            // queryTaskList 和 queryBatchTasksResult 方法执行有先后顺序，queryTaskList获取状态，queryBatchTasksResult获取内容，不能变，变会出错
            await this.queryTaskList()
            await this.queryBatchTasksResult()
            await this.getAbilitiesAndResult()
          }
        } catch (error) {
          this.$message.error('接口错误')
        }
      }
    },
    setActiveZl(){
        this.activeZl= 'first'
    },
    handleCommandSelected(commandContent) {
      this.toMessage.content = commandContent; // 将指令内容填充到输入框中
    },
    changeActiveZl(tab, event){
      if(tab.name == 'first'){
        this.qieStatus = true
      }else if(tab.name == 'task'){
        this.$refs.taskNewRef.queryTableData()
      }else if(tab.name == 'second'){
        this.$refs.ProductionComDetailRef.getData()
      }
      else {
        this.qieStatus = false
        this.setIsRunning(false)
      }
      this.activeZl = tab.name

    },
    handleGussText(text) {
      console.log('发送参考提示词消息', text)
      this.currentRoleInfo = this.agentAvatorInfo
      this.qaList = []
      startConversation({
        message: text,
        agent_role_id: this.agentAvatorInfo.id,
        session_id: this.sessionId
      })
      this.showGuess = !this.showGuess
    },
    toggleGuess() {
      this.showGuess = !this.showGuess
      const target = document.getElementById('myInputText')
      // console.log('输入框高度', target.getBoundingClientRect().height);
      this.inputHeight = target.getBoundingClientRect().height
    },
    saveImg() {
      const tath = this
      document.getElementById('saveImgPlan').addEventListener('paste', async function (event) {
        event.preventDefault();

        // 检查是否启用了图像和文本消息功能
        const enableImageTextMsg = tath.instanceInfo?.enable_image_text_msg

        // 获取剪切板数据
        const clipboardData = event.clipboardData || window.clipboardData;

        if (!clipboardData || !clipboardData.items) {
          console.warn('No clipboard data available.');
          return;
        }

        let hasImage = false;
        let textContent = '';
        const promises = [];

        console.log('Clipboard items:', clipboardData.items);

        for (let i = 0; i < clipboardData.items.length; i++) {
          const item = clipboardData.items[i];

          if (item && item.type) {
            console.log('Item type:', item.type, 'Item kind:', item.kind);

            if (item.type.startsWith("image/") && tath.instanceInfo?.enable_image_text_msg) {
              // 处理图像
              hasImage = true;
              const blob = item.getAsFile();

              if (blob) {
                // this.toMessage.image_name = blob.name;
                await tath.newUpdown(blob, blob.type, blob.size, blob.name);
              }
            } else if (item.kind === 'string' && item.type === 'text/plain') {
              // 处理文本
              console.log('Found string item:', item);
              promises.push(new Promise((resolve) => {
                item.getAsString(function (str) {
                  console.log('getAsString callback triggered with:', str);
                  resolve(str || ''); // 如果 str 为空，返回空字符串
                });
              }));
            }
          }
        }

        // 等待所有文本 Promise 完成
        try {
          const allTexts = await Promise.all(promises);
          textContent = allTexts.join('');
          console.log('All texts:', allTexts);
        } catch (error) {
          console.error('Error processing clipboard text:', error);
        }

        // 如果有文本内容，插入到文档中
        if (textContent) {
          console.log('Inserting text:', textContent);
          document.execCommand('insertText', false, textContent);
        }

        // 调试信息
        console.log('Images:', hasImage ? 'Yes' : 'No');
        console.log('Text Content:', textContent);

        // 如果未启用图像和文本消息功能，仅处理文本
        if (!enableImageTextMsg && textContent) {
          console.warn('Image and text message feature is not enabled. Only text will be processed.');
        }
      });
    },
    async newUpdown(files) {
      try {
        console.log('blob----------',this.toMessage,files)
        this.toMessage.image_name = files.name
        const formData = new FormData();
        const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
          fileType: 'png'
        })
        if (res.data.status === 200) {
          formData.append('key', res.data.data.key)
          formData.append('accessKeyId', res.data.data.accessKeyId)
          formData.append('signature', res.data.data.signature)
          formData.append('policy', res.data.data.policy)
          formData.append('file', files)
        }
        const res1 = await this.$axios.post(res.data.data.obsUrl, formData)
        const fileName = this.$fileUtil.getFileName(files.name)
        const fileSize = files.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(files.name)
        const fileKey = res.data.data.key
        this.toMessage.image_key = res.data.data.key
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        await this.$axios
          .post('/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs',

          })
          .then((res1) => {
            if (res1.data.status === 200) {
              console.log(res1.data.data, '文件id')
              this.toMessage.image_path = res1.data.data.path
            }
          })
        // await this.save()
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    getQuickReply() {
      queryQuickReply({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.guessList = res.data.result ? JSON.parse(res.data.result) : []
        } else {
          console.error(res.data?.msg || 'queryQuickReply 接口异常!');
        }
      })
    },
    async updateOptHeader2TabsList(data){
      console.log("updateOptHeader2TabsList data",data);
      const newItems = Object.entries(data).flatMap(([key, source]) =>
        this.transformData(source, key)
      );
      const existingNames = new Set(this.optHeader2TabsList.map(item => item.name)); // 收集现有的 name
      newItems.forEach(item => {
        // if (!existingNames.has(item.name)) {
          this.optHeader2TabsList.push(item);
          existingNames.add(item.name);
        // }
      });
      const api_generate_ability_code = this.optHeader2TabsList?.find(item => item.name === 'api_generate_ability.代码生成')
      this.optHeader2TabsList.forEach(item => {
        const matchingItem = this.flowData.find(flowItem => flowItem.runCode === item.ability_type);
        this.dataProcess[item.name] = item.sub_content || '';
        if (matchingItem) {
          console.log('matchingItem optHeader2TabsList',matchingItem.runCode);
          item.execute_result = matchingItem.summarize;
          // console.log('3333333333--33-3-3-3-334444', )
          // this.isResult = false
        }
        if (!item.hasOwnProperty('execute_result')){
          // console.log('3333333333--33-3-3-3-33', )
          item.execute_result = '';
          // this.isResult = true
        }
      })
      //仅保留有内容的tab
      this.optHeader2TabsList = this.optHeader2TabsList.filter(item => !!item.execute_result || (item.name === '方案详情key' || item.name === '设备匹配key'));
      if(this.capability_type && this.capability_type != 'api_service_development'){
        this.optHeader2TabsList = this.optHeader2TabsList.filter(item => item.name != '方案详情key' );
      }
      let intersection =this.optHeader2TabsList.filter(itemA =>
          [...this.instanceInfo.user_ability_config,...this.instanceInfo.system_ability_config].some(itemB => itemB.value === itemA.value)
      );

      for(let i = this.optHeader2TabsList.length - 1; i >= 0; i--) {
        const item = this.optHeader2TabsList[i];
        if(item.isTaskStatus) {
          intersection.unshift(item);
        }
      }
      if(this.capability_type && this.capability_type == 'api_service_development'){
      //   intersection.unshift({
      //     "label": "能力方案",
      //     "name": "方案详情key",
      //     "execute_result": ""
      // })
      }
      this.optHeader2TabsList = this.optHeader2TabsList.reduce((acc, current) => {
        const isDuplicate = acc.find(item => item.label === current.label)
        if (!isDuplicate) {
          acc.push(current)
        }
        return acc
      }, [])
      if(!this.optHeader2TabsList.some(i => i.name === '文件key')) {
       this.optHeader2TabsList.unshift({
           "label": "文件",
           "name": "文件key",
           execute_result:'1',
           display_component: 'EnnObjectFiles',
           obs_file_key: this.firstUrl || ''
       })

      }

      this.optHeader2TabsList = this.optHeader2TabsList.filter(i => i.name !== 'api_generate_ability.代码生成')
      // this.optHeader2TabsList = this.optHeader2TabsList.filter(item => !item.isTaskStatus);
      intersection = intersection.filter(item =>  item.name != '文件key' &&  item.name != 'api_generate_ability.代码生成');
     //  if(this.taskContent) {
     // const task = {
     //      "label": "任务",
     //      "name": "任务key",
     //      display_component: 'EnnMarkdown',
     //      execute_result: this.taskContent
     //  }
     //  if(!this.optHeader2TabsList.some(i => i.name === '任务key')) {
     //   this.optHeader2TabsList?.splice(1,0, task)

     //  }
     // }
      this.$store.commit('production/setList',intersection)
      console.log("this.optHeader2TabsList", this.optHeader2TabsList,this.instanceInfo,api_generate_ability_code);
    },
    addTabToOptHeader2TabsList(TabName){
      const newTab = Object.entries(this.AllAbility).flatMap(([key, source]) =>
        this.returnMyaddItemFromData(source, key, TabName)
      );
      console.log("newTab",newTab);

      const existingNames = new Set(this.optHeader2TabsList.map(item => item.name)); // 收集现有的 name
      console.log('existingNames',existingNames)
      newTab.forEach(item => {
        if (!existingNames.has(item.name)) {
          item.isLoading = true
          item.isLoadingNew = true
          this.optHeader2TabsList.push(item);
          let newArray = [...this.getList];
            newArray.push(item);
          this.$store.commit('production/setList',newArray)
          existingNames.add(item.name);
        }
      });
    },
    async getShouldShowSchemeOpt(){
      try {
        const res = await shouldShowSchemeOptAPI({ scheme_id: this.$route.query.id })
        this.shouldShowSchemeOpt = this.isTruthy(res)
        console.log('getShouldShowSchemeOpt', res, this.isTruthy(res));
      } catch (error) {
        console.error(error);
      }
    },
    isTruthy(obj) {
      return obj && Object.keys(obj).length > 0;
    },
    transformData2(source, key) {
      return source.flatMap(item => {
        // 动态判断是否有嵌套结构（如 config）
        if (Array.isArray(item.config)) {
          return item.config
            .map(configItem => ({
              Toolname: configItem.name, // 显示名称
              name: `${key}.${configItem.name}`, // 拼接 key 和 name 作为唯一标识
              description: configItem?.description,
              instruction_code: 'sure_template',
              goal: `${key}.${configItem.name}`,
              agent_template_id: configItem.value,
              uuid: uuidv4(),
              // default_chat_message: configItem?.default_chat_message,
            }));
        } else {
          // 如果是非嵌套结构，直接处理当前对象
          return {
            Toolname: item.name, // 显示名称
            name: `${key}.${item.name}`, // 拼接 key 和 name 作为唯一标识
            description: item.description,
            instruction_code: 'sure_template',
            goal: `${key}.${item.name}`,
            agent_template_id: item.value,
            uuid: uuidv4(),
            // default_chat_message:'tempTestmessage'
            default_chat_message: item?.default_chat_message,
          }
        }
      });
    },
    SOhandleConfirm(val){
      try {
        saveSimpleSchemeGenerate({
          session_id: this.sessionId,
          messages: val.content,
        })
        this.activeZl = 'first'
      } catch (error) {
        console.error(error);
      }
    },
    getIconPath(iconName) {
      return require(`@/assets/images/planGenerater/${iconName}`);
    },
    getButtonsForTab(tabName) {
      const tab = this.optHeader2TabsList.find((t) => t.name === tabName);
      if (tab) {
        return ButtonConfig[tab.display_component] || [];
      }
      return [];
    },
    handleButtonClick(action) {
      const currentTab = this.optHeader2Tab; // 当前 Tab 名称
      const currentComponent = this.$refs[`tab-${currentTab}`][0]; // 获取动态组件实例
      console.log("currentTab", currentTab);
      console.log("currentComponent", currentComponent);
      if (!currentComponent) return; // 无实例时直接返回

      if (action === 'toggleEdit') {
        console.log("currentComponent.setEditMode", currentComponent, currentComponent.setEditMode);

        // 调用动态组件的编辑模式方法
        if (currentComponent.setEditMode) {
          console.log("哈喽1");
          currentComponent.setEditMode(true); // 切换编辑模式
        } else {
          console.log("哈喽2 toggleEdit");
        }
      }

      if (action === 'save') {
        if (currentComponent.saveChanges) {
          currentComponent.saveChanges(); // 调用保存方法
        } else {
          // 自定义保存逻辑
          console.log('保存逻辑需要组件实现');
        }
      }
    },
    updateComponentEditMode(tabName, isEdit) {
      const componentRef = this.$refs[tabName];
      if (componentRef && componentRef.setEditMode) {
        componentRef.setEditMode(isEdit);
      }
    },
    handleTabUpdate(val){
      this.activeZl = 'first'
      this.roleName= val
      this.isEmpty = true
      console.log(val,'点击了吗------------')
      this.addTabToOptHeader2TabsList(val)
      this.optHeader2TabsList.forEach(it =>{
      if(it.name == val){
        this.optHeader2Tab = val
        it.isLoading = true
        it.isLoadingNew = true
        this.isLoadPage = true
      }
    })
    },
    ChangeEndStatus(){
      this.endStatus = true
    },
  handleSureTemplateStream(PrasedMessage) {
    // 每次调用时累积消息
    accumulatedMessages.push(PrasedMessage);

    // 如果已经有定时器，则不重新设置
    if (!timer) {
        timer = setTimeout(() => {
            this.processAccumulatedMessages();
            timer = null; // 处理完后重置定时器
        }, 1000); // 设置定时器的时间间隔，例如1秒
    }

    // 当累积的消息达到6条时立即处理
    if (accumulatedMessages.length >= 6) {
        clearTimeout(timer); // 清除定时器
        timer = null;
        this.processAccumulatedMessages();
    }
    if(PrasedMessage.role && PrasedMessage.role === `sure_template_process_generate.${this.optHeader2Tab}`) {
    console.log('PrasedMessage', PrasedMessage.data + PrasedMessage.data)
        if(PrasedMessage.action === 'start') {
            this.optionDataProcess = ''
        }
        if (PrasedMessage.action === 'running') {
          this.optionDataProcess += PrasedMessage.data
        }
        if(!this.dataProcess[this.optHeader2Tab] && PrasedMessage.action === 'end') {
          this.dataProcess[this.optHeader2Tab] = this.optionDataProcess
        }
    }
},

 processAccumulatedMessages() {
    if (accumulatedMessages.length === 0) return; // 如果没有消息需要处理，则直接返回

    accumulatedMessages.forEach((message) => {
        handleThrottleLog('handleSureTemplateStream', message.role);
        const [RolekeyPrefix, ...rest] = message.role.split('.');
        const RoleJoinName = rest.join('.'); // 将剩余部分重新组合成字符串
        this.optHeader2TabsList.forEach(item => {
            if (RolekeyPrefix === 'sure_template_process_generate') {
                if ((typeof item.name === 'string' && item.name.trim() !== '' &&
                    typeof RoleJoinName === 'string' && RoleJoinName.trim() !== '' &&
                    item.name === RoleJoinName)) {
                    item.isLoading = true;
                    if (item.display_component == "EnnMarkdown") {
                        item.isLoading = false;
                    }
                    log(item.name, RoleJoinName, 'isloading before', item.isLoading);
                    handleThrottleLog(item.name, RoleJoinName, 'isloading before', item.isLoading);
                }
            } else if (item.name === message.role) {
                if (message.action === 'start') {
                    handleThrottleLog(item.name, 'start');
                    item.isLoading = true;
                    item.isLoadingNew = true;
                    item.execute_result = message.data;
                } else if (message.action === 'running') {
                    item.execute_result += message.data;
                    if (item.display_component == "EnnMarkdown") {
                        item.isLoading = false;
                    }
                    this.isEmpty = false;
                    handleThrottleLog('item.isLoading', item.isLoading);
                } else if (message.action === 'end') {
                    item.isLoading = false;
                    item.isLoadingNew = false;
                    this.isEmpty = false;
                    this.endStatus = false
                }
                handleThrottleLog('item.execute_result', item.execute_result, item.isLoading);
            }
        });
    });

    // 清空已处理的消息列表
    accumulatedMessages = [];
},
    // 通用 props
    getCommonProps(item) {
      return {
        executeResult: item.execute_result, // 通用数据
        isLoading: item.isLoading
      };
    },

    // 通用事件处理器
    getCommonEmitHandlers(item) {
      return {
        "e-update-executeResult": (updatedExecuteResult) =>
          this.updateExecuteResult(item.name, updatedExecuteResult),
      };
    },

    // 根据组件类型动态返回 props（覆盖或扩展通用 props）
    getComponentProps(item) {
      const commonProps = this.getCommonProps(item);
      const specificProps = (() => {
        switch (item.display_component) {
          case "EnnMarkdown":
            return { isEditable: true };
          case "EnnTable":
            return { showHeader: true };
          default:
            return {};
        }
      })();
      return { ...commonProps, ...specificProps };
    },

    // 根据组件类型动态返回事件处理器（覆盖或扩展通用事件）
    getComponentEmitHandlers(item) {
      const commonEmitHandlers = this.getCommonEmitHandlers(item);
      const specificEmitHandlers = (() => {
        switch (item.display_component) {
          case "EnnMarkdown":
            return {
              "e-update-content": (newContent) =>
                this.updateMarkdown(item.name, newContent),
            };
          case "EnnTable":
            return {
              "e-update-table": (updatedTable) =>
                this.updateTable(item.name, updatedTable),
            };
          default:
            return {};
        }
      })();
      return { ...commonEmitHandlers, ...specificEmitHandlers };
    },

    // 更新 executeResult 的通用方法
    updateExecuteResult(tabName, updatedResult) {
      // 查找对应的 tab
      const targetTab = this.optHeader2TabsList.find(tab => tab.name === tabName);

      if (targetTab) {
        // 更新 execute_result 值
        targetTab.execute_result = updatedResult;
        console.log(`Updated executeResult for ${tabName}:`, updatedResult);
      } else {
        console.warn(`Tab with name "${tabName}" not found.`);
      }
    },

    // 特定组件更新方法
    updateMarkdown(tabName, newContent) {
      console.log(`Updated Markdown content for ${tabName}:`, newContent);
    },
    updateTable(tabName, updatedTable) {
      console.log(`Updated Table for ${tabName}:`, updatedTable);
    },
    getComponent(componentCode) {
      const mappedComponent = ComponentAliasMap[componentCode] || componentCode; // 优先检查映射表
      return ComponentRegistry[mappedComponent] || null;
    },
    transformData(source, key) {
      return source.flatMap(item => {
        // 动态判断是否有嵌套结构（如 config）
        if (Array.isArray(item.config)) {
          return item.config
            // .filter(configItem => configItem.is_ability_result) // 筛选符合条件的数据
            .map(configItem => {
              let data = this.flowData.find(flowItem => flowItem.runCode === configItem.name);
              return ({
              ...configItem,
              label: configItem.name, // 显示名称
              name: `${key}.${configItem.name}`, // 拼接 key 和 name 作为唯一标识
              isLoading: false,
              isTaskStatus: true,
              execute_result: data?.summarize_message,
              execute_result_new: data?.summarize_message
            })
            });
        } else {
          // 如果是非嵌套结构，直接处理当前对象
          return item.is_ability_result
            ? {
              ...item,
              label: item.name, // 显示名称
              name: `${key}.${item.name}`, // 拼接 key 和 name 作为唯一标识
              isLoading: false,
              execute_result_new:item.execute_result,
            }
            : [];
        }
      });
    },
    returnMyaddItemFromData(source, key, TabName){
      return source.flatMap(item => {
        // 动态判断是否有嵌套结构（如 config）
        if (Array.isArray(item.config)) {
          return item.config
            .filter(configItem => `${key}.${configItem.name}` === TabName) // 筛选符合条件的数据
            .map(configItem => ({
              ...configItem,
              label: configItem.name, // 显示名称
              name: `${key}.${configItem.name}`, // 拼接 key 和 name 作为唯一标识
              isLoading: false
            }));
        } else {
          // 如果是非嵌套结构，直接处理当前对象
          return `${key}.${item.name}` === TabName
            ? {
              ...item,
              label: item.name, // 显示名称
              name: `${key}.${item.name}`, // 拼接 key 和 name 作为唯一标识
              isLoading: false
            }
          : [];
        }
      });
    },
    async getAbilitiesAndResult() {
      try {
        const res = await get_instance_abilities_with_result({
          scheme_id: this.$route.query.id,
          scheme_detail_id: this.detailContent?.id || ''
        })
        if(res.status === 200 && res.data.code === 200){
          this.AllAbility = res.data.result
          this.tools = Object.entries(this.AllAbility)
            .filter(([key]) => key !== 'api_generate_ability')
            .flatMap(([key, source]) =>
              this.transformData2(source, key)
            );
          console.log("this.tools", this.tools);
          console.log("AllAbility:", this.AllAbility);
          this.updateOptHeader2TabsList(this.AllAbility)
        }
        console.log('getAbilitiesAndResult:', res);
      } catch (error) {
        console.error('getAbilitiesAndResult:', error);
      }
    },
    async getAbilitiesAndResultNew(){
      try {
        const res = await get_instance_abilities_with_result({
          scheme_id: this.$route.query.id,
          scheme_detail_id: this.detailContent?.id || ''
        })
        if(res.status === 200 && res.data.code === 200){
          this.AllAbility = res.data.result
        }
      }catch{

      }
    },
    handleCATClick(tab, event) {
      console.log('handleCATClick', this.chatActiveTab, tab, event);
    },
    handleOptHeader2TabClick(tab, event){
      //lastMessage
      console.log('handleOptHeader2TabClick', tab.name, event);
      if(tab.name === '任务key') {
       // this.getQueryTaskContent()
      }
      this.dagangFlag = false
      this.dagangFlagNew = false
      if (tab.name === '方案详情key') {
        this.dagangFlag = true
        this.changeDagang()
      }
      if(this.dataProcess[tab.name]) {
        this.optionDataProcess = this.dataProcess[tab.name]
      }else {
        this.optionDataProcess = ''
      }
    },
    chendianSearchFun(val) {
      if (val) {
        const temp = this.globalChendianList.filter((item) => item.question.indexOf(val) > -1)
        this.chendianList = temp
      } else {
        this.chendianList = [...this.globalChendianList]
      }
    },
    handleChendianText(data) {
      this.chendianVisable = false
      this.planSearchFlag = false
      // this.$refs.chendianfanganRef.hide();
      startAppendHistory({
        qa: data,
        agent_role_id: this.agentAvatorInfo.id,
        session_id: this.sessionId
      })
    },
    handleDetailOpt(type, data) {
      if (
        !this.isEdit &&
        (this.systemMessages === 'process running' ||
          this.systemMessages === 'process stream' ||
          this.systemMessages === 'process_stream_message')
      ) {
        return false
      } else {
        if (!this.isInsertFlag && type === 'insert' && this.isEdit) {
          console.log('插入位置', this.$refs.MyEditor)
          console.log(this.$refs.MyEditor.$refs, this.$refs.MyEditor.$refs)
          this.$refs.MyEditor.$refs.editorFin.insert(() => {
            return { text: data }
          })
          this.insertWriteFlag = true
          this.$message({
            type: 'success',
            message: '插入成功!'
          })
        }
        if (!this.isReplaceFlag && type === 'replace' && this.isEdit) {
          this.$refs.MyEditor.$refs.editorFin.replaceSelectionText(data)
          this.replaceWriteFlag = true
          this.$message({
            type: 'success',
            message: '替换成功!'
          })
        }
      }
    },
    decryptFn(data) {
      const nKey = CryptoJs.enc.Base64.parse(data.key)
      const iv = CryptoJs.enc.Utf8.parse('\0'.repeat(16))
      const encrypted = CryptoJs.AES.decrypt(data.text, nKey, {
        iv: iv
      })
      return CryptoJs.enc.Utf8.stringify(encrypted)
    },
    refresh() {
      this.eventSource && this.eventSource.close()

      // this.getChatIsUseing();
      this.$emit('')
    },
    getBaiduFn() {
      getBaidu({})
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            const data = res.data.result
            console.log(data, 'baidu1')
            const baiduInfo = this.decryptFn(data)
            console.log(baiduInfo, 'baiduInfo字符串')
            const regex = /{([^}]*)}/g
            const match = baiduInfo.match(regex)
            if (match) {
              const extractedData = match[0]
              const baiduObj = JSON.parse(extractedData)
              this.websocketPara.URI = baiduObj.uri
              this.websocketPara.APPKEY = baiduObj.appkey
              this.websocketPara.DEV_PID = +baiduObj.dev_pid
              this.websocketPara.APPID = +baiduObj.app_id
            }
          }
        })
        .finally(() => {
          this.qaBoxLoading = false
        })
    },
    async getInstance() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      const instance = await getInstanceInfo({ instanceId: this.$route.query.id })
      if (instance.status === 200) {
        this.instanceInfo = instance.data
        this.adminJueSe = (instance.data.agent_role_id == '' || instance.data.agent_role_id == null ) ? true : false
        this.roleList= await this.searchManagerData()
        if(this.managerList.length > 0 && instance.data.agent_role_id) {
         const adminAgen = this.managerList.find(item => item.id === instance.data.agent_role_id)
         console.log('gggthis.instanceInfo3333333',this.managerList)
          const avatorInfo = {
              name: adminAgen?.name,
              alias: adminAgen?.alias,
              icon: adminAgen?.icon || '"@/assets/images/planGenerater/chat-icon.png"',
              id: adminAgen?.id,
              enable_image_text_msg: adminAgen.enable_image_text_msg
            }
          this.adminRole = [avatorInfo]
          this.selectAgentRole = avatorInfo
          this.selectAgentRoleNew = _.cloneDeep(this.selectAgentRole)
          console.log('bbbbthis.instanceInfo',this.adminRole)
        }
      }
    },
    // 清空聊天记录
    async clearChat(type) {
      if (type) {
        const res = await queryAgentInfoDetail({ scheme_id: this.$route.query.id })
        this.historyChat.messages = [
            {
              author: { role: 'agent' },
              auto: true,
              agent_role_id: this.currentRoleInfo?.id,
              content: {
                chat_message: {
                  content: res?.data[0].prologue || '请问有什么我可以帮您？',
                  preset_question: [''] //res?.data[0].preset_question
                }
              },
              create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
          ]
        const res2 = await get_preset_question({ scheme_id: this.$route.query.id });
        this.historyChat.messages[0].content.chat_message.preset_question = res2.data
        console.log('清空聊天记录成功', this.historyChat)

        // this.systemMessages = ''
        this.$emit('updateSystemMessage', '')
        this.qaList = []
        // this.historyChat.messages = [{
        //   author: {role: 'agent'},
        //   auto: true,
        //   content: {
        //     parts: this.greets || '请问有什么我可以帮您？'
        //   },
        //   create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        // }]
      } else {
        this.qaList = []
        this.$message({
          type: 'error',
          message: '清空聊天记录失败!'
        })
      }
    },
    closeCaiNaFunc(data) {
      console.log('closeCaiNaFunc', data)
      this.optHeader2TabsList.forEach(item =>{
        if(item.name == '方案详情key'){
          this.detailContent.text = item.execute_result_new
        }
        if(item.name == data.name){
          item.execute_result = item.execute_result_new
        }
      })
      this.$forceUpdate()
    },
    async handleSave(data,item,status,isCaiNa) {
     console.log('handleSave1', data, item, status, isCaiNa)
      if(status && !this.doneSend){
        return false
      }
      this.activeZl = 'first'
      this.isLoadPage = true
      if(isCaiNa){
        this.optHeader2TabsList.forEach(it =>{
        if(item.name == '方案详情key'){
          this.optHeader2Tab = '方案详情key'
          it.execute_result = this.detailContent.text

          this.detailContent.text = this.extractAfterThinkTag(data)
        }
        if(it.value == item.value && item.name != '方案详情key'){
          this.optHeader2Tab = item.name
          it.execute_result = this.extractAfterThinkTag(data)
        }
      })
      this.isEdit = true
      return false
      }
      let display_component_data = this.optHeader2TabsList.filter(it =>it.value == item?.value)[0]
      let datavt= {}
      let system_ability_config = this.instanceInfo.system_ability_config.filter(it => item.label == it.name)[0]
      let user_ability_config = this.instanceInfo.user_ability_config.filter(it => item.label == it.name)[0]
      console.log(system_ability_config,user_ability_config,'user_ability_config')
      if(system_ability_config){
        datavt ='system_ability'
      }else if(user_ability_config){
        datavt ='user_ability'
      }
      const res = await update_ability_result({
        scheme_id: this.$route.query.id,
        ability_code: item.isTaskStatus ? `api_generate_ability.${item.label}`: (item.label ? (item.label == '能力方案'  ? '方案详情':`${datavt}.${item.label}`): undefined),
        ability_result: this.extractAfterThinkTag(data),
        ability_display_component: item.label == '能力方案' ? null : display_component_data.display_component,
      })
      // 任务产物单独走新的逻辑
      if (item.isTaskStatus) {
        let agent_template_id = ''
        this.flowData.forEach(it => {
          this.optHeader2TabsList.forEach(itt => {
            if (it.runCode == itt.label) {
              agent_template_id = it.agent_template_id
            }
          })
        })
        await chain_variable_session_upd({
          "scheme_id": this.$route.query.id,
          "agent_template_id": agent_template_id,
          "name": item.label,
          "value": this.extractAfterThinkTag(data),
        })
      }
      this.optHeader2TabsList.forEach(it =>{
        if(item.name == '方案详情key'){
          this.optHeader2Tab = '方案详情key'
          this.detailContent.text = res.data.result.handle_text
          it.execute_result_new = res.data.result.handle_text
        }
        if(it.value == item.value){
          this.optHeader2Tab = item.name
          it.execute_result = res.data.result.handle_text
          it.execute_result_new = res.data.result.handle_text
        }
      })
    },
    async handelQuestion() {
      this.qaBoxLoading = true
      this.$nextTick(async () => {
        this.throttledScrollToBottom()
        await queryQuestionList({ session_id: this.sessionId })
          .then((res) => {
            this.qaBoxLoading = false
            if (res.status === 200 && res.data.code === 200) {
              this.qaList = res.data?.result || []
              this.$nextTick(() => {
                this.throttledScrollToBottom()
              })
            }
          })
          .finally(() => {
            this.qaBoxLoading = false
          })
      })
    },
    handleMessageStream(message) {
      const userInfo = sessionStorage.getItem('USER_INFO')
      ? JSON.parse(sessionStorage.getItem('USER_INFO'))
      : {}
      if (message.action === 'start') {
        console.log('会话消息', message)
        // this.systemMessages = 'process stream'
        this.$emit('updateSystemMessage', 'process stream')
        this.historyChat.messages.push({
          author: { role: message.role },
          content: {
            chat_message_type: 'text',
            chat_message: {
              content: message?.content || message?.data
            },
            nickName: message.role === 'user_proxy' ? userInfo.nickName : '智能伙伴',
            username: message.role === 'user_proxy' ? userInfo.username : ''
          },
          agent_role_id: this.getagent_role_id(),
          create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        })
        this.$nextTick(() => {
          this.throttledScrollToBottom()
        })
      } else {
        // this.systemMessages = 'process stream running'
        this.$emit('updateSystemMessage', 'process stream running')
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
        lastMessage.content.chat_message.content =
          lastMessage.content.chat_message.content + message.data
        // lastMessage.content.parts = lastMessage.content.parts + message.data;
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
        // console.log('最后拼接的', lastMessage);
        this.lastMessage = lastMessage
        this.$nextTick(() => {
          this.throttledScrollToBottom()
        })
      }
      if (message.action === 'end') {
        this.doneSend = true
        // this.systemMessages = ''
        this.$emit('updateSystemMessage', '')
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
        lastMessage.sub_content = this.processRunningContent
        lastMessage.id = dayjs().format('MMDDHHmm')
        this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
        if(this.ischuanzuoShow && this.chatStatus && !this.lastChatShow && this.choseData?.labelChanWu && this.jjRole == 'Agent'){
          let data = this.optHeader2TabsList.filter(it => it.label == this.choseData.labelChanWu)[0]
          this.optHeader2Tab = this.choseData.label
          this.lastChatShow = true
          this.handleSave(this.historyChat.messages[this.historyChat.messages.length -1].content.chat_message.content,data)
        }
        console.log('当前对话结束后将思考过程拼接到消息中', lastMessage)
        // this.qaBoxLoading = true;
        // this.$nextTick(() => {
        //   this.throttledScrollToBottom();
        //   // 查询猜你想问
        //   this.handelQuestion();
        // })
        this.$parent.$nextTick(async () => {
          this.throttledScrollToBottom()
          // 查询猜你想问，暂时先屏蔽
          // this.handelQuestion();
          // 循环模式，直到speaker_selection返回的角色type=user的时候，停止循环
          this.ansBoxLoading = true
          let roleId = ''
          console.log('开启下一次的轮训，直到type==user')
          if(this.adminRole.length ==0) return
          await queryAnsRole({ session_id: this.sessionId, scheme_id: this.$route.query.id })
            .then((res) => {
              this.ansBoxLoading = false
              if (res.status === 200 && res.data.code === 200 && res.data.result?.id) {
                roleId = res.data.result?.id || ''
                if (res.data.result.type !== 'user') {
                  this.currentRoleInfo = this.agentAvatorInfoMap[res.data.result?.id]
                  startConversation({
                    message: '',
                    agent_role_id: roleId,
                    session_id: this.sessionId,
                    mode: this.activeTab == 'chat' ? 'chat' : 'builder'
                  })
                } else {
                  this.qaBoxLoading = true
                  this.handelQuestion()
                }
              } else {
                this.currentRoleInfo = this.agentAvatorInfo
              }
            })
            .finally(() => {
              this.ansBoxLoading = false
            })
        })
      }
    },

    // handleMessage(message) {
    //   const userInfo = sessionStorage.getItem('USER_INFO')
    //     ? JSON.parse(sessionStorage.getItem('USER_INFO'))
    //     : {}
    //   this.historyChat.messages.push({
    //     author: { role: message.role },
    //     auto: message.role === 'auto' || message.data === '请您发送反馈信息',
    //     content: {
    //       // parts: message?.content || message?.data,
    //       chat_message_type:
    //         message?.image_key !== undefined && message?.image_key !== '' ? 'img_text' : 'text',
    //       chat_message: {
    //         content: message?.content || message?.data,
    //         image_path: message?.image_path,
    //         image_key: message?.image_key
    //       },
    //       nickName: message.role === 'user_proxy' ? userInfo.nickName : '',
    //       username: message.role === 'user_proxy' ? userInfo.username : ''
    //     },
    //     agent_role_id: this.currentRoleInfo?.id,
    //     create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
    //   })
    //   this.$nextTick(() => {
    //     this.throttledScrollToBottom()
    //   })
    // },
    async queryAgentAvator() {
      await queryAgentInfoDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data) {
          this.agentRoleList = res.data || []
          if (res.data && res.data.length) {
              await this.getInstance()
              this.currentRoleInfo = res.data?.[0] || {}
              this.agentAvatorInfo = {
              name: res.data?.[0]?.name,
              alias: res.data?.[0]?.alias,
              icon: res.data?.[0]?.icon,
              id: res.data?.[0]?.id,
              enable_image_text_msg: res.data?.[0]?.enable_image_text_msg
            }
            this.checkRole = this.agentAvatorInfo.name
            const temp = {}
            const temp2 = []
            const combinedArray = [...res.data, ...this.roleList];
            combinedArray.map((item) => {
              temp[item.id] = {
                name: item.name,
                alias: item.alias,
                icon: item.icon || "@/assets/images/planGenerater/chat-icon.png",
                id: item.id,
                enable_image_text_msg: item.enable_image_text_msg
              }
              temp2.push('@' + item.name + ' ')
            })
              this.agentAvatorInfoMap = temp
            if(this.historyChat.messages.length > 0 && this.adminRole.length <=0) {
              const exists = res.data.some(item => item.id === this.historyChat.messages[this.historyChat.messages.length - 1].agent_role_id);
              if(exists) {
                this.currentRoleInfo = temp[this.historyChat.messages[this.historyChat.messages.length -1].agent_role_id] || {}
                this.selectAgentRole = temp[this.historyChat.messages[this.historyChat.messages.length -1].agent_role_id]
                this.selectAgentRoleNew = _.cloneDeep(this.selectAgentRole)
              }else {
                this.currentRoleInfo = this.agentAvatorInfo || {}
                this.selectAgentRole = this.agentAvatorInfo
                this.selectAgentRoleNew = _.cloneDeep(this.selectAgentRole)
              }
            }
            this.checkRole = this.selectAgentRoleNew.name
            this.agentAvatorInfoList = temp2
          }
        }
      })
    },
    handleChangeRole(row) {
      console.log(row,'row')
      //if(this.ischuanzuoShow){
      //  return false
      //}
      const avatorInfo = {
          name: row?.name,
          alias: row?.alias,
          icon: row?.icon,
          id: row?.id,
          enable_image_text_msg: row?.enable_image_text_msg
      }
      this.selectAgentRoleNew = avatorInfo
      const lastMess = this.historyChat.messages[this.historyChat.messages.length - 1]
      this.selectAgentRole = row
      if (
        lastMess.id &&
        this.systemMessages !== 'process running' &&
        this.systemMessages !== 'process stream' &&
        this.systemMessages !== 'process_stream_message' &&
        this.systemMessages !== 'process stream running'
      ) {
        this.currentRoleInfo = row
        this.chendianVisable2 = false
      } else {
        if (lastMess?.author?.role === 'system') {
          this.currentRoleInfo = row
          this.chendianVisable2 = false
        } else {
          this.chendianVisable2 = false
        }
      }
    },
    handleAsk(qa,type = 'chat') {
     console.log('fdjapfjsdpoasjd',qa)
      this.qaList = []
      startConversation({
        message: qa,
        agent_role_id: this.currentRoleInfo.id,
        session_id: this.sessionId,
        "mode": 'chat',
      })
    },
    clearHistory() {
      console.log(
        '清空聊天记录123',
        [
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) < 0 &&
        !this.hasChatingName &&
        !this.qaBoxLoading
      )
      // this.systemMessages = 'clear_history'
      this.$emit('updateSystemMessage', 'clear_history')
      this.qaList = []
      if (this.agentRoleList.length > 1) {
        this.currentRoleInfo = this.agentRoleList[0]
      }
      startClearHistory({ session_id: this.sessionId })
    },
    changeViewsCallZhuge(val) {
      this.changeViews(val)
    },
    async updateSchemeDetailName(newSchemeDetailName) {
      const params = {
        id: this.schemeInfo.id + '',
        display_type: this.displayType,
        name: this.schemeInfo.name,
        scheme_detail_name: newSchemeDetailName,
        description: this.schemeInfo.description,
        agent_scene: this.schemeInfo.agent_scene,
        agent_scene_code: this.schemeInfo.agent_scene_code,
        agent_id: this.schemeInfo.agent_id || '',
        contributors: this.schemeInfo.contributors,
        tag_ids: this.schemeInfo.tag_ids,
        ext_data_info: this.schemeInfo.ext_data_info,
        visibility: this.schemeInfo.visibility,
        share_userids: this.schemeInfo.share_infos
      }
      UpdateScheme(params).then(async (res) => {
        if (res.status === 200 && res.data.code * 1 === 200) {
          this.$emit('update-scheme-info', { ...this.schemeInfo, display_type: this.displayType })
          // this.schemeInfo = { ...this.schemeInfo, display_type: this.displayType };
        }
      })
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.chat) {
          this.$refs.chat.scrollToBottom()
        }
      })
    },

    handleSelectRole(role) {
      // 将选中的人员添加到输入框中
      this.currentRoleInfoTemp = role
      // this.currentText = this.currentText.slice(0, -1);
      this.showSelectVisable = false
      this.toMessage.content = this.toMessage.content.slice(0, -1)
      console.log('选择角色', role, this.toMessage)
      this.$refs.myChatInputText?.focus()
      this.$nextTick(() => {
        const target = document.getElementById('myInputText')
        this.inputHeight = target.getBoundingClientRect().height
      })
    },
    removeRole() {
      this.currentRoleInfoTemp = {}
      this.currentRoleInfo = this.agentAvatorInfo
      this.$refs.myChatInputText?.focus()
      this.$nextTick(() => {
        const target = document.getElementById('myInputText')
        this.inputHeight = target.getBoundingClientRect().height
      })
    },
    // 方案优化信号推送
    emitOptimize(val) {
      startSchemeOptimize({ scheme_optimize_ability_id: val, session_id: this.sessionId })
    },
    closeYouhuaEdit() {
      this.youhuaVisable = false
      this.queryPlanDetail()
    },
    fangda2(e) {
      if (this.isEdit) {
        const selection = this.$refs.MyEditor.$refs.editorFin?.getCurrentSelectedStr()
        console.log('触发绑定', selection)
        if (selection) {
          this.writeText = selection
          this.insertWriteFlag = true
          this.replaceWriteFlag = false
        } else {
          this.writeText = selection
          this.insertWriteFlag = false
          this.replaceWriteFlag = true
        }
      }
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = 'calc( (100vw - 364px) *0.4)'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    changeDagang() {
      this.dagangFlag = !this.dagangFlag
      if (!this.dagangFlag) {
        const nodeMarkmap = document.getElementById('markmap')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
      } else {
        const transformer = new Transformer()
        const { root } = transformer.transform(this.detailContent.text)
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root)
        })
      }
    },
    changeDagangNew() {
      this.dagangFlagNew = !this.dagangFlagNew
      if (!this.dagangFlagNew) {
        const nodeMarkmap = document.getElementById('markmapenn')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
      } else {
        const transformer = new Transformer()
        const currentTab = this.optHeader2Tab; // 当前 Tab 名称
        const currentComponent = this.$refs[`tab-${currentTab}`][0]; // 获取动态组件实例
        const { root } = transformer.transform(currentComponent.executeResultData)
        this.$nextTick(() => {
          Markmap.create('#markmapenn', null, root)
        })
      }
    },
    modelUploadSuccess(response, file) {
      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        this.toMessage.image_name = file.name;
        this.$refs.chat.$refs.uploadBtn.clearFiles()
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key
        this.toMessage.image_key = fileKey
        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.$nextTick(() => {
                this.toMessage.image_path = res.data.data.path;

            });
            }
          })
      } else {
        this.$message.warning(`模型上传状态为:${this.uploadStatus}`)
      }
    },
    async beforeUpload(file) {
      try {
        console.log('beforeUpload this.baseUrl', this.baseUrl)
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        })
        if (res.data.status === 200) {
          this.uploadUrl = res.data.data.obsUrl
          console.log('beforeUpload this.uploadUrl', this.uploadUrl)
          this.toMessage.image_key = res.data.data.key
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    handleInput(val) {
      if (val.charAt(0) === '@') {
        // 输入了 @ 字符，触发选人逻辑
        this.showSelectVisable = true
      }
      if(val.charAt(0) === '@'){
        this.toMessage.content = ''
      }
    },
    chendianFun() {
      getSuggestionList().then(async (res) => {
        if (res.status === 200 && res.data.success) {
          console.log('沉淀方案列表', res.data.data)
          this.chendianList = res.data.data || []
          this.globalChendianList = res.data.data || []
          this.chendianVisable = true
          const target = document.getElementById('myInputText')
          // console.log('输入框高度', target.getBoundingClientRect().height);
          this.inputHeight = target.getBoundingClientRect().height
        } else {
          this.$message({
            type: 'error',
            message: res.data?.message || '接口异常!'
          })
        }
      })
    },
    startWeb() {
      console.log('----开始监听说话------')
      console.log('this.historyChat 2222222', this.historyChat)
      console.log('this.systemMessages2222222', this.systemMessages)
      const that = this
      if (
        ([
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 &&
          !this.historyChat.messages[this.historyChat.messages.length - 1].id) ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false
      } else {
        // 获取麦克风输入流
        navigator.mediaDevices.getUserMedia({ audio: true }).then(function (stream) {
          that.websocket = new WebSocket(
            that.websocketPara.URI + '?sn=sn' + Math.round(Math.random() * 10000),
            'websocket'
          )
          that.websocket.onopen = function () {
            console.log('that.websocket.onopen')
            let buffer
            const req = {
              type: 'START',
              data: {
                appid: that.websocketPara.APPID,
                appkey: that.websocketPara.APPKEY,
                dev_pid: that.websocketPara.DEV_PID,
                cuid: userInfo.userId,
                sample: that.websocketPara.sample,
                format: 'pcm'
              }
            }
            that.phoneStatus = 'running'
            const message = JSON.stringify(req)
            console.log('发送开始帧：' + message)
            that.websocket.send(message)
            // 初始化音频上下文
            that.audioContext = new AudioContext()
            // 创建音频输入节点
            const source = that.audioContext.createMediaStreamSource(stream)
            // 创建缓冲区节点
            const scriptProcessor = that.audioContext.createScriptProcessor(
              that.websocketPara.CHUNK_SIZE,
              1,
              1
            )
            source.connect(scriptProcessor)
            scriptProcessor.connect(that.audioContext.destination)
            scriptProcessor.onaudioprocess = async function (event) {
              const audioData = event.inputBuffer.getChannelData(0)
              const sampleRate = that.audioContext?.sampleRate
              const sampleCount = audioData.length
              const sampleLength = Math.floor(
                sampleCount / (sampleRate / that.websocketPara.sample)
              )
              const sampleData = new Float32Array(sampleLength)
              for (let i = 0; i < sampleLength; i++) {
                sampleData[i] = audioData[Math.floor((i * sampleRate) / that.websocketPara.sample)]
              }
              // 将音频数据转换为二进制数据类型
              buffer = new ArrayBuffer(sampleData.length * 2)
              const view = new DataView(buffer)
              for (let i = 0; i < sampleData.length; i++) {
                view.setInt16(i * 2, sampleData[i] * 0x7fff, true)
              }
              that.websocket?.send(buffer)
            }
          }
          that.websocket.onmessage = that.onmessage
          that.websocket.onerror = that.onerror
          that.websocket.onclose = that.onclose
        })
      }
    },
    onerror() {
      console.log('WebSocket 连接发生错误')
    },
    onmessage(event) {
      console.log('接收到的message', event.data)
      const webMessage = JSON.parse(event.data)
      if (webMessage.type === 'FIN_TEXT' && webMessage.err_msg === 'OK') {
        // this.currentText = webMessage.result;
        this.toMessage.content = webMessage.result
        this.phoneStatus = 'shibie'
        this.sendMessage()
        this.stopWeb()
      }
    },
    onclose() {
      console.log('WebSocket连接关闭')
    },
    // realTime() {
    //   this.phoneFlag = true
    //   this.leftWidth = '100%'
    //   this.startWeb()
    // },
    closePhone() {
      this.phoneFlag = false
      this.leftWidth = 'calc( (100vw - 364px) *0.4)'
      this.phoneStatus = 'start'
      this.stopThink()
      this.stopWeb()
    },
    stopWeb() {
      console.log('stopWebxxx')
      // 关闭 WebSocket 连接
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close()
        this.audioContext = null
      }
    },
    stopThink() {
      startStopThinking({ session_id: this.sessionId })
      this.refresh()
      // this.systemMessages = ''
      this.$emit('updateSystemMessage', '')
      const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1]
      lastMessage.sub_content = this.processRunningContent
      lastMessage.id = dayjs().format('MMDDHHmm')
      this.historyChat.messages[this.historyChat.messages.length - 1] = lastMessage
      console.log('当前对话结束后将思考过程拼接到消息中', lastMessage)
      this.phoneStatus = 'start'
      this.stopWeb()
      this.$nextTick(() => {
        this.throttledScrollToBottom()
      })
      // this.queryDetail();
    },
    // all above methods from yeahz
    queryDetailFn() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        // this.schemeInfo = res.data.result;
        this.$emit('update-scheme-info', res.data.result)
      })
    },
    // 跳转至侄子孪生页面
    handleJumpToDigitalTwin() {
      console.log('444', process.env.VUE_APP_ENV)
      window.open(
        `https://air${process.env.VUE_APP_ENV === 'production' ? '.fat' : '.' + process.env.VUE_APP_ENV
        }.ennew.com/dt-manage/project/list`
      )
    },
    // 获取UUId
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0
        const v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    // 获取ability_id
    async handleGetAbilityId() {
      this.deviceLoading = true
      const vm = this
      const res = await vm.$axios.get(
        `${vm.baseUrl}/platform/conf/getApolloVal?key=gpts.twin_iot_model_analysis_ability.id`
      )
      if (res && res.data && res.data.status === 200) {
        this.ability_id = res.data.data
        await this.handleGetModeTypeAndModeCode()
      }
    },
    // 获取孪生物联模型页面的modeType和modeCode
    async handleGetModeTypeAndModeCode() {
      await this.handleGetSchemeDescription()
      // const res = await getExecuteSync({
      //   scene_instance_id: this.$route.query.id,
      //   ability_id: this.ability_id,
      //   name: this.generateUUID(),
      //   goal: this.schemeDescription
      // })
      // if (res?.status === 200) {
      //   this.modelObj = res.data
      //   // 处理加工iframe的url
      //   const path = `${this.env[process.env.VUE_APP_ENV]}&modeType=${this.modelObj.modelType
      //     }&modelCode=${this.modelObj.modelCode}`
      //   this.iframeSrc = this.getQueryToken(path)
      // }
    },
    // 获取方案描述
    async handleGetSchemeDescription() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      await querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        this.schemeDescription = this.schemeDescription || res.data.result?.description
        this.capability_type = res.data.result?.capability_type
        this.abilitys =[{
          name: res.data.result?.result_file_root_path,
          objectKey: res.data.result?.result_file_root_path,
          scheme_id:this.$route.query.id
        }]
        this.firstUrl =res.data.result?.result_file_root_path
      })
    },
    // 更新外层方案描述
    async handleUpdateSchemeDescription() {
      await updateSchemeDescription({
        scheme_id: this.$route.query.id,
        description: this.schemeDescription
      }).then((res) => { })
    },
    handleNext() {
      console.log('下一步')
      this.codeAnalysisProcessStatus = false
      this.$emit('updateStep', 1)
    },
    // url地址上的token参数
    getQueryToken(url) {
      setTimeout(() => {
        this.deviceLoading = false
      }, 2000)
      return this.authSdk?.transformToAuthUrl(url, 'local')
    },
    updateDeviceId(obj) {
      this.deviceObj = obj
      window.DEVICE_OBJ = this.deviceObj
      const params = {
        scheme_id: this.$route.query.id,
        device_id: obj.deviceId || '',
        device_name: obj.deviceName || obj.modelName || '',
        model_type: obj.modeType,
        model_code: obj.modelCode || ''
      }
      // TODO 多选透传传参数据格式改变，带后端确认
      if (obj.type === 'IOTtoIMPSelectList') {
      }
      // 多选透传不需要更新iframe的url
      if (obj.type !== 'IOTtoIMPSelectList') {
        const path = `${this.env[process.env.VUE_APP_ENV]}&modeType=${obj.modeType}${obj.modelCode ? '&modelCode=' + obj.modelCode : ''
          }`
        this.iframeSrc = this.getQueryToken(path)
      }
      // 请求增加条件限制
      if ((obj.modeType === '2' && !obj.modelCode) || (obj.modeType === '1' && !obj.deviceId)) {
        return
      }
      updateByDeviceId(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('更新设备完成', res.data)
          this.$emit('updateDeviceId', res.data.result?.device_id)
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    // 回车发送消息
    carriageReturn(event) {
      // const e = window.event || arguments[0];
      // console.log('event', event)
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        if (!event.metaKey && !event.ctrlKey) {
          event.preventDefault()
          this.$nextTick(() => {
            if (this.toMessage.content) {
              this.sendMessage()
            }
          })
        } else {
          if (event.ctrlKey || event.metaKey) {
            this.toMessage.content += '\n'
            const target = document.getElementById('myInputText')
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              })
            }
          } else {
            event.preventDefault()
          }
        }
      }
      // 英文下｜中文下： 13 Enter Enter
      // 中文下有文字没进入输入框情况是：299 Enter Enter
      // if (e.key === 'Enter' && e.code === 'Enter' && e.keyCode === 13) {
      //   // console.log('this.text', this.currentText);
      //   this.$nextTick(() => {
      //     if (this.currentText) {
      //       this.sendMessage();
      //     }
      //   });
      // }
    },

    async sendMessage(inStatus,inData) {


      if(this.isReadOnly) return
      // 将消息发送到服务器
      // console.log('发送消息', this.currentText);
      // this.activeZl = 'first'
      this.doneSend = false
      if(this.ischuanzuoShow && this.choseData?.labelChanWu){
        this.lastChatShow = false
      }
      this.qaList = []
      let roleId = ''
      const currentId = this.agentRoleList?.some(item => item.id === this.selectAgentRole.id)
      if (currentId) {
        this.currentRoleInfo = this.selectAgentRole
        roleId = this.selectAgentRole.id
      } else {
        console.log('使用角色 this.currentRoleInfo1',this.agentRoleList)
        if (this.agentRoleList.length > 1) {
          this.ansBoxLoading = true
          await queryAnsRole({
            session_id: this.sessionId,
            scheme_id: this.$route.query.id,
            message: this.toMessage.content
          })
            .then((res) => {
              this.ansBoxLoading = false
              console.log('查询角色的结果', res)
              if (
                res.status === 200 &&
                res.data.code === 200 &&
                res.data.result?.id &&
                res.data.result?.id !== 'user'
              ) {
                roleId = res.data.result?.id || ''
                // this.currentRoleInfo = this.agentAvatorInfoMap[res.data.result?.id]
              } else {
                // this.currentRoleInfo = this.agentAvatorInfo
                // roleId = this.agentAvatorInfo.id
              }
            })
            .finally(() => {
              this.ansBoxLoading = false
            })
        } else {
          this.currentRoleInfo = this.agentAvatorInfo
          roleId = this.agentAvatorInfo.id
        }
      }
      let datavt= {}
      let system_ability_config = this.instanceInfo.system_ability_config.filter(it => this.choseData.label == it.name)[0]
      let user_ability_config = this.instanceInfo.user_ability_config.filter(it => this.choseData.label == it.name)[0]
      if(system_ability_config){
        datavt ='system_ability'
      }else if(user_ability_config){
        datavt ='user_ability'
      }
      let agent_role_id = ''
      if(!this.showSelectRoleId){
        agent_role_id = this.currentRoleInfo?.id
      } else{
        agent_role_id = this.showSelectRoleId
      }
      let rel_files = this.$refs.chat.gethandleCheckedFiles()
      let obj = Object.assign({},inStatus ? {content:inData} : this.toMessage,{context_code: (this.choseData.label ? (this.choseData.label == '能力方案'  ? '方案详情':`${datavt}.${this.choseData.label}`): undefined)})
          obj = {
           ...obj,
           ...((this.ischuanzuoShow && this.getJujiaoStatus?.data?.label != '代码生成') ? { 'chat_context_codes': [this.getJujiaoStatus?.data?.name] } : {}),
           rel_files: rel_files,
          }

      startConversation({
        message: obj,
        agent_role_id: this.adminRole.length > 0 ? agent_role_id :this.selectAgentRoleNew.id,
        session_id: this.sessionId,
        "mode": 'chat',
      })
      // 将选中的文件清空
      this.$refs.chat.clearCheckedFiles()
      // this.currentText = '';
      this.toMessage = { content: '', image_key: '', image_path: '' }
      this.speakingFlag = 'start'
      this.yuyinText = ''
      this.taskStatusText = ''
      if (this.phoneFlag) {
        this.phoneStatus = 'shibie'
      }

      // this.currentRoleInfoTemp = {}
    },
    async sendMessage2(inStatus,inData) {


      if(this.isReadOnly) return
      // 将消息发送到服务器
      // console.log('发送消息', this.currentText);
      // this.activeZl = 'first'
      this.doneSend = false
      if(this.ischuanzuoShow && this.choseData?.labelChanWu){
        this.lastChatShow = false
      }
      this.qaList = []
      let roleId = ''
      const currentId = this.agentRoleList?.some(item => item.id === this.selectAgentRole.id)
      if (currentId) {
        this.currentRoleInfo = this.selectAgentRole
        roleId = this.selectAgentRole.id
      } else {
        console.log('使用角色 this.currentRoleInfo1',this.agentRoleList)
        if (this.agentRoleList.length > 1) {
          this.ansBoxLoading = true
          await queryAnsRole({
            session_id: this.sessionId,
            scheme_id: this.$route.query.id,
            message: this.toMessage.content
          })
            .then((res) => {
              this.ansBoxLoading = false
              console.log('查询角色的结果', res)
              if (
                res.status === 200 &&
                res.data.code === 200 &&
                res.data.result?.id &&
                res.data.result?.id !== 'user'
              ) {
                roleId = res.data.result?.id || ''
                // this.currentRoleInfo = this.agentAvatorInfoMap[res.data.result?.id]
              } else {
                // this.currentRoleInfo = this.agentAvatorInfo
                // roleId = this.agentAvatorInfo.id
              }
            })
            .finally(() => {
              this.ansBoxLoading = false
            })
        } else {
          this.currentRoleInfo = this.agentAvatorInfo
          roleId = this.agentAvatorInfo.id
        }
      }
      let datavt= {}
      let system_ability_config = this.instanceInfo.system_ability_config.filter(it => this.choseData.label == it.name)[0]
      let user_ability_config = this.instanceInfo.user_ability_config.filter(it => this.choseData.label == it.name)[0]
      if(system_ability_config){
        datavt ='system_ability'
      }else if(user_ability_config){
        datavt ='user_ability'
      }
      let agent_role_id = ''
      if(!this.showSelectRoleId){
        agent_role_id = this.currentRoleInfo?.id
      } else{
        agent_role_id = this.showSelectRoleId
      }
      let rel_files = this.$refs.chat.gethandleCheckedFiles()
      let obj = Object.assign({},inStatus ? {content:inData} : this.toMessage,{context_code: (this.choseData.label ? (this.choseData.label == '能力方案'  ? '方案详情':`${datavt}.${this.choseData.label}`): undefined)})
          obj = {
           ...obj,
           ...((this.ischuanzuoShow && this.getJujiaoStatus?.data?.label != '代码生成') ? { 'chat_context_codes': [this.getJujiaoStatus?.data?.name] } : {}),
           rel_files: rel_files,
          }

      startConversation({
        message: obj,
        agent_role_id: this.adminRole.length > 0 ? agent_role_id :this.selectAgentRoleNew.id,
        session_id: this.sessionId,
        "mode": 'builder',
      })
      // 将选中的文件清空
      this.$refs.chat.clearCheckedFiles()
      // this.currentText = '';
      this.toMessage = { content: '', image_key: '', image_path: '' }
      this.speakingFlag = 'start'
      this.yuyinText = ''
      this.taskStatusText = ''
      if (this.phoneFlag) {
        this.phoneStatus = 'shibie'
      }

      // this.currentRoleInfoTemp = {}
    },
    // modify same name function yeahz
    // async sendMessage() {
    //   console.log('22222222222222222222222222255555');
    //   if (this.isExpert) {
    //     this.$emit('initFlowData', 0);
    //     this.runTaskStatus = 0;
    //   }
    //   // 正在生成方案明细时，不能再次发送
    //   if (this.reTree) {
    //     this.$message({
    //       type: 'error',
    //       message: '正在生成方案明细，请稍后发送！'
    //     });
    //     return;
    //   }
    //   this.treeData = '';
    //   // 将消息发送到服务器
    //   console.log('发送消息', this.currentText);
    //   this.planLoading = true;
    //   this.$emit('updateSendMsg', this.currentText);
    //   // this.getKonwledgeSourceFn(this.currentText)
    //   // this.isKnowledge = true
    //   this.schemeDescription = this.currentText;
    //   await this.handleGetAbilityId();
    //   await this.handleUpdateSchemeDescription();
    //   this.currentText = '';
    //   this.speakingFlag = 'start';
    //   this.yuyinText = '';
    // },
    async startRecording() {
      if(this.isReadOnly) return
      if (
        [
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false
      } else {
        try {
          this.recorderEx.start()
          this.speakingFlag = 'running'
          this.yuyinText = '正在语音中...'
        } catch (err) {
          console.error('无法获取媒体流:', err)
          this.speakingFlag = 'start'
        }
      }
    },
    stopRecording() {
      this.speakingFlag = 'end'
      this.recorderEx.stop()
      setTimeout(() => {
        const wavBlob = this.recorderEx.getWAVBlob() // blob格式
        console.log('this.audioChunks33', wavBlob)
        this.yuyinText = '正在转换文字中...'
        this.runExcute(wavBlob)
      })
    },
    async runExcute(audioBlob) {
      this.shibieLoading = true
      this.currentText = ''
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
        : process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
      // const url = 'http://10.20.50.95:80/voice/conversion/text'
      console.log('url', url, audioBlob)
      await this.$axios
        .post(url, audioBlob, {
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          headers: {
            'Content-Type': 'application/octet-stream'
          },
          onDownloadProgress: (event) => {
            const xhr = event.target
            const { responseText } = xhr
            console.log('流信息', responseText)
            let chunk = ''
            let dataArray
            const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2)
            if (lastIndex !== -1) {
              chunk = responseText.slice(0, lastIndex)
              dataArray = chunk.match(/(.*(\n\n\")?\})(\n\n)?/g)
              const lastText = JSON.parse(dataArray[dataArray.length - 2]?.replace('data:', ''))
              console.log('lastTextlastTextlastText', lastText)
              if (lastText) {
                this.currentText = lastText?.message
              }
            }
          },
          onError: function (error) {
            // 处理流错误
            console.error(error)
            this.speakingFlag = 'start'
            this.shibieLoading = false
          }
        })
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流结束', response)
          this.speakingFlag = 'start'
          this.shibieLoading = false
        })
        .catch((err) => {
          this.loading = false
          console.log('识别接口错误', err)
          this.speakingFlag = 'start'
          this.shibieLoading = false
        })
    },
    // 查看任务进度
    showTask() {
      this.$emit('showTaskModal')
    },
        // 查看任务进度
    showTaskNew() {
      // this.$emit('showTaskModal')
      let query = this.getInstructionParam()
      this.$router.push({path:'/planGenerate/runTask',query:{
        ...this.$route.query,
        backStatus:1,
        instruction_param: JSON.stringify(query)
      }})
    },
    async initTaskStatus() {
      getTaskStatus({ scheme_id: this.$route.query.id }).then((gres) => {
        console.log('任务是否需要触发', gres)
        this.runTaskStatus = gres.data?.result?.task_status || 0
        console.log('runTaskStatus 1', this.runTaskStatus)
      })
    },
    async changeViews(val) {
      this.nextLoading = true
      if (this.schemeInfo.agent_scene_code === 'digital_twin_assistant_scene'){
        const res = await checkByDeviceId({scheme_id: this.$route.query.id})
        console.log('res checkByDeviceId', res);
        if (res.status === 200 && res.data.code === 200){
          // if (!this.developFlag)
          window.DEVICE_TYPE = res.data.result.model_code
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '设备未绑定成功'
          });
        }
      }
      try {
        if (this.taskAllComplete) {
          console.log('this.taskAllComplete', this.taskAllComplete);
          this.$emit('updateStep', val);
        } else {
          console.log("this.MyAbilityId 00", this.MyAbilityId);
          let isFirst = true
          try{
            isFirst = this.flowData[0].status == 0
          } catch{

          }
          if (isFirst) {
            // this.$emit('handleReStart', 'startrun');
            let query = this.getInstructionParam()
            this.$router.push({
            name:'planGenerateRunTask',
            query:{
              ...this.$route.query,
              backStatus:1,
              instruction_param: JSON.stringify(query)
            }})
          } else {
            this.showTaskNew();
          }
        }
      } catch (e) {
        console.log(e, 'eee')
      } finally {
        this.nextLoading = false;
      }
    },
    async changeViewsNew(val) {
      this.nextLoading = true
      if (this.schemeInfo.agent_scene_code === 'digital_twin_assistant_scene'){
        const res = await checkByDeviceId({scheme_id: this.$route.query.id})
        console.log('res checkByDeviceId', res);
        if (res.status === 200 && res.data.code === 200){
          // if (!this.developFlag)
          window.DEVICE_TYPE = res.data.result.model_code
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '设备未绑定成功'
          });
        }
      }
      try {
        if (this.taskAllComplete) {
          console.log('this.taskAllComplete', this.taskAllComplete);
          this.$emit('updateStep', val);
        } else {
          console.log("this.MyAbilityId 00", this.MyAbilityId);
          let isFirst = true
          try{
            isFirst = this.flowData[0].status == 0
          } catch{

          }
          if (isFirst) {
            // this.$emit('handleReStart', 'startrun');
            let query = this.getInstructionParam()
            this.$router.push({
            name:'planGenerateRunTask',
            query:{
              ...this.$route.query,
              backStatus:1,
              instruction_param: JSON.stringify(query)
            }})
          } else {
            this.showTaskNew();
          }
        }
      } catch (e) {
        console.log(e, 'eee')
      } finally {
        this.nextLoading = false;
      }
    },
    handleComplete() {
      this.$router.push({
        path: '/planGenerate/index',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      })
      if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
      })
    },
    async queryCacheHandle() {
      queryCache({ scheme_id: this.schimeId, ability_name: 'decision_tree_generate' }).then(
        async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result) {
              this.isCache = res.data.result.isCache
              this.isCacheDisabled = false
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        }
      )
    },

    // 知识保存接口
    saveKnowladge() {
      SchemeSaveKnow({
        scheme_id: this.$route.query.id,
        scheme_name: this.schemeInfo.name,
        scheme_content: this.detailContent.text
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$message({
            type: 'success',
            message: '知识保存成功！'
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showSikao()
      } else if (command === 'youhua') {
        this.showYouhua()
      } else if (command === 'zhishi') {
        // this.saveKnowladge()
      } else {
        this.copyText()
      }
    },
    showYouhua() {
      this.youhuaVisable = true
    },
    handleSchemeCommand(command) {
      if (command === 'sikao') {
        this.showSikao()
      } else if (command === 'zhishi') {
        this.saveKnowladge()
      } else if (command === 'history') {
        this.showHistory()
      } else {
        navigator.clipboard
          .writeText(this.detailContent.text)
          .then(() => {
            this.$message({
              type: 'success',
              message: '复制成功！'
            })
          })
          .catch((error) => {
            this.$message({
              type: 'error',
              message: '复制失败！'
            })
          })
      }
    },
    // handleCommand(command) {
    //   if (command === 'sikao') {
    //     this.showTreeSikao();
    //   } else if (command === 'addCache') {
    //     addCache({ scheme_id: this.$route.query.id, ability_name: 'decision_tree_generate' }).then(
    //       async (res) => {
    //         if (res.status === 200 && res.data.code === 200) {
    //           this.$message({
    //             type: 'success',
    //             message: res.data?.result || '新增成功'
    //           });
    //           this.isCache = !this.isCache;
    //         } else {
    //           this.$message({
    //             type: 'error',
    //             message: res.data?.msg || '接口异常!'
    //           });
    //         }
    //       }
    //     );
    //   } else if (command === 'removeCache') {
    //     removeCache({ scheme_id: this.$route.query.id, ability_name: name }).then(async (res) => {
    //       if (res.status === 200 && res.data.code === 200) {
    //         this.$message({
    //           type: 'success',
    //           message: res.data?.result || '删除成功'
    //         });
    //         this.isCache = !this.isCache;
    //       } else {
    //         this.$message({
    //           type: 'error',
    //           message: res.data?.msg || '接口异常!'
    //         });
    //       }
    //     });
    //   } else {
    //     this.copyText();
    //   }
    // },
    async historyDataFn() {
      await this.queryPlanDetail()
    },
    copyText() {
      // 获取需要复制的文本
      const text = this.treeData
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功！'
          })
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          })
        })
    },
    copyTextNewEnn(){
      const currentTab = this.optHeader2Tab; // 当前 Tab 名称
      const currentComponent = this.$refs[`tab-${currentTab}`][0]; // 获取动态组件实例
      this.copyTextNew(currentComponent.executeResultData)
    },
    copyTextNew(text) {
      // 获取需要复制的文本
      console.log(text,'text')
      if (!text) {
        this.$message({
          type: 'warning',
          message: '复制的内容不能为空！'
        })
        return
      }

      try {
        if (navigator.clipboard && window.isSecureContext) {
          // 使用新的 Clipboard API
          navigator.clipboard.writeText(text)
            .then(() => {
              this.$message({
                type: 'success',
                message: '复制成功！'
              })
            })
            .catch(() => {
              this.fallbackCopyText(text)
            })
        } else {
          // 使用传统方法
          this.fallbackCopyText(text)
        }
      } catch (error) {
        console.error('复制失败:', error)
        this.$message({
          type: 'error',
          message: '复制失败！'
        })
      }
    },

    fallbackCopyText(text) {
      // 创建临时文本区域
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-9999px'
      textArea.style.top = '0'
      document.body.appendChild(textArea)

      try {
        textArea.select()
        document.execCommand('copy')
        this.$message({
          type: 'success',
          message: '复制成功！'
        })
      } catch (error) {
        console.error('复制失败:', error)
        this.$message({
          type: 'error',
          message: '复制失败！'
        })
      } finally {
        document.body.removeChild(textArea)
      }
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    // 显示历史记录
    showHistory() {
      this.historyVisable = true
    },
    closeHistory() {
      this.historyVisable = false
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示')
      this.processVisable = true
    },
    showTreeSikao() {
      console.log('思维树生成过程显示')
      this.processTreeVisable = true
    },
    closeSikaoRizhi() {
      this.processVisable = false
      this.processTreeVisable = false
    },
    handleClose() {
      this.dialogTableVisible = false
      this.saveLoading = false
      clearInterval(this.timer)
      this.timer = null
    },
    handleDone() {
      console.log(this.gridData, '222')
      updateAbilityMapping({
        scheme_id: this.$route.query.id,
        config: {
          header: this.dataThs,
          data: this.gridData
        },
        ability_status: 'finished'
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          this.$message({
            type: 'success',
            message: '更新完成!'
          })

          this.dialogTableVisible = false
          this.saveLoading = false
        } else {
          this.$message({
            type: 'success',
            message: '更新失败!'
          })
          this.dialogTableVisible = false
          this.saveLoading = false
        }
      })
    },
    async handleAbility() {
      queryAbilityList({ scheme_id: this.$route.query.id })
        .then((res) => {
          console.log('this.abilityList 000000', res)

          if (res.data) {
            this.abilityList = res.data || []
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
      console.log('this.abilityList 111111111', this.abilityList)
    },
    handleAbilityMapping() {
      this.saveLoading = true
      if (this.timer != null) {
        return
      }
      this.timer = setInterval(() => {
        queryAbilityMapping({ scheme_id: this.$route.query.id })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.dataStatus = res.data.result.ability_status
              const status = res.data.result.ability_status
              let configData = {}
              try {
                configData = JSON.parse(res.data.result?.config) || {}
              } catch (error) {
                configData = res.data.result?.config || {}
              }
              if (status !== 'generating') {
                this.saveLoading = false
                clearInterval(this.timer)
                this.timer = null
              }
              console.log(configData, '111')
              this.dataThs = configData.header || {}
              this.dataThDatas = Object.keys(configData.header || {}).map((item) => {
                return { name: configData.header[item], field: item }
              })
              this.gridData = configData.data || []
              //   this.gridData = configData.map((item)=>{
              //   return {
              //     param_name: item.param_name,
              //     dataset: item.dataset,
              //     param_key: item.param_key,
              //     test_point:item.test_point,
              //     frequency: item.frequency,
              //     calculation_formula: item.calculation_formula,
              //   }
              // })
              this.dialogTableVisible = true
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          })
          .catch((_err) => {
            clearInterval(this.timer)
            this.timer = null
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            })
          })
          .finally(() => { })
      }, 1000)
    },
    // 关闭数据配置
    shujuCompete() {
      this.dialogTableVisible = false
      if (
        this.treeData &&
        (this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1)
      ) {
      } else {
        if (this.jueceYulanFlag) {
          this.thinkingHandle()
        }
      }
    },

    // 思维图渲染模式切换
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
    },
    // 渲染思维图
    thinkingHandle() {
      if (this.treeData) {
        if (
          this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1
        ) {
        } else {
          const transformer = new Transformer()
          const { root } = transformer.transform(this.treeData)
          this.$nextTick(() => {
            Markmap.create('#markmap', null, root)
          })
        }
      }
    },

    regenerate() {
      this.treeData = ''
      this.treeProcess = ''
      this.taskLoading = true
      this.$emit('e-generalExec', DECISION_TREE)
      if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
      })
    },
    queryDecision() {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: DECISION_TREE
      }
      GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$emit('handleUpdateTreeData', res.data.result?.decision_making_content || '')
          this.treeData = res.data.result?.decision_making_content || ''
          this.treeDataProcess = res.data.result?.sub_content || ''
          // this.optionDataProcess = res.data.result?.sub_content
          if (this.treeData) {
            // 思维图
            this.thinkingHandle()
          } else {
            const nodeMarkmap = document.getElementById('markmap')
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = ''
            }
          }
          this.queryCacheHandle()
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    queryAbilityId() {
      const params = {
        // instance_id: this.$route.query.id,
        instance_id: 797,
        ability_type: 'knowledge_base_search'
      }
      getknowledgeRetrievalCapabilityId(params).then((res) => {
        if (res.status === 200) {
          this.knowledgeId = res.data.id
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    async getKonwledgeSourceFn(goal) {
      const random = Math.floor(Math.random() * 10000)
      const params = {
        scene_instance_id: this.$route.query.id,
        agent_template_id: this.knowledgeId,
        name: this.schemeInfo.name + '_知识检索_' + new Date().getTime() + random,
        goal,
        scheme_detail: this.detailContent.text
      }
      await getKonwledgeSource(params).then((res) => {
        if (res.status === 200) {
          this.sourceList = res.data
          saveSimpleSchemeGenerate({
            session_id: this.sessionId,
            messages: goal,
            knowledge_base_result: res.data
          }).then((result) => {
            if (result.status === 200 && result.data.status === 'success') {
              getAssociationGeneration({ session_id: this.sessionId }).then((re) => {
                if (re.status === 200) {
                  this.relevanceList = re.data.result
                }
              })
            }
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    //这里怎么用的全是老的方法。感觉不对。改一下吧
    async handleDetailSave() {
      console.log('保存了吗？')
      const { id, ...rest } = this.detailContent
      const res = await PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id })
      if (res?.data?.code !== 200) {
        console.log('保存了吗2？')
        this.$message.error(res?.data?.msg || '保存失败')
        return
      }
      this.$message.success('保存成功')
      this.isEdit = false
      const content = document.getElementById('detail-content')
      content.removeEventListener('mousedown', (e) => { })
      content.removeEventListener('mouseup', (e) => { })
      this.insertWriteFlag = true
      this.replaceWriteFlag = true
      this.$emit('executeQueryTask')
      // this.queryTask()
      this.queryPlanDetail()
      // 编辑成功自动生成思维树
      // this.regenerate()
      // 编辑成功自动更新任务
      if (this.isExpert) {
        this.$emit('initFlowData', 0)
      }
    },
    handleUpdateContent(val) {
      this.detailContent.text = val
      console.log(3333)
    },
    closeCaiNaFun(){
      this.isEdit = false
      this.optHeader2TabsList.forEach(item =>{
        if(item.name == '方案详情key'){
          this.detailContent.text = item.execute_result_new
        }
        if(item.name == '任务key'){
         tem.execute_result = item.execute_result
        }
        if(item.name == this.optHeader2Tab){
          item.execute_result = item.execute_result_new
        }
      })
      this.$forceUpdate()
    },
    handleDetailSaveClose() {
      this.isEdit = false
      this.insertWriteFlag = true
      this.replaceWriteFlag = true
      this.detailContent.text = this.hisDetail
      const content = document.getElementById('detail-content')
      content.removeEventListener('mousedown', (e) => { })
      content.removeEventListener('mouseup', (e) => { })

      if (this.dagangFlag) {
        const nodeMarkmap = document.getElementById('markmap')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
        const transformer = new Transformer()
        const { root } = transformer.transform(this.hisDetail)
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root)
        })
      } else {
        this.optHeader2TabsList.forEach(item =>{
        if(item.name == '方案详情key'){
          this.detailContent.text = item.execute_result
        }
        // if(item.name == this.optHeader2Tab){
        //   item.execute_result = item.execute_result
        // }
      })
        this.$forceUpdate()
        // this.$refs.MyEditor.$refs.editorFin.text = this.hisDetail
      }
    },
    handleDetailSaveCloseNew() {
      console.log(3333)
      this.isEdit = false
      this.insertWriteFlag = true
      this.replaceWriteFlag = true
      const content = document.getElementById('detail-content')
      content.removeEventListener('mousedown', (e) => { })
      content.removeEventListener('mouseup', (e) => { })
      console.log(22222)
      if (this.dagangFlag) {
        const nodeMarkmap = document.getElementById('markmap')
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = ''
        }
        const transformer = new Transformer()
        const { root } = transformer.transform(this.hisDetail)
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root)
        })
      } else {
        console.log(11111)
        this.optHeader2TabsList.forEach(item =>{
        if(item.name == '方案详情key'){
          this.detailContent.text = item.execute_result
        }
      })
      this.$forceUpdate()
        // this.$refs.JujiaoComRef.$refs.MyEditorRef.$refs.editorFin.text = this.hisDetail
      }
    },
    goToDetail(task) {
      task.adjust_url && window.open(task.adjust_url)
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth <= this.minWidth ? this.minWidth : leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = (widthLeft <= this.minWidth ? this.minWidth : widthLeft) + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
        console.log('111111111111---222',this.leftWidth,this.rightWidth)
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    startTopDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startY = event.clientY
        const topHeight = document.getElementById('top-content').getBoundingClientRect().height
        this.startHeight = topHeight
        document.addEventListener('mousemove', this.onTopDrag)
        document.addEventListener('mouseup', this.stopTopDrag)
      }
    },
    onTopDrag(event) {
      if (this.isDragging) {
        const deltaY = event.clientY - this.startY
        const topHeight = this.startHeight + deltaY
        this.topHeight = topHeight + 'px'
        this.bottomHeight = this.totalHeight - topHeight - 30 + 'px'
      }
    },
    stopTopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onTopDrag)
      document.removeEventListener('mouseup', this.stopTopDrag)
    },
    getWsID() {
      let workspaceId = ''
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?')
          const params = Object.fromEntries(new URLSearchParams(query))
          workspaceId = params.workspaceId
        } catch (error) {
          console.log('error', error)
        }
      }
      return workspaceId
    },
    changeShowTopRight() {
      this.planDetailShow = !this.planDetailShow
      this.planDetailTopShow = !this.planDetailTopShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = 'calc( (100vw - 364px) *0.4)'
        this.topHeight = '50%'
        this.bottomHeight = '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
        this.topHeight = ''
        this.bottomHeight = '0px'
      }
    },
    changeShowBottomRight() {
      this.planDetailShow = !this.planDetailShow
      this.planDetailBottomShow = !this.planDetailBottomShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = 'calc( (100vw - 364px) *0.4)'
        this.topHeight = '50%'
        this.bottomHeight = '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
        this.topHeight = '0px'
        this.bottomHeight = ''
      }
    },
    // 改变思维树容器的大小
    handleChangeThinkingTreeSize() {
      this.isMaximize = !this.isMaximize
      if (this.isMaximize) {
        this.topHeight = 'calc( 100% - 47px)'
        this.bottomHeight = '47px'
      } else {
        this.topHeight = '50%'
        this.bottomHeight = '50%'
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = 'calc( (100vw - 364px) *0.4)'
        this.rightWidth = '100%'
      }
    },
    // 显示思考过程
    saveFangan() {
      console.log('修改的值', this.contentEditor.getValue())
      if (this.hasChatingName === '') {
        this.detailContent.text = this.contentEditor.getValue()
        const { id, ...rest } = this.detailContent
        PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id })
      }
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.schimeId }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result
          console.log("this.detailContent 1111",this.detailContent);
          try {
            await this.getAbilitiesAndResult()
          } catch (error) {
            console.error('getAbilitiesAndResult', error);
          }
          this.$emit('handleUpdateScheme', this.detailContent.text)
          this.$emit('handlePublishAbility', this.detailContent.publish_ability)
          this.optionDataProcess = this.detailContent.sub_content
          this.dataProcess[this.optHeader2Tab] = this.optionDataProcess
          if (res.data.result.ability_id) {
            this.MyAbilityId = res.data.result.ability_id
            console.log("this.MyAbilityId 01", this.MyAbilityId);
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.toMessage.image_path = ''
          this.toMessage.image_key = ''
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    startWriteOpt() {
      const content = document.getElementById('detail-content')
      content.addEventListener('mouseup', () => {
        const selection = window.getSelection()
        console.log('selection', selection)
        if (selection.toString() !== '') {
          // 用户选中了文本，执行相应的动作
          console.log('用户选中了文本：' + selection.toString())
          this.writeText = selection.toString()
          const range = selection.getRangeAt(0)
          this.range = range
          this.writeFlag = false
        } else {
          this.writeText = ''
          this.writeFlag = true
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// .uploaded-image-container {
//   position: relative;
//   display: block;

//   .uploaded-image {
//     width: 40px;
//     height: 40px;
//     border-radius: 4px;
//     object-fit: cover;
//     cursor: pointer;
//   }

//   .delete-image-btn {
//     position: absolute;
//     top: -5px;
//     right: -5px;
//     background-color: rgba(255, 255, 255, 0.8);
//     color: red;
//     font-size: 12px;
//     display: none;
//     z-index: 1;
//     transition: opacity 0.3s;
//     opacity: 0;
//     border: none;
//     padding: 2px;
//     cursor: pointer;

//     &:hover {
//       background-color: rgba(255, 255, 255, 1);
//       /* 或者选择其他合适的颜色 */
//     }
//   }

//   &:hover {
//     .delete-image-btn {
//       display: block;
//       opacity: 1;
//     }
//   }
// }
// 86 + 48 = 134
.chatContainerTest {
  flex: 1;
  height: 100%;
  overflow: hidden;
  max-height: calc(100vh - 88px);
  .containerBox {
    display: flex;
    flex-direction: row;
    height: 100%;
    max-height: 100%;
    overflow-y: hidden;
    position: relative;
    width: 100%;

    &.containerBoxBig {
      height: calc(100vh - 147px) !important;
      max-height: calc(100vh - 147px) !important;
    }

    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;

      &:hover {
        background: #3455ad;
      }

      &:active {
        background: #264480;
      }

      img {
        width: 12px;
        height: auto;
      }
    }

    .phoneContent {
      overflow-y: hidden;
      overflow-x: hidden;
      background-image: url('@/assets/images/planGenerater/phoneBg.png');
      background-size: cover;
      margin: 16px 0px 0px 16px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      width: 30%;
      display: flex;
      flex-direction: column;
      position: relative;

      .phoneTitle {
        display: flex;
        width: 100%;
        justify-content: center;
        font-weight: 500;
        font-size: 16px;
        color: #323233;
        line-height: 24px;
        margin-top: 16px;

        span {
          width: 80%;
          text-align: center;
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
          overflow: hidden;
        }
      }

      .phonePhoto {
        display: flex;
        justify-content: center;
        align-content: center;
        margin-top: 25%;
        position: relative;

        .phonePh {
          width: 100px;
          height: 100px;
          box-shadow: 0 0 0 4px white;
          border-radius: 50%;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .bubble {
          width: 80px;
          height: 80px;
          position: absolute;
          top: -50px;
          right: 50px;
          background-image: url('@/assets/images/planGenerater/bubble.png');
          background-size: contain;
          background-repeat: no-repeat;

          img {
            position: absolute;
            left: 25px;
            top: 15px;
            width: 50%;
            height: 50%;
          }
        }
      }

      .phoneStatus {
        margin-top: 35%;
        display: flex;
        justify-content: center;
        cursor: pointer;
      }

      .phoneClose {
        position: absolute;
        left: 50%;
        bottom: 5%;
        transform: translate(-50%, 0%);
        width: 150px;
        height: 100px;
        display: flex;
        justify-content: space-between;
      }

      .phoneStop {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #4775f3;
        border-radius: 50%;

        &.phoneStopDisabled {
          opacity: 0.5;
          cursor: not-allowed;
          // pointer-events: none; /* 阻止事件 */
        }

        img {
          width: 100%;
          height: 100%;
        }
      }

      .phoneClone {
        width: 48px;
        height: 48px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f7622c;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .containerCard {
      //height: calc(100% - 18px);
      // max-height: calc(100vh - 160px);
      overflow-y: hidden;
      overflow-x: hidden;
      // margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: #fff;
      // margin-left: 16px;
      height: 100%;
      max-height: 100%;

      &.containerCardFull {
        position: fixed !important;
        // top: 32px;
        z-index: 2005;
        height: calc(100% - 48px);
        max-height: calc(100% - 48px);
        width: 100%;
        left: 0px;
        top: 48px;
        width: 100%;
        margin-left: 0px !important;

      }

    }

    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      height: 100%;
      max-height: 100%;
      overflow-y: hidden;
      // margin-top: 16px;
      // margin-right: 16px;
      position: relative;

      &.chatRightFull {
        position: fixed;
        top: 48px;
        z-index: 2005;
        height: calc(100% - 48px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }

        .optContentBox {
          height: calc(100vh - 180px) !important;
          max-height: calc(100vh - 180px) !important;
        }
      }

      .optContentBox {
        //height: calc(100% - 340px);
        // max-height: calc(100vh - 340px);
        // max-height: 100%;
        // height: 100%;
        overflow-y: auto;
        width: 100%;
        position: relative;
        background: transparent !important;
      }

      .h-full {
        max-height: 100%;
        height: 100%;
      }

      .h-full-340 {
        height: calc(100% - 340px);
      }

      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;


        .rightTitle {
          font-weight: 500;
          font-size: 16px;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
          cursor: pointer;
        }

        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
         &-lf {
          margin-right: 12px;
          line-height: 30px;
         }

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;

            &:hover {
              background: #ebecf0;
            }

            &:active {
              background: #dcdde0;
            }

            &.rightBtnDisabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
              /* 阻止事件 */
            }

            &.rightBtnBlue {
              background-color: #406bd4;

              &:hover {
                background: #3455ad;
              }

              &:active {
                background: #264480;
              }
            }

            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }

      .optHeader2 {
        padding: 0px 20px;
        // border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .el-tabs {
            flex: 1;
            /* 可缩放，但优先考虑剩余空间 */
            // max-width: calc(100% - 115px);
            height: 40px;
            max-width: 100%;
            /* 动态减少宽度，预留按钮区域 */
            /* Tabs 占据剩余宽度 */
            overflow-x: auto;
            white-space: nowrap;

            /* 强制单行显示，支持滚动 */
            .el-tabs__header {
              display: flex;
              align-items: center;
            }
          }

          .rightTitleOpt-tab {
            // min-width: 110px;
              /* 设置最小宽度 */
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-shrink: 0;
            /* 防止按钮被压缩 */
            // margin-left: 16px;
            &:empty {
                margin-left: 0;
                /* 当没有内容时移除 margin */
              }

            .item {
              margin-left: 8px;
            }

            img {
              width: 16px;
              height: auto;
            }
          }

        :deep(.el-tabs__header){
          margin: 0px;
        }
        :deep(.el-tabs__nav-wrap::after) {
          //不要下面那条杠
          content: none;
          /* 移除伪元素内容 */
          background: none;
          /* 移除背景 */
        }

        .tabTitle {
          font-weight: 500;
          font-size: 14px;
          // font-size: 14px;
          // font-weight: bold;
          // color: #323233;
          // line-height: 22px;
          // padding: 12px 0px;
        }
      }
      .optEdit {
       padding: 0px !important;
      }
      .optScroll {
        position: relative;
        overflow-y: hidden;
        overflow-x: hidden;
        padding: 20px;
        padding-bottom: 40px;
        display: flex;
        height: calc(100% - 40px);

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        :deep(.scrollbar__bar.is-vertical) {
          top: 2px;
          width: 4px;
          /* width: 6px; */
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optScroll2 {
        position: relative;
        height: calc(100vh - 210px);
        max-height: calc(100vh - 210px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .optContent {
        // height: calc(100% - 54px);
        // max-height: calc(100% - 54px);
        height: 100%;
        max-height: 100%;
        overflow-y: hidden;
        display: flex;
        flex-direction: column;
      }

      .ToolButton{
      position: absolute;
      bottom: 0px;
      left: 0px;
      background: #ffffff;
      display: flex;
      align-items: center;
      padding: 12px 20px;
      min-height: 54px;

      }

      .optContent2 {
        max-height: calc(100% - 0px) !important;
        overflow-y: hidden;
      }
    }
  }

  &.chatContainerTestFrame {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #f4f5f9;
    display: flex;
    max-height: 100vh !important;
    width: 100%;
    .EmbedModeWrap {
        position: fixed;
        bottom: 27px;
        right: 27px;
      }
    .containerBox{
      height: calc(100% - 16px);
      max-height: calc(100% - 16px);
    }

    .containerCard.containerCardFull {
      top: 0;
      height: 100%;
      max-height: 100%;
      margin-top: 0;
    }

    .chatRight.chatRightFull{
      height: 100%;
      max-height: 100%;
      top: 0;
      margin-top: 0;
    }

    .optFooter{
      display: none;
    }
  }
}

.headerScroll{
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: white;
  z-index: 999;
  padding-bottom: 0px !important;
}



.cardEmpty {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.topContent {
  height: calc(100% - 47px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .el-empty {
    height: 100%;
    display: flex;
    padding: 10px 0;

    :deep(.el-empty__image) {
      height: 50%;
      margin-top: -15%;
    }

    :deep(.el-empty__description) {
      margin-top: 5%;
      height: 10%;
    }

    :deep(.el-empty__bottom) {
      height: 10%;
      margin-top: 10%;
    }
  }
}

.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;

    .containerBox {
      height: calc(100vh - 260px) !important;
      max-height: calc(100vh - 260px) !important;
    }

    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }

    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }

    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
  }

  display: flex;
  flex-direction: row;
  height: calc(100vh - 205px);
  overflow-y: hidden;
  position: relative;

  .containerCard {
    //height: calc(100% - 18px);
    // max-height: calc(100vh - 210px);
    overflow-y: hidden;
    overflow-x: hidden;
    margin: 16px 16px 0px 0px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: #fff;
    margin-left: 16px;
    display: flex;
    flex-direction: column;

    &.containerCardFull {
      position: fixed !important;
      top: 32px;
      z-index: 2005;
      height: calc(100% - 50px);
      max-height: calc(100% - 50px);
      width: 100%;
      left: 0px;
      width: 100%;
      margin-left: 0px !important;

      .chatScroll {
        max-height: calc(100vh - 220px) !important;
      }

      .optScroll {
        height: calc(100vh - 190px) !important;
        max-height: calc(100vh - 190px) !important;
      }

      .optContentBox {
        height: calc(100vh - 210px) !important;
        max-height: calc(100vh - 210px) !important;
      }
    }

    .optContentBox {
      // height: calc(100vh - 340px);
      max-height: 100%;
      width: 100%;
      position: relative;
      overflow-y: auto;
    }

    .container-list {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 20px;
      max-height: calc(100vh - 275px);
      flex: 1;

      .list-item {
        display: flex;
        flex-direction: column;
        margin: 0px 20px;

        .list-title {
          display: flex;
          color: #4068d4;
          font-size: 14px;
          line-height: 22px;
          margin-bottom: 12px;

          img {
            width: 18px;
            height: 18px;
            margin-top: 2px;
          }
        }

        .list-source {
          display: flex;
          flex-wrap: wrap;
          height: calc(100% - 22px);
          overflow: auto;

          .source-item {
            width: calc(33.33% - 10px);
            background: #f6f7fb;
            margin-bottom: 12px;
            margin-right: 10px;
            padding: 5px;

            &:last-child {
              margin-right: 0;
            }

            .source-tit {
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
              font-weight: 600;
              font-size: 14px;
              color: #323233;
            }

            .source-box {
              display: flex;
              justify-content: space-between;

              .source-sub {
                margin-right: 10px;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; // 默认不换行；
                font-size: 12px;
                line-height: 20px;
                color: #969799;
              }
            }
          }
        }

        .relevance-item {
          height: calc(100% - 30px);
          overflow: auto;

          .relevance-tit {
            width: 100%;
            background: #f6f7fb;
            margin-bottom: 12px;
            padding: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .optContent {
      position: relative;
      display: flex;
      flex-direction: column;
      flex: 1;

      .optScroll {
        position: relative;
        overflow-y: hidden;
        overflow-x: hidden;
        padding-bottom: 10px;
        display: flex;
        flex: 1;
        flex-direction: column;

        &.optScrollMini {
          max-height: calc(100vh - 450px);
        }

        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }

        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }

      .chatFooter {
        position: relative;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }


  }
}

.topResize {
  cursor: row-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  height: 10px;
  width: 100%;
  color: #c3cadd;
  display: flex;
  flex-direction: row;
  align-items: center;

  &:hover {
    background: #e0e6ff;

    .process-icon {
      color: #3455ad !important;
    }
  }

  .el-two-column__icon-top {
    width: 50%;
    height: 4px;
    display: flex;
    flex-direction: row-reverse;

    .el-two-column__icon-top-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(right, #d5dbed, #e6eafb) no-repeat;
    }
  }

  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;

    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }

  .el-two-column__trigger-icon_shu {
    width: 25px;
    height: 25px;
    color: #c3cadd;

    .process-icon {
      width: 25px;
      height: 10px;
      color: #c3cadd;
    }
  }

  .el-two-column__icon-bottom {
    width: 50%;
    height: 4px;

    .el-two-column__icon-bottom-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(left, #d5dbed, #e6eafb) no-repeat;
    }
  }
}

.resize {
  cursor: col-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  width: 10px;
  color: #c3cadd;
  display: flex;
  flex-direction: column;
  align-items: center;

  &:hover {
    background: #e0e6ff;

    .process-icon {
      color: #3455ad !important;
    }
  }

  .el-two-column__icon-top {
    height: 50%;
    width: 4px;
    display: flex;
    flex-direction: column-reverse;

    .el-two-column__icon-top-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
    }
  }

  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;

    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }

  .el-two-column__icon-bottom {
    height: 50%;
    width: 4px;

    .el-two-column__icon-bottom-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
    }
  }
}

.optFooter {
  position: absolute;
  bottom: 7px;
  // right: 365px;
  // width: 100%;
  // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 20px;
  min-height: 54px;
}

::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 16px;
  border-radius: 2px;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;

  img {
    height: 16px;
    margin-top: -2px;
  }
}

::v-deep .el-button--primary.is-plain {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    font-size: 10px;
  }
}

.mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}

:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}

:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 20px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 定义 fade 过渡的 CSS 类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s ease, transform 1s ease;
  /* 增加持续时间 */
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.6);
  /* 添加缩放效果 */
}

.fade-enter-to,
.fade-leave {
  opacity: 1;
  transform: scale(1);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  /* 半透明白色背景 */
  z-index: 1000;
  /* 确保覆盖层位于较高层级 */
  cursor: not-allowed;
  /* 更改鼠标指针样式，提示用户不可交互 */
}
</style>
<style lang="scss">
@import 'github-markdown-css/github-markdown.css';
@import 'highlight.js/styles/stackoverflow-dark.css';

.chendianfangan {
  padding: 0px !important;
  bottom: 98px;
  top: inherit !important;

  &.chendianfanganHigh {
    bottom: 124px !important;
  }

  .guessPovper {
    // margin-bottom: 5px;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    max-width: 500px;
    min-width: 88px;
    padding: 5px;
    max-height: 150px;
    overflow-y: auto;

    //position: absolute;
    //bottom: 23px;
    //left: 0px;
    //z-index: 7;
    .guess-header {
      font-size: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      border-bottom: 1px solid #ebecf0;

      .title {
        color: #646566;
        font-size: 12px;
      }

      .huanyihuan {
        font-size: 12px;
        margin-left: 2px;
        color: #4068d4;
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }

      .plansearch {
        cursor: pointer;

        &:hover {
          color: #3455ad;
        }

        &:active {
          color: #264480;
        }
      }
    }

    .otherRole {
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 4px;
      }

      .mark-tag {
        background: #eff3ff;
        border-radius: 2px;
        padding: 0px 8px;
        color: #4068d4 !important;
        line-height: 17px;
        font-size: 12px;
        margin-left: 4px;
        display: inline-block;
        max-width: 260px;
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
      }
    }

    li {
      cursor: pointer;

      span {
        display: block;
        line-height: 32px;
        padding: 0 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
      }

      &:hover {
        background: #eff3ff;
      }

      &:active {
        background: #eff3ff;
      }
    }
  }
}

.others {
  display: flex;
  gap: 20px;

  .o-l {
    flex: 5 0 auto;
    min-width: 0;

    .lt {
      font-weight: 800;
      margin-bottom: 12px;
    }

    .k-list {
      display: flex;
      border: 1px solid #ccc;
      padding: 15px 16px 22px;

      .k-box {
        width: 110px;
        display: flex;
        align-items: center;
        flex-direction: column;

        img {
          width: 80px;
        }

        .kt {
          text-align: center;
          white-space: wrap;
          line-height: 20px;
        }
      }
    }
  }

  .o-r {
    flex: 2 0 auto;
    min-width: 0;

    .rt {
      font-weight: 800;
      margin-bottom: 12px;
    }

    .q-list {
      flex: 2 0 auto;
      min-width: 0;
      border: 1px solid #ccc;
      padding: 8px 16px;

      .q-box {
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eee;

        &:last-of-type {
          border-bottom: none;
        }
      }
    }
  }
}


// .dialog-dropdown-menu{
//   padding: 0px;
//   .el-dropdown-menu__item{
//     border-radius: 2px;
//     border: 1px solid rgba(64, 107, 212, 0.4);
//     padding: 2px 8px;
//     font-weight: 500;
//     color: #4068d4;
//     line-height: 18px;
//     word-break: keep-all;
//     height: 24px;
//     margin-left: 0px;
//     font-size: 12px !important;
//   }
// }
</style>
<style lang="postcss" scoped>
.center_flex {
  :deep(.el-tabs__nav) {
    display: flex;
    justify-content: center;
  }
}
.ziliaoNew {
  /* margin-right: 23px; */
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  transition: color 0.3s ease, font-weight 0.3s ease; /* 添加过渡效果 */
}
.ziliaoNew:hover {
  color: #0056b3; /* 悬停时改变颜色 */
}
.activeZl {
  color: #4068D4 !important;
  font-weight: bold !important;
}
.newClass {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* padding-bottom: 30px; */
}
.modern-button-container {
  display: flex;
  gap: 10px;
  align-items: center;
}
.modern-button {
  color: #007BFF;
  border: none;
  border-radius: 20px; /* 调整按钮圆角 */
  padding: 0px 16px; /* 调整按钮高度和内边距 */
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease, border-radius 0.3s ease; /* 添加过渡效果 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.modern-button:hover {
  background-color: #cce5ff; /* 悬停时的颜色 */
}
.modern-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
}
.modern-button.active {
  background-color: #007BFF; /* 选中时的深蓝色 */
  color: white;
}
.modern-button-secondary {
  background-color: #e6f9e6; /* 未选中时的浅绿色 */
  color: #28a745;
  border: none;
  border-radius: 12px; /* 调整按钮圆角 */
  padding: 8px 16px; /* 调整按钮高度和内边距 */
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease, border-radius 0.3s ease; /* 添加过渡效果 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.modern-button-secondary:hover {
  background-color: #d4edda; /* 悬停时的颜色 */
}
.modern-button-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.5);
}
.modern-button-secondary.active {
  background-color: #28a745; /* 选中时的深绿色 */
  color: white;
}
.new_flex{
  width: 132px;
  height: 32px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #323233;
  line-height: 32px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  /* margin-left: 8px; */
  display: inline-block;
  /* 设置固定的宽度，根据实际情况调整 */
  /* 强制内容不换行 */
  white-space: nowrap;
  /* 超出部分隐藏 */
  overflow: hidden;
  /* 文字超出宽度时显示省略号 */
  text-overflow: ellipsis;
}
.new_flex:hover{
  color:#3455AD!important;
}
.icon_plus{
  font-size: 20px;
  border-radius: 2px;
  border: 1px solid #DCDDE0;
  color: #4068D4;
  margin-right: 4px;
}
.chuangzuo{
  margin-left: 30px;
  margin-bottom: 9px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 29px;
  width: 106px;
  border-radius: 2px;
  border: 1px solid rgba(64,107,212,0.4);
  cursor: pointer;
  i{
    margin-left: 12px;
    color: #4068D4;
  }
  div{
    margin-left: 4px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #4068D4;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
.flex_chuangzuo{
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  height: 31px;
  background: #EFF3FF;
  border-radius: 6px;
  /* border: 1px solid; */
  /* border-image: linear-gradient(135deg, rgba(172, 0, 255, 1), rgba(64, 104, 212, 1), rgba(0, 178, 255, 1)) 1 1; */
}
.cursor{
  cursor: pointer;
}
.image-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  height: 24px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #DCDDE0;
}




.leftTitleOpt{
  max-width: 333px;
  overflow-x: auto;

}
.modern-button{
  min-width: 104px;
  white-space: normal;
  word-break: break-all;
}
.tag-item{
  min-width: 80px;
  white-space: normal;
  word-break: break-all;
}
.tag-name{
  min-width: 60px;
  white-space: normal;
  word-break: break-all;
}
.selectRole2{
  left: 154px!important;
  bottom: 170px!important;
}
.chuangzuo:hover {
  background-color: #f5f5f5; /* 悬浮时的背景颜色 */
  cursor: pointer; /* 悬浮时的鼠标样式 */
}
.new_flex{
  display: flex;
  width: 100%;
  height: 100%;
}
.new_flex_width{
  flex: 1;
}
.right_gongju{
  width: 364px;
  /* padding-top: 16px; */
  /* padding-bottom: 2px; */
  /* border-radius: 4px; */
  position: relative;
  border-left: 1px solid #dcdfe6;
}
.op_i{
  position: fixed;
  top: 8px;
  font-size: 24px;
  right: 74px;
}
.op_i2{
  position: fixed;
  top: 8px;
  font-size: 24px;
  right: 104px;
  transform: rotate(180deg);
}
.left{
  /* left: -20px; */
}
.enn-markdown-div{
  position:absolute;
  z-index: 1100;
  text-align: center;
  background: #fff;
  box-shadow:0 12px 24px -16px rgba(54,54,73,.04),0 12px 40px 0 rgba(51,51,71,.08),0 0 1px 0 rgba(44,44,54,.02);
  bottom: 54px;
  display: flex;
  gap: 4px;
  height: 40px;
  justify-content: space-between;
  left: 50%;
  padding:  16px;
  border: 1px solid #e8eaf2;
  border-radius: 16px;
  align-items: center;
  transform: translateX(-50%);
}
.tag-selected {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.el-dropdown-menu__item.tag-selected {
  color: #409eff !important;
  background-color: #ecf5ff !important;
}
:deep(.rightTitleOpt-tab .tag-item){
  min-width: auto!important;
}
:deep(.leftTitleOpt .el-tabs__nav-wrap::after){
  background-color:#fff;
}
.center_flex{
  :deep(.el-tabs--top){
    line-height: 40px;
  }
  :deep(.el-tabs__item){
    border-radius: 16px;
    background: #F6F7FB;
    height: 30px;
    line-height: 30px;
    padding: 4px 12px;
    font-weight: 400;
    font-size: 14px;
    color: #323233;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    margin-right: 8px;
  }
  :deep(.is-active){
    background: #4068D4;
    .tabTitle{
      color: #FFFFFF;
    }
  }
  :deep(.el-tabs__active-bar){
    display: none;
  }
  :deep(.el-tabs__nav-prev){
    line-height: inherit;
  }
  :deep(.el-tabs__nav-next){
    line-height: inherit;
  }
  :deep(.el-tabs__header){
    width: -webkit-fill-available;
  }
}
.solid_class{
  padding:4px 8px;
  margin-left: 10px;
  background: #F2F3F5;
  border-radius: 2px;
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
    i{
      color:#4068D4;
    }
}
.optHeader{
 position: relative;
  :deep(.el-tabs__header){
    margin: 0 8px 7px!important;
  }
  .optFooter {
   position: absolute;
   bottom: 0px;
   left: -50px;
   width: 100%;
   display: flex;
   justify-content: flex-end;
   align-items: center;
   padding: 6px 20px;
   min-height: 48px;
 }
}
.chuanzuozhe{
  width: 30px;
  height: 30px;
  background: #F2F3F5;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}
#left-content{
  background: url('@/assets/images/planGenerater/bgChat.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0px 0px!important;
  /* background: #E7EEFF; */
  /* background: linear-gradient(90deg, rgb(231, 238, 255) 0%, rgb(227, 236, 254) 13%, rgb(227, 236, 254) 26%, rgb(229, 239, 254) 39%, rgb(222, 237, 253) 50%, rgb(223, 238, 252) 63%, rgb(225, 239, 255) 74%, rgb(228, 240, 255) 88%, rgb(239, 241, 255) 100%); */
}
</style>
<style lang="postcss">
.enn-markdown-div .el-button--info{
  background:none!important;
  border: none!important;
}
.center_flex{
  .el-tabs--top{
    display: flex;
    align-items: center;
  }
}
</style>
<style scoped>
.tab-switch {
  display: flex;
  border-radius: 8px;
  width: fit-content;
  position: absolute;
  left: 40px;
  top: 10px;
  z-index: 1;
}
.tab-btn {
  padding: 0px 12px;
  color: #4d7cff;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  background: transparent;
  transition: background 0.2s, color 0.2s;
}
.tab-btn.activetab {
  background: #fff;
  color: #4d7cff;
  box-shadow: 0 2px 2px #e5e9f2;
}
</style>
