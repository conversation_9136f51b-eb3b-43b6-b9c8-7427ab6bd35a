<template>
  <div v-if="showFlag" class="fanganyouhua">
    <div class="planSearch">
      <div class="headerTitle">
        <div class="title">方案优化</div>
        <div>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="loading || disableBtn || treeStatus === 3 || textarea === ''"
            @click="save"
            >确定</el-button
          >
          <el-button type="info" @click="onClose">返回</el-button>
        </div>
      </div>
      <div style="color: #646566; line-height: 22px; margin-bottom: 8px">优化能力：</div>
      <div class="plan-filter">
        <el-select v-model="abilityId" placeholder="请选择能力" style="width: 40%">
          <el-option v-for="item in tablesList" :key="item.id" :label="item.name" :value="item.id">
            <el-popover placement="right-end" title="" trigger="hover" :content="item.goal">
              <div slot="reference">{{ item.name }}</div>
            </el-popover>
          </el-option>
        </el-select>
        <el-button
          type="primary"
          style="margin-left: 16px"
          :loading="loading"
          :disabled="loading || disableBtn || treeStatus === 3 || tablesList.length === 0"
          @click="onGenerateSQL"
          >优化</el-button
        >
      </div>
    </div>
    <div class="planContent">
      <div class="planOpt">
        <div v-if="(!isEdit && treeStatus !== 3) || (!isEdit && textarea)">
          <el-tooltip class="item" effect="dark" content="编辑" placement="top">
            <el-button
              :disabled="loading || disableBtn || treeStatus === 3"
              type="info"
              size="mini"
              @click="
                () => {
                  hisDetail = textarea
                  isEdit = true
                }
              "
              ><img src="@/assets/images/planGenerater/bianji.png"
            /></el-button>
          </el-tooltip>
        </div>
        <template v-if="isEdit">
          <div>
            <el-tooltip class="item" effect="dark" content="保存" placement="top">
              <el-button type="info" size="mini" @click="handleDetailSave"
                ><img src="@/assets/images/planGenerater/baocun.png"
              /></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="取消" placement="top">
              <el-button type="info" size="mini" @click="handleDetailSaveClose"
                ><img src="@/assets/images/planGenerater/quxiao.png"
              /></el-button>
            </el-tooltip>
          </div>
        </template>
      </div>
      <div class="contentFlex">
        <div v-if="treeStatus === 3" style="margin-top: 20px">
          <el-result icon="error" title="方案优化失败！"></el-result>
        </div>
        <div v-else v-loading="loading" style="width: 100%; height: 100%" @mouseenter="fangda">
          <MyEditor
            id="MyFanganEditor"
            ref="MyFanganEditor"
            :md-content="textarea"
            :is-edit="isEdit"
            @updateContent="handleUpdateContent"
          ></MyEditor>
          <!-- <div id="vditorFangan" style="height: 100%;width:100%"></div> -->
          <!-- <template v-if="isEdit">
                        <el-input id="detail-content" v-model="textarea"  type="textarea" :autosize="{ minRows: 10}" placeholder="请输入"/>
                    </template>
                    <template v-else>
                        <pre v-if="(textarea?.indexOf('graph') > -1 || textarea?.indexOf('flowchart') > -1) && textarea?.indexOf('mermaid')<0 && textarea?.indexOf('```')<0"><div class="language-mermaid">{{textarea}}</div></pre>
                        <vue-markdown v-else :source="textarea" v-highlight class="markdown-body"></vue-markdown>
                    </template> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { PlanTaskEdit, queryAbilityList } from '@/api/planGenerateApi.js'
import panzoom from 'panzoom'
import MyEditor from './mdEditor.vue'

export default {
  name: 'ModelDialog',
  components: {
    MyEditor
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    sqlData: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    agentScene: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      abilityId: '',
      textarea: '',
      loading: false,
      showFlag: false,
      dataStatus: '',
      disableBtn: false,
      configData: {},
      tablesList: [],
      panZoomRef: null,
      isEdit: false,
      hisDetail: ''
    }
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
          if (this.agentScene) {
            this.handleAbility()
          }
          this.textarea = ''
          this.contentEditor = ''
          this.abilityId = ''
          this.disableBtn = false
          this.treeStatus = -1
          this.isEdit = false
          this.hisDetail = ''
          this.panZoomRef = null
          // 查询sql语句，todo
        } else {
          this.showFlag = false
        }
      },
      immediate: true
    },
    sqlData: {
      handler(val) {
        if (val) {
          this.textarea = val
        }
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        if (val === 2 || val === 3) {
          this.disableBtn = false
        }
      },
      immediate: true
    }
  },
  methods: {
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    async handleDetailSave() {
      this.isEdit = false
    },
    handleDetailSaveClose() {
      this.textarea = this.hisDetail
      this.isEdit = false
    },
    handleAbility() {
      if (this.agentScene) {
        this.loading = true
        queryAbilityList({ scheme_id: this.$route.query.id })
          .then((res) => {
            if (res.data) {
              this.tablesList = res.data || []
              if (res.data.length) {
                this.abilityId = res.data[0]?.id
              }
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            })
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    handleUpdateContent(val) {
      this.textarea = val
    },
    save() {
      if (this.textarea) {
        this.loading = true
        PlanTaskEdit({
          scheme_id: this.$route.query.id,
          text: this.textarea,
          file_url: '',
          sub_content: ''
        })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.$message({
                type: 'success',
                message: '保存完成!'
              })
              this.loading = false
              this.textarea = ''
              this.$emit('close')
            } else {
              this.$message({
                type: 'success',
                message: '保存失败!'
              })
              this.laoding = false
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            })
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        this.$message({
          type: 'error',
          message: '请优化方案后再保存！'
        })
      }
    },
    queryDataInfo() {
      console.log('d')
    },
    onClose() {
      if (!this.disableBtn) {
        this.textarea = ''
        this.$emit('close')
      } else {
        this.$message({
          type: 'warning',
          message: '请等待方案优化结束后再关闭'
        })
      }
    },
    onGenerateSQL() {
      this.disableBtn = true
      this.$emit('updateOptimize', this.abilityId)
    }
  }
}
</script>
<style lang="scss" scoped>
.fanganyouhua {
  position: fixed !important;
  top: 44px;
  z-index: 2005;
  height: calc(100vh - 44px) !important;
  max-height: calc(100vh - 44px) !important;
  width: 100%;
  left: 0px;
  margin-left: 0px !important;
  background: #f2f3f5;
  .planSearch {
    background-color: #fff;
    padding: 0px 20px 16px;
    .headerTitle {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
        padding: 14px 0px;
      }
    }
    .button-last {
      line-height: 14px;
    }
  }
  .plan-filter {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }
  .planContent {
    height: calc(100vh - 195px);
    max-height: calc(100vh - 195px);
    overflow: hidden;
    margin: 16px 16px 0px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .planOpt {
      padding: 8px 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border-bottom: 1px solid #ebecf0;
      height: 46px;
    }
    .contentFlex {
      max-height: calc(100% - 60px);
      overflow-y: auto;
      padding: 16px;
    }
  }
}
</style>
