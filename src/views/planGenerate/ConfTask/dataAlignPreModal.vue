<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="数据对齐确认"
      :visible.sync="showFlag"
      :close="onClose"
      append-to-body
      width="56%"
    >
      <div>
        <div class="headerTip">
          <svg
            t="1648437035368"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="5848"
            width="16"
            height="16"
          >
            <path
              d="M864 96a64 64 0 0 1 64 64v704a64 64 0 0 1-64 64H160a64 64 0 0 1-64-64V160a64 64 0 0 1 64-64h704z m-307.2 352H480v288h76.8v-288z m0-160H480v76.8h76.8V288z"
              fill="#406BD4"
              p-id="5849"
            ></path>
          </svg>
          <div style="margin-left: 8px">
            请确认方案测点与实际匹配到的传感器测点的对应关系是否正确。
          </div>
        </div>
        <div v-loading="!allSuccess" element-loading-text="数据解析中..." element-loading-spinner="el-icon-loading" style="margin: 16px 0px; display: flex; align-items: center;min-height: 100px">
          <el-table
            size="medium"
            style="width: 100%; max-height: 700px"
            :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
            class="transition-box"
            :data="tableData"
          >
          <el-table-column
                v-for="(headerKey, headerIndex) in Object.keys(headerData)"
                min-width="170"
                prop="param_key"
                :key="headerKey"
                :label="headerData[headerKey]"
                >
                <template #default="scope">
                    <div v-if="scope.row[headerKey] === '未匹配到测点'"><el-tag type="danger">{{scope.row[headerKey]}}</el-tag></div>
                    <div v-else>{{ scope.row[headerKey] }}</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="onConfirm"
          >确认</el-button
        >
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose"
          >返回修改方案</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { saveRel } from '@/api/planGenerateApi.js';
import { cloneDeep, isEmpty } from 'lodash';
export default {
  name: 'DataAlignPreDialog',
  components: {},
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    runStreamList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      loading: false,
      showFlag: false,
      tableData: [],
      headerData: {}
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val;
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
    allSuccess: {
      handler(val) {
        if(val) {
            const temp = this.runStreamList[1].content.replace('```json', '')
            const temp2 = String(temp).replace('```', '');
            // console.log('temp', temp, temp2);
            console.log(JSON.parse(temp2))
            const rdata = JSON.parse(temp2);
            // const valTemp = eval(temp2);
            if (rdata) {
                this.headerData = rdata.header || {};
                this.tableData = rdata.data || {};
            }
        }
      },
      immediate: true
    }
  },
  methods: {
    onConfirm() {
      this.$emit('close', true);
    },
    onClose() {
      this.$emit('close', '');
    }
  }
};
</script>
<style lang="scss" scoped>
.headerTip {
  display: flex;
  align-items: center;
  background-color: #ebeffa;
  color: #4068d4;
  border: 1px solid #4068d4;
  border-radius: 2px;
  padding: 7px 12px;
  .tipIcon {
    width: 16px;
    height: 22px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
