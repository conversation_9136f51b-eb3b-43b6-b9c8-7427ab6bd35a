<template>
  <div style="width: 100%;overflow:auto">
    <div class="file-item" v-for="(item, index) in mappedFileData" :key="index">
      <div class="flex_body" @click="handleItemClick(item)">
        <div class="flex_body">
          <!-- 文件夹/文件图标 -->
          <el-checkbox v-if="!item.isDir" style="margin-right:15px" v-model="item.checked"></el-checkbox>
          <!-- <img v-if="item.isDir" src="@/assets/images/planGenerater/ic_wjj.png" alt="">
          <img v-else src="@/assets/images/planGenerater/ic_wj.png" alt=""> -->
          <i v-if="item.isDir" :class="['el-icon', item.expanded ? 'el-icon-folder-opened' : 'el-icon-folder']" style="margin-right: 8px; color: #8c939d;"></i>
          <i v-else class="el-icon-document" style="margin-right: 8px; color: #8c939d;"></i>
          <el-tooltip class="item" effect="dark" :content="item.name  == firstUrl ? '文件' : item.name" placement="top">
            <div @click="showFileFun(item)" class="file-scene">{{ item.name  == firstUrl ? '文件' : item.name }}</div>
          </el-tooltip>
          
          <div v-if="item.isDir && item.expanded" type="text" @click="(e) => { addFileFun(e, item) }" class="file-name">
            上传文件
          </div>
          <el-tooltip class="item" effect="dark" content="下载选中" placement="top">
            <img v-if="firstUrl == item.objectKey" style="margin-left:15px" src="@/assets/images/planGenerater/ic_xaiz.png" alt="" @click.stop="downlFunAll()">
          </el-tooltip>
        </div>
        <!-- 文件夹展开箭头 -->
        <div style="margin: 0px 10px" v-if="!item?.isDir">
          <el-tooltip class="item" effect="dark" content="删除" placement="top">
            <img src="@/assets/images/planGenerater/del_ic.png" alt="" style="margin-right:10px"
              @click.stop="delnlFun(item)">
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="下载" placement="top">
            <img src="@/assets/images/planGenerater/ic_xaiz.png" alt="" @click.stop="downlFun(item)">
          </el-tooltip>
        </div>
        <i v-if="item.isDir" :class="['el-icon', item.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
      </div>
      <!-- 递归展示子文件夹内容 -->
      <div v-if="item.isDir && item.expanded" class="sub-files">
        <abilitys-file :fileData="item.children || []" :firstUrl="firstUrl" @editFile="(data,objectKey) => $emit('editFile', data,objectKey)" />
      </div>
    </div>
    <el-dialog :visible.sync="show" :title="title" width="80%" height="70vh" @closed="show = false" append-to-body>
      <div style="height: 65vh;overflow: auto;">
        <highlightjs autodetect :code="codeStr" />
      </div>
      <template #footer>
        <div>
          <el-button @click="show = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { queryListFiles, queryObjectKey, abilityDel, generate_sign_url } from '@/api/planGenerateApi.js'
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import _ from 'lodash'
export default {
  name: 'AbilitysFile',
  props: {
    fileData: {
      type: Array,
      default: () => [],
      required: true
    },
    firstUrl: {
      type: String,
    },
    editFile:{
      type: Function,
    }
  },
  data() {
    return {
      show: false,
      title: '',
      codeStr: '',
      mappedFileData: []
    }
  },
  watch: {
    fileData: {
      handler(newVal) {
        this.mappedFileData = newVal.map(item => ({
          isDir: item.isDir != undefined ? item.isDir : true,
          name: item.name || '',
          objectKey: item.objectKey || '',
          objectSize: 0,
          checked: item.checked || false,
          objectType: item.objectType || '',
          scheme_id: item.scheme_id
        }))
        if (this.mappedFileData.length == 1) {
          this.handleItemClick(this.mappedFileData[0])
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getAllMappedData() {
      let allData = [];
      // 添加当前层级的数据
      allData = allData.concat(this.mappedFileData);
      
      // 递归获取子层级的数据
      this.mappedFileData.forEach(item => {
        if (item.isDir && item.children) {
          // 获取子组件实例
          const childComponent = this.$children.find(child => 
            child.$options.name === 'AbilitysFile' && 
            child.fileData === item.children
          );
          if (childComponent) {
            allData = allData.concat(childComponent.getAllMappedData());
          }
        }
      });
      
      return allData;
    },
    downlFunAll() {
      // 获取所有层级的数据
      const allMappedData = this.getAllMappedData();
      // 筛选出选中的文件
      const selected = allMappedData.filter(item => item.checked);
      Promise.all(selected.map((item) => this.generateSignByObjectKey(item.objectKey)))
      .then((res) => {
      const data = res;
      this.download(data);
      })
      .catch((err) => {
      console.log(err);
      });
    },
    // 当前url获取blob 对象
    async getBlob(url) {
      return new Promise((resolve) => {
        fetch(url)
          .then((response) => {
            return response.blob();
          })
          .then((res) => {
            const blob = new Blob([res]);
            resolve(blob);
          });
      });
    },
    downlFun(item) {
      Promise.all([this.generateSignByObjectKey(item.objectKey)])
        .then((res) => {
          const data = res;
          this.download(data);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async download(urls) {
      const zip = new JSZip();
      try {
        await this.zipFiles(zip, urls);
        zip.generateAsync({ type: 'blob' }).then(function (content) {
          FileSaver.saveAs(content, '文件下载.zip');
        });
      } catch (e) {
        console.log(e);
      }
    },
    async zipFiles(zip, urls) {
      return new Promise((resolve, reject) => {
        let curIdx = 0;
        urls.forEach((item, index) => {
          const endNum = item.indexOf('?');
          const startNum = item.lastIndexOf('/') + 1;
          const name = item.substring(startNum, endNum);
          this.getBlob(item)
            .then((res) => {
              zip.file(name, res);
            })
            .finally(() => {
              curIdx++;
              if (curIdx === urls.length) resolve();
            });
        });
      });
    },
    async generateSignByObjectKey(urlKey) {
      try {
        const res = await queryObjectKey({ 'objectKey': urlKey });
        // 明确返回 res.data.result，假设接口响应结构为 { data: { result: ... } }
        return res?.data?.result;
      } catch (err) {
        // 错误处理（可选：抛出错误或返回默认值）
        console.error(`请求 ${urlKey} 失败:`, err);
        throw err; // 保持 Promise 链的 reject 状态
        // 或返回默认值（如 null），避免 Promise.all 整体失败
        // return null;
      }
    },
    addFileFun(e, item) {
      e.stopPropagation()
      // 创建一个隐藏的文件输入框
      const fileInput = document.createElement('input')
      fileInput.type = 'file'
      fileInput.style.display = 'none'

      // 监听文件选择事件
      fileInput.addEventListener('change', async (event) => {
        const file = event.target.files[0]
        if (!file) return
        const formData = new FormData();
        try {
          const res = await generate_sign_url({
            file_key: item.objectKey + file.name
          });
          if (res.data.code === 200) {
            formData.append('key', item.objectKey + file.name);
            formData.append('accessKeyId', res.data.result.accessKeyId);
            formData.append('signature', res.data.result.signature);
            formData.append('policy', res.data.result.policy);
            formData.append('file', file);
            const res1 = await this.$axios.post(res.data.result.obs_url, formData);
            if (res1.status === 204) { // 上传成功
              this.$message({
                type: 'success',
                message: '文件上传成功'
              });

              // 获取当前文件夹的路径
              const currentPath = item.objectKey

              // 更新当前文件夹的文件列表
              try {
                const listRes = await queryListFiles({
                  scheme_id: Number(item.scheme_id),
                  prefix: currentPath
                })

                if (listRes.data.code === 200 && listRes.data.result) {
                  // 只更新当前文件夹的子项
                  const updatedFiles = listRes.data.result.map(it => ({
                    ...it,
                    scheme_id: item.scheme_id,
                    name: it.objectName,
                    objectType: '',
                    checked: item.checked || false,
                    isDir: it.isDir !== undefined ? it.isDir : true
                  }))

                  // 找到当前文件夹并更新其 children
                  const currentFolder = this.mappedFileData.find(f => f.objectKey === item.objectKey)
                  if (currentFolder) {
                    currentFolder.children = updatedFiles
                  }
                }
              } catch (error) {
                console.error('更新文件列表失败:', error)
                this.$message.error('更新文件列表失败')
              }
            }
          }

        } catch (e) {
          console.log(e);
          this.$message.error('获取签名出错！');
        }


        // 清理文件输入框
        document.body.removeChild(fileInput)
      })

      // 将文件输入框添加到 body 并触发点击
      document.body.appendChild(fileInput)
      fileInput.click()
    },
    // 获取文件扩展名
    getFileExtension(filename) {
      const parts = filename.split('.');
      return parts.pop() || '';
    },
    async showFileFun(item) {
      if (!item.isDir) {
        if (this.getFileExtension(item.name) == 'pkl' || this.getFileExtension(item.name) == 'png' || this.getFileExtension(item.name) == 'jpg' || this.getFileExtension(item.name) == 'jpeg' || this.getFileExtension(item.name) == 'gif') {
          this.$message({
            type: 'warning',
            message: `当前文件不能预览`
          });
          return false
        }
        const res = await queryObjectKey({
          'objectKey': item.objectKey,
        })
        if (res.data.code == 200) {
          this.title = item.name
          fetch(res.data.result)
            .then(response => response.text())
            .then(data => {
              // data就是文件的内容
              // this.codeStr = data
              console.log(data, 'data')
              this.$emit('editFile',data,item.objectKey)
              // this.show = true
            })
            .catch(error => { console.log('error', error) });
        }
      }
    },
    async delnlFun(item) {
      let data = item.objectKey.replace(this.firstUrl, '')
      const res = await abilityDel({
        "scheme_id": item.scheme_id,
        "obs_suffix_file_key": data
      })
      if (res.data.code == 200) {
        this.$message({
          type: 'success',
          message: `删除成功`
        });

        // 获取当前文件所在文件夹的路径
        const parentPath = item.objectKey.substring(0, item.objectKey.lastIndexOf('/'))

        // 使用父文件夹路径作为prefix获取文件列表
        try {
          const listRes = await queryListFiles({
            scheme_id: Number(item.scheme_id),
            prefix: parentPath
          })

          if (listRes.data.code === 200 && listRes.data.result) {
            // 更新当前文件夹的文件列表
            this.mappedFileData = listRes.data.result.map(it => ({
              ...it,
              scheme_id: item.scheme_id,
              name: it.objectName,
              objectType: '',
              checked: item.checked || false,
              isDir: it.isDir !== undefined ? it.isDir : true
            }))
          }
        } catch (error) {
          console.error('更新文件列表失败:', error)
          this.$message.error('更新文件列表失败')
        }
      }
    },
    async handleItemClick(item) {
      console.log(item, 'item')
      if (!item.isDir) return

      // 切换展开状态
      this.$set(item, 'expanded', !item.expanded)

      // 如果已经有子文件数据，直接显示/隐藏
      if (item.children) return

      try {
        const res = await queryListFiles({
          scheme_id: Number(item.scheme_id),
          prefix: item?.objectKey
        })

        if (res.data.code === 200 && res.data.result) {
          // 设置子文件数据，子层数据不需要特殊处理
          this.$set(item, 'children', res.data.result.map(it => ({
            ...it,
            scheme_id: item.scheme_id,
            name: it.objectName,
            checked: item.checked || false,
            objectType: '',
          })))
        }
      } catch (error) {
        console.error('获取子文件失败:', error)
        this.$message.error('获取子文件失败')
      }
    }
  }
}
</script>

<style scoped>
.file-item {
  width: 100%;
  color: #616161;
}

.flex_body {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 26px;
  width: 100%;
  border-radius: 2px;
  border-bottom: 1px solid #EBECF0;
  /* padding: 0 10px; */
  cursor: pointer;
}

.flex_body:hover {
  background-color: #f5f7fa;
}

.file-name {
  margin-left: 8px;
  width: 100%;
  text-align: right;
}

.file-scene {
  max-width: 100%;
  text-align: left;
  margin-left: 8px;
  cursor: pointer;
}

.sub-files {
  /* padding-left: 20px; */
}

/* 图标样式 */
.el-icon-folder {
  color: #909399;
}

.el-icon-document {
  color: #909399;
}
</style>
