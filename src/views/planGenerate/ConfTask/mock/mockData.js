export const mockData = [
  //   {
  //     task_order: 0,
  //     task_desc: 'scheme_optimize',
  //     task_name: 'scheme_optimize',
  //     task_statu: 1,
  //     execute_param: {},
  //     execute_instruction: 'scheme_optimize',
  //     process_message_code: 'scheme_optimize_gen_process',
  //     summarize_message_code: 'scheme_optimize'
  //   },
  {
    icon: 'fangan', // 任务图标
    task_order: 0,
    task_desc: '思维树生成',
    task_name: 'decision_tree',
    task_statu: 0,
    is_universal: true,
    execute_instruction: 'api',
    execute_param: {},
    process_message_code: 'decision_tree_process_stream_message',
    summarize_message_code: 'decision_tree_stream_message'
  },
  {
    icon: 'fangan', // 任务图标
    task_order: 1,
    task_desc: '数据对齐分析',
    task_name: 'align_analysis',
    task_statu: 1,
    is_universal: true,
    execute_instruction: 'api',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  },
  // {
  //   icon: 'fangan',
  //   title: '模型参数识别',
  //   status: 0,
  //   runCode: 'model_param_extraction',
  //   process: ''
  // },
  // {
  //   icon: 'fangan',
  //   title: '结构化建模信息',
  //   status: 0,
  //   runCode: 'modeling_info_structure',
  //   process: ''
  // },
  // {
  //   icon: 'fangan',
  //   title: '生成数学模型',
  //   status: 0,
  //   runCode: 'math_model_generate',
  //   process: ''
  // },
  {
    icon: 'fangan', // 任务图标
    task_order: 2,
    task_desc: '数据对齐',
    task_name: 'align_data_generate',
    task_statu: 1,
    is_universal: false,
    execute_instruction: 'apixxx',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  },
  {
    icon: 'fangan', // 任务图标
    task_order: 3,
    task_desc: '代码生成',
    task_name: 'decision_making_generate',
    task_statu: 1,
    is_universal: true,
    execute_instruction: 'apixxx',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  },
  {
    icon: 'fangan', // 任务图标
    task_order: 4,
    task_desc: '代码部署',
    task_name: 'code_deploy',
    task_statu: 1,
    is_universal: false,
    execute_instruction: '',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  },
  {
    icon: 'fangan', // 任务图标
    task_order: 5,
    task_desc: '代码测试',
    task_name: 'code_test',
    task_statu: 1,
    is_universal: false,
    execute_instruction: '',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  },
  {
    icon: 'fangan', // 任务图标
    task_order: 6,
    task_desc: '能力参数分析',
    task_name: 'code_analysis',
    task_statu: 0,
    execute_instruction: '',
    execute_param: {},
    process_message_code: '',
    summarize_message_code: ''
  }
]
