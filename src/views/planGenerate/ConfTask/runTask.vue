<template>
  <div>
    <el-dialog
      custom-class="last-dialog2"
      title="执行任务"
      :visible.sync="showFlag"
      :before-close="onClose"
      :close="onClose"
      width="96%"
    >
      <div slot="title" class="dialog-title">
        <div class="title-text">执行任务</div>
        <div class="zuixiaohua" @click="miniModalShow">
          <el-tooltip class="item" effect="dark" content="最小化" placement="top">
            <img src="@/assets/images/planGenerater/full.png"/>
          </el-tooltip>
        </div>
      </div>
      <div class="task-container">
        <div class="task-left">
          <div id="content-item1" class="content-item"><DataAns :run-stream-list="runStreamList" :all-success="allSuccess" :template-id="templateId" :status="taskList?.[0]?.status"></DataAns></div>
          <div id="content-item2" class="content-item"><DataAlign :data-align-data-status="dataAlignDataStatus" :data-align-data="dataAlignData" :status="taskList?.[1]?.status"></DataAlign></div>
          <div id="content-item3" class="content-item"><CodeCreate :code-data="codeData" :code-data-status="codeDataStatus" :status="taskList?.[2]?.status" :process-status="processStatus"></CodeCreate></div>
          <div id="content-item4" class="content-item"><CodeDeploy :update-develop-flag="updateDevelopFlag"  :status="taskList?.[3]?.status" @handleOk="handleDevelopOk"></CodeDeploy></div>
          <div id="content-item5" class="content-item"><CodeTest :update-test-flag="updateTestFlag" :status="taskList?.[4]?.status" @handleOk="handleTestOk"></CodeTest></div>
        </div>
        <div class="task-right">
          <div class="header-title">当前任务</div>
          <div class="task-list">
              <div v-for="(item, index) in taskList" :key="index" class="task-list-item" @click="scrollView(index)">
                <div class="task-icon">
                  <img v-if="item.status === 1" src="@/assets/images/planGenerater/task-success.png"/>
                  <img v-else-if="item.status === 3" src="@/assets/images/planGenerater/task-running.png"/>
                  <img v-else-if="item.status === 2" src="@/assets/images/planGenerater/task-error.png"/>
                  <img v-else src="@/assets/images/planGenerater/task-info.png"/>
                  <div v-if="index !== taskList.length - 1" :class="(item.status === 0 || !item.status) ?'task-line task-line-info' : 'task-line'"></div>
                </div>
                <div class="task-text">
                  <div class="task-text-title">{{item.title}}</div>
                  <div v-if="(item.status === 1 || item.status === 2 )&& allCompleted" class="task-text-link" @click="redoTask(item)">从当前开始重试</div>
                </div>
              </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="info" :loading="loading" :disabled="(taskList[4].status !== 1 && taskList.filter(item => item.status===0).length !== 5) || loading" @click="reStart">重试</el-button>
        <el-button type="primary" :loading="loading" :disabled="loading" @click="accessDevlop">转研发</el-button>
        <el-button v-if="taskList[4].status === 1" type="info" :loading="loading" :disabled="loading" @click="onClose('finish')">完成</el-button>
        <el-popconfirm
          v-else-if="taskList.filter(item=>item.status === 3).length"
          title="是否终止执行当前任务？"
          confirm-button-text='终止'
          cancel-button-text='取消'
          @confirm="confirmCancel"
          @cancel="onClose"
        >
          <el-button slot="reference" style="margin-left: 12px" type="info" :loading="loading" :disabled="loading">终止</el-button>
        </el-popconfirm>
        <el-button v-else type="info" :loading="loading" :disabled="loading" @click="onClose">取消</el-button>
      </div>
      <devlopModal :is-visible="devlopModalVisable" :cur-id="curId" :dev-person="devPersonInfo" @close="handleClose" />
    </el-dialog>
  </div>
</template>
<script>
// import MyEditor from './mdEditor.vue';
import DataAns from './task/dataAns.vue';
import DataAlign from './task/dataAlign.vue';
import CodeCreate from './task/codeCreate.vue';
import CodeDeploy from './task/codeDeploy.vue';
import CodeTest from './task/codeTest.vue';
import devlopModal from './devlopModal.vue';
import {startAlignDataGenerate, startAbilityGenerate, reexecuteTask} from '@/api/planGenerateApi.js'
export default {
  name: 'RunTaskDialog',
  components: {
    // MyEditor,
    DataAns,
    DataAlign,
    CodeCreate,
    CodeDeploy,
    CodeTest,
    devlopModal
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    // 会话流的唯一id
    sessionId:  {
      type: String,
      default: ''
    },
    runStreamList: {
      type: Array,
      default: []
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    templateId:  {
      type: [String, Number],
      default: ''
    },
    dataAlignDataStatus: {
      type: [String, Number],
      default: ''
    },
    dataAlignData: {
      type: Array,
      default: []
    },
    codeDataStatus: {
      type: [String, Number],
      default: ''
    },
    codeData: {
      type: String,
      default: ''
    },
    updateDevelopFlag: {
      type: [String, Number],
      default: 1
    },
    flowData: {
      type: Array,
      default() {
        return [];
      }
    },
    devPerson: {
      type: Object,
      default() {
        return {}
      }
    },
    processStatus: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      loading: false,
      showFlag: false,
      devlopModalVisable: false,
      updateTestFlag: 1, // 触发更新代码测试的
      devPersonInfo:{},
      curId:'',
      taskList: []
    };
  },
  computed: {
    allCompleted(){
     const filter = this.taskList.filter(item => item.status === 3)
      return !(filter.length > 0)
    },
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.scrollView(1);
          console.log('flowData', this.flowData);
          this.showFlag = val;
          this.taskList = this.flowData
          // 下面注释的代码是内容滚动看滚动位置进行步骤定位的，先不用
          // this.$nextTick(() => {
          //   const content = document.querySelector('.task-left');
          //   content.addEventListener('scroll', () => {
          //     console.log('滚动高度', content.scrollTop);
          //     this.updateStep(content.scrollTop);
          //   });
          // })
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
    flowData: {
      handler(val) {
        this.taskList = val
        console.log('更新任务列表状态', val);
        let lastRunIndex = 0
        val.forEach((item, index) => {
          if(item.status === 3) {
            lastRunIndex = index
          }
        })
        console.log('开始滚动', lastRunIndex);
        this.scrollView(lastRunIndex);
      },
      immediate: true
    }
  },
  async mounted() {

  },
  methods: {
    handleClose(val){
      this.devlopModalVisable = false
      if (val) {
        this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'success'
        }).then(() => {
          this.devlopModalVisable = false
          this.devPerson = val;
          this.devPersonInfo = val;
        }).catch(() => {
          this.devlopModalVisable = false
          this.devPerson = val;
          this.devPersonInfo = val;
        });
      }
    },
    updateStep(scrollTop) {
      const allContents = document.querySelectorAll('.content-item');
      const content = document.querySelector('.task-left');
        const rectContent = [];

        console.log('content.innerHeight', content.scrollTop)
        allContents.forEach((ele) => {
          const eleRect = ele.getClientRects()[0];
          console.log('eleRect.top', eleRect.top, eleRect.height)
          if (
            (eleRect.top >= 0 && content.scrollTop - eleRect.top >= eleRect.height) ||
            (eleRect.top < 0 && content.scrollTop <=
            eleRect.height - Math.abs(eleRect.top)) ||
            eleRect.top >= 0
          ) {
              rectContent.push(ele);
          }
        });
        console.log('视窗rectContent', rectContent);
        let linkId
        if (rectContent[0]) linkId = rectContent[0].id
        // allLinks.forEach(link => link.classList.remove('active'))
        // const linkDom = document.querySelector(`a[href="#${linkId}"]`)
        // linkDom.classList.add('active')
    },
    scrollView(index) {

      const id = 'content-item'+(index+1);
      const sectionEl = document.getElementById(id);
      console.log('000', sectionEl,id);
      // sectionEl.scrollIntoView({ behavior: "smooth" });
      sectionEl && sectionEl.scrollIntoView({ block: 'start', inline: 'nearest',behavior: 'smooth'  });
    },
    accessDevlop(){
      this.devPersonInfo = this.devPerson
      this.curId = this.$route.query.id
      this.devlopModalVisable = true;
    },
    onClose(val) {
      this.$emit('updateTaskModal',val);
    },
    // 最小化窗口
    miniModalShow() {
      this.$emit('updateTaskMiniModal');
    },
    // 确认关闭任务执行
    confirmCancel() {
      if (this.taskList[0].status === 3) {
        this.taskList[0].status = 2;
        this.taskList[1].status = 0;
        this.taskList[2].status = 0;
        this.taskList[3].status = 0;
        this.taskList[4].status = 0;
      }
      this.$emit('stopAbilityGen')
      // this.$emit('updateTaskModal');
    },
    // 重试
    reStart() {
      this.$emit('handleReStart');
      reexecuteTask({scheme_id: this.$route.query.id, task_name: 'align_analysis'})
      this.taskList[0].status = 3;
      this.taskList[1].status = 0;
      this.taskList[2].status = 0;
      this.taskList[3].status = 0;
      this.taskList[4].status = 0;
    },
    // 从点击的步骤开始执行，只针对已成功的步骤
    redoTask(row) {
      reexecuteTask({scheme_id: this.$route.query.id, task_name: row.runCode})
      // 除去测试不需要通知后端接口外，其他步骤需要通知后端存储
      if(row.runCode === 'align_analysis') {
        console.log('执行数据对齐分析');
        this.$emit('handleReStart');
        this.taskList[0].status = 3;
        this.taskList[1].status = 0;
        this.taskList[2].status = 0;
        this.taskList[3].status = 0;
        this.taskList[4].status = 0;
        this.$emit('updateFlowData', this.taskList);
      }
      if(row.runCode === 'align_data_generate') {
        console.log('执行多模态数据对齐');
        this.$emit('reAlignData');
        this.taskList[0].status = 1;
        this.taskList[1].status = 3;
        this.taskList[2].status = 0;
        this.taskList[3].status = 0;
        this.taskList[4].status = 0;
        this.$emit('updateFlowData', this.taskList);
        this.scrollView(1);
      }
      if(row.runCode === 'decision_making_generate') {
        console.log('执行代码生成');
        this.scrollView(2);
        // 重新执行代码生成
        startAbilityGenerate({ability_type: 'decision_ability', session_id: this.sessionId})
        this.taskList[0].status = 1;
        this.taskList[1].status = 1;
        this.taskList[2].status = 3;
        this.taskList[3].status = 0;
        this.taskList[4].status = 0;
        this.$emit('updateFlowData', this.taskList);
      }
      if(row.title === '代码部署') {
        this.scrollView(3);
        console.log('执行代码部署');
        this.taskList[0].status = 1;
        this.taskList[1].status = 1;
        this.taskList[2].status = 1;
        this.taskList[3].status = 3;
        this.taskList[4].status = 0;
        this.$emit('updateFlowData', this.taskList);
        setTimeout(() => {
          this.updateDevelopFlag = Number(this.updateDevelopFlag) + 1;
        }, 500)
      }
      if(row.title === '代码测试') {
        this.scrollView(4);
        console.log('执行代码测试');
        this.$emit('updateFlowData', this.taskList);
        setTimeout(() => {
          this.handleDevelopOk(true);
        }, 500)
      }
    },
    // 代码部署完成通知
    handleDevelopOk (flag) {
      console.log('代码部署结果', flag, this.taskList);
      this.scrollView(4);
      if (flag) {
        this.taskList[3].status = 1;
        this.taskList[4].status = 3;
        setTimeout(() => {
          this.updateTestFlag = Number(this.updateTestFlag) + 1;
        }, 1000);
      } else {
          this.taskList[3].status = 2;
          this.taskList[4].status = 0;
      }
      this.$emit('updateFlowData', this.taskList);
    },
    // 代码测试通知
    handleTestOk (flag) {
      console.log('测试成功表示', flag, this.taskList);
      if (this.taskList[1].status === 1) {
        if (flag) {
          this.taskList[4].status = 1;
        } else {
          this.taskList[4].status = 2;
        }
        this.$emit('updateFlowData', this.taskList);
      } else {
        this.taskList[4].status = 0;
      }
    },
  }
};
</script>
<style lang="scss">
.task-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  height: 100%;
  .task-left {
    flex: 1;
    max-height: 100%;
    overflow-y: auto;
  }
  .task-right {
    min-width: 300px;
    margin-right: 16px;
    border: 1px solid #dcdde0;
    border-radius: 4px;
    margin-left: 16px;
    .header-title {
      border-bottom: 1px solid #ebecf0;
      padding: 12px 20px;
      font-weight: 500;
      font-size: 14px;
      color: #323233;
      line-height: 22px;
    }
    .task-list {
      padding: 16px 20px;
      .task-list-item {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        justify-content: space-between;
        cursor: pointer;
        &.active {
          .task-text {
            border-color: #4068D4;
            .task-text-link {
              opacity: 1 !important;
            }
          }
        }
        .task-icon {
          width: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          img {
            width: 16px;
            height: 16px;
          }
          .task-line {
            flex: 1;
            width: 0px;
            border-left: 1px solid #DCDDE0;
            height: 100%;
            // background: #DCDDE0;
            margin-bottom: 4px;
            margin-top: 4px;
            &.task-line-info {
              border-left: 1px dashed #DCDDE0 !important;
            }
          }
        }
        .task-text {
          flex: 1;
          margin-right: 12px;
          border: 1px solid #DCDDE0;
          border-radius: 4px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          margin-bottom: 12px;
          margin-left: 12px;
          &:hover {
            border-color: #4068D4;
            .task-text-link {
              opacity: 1 !important;
            }
          }
          .task-text-title {
            font-weight: 400;
            font-size: 14px;
            color: #323233;
            line-height: 20px;
            flex: 1;
            margin-right: 8px;
          }
          .task-text-link {
            font-weight: 400;
            font-size: 14px;
            color: #4068D4;
            line-height: 20px;
            opacity: 0;
          }
        }
      }
    }
  }
}
.last-dialog2 {
  border-radius: 8px;
  margin-top: 60px !important;
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-text {
      flex: 1;
      font-size: 16px;
      color: #323233;
      line-height: 24px;
      font-weight: bold;;
    }
    .zuixiaohua {
      margin-right: 30px;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-top: 3px;
        cursor: pointer;
      }
    }
  }
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    height: calc(100vh - 240px) !important;
    max-height: calc(100vh - 240px) !important;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .el-button {
        line-height: 20px;
      }
    }

  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
