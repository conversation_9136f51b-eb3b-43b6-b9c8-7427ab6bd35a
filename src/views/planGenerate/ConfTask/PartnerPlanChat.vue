<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide
        ? 'chatContainerTest chatContainerTestFrame'
        : 'chatContainerTest'
    "
  >
    <div
      :class="
        $store.state.planGenerate.isIframeHide
          ? 'containerBox2 containerBox2IFrame'
          : 'containerBox2'
      "
      style="width: 100%"
    >
      <div
        id="left-content"
        v-loading="planLoading"
        element-loading-text="方案生成中..."
        element-loading-spinner="el-icon-loading"
        :style="{
          width: leftWidth,
          maxWidth: leftWidth,
          marginRight: !rightFullFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: thinkFullFlag ? '' : 'relative'
        }"
        :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'"
      >
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">我的经验沉淀</div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
                <el-button
                  type="info"
                  :disabled="hasChatingName !== ''"
                  size="mini"
                  @click="
                    () => {
                      hisDetail = detailContent.text;
                      isEdit = true;
                    }
                  "
                  ><img src="@/assets/images/planGenerater/bianji.png"
                /></el-button>
              </el-tooltip>
              <template v-if="isEdit">
                <el-tooltip class="item" effect="dark" content="保存" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSave"
                    ><img src="@/assets/images/planGenerater/baocun.png"
                  /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSaveClose"
                    ><img src="@/assets/images/planGenerater/quxiao.png"
                  /></el-button>
                </el-tooltip>
              </template>
              <el-tooltip
                class="item"
                effect="dark"
                :content="rightFullFlag ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="rightFullFlag ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowFull"
                  ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                /></el-button>
              </el-tooltip>
              <el-dropdown
                :disabled="systemMessages === 'scheme generating' || hasChatingName !== ''"
                style="margin-left: 10px"
                @command="handleSchemeCommand"
              >
                <el-button type="info" size="mini"
                  ><img src="@/assets/images/planGenerater/more.png"
                /></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
                  <el-dropdown-item command="history">历史记录</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div id="leftContainer" class="container-list">
            <div class="plan-details">
              <div :class="miniFlag ? 'optScroll optScrollMini' : 'optScroll'">
                <div class="optContentBox customMd" @mouseenter="fangda">
                  <MyEditor
                    id="MyEditor2"
                    ref="MyEditor2"
                    :md-content="detailContent.text"
                    :is-edit="isEdit"
                    @updateContent="handleUpdateContent"
                  ></MyEditor>
                </div>
                <el-alert v-if="hasChatingName" :closable="false" type="error">
                  <span slot="title">正在与【{{ hasChatingName }}】对话中</span>
                </el-alert>
                <el-alert v-if="agentError" :closable="false" type="error">
                  <span slot="title"
                    >人机对话连接失败请刷新后再进行对话 &nbsp; &nbsp;<el-link
                      type="error"
                      text
                      :style="{ verticalAlign: 'baseline' }"
                      @click="refresh"
                      >刷新</el-link
                    ></span
                  >
                </el-alert>
              </div>
              <div class="chatFooter">
                <!-- systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' -->
                <div id="myInputText" class="chatInput">
                  <el-input
                    v-if="speakingFlag === 'running' || shibieLoading || disfangan"
                    ref="myChatInputText"
                    v-model.trim="yuyinText"
                    clearable
                    readonly
                    type="textarea"
                    resize="none"
                    :autosize="{ minRows: 3, maxRows: 3 }"
                    :disabled="
                      [
                        'process stream',
                        'process stream running',
                        'process running',
                        'processing',
                        'scheme generating',
                        'clear_history',
                        'process_stream_message'
                      ].indexOf(systemMessages) > -1 || hasChatingName !== ''
                    "
                    style="width: 100%"
                    placeholder=""
                  >
                  </el-input>
                  <el-input
                    v-else
                    ref="myChatInputText"
                    v-model.trim="currentText"
                    clearable
                    type="textarea"
                    resize="none"
                    :autosize="{ minRows: 3, maxRows: 3 }"
                    :disabled="
                      [
                        'process stream',
                        'process stream running',
                        'process running',
                        'processing',
                        'scheme generating',
                        'clear_history',
                        'process_stream_message'
                      ].indexOf(systemMessages) > -1 ||
                      hasChatingName !== '' ||
                      disfangan
                    "
                    style="width: 100%"
                    placeholder="请输入您的问题/调整方向，系统将根据您的描述重新生成方案内容"
                    @keydown.native="carriageReturn($event)"
                  >
                  </el-input>
                </div>
                <div class="send-btn">
                  <div
                    :class="
                      [
                        'process stream',
                        'process stream running',
                        'process running',
                        'processing',
                        'scheme generating',
                        'clear_history',
                        'process_stream_message'
                      ].indexOf(systemMessages) > -1 ||
                      hasChatingName !== '' ||
                      disfangan ||
                      shibieLoading
                        ? 'yuyinBtn yuyinBtnDisabled'
                        : 'yuyinBtn'
                    "
                  >
                    <img
                      v-if="speakingFlag === 'start'"
                      src="@/assets/images/planGenerater/shuohua.png"
                      @click="startRecording"
                    />
                    <img
                      v-if="speakingFlag === 'running'"
                      src="@/assets/images/planGenerater/yuyin.gif"
                      @click="stopRecording"
                    />
                    <img
                      v-if="speakingFlag === 'end'"
                      src="@/assets/images/planGenerater/zhuanhuan-loading.gif"
                    />
                  </div>
                  <el-button
                    type="info"
                    :disabled="
                      [
                        'process stream',
                        'process running',
                        'process stream running',
                        'processing',
                        'scheme generating',
                        'process_stream_message',
                        'process stream running',
                        'clear_history'
                      ].indexOf(systemMessages) > -1 ||
                      hasChatingName !== '' ||
                      currentText === '' ||
                      speakingFlag === 'running' ||
                      disfangan ||
                      shibieLoading
                    "
                    @click="sendMessage"
                    >生成方案</el-button
                  >
                </div>
                <!-- </div> -->
              </div>
            </div>
            <div class="tools">
              <span class="tools-title">验证工具</span>
              <div :class="{ 'tools-item': true, disabled: true }" @click="handlePlanOptimization">
                <img src="https://res.ennew.com/image/png/29e21aa007a5e073c3dc7ad3719e732c.png" />
                <span>方案结构优化</span>
              </div>
              <div
                :class="{ 'tools-item': true, swdt: true, disabled: disTree }"
                @click="handleShowThinkingTree"
              >
                <img src="https://res.ennew.com/image/png/c0ed4d82f776cdab57d22c1adaf79294.png" />
                <span>思维树</span>
              </div>
              <div class="tools-item disabled">
                <img src="https://res.ennew.com/image/png/3f9e8893b5c8c033961a920fc05a2181.png" />
                <span>物联孪生</span>
              </div>
              <div class="tools-item disabled">
                <img src="https://res.ennew.com/image/png/972a711f1a261fc675b3237298aa92f6.png" />
                <span>数学建模</span>
              </div>
              <div class="tools-item xtfz disabled">
                <img src="https://res.ennew.com/image/png/b27bab1457e4bdc3891dc7051ceb95af.png" />
                <span>系统仿真</span>
              </div>
              <div class="tools-item btn disabled">
                <img
                  src="https://res.ennew.com/image/png/e4d3e3a764ff91f4d820ac1dff30c886.%E5%9B%BE%E6%A0%87%EF%BC%8F%E4%B8%80%E8%88%AC%E5%9B%BE%E6%A0%87%EF%BC%8Fadd-number%402x.png"
                />
                <span>添加工具</span>
              </div>
            </div>
          </div>
          <treeProcess
            :is-visible="processVisable"
            :tree-process-val="optionDataProcess"
            @close="closeSikaoRizhi"
          />
          <historyProcess
            :is-visible="historyVisable"
            :history-data="historyData"
            @updateData="historyDataFn"
            @close="closeHistory"
          />
          <el-dialog title="思维树" :visible.sync="dialogVisible" width="60%">
            <div
              v-if="treeStatus == 1 || treeStatus == 0"
              class="optContentBox"
              :style="{ height: '100%' }"
            >
              <vue-markdown :source="treeData"></vue-markdown>
            </div>
            <MyEditorPreview
              v-else
              id="MyEditor4"
              ref="MyEditor4"
              :md-content="treeData"
            ></MyEditorPreview>
          </el-dialog>
        </div>
      </div>
      <div id="resize" class="resize" title="收缩侧边栏" @mousedown="startDrag"></div>
      <div
        id="right-content"
        :style="{
          width: rightWidth,
          marginRight: '16px',
          transition: isDragging ? 'none' : 'width 0.2s',
          userSelect: isDragging ? 'none' : 'auto',
          overflow: 'auto'
        }"
        :class="miniFlag ? 'chatRight chatRightMini' : 'chatRight'"
      >
        <div
          id="top-content"
          :style="{
            userSelect: isDragging ? 'none' : 'auto',
            transition: isDragging ? 'none' : 'width 0.2s',
            position: thinkFullFlag ? '' : 'relative'
          }"
          :class="!planDetailTopShow ? 'optContent chatRightFull' : 'optContent'"
        >
          <div class="optHeader header-right">
            <div class="rightTitle">
              <img src="https://res.ennew.com/image/png/3fc1d6d64903a4001fe1b181982b68c4.png" />
              <span>关联文章/知识库</span>
            </div>
          </div>
          <div
            id="iframeBox"
            v-loading="false"
            class="topContent"
            element-loading-text="关联文章/知识库加载中..."
            element-loading-spinner="el-icon-loading"
          >
            <!-- <div class="topContent-img">
              <div class="img-item">
                <img src="https://res.ennew.com/image/png/32f028e424ca24356f0c51c5dcb8e55b.png" />
                <span>城市基础设施安全工程</span>
              </div>
              <div class="img-item">
                <img src="https://res.ennew.com/image/png/32f028e424ca24356f0c51c5dcb8e55b.png" />
                <span>基础知识</span>
              </div>
            </div> -->
            <div class="topContent-list">
              <div v-for="(item, index) in knowledgeBaseList" :key="index" class="list-item">
                <span class="top">{{ item.name }}</span>
                <div class="bottom" @click="handleJump(item.url)">
                  <img src="https://res.ennew.com/image/png/610384d37916f8d50f8afd5ae5a4cedd.png" />
                  <span>{{ item.url }}</span>
                </div>
              </div>
              <div class="more">
                <img src="https://res.ennew.com/image/png/dd88c526662da5178f838b0bb72ddb44.png" />
                <span>查看更多</span>
              </div>
            </div>
          </div>
        </div>
        <div id="topResize" class="topResize" title="收缩侧边栏" @mousedown="startTopDrag"></div>
        <div
          id="bottom-content"
          :style="{
            transition: isDragging ? 'none' : 'width 0.2s',
            userSelect: isDragging ? 'none' : 'auto'
          }"
          :class="!planDetailBottomShow ? 'optContent chatRightFull' : 'optContent'"
        >
          <div class="optHeader header-right">
            <div class="rightTitle">
              <img src="https://res.ennew.com/image/png/445f4a28f257896de6beeaaaf8c0efed.png" />
              <span>相关问题</span>
            </div>
          </div>
          <div
            v-loading="taskLoading"
            class="bottomScroll question"
            element-loading-text="相关问题生成中..."
            element-loading-spinner="el-icon-loading"
          >
            <div v-for="(item, index) in questionsList" :key="index" class="question-item">
              <span>
                {{ item }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <treeProcess
      :is-visible="processTreeVisable"
      :tree-process-val="treeDataProcess"
      @close="closeSikaoRizhi"
    />
    <codeAnalysis
      ref="chatScrollBox2"
      :is-visible="codeAnalysisProcessStatus"
      :tree-process-val="codeAnalysisProcess"
      :tree-process-val-first="codeAnalysisProcessFirst"
      :title-val="'代码分析日志'"
      @close="handleNext"
    />
    <planOptimize
      :is-visible="youhuaVisable"
      :tree-status="planStatus"
      :sql-data="planDataVal"
      :agent-scene="schemeInfo.agent_scene"
      @close="closeYouhuaEdit"
      @updateOptimize="emitOptimize"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  updateAbilityMapping,
  SchemeDetail,
  GetDecision,
  queryAbilityMapping,
  PlanTaskEdit,
  queryCache,
  addCache,
  removeCache,
  querySchemeDetailById,
  SchemeSaveKnow,
  updateByDeviceId,
  checkByDeviceId,
  saveSimpleSchemeGenerate,
  getTaskStatus,
  getExecuteSync,
  updateSchemeDescription,
  getknowledgeRetrievalCapabilityId,
  getKonwledgeSource,
  getAssociationGeneration,
  updateTasksRes,
  getTasksRes,
  queryAbilityList
} from '@/api/planGenerateApi.js';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import treeProcess from '../treeProcess.vue';
import historyProcess from './historyProcess.vue';
import planOptimize from '../fanganyouhua.vue';

import panzoom from 'panzoom';
import MyEditor from '../mdEditor.vue';
import MyEditorPreview from '../mdEditorPreview.vue';
import codeAnalysis from '../codeAnalysis.vue';
import Recorder from 'js-audio-recorder';
const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
};
export default {
  components: {
    treeProcess,
    MyEditor,
    MyEditorPreview,
    historyProcess,
    codeAnalysis,
    planOptimize
  },
  props: {
    agentSenceCode: {
      type: String,
      default: ''
    },
    planDataVal: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    planProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    planStatus: {
      type: Number,
      default: -1
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    sessionId: {
      type: String,
      default: '1'
    },
    displayType: {
      type: Number,
      default: 1
    },
    miniFlag: {
      type: Boolean,
      default: false
    },
    agentError: {
      type: Boolean,
      default: false
    },
    developFlag: {
      type: Boolean,
      default: false
    },
    isExpert: {
      type: Boolean,
      default: false
    },
    flowData: {
      type: Array,
      default() {
        return [];
      }
    },
    systemMessages: {
      type: String,
      default: ''
    },
    changeRunTaskStatus: {
      type: [Number, String],
      default: ''
    },
    codeAnalysisData: {
      type: String,
      default: ''
    },
    codeAnalysisDataStatus: {
      type: String | Number,
      default: ''
    },
    codeAnalysisProcessData: {
      type: String,
      default: ''
    },
    emergentScene: {
      type: String
    }
  },
  data() {
    return {
      dialogVisible: false,
      youhuaVisable: false,
      knowledgeBaseList: [],
      questionsList: [],
      knowledgeId: '',
      sourceList: [],
      relevanceList: [],
      topologyData: {
        title: '找不到拓扑关系？'
      },
      isReplay: false, // 重新执行任务
      deviceObj: {},
      bottomHeightSpecial: '100%',
      specialText: '锅炉',
      iframeSrc: '',
      schemeInfo: {},
      optimizeData: '', // 方案优化流数据
      currentText: '',
      optionDataProcess: '',
      speakingFlag: 'start',
      yuyinText: '',
      shibieLoading: false,
      recorderEx: new Recorder(parameter),
      startY: '',
      startHeight: '',
      contentEditor: '',
      saveLoading: false,
      isSuperAdmin: false, // 是否超级管理员
      dialogTableVisible: false,
      gridData: [],
      tableLoading: false, // 加载状态
      detailContent: { text: '', file_url: '' },
      hisDetail: '',
      processContent: { text: '' },
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      timer: null,
      isDragging: false,
      leftWidth: '65%',
      topHeight: '50%',
      rightWidth: '',
      bottomHeight: '50%',
      totalWidth: 1000,
      totalHeight: 1000,
      isEdit: false,
      planDetailShow: true,
      planDetailTopShow: true,
      planDetailBottomShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskLoading: false,
      disfangan: false,
      deviceLoading: false,
      planLoading: false,
      writeFlag: true,
      writeText: '',
      treeData: '',
      treeDataProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      processTreeVisable: false, // 思维树生成过程弹窗标志
      panZoomRef: null,
      dataThDatas: [],
      dataStatus: '',
      checkNum: 0,
      isCache: false,
      isCacheDisabled: true,
      historyVisable: false,
      historyData: [],
      runTaskStatus: 0,
      env: {
        dev: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=5197&paperId=14649&sceneCode=1666686413389905975&editorMode=function-energy-topology&isFromIMP=true`,
        fat: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=396&paperId=1066&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`,
        uat: '',
        production: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=129&paperId=4201&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`
      },
      nextLoading: false,
      disTree: false,
      codeAnalysisProcessStatus: false,
      codeAnalysisProcess: '',
      codeAnalysisProcessFirst: '',
      ploading: false,
      reTree: false,
      reTreeTimes: 0,
      ability_id: '',
      schemeDescription: '',
      modelObj: {
        modelType: '',
        modelCode: ''
      },
      isMaximize: false,
      abilityList: []
    };
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    }),
    taskAllComplete() {
      const successTask = [];
      this.flowData.forEach((item) => {
        console.log(item.title + '-hello--' + item.status);
        if (item.status === 1) {
          successTask.push(1);
        }
      });
      console.log('任务进度', successTask);
      return successTask.length === 7;
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成');
          this.taskLoading = false;
          this.disfangan = false;
          setTimeout(() => {
            this.queryCacheHandle();
          }, 2000);
        } else if (val === 1) {
          this.taskLoading = true;
          this.disfangan = true;
        } else if (val === 0) {
          this.taskLoading = false;
          this.disfangan = true;
        } else if (val === 3) {
          // 生成失败
          this.taskLoading = false;
          this.disfangan = false;
        }
      },
      immediate: true
    },
    planStatus: {
      handler(val) {
        if (val === 0) {
          console.log('准备接收完方案信息');
          this.planLoading = true;
          this.disTree = true;
          this.reTree = true;
          console.log('planStatus : ' + val + ',this.reTree: ' + this.reTree);
        } else if (val === 1) {
          this.disTree = true;
          this.planLoading = false;
          this.reTree = true;
          console.log('planStatus : ' + val + ',this.reTree: ' + this.reTree);
        } else {
          if (val === 2 && (!this.developFlag || !this.isExpert)) {
            // 输出完方案 触发任务流程
            console.log('输出完方案触发任务流程111');
            this.$emit('handleUpdateScheme', this.detailContent.text);
            if (this.reTree) {
              this.regenerate();
              this.reTree = false;
              this.reTreeTimes++;
            }
          }
          console.log('方案信息接收', val);
          this.planLoading = false;
          if (Number(val) === 2) {
            this.disTree = false;
            this.getAssociationGenerationFn();
          }
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.treeData = val;
      },
      immediate: true
    },
    planDataVal: {
      handler(val) {
        this.detailContent.text = val;
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.treeDataProcess = val;
      },
      immediate: true
    },
    planProcessVal: {
      handler(val) {
        this.optionDataProcess = val;
      },
      immediate: true
    },
    changeRunTaskStatus: {
      handler(val) {
        this.runTaskStatus = val;
      },
      immediate: true
    },
    codeAnalysisData: {
      handler(val) {
        console.log('代码分析流', val);
        this.codeAnalysisProcess = val;
      },
      immediate: true
    },
    codeAnalysisProcessData: {
      handler(val) {
        this.codeAnalysisProcessFirst = val;
      },
      immediate: true
    },
    codeAnalysisDataStatus: {
      handler(val) {
        console.log('代码分析状态状变化', val);
        if (Number(val) === 1) {
          this.ploading = false;
          this.codeAnalysisProcessStatus = true;
        } else {
          this.codeAnalysisProcessStatus = false;
        }
        if (Number(val) === 2 || Number(val) === 3) {
          this.ploading = false;
          this.handleNext();
        }
      },
      immediate: true
    }
  },
  created() {},
  beforeDestroy() {
    clearInterval();
    clearInterval(this.timer);
    this.timer = null;
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    console.log('宽度', this.rightWidth, this.bottomHeight);
    const nodeMarkmap = document.getElementById('markmap');
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = '';
    }
    await this.queryDetailFn();
    await this.initTaskStatus();
    await this.queryDecision();
    await this.queryAbilityId(); // 获取知识检索能力
    await this.queryPlanDetail(); // 查询方案明细
    await this.handleGetAbilityId();
    await this.handleAbility();
    await this.getRelationsFrom();
    await this.getKnowledgeFrom();
  },
  methods: {
    handleShowThinkingTree() {
      this.dialogVisible = true;
    },
    // 知识来源跳转
    handleJump(url) {
      window.open(url);
    },
    async handleAbility() {
      queryAbilityList({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.data) {
            this.abilityList = res.data || [];
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          });
        });
    },
    closeYouhuaEdit() {
      this.youhuaVisable = false;
      this.queryPlanDetail();
    },
    // 方案优化信号推送
    emitOptimize(val) {
      startSchemeOptimize({ scheme_optimize_ability_id: val, session_id: this.sessionId });
    },
    handlePlanOptimization() {
      // this.youhuaVisable = !!this.abilityList.length;
    },
    queryDetailFn() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        this.schemeInfo = res.data.result;
      });
    },
    // 跳转至侄子孪生页面
    handleJumpToDigitalTwin() {
      console.log('444', process.env.VUE_APP_ENV);
      window.open(
        `https://air${
          process.env.VUE_APP_ENV === 'production' ? '.fat' : '.' + process.env.VUE_APP_ENV
        }.ennew.com/dt-manage/project/list`
      );
    },
    // 获取UUId
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    },
    // 获取ability_id
    async handleGetAbilityId() {
      this.deviceLoading = true;
      const vm = this;
      const res = await vm.$axios.get(
        `${vm.baseUrl}/platform/conf/getApolloVal?key=gpts.twin_iot_model_analysis_ability.id`
      );
      if (res && res.data && res.data.status === 200) {
        this.ability_id = res.data.data;
      }
    },
    // 获取方案描述
    async handleGetSchemeDescription() {
      await querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        this.schemeDescription = this.schemeDescription || res.data.result?.description;
      });
    },
    // 更新外层方案描述
    async handleUpdateSchemeDescription() {
      await updateSchemeDescription({
        scheme_id: this.$route.query.id,
        description: this.schemeDescription
      }).then((res) => {});
    },
    handleNext() {
      console.log('下一步');
      this.codeAnalysisProcessStatus = false;
      this.$emit('updateStep', 1);
    },
    // url地址上的token参数
    getQueryToken(url) {
      setTimeout(() => {
        this.deviceLoading = false;
      }, 2000);
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    updateDeviceId(obj) {
      this.deviceObj = obj;
      window.DEVICE_OBJ = this.deviceObj;
      const params = {
        scheme_id: this.$route.query.id,
        device_id: obj.deviceId || '',
        device_name: obj.deviceName || obj.modelName || '',
        model_type: obj.modeType,
        model_code: obj.modelCode || ''
      };
      // TODO 多选透传传参数据格式改变，带后端确认
      if (obj.type === 'IOTtoIMPSelectList') {
      }
      // 多选透传不需要更新iframe的url
      if (obj.type !== 'IOTtoIMPSelectList') {
        const path = `${this.env[process.env.VUE_APP_ENV]}&modeType=${obj.modeType}${
          obj.modelCode ? '&modelCode=' + obj.modelCode : ''
        }`;
        this.iframeSrc = this.getQueryToken(path);
      }
      // 请求增加条件限制
      if ((obj.modeType === '2' && !obj.modelCode) || (obj.modeType === '1' && !obj.deviceId)) {
        return;
      }
      updateByDeviceId(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('更新设备完成', res.data);
          this.$emit('updateDeviceId', res.data.result?.device_id);
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    // 回车发送消息
    carriageReturn(event) {
      // const e = window.event || arguments[0];
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        console.log('event1', event);
        if (!event.metaKey && !event.ctrlKey) {
          console.log('event2', event);
          event.preventDefault();
          this.$nextTick(() => {
            if (this.currentText) {
              this.sendMessage();
            }
          });
        } else {
          console.log('event3', event);
          if (event.ctrlKey || event.metaKey) {
            this.currentText += '\n';
            const target = document.getElementById('myInputText');
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50;
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              });
            }
          } else {
            event.preventDefault();
          }
        }
      }
      // 英文下｜中文下： 13 Enter Enter
      // 中文下有文字没进入输入框情况是：299 Enter Enter
      // if (e.key === 'Enter' && e.code === 'Enter' && e.keyCode === 13) {
      //   // console.log('this.text', this.currentText);
      //   this.$nextTick(() => {
      //     if (this.currentText) {
      //       this.sendMessage();
      //     }
      //   });
      // }
    },
    async sendMessage() {
      console.log('22222222222222222222222222255555');
      // 正在生成方案明细时，不能再次发送
      if (this.reTree) {
        this.$message({
          type: 'error',
          message: '正在生成方案明细，请稍后发送！'
        });
        return;
      }
      this.treeData = '';
      // 将消息发送到服务器
      console.log('发送消息', this.currentText);
      this.planLoading = true;
      // this.$emit('updateSendMsg', this.currentText);
      await this.getKnowledgeSourceFn(this.currentText);
      this.schemeDescription = this.currentText;
      await this.handleGetAbilityId();
      await this.handleUpdateSchemeDescription();
      this.currentText = '';
      this.speakingFlag = 'start';
      this.yuyinText = '';
    },
    async startRecording() {
      if (
        [
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false;
      } else {
        try {
          this.recorderEx.start();
          this.speakingFlag = 'running';
          this.yuyinText = '正在语音中...';
        } catch (err) {
          console.error('无法获取媒体流:', err);
          this.speakingFlag = 'start';
        }
      }
    },
    stopRecording() {
      this.speakingFlag = 'end';
      this.recorderEx.stop();
      setTimeout(() => {
        const wavBlob = this.recorderEx.getWAVBlob(); // blob格式
        console.log('this.audioChunks33', wavBlob);
        this.yuyinText = '正在转换文字中...';
        this.runExcute(wavBlob);
      });
    },
    async runExcute(audioBlob) {
      this.shibieLoading = true;
      this.currentText = '';
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
        : process.env.VUE_APP_PLAN_API + '/voice/conversion/text';
      // const url = 'http://10.20.50.95:80/voice/conversion/text'
      console.log('url', url, audioBlob);
      await this.$axios
        .post(url, audioBlob, {
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          headers: {
            'Content-Type': 'application/octet-stream'
          },
          onDownloadProgress: (event) => {
            const xhr = event.target;
            const { responseText } = xhr;
            console.log('流信息', responseText);
            let chunk = '';
            let dataArray;
            const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2);
            if (lastIndex !== -1) {
              chunk = responseText.slice(0, lastIndex);
              dataArray = chunk.match(/(.*(\n\n\")?\})(\n\n)?/g);
              const lastText = JSON.parse(dataArray[dataArray.length - 2]?.replace('data:', ''));
              console.log('lastTextlastTextlastText', lastText);
              if (lastText) {
                this.currentText = lastText?.message;
              }
            }
          },
          onError: function (error) {
            // 处理流错误
            console.error(error);
            this.speakingFlag = 'start';
            this.shibieLoading = false;
          }
        })
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流结束', response);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log('识别接口错误', err);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        });
    },
    // 查看任务进度
    showTask() {
      this.$emit('showTaskModal');
    },
    async initTaskStatus() {
      getTaskStatus({ scheme_id: this.$route.query.id }).then((gres) => {
        console.log('任务是否需要触发', gres);
        this.runTaskStatus = gres.data?.result?.task_status || 0;
      });
    },
    async changeViews(val) {
      this.nextLoading = true;
      await checkByDeviceId({
        scheme_id: this.$route.query.id
      })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            // 如果是专家场景，需要判断任务状态来决定是否进入下一步，还是触发自动任务执行
            if (!this.developFlag) {
              // 如果生成新的思维树，自动触发重试
              // if(!this.reTree && this.reTreeTimes > 0){
              //   this.showTask();
              //   this.$emit('handleReStart');
              //   this.reTreeTimes = 0;
              // }else

              if (this.taskAllComplete) {
                this.$emit('updateStep', val);
                window.DEVICE_OBJ = this.deviceObj;
              } else {
                if (
                  !Object.keys(res.data.result).length &&
                  this.detailContent.text.includes(this.specialText)
                ) {
                  this.$message({
                    type: 'error',
                    message: '请选择有效的设备ID'
                  });
                  return;
                }
                console.log('任务没有全部执行完', this.runTaskStatus);
                // 如果任务执行还未执行，触发自动执行，否则直接进入下一步
                // 如果方案明细有调整，重新生成了思维树 也触发自动执行
                if (this.runTaskStatus === 0) {
                  this.$emit('handleReStart', 'startrun', res.data.result?.device_id);
                } else {
                  this.showTask();
                }
              }
            } else {
              // 进入下一步
              this.$emit('updateStep', val);
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '设备未绑定成功'
            });
          }
        })
        .finally(() => {
          this.nextLoading = false;
        });
    },
    handleComplete() {
      this.$router.push({
        path: '/planGenerate/index',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      });
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name;
      });
    },
    async queryCacheHandle() {
      queryCache({ scheme_id: this.$route.query.id, ability_name: 'decision_tree_generate' }).then(
        async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result) {
              this.isCache = res.data.result.isCache;
              this.isCacheDisabled = false;
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        }
      );
    },

    // 知识保存接口
    saveKnowladge() {
      SchemeSaveKnow({
        scheme_id: this.$route.query.id,
        scheme_name: this.schemeInfo.name,
        scheme_content: this.detailContent.text
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$message({
            type: 'success',
            message: '知识保存成功！'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    handleSchemeCommand(command) {
      if (command === 'sikao') {
        this.showSikao();
      } else if (command === 'zhishi') {
        // this.saveKnowladge();
      } else if (command === 'history') {
        this.showHistory();
      } else {
        navigator.clipboard
          .writeText(this.detailContent.text)
          .then(() => {
            this.$message({
              type: 'success',
              message: '复制成功！'
            });
          })
          .catch((error) => {
            this.$message({
              type: 'error',
              message: '复制失败！'
            });
          });
      }
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showTreeSikao();
      } else if (command === 'addCache') {
        addCache({ scheme_id: this.$route.query.id, ability_name: 'decision_tree_generate' }).then(
          async (res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                type: 'success',
                message: res.data?.result || '新增成功'
              });
              this.isCache = !this.isCache;
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          }
        );
      } else if (command === 'removeCache') {
        removeCache({ scheme_id: this.$route.query.id, ability_name: name }).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '删除成功'
            });
            this.isCache = !this.isCache;
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      } else {
        this.copyText();
      }
    },
    async historyDataFn() {
      await this.queryPlanDetail();
    },
    copyText() {
      // 获取需要复制的文本
      const text = this.treeData;
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功！'
          });
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          });
        });
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg');
      const arr = [...svgdoms];
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          });
        }
      });
    },
    // 显示历史记录
    showHistory() {
      this.historyVisable = true;
    },
    closeHistory() {
      this.historyVisable = false;
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示');
      this.processVisable = true;
    },
    showTreeSikao() {
      console.log('思维树生成过程显示');
      this.processTreeVisable = true;
    },
    closeSikaoRizhi() {
      this.processVisable = false;
      this.processTreeVisable = false;
    },
    handleClose() {
      this.dialogTableVisible = false;
      this.saveLoading = false;
      clearInterval(this.timer);
      this.timer = null;
    },
    handleDone() {
      console.log(this.gridData, '222');
      updateAbilityMapping({
        scheme_id: this.$route.query.id,
        config: {
          header: this.dataThs,
          data: this.gridData
        },
        ability_status: 'finished'
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          this.$message({
            type: 'success',
            message: '更新完成!'
          });

          this.dialogTableVisible = false;
          this.saveLoading = false;
        } else {
          this.$message({
            type: 'success',
            message: '更新失败!'
          });
          this.dialogTableVisible = false;
          this.saveLoading = false;
        }
      });
    },

    handleAbilityMapping() {
      this.saveLoading = true;
      if (this.timer != null) {
        return;
      }
      this.timer = setInterval(() => {
        queryAbilityMapping({ scheme_id: this.$route.query.id })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.dataStatus = res.data.result.ability_status;
              const status = res.data.result.ability_status;
              let configData = {};
              try {
                configData = JSON.parse(res.data.result?.config) || {};
              } catch (error) {
                configData = res.data.result?.config || {};
              }
              if (status !== 'generating') {
                this.saveLoading = false;
                clearInterval(this.timer);
                this.timer = null;
              }
              console.log(configData, '111');
              this.dataThs = configData.header || {};
              this.dataThDatas = Object.keys(configData.header || {}).map((item) => {
                return { name: configData.header[item], field: item };
              });
              this.gridData = configData.data || [];
              //   this.gridData = configData.map((item)=>{
              //   return {
              //     param_name: item.param_name,
              //     dataset: item.dataset,
              //     param_key: item.param_key,
              //     test_point:item.test_point,
              //     frequency: item.frequency,
              //     calculation_formula: item.calculation_formula,
              //   }
              // })
              this.dialogTableVisible = true;
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            clearInterval(this.timer);
            this.timer = null;
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {});
      }, 1000);
    },
    // 关闭数据配置
    shujuCompete() {
      this.dialogTableVisible = false;
      if (
        this.treeData &&
        (this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1)
      ) {
      } else {
        if (this.jueceYulanFlag) {
          this.thinkingHandle();
        }
      }
    },

    // 思维图渲染模式切换
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag;
    },
    // 渲染思维图
    thinkingHandle() {
      if (this.treeData) {
        if (
          this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1
        ) {
        } else {
          const transformer = new Transformer();
          const { root } = transformer.transform(this.treeData);
          this.$nextTick(() => {
            Markmap.create('#markmap', null, root);
          });
        }
      }
    },

    regenerate() {
      this.treeData = '';
      this.treeProcess = '';
      this.taskLoading = true;
      this.$emit('updateGenerate');
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name;
      });
    },
    queryDecision() {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_tree'
      };
      GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$emit('handleUpdateTreeData', res.data.result?.decision_making_content || '');
          this.treeData = res.data.result?.decision_making_content || '';
          this.treeDataProcess = res.data.result?.sub_content || '';
          // this.optionDataProcess = res.data.result?.sub_content
          if (this.treeData) {
            // 思维图
            this.thinkingHandle();
          } else {
            const nodeMarkmap = document.getElementById('markmap');
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = '';
            }
          }
          this.queryCacheHandle();
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    queryAbilityId() {
      const params = {
        instance_id: this.$route.query.id,
        ability_type: 'knowledge_base_search'
      };
      getknowledgeRetrievalCapabilityId(params).then((res) => {
        if (res.status === 200) {
          this.knowledgeId = res.data.id;
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    // 获取知识来源
    getKnowledgeFrom() {
      getTasksRes({ scheme_id: this.$route.query.id, biz_type: 'knowledge_base_search' }).then(
        (res) => {
          if (res.status === 200) {
            if (res.data?.content?.length) {
              this.knowledgeBaseList = JSON.parse(res.data.content);
            }
          }
        }
      );
    },
    // 获取关联
    getRelationsFrom() {
      getTasksRes({ scheme_id: this.$route.query.id, biz_type: 'create_questions_result' }).then(
        (res) => {
          if (res.status === 200) {
            if (res.data?.content?.length) {
              this.questionsList = JSON.parse(res.data.content);
            }
          }
        }
      );
    },
    async getKnowledgeSourceFn(goal) {
      const random = Math.floor(Math.random() * 10000);
      const params = {
        scene_instance_id: this.$route.query.id,
        agent_template_id: this.knowledgeId,
        name: this.schemeInfo.name + '_知识检索_' + new Date().getTime() + random,
        goal,
        scheme_detail: this.detailContent.text
      };
      window.NEED_SEARCH_KNOWLEDGE = false;
      window.customeDescription = '';
      await getKonwledgeSource(params).then((res) => {
        if (res.status === 200) {
          this.knowledgeBaseList = res.data;
          // 知识库查询成功后调用保存接口
          updateTasksRes({
            scheme_id: this.$route.query.id,
            biz_type: 'knowledge_base_search',
            content: JSON.stringify(res.data)
          }).then((res) => {
            saveSimpleSchemeGenerate({
              session_id: this.sessionId,
              messages: goal,
              knowledge_base_result: res.data
            }).then((result) => {
              if (result.status === 200 && result.data.status === 'success') {
              }
            });
          });
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    getAssociationGenerationFn() {
      getAssociationGeneration({ session_id: this.sessionId })
        .then((re) => {
          if (re.status === 200) {
            this.questionsList = re.data.result;
            // 关联生成成功后调用保存接口
            updateTasksRes({
              scheme_id: this.$route.query.id,
              biz_type: 'create_questions_result',
              content: JSON.stringify(re.data.result)
            });
          }
        })
        .finally((re) => {});
    },
    async handleDetailSave() {
      const { id, ...rest } = this.detailContent;
      const res = await PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败');
        return;
      }
      this.$message.success('编辑成功');
      this.isEdit = false;
      this.queryPlanDetail();
      // 编辑成功自动生成思维树
      this.regenerate();
      // 编辑成功自动更新任务
      if (this.isExpert) {
        this.$emit('initFlowData', 0);
        this.runTaskStatus = 0;
      }
    },
    handleUpdateContent(val) {
      this.detailContent.text = val;
      console.log(3333);
    },
    handleDetailSaveClose() {
      this.isEdit = false;
      this.detailContent.text = this.hisDetail;
    },
    goToDetail(task) {
      task.adjust_url && window.open(task.adjust_url);
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true;
        this.startX = event.clientX;
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
        this.startWidth = leftWidth;
        document.addEventListener('mousemove', this.onDrag);
        document.addEventListener('mouseup', this.stopDrag);
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX;
        const widthLeft = this.startWidth + deltaX;
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px';
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    startTopDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true;
        this.startY = event.clientY;
        const topHeight = document.getElementById('top-content').getBoundingClientRect().height;
        this.startHeight = topHeight;
        document.addEventListener('mousemove', this.onTopDrag);
        document.addEventListener('mouseup', this.stopTopDrag);
      }
    },
    onTopDrag(event) {
      if (this.isDragging) {
        const deltaY = event.clientY - this.startY;
        const topHeight = this.startHeight + deltaY;
        this.topHeight = topHeight + 'px';
        this.bottomHeight = this.totalHeight - topHeight - 30 + 'px';
      }
    },
    stopTopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onTopDrag);
      document.removeEventListener('mouseup', this.stopTopDrag);
    },
    getWsID() {
      let workspaceId = '';
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId;
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId;
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?');
          const params = Object.fromEntries(new URLSearchParams(query));
          workspaceId = params.workspaceId;
        } catch (error) {
          console.log('error', error);
        }
      }
      return workspaceId;
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10;
    },
    changeShowTopRight() {
      this.planDetailShow = !this.planDetailShow;
      this.planDetailTopShow = !this.planDetailTopShow;
      if (this.planDetailShow) {
        this.rightWidth = '';
        this.leftWidth = '40%';
        this.topHeight = '50%';
        this.bottomHeight = '50%';
      } else {
        this.rightWidth = '';
        this.leftWidth = '0px';
        this.topHeight = '';
        this.bottomHeight = '0px';
      }
    },
    changeShowBottomRight() {
      this.planDetailShow = !this.planDetailShow;
      this.planDetailBottomShow = !this.planDetailBottomShow;
      if (this.planDetailShow) {
        this.rightWidth = '';
        this.leftWidth = '40%';
        this.topHeight = '50%';
        this.bottomHeight = '50%';
      } else {
        this.rightWidth = '';
        this.leftWidth = '0px';
        this.topHeight = '0px';
        this.bottomHeight = '';
      }
    },
    // 改变思维树容器的大小
    handleChangeThinkingTreeSize() {
      this.isMaximize = !this.isMaximize;
      if (this.isMaximize) {
        this.topHeight = 'calc( 100% - 47px)';
        this.bottomHeight = '47px';
      } else {
        this.topHeight = '50%';
        this.bottomHeight = '50%';
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag;
      if (this.rightFullFlag) {
        this.leftWidth = '100%';
        this.rightWidth = '0';
        document.getElementById('leftContainer').style['max-height'] = 'calc(100vh - 100px)';
      } else {
        this.leftWidth = '65%';
        document.getElementById('leftContainer').style['max-height'] = 'calc(100vh - 245px)';
      }
    },
    closechangeThinkWrap() {
      this.thinkFlag = !this.thinkFlag;
      this.thinkFullFlag = false;
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
      }
    },
    // 显示思考过程
    changeThinkWrap(data) {
      this.thinkFlag = !this.thinkFlag;
      this.thinkFullFlag = false;
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
      }
      if (data) {
        this.processContent.text = data;
      } else {
        this.processContent.text = this.processContent.text || '';
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag;
    },
    saveFangan() {
      console.log('修改的值', this.contentEditor.getValue());
      if (this.hasChatingName === '') {
        this.detailContent.text = this.contentEditor.getValue();
        const { id, ...rest } = this.detailContent;
        PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
      }
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result;
          this.$emit('handleUpdateScheme', this.detailContent.text);
          this.$emit('handlePublishAbility', this.detailContent.publish_ability);
          this.optionDataProcess = this.detailContent.sub_content;
          // this.detailContent.text = '```mermaid\ngraph LR\nA[变压器运维方案]\nB[日常维护]\nC[定期检修]\nD[长期检修]\nE[变压器故障]\nF[常见故障]\nG[故障诊断]\nH[故障解决]\nA-->B\nB-->B1(检测冷却设备)\nB-->B2(检查油位)\nB-->B3(检查温度)\nA-->C\nC-->C1(3个月-检查油样)\nC-->C2(6个月或1年-检验绝缘电阻)\nC-->C3(1到2年-进行大修)\nA-->D\nD-->D1(长期维护节点-沟通制造商)\nA-->E\nE-->F\nF-->F1(变压器热故障)\nF-->F2(绝缘破损)\nF-->F3(电极磨损)\nF-->F4(机械故障)\nF-->F5(油位故障)\nF-->F6(油质故障)\nF-->F7(电磁故障)\nE-->G\nG-->G1(观察法)\nG-->G2(试验法)\nG-->G3(分析法)\nG-->G4(维护记录法)\nG-->G5(无损检测)\nG-->G6(红外热像技术)\nG-->G7(振动检测)\nE-->H\nH-->H1(调整变压器油位)\nH-->H2(修复冷却系统)\nH-->H3(修复绝缘破损)\nH-->H4(修理机械故障)\nH-->H5(更换电极部件)\nH-->H6(红外热像处理)\nH-->H7(无损检测和振动检测)\n```';
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },

    startWriteOpt() {
      const content = document.getElementById('detail-content');
      content.addEventListener('mouseup', () => {
        const selection = window.getSelection();
        console.log('selection', selection);
        if (selection.toString() !== '') {
          // 用户选中了文本，执行相应的动作
          console.log('用户选中了文本：' + selection.toString());
          this.writeText = selection.toString();
          const range = selection.getRangeAt(0);
          this.range = range;
          this.writeFlag = false;
        } else {
          this.writeText = '';
          this.writeFlag = true;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 20px;
}
.chatContainerTest {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
.cardEmpty {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.topContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #000000;
    text-align: left;
    font-style: normal;
  }
  .topContent-img {
    display: flex;
    width: 100%;
    padding: 0 0 0 20px;
    justify-content: space-between;
    align-items: center;

    .img-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      border: 1px solid #dbdfea;
      padding: 21px 8px 14px 11px;
      width: 45%;
      margin: 0 15px 15px 0;
      height: 117px;
      img {
        width: 65px;
        height: 51px;
      }
    }
  }
  .topContent-list {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    padding: 0 0 0 20px;
    .list-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      border-radius: 6px;
      border: 1px solid #dbdfea;
      padding: 21px 8px 14px 11px;
      margin: 0 15px 15px 0;
      width: 45%;
      height: 117px;
      .top {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .bottom {
        display: flex;
        margin-top: 8px;
        cursor: pointer;
        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        span {
          font-size: 12px;
          color: #646566;
          white-space: nowrap; /* 防止文本换行 */
          overflow: hidden; /* 隐藏超出的文本 */
          text-overflow: ellipsis; /* 超出时显示省略号 */
        }
      }
    }
    .more {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 6px;
      border: 1px solid #dbdfea;
      margin: 0 15px 15px 0;
      width: 45%;
      height: 117px;
      img {
        width: 14px;
        height: 23px;
        margin-bottom: 10px;
      }
    }
  }
}

.optHeader {
  padding: 0px 20px;
  border-bottom: 1px solid #ebecf0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .rightTitle {
    font-size: 14px;
    font-weight: bold;
    color: #323233;
    line-height: 22px;
    padding: 12px 0px;
  }
  .rightTitleOpt {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .twin {
      color: #4068d4;
      text-decoration: underline;
      cursor: pointer;
      &:hover {
        color: #3c60bf;
      }
    }
    .rightTextBtn {
      background-color: #406bd4;
      font-size: 12px;
      color: #fff;
      padding: 0px 6px;
      height: 24px;
      line-height: 24px;
      border-radius: 2px;
      margin-left: 8px;
      cursor: pointer;
      &:hover {
        background: #3455ad;
      }
      &:active {
        background: #264480;
      }
    }
    .rightBtn {
      // background: #F2F3F5;
      border-radius: 2px;
      width: 30px;
      height: 30px;
      color: #4068d4;
      margin-left: 8px;
      text-align: center;
      line-height: 28px;
      cursor: pointer;
      &:hover {
        background: #ebecf0;
      }
      &:active {
        background: #dcdde0;
      }
      &.rightBtnBlue {
        background-color: #406bd4;
        &:hover {
          background: #3455ad;
        }
        &:active {
          background: #264480;
        }
      }
      img {
        width: 16px;
        height: auto;
      }
    }
  }
}
.header-right {
  border: 0;
  .rightTitle {
    display: flex;
    align-items: center;
    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    span {
      font-weight: 500;
      font-size: 16px;
      color: #323233;
      line-height: 24px;
      font-family: PingFangSC, PingFang SC;
    }
  }
}
.question {
  padding: 0 20px !important;
  .question-item {
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #dbdfea;
    margin-bottom: 8px;
    span {
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      font-family: PingFangSC, PingFang SC;
    }
  }
}
.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 260px) !important;
      max-height: calc(100vh - 260px) !important;
    }
    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }

    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }
    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
  }
  display: flex;
  flex-direction: row;
  height: calc(100vh - 175px);
  overflow-y: hidden;
  position: relative;
  .containerCard {
    //height: calc(100% - 18px);
    // max-height: calc(100vh - 210px);
    overflow-y: hidden;
    overflow-x: hidden;
    margin: 16px 16px 0px 0px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: #fff;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    &.containerCardFull {
      position: fixed !important;
      top: 32px;
      z-index: 2005;
      height: calc(100% - 50px);
      max-height: calc(100% - 50px);
      width: 100%;
      left: 0px;
      width: 100%;
      margin-left: 0px !important;
      .chatScroll {
        max-height: calc(100vh - 220px) !important;
      }
      .optScroll {
        height: calc(100vh - 190px) !important;
        max-height: calc(100vh - 190px) !important;
      }
      .optContentBox {
        height: calc(100vh - 210px) !important;
        max-height: calc(100vh - 210px) !important;
      }
    }

    .optContentBox {
      // height: calc(100vh - 340px);
      max-height: 100%;
      width: 100%;
      position: relative;
      overflow-y: auto;
    }
    .container-list {
      display: flex;
      flex-direction: row;
      padding: 0 20px;
      max-height: calc(100vh - 245px);
      .plan-details {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        width: 80%;
        .optScroll {
          height: 100vh;
        }
      }
      .tools {
        height: 100%;
        width: 20%;
        border-left: 1px solid #e8e8e8;
        padding: 19px 16px 0;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #323233;
          text-align: left;
          font-style: normal;
        }
        .tools-title {
          display: flex;
          margin-bottom: 16px;
        }
        .tools-item {
          display: flex;
          align-items: center;
          padding: 9px 12px;
          border-radius: 2px;
          border: 1px solid #dcdde0;
          margin-bottom: 8px;
          cursor: pointer;
          img {
            width: 16px;
            height: 16px;
          }
          span {
            margin-left: 4px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #323233;
          }
        }
        .swdt,
        .xtfz {
          margin-bottom: 24px;
        }
        .btn {
          color: #4068d4;
        }
        .disabled {
          opacity: 0.4;
          background-color: #f2f3f5 !important;
          color: #4068d4;
          cursor: not-allowed;
        }
      }
      .list-item {
        display: flex;
        flex-direction: column;
        margin: 0px 20px;
        .list-title {
          display: flex;
          color: #4068d4;
          font-size: 14px;
          line-height: 22px;
          margin-bottom: 12px;
          img {
            width: 18px;
            height: 18px;
            margin-top: 2px;
          }
        }
        .list-source {
          display: flex;
          flex-wrap: wrap;
          height: calc(100% - 22px);
          overflow: auto;
          .source-item {
            width: calc(33.33% - 10px);
            background: #f6f7fb;
            margin-bottom: 12px;
            margin-right: 10px;
            padding: 5px;
            &:last-child {
              margin-right: 0;
            }
            .source-tit {
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
              font-weight: 600;
              font-size: 14px;
              color: #323233;
            }
            .source-box {
              display: flex;
              justify-content: space-between;
              .source-sub {
                margin-right: 10px;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; // 默认不换行；
                font-size: 12px;
                line-height: 20px;
                color: #969799;
              }
            }
          }
        }
        .relevance-item {
          height: calc(100% - 30px);
          overflow: auto;
          .relevance-tit {
            width: 100%;
            background: #f6f7fb;
            margin-bottom: 12px;
            padding: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    .optContent {
      position: relative;
      display: flex;
      flex-direction: column;
      flex: 1;
      .optScroll {
        position: relative;
        // max-height: calc(100vh - 450px);
        // max-height: calc(100vh - 600px);
        overflow-y: hidden;
        overflow-x: hidden;
        padding-bottom: 10px;
        display: flex;
        flex-direction: column;
        &.optScrollMini {
          max-height: calc(100vh - 450px);
        }
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .chatFooter {
        position: relative;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .chatInput {
          flex: 1;
          border-radius: 4px;
          ::v-deep .el-textarea__inner {
            border-radius: 4px;
          }
        }
        .send-btn {
          position: absolute;
          right: 30px;
          display: flex;
          flex-direction: row;
          align-items: flex-end;
          // margin-top: 6px;
          font-size: 12px;
          bottom: 10px;
          .yuyinBtn {
            cursor: pointer;
            margin-right: 8px;
            &.yuyinBtnDisabled {
              cursor: not-allowed;
              pointer-events: none;
            }
            img {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }
  }
  .chatRight {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    height: calc(100% - 18px);
    max-height: calc(100% - 18px);
    overflow-y: hidden;
    margin-top: 16px;
    position: relative;
    &.chatRightMini {
      height: calc(100% - 68px);
      max-height: calc(100% - 68px);
    }
    .optContent {
      background: #ffffff;
      &.chatRightFull {
        position: fixed !important;
        top: 52px;
        z-index: 2005;
        height: calc(100% - 50px);
        width: 100%;
        height: 100vh;
        left: 0px;
        margin-left: 0px !important;
      }
      .bottomScroll {
        height: calc(100% - 47px);
        padding: 10px;
        overflow-x: hidden;
        overflow-y: auto;
        .optContentBox {
          height: 100%;
        }
      }
    }
  }
}
.topResize {
  cursor: row-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  height: 10px;
  width: 100%;
  color: #c3cadd;
  display: flex;
  flex-direction: row;
  align-items: center;
  &:hover {
    background: #e0e6ff;
    .process-icon {
      color: #3455ad !important;
    }
  }
  .el-two-column__icon-top {
    width: 50%;
    height: 4px;
    display: flex;
    flex-direction: row-reverse;
    .el-two-column__icon-top-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(right, #d5dbed, #e6eafb) no-repeat;
    }
  }
  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }
  .el-two-column__trigger-icon_shu {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      height: 10px;
      color: #c3cadd;
    }
  }
  .el-two-column__icon-bottom {
    width: 50%;
    height: 4px;
    .el-two-column__icon-bottom-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(left, #d5dbed, #e6eafb) no-repeat;
    }
  }
}
.resize {
  cursor: col-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  width: 10px;
  color: #c3cadd;
  display: flex;
  flex-direction: column;
  align-items: center;
  &:hover {
    background: #e0e6ff;
    .process-icon {
      color: #3455ad !important;
    }
  }
  .el-two-column__icon-top {
    height: 50%;
    width: 4px;
    display: flex;
    flex-direction: column-reverse;
    .el-two-column__icon-top-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
    }
  }
  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }
  .el-two-column__icon-bottom {
    height: 50%;
    width: 4px;
    .el-two-column__icon-bottom-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
    }
  }
}
.optFooter {
  position: fixed;
  bottom: 0px;
  left: 0px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 20px;
  min-height: 54px;
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 16px;
  border-radius: 2px;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
::v-deep .el-button--primary.is-plain {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    font-size: 10px;
  }
}
</style>
