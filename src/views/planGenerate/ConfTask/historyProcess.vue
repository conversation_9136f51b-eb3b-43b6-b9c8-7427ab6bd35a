<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="历史记录"
      :visible.sync="showFlag"
      :before-close="onClose"
      :append-to-body="true"
      width="50%"
    >
    <div v-if="historyChatMessage.length>0" ref="chatBox" class="chatScroll" >
        <div v-for="(item, index) in historyChatMessage" :key="item.id">
          <div v-if='item.scheme_detail' class="gptAnsWrap">
            <div class="gptAvator" style="margin-right: 6px">
              <!-- <img v-if="item.agent_role_id && agentAvatorInfoMap[item.agent_role_id]?.icon" :src="agentAvatorInfoMap[item.agent_role_id]?.icon"/>
              <img v-else-if="agentAvatorInfo.icon" :src="agentAvatorInfo.icon"/> -->
              <img src="@/assets/images/planGenerater/chat-icon.png"/>
            </div>
            <div style="flex: 1;padding-bottom:14px; border-bottom: 1px solid #EBECF0;">
                <div class="user">
                  <span class="alias">智伴</span>
                  <span class="time">{{item.create_date}} </span>
                </div>
              <div class="gptAnsBox">
                <div class="ansUse">
                  <div :id="'gptAns'+index" class="gptAns" :style="{maxWidth: 'calc(100% - 50px)'}">
                    <MyEditorPreview :id="item.id + index + ''" :ref="item.id + index" :md-content="item.scheme_detail" :class="getHeightClass('gptAns'+index) && item.isExpand ? 'maxHeightBox':'heightBox'"></MyEditorPreview>
                  </div>
                  <el-button type="info" size="mini" @click="useFn(item.scheme_detail)">使用</el-button>
                </div>
              </div>
              <div class="think-wrap">
                <div :id="'thinkBtn'+index" class="think-btn"  @click="expandFn(item, index)">
                  {{  item.isExpand? '展开':'收起'}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else style="height: 400px;max-height: 400px;overflow-y: auto;">
        <el-empty description="暂无历史记录"></el-empty>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  schemeDetailHistory,
  PlanTaskEdit
} from '@/api/planGenerateApi.js';
import MyEditorPreview from '../mdEditorPreview.vue';
export default {
  name: 'ModelDialog',
  components: {
    MyEditorPreview
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    historyData:{
      type:Array,
      default:()=>{
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      showFlag: false,
      title: '',
      agentAvatorInfo: {}, // 角色基本信息
      agentAvatorInfoMap: {}, // 角色信息map
      historyChatMessage: []
    };
  },
  computed: {
  },
  watch: {
    isVisible: {
      handler: async function(val){
        if (val) {
          await this.getData()
          this.$nextTick(()=>{
            this.showFlag = val
          })

        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },

  },
  methods: {
    getData(){
      schemeDetailHistory({
        scheme_id: this.$route.query.id,
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          console.log(res.data.result)
          this.historyChatMessage =res.data.result.map(item=>{
            return {
              ...item,
              isExpand:true
            }
          })
          this.$nextTick(() => {
            this.historyChatMessage.forEach((item, index) => {
            const el = document.getElementById('gptAns'+index)
            const height = el ? el.clientHeight : 0
            if(height > 120){
              document.getElementById('thinkBtn'+index)?.classList.add('showExp');
            } else {
              document.getElementById('thinkBtn'+index)?.classList.remove('showExp');
            }
          })
          })

        }
      }).catch((e)=>{
        this.$message({
            type: 'error',
            message: '查询失败!'
          });
      });
    },
    async useFn(val){
      const res = await PlanTaskEdit({
        file_url:'',
        publish_ability:'',
        sub_content:'',
        text:val,
        scheme_id: this.$route.query.id
      });
      if(res?.data?.code !== 200){
        this.$message.error(res?.data?.msg || '编辑失败');
        return;
      }
      this.$message.success('编辑成功');
      this.$emit('updateData', val)
      this.onClose()
    },
    getHeightClass(id) {
      const el = document.getElementById(id)
      const height = el ? el.clientHeight : 0
      return height > 120
    },
    expandFn(item,index){
      if (item.isExpand) {
        console.log(item.isExpand,'ppooo')
        document.getElementById('gptAns'+index)?.classList.add('showAll');
      } else {
        document.getElementById('gptAns'+index)?.classList.remove('showAll');
      }
      item.isExpand = !item.isExpand

    },
    onClose() {
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
  .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  .chatScroll  {
    // max-height: calc(100vh - 320px);
    flex: 1;
    overflow-y: auto;
    padding: 0px 16px 16px;
    transition: all 0.3s ease-out;
    overflow-x: hidden;
    padding-top: 16px;
    .gptAnsWrap {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 18px;
      max-width: 100%;
      .gptAvator {
        width: 34px;
        height: 34px;
        margin-right: 3px;
        img {
          width: 34px;
          height: 34px;
        }
      }
      .user{
        .agent-mark{
          background: #eff3ff;
          border-radius: 2px;
          padding:3px 8px;
          color: #4068d4 !important;
          font-size: 12px;
          margin: 0 10px;
          max-width: 260px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .time{
          font-size: 12px;
        }
      }
      .gptAnsBox{
        margin-top: 5px;
        .ansUse{
          width: 100%;
          display: flex;
          justify-content:space-between;
          .el-button--mini{
            width: 40px;
            height: 24px;
            border-radius: 2px;
            text-align: center;
            background: #fff;
            border: 1px solid rgba(64,107,212,0.4);
            padding:0;
            span{
              font-size: 12px;
            }
            &:hover{
              background: #EBEEF5;
            }
          }
        }

      }
      .gptAns {
        background: #F6F8FB;
        border-radius: 4px;
        color: #323232;
        line-height: 20px;
        position: relative;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        max-height: 130px;
        &.showAll {
          max-height: 100% !important;
        }
        &.gptUser {
          background: rgba(194, 210, 255, 0.56);
        }
      }
      .gptAns2 {
        background: #F6F8FB;
        border-radius: 6px;
        color: #4068D4;
        line-height: 20px;
        position: relative;
        //padding: 8px 12px;
        display: flex;
        align-items: center;
      }
      .think-wrap  {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        cursor: pointer;
        margin-top: 8px;
        // margin-left: 37px;
        .think-btn {
          border-radius: 2px;
          border: 1px solid rgba(64,107,212,0.4);
          font-size: 12px;
          padding: 2px 8px;
          font-weight: 500;
          color: #4068D4;
          line-height: 18px;
          margin-right: 8px;
          word-break: keep-all;
          display: none;
          &.showExp {
            display: block !important;
          }
          &:hover {
            background: #F6F7FB;
          }
          &:active{
            background: #EFF3FF;
          }
          &.think-btn-disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .maxHeightBox .v-md-editor .scrollbar__view{
    overflow: hidden;
    max-height:120px;
  }
  .heightBox .v-md-editor .scrollbar__view{
    height:100%;
    overflow: auto;
  }
}
</style>
