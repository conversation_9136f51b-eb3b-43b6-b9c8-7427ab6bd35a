<template>
  <div class="bg" v-loading="isLoading">
    <div class="dialog-title">
      <div class="bg_bt" style="width: calc(35% - 28px);">
        <div style="display:flex;    align-items: center;">
        <div class="jiantou" v-if="$route.query.backStatus"
          @click="() => $router.push({ path: '/planGenerate/ConfTaskPlanchat', query: { ...$route.query } })"><i
            class="el-icon-arrow-left"></i><span>返回</span>
        </div>
        <div class="title-text">执行任务（{{ $route.query.id }}）</div>
        </div>
        <el-button type="info" :loading="loading" :disabled="(getTaskByRunCode(CODE_DEPLOY)?.status !== 1 &&
            flowData.filter((item) => item.status === 3).length)
            " @click="reStart">重试</el-button>
      </div>

      <div v-if="currentDetailrunCode" class="header-title">
        <div style="font-weight: bold">
          <div class="flex_body" v-if="getTaskByRunCode(currentDetailrunCode)?.status != 0">
              <el-tabs v-model="activeName">
                  <el-tab-pane label="日志追踪" name="first"></el-tab-pane>
                  <el-tab-pane label="文件" name="second"></el-tab-pane>
              </el-tabs>
        </div>
        </div>
        <div class="editorMark">
          <div>
            <div v-if="!getTaskByRunCode(currentDetailrunCode)?.is_universal">
              <el-button v-if="shouldShowSaveButton" :disabled="isSaveButtonDisabled" type="primary"
                @click="handleSave">保存</el-button>
              <el-button v-if="shouldShowEditButton" :disabled="isEditButtonDisabled" type="primary"
                @click="editJsonMark(currentDetailrunCode)">编辑</el-button>
              <el-button v-if="shouldShowCancelButton" type="info"
                @click="cancelEditJsonMark(currentDetailrunCode)">取消</el-button>
            </div>

            <!--通用编辑保存  -->
            <div v-else>
              <el-button
                v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown_CanEdit' && !CanEdit"
                :disabled="isEditButtonDisabledNew" type="primary" @click="handleSaveNew">保存</el-button>
              <el-button
                v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown_CanEdit' && CanEdit"
                :disabled="isEditButtonDisabledNew" type="primary"
                @click="editJsonMarkNew(currentDetailrunCode)">编辑</el-button>
              <el-button
                v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown_CanEdit' && !CanEdit"
                type="info" @click="cancelEditJsonMarkNew(currentDetailrunCode)">取消</el-button>
                <el-button  type="info" v-if="testAbilityConfig && getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodeCreateWithTest'"
                :disabled="flowData.some((item) => item.status !== 1)"
                @click="copyRedoTask">代码测试</el-button>
            </div>
            <!-- <el-button v-if="currentDetailrunCode == 'DECISION_MAKING_GENERATE'" type="info"
              @click="cancelEditJsonMark(currentDetailrunCode)">代码测试</el-button> -->
             <el-tooltip class="item" effect="dark" content="进入聚焦模式" placement="top">
              <div class="chuanzuozhe" @click="openJJ">
                <SvgIcon name="focus" class="menu-icon" style="height: 24px;width: 24px;"></SvgIcon>
              </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="下载代码" placement="top-start" v-if="shouldShowDownloadButton">
              <el-button style="margin: 0 8px;padding: 4px;" icon="el-icon-download" type="info" circle
                @click="downloadCode(currentDetailrunCode)"></el-button>
            </el-tooltip>
          </div>
        </div>
        <!-- <el-link :underline="false" icon="el-icon-close" type="primary" @click="setRightShow"></el-link> -->
      </div>
    </div>
    <div class="task-container">

      <div class="task-left" :style="!isHideIframe ? 'height: calc( 100vh - 269px )' : 'height: calc( 100vh - 221px )'">
        <div class="task-list" v-if="flowData.length > 0">
           <div v-for="(item, index) in flowData" :key="index" class="task-list-item">
            <div class="task-icon">
              <SvgIcon v-if="item.status !== 3" :name="Seticon(item.task_desc, item.status, item.icon_code)" />
              <div v-else-if="item.status === 3" class="loading-task">
                <i class="el-icon-refresh-right"></i>
              </div>
              <div v-if="index !== flowData.length - 1" :class="item.status === 0 || !item.status
                ? 'task-line task-line-info'
                : item.status === 2
                  ? 'task-line task-line-error'
                  : 'task-line'
                "></div>
            </div>
            <div class="task-text-wrap">
              <div :class="item.runCode == currentDetailrunCode ? 'task-text ac-task-text' : 'task-text'">
                <div :class="item.runCode == currentDetailrunCode ? 'task-text-title acTitle' : 'task-text-title'"
                  @click="changeCurrentRunCode(item.runCode)">
                  {{ item.title }}
                </div>
                <div class="task-text-link">
                  <el-link v-if="item.status === 3" type="primary" :underline="false"
                    @click="stopGen(index)">停止生成</el-link>
                  <el-tooltip class="item" effect="dark" content="重试" placement="top">
                    <el-link v-if="
                      item.status === 1 ||
                      item.status === 2 ||
                      (flowData.filter((titem) => titem.status === 0).length ===
                        flowData.length &&
                        index === 0) ||
                      flowData[flowData.length - 1].status === 1
                    " icon="el-icon-refresh" :underline="false" style="padding-left: 8px"
                      @click="redoTask(item.runCode)"></el-link>
                  </el-tooltip>

                  <el-tooltip class="item" effect="dark" :content="isExpanded(item.runCode) ? '收起' : '展开'"
                    placement="top">
                    <el-link :icon="isExpanded(item.runCode) ? 'el-icon-arrow-right' : 'el-icon-arrow-down'
                      " :underline="false" style="padding-left: 8px" @click="toggleFold(item.runCode)"></el-link>
                  </el-tooltip>
                </div>
              </div>
              <div :class="['task-text-process', isExpanded(item.runCode) ? 'show' : '']">
                <div v-if="!item?.is_universal">
                  <div v-if="item.runCode === SCHEME_OPTIMIZE">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="item.process || '暂无'"></MyEditorPreview>
                    <!-- schemeOptimizeProcessData -->
                  </div>
                  <div v-else-if="item.runCode === CODE_DEPLOY_NEW">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="item.process || '暂无'"></MyEditorPreview>
                    <!-- schemeOptimizeProcessData -->
                  </div>
                  <!-- 新测试 -->
                  <div v-else-if="item.runCode === CODE_CUSTOM_TEST">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="item.process || '暂无'"></MyEditorPreview>
                    <!-- schemeOptimizeProcessData -->
                  </div>
                  <div v-else-if="item.runCode === DECISION_TREE">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="treeDataProcess || item.process || '暂无'"></MyEditorPreview>
                  </div>
                  <div v-else-if="item.runCode === ALIGN_ANALYSIS">
                    <dataAnsProcess :run-stream-list="runStreamList" :all-success="allSuccess"></dataAnsProcess>
                  </div>
                  <div v-else-if="item.runCode === MODEL_PARAM_EXTRACTION">
                    <pre style="white-space: pre-line; margin-bottom: -20px" class="max_height">
                      {{
                        item.process.replaceAll('**', '').replaceAll('`', '') ||
                        item.process
                      }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === MODELING_INFO_STRUCTURE">
                    <pre style="white-space: pre-line; margin-bottom: -20px" class="max_height">
                      {{ item.process }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === MATH_MODEL_GENERATE">
                    <pre style="white-space: pre-line" class="max_height" id="toplog">
                      {{ item.process }}
                    </pre>
                  </div>
                  <div v-else-if="item.runCode === ALIGN_DATA_GENERATE">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="alignProcessData || item.process || '暂无'"></MyEditorPreview>
                    <!-- {{ alignProcessData || item.process }} -->
                  </div>
                  <div v-else-if="item.runCode === DECISION_MAKING_GENERATE">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="codeProcessData || item.process || '暂无'"></MyEditorPreview>
                  </div>
                  <div v-else-if="item.runCode === CODE_DEPLOY">
                    {{ item.status === 1 ? '部署成功' : '暂无' }}
                  </div>
                  <!-- <div v-else-if="item.runCode === CODE_DEPLOY_NEW">
                    {{ item.status === 1 ? '部署成功' : '暂无' }}
                  </div> -->
                  <div v-else-if="item.runCode === CODE_TEST || item.runCode === ABILITY_CHECK">
                    {{ item.status === 1 ? '检查成功' : '暂无' }}
                  </div>
                  <div v-else-if="item.runCode === CODE_ANALYSIS">
                    <MyEditorPreview :id="'MyEditorTree' + item.runCode" :ref="'MyEditorTree' + item.runCode"
                      :autoScroll="true" :md-content="codeAnalysisProcessFirst || item.process || '暂无'">
                    </MyEditorPreview>
                    <!-- {{ codeAnalysisProcessFirst || item.process }} -->
                  </div>

                  <div v-else>暂无</div>
                </div>
                <div v-else>
                  <MyEditorPreview :autoScroll="true" class="max_height"
                    v-if="getTaskByRunCode(item.runCode)?.process_message_component == 'EnnMarkdown' || getTaskByRunCode(item.runCode)?.process_message_component == 'MyEditorPreview' || getTaskByRunCode(item.runCode)?.process_message_component == 'div'"
                    :id="'MyEditorTree' + item.execute_instruction" :ref="'MyEditorTree' + item.execute_instruction"
                    :md-content="getTaskByRunCode(item.runCode)?.process || '暂无'"></MyEditorPreview>
                  <!-- <pre style="white-space: pre-line; margin-bottom: -20px" class="max_height" v-else-if="getTaskByRunCode(item.runCode)?.process_message_component == 'div'">
                      {{ getTaskByRunCode(item.runCode)?.process }}
                    </pre> -->
                  <div v-else>暂无</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lay-out" :class="!showLeft ? 'showRight task-right' : 'task-right'"
        :style="!isHideIframe ? 'height: calc( 100vh - 269px )' : 'height: calc( 100vh - 221px )'" v-if="activeName == 'first' || getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodeCreateWithTest' ">
        <div v-loading="childLoading" class="task-show"
          v-if="(getTaskByRunCode(currentDetailrunCode)?.is_universal == false)">

          <MyEditorDialog v-if="currentDetailrunCode === SCHEME_OPTIMIZE" id="MyEditorDialog" ref="MyEditorDialog"
            :md-content="getTaskByRunCode(SCHEME_OPTIMIZE).summarize" :el-box="SCHEME_OPTIMIZE"
            :status="getTaskByRunCode(SCHEME_OPTIMIZE)?.status"></MyEditorDialog>
          <!-- 新部署 -->
          <MyEditorDialog v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown'"
            id="MyEditorDialog" ref="MyEditorDialog" :md-content="getTaskByRunCode(currentDetailrunCode).summarize"
            :el-box="CODE_DEPLOY_NEW" :status="getTaskByRunCode(currentDetailrunCode)?.status"></MyEditorDialog>
          <!-- 新测试 -->
          <MyEditorDialog v-if="currentDetailrunCode === CODE_CUSTOM_TEST" id="MyEditorDialog" ref="MyEditorDialog"
            :md-content="getTaskByRunCode(CODE_CUSTOM_TEST).summarize" :el-box="CODE_CUSTOM_TEST"
            :status="getTaskByRunCode(CODE_CUSTOM_TEST)?.status"></MyEditorDialog>
          <TreeMind v-if="currentDetailrunCode === DECISION_TREE" :tree-data-val="treeDataVal" :tree-status="treeStatus"
            :status="getTaskByRunCode(DECISION_TREE)?.status"></TreeMind>
          <DataAns v-if="currentDetailrunCode === ALIGN_ANALYSIS" :run-stream-list="runStreamList"
            :all-success="allSuccess" :template-id="templateId" :tree-data-val="treeDataVal" :formData="formData"
            :status="getTaskByRunCode(ALIGN_ANALYSIS)?.status" :dataAnsTask="getTaskByRunCode(ALIGN_ANALYSIS)"
            @e-updateAllsuccess="handleUpdateAllsuccess"
            :scheme_detail_optimizeData="getTaskByRunCode(SCHEME_OPTIMIZE)?.summarize">
          </DataAns>
          <codeJsonEdit v-if="currentDetailrunCode === MODEL_PARAM_EXTRACTION" :key="MODEL_PARAM_EXTRACTION"
            :ref="MODEL_PARAM_EXTRACTION" :tree-data-val="getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize"
            language-type="markdown" :stop-scroll="customStopScrollTobottom">
          </codeJsonEdit>
          <codeJsonEdit v-if="currentDetailrunCode === MODELING_INFO_STRUCTURE" :ref="MODELING_INFO_STRUCTURE"
            :key="MODELING_INFO_STRUCTURE" :tree-data-val="getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize"
            language-type="markdown" :stop-scroll="customStopScrollTobottom">
          </codeJsonEdit>
          <codeJsonEdit v-if="currentDetailrunCode === MATH_MODEL_GENERATE" :key="MATH_MODEL_GENERATE"
            :ref="MATH_MODEL_GENERATE" :tree-data-val="getTaskByRunCode(MATH_MODEL_GENERATE).summarize"
            :data-align-data-status="mathModelGenerateStatus" :el-box="MATH_MODEL_GENERATE" language-type="markdown"
            :stop-scroll="customStopScrollTobottom">
          </codeJsonEdit>
          <DataAlign v-if="currentDetailrunCode === ALIGN_DATA_GENERATE" :ref="ALIGN_DATA_GENERATE"
            :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
            :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-status="treeStatus"
            :data-align-data-status="dataAlignDataStatus" :data-align-data="dataAlignData"
            :status="getTaskByRunCode(ALIGN_DATA_GENERATE)?.status" @e-reExecTaskAfter="reExecTaskAfter"></DataAlign>
          <CodeCreate :ref="DECISION_MAKING_GENERATE" :key="DECISION_MAKING_GENERATE"
            v-if="currentDetailrunCode === DECISION_MAKING_GENERATE" :code-data="codeData"
            :code-data-status="codeDataStatus" :status="getTaskByRunCode(DECISION_MAKING_GENERATE)?.status"
            :process-status="processStatus" :schemeStatus="schemeStatus"></CodeCreate>
          <CodeDeploy v-if="currentDetailrunCode === CODE_DEPLOY" :update-develop-flag="updateDevelopFlag"
            :get-task-by-run-code="getTaskByRunCode" :currentDetailrunCode="currentDetailrunCode"
            :status="getTaskByRunCode(CODE_DEPLOY)?.status" :schemeStatus="schemeStatus" @handleOk="handleDevelopOk">
          </CodeDeploy>
          <!-- <CodeDeploy v-if="currentDetailrunCode === CODE_DEPLOY_NEW" :update-develop-flag="updateDevelopFlag"
              :status="getTaskByRunCode(CODE_DEPLOY_NEW)?.status" :schemeStatus="schemeStatus" @handleOk="handleDevelopOkNEW">
            </CodeDeploy> -->
          <CodeTest v-if="currentDetailrunCode === CODE_TEST || currentDetailrunCode === ABILITY_CHECK"
            :update-test-flag="updateTestFlag"
            :status="getTaskByRunCode(CODE_TEST)?.status || getTaskByRunCode(ABILITY_CHECK)?.status"
            :schemeStatus="schemeStatus" @handleOk="handleTestOk" :get-task-by-run-code="getTaskByRunCode"
            :currentDetailrunCode="currentDetailrunCode" :summarize="getTaskByRunCode(ABILITY_CHECK)?.summarize"
            :runCode="currentDetailrunCode">
          </CodeTest>
          <CodeAns :ref="CODE_ANALYSIS" :key="CODE_ANALYSIS" v-if="currentDetailrunCode === CODE_ANALYSIS"
            :tree-process-val="codeAnalysisData" :tree-process-val-first="codeAnalysisProcessFirst"
            :schemeStatus="schemeStatus" :status="getTaskByRunCode(CODE_ANALYSIS)?.status"></CodeAns>
        </div>
        <div
         v-loading="childLoading" class="task-show" v-else>
         <EnnJson v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnJson'"
            :data-val="getTaskByRunCode(currentDetailrunCode).summarize"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"
            :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"></EnnJson>
         <EnnIframe v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnIframe'"
            :data-val="getTaskByRunCode(currentDetailrunCode).summarize"
            :tree-status="getTaskByRunCode(currentDetailrunCode)?.treeStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"
            :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"></EnnIframe>
          <TreeMind v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'TreeMind'"
            :tree-data-val="getTaskByRunCode(currentDetailrunCode).summarize"
            :tree-status="getTaskByRunCode(currentDetailrunCode)?.treeStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"
            :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"></TreeMind>
          <CodeCreateNew :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"
            v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodeCreate'"
            :code-data="getTaskByRunCode(currentDetailrunCode).summarize" :code-data-status="codeDataStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status" :process-status="processStatus"
            :schemeStatus="schemeStatus"></CodeCreateNew>
          <CodeCreateNew :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"
            v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodePreview'"
            :code-data="getTaskByRunCode(currentDetailrunCode).summarize" :code-data-status="codeDataStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status" :process-status="processStatus"
            :schemeStatus="schemeStatus"></CodeCreateNew>
          <CodeCreateNew :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"
            v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown_CanEdit'"
            :code-data="getTaskByRunCode(currentDetailrunCode).summarize" :code-data-status="codeDataStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status" :process-status="processStatus"
            :schemeStatus="schemeStatus"></CodeCreateNew>
          <DataAlign v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'DataAlign'"
            :ref="getTaskByRunCode(currentDetailrunCode)?.runCode" :agent-sence-code="schemeInfo.agent_scene_code"
            :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
            :tree-status="treeStatus" :data-align-data-status="dataAlignDataStatus" :data-align-data="dataAlignData"
            :status="getTaskByRunCode(currentDetailrunCode)?.status" @e-reExecTaskAfter="reExecTaskAfter"></DataAlign>
          <CodeAns :ref="getTaskByRunCode(currentDetailrunCode)?.runCode"
            v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodeAns'"
            :tree-process-val="getTaskByRunCode(currentDetailrunCode).summarize"
            :tree-process-val-first="codeAnalysisProcessFirst" :schemeStatus="schemeStatus"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"></CodeAns>
          <MyEditorDialog v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'pre' ||
            getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'EnnMarkdown' ||
            getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'MyEditorPreview'"
            id="MyEditorDialog" ref="MyEditorDialog" :md-content="getTaskByRunCode(currentDetailrunCode).summarize"
            :el-box="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"></MyEditorDialog>
          <MyEditorDialog v-if="!getTaskByRunCode(currentDetailrunCode)?.summarize_message_component"
            :md-content="getTaskByRunCode(currentDetailrunCode).summarize"
            :el-box="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"></MyEditorDialog>
          <CodeCreateWithTest v-if="getTaskByRunCode(currentDetailrunCode)?.summarize_message_component == 'CodeCreateWithTest'"
            :ref="DECISION_MAKING_GENERATE" :key="DECISION_MAKING_GENERATE" :activeName="activeName"
            :code-data-status="codeDataStatus"
            :getTaskByRunCode="getTaskByRunCode" :currentDetailrunCode="currentDetailrunCode"
            :code-data="getTaskByRunCode(currentDetailrunCode).summarize"
            :status="getTaskByRunCode(currentDetailrunCode)?.status"
            :process-status="processStatus"
            :schemeStatus="schemeStatus"
            :codeTestProcess="codeTestText"
            :result_file_root_path="result_file_root_path"
            :type="testAbilityConfig.task_desc">
          </CodeCreateWithTest>
        </div>
      </div>
      <div class="lay-out" :class="!showLeft ? 'showRight task-right' : 'task-right'"
        :style="!isHideIframe ? 'height: calc( 100vh - 269px )' : 'height: calc( 100vh - 221px )'" v-if="activeName == 'second' && getTaskByRunCode(currentDetailrunCode)?.summarize_message_component != 'CodeCreateWithTest' ">
        <CodeFile   ref="codeFileRef" :topHeight="!isHideIframe ? 'calc( 100vh - 269px )' : ' calc( 100vh - 221px )'" :result_file_root_path="result_file_root_path" :activeName="activeName" :name="getTaskByRunCode(currentDetailrunCode)?.title"></CodeFile>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <div v-if="hasChatingName && hasChatresult?.using_user?.nickName">{{hasChatresult?.using_user?.nickName}}({{hasChatresult?.using_user?.username}})</div>
      <div class="refFont" v-if="hasChatingName">当前打造已被占用</div>
      <el-button v-if="hasChatingName" type="info" @click="showKickMessageBox">强制退出</el-button>
      <el-button v-if="
        flowData[flowData.length - 1].status === 1 &&
        flowData.filter((item) => item.status === 1).length === flowData.length && capability_type != 'api_service_development'
      " type="info" :loading="loading" :disabled="loading" @click="onClose('finish')">版本保存</el-button>
      <el-popconfirm v-else-if="flowData.filter((item) => item.status === 3).length" title="是否终止执行当前任务？"
        confirm-button-text="终止" cancel-button-text="取消" @confirm="confirmCancel">
        <el-button slot="reference" style="margin-left: 12px" type="info" :loading="loading"
          :disabled="loading">终止</el-button>
      </el-popconfirm>
    </div>
    <devlopModal :is-visible="devlopModalVisable" :cur-id="curId" :dev-person="devPersonInfo" @close="handleClose" />
  </div>
</template>
<script>
import { mapGetters,mapState, mapMutations } from 'vuex'
import { RunCode } from './constants.js'
import TreeMind from './taskNew/treeData.vue'
import EnnIframe from './taskNew/ennIframe.vue'
import DataAns from './taskNew/dataAns.vue'
import DataAlign from './taskNew/dataAlign.vue'
import CodeCreate from './taskNew/codeCreate.vue'
import CodeCreateNew from './taskNew/codeCreateNew.vue'
import CodeCreateWithTest from './taskNew/CodeCreateWithTest.vue'
import CodeDeploy from './taskNew/codeDeploy.vue'
import CodeTest from './taskNew/codeTest.vue'
import CodeAns from './taskNew/codeAns.vue'
import devlopModal from './devlopModal.vue'
import MyEditorPreview from '../mdEditorPreviewNew.vue'
import codeJsonEdit from './taskNew/codeJsonEdit.vue'
import MyEditorDialog from './taskNew/mdEditorDialog.vue'
import CodeFile from './taskNew/codeFile.vue'
import EnnJson from './taskNew/ennJson.vue'
import {
  getCodeZipBySchemeIdCode,
  startAbilityGenerate,
  reexecuteTask,
  AbilityTest,
  GetDecision,
  AbilityPublish,
  CodeEdit,
  CodeAnalysisEdit,
  updateTasksRes,
  MarketAbilityPublish,
  updateLogTaskStatus,
  updateCom,
  chain_variable_session_upd,
  queryChatIsUseing,
  saveAlignType
} from '@/api/planGenerateApi.js'
import { OnRuleTest } from '@/api/ruleMarketApi.js'
import JSZip from 'jszip';
import dataAnsProcess from './taskNew/dataAnsProcess.vue'
import { safeStringToJSON, isValidJSON, log } from '@/utils/ThrottledLogger.js';
import { saveAs } from 'file-saver';
import twoColumn from "@/components/twoColumn.vue";
import { throttle } from 'lodash'
const {
  KNOWLEDGE_BASE_SEARCH,
  CREATE_QUESTIONS_RESULT,
  SCHEME_OPTIMIZE,
  DECISION_TREE,
  ALIGN_ANALYSIS,
  MODEL_PARAM_EXTRACTION,
  MODELING_INFO_STRUCTURE,
  MATH_MODEL_GENERATE,
  ALIGN_DATA_GENERATE,
  DECISION_MAKING_GENERATE,
  CODE_DEPLOY,
  CODE_DEPLOY_NEW,
  CODE_CUSTOM_TEST,
  CODE_TEST,
  ABILITY_CHECK,
  CODE_ANALYSIS,
  API_PARAM_ANALYSIS
} = RunCode

const iconsMap = {
  [KNOWLEDGE_BASE_SEARCH]: '',
  [CREATE_QUESTIONS_RESULT]: '',
  [SCHEME_OPTIMIZE]: 'shujufenxi',
  [DECISION_TREE]: 'siweishu',
  [ALIGN_ANALYSIS]: 'shujufenxi',
  [MODEL_PARAM_EXTRACTION]: 'siweishu1',
  [MODELING_INFO_STRUCTURE]: 'shujufenxi1',
  [MATH_MODEL_GENERATE]: 'shujuduiqi1',
  [ALIGN_DATA_GENERATE]: 'shujuduiqi',
  [DECISION_MAKING_GENERATE]: 'daimashengcheng',
  [CODE_DEPLOY]: 'daimabushu',
  [CODE_DEPLOY_NEW]: 'daimabushu',
  [CODE_CUSTOM_TEST]: 'daimaceshi',
  [CODE_TEST]: 'daimaceshi',
  [ABILITY_CHECK]: 'daimaceshi',
  [CODE_ANALYSIS]: 'daimafenxi',
  [API_PARAM_ANALYSIS]: ''
}

export default {
  name: 'RunTaskNew',
  components: {
    twoColumn,
    MyEditorPreview,
    MyEditorDialog,
    TreeMind,
    EnnIframe,
    DataAns,
    DataAlign,
    CodeCreate,
    CodeDeploy,
    CodeTest,
    devlopModal,
    CodeAns,
    dataAnsProcess,
    AbilityPublish,
    CodeFile,
    CodeCreateWithTest,
    CodeCreateNew,
    EnnJson,
    codeJsonEdit
  },
  props: {
    result_file_root_path: {
      type: String,
      required: true
    },
   testAbilityConfig: {
    type: [Object, null],
    default: () => null
   },
    title: {
      type: String,
      required: true
    },
    agentSenceCode: {
      type: String,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    schemeStatus: {
      type: String
    },
    hasChatingName: {
      type: String
    },
    schemeInfo: {
      type: Object,
      default: () => { }
    },
    optimizeData: {
      type: String,
      default: ''
    },
    mathModelGenerateStatus: {
      type: [String, Number],
      default: ''
    },
    setModelData: {
      type: Function
    },
    queryBatchTasksResult: {
      type: Function
    },
    modelingInfoStructureStatus: {
      type: Number,
      default: -1
    },
    isVisible: {
      type: Boolean,
      default: false
    },
    // 会话流的唯一id
    sessionId: {
      type: String,
      default: ''
    },
    runStreamList: {
      type: Array,
      default: []
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    templateId: {
      type: [String, Number],
      default: ''
    },
    dataAlignDataStatus: {
      type: [String, Number],
      default: ''
    },
    dataAlignData: {
      type: Array,
      default: []
    },
    codeDataStatus: {
      type: [String, Number],
      default: ''
    },
    codeData: {
      type: String,
      default: ''
    },
    updateDevelopFlag: {
      type: [String, Number],
      default: 1
    },
    flowData: {
      type: Array,
      required: true
    },
    devPerson: {
      type: Object,
      default() {
        return {}
      }
    },
    processStatus: {
      type: [String, Number],
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    planStatus: {
      type: [String, Number],
      default: -1
    },
    planStatusNewBuShu: {
      type: [String, Number],
      default: -1
    },
    planStatusNewTest: {
      type: [String, Number],
      default: -1
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    modelParamExtractionStatus: {
      type: Number,
      default: -1
    },
    codeAnalysisData: {
      type: String,
      default: ''
    },
    codeAnalysisDataStatus: {
      type: String | Number,
      default: ''
    },
    codeAnalysisProcessData: {
      type: String,
      default: ''
    },
    schemeOptimizeProcessData: {
      type: String,
      default: ''
    },
    alignProcessData: {
      type: String,
      default: ''
    },
    codeProcessData: {
      type: String,
      default: ''
    },
    getTaskByRunCode: {
      type: Function,
      required: true
    },
    updateTaskStatus: {
      type: Function,
      required: true
    },
    getTasksBefore: {
      type: Function,
      required: true
    },
    updateTaskStatusesBefore: {
      type: Function,
      required: true
    },
    getTasksAfter: {
      type: Function,
      required: true
    },
    updateTaskStatusesAfter: {
      type: Function,
      required: true
    },
    updatePreviousTaskStatus: {
      type: Function,
      required: true
    },
    updateNextTaskStatus: {
      type: Function,
      required: true
    },
    getNextTask: {
      type: Function,
      required: true
    },
    getPreviousTask: {
      type: Function,
      required: true
    },
    capability_type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hasChatresult:{},
      activeName:'first',
      codeTestProcess:'',
      totalWidth: 1000,
      leftHeight: '100%',
      rightWidth: '30%',
      isDragging: false,
      startY: null,
      startHeight: null,
      showRight: true,
      childLoading: false,
      CanEdit: true,
      isLoading: false,
      ...RunCode,
      EditTemp: '',
      isreadOnly: true,
      customStopScrollTobottom: false,
      fullSizeFlag: false,
      currentDetailrunCode: '',
      loading: false,
      showFlag: false,
      devlopModalVisable: false,
      updateTestFlag: 1, // 触发更新代码测试的
      devPersonInfo: {},
      curId: '',
      foldMap: {},
      detailIndex: -1,
      codeAnalysisProcessStatus: false,
      codeAnalysisProcessFirst: '',
      ploading: false,
      treeData: '',
      treeDataProcess: '',
      taskLoading: false,
      showLeft: false,
    }
  },
  computed: {
   ...mapState('operations', ['codeTestText', 'codeTestStatus']),
   // 过滤包含 code_test 的任务
  filteredFlowData() {
    // 判断是否存在 code_test 任务
    const hasCodeTest = this.flowData.some(item => item.is_hide == true)

    // 如果存在则过滤，否则返回原数组
    return hasCodeTest
      ? this.flowData.filter(item => item.task_desc !== 'code_test')
      : this.flowData
  },
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },
    allCompleted() {
      const filter = this.flowData.filter((item) => item.status === 3)
      return !(filter.length > 0)
    },
    currentTask() {
      return this.getTaskByRunCode(this.currentDetailrunCode)
    },
    shouldShowSaveButton() {
     console.log('shouldShowSaveButton',this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component,this.isreadOnly)
      if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate' || this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeAns') {
        return !this.isreadOnly
      }
      return (
        this.currentDetailrunCode === this.ALIGN_ANALYSIS ||
        this.currentDetailrunCode === this.ALIGN_DATA_GENERATE ||
        (this.currentDetailrunCode === this.DECISION_MAKING_GENERATE && !this.isreadOnly) ||
        (this.currentDetailrunCode === this.CODE_ANALYSIS && !this.isreadOnly)
      )
    },
    shouldShowEditButton() {
      // 代码生成 //能力参数分析
      if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate' || this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeAns') {
        return ([1, 2].includes(this.currentTask?.status) &&
          this.isreadOnly) ||
          (this.isreadOnly)
      }
      return (
        (this.currentDetailrunCode === this.DECISION_MAKING_GENERATE &&
          [1, 2].includes(this.currentTask?.status) &&
          this.isreadOnly) ||
        (this.currentDetailrunCode === this.CODE_ANALYSIS && this.isreadOnly)
      )
    },
    shouldShowCancelButton() {
      // 代码生成 //能力参数分析
      if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate' || this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeAns') {
        return !this.isreadOnly
      }
      return (
        [this.DECISION_MAKING_GENERATE, this.CODE_ANALYSIS].includes(this.currentDetailrunCode) &&
        !this.isreadOnly
      )
    },
    shouldShowDownloadButton() {
      if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate') {
        return [1].includes(this.currentTask?.status)
      }
      return [this.DECISION_MAKING_GENERATE].includes(this.currentDetailrunCode) && [1].includes(this.currentTask?.status)
    },
    isSaveButtonDisabled() {
      if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate' || this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeAns') {
        return (
          this.currentTask?.status === 3
        )
      }
      return (
        this.currentDetailrunCode === this.ALIGN_DATA_GENERATE && this.currentTask?.status !== 1 ||
        (this.currentDetailrunCode === this.ALIGN_ANALYSIS && ((this.currentTask?.status === 0 || this.currentTask?.status === 3) || !this.templateId))
      )
    },
    isEditButtonDisabled() {
      return this.shouldShowEditButton && this.currentTask?.status === 3
    },
    isEditButtonDisabledNew() {
      return this.currentTask?.status === 3
    }
  },
  watch: {
    hasChatingName:{
      async  handler(val){
        if(val){
          const res =  await queryChatIsUseing({ scheme_id: this.$route.query.id })
          if(res.data.code == 200){
            this.hasChatresult = res.data.result
          }
        }
      },
      deep:true,
      immediate: true
    },
    currentDetailrunCode: {
      handler(newVal, oldVal) {
       if(newVal === 'code_test') {
        // this.leftHeight = '70%'
       }else {
        // this.leftHeight = '100%'
       }
        this.isreadOnly = true
      }
    },
    alignProcessData: {
      handler(val) {
        console.log(val)
      }
    },
    mathModelGenerateStatus: {
      handler(val) {
        if (
          (Number(val) === 2 || Number(val) === 3) &&
          this.getTaskByRunCode(MATH_MODEL_GENERATE)?.status === 1
        ) {
          if (this.isExpanded[MATH_MODEL_GENERATE]) {
            this.toggleFold(MATH_MODEL_GENERATE) // 合并
          }
        } else {
          if (Number(val) === 1 && this.getTaskByRunCode(MATH_MODEL_GENERATE)?.status === 3) {
            if (this.isExpanded[MODELING_INFO_STRUCTURE]) {
              this.toggleFold(MODELING_INFO_STRUCTURE) // 展开
            }
            this.toggleFold(MATH_MODEL_GENERATE) // 展开
          }
        }
      },
      immediate: true
    },
    modelingInfoStructureStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成11')
          if (this.isExpanded[MODELING_INFO_STRUCTURE]) {
            this.toggleFold(MATH_MODEL_GENERATE)
          }
        }
        if (Number(val) === 1) {
          this.setFold(MODELING_INFO_STRUCTURE, true)
        }
      },
      immediate: true
    },
    isVisible: {
      handler(val) {
        if (val) {
          this.scrollView(1)
          this.showFlag = val
          console.log('isVisible1111111111111111111111', JSON.stringify(val, null, 2))
          this.flowData.forEach((item) => {
            if (item?.status === 3) {
              console.log('这里触发', item)
              this.setFold(item.runCode, true)
            }
          })
        } else {
          this.detailIndex = -1
          this.showFlag = false
        }
      },
      immediate: true
    },
    flowData: {
      handler(newVal) {
        this.currentDetailrunCode = this.initDetailRunCode(newVal);
        requestIdleCallback(() => {
          if (!Array.isArray(newVal)) return;
          console.log("子组件flowData", this.flowData);
          newVal.forEach((item, index) => {
            // 先检查是否已经添加了_watched标记，如果没有，则添加
            if (!item._watched) {
              this.$set(item, "_watched", true);
            }
            // 如果已经添加了_watched标记，则创建监听器
            if (item._watched) {
              this.$watch(
                () => item.status, // 监听 status 的变化
                (newStatus, oldStatus) => {
                  console.log(`RuntaskNew RunCode: ${item.runCode} status changed to: ${newStatus}`);
                  if (newStatus !== oldStatus) {
                    let currentRunCode;
                    newVal.forEach((item, index) => {
                      if (item?.status === 3) {
                        currentRunCode = item.runCode;
                        this.resetFoldMap();
                        this.setFold(item.runCode, true);
                      }
                    });
                    this.currentDetailrunCode = this.initDetailRunCode(newVal);
                    // if (currentRunCode === ALIGN_ANALYSIS && !this.templateId) {
                    //   this.updateTaskStatus(ALIGN_ANALYSIS, 2);
                    // }
                  }
                }
              );
              this.$watch(
                () => item.process, // 监听 status 的变化
                (newStatus, oldStatus) => {
                  console.log(`RuntaskNew RunCode: ${item.runCode} status changed to: ${newStatus}`);
                  if (newStatus !== oldStatus && this.currentRunCode == MATH_MODEL_GENERATE) {
                    let logElement = document.getElementById('toplog');
                    logElement.scrollTop = logElement?.scrollHeight;
                  }
                })
            }
          })
        })
      },
      immediate: true,
      deep: false // 不需要深度监听整个数组
    },
    updateDevelopFlag: {
      handler(val) {
        console.log('触发代码部署', val)
        if (this.currentDetailrunCode !== CODE_DEPLOY && val !== 1) {
          // 如果没有打开代码部署详情，触发代码部署
          this.bushu()
        }
      }
    },
    codeAnalysisData: {
      handler(val) {
      },
      immediate: true
    },
    codeAnalysisProcessData: {
      handler(val) {
        if (val) {
          this.setFold(CODE_ANALYSIS, true)
        }
        this.codeAnalysisProcessFirst = val
      },
      immediate: true
    },
    codeAnalysisDataStatus: {
      handler(val) {
        console.log('代码分析状态状变化2222222', val)
        if (Number(val) === 1) {
          this.ploading = false
          this.codeAnalysisProcessStatus = true
        } else {
          this.codeAnalysisProcessStatus = false
        }
        if (Number(val) === 2 || Number(val) === 3) {
          console.log('代码分析状态状变化3333333', val)
          this.ploading = false
          this.resetFoldMap()
        }
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        console.log('思维树状态变化 treeStatus in runTaskNew.vue', val)
        if (val === 2) {
          console.log('重新生成完成111')
        }
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(DECISION_TREE, true)
        }
      },
      immediate: true
    },
    planStatus: {
      handler(val) {
        console.log('方案优化状态变化 planStatus in runTaskNew.vue', val)
        if (Number(val) === 2) {
          console.log('等于2应该代表方案优化这个任务生成好了')
          if (this.isExpanded(SCHEME_OPTIMIZE)) {
            this.resetFoldMap()
            const nextTask = this.getNextTask(SCHEME_OPTIMIZE)
            if (nextTask) {
              this.setFold(nextTask.runCode, true)
            }
          }
        }
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(SCHEME_OPTIMIZE, true)
        }
      },
      immediate: true
    },
    planStatusNewBuShu: {
      handler(val) {
        console.log('方案优化状态变化 planStatus in runTaskNew.vue', val)
        if (Number(val) === 2) {
          console.log('等于2应该代表方案优化这个任务生成好了')
          if (this.isExpanded(CODE_DEPLOY_NEW)) {
            this.resetFoldMap()
            const nextTask = this.getNextTask(CODE_DEPLOY_NEW)
            if (nextTask) {
              this.setFold(nextTask.runCode, true)
            }
          }
        }
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(CODE_DEPLOY_NEW, true)
        }
      },
      immediate: true,
      deep: true
    },
    planStatusNewTest: {
      handler(val) {
        console.log('方案优化状态变化 planStatus in runTaskNew.vue', val)
        if (Number(val) === 2) {
          console.log('等于2应该代表方案优化这个任务生成好了')
          if (this.isExpanded(CODE_CUSTOM_TEST)) {
            this.resetFoldMap()
            const nextTask = this.getNextTask(CODE_CUSTOM_TEST)
            if (nextTask) {
              this.setFold(nextTask.runCode, true)
            }
          }
        }
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(CODE_CUSTOM_TEST, true)
        }
      },
      immediate: true,
      deep: true
    },
    modelParamExtractionStatus: {
      handler(val) {
        if (val === 2) {
          if (this.isExpanded(MODEL_PARAM_EXTRACTION)) {
            this.resetFoldMap()
            const nextTask = this.getNextTask(MODEL_PARAM_EXTRACTION)
            if (nextTask) {
              this.setFold(nextTask.runCode, true)
            }
          }
        }
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(MODEL_PARAM_EXTRACTION, true)
        }
      },
      immediate: true
    },
    runStreamList: {
      handler(val) {
        console.log('runStreamList 的变化情况', val)
        if (val && this.getTaskByRunCode(ALIGN_ANALYSIS)?.status === 3) {
          this.resetFoldMap()
          this.setFold(ALIGN_ANALYSIS, true)
        }
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        console.log('思维树过程数据', val)
        if (val && this.getTaskByRunCode(DECISION_TREE)?.status === 3) {
          this.resetFoldMap()
          this.setFold(DECISION_TREE, true)
        }
        this.treeDataProcess = val
      },
      immediate: true
    },
    modelParamExtractionProcess: {
      handler(val) {
        console.log('模型参数识别', val)
        if (val && this.getTaskByRunCode(MODEL_PARAM_EXTRACTION)?.status === 3) {
          this.resetFoldMap()
          this.setFold(MODEL_PARAM_EXTRACTION, true)
        }
      },
      immediate: true
    },

    dataAlignDataStatus: {
      handler(val) {
        console.log('数据对齐状态变化', val)
        if (
          (Number(val) === 2 || Number(val) === 3) &&
          this.getTaskByRunCode(ALIGN_DATA_GENERATE)?.status === 1
        ) {
          if (this.isExpanded(ALIGN_DATA_GENERATE)) {
            this.toggleFold(ALIGN_DATA_GENERATE) // 合并
          }
        } else {
          if (Number(val) === 1 && this.getTaskByRunCode(ALIGN_DATA_GENERATE)?.status === 3) {
            console.log('数据对齐中', val)
            if (this.isExpanded(ALIGN_ANALYSIS)) {
              this.toggleFold(ALIGN_ANALYSIS)
            }
            this.setFold(ALIGN_DATA_GENERATE, true) // 展开
          }
        }
      },
      immediate: true
    },
    processStatus: {
      handler(val) {
        console.log('代码生成过程日志', val)
        if (Number(val) === 1) {
          this.resetFoldMap()
          this.setFold(DECISION_MAKING_GENERATE, true)
        }
      },
      immediate: true
    },
    codeDataStatus: {
      handler(val) {
        if (Number(val) === 2 || Number(val) === 3) {
          if (this.isExpanded(DECISION_MAKING_GENERATE)) {
            this.resetFoldMap()
          }
        }
      },
      immediate: true
    }
  },
  async created() { },
  async mounted() {
    if (this.flowData && this.flowData.length > 0) {
      this.initFoldMap(this.flowData)
      this.currentDetailrunCode = await this.initDetailRunCode(this.flowData)
    }
  },
  beforeDestroy() {
   if (this.throttledOnDrag?.cancel) {
      this.throttledOnDrag.cancel()
    }
  },
  methods: {
   ...mapMutations('operations',['setTopHeight']),
   startDrag(event) {
    console.log('vdadjafpsdas',event)
      if (!this.isDragging) {
        this.isDragging = true
        this.startY = event.clientY
        const leftHeight = document.querySelector('.left-content')?.getBoundingClientRect()?.height
        this.startHeight = leftHeight
        this.throttledOnDrag = throttle(this.onDrag, 100) // 100ms间隔
        document.addEventListener('mousemove', this.throttledOnDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaY = event.clientY - this.startY
        const newHeight = this.startHeight + deltaY
        // console.log('heightLeft', heightLeft, this.startHeight );
            // 添加边界检查
        const minHeight = 251
        const maxHeight = window.innerHeight - 269
        this.leftHeight = `${Math.max(minHeight, Math.min(maxHeight, newHeight))}px`
        // this.leftHeight = heightLeft + 'px'
        // this.rightWidth = this.totalWidth - heightLeft + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.throttledOnDrag)
      document.removeEventListener('mouseup', this.stopDrag)
       // 取消未执行的 throttle
    if (this.throttledOnDrag?.cancel) {
      this.throttledOnDrag.cancel()
    }
    },
   handleCodeTest(val) {

   },
    openJJ() {
      let data = {...this.getTaskByRunCode(this.currentDetailrunCode)}
      console.log('currentDetailrunCode', data)
      if(data.title == '代码生成'){
        data.title = '文件'
      }
      this.$store.commit('common/setJujiaoStatus', {
        data: data,
        status:true
      })
      this.$store.commit('common/setTaskJujiaoStatus', {
        status: true,
        data: data
      })
      this.$router.push({ path: '/planGenerate/ConfTaskPlanchat', query: { ...this.$route.query } })
    },
    setRightShow() {
      this.showLeft = true
    },
    showKickMessageBox() {
      this.$confirm(`此操作将强制退出,是否继续?`, '提示', {
        cancelButtonText: '',
        confirmButtonText: '确定',
        type: 'warning',
        customClass: 'my-message-box'
      })
        .then(async () => {
          this.$emit('chatDisconnectFun')
        })
        .catch(() => {
          console.warn('操作异常');
        })
    },
    async downloadCode(runCode) {
      // console.log('下载代码', runCode, this.$refs[DECISION_MAKING_GENERATE].showCodeData)
      const nameRef = this.getTaskByRunCode(this.currentDetailrunCode).runCode
      console.log('下载代码', runCode, this.$refs[nameRef].showCodeData)
      try {
        // const res = await getCodeZipBySchemeIdCode(this.$route.query.id, this.$refs[DECISION_MAKING_GENERATE].showCodeData)
        const res = await getCodeZipBySchemeIdCode(this.$route.query.id, this.$refs[nameRef].showCodeData)
        if (res?.data?.code === 200) {
          const zip = new JSZip()
          for (const item of res.data.result) {
            zip.file(item.name, item.content)
          }
          zip.generateAsync({ type: 'blob' }).then((blob) => {
            saveAs(blob, this.$route.query.id + '.zip');
          });
        } else {
          this.$message({
            type: 'error',
            message: res.data.msg
          });
        }
      } catch (error) {
        console.error('下载代码失败', error)
      }
    },
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    async handleSaveAlignType() {
      try {
        const res = await saveAlignType({
          align_type: {
            table: [],
            status: 1,
            tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
            table: this.formData.chooseData.length && this.getTaskByRunCode(ALIGN_ANALYSIS)?.align_fields.includes('table') ? this.formData.chooseData : null,
            rule_tag_ids: this.formData.chooseData.length && this.getTaskByRunCode(ALIGN_ANALYSIS)?.align_fields.includes('rule_api') ? this.formData.chooseData : null,
            manual_input: this.formData.manual_input,
            ansData: JSON.stringify(this.runStreamList)
          },
          scheme_id: this.$route.query.id
        })
        if (res.status === 200 && res.data.code === 200) {
          this.$message.success('保存成功')
          await this.delay(1000);
        }
        console.log('保存对齐类型 handleSaveAlignType', res)
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存对齐类型失败', error)
      }
    },
    handleUpdateAllsuccess(val) {
      console.log("啥时候执行的啊？00", val);
      this.$emit('e-updateAllsuccess', val)
    },
    async handleSaveNew() {
     console.log('handleSaveNew', this.currentDetailrunCode)
      const nameRef = this.getTaskByRunCode(this.currentDetailrunCode).runCode
      this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly
      this.childLoading = true
      const res = await updateCom({
        scheme_id: this.$route.query.id,
        "task_use_type": "api_generate_ability",
        "biz_type": "general_use",
        "task_name": this.getTaskByRunCode(this.currentDetailrunCode).title,
        "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
        "content": this.$refs[nameRef].showCodeData
      })
      this.childLoading = false
      this.CanEdit = true
      if (res.data.status == 'success') {
        this.$message({
          type: 'success',
          message: '更新成功'
        });
        await chain_variable_session_upd({
          "scheme_id": this.$route.query.id,
          "agent_template_id": this.getTaskByRunCode(this.currentDetailrunCode)?.agent_template_id,
          "name": this.getTaskByRunCode(this.currentDetailrunCode).title,
          "value": this.$refs[nameRef].showCodeData
        })
        await updateLogTaskStatus({
          scheme_id: this.$route.query.id,
          "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
          "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
          task_status: 1
        });
        await this.queryBatchTasksResult()
        let runCode = this.getTaskByRunCode(this.currentDetailrunCode).task_desc
        const taskIndex = this.flowData.findIndex((f) => f.runCode === runCode || f.process_message_code == runCode || f.summarize_message_code == runCode || f.execute_instruction == runCode)
        console.log(this.flowData[taskIndex + 1].task_desc, 'this.flowData[taskIndex + 1].task_desc')
        let code = this.flowData[taskIndex + 1].task_desc
        await this.redoTask(code)
      } else {
        this.$message({
          type: 'error',
          message: '更新失败'
        });
      }
    },
    async handleSave() {
      const nameRef = this.getTaskByRunCode(this.currentDetailrunCode).runCode
      console.log('handleSave', this.currentDetailrunCode, nameRef,this.currentDetailrunCode === ALIGN_ANALYSIS)
      if (this.currentDetailrunCode === this.ALIGN_DATA_GENERATE) {
        this.exeCildhandleDone()
      } else if (this.currentDetailrunCode === ALIGN_ANALYSIS) {
        console.log("this.ALIGN_ANALYSIS", ALIGN_ANALYSIS, this.currentDetailrunCode);
        await this.handleSaveAlignType()
        await this.redoTask(ALIGN_DATA_GENERATE)
      } else if (
        [this.DECISION_MAKING_GENERATE, this.CODE_ANALYSIS].includes(this.currentDetailrunCode) &&
        !this.isreadOnly
      ) {
       console.log('handleSave1'),
        this.updJsonMark(this.currentDetailrunCode)
      }
      else if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeCreate') {
       console.log('handleSave2'),
        CodeEdit({
          scheme_id: this.$route.query.id,
          scheme_status: 'decision_ability',
          text: this.$refs[nameRef].showCodeData
        }).then(async (res) => {
          if (res.data.code === 200) {
            await chain_variable_session_upd({
              "scheme_id": this.$route.query.id,
              "agent_template_id": this.getTaskByRunCode(this.currentDetailrunCode)?.agent_template_id,
              "name": this.getTaskByRunCode(this.currentDetailrunCode).title,
              "value": this.$refs[nameRef].showCodeData
            })
            await updateLogTaskStatus({
              scheme_id: this.$route.query.id,
              "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
              "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
              task_status: 1
            });
            this.$message.success('保存成功')
            await this.queryBatchTasksResult()
            let runCode = this.getTaskByRunCode(this.currentDetailrunCode).task_desc
            const taskIndex = this.flowData.findIndex((f) => f.runCode === runCode || f.process_message_code == runCode || f.summarize_message_code == runCode || f.execute_instruction == runCode)
            console.log(this.flowData[taskIndex + 1].task_desc, 'this.flowData[taskIndex + 1].task_desc')
            let code = this.flowData[taskIndex + 1].task_desc
            await this.redoTask(code)
            // this.setModelData('codeData', this.$refs[nameRef].showCodeData)
            // this.$message.success('保存成功')
            // this.editJsonMark(nameRef)

            // this.$nextTick(() => {
            //   this.updateTaskStatusesAfter(DECISION_MAKING_GENERATE, 0)
            //   const nextTask = this.getNextTask(DECISION_MAKING_GENERATE)
            //   if (nextTask) {
            //     nextTask.status = 3
            //     this.$emit('e-generalExec', nextTask.runCode)
            //     console.log("代码保存后，重新执行后面的内容");
            //   }
            // })
          } else {
            this.$message.error('保存失败')
          }
        })
      }
      else if (this.getTaskByRunCode(this.currentDetailrunCode).summarize_message_component == 'CodeAns') {
       console.log('handleSave3'),
        updateTasksRes({
          scheme_id: this.$route.query.id,
          biz_type: nameRef,
          content: this.$refs[nameRef].showCodeData
        }).then((res) => {
          console.log(res, 'eeeeeeeeeeeeee')
          if (res.data.status === 'success') {
            if (nameRef === MODEL_PARAM_EXTRACTION) {
              this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize = this.$refs[nameRef].showCodeData
            } else if (nameRef === MODELING_INFO_STRUCTURE) {
              this.getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize = this.$refs[nameRef].showCodeData
            } else {
              this.getTaskByRunCode(MATH_MODEL_GENERATE).summarize = this.$refs[nameRef].showCodeData
              // this.setModelData('mathModelGenerateData', this.$refs[nameRef].showCodeData)
            }
            this.$message.success('保存成功')
          }
          this.editJsonMark(nameRef)
        })
      }
    },
    exeCildhandleDone() {
      console.log(
        '执行子组件的方法',
        this,
        this.$refs,
        this.$refs[ALIGN_DATA_GENERATE],
        this.$refs.ALIGN_DATA_GENERATE
      )
      this.$refs[ALIGN_DATA_GENERATE].handleDone()
    },
    reExecTaskAfter(runCode) {
      const nextTask = this.getNextTask(runCode)
      if (nextTask) {
        // reexecuteTask({ scheme_id: this.$route.query.id, task_name: nextTask.runCode })
        this.updateTaskStatus(nextTask.runCode, 3)
        this.$emit('e-generalExec', nextTask.runCode)
        this.updateTaskStatusesAfter(nextTask.runCode, 0)
      }
    },
    // need to check hqw 相关流程的细节
    updJsonMark(nameRef) {
      if (nameRef === DECISION_MAKING_GENERATE) {
       console.log('handleSave222')
        CodeEdit({
          scheme_id: this.$route.query.id,
          scheme_status: 'decision_ability',
          text: this.$refs[nameRef].showCodeData
        }).then(async (res) => {
          if (res.data.code === 200) {
            await chain_variable_session_upd({
              "scheme_id": this.$route.query.id,
              "agent_template_id": this.getTaskByRunCode(this.currentDetailrunCode)?.agent_template_id,
              "name": this.getTaskByRunCode(this.currentDetailrunCode).title,
              "value": this.$refs[nameRef].showCodeData
            })
            await updateLogTaskStatus({
              scheme_id: this.$route.query.id,
              "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
              "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
              task_status: 1
            });
            this.$message.success('保存成功')
            this.isreadOnly = true
            console.log('保存成功')
            await this.queryBatchTasksResult()
            let runCode = this.getTaskByRunCode(this.currentDetailrunCode).task_desc
            const taskIndex = this.flowData.findIndex((f) => f.runCode === runCode || f.process_message_code == runCode || f.summarize_message_code == runCode || f.execute_instruction == runCode)
            console.log(this.flowData[taskIndex + 1].task_desc, 'this.flowData[taskIndex + 1].task_desc')
            let code = this.flowData[taskIndex + 1].task_desc
            await this.redoTask(code)
            // this.setModelData('codeData', this.$refs[nameRef].showCodeData)
            // this.$message.success('保存成功')
            // this.editJsonMark(nameRef)

            // this.$nextTick(() => {
            //   this.updateTaskStatusesAfter(DECISION_MAKING_GENERATE, 0)
            //   const nextTask = this.getNextTask(DECISION_MAKING_GENERATE)
            //   if (nextTask) {
            //     nextTask.status = 3
            //     this.$emit('e-generalExec', nextTask.runCode)
            //     console.log("代码保存后，重新执行后面的内容");
            //   }
            // })
          } else {
            this.$message.error('保存失败')
          }
        })
      } else if (nameRef === CODE_ANALYSIS) {
        let treeProcessData = this.$refs[nameRef].treeProcessData
        let parsedData

        try {
          parsedData = JSON.parse(treeProcessData)
        } catch (error) {
          this.$message.error('输入的数据不是有效的 JSON 格式')
          return
        }
        CodeAnalysisEdit({
          scheme_id: this.$route.query.id,
          scheme_status: 'decision_ability',
          code_params: parsedData
        }).then(async (res) => {
          await chain_variable_session_upd({
            "scheme_id": this.$route.query.id,
            "agent_template_id": this.getTaskByRunCode(this.currentDetailrunCode)?.agent_template_id,
            "name": this.getTaskByRunCode(this.currentDetailrunCode).title,
            "value": this.$refs[nameRef].showCodeData
          })
          if (res.data.code === 200) {
            this.setModelData('codeAnalysisData', treeProcessData)
            this.$message.success('保存成功')
            this.editJsonMark(nameRef)
          } else {
            this.$message.error('保存失败')
          }
        })
        console.log('代码分析的内容', treeProcessData)
      } else {
        updateTasksRes({
          scheme_id: this.$route.query.id,
          biz_type: nameRef,
          content: this.$refs[nameRef].showCodeData
        }).then((res) => {
          console.log(res, 'eeeeeeeeeeeeee')
          if (res.data.status === 'success') {
            if (nameRef === MODEL_PARAM_EXTRACTION) {
              this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize = this.$refs[nameRef].showCodeData
            } else if (nameRef === MODELING_INFO_STRUCTURE) {
              this.getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize = this.$refs[nameRef].showCodeData
            } else {
              this.getTaskByRunCode(MATH_MODEL_GENERATE).summarize = this.$refs[nameRef].showCodeData
              // this.setModelData('mathModelGenerateData', this.$refs[nameRef].showCodeData)
            }
            this.$message.success('保存成功')
          }
          this.editJsonMark(nameRef)
        })
      }
    },
    // 编辑mark方法
    editJsonMark(nameRef) {
      this.EditTemp = ''
      console.log(nameRef, 'nameRef')
      this.$nextTick(() => {
        if ([DECISION_MAKING_GENERATE].includes(nameRef)) {
          this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly
          this.EditTemp = this.$refs[nameRef].showCodeData
        } else if ([CODE_ANALYSIS].includes(nameRef)) {
          this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly
          this.EditTemp = this.$refs[nameRef].treeProcessData
        } else {
          this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly
          this.$refs[nameRef].isEdit = !this.$refs[nameRef].isEdit
        }
      })
      this.isreadOnly = !this.isreadOnly
    },
    // 通用编辑
    editJsonMarkNew(nameRef) {
     console.log(nameRef, 'nameRef')
      this.$refs[nameRef].options.readOnly = !this.$refs[nameRef].options.readOnly
      this.CanEdit = false
    },
    cancelEditJsonMark(nameRef) {
      console.log(nameRef, 'nameRef')
      this.$nextTick(() => {
        if ([DECISION_MAKING_GENERATE].includes(nameRef)) {
          this.$refs[nameRef].options.readOnly = true
          this.$refs[nameRef].showCodeData = this.EditTemp
        } else if ([CODE_ANALYSIS].includes(nameRef)) {
          this.$refs[nameRef].options.readOnly = true
          this.$refs[nameRef].treeProcessData = this.EditTemp
        } else {
          this.$refs[nameRef].isEdit = false
        }
      })
      this.isreadOnly = true
    },
    cancelEditJsonMarkNew(nameRef) {
      this.CanEdit = true
      this.$refs[nameRef].showCodeData = this.getTaskByRunCode(this.currentDetailrunCode).summarize
      this.$refs[nameRef].options.readOnly = true
    },
    handleDevelopvalAdd() {
      this.updateDevelopFlag = Number(this.updateDevelopFlag) + 1
    },
    async handleRedoCodeTest() {
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: this.schemeStatus
      })
        .then(async (codeRes) => {
          const codeData = codeRes.data.result?.decision_making_content || ''
          console.log("handleRedoCodeTestcodeData 00", codeData);
          switch (this.schemeStatus) {
            case 'decision_ability':
              await AbilityTest({
                code_str: codeData,
                scheme_id: this.$route.query.id
              })
                .then((resTest) => {
                  console.log('测试结果', resTest)
                  const isTestSuccess = resTest?.data?.result.resp.success
                  log('isTestSuccess', isTestSuccess)
                  if (resTest?.data?.code !== 200 || isTestSuccess === false) {
                    this.handleTestOk(false)
                  } else {
                    this.handleTestOk(true)
                  }
                })
                .catch(() => {
                  this.updateTaskStatus(CODE_TEST, 2)
                  this.updateTaskStatusesAfter(CODE_TEST, 0)
                })
              break
            case 'rule':
              await OnRuleTest({
                rule_content: codeData,
                scheme_id: this.$route.query.id
              })
                .then((resTest) => {
                  console.log('测试结果', resTest)
                  if (resTest?.data?.code !== 200) {
                    this.handleTestOk(false)
                  } else {
                    this.handleTestOk(true)
                  }
                })
                .catch(() => {
                  this.updateTaskStatus(CODE_TEST, 2)
                  this.updateTaskStatusesAfter(CODE_TEST, 0)
                })
              break
            default:
              // 如果表达式的值不匹配任何 case，则执行这里的代码
              console.log('都不是 02')
              break
          }
        })
        .catch(() => {
          this.updateTaskStatus(CODE_TEST, 2)
          this.updateTaskStatusesAfter(CODE_TEST, 0)
        })
    },
    changeCurrentRunCode(runCode) {
      this.showLeft = false
      this.customStopScrollTobottom = true
      this.currentDetailrunCode = runCode
    },
    isExpanded(runCode) {
      if (this.foldMap) {
        return this.foldMap[runCode] || false // 默认为 false
      } else {
        return false
      }
    },
    toggleFold(runCode) {
      console.log('this.foldMap toggleFold', this.foldMap)
      console.log('方案优化的数据呢？0 ', this.schemeOptimizeProcessData)

      const currentState = this.isExpanded(runCode)
      this.setFold(runCode, !currentState)
    },
    setFold(runCode, bool) {
      console.log('this.foldMap setFold', runCode)
      if (!this.foldMap) {
        return
      }
      this.$set(this.foldMap, runCode, bool) // 使用 $set 确保 foldMap 是响应式的
    },
    resetFoldMap() {      // 遍历 foldMap，将所有状态设置为 false'
      if (this.foldMap && typeof this.foldMap === 'object') {
        Object.keys(this.foldMap).forEach((key) => {
          this.$set(this.foldMap, key, false) // 或者设为 0，依据你业务中的需求
        })
      }
    },
    initFoldMap(data) {
      // 如果 foldMap 未初始化，则初始化为一个空对象
      if (!this.foldMap) {
        this.foldMap = {}
      }

      data.forEach((task) => {
        // 检查 task 和 task.runCode 是否有效
        if (task && task.runCode) {
          this.$set(this.foldMap, task.runCode, false) // 初始化为折叠状态
        } else {
          console.warn('Invalid task or missing runCode', task)
        }
      })
      console.log('代码分析状态状变化55555', this.foldMap)
    },

    // 初始化执行任务弹窗的展开详情值
    initDetailRunCode(arr) {
      let code
      const statusArr = arr.map((item) => {
        return item.status
      })
      if (statusArr.every((status) => status === 0)) {
        code = arr[0]?.runCode
      } else if (statusArr.every((status) => status === 1)) {
        code = arr[arr.length - 1]?.runCode
      } else if (statusArr.includes(2)) {
        code = arr[statusArr.lastIndexOf(2)]?.runCode
      } else if (statusArr.includes(3)) {
        code = arr[statusArr.lastIndexOf(3)]?.runCode
      } else {
        code = arr[statusArr.lastIndexOf(1)]?.runCode
      }
      console.log("wait current detail code", code)
      return code
    },
    Seticon(index, status, icon_code) {
     console.log('icon_code', status)
      const statusColor = ['-default', '', '-error', '']
      if (icon_code != null) {
        if (['shujufenxi', 'siweishu', 'siweishu1', 'shujufenxi1', 'shujuduiqi1', 'shujuduiqi', 'daimashengcheng', 'daimabushu', 'daimaceshi', 'daimafenxi',].indexOf(icon_code) > -1) {
          return icon_code + statusColor[status]
        } else {
          return iconsMap[SCHEME_OPTIMIZE] + statusColor[status]
        }
      } else {
        // 后端没有传值默认图标 shujufenxi
        return iconsMap[SCHEME_OPTIMIZE] + statusColor[status]
      }
    },
    handleClose(val) {
      this.devlopModalVisable = false
      if (val) {
        this.$confirm('您的研发工单已提交成功，处理完后会通过iCome进行消息通知', '成功', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'success'
        })
          .then(() => {
            this.devlopModalVisable = false
            this.devPerson = val
            this.devPersonInfo = val
          })
          .catch(() => {
            this.devlopModalVisable = false
            this.devPerson = val
            this.devPersonInfo = val
          })
      }
    },
    updateStep(scrollTop) {
      const allContents = document.querySelectorAll('.content-item')
      const content = document.querySelector('.task-left')
      const rectContent = []

      console.log('content.innerHeight', content.scrollTop)
      allContents.forEach((ele) => {
        const eleRect = ele.getClientRects()[0]
        console.log('eleRect.top', eleRect.top, eleRect.height)
        if (
          (eleRect.top >= 0 && content.scrollTop - eleRect.top >= eleRect.height) ||
          (eleRect.top < 0 && content.scrollTop <= eleRect.height - Math.abs(eleRect.top)) ||
          eleRect.top >= 0
        ) {
          rectContent.push(ele)
        }
      })
      console.log('视窗rectContent', rectContent)
      let linkId
      if (rectContent[0]) linkId = rectContent[0].id
      // allLinks.forEach(link => link.classList.remove('active'))
      // const linkDom = document.querySelector(`a[href="#${linkId}"]`)
      // linkDom.classList.add('active')
    },
    scrollView(index) {
      const id = 'content-item' + (index + 1)
      const sectionEl = document.getElementById(id)
      console.log('scrollView 000', sectionEl, id)
      // sectionEl.scrollIntoView({ behavior: "smooth" });
      sectionEl &&
        sectionEl.scrollIntoView({ block: 'start', inline: 'nearest', behavior: 'smooth' })
    },
    accessDevlop() {
      this.devPersonInfo = this.devPerson
      this.curId = this.$route.query.id
      this.devlopModalVisable = true
    },
    async onClose(val) {
      // this.queryBatchTasksResult() //之前的旧逻辑
      if (this.hasChatingName) {
        this.$message({
          type: 'warning',
          message: '会话已被占用!'
        });
        return false
      }
      this.fabuFetch()
      this.$emit('updateTaskModal', val)
    },
    async onCloseOld(val) {
      this.queryBatchTasksResult()
      this.$emit('updateTaskModal', val)
    },
    async fabuFetch() {
      this.isLoading = true
      const res = await MarketAbilityPublish({
        ext_info: {
          abilityIcon: 'https://ai-platform-prod.obsv3.cn-lflt-1.enncloud.cn/aip-agent/473cef3b-985a-4e49-9bfe-3573f0f337a3.png',
          iconType: "ai",
          abilityValue: '',
          abilityIntents: '',
          abilityKeywords: [],
          mode_selection: "task",
          opening_statement: ''
        },
        scheme_id: Number(this.$route.query.id),
        description: '',
        iot_type: 0,
        code_params: [],
        ext_data_info: [],
        engine_service_id: null,
        engine_request_url_prefix: null,
        engine_service_name: null,
        tag_ids: []
      })
      this.isLoading = false
      if (res.data.code == 200) {
        this.$message({
          type: 'success',
          message: '保存成功!'
        });
      } else {
        this.$message({
          type: 'error',
          message: '保存失败!'
        });
      }
    },
    // 最小化窗口
    miniModalShow() {
      this.$emit('updateTaskMiniModal')
    },
    // 确认关闭任务执行
    confirmCancel() {
      // if (this.getTaskByRunCode(ALIGN_ANALYSIS)?.status === 3) {
      //   this.updateTaskStatusesBefore(ALIGN_ANALYSIS, 1)
      //   this.updateTaskStatus(ALIGN_ANALYSIS, 2)
      //   this.updateTaskStatusesAfter(ALIGN_ANALYSIS, 0)
      // }
      this.$emit('stopAbilityGen', {
        scheme_id: this.$route.query.id,
        "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
        "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
        task_status: 2
      })
      // this.$emit('updateTaskModal');
    },
    async stopGen(index) {
      this.$emit('stopAbilityGen', {
        scheme_id: this.$route.query.id,
        "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
        "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
        task_status: 2
      })
    },
    // 重试
    reStart() {
      if (this.hasChatingName) {
        this.$message({
          type: 'warning',
          message: '会话已被占用!'
        });
        return false
      }
      this.$emit('e-updateDevelop', 1)
      this.updateTestFlag = 1
      this.customStopScrollTobottom = false
      this.$emit('handleReStart')
      // this.$emit('alignDataAns')
      // reexecuteTask({ scheme_id: this.$route.query.id, task_name: this.flowData[0].runCode })
    },
    // 从点击的步骤开始执行，只针对已成功的步骤
    async redoTask(runCode) {
      if (this.hasChatingName) {
        this.$message({
          type: 'warning',
          message: '会话已被占用!'
        });
        return false
      }
      if (this.flowData.filter((item) => item.status === 3).length || this.codeTestStatus === 3) {
        this.$message({
          type: 'warning',
          message: '请等待其他任务执行后再重试'
        })
      } else {
       console.log('djaosjdfapsfjapsdfsa')
        const developIndex = this.flowData.findIndex((item) => item.runCode === CODE_DEPLOY)
        const codetestIndex = this.flowData.findIndex((item) => (item.runCode === CODE_TEST || item.runCode === ABILITY_CHECK))
        const curIndex = this.flowData.findIndex((item) => item.runCode === runCode)
        if (curIndex <= developIndex) {
          console.log("developIndex", curIndex, developIndex);
          this.$emit('e-updateDevelop', 1)
        }
        if (curIndex <= codetestIndex) {
          console.log("codetestIndex", curIndex, codetestIndex);
          this.getTaskByRunCode(this.flowData[codetestIndex].runCode).summarize = ''
          this.updateTestFlag = 1
        }
        this.setFold(runCode, true)
        console.log('this.foldMap redoTask', JSON.stringify(this.foldMap, null, 2))
        // reexecuteTask({ scheme_id: this.$route.query.id, task_name: runCode })
        this.currentDetailrunCode = runCode
        this.updateTaskStatusesBefore(runCode, 1)
        this.updateTaskStatus(runCode, 3)
        this.updateTaskStatusesAfter(runCode, 0)
        this.$nextTick(() => {
          this.$emit('e-generalExec', runCode)
        })
        console.log('redoTask log 00', JSON.stringify(this.flowData, null, 2))
      }
    },
    async copyRedoTask(runCode) {
      if (this.hasChatingName) {
        this.$message({
          type: 'warning',
          message: '会话已被占用!'
        });
        return false
      }
      if (this.flowData.filter((item) => item.status === 3).length) {
        this.$message({
          type: 'warning',
          message: '请等待其他任务执行后再重试'
        })
      } else {
        this.$nextTick(() => {
          this.setTopHeight('70%')
          this.currentDetailrunCode = 'decision_making_generate'
          this.$emit('e-generalExecTest', this.testAbilityConfig)
        })
      }
    },
    // 代码部署完成通知
    async handleDevelopOk(flag, cdata) {
      // console.log('代码部署结果', flag, cdata)
      console.log("代码部署结果2", this.getTaskByRunCode(CODE_TEST)?.status || this.getTaskByRunCode(ABILITY_CHECK)?.status);
      console.log('ABILITY_CHECK', this.getTaskByRunCode(ABILITY_CHECK)?.status)
      console.log('CODE_TEST', this.getTaskByRunCode(CODE_TEST)?.status)
      const temps = this.getNextTask(CODE_DEPLOY)?.status
      console.log('CODE_TEST2', temps)
      if (flag) {
        if (temps !== 1) {
          // this.foldMap = [0, 0, 0, 0, 0, 0, 1]
        }
        this.updateTaskStatus(CODE_DEPLOY, 1)
        if (temps === 0) {
          if (cdata) {
            this.updateNextTaskStatus(CODE_DEPLOY, 3)
            const nextTask = this.getNextTask(CODE_DEPLOY)
            if (nextTask) {
              this.updateTaskStatusesAfter(nextTask.runCode, 0)
            }
            switch (this.schemeStatus) {
              case 'decision_ability':
                if (nextTask.runCode === ABILITY_CHECK) {
                  this.$emit('e-generalExec', nextTask.runCode)
                } else {
                  await AbilityTest({
                    code_str: cdata,
                    scheme_id: this.$route.query.id
                  }).then((resTest) => {
                    console.log('测试结果333', resTest)
                    const isTestSuccess = resTest?.data?.result.resp.success
                    log('isTestSuccess', isTestSuccess)
                    if (resTest?.data?.code !== 200 || isTestSuccess === false) {
                      this.handleTestOk(false)
                    } else {
                      console.log("为啥这个时候直接就是1了", this.getTaskByRunCode(ABILITY_CHECK)?.status);
                      this.handleTestOk(true)
                    }
                  })
                }
                break
              case 'rule':
                await OnRuleTest({
                  rule_content: cdata,
                  scheme_id: this.$route.query.id
                }).then((resTest) => {
                  console.log('测试结果335', resTest)
                  if (resTest?.data?.code !== 200) {
                    this.handleTestOk(false)
                  } else {
                    this.handleTestOk(true)
                  }
                })
                break
              default:
                // 如果表达式的值不匹配任何 case，则执行这里的代码
                console.log('都不是 05')
                break
            }
          }
          setTimeout(() => {
            this.updateTestFlag = Number(this.updateTestFlag) + 1
          }, 500)
        } else {
          console.log('buxuyao')
        }
      } else {
        this.updateTaskStatus(CODE_DEPLOY, 2)
        this.updateTaskStatusesAfter(CODE_DEPLOY, 0)
      }
    },
    // 代码测试通知
    handleTestOk(flag) {
      const deployNextTask = this.getNextTask(CODE_DEPLOY)
      if (this.getTaskByRunCode(ALIGN_DATA_GENERATE)?.status === 1 || deployNextTask?.status !== 2) {
        const temp = this.getTaskByRunCode(CODE_ANALYSIS)?.status
        if (temp !== 1) {
          // this.foldMap = [0, 0, 0, 0, 0, 0, 1]
        }
        if (flag) {
          this.updateTaskStatus(CODE_TEST, 1)

          this.updateTaskStatus(ABILITY_CHECK, 1)
          if (temp === 0) {
            const nextTask = this.getNextTask(CODE_TEST) || this.getNextTask(ABILITY_CHECK)
            if (nextTask) {
              nextTask.status = 3
              console.log("这不该调用能力参数分析的吗？00", deployNextTask?.status);
              this.$emit('e-generalExec', nextTask.runCode)
            }
          }
        } else {
          this.updateTaskStatus(CODE_TEST, 2)
          this.updateTaskStatusesAfter(CODE_TEST, 0)

          this.updateTaskStatus(ABILITY_CHECK, 2)
          this.updateTaskStatusesAfter(ABILITY_CHECK, 0)
        }
      } else {
        this.updateTaskStatus(CODE_TEST, 2)
        this.updateTaskStatusesAfter(CODE_TEST, 0)


        this.updateTaskStatus(ABILITY_CHECK, 2)
        this.updateTaskStatusesAfter(ABILITY_CHECK, 0)
      }
    },
    async bushu() {
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: this.schemeStatus
      }).then(async (codeRes) => {
        const codeData = codeRes.data.result?.decision_making_content || ''

        await AbilityPublish({
          code_str: codeData,
          scheme_id: this.$route.query.id
        })
          .then((res) => {
            if (res?.data?.code !== 200) {
              this.handleDevelopOk(false, '')
            } else {
              console.log('哪里圈了部署为true呀 00')
              this.handleDevelopOk(true, codeData)
            }
          })
          .catch(() => {
            this.handleDevelopOk(false, '')
          })
        // }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content {
 // min-height: 251px;
}
.lay-out {
 .task-show {
  // flex: 4 !important;
 }
 // .code-test {
 //  flex: 1;
 // }
 // .code-line {
 //  height: 5px;
 //  transition: background 0.2s;
 //  cursor: text;
 //  &:hover {
 //   background-color: #cbd5e1;
 // }
 // }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.task-container {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;

  .task-left {
    flex: 1;
    // max-height: 100%;
    overflow-y: auto;
  }

  .task-list {
    margin: 0px 5px 0 15px;
    border-right: 1px solid #dcdde0;
    height: 100%;
    // height: calc(100% - 34px);

    .task-list-item {
      display: flex;
      flex-direction: row;
      align-items: stretch;
      justify-content: space-between;
      cursor: pointer;
      padding: 5px 0px;

      .task-icon {
        width: 32px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .icon-svg {
          font-size: 32px;
          width: 32px;
          height: 32px;
        }

        img {
          width: 32px;
          height: 32px;
        }

        .task-line {
          flex: 1;
          min-height: 28px;
          width: 0px;
          border-left: 1px solid #dcdde0;
          height: 100%;
          // background: #DCDDE0;
          margin-bottom: 4px;
          margin-top: 4px;

          &.task-line-info {
            border-left: 1px dashed #dcdde0 !important;
          }

          &.task-line-error {
            border-left: 1px solid #ea4646 !important;
          }
        }
      }

      .task-text-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-left: 8px;
        width: 80%;
      }

      .task-text-process {
        // flex: 1;
        background: #f6f7fb;
        border-radius: 2px;
        height: 0px;
        overflow: auto;

        &.show {
          height: auto !important;
          max-height: 504px;
          margin: 16px;
        }
      }

      .task-text {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 32px;

        .task-text-title {
          font-weight: 400;
          user-select: none;
          flex: 1;
          font-size: 14px;
          color: #323233;
          line-height: 20px;
          flex: 1;
          margin: 8px;

          &:hover {
            color: #4068d4;
          }
        }

        .task-text-link {
          font-weight: 400;
          font-size: 14px;
          color: #4068d4;
          line-height: 20px;
          display: flex;
          align-items: center;
          margin-right: 12px;
        }
      }
    }

    .loading-task {
      width: 32px;
      height: 32px;
      display: flex;
      border-radius: 50%;
      align-items: center;
      justify-content: center;
      background-color: #e7e9f8;
      color: #4068d4;

      i {
        font-size: 18px;
        animation: rotate 1s linear infinite;
      }
    }
  }

  .task-right {
    width: 0px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px;

    &.showRight {
      width: 65%;
      // height: 600px;
      //margin-left: 16px;
      // border-left: 1px solid #dcdde0;
    }


    .task-show {
      // flex: 1;
      height: 100%;
      overflow-y: auto;
      // margin-left: 20px
    }
  }
}


.dialog-title {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  width: 100%;
  justify-content: space-between;
  height: 70px;
  // border-bottom: 1px solid #e4e7ed;

  .jiantou {
    // width: 20px;

    height: 20px;
    line-height: 20px;
    margin-right: 8px;
    // background: url('../../../assets/images/jiantou.png') no-repeat center;
    background-size: contain;
    cursor: pointer;
    border-right: 1px solid #EBECF0;
    padding-right: 8px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    i {
      font-size: 16px;
    }

    span {
      font-size: 16px;
    }
  }

  .title-text {
    line-height: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #323233;
    margin-left: 20px;
  }
}

.el-dialog__header {
  padding: 12px 20px;
  border-bottom: 1px solid #ebecf0;

  .el-dialog__title {
    font-size: 16px;
    color: #323233;
    line-height: 24px;
  }

  .el-dialog__headerbtn {
    top: 14px;

    .el-dialog__close {
      font-size: 18px;
    }
  }
}

.el-message-box__header {
  padding: 12px 20px;
  border-bottom: 1px solid #ebecf0 !important;

  .el-message-box__title {
    font-size: 16px;
    color: #323233;
    line-height: 24px;
  }

  .el-message-box__headerbtn {
    top: 14px;

    .el-message-box__close {
      font-size: 18px;
    }
  }
}

.el-message-box__content {
  padding: 16px 20px;

  .el-message-box__message {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
}

.el-message-box__btns {
  padding: 0px 20px;

  button {
    width: 60px !important;
  }

  .el-button {
    line-height: 20px !important;
  }
}

.el-dialog__body {
  padding: 16px 20px;
  height: calc(100vh - 240px) !important;
  max-height: calc(100vh - 240px) !important;
  overflow-y: auto;
}

.small-last-dialog {
  .el-dialog__body {
    padding: 16px 20px;
    height: auto !important;
    max-height: 340px;
    overflow-y: auto;
  }
}

.el-dialog__footer {
  padding: 16px 20px;

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .el-button {
      line-height: 20px;
    }
  }
}

.el-input__inner {
  border-radius: 2px;
}

::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;

  img {
    height: 16px;
    margin-top: -2px;
  }
}

::v-deep .el-button--primary.is-plain {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    font-size: 10px;
  }
}

::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 16px;
  border-radius: 2px;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.max_height {
  max-height: 500px;
  overflow: auto;
}

.bg {
  background: #fff;
  margin: 20px;
  width: calc(100% - 40px);
  height: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: end;
  padding: 20px;
  align-items: center;
}

.cur {
  cursor: pointer;
}

.refFont {
  color: red;
  // line-height: 30px;
  margin-right: 20px;
  margin-left:10px;
}

.acTitle {
  // background: #EFF3FF;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #4068D4 !important;
}

.ac-task-text {
  background: #EFF3FF !important;
}

.bg_bt {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.header-title {
  // padding: 12px 4px 12px 20px;
  font-weight: 500;
  font-size: 14px;
  color: #323233;
  line-height: 22px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(65% - 16px);
  ::deep(.el-tabs__header){
    margin: 0!important;
  }
  .editorMark {
    display: flex;
    flex: 1;
    padding: 0 5px 0 12px;
    justify-content: flex-end;
    align-items: center;

    div {
      display: flex;
    }
  }
}

.chuanzuozhe {
  width: 30px;
  height: 30px;
  background: #F2F3F5;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  margin: 0 8px;
  cursor: pointer;
}
.flex_body{
  :deep(.el-tabs__item){
    border-radius: 16px;
    background: #f6f7fb;
    height: 30px;
    line-height: 30px;
    padding: 4px 12px!important;
    font-weight: 400;
    font-size: 14px;
    color: #323233;
    line-height: 22px;
    // text-align: left;
    font-style: normal;
    margin-right: 8px;
  }
  :deep(.is-active){
    background: #4068d4;
    color: #ffffff;
  }
  :deep(.el-tabs__active-bar){
    display: none;
  }
  :deep(.el-tabs__nav-wrap::after){
    display: none;
  }
  :deep(.el-tabs__header){
    margin: 0!important;
  }
}
</style>
