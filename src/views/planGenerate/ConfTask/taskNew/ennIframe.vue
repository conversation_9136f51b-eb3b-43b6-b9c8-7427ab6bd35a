<template>
  <div
    class="task-card-content"
    id="taskTable"
    v-loading="loading"
    element-loading-text="生成中..."
    element-loading-spinner="el-icon-loading"
  >
    <div class="transition-box" v-if="status !== 0">
      <iframe
      id="iframeNest"
      :src="dataVal.trim()"
      allow="clipboard-write"
      frameborder="0"
      class="iframe-nest"
    ></iframe>
    </div>
  </div>
</template>
<script type="text/javascript">

export default {
  name: 'ennIframe',
  props: {
    dataVal: {
      type: String,
      default: ''
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    status: {
      handler(val) {
        console.log('tree status in treeData.vue', val)
        if (Number(val) !== 3) {
          this.loading = false
        }else{
          this.loading = true
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      loading: false,
    }
  },
  async mounted() {},
  methods: {
  }
}
</script>
<style lang="scss" scoped>

.iframe-nest {
  width: 100%;
  height: calc(100% - 16px);
  overflow: auto;
}
.task-card-content{
  height: -webkit-fill-available;
}
.transition-box{
  height: -webkit-fill-available;
}
</style>
