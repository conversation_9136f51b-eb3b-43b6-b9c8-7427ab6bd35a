<template>
  <div
    v-loading="loading"
    class="task-card-content"
    element-loading-text="代码生成中..."
    element-loading-spinner="el-icon-loading"
  >
    <!-- <MyEditor
      v-if="status !== 0"
      id="MyEditor2"
      ref="MyEditor2"
      :md-content="showCodeData"
      :is-edit="isEdit"
      @updateContent="handleUpdateContent"
    ></MyEditor> -->
    <!-- {{ showCodeData }} -->
    <MonacoEditor
      v-if="status !== 0"
      id="codeCreated"
      class="editor"
      :value="showCodeData"
      language="json"
      :scroll-beyond-last-line="true"
      :original="codeDataOri"
      theme="vs-dark"
      :diff-editor="false"
      :autoScroll="true"
      :options="options"
      @editorDidMount="editorDidMount"
      @change="change"
    />
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import MonacoEditor from '@/components/MonacoEditor'
// import Status from '@/components/Status/index.vue';
import { GetDecision } from '@/api/planGenerateApi.js'
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';
import MyEditor from '../../mdEditor.vue'

export default {
  name: 'ExampleCom',
  components: { MonacoEditor, MyEditor },
  props: {
    dataVal: {
      type: String
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      options: {
        theme: 'vs',  // 使用默认浅色主题
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      },
      showCodeData: '',
      codeDataOri: ''
    }
  },
  watch: {
    dataVal: {
      handler(val) {
        this.showCodeData = val
        this.codeDataOri = val
        // const temp = document.getElementById('codeCreated');
        // if (temp) {
        //   temp.scrollIntoView({ block: 'end', inline: 'nearest' });
        //   temp.scrollTop = temp.scrollHeight + 200;
        // }
        const target = document.getElementById('codeCreated')
        console.log('target.scrollTop', target)
        if (target) {
          target.scrollTop = target.scrollHeight - 40
          // target.scrollTop = target.scrollHeight - target.clientHeight + 30;
        }
      },
      immediate: true,
      deep:true
    },
    status: {
      handler(val) {
        // 状态是成功时获取代码
        if (Number(val) === 1) {
        }
        if (Number(val) === 2) {
          this.loading = false
        }
      },
      immediate: true
    },
    processStatus: {
      handler(val) {
        console.log('生成日志梳妆台', val)
        // 代码生成过程日志状态是成功时获取代码
        if (Number(val) === 2 || Number(val) === 3) {
          this.handleInit()
        }
        if (Number(val) === 1) {
          this.loading = true
        }
      },
      immediate: true
    }
  },
  methods: {
    editorDidMount(val) {},
    change(val) {
      this.showCodeData = val
    },
    handleUpdateContent(val) {
      this.showCodeData = val
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  // padding: 16px 20px;
  height: 100%;
  // min-height: 504px;

  .editor {

    height: 100%;
    width: 100%;
    margin-left: 1px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
