<template>
  <div class="task-card-content" v-if="templateId && status !== 0" v-loading="loading"
    element-loading-text="多模态数据对齐分析中..." element-loading-spinner="el-icon-loading">
    <template>
      <EditDataAns :runStreamList="runStreamList" :dataAnsTask="dataAnsTask" :templateId="templateId"
        :treeData="treeDataVal" :formData="formData" @e-updateAllsuccess="handleUpdateAllsuccess"
        :allSuccess="allSuccess" :scheme_detail_optimizeData="scheme_detail_optimizeData"/>
    </template>
  </div>
  <div v-else-if="status !== 0">
    <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
      ">
      <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
      <div style="
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        ">
        对齐分析模版ID为空，请转研发进行处理。
      </div>
    </div>
  </div>
  <div v-else-if="status === 0">
    <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
      ">
      <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
      <div style="
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        ">
        暂无
      </div>
    </div>
  </div>
  <div v-else>
    <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
      ">
      <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
      <div style="
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        ">
        暂无
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { queryTempIfFromScene, queryAbilityMapping, SchemeDetail, } from '@/api/planGenerateApi.js'
import EditDataAns from '../components/EditDataAns.vue';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  components: { EditDataAns },
  // components: { Status, MyEditor },
  props: {
    scheme_detail_optimizeData: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      required: true
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    dataAnsTask:{
      type: Object,
      required: true
    },
    runStreamList: {
      type: Array,
      default: []
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    templateId: {
      type: [String, Number],
      default: ''
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    allSuccess:{
      handler(val) {
      console.log("allSuccess 00", val);
      if (val) {
         this.loading = false
      }else{
        this.loading = true
      }
      },
      immediate: true,
    },
    dataAnsTask:{
      handler(val) {
        console.log("dataAnsTask 00", val);
      },
      immediate: true,
      deep: true
    },
    runStreamList: {
      handler(val) {
        console.log('流式变化', val)
        this.taskList = val
        const temp = document.getElementById('taskBox')
        if (temp) {
          temp.scrollIntoView({ block: 'end', inline: 'nearest' })
        }
      },
      immediate: true
    },
    status: {
      handler(val) {
        console.log('第一次333', this.runStreamList)
        if (Number(val) === 1) {
          this.taskList = this.runStreamList
          const temp = document.getElementById('taskBox')
          if (temp) {
            temp.scrollIntoView({ block: 'end', inline: 'nearest' })
          }
        }
      },
      immediate: true
    }
  },
  async mounted() {
    console.log('第一次', this.runStreamList)
    await this.queryPlanDetail();
  },
  data() {
    return {
      loading: false,
      taskList: []
    }
  },
  methods: {

    handleUpdateAllsuccess(val){
      console.log("啥时候执行的啊？00", val);
      // this.allSuccess = val
      this.$emit('e-updateAllsuccess', val)
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result?.text;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  padding: 16px 20px;
}
.headerTip {
  display: flex;
  align-items: center;
  .tipIcon {
    width: 48px;
    height: 48px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .tipDesc {
    flex: 1;
    background: #eff3ff;
    margin-left: 13px;
    position: relative;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 20px;
    color: #323233;
    border-radius: 6px;
    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
      border-top: 5px solid transparent; /*左边透明*/
      border-bottom: 5px solid transparent; /*右边透明*/
      border-right: 8px solid #eff3ff; /*底部为黑色线条*/
    }
  }
}
.taskCard {
  border: 1px solid #dcdde0;
  border-radius: 4px;
  padding: 10px 12px;
  margin-bottom: 12px;
  .title {
    color: #323233;
    font-weight: bold;
    line-height: 20px;
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
    .subTitle {
      font-weight: normal !important;
    }
  }
  .desc {
    color: #646566;
    margin-top: 12px;
    line-height: 22px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
