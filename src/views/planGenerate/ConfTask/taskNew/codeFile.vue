<template>
    <div class="code-file" :style="{ height: '100%', overflow: 'auto' }">
     <EnnObjectFiles
     ref="ennfile"
     :firstUrl="result_file_root_path"
     :isRight="false"
      />
    </div>
</template>
<script>
import _ from 'lodash'
import { ComponentRegistry, ComponentAliasMap } from '../../ConfTask/toolComponents/ComponentRegistry';
export default {
    props: {
        topHeight: {
            type: String
        },
        name: {
            type: String
        },
        activeName:{
            type: String
        },
        result_file_root_path:{
            type: String
        }
    },
    // components: { MonacoEditor,AbilitysFile,AbilitysCode
    // },
    components: { EnnObjectFiles: () =>
      import('@/views/planGenerate/ConfTask/toolComponents/EnnObjectFiles.vue')
    },
    data() {
        return {

        }
    },
    methods: {
      setCurrentRunCode(runCode){
        this.$refs.ennfile.setCurrentRunCode(runCode)
      }
    },
    mounted() {},
    watch: {}
}
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  background: none !important;
}
.code-file {
 // height: calc(100% - 34px) !important;
 :deep(.sidebar) {
  .bottom {
   border-bottom: 1px solid rgb(235, 235, 235);
  }
 }
}
</style>
