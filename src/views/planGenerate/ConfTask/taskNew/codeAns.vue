<template>
  <div
    v-loading="loading"
    class="task-card-content"
    element-loading-text="代码解析中..."
    element-loading-spinner="el-icon-loading"
  >
    <div v-if="status !== 0" class="transition-box" style="overflow-y: hidden; height: 100%">
      <div
        v-if="treeProcessData !== ''"
        id="treeProcessEl2"
        ref="treeProcess2"
        style="overflow-y: auto; height: 100%"
      >
        <!-- <MyEditorPreview
          id="MyEditorTreeFirst"
          ref="MyEditorTreeFirst"
          :mdContent="treeProcessDataFirst"
        ></MyEditorPreview> -->
        <!-- <MonacoEditor
          id="codeAns"
          class="editor"
          :value="treeProcessData"
          language="json"
          :scroll-beyond-last-line="true"
          :original="treeProcessData"
          theme="vs-dark"
          :diff-editor="false"
          :options="options"
        /> -->
        <!-- <MyEditorPreview
          id="MyEditorTree2"
          ref="MyEditorTree2"
          :md-content="treeProcessData"
        ></MyEditorPreview> -->
        <!-- <MyEditor
          id="MyEditorTree2"
          ref="MyEditorTree2"
          :isEdit="true"
          :md-content="treeProcessData"
        ></MyEditor> -->
        <MonacoEditor
          v-if="status !== 0"
          id="code_analysis"
          class="editor"
          :value="treeProcessData"
          language="json"
          :scroll-beyond-last-line="true"
          :original="codeDataOri"
          theme="vs-dark"
          :diff-editor="false"
          :autoScroll="true"
          :options="options"
          @change="change"
        />
        <!-- <vue-markdown v-highlight id="treeProcessMd" :source="treeProcessData" class="markdown-body"></vue-markdown> -->
      </div>
    </div>
    <div v-else-if="status === 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { GetDecision } from '@/api/planGenerateApi.js'
// import MyEditorPreview from '../../mdEditorPreview.vue'
import MonacoEditor from '@/components/MonacoEditor'
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue'

export default {
  name: 'ExampleCom',
  components: { MonacoEditor },
  props: {
    schemeStatus: {
      type: String
    },
    treeProcessVal: {
      type: String,
      default: null
    },
    treeProcessValFirst: {
      type: String,
      default: null
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      treeProcessData: '',
      treeProcessDataFirst: '',
      codeDataOri: '',
      options: {
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      }
    }
  },
  watch: {
    treeProcessVal: {
      handler(val) {
        if (val) {
          this.treeProcessData = val
          this.codeDataOri = val
          // this.loading = true;
          this.scrollFn()
        }
      },
      immediate: true
    },
    treeProcessValFirst: {
      handler(val) {
        if (val) {
          this.treeProcessDataFirst = val
          // this.loading = true;
          this.scrollFn()
        }
      },
      immediate: true
    },
    status: {
      handler(val) {
        console.log('code status', val)
        if (Number(val) !== 3) {
          this.loading = false
        }
        if (Number(val) === 1) {
          this.handleInit()
        }
      },
      immediate: true
    }
  },
  async mounted() {
    // this.handleInit();
  },
  methods: {
    change(val) {
      this.treeProcessData = val
    },
    scrollFn() {
      const temp = document.getElementById('treeProcessEl2')
      // console.log('内Height', document.getElementById('treeProcessEl'), temp?.scrollTop, temp?.scrollHeight)
      if (temp) {
        temp.scrollIntoView({ block: 'end', inline: 'nearest' })
      }
    },
    async handleInit() {
      this.loading = true
      console.log('初始化')
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: this.schemeStatus
      }).then((res) => {
        this.loading = false
        console.log('代码解析', res.data.result)
        if (
          res.data.result?.ext_info?.code_analysis_status ||
          res.data.result?.ext_info?.code_params
        ) {
          console.log('已经有参数了')
          // this.treeProcessData =
          //   '```json \n' +
          //     JSON.stringify(res.data.result.ext_info.code_params, null, 2) +
          //     '\n```' || ''
          this.treeProcessData = JSON.stringify(res.data.result.ext_info.code_params, null, 2)
        } else {
          console.log('没有参数，触发流信息')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  // padding: 0px 20px 16px 20px;
  height: 100%;
}
.editor {
  height: 100%;
  width: 100%;
  margin-left: 1px;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
