<template>
  <div
    v-loading="loading"
    class="task-card-content"
    element-loading-text="代码部署中..."
    element-loading-spinner="el-icon-loading"
  >
    <div v-if="status !== 0">
      <el-result v-if="deployStatus && !testFlag" icon="success" sub-title="代码部署成功" />
      <el-result v-else-if="deployStatus && testFlag" icon="error" sub-title="代码部署失败" />
      <el-result v-else icon="warning" sub-title="代码未部署" />
    </div>
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { GetDecision, AbilityPublish,updateLogTaskStatus } from '@/api/planGenerateApi.js'
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  // components: { Status, MyEditor },
  props: {
    schemeStatus: {
      type: String
    },
    updateDevelopFlag: {
      type: [Number, String],
      default: 1
    },
    status: {
      type: [String, Number],
      default: ''
    },
    getTaskByRunCode: {
      type: Function,
      required: true
    },
    currentDetailrunCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      deployStatus: false, // 部署状态
      testFlag: false // 是否部署失败
    }
  },
  watch: {
    status: {
      handler(val) {
        if (Number(val) === 3) {
          this.loading = true
        }
        if (Number(val) === 1) {
          this.deployStatus = true
          this.testFlag = false
          // this.bushu();
        }
        if (Number(val) === 2) {
          this.testFlag = true
          console.log('部署失败，部署val', val, this.testFlag, this.deployStatus)
          this.testFlag = true
          this.deployStatus = true
        }
        if (Number(val) === 0) {
          this.loading = false
        }
      },
      immediate: true
    },
    updateDevelopFlag: {
      handler(val) {
        console.log('部署更新val', val)
        if (val !== 1) {
          this.bushu()
        }
      },
      immediate: true
    }
  },
  methods: {
    async bushu() {
      this.loading = true
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: this.schemeStatus
      }).then(async (codeRes) => {
        const codeData = codeRes.data.result?.decision_making_content || ''

        await AbilityPublish({
          code_str: codeData,
          scheme_id: this.$route.query.id
        })
          .then(async (res) => {
            if (res?.data?.code !== 200) {
              // this.$message.error(res?.data?.msg || '部署失败');
              this.loading = false
              this.testFlag = true
              console.log('哪里圈了部署为true呀 03')
              this.$emit('handleOk', false, '')
            } else {
              this.loading = false
              this.deployStatus = true
              this.testFlag = false
              await updateLogTaskStatus({
              scheme_id: this.$route.query.id,
              "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
              "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
              task_status: 1
            });
              console.log('哪里圈了部署为true呀 01')
              this.$emit('handleOk', true, codeData)
            }
          })
          .catch(() => {
            this.loading = false
            this.testFlag = true
            this.$emit('handleOk', false, '')
          })
          .finally(() => {
            this.loading = false
          })
      })
      // this.$message.success('能力代码生成部署成功');
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  // padding: 16px 20px;
  text-align: center;
  .deploy-text {
    color: #323233;
    font-size: 14px;
    line-height: 22px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
