<template>
  <!-- v-loading="!showCodeData" element-loading-text="生成中..." element-loading-spinner="el-icon-loading" -->
  <div class="editor" >
    <!-- <MonacoEditor
      v-if="status !== 0"
      id="codeCreated"
      class="editor"
      :value="showCodeData"
      language="markdown"
      :scroll-beyond-last-line="true"
      :original="showCodeData"
      theme="vs-dark"
      :diff-editor="false"
      :options="options"
      @editorDidMount="editorDidMount"
      @change="change"
    /> -->
    <MyEditor id="MyEditor2" ref="MyEditor2" :md-content="showCodeData" :is-edit="isEdit" :stop-scroll="stopScroll"
      :data-align-status="codeStatus" :el-box="elBox" @updateContent="handleUpdateContent"></MyEditor>
    <!-- <vue-markdown v-highlight :source="showCodeData"></vue-markdown> -->
  </div>
</template>

<script>
// import MonacoEditor from '@/components/MonacoEditor';
import MyEditor from '../../mdEditor.vue';

export default {
  components: { MyEditor },
  props: {
    treeDataVal: String,
    languageType: String,
    stopScroll: Boolean,
    dataAlignDataStatus: [Number, String],
    elBox: String
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      options: {
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      },
      showCodeData: '',
      codeStatus: '',
      codeDataOri: ''
    };
  },

  watch: {
    treeDataVal: {
      handler(val) {
        this.showCodeData = val;
      },
      immediate: true,
      deep: true
    },
    dataAlignDataStatus: {
      handler(val) {
        this.codeStatus = val + '';
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    handleUpdateContent(val) {
      this.showCodeData = val;
    },
    editorDidMount(val) {
      console.log(val, '22222222222222');
    },
    change(val) {
      this.showCodeData = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.editor {
  // min-height: 520px;
  height: 100%;
  width: 100%;
  // margin-left: 1px;
  // padding: 16px 20px;
  position: relative;
  z-index: 10;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
