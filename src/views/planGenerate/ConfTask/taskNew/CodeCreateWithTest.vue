<template>
 <div class="layout-test" :class="topHeight === '0%' ? 'no-height' : ''">
  <div v-loading="loading" class="task-card-content" element-loading-text="代码生成中..." id="left-content" :style="{
   height: topHeight,
   userSelect: isDragging ? 'none' : 'auto',
   transition: isDragging ? 'none' : 'width 0.2s',
  }" element-loading-spinner="el-icon-loading">
   <div v-show="activeName === 'first'" style="height: 100%;">
    <MonacoEditor v-if="status !== 0" id="codeCreated" class="editor" :value="showCodeData" language="python"
     :scroll-beyond-last-line="true" :original="codeDataOri" theme="vs-dark" :diff-editor="false" :autoScroll="true"
     :options="options" @editorDidMount="editorDidMount" @change="change" />
    <div v-else>
     <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        ">
      <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
      <div style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          ">
       暂无
      </div>
     </div>
    </div>
   </div>
   <CodeFile :result_file_root_path="result_file_root_path" v-show="activeName === 'second'" :topHeight="topHeight"
    :activeName="activeName" :name="getTaskByRunCode(currentDetailrunCode)?.title"></CodeFile>
  </div>
  <div class="drag-handle" @mousedown="startDrag"></div>
  <!-- <twoColumn :startDrag="startDrag" /> -->
  <div id="right-content" :style="{
   userSelect: 'none',
   transition: isDragging ? 'none' : 'width 0.2s',
  }" class="code-test">
   <div class="has-composite-bar">
    <div class="composite-bar-container">
     <el-tabs v-model="activeName">
      <el-tab-pane label="输出" name="first"></el-tab-pane>
     </el-tabs>
     <div class="global-actions">
      <el-tooltip class="item" effect="dark" :content="isExpanded ? '最大化面板大小' : '恢复面板大小'" placement="bottom">
       <div class="customer" @click="handleExpand">
        <i style="font-size: 16px; margin-right: 8px;"
         :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
       </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="隐藏面板" placement="top-start">
       <i @click="handleClose" style="font-size: 16px" class="el-icon-close"></i>
      </el-tooltip>
     </div>
    </div>

   </div>
   <MyEditorPreview :autoScroll="true" class="max_height" id="MyEditorTree" ref="MyEditorTree"
    :md-content="(mdContentText == 'NaN' ? '' : mdContentText) || ''"></MyEditorPreview>
  </div>
 </div>
</template>
<script type="text/javascript">
import twoColumn from "@/components/twoColumn.vue";
import { throttle } from 'lodash'
import MonacoEditor from '@/components/MonacoEditor'
import { GetDecision } from '@/api/planGenerateApi.js'
import MyEditor from '../../mdEditor.vue'
import MyEditorPreview from './mdEditorPreviewNew.vue'
import { mapGetters, mapState, mapMutations } from 'vuex'
import CodeFile from './codeFile.vue'
export default {
 name: 'ExampleCom',
 components: { MonacoEditor, MyEditor, twoColumn, CodeFile, MyEditorPreview },
 props: {
  result_file_root_path: {
   type: String
  },
  type: {
   type: String
  },
  codeTestProcess: {
   type: String
  },
  schemeStatus: {
   type: String
  },
  codeDataStatus: {
   type: [String, Number],
   default: ''
  },
  codeData: {
   type: String,
   default: ''
  },
  status: {
   type: [String, Number],
   default: ''
  },
  processStatus: {
   type: [String, Number],
   default: ''
  },
  getTaskByRunCode: {
   type: Function
  },
  currentDetailrunCode: {
   type: String,
   default: ''
  },
  activeName: {
   type: String,
   default: 'first'
  }
 },
 data() {
  return {
   isExpanded: true,
   activeName: 'first',
   mdContentText: '',
   totalWidth: 1000,
   // topHeight: '70%',
   rightWidth: '30%',
   isDragging: false,
   startY: null,
   startHeight: null,
   isEdit: false,
   loading: false,
   options: {
    theme: 'vs',  // 使用默认浅色主题
    readOnly: true,
    lineNumbers: true,
    fontSize: 15,
    mouseStyle: 'default',
    colorDecorators: true,
    foldingStrategy: 'indentation', // 代码可分小段折叠
    automaticLayout: true, // 自适应布局
    overviewRulerBorder: false, // 不要滚动条的边框
    autoClosingBrackets: true,
    renderLineHighlight: 'all',
    wordWrap: 'on',
    scrollBeyondLastLine: true,
    tabSize: 4, // tab 缩进长度
    minimap: {
     enabled: true // 不要小地图
    },
    fontFamily:
     'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
    folding: true
   },
   showCodeData: '',
   codeDataOri: ''
  }
 },
 computed: {
  ...mapState('operations', ['codeTestText', 'topHeight']),
 },
 watch: {
  codeTestProcess: {
   handler(val) {
    if (this.type === 'code_test') {
     this.mdContentText = val || ''
    }
   },
   immediate: true,
   deep: true
  },
  codeDataStatus: {
   handler(val) {
    if (Number(val) === 2 || Number(val) === 3) {
     this.handleInit()
    } else {
     this.showCodeData = this.codeData
     this.codeDataOri = this.codeData
     const temp = document.getElementById('codeCreated')
     if (temp) {
      temp.scrollIntoView({ block: 'end', inline: 'nearest' })
      temp.scrollTop = temp.scrollHeight + 200
     }
    }
   },
   immediate: true
  },
  codeData: {
   handler(val) {
    this.showCodeData = val
    this.codeDataOri = val
    const target = document.getElementById('codeCreated')
    console.log('target.scrollTop', target)
    if (target) {
     target.scrollTop = target.scrollHeight - 40
    }
   },
   immediate: true,
   deep: true
  },
  status: {
   handler(val) {
    // 状态是成功时获取代码
    if (Number(val) === 1) {
     this.handleInit()
    }
    if (Number(val) === 2) {
     this.loading = false
    }
   },
   immediate: true
  },
  processStatus: {
   handler(val) {
    console.log('生成日志梳妆台', val)
    // 代码生成过程日志状态是成功时获取代码
    if (Number(val) === 2 || Number(val) === 3) {
     this.handleInit()
    }
    if (Number(val) === 1) {
     this.loading = true
    }
   },
   immediate: true
  }
 },
 methods: {
  ...mapMutations('operations', ['setTopHeight']),
  // startDrag(event) {
  //  this.$emit('handleStartDrag', event)
  // },
  handleClose() {

  },
  handleExpand() {
   this.isExpanded = !this.isExpanded
   if (this.isExpanded) {
    this.setTopHeight('70%')
   } else {
    this.setTopHeight('0%')
   }
  },
  handleClose() {
   this.setTopHeight('100%')
  },
  startDrag(event) {
   console.log('vdadjafpsdas', event)
   if (!this.isDragging) {
    this.isDragging = true
    this.startY = event.clientY
    const topHeight = document.getElementById('left-content')?.getBoundingClientRect()?.height
    this.startHeight = topHeight
    this.throttledOnDrag = throttle(this.onDrag, 100) // 100ms间隔
    document.addEventListener('mousemove', this.throttledOnDrag)
    document.addEventListener('mouseup', this.stopDrag)
   }
  },
  onDrag(event) {
   if (this.isDragging) {
    const deltaY = event.clientY - this.startY
    const newHeight = this.startHeight + deltaY
    // console.log('heightLeft', heightLeft, this.startHeight );
    // 添加边界检查
    const minHeight = 0
    const maxHeight = window.innerHeight - 269
    this.setTopHeight(`${Math.max(minHeight, Math.min(maxHeight, newHeight))}px`)
    // this.topHeight = `${Math.max(minHeight, Math.min(maxHeight, newHeight))}px`
    // this.topHeight = heightLeft + 'px'
    // this.rightWidth = this.totalWidth - heightLeft + 'px'
   }
  },
  stopDrag() {
   this.isDragging = false
   document.removeEventListener('mousemove', this.throttledOnDrag)
   document.removeEventListener('mouseup', this.stopDrag)
   // 取消未执行的 throttle
   if (this.throttledOnDrag?.cancel) {
    this.throttledOnDrag.cancel()
   }
  },
  editorDidMount(val) { },
  change(val) {
   this.showCodeData = val
  },
  handleUpdateContent(val) {
   this.showCodeData = val
  },
  async handleInit() {
   this.loading = true
   console.log('初始化')
   await GetDecision({
    scheme_id: this.$route.query.id,
    scheme_status: this.schemeStatus
   })
    .then((res) => {
     this.loading = false
     this.showCodeData = res.data.result?.decision_making_content || ''
     this.codeDataOri = res.data.result?.decision_making_content || ''
    })
    .finally(() => {
     this.loading = false
    })
  }
 }
}
</script>
<style lang="scss" scoped>
.no-height {
 :deep(.monaco-editor.no-user-select.showUnused.showDeprecated.vs-dark) {
  height: 0px !important;

  .overflow-guard {
   height: 0px !important;
  }
 }

 .drag-handle {
  display: none;
 }
}

.layout-test {
 display: flex;
 // flex: 1;
 height: 100%;
 overflow: hidden;
 min-height: 30px;
 flex-direction: column;

 :deep(.el-tabs__nav-wrap::after) {
  height: 0 !important;
 }

 :deep(.max_height) {
  flex: 1;
  overflow-y: auto;
 }

 // :deep(.vuepress-markdown-body) {
 //  overflow-y: auto;
 // }

 .drag-handle {
  cursor: ns-resize !important;
  height: 5px;
  width: 100%;
  background: #ccc;
  transition: background 0.2s;

  &:hover {
   background: #0078d4;
  }
 }
}

.code-test {
 flex: 1;
 overflow-y: hidden;
 position: relative;
 display: flex;
 flex-direction: column;

 .has-composite-bar {
  .composite-bar-container {
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 0 8px;

   i {
    cursor: pointer;
    padding: 3px;

    &:hover {
     background: #f5f5f5;
    }
   }

   .global-actions {
    display: flex;
   }
  }
 }
}

.task-card-content {
 height: 100%;

 .editor {

  height: 100%;
  width: 100%;
  margin-left: 1px;
 }
}

:deep(.el-loading-spinner) {
 width: 130px !important;
 background: none !important;
}
</style>
