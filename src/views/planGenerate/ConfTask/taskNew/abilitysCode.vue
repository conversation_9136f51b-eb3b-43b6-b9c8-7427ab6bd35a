<template>
  <div style="width: 100%;overflow:auto;height: 100%;">
    <div style="width: 100%;height: 30px;line-height: 30px;text-align: right;margin-bottom:10px">
      <div style="width: 100%; height: 30px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px">
        <div style="display: flex; align-items: center; cursor: pointer;" @click="goback">
          <i class="el-icon-back" style="margin-right: 5px;"></i>
          <span>返回</span>
        </div>
        <div style="display: flex; gap: 10px">
          <el-button type="primary" v-if="!saveStatus" @click="editCode">编辑</el-button>
          <el-button type="primary" v-if="saveStatus" @click="saveCode">保存</el-button>
          <el-button type="primary" v-if="saveStatus" @click="closeCode">取消</el-button>
        </div>
      </div>
    </div>
    <MonacoEditor
      id="codeCreated"
      class="editor"
      :value="routerFileCode"
      language="python"
      :scroll-beyond-last-line="true"
      :original="routerFileCode"
      theme="vs-dark"
      :diff-editor="false"
      :autoScroll="true"
      :options="options"
      @change="change"
    />
  </div>
</template>

<script>
import MonacoEditor from '@/components/MonacoEditor'
import { queryListFiles, queryObjectKey, abilityDel, generate_sign_url } from '@/api/planGenerateApi.js'
export default {
  name: 'AbilitysCode',
  props: {
    routerFileCode: {
      type: String,
      default: ''
    },
  },
  components: { MonacoEditor },
  data() {
    return {
      saveStatus: false,
      options: {
        theme: 'vs',  // 使用默认浅色主题
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      },
    }
  },
  methods: {
    goback() {
      this.$emit('goback')
    },
    closeCode() {
      this.options.readOnly = true
      this.saveStatus = false
    },
    editCode() {
      this.options.readOnly = false
      this.saveStatus = true
    },
    saveCode() {
      this.options.readOnly = true
      this.saveStatus = false
      this.$emit('saveCode', this.routerFileCode)
    },
    change(val) {
      this.routerFileCode = val
    },
  }
}
</script>

<style scoped>
.editor{
  height: calc(100% - 40px);
}
</style>
