<template>
  <!-- v-loading="status===3"
  element-loading-text="生成中..."
  element-loading-spinner="el-icon-loading" -->
  <div class="task-card-content" style="width: 100%; height: 100%">
    <div class="transition-box" v-if="mdContentText !== ''">
      <div v-if="isEdit" style="width: 100%; height: 100%">
        <v-md-editor id="editorFin" ref="editorFin" v-model="mdContentText" mode="editable"
          left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link  code"
          right-toolbar="preview" height="100%" @input="handleInput" @focus="handleFocus"></v-md-editor>
      </div>
      <div v-if="!isEdit && !errorFlag" style="width: 100%; height: 100%; overflow: scroll" class="editor-show">
        <pre ref="mathPre" v-if="mathPre" class="pre">{{ mdContentText }}</pre>
        <v-md-editor v-else id="editor555" ref="editor555" v-model="mdContentText" mode="preview"
          right-toolbar="preview" height="100%"></v-md-editor>
      </div>
      <div v-if="!isEdit && errorFlag" style="width: 100%; height: 100%">
        <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
          ">
          <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
          <div style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              margin-top: 16px;
            ">
            转换失败，请修正后重试
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        ">
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          ">
          暂无
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn'
import createLineNumbertPlugin from '@kangc/v-md-editor/lib/plugins/line-number/index'
import createCopyCodePlugin from '@kangc/v-md-editor/lib/plugins/copy-code/index'
import createKaTeXPlugin from '@kangc/v-md-editor/lib/plugins/katex/cdn';
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-python'
import hljs from 'highlight.js'
import { debounce } from '@/utils/util'

VMdEditor.use(vuepressTheme, {
  Prism,
  extend(md) {
    console.log('ddd', md)
    // md为 markdown-it 实例，可以在此处进行修改配置,并使用 plugin 进行语法扩展
    // md.set(option).use(plugin);
  }
})
VMdEditor.use(createKaTeXPlugin());
VMdEditor.use(createMermaidPlugin())
export default {
  components: {
    VMdEditor
  },
  props: {
    mdContent: {
      type: String,
      default() {
        return ''
      }
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default() {
        return 'test11'
      }
    },
    stopScroll: {
      type: Boolean,
      default: false
    },
    elBox: {
      type: String,
      default() {
        return ''
      }
    },
    status: {
      type: Number,
      default() {
        return 0
      }
    }
  },
  data() {
    return {
      mathPre: false,
      mdContentText: '',
      showFlag: false,
      scrollToBottomStatus:false,
      errorFlag: false
    }
  },
  watch: {
    mdContent: {
      handler(val) {
        this.scrollToBottomStatus = false
        const prevLength = this.mdContentText ? this.mdContentText.length : 0
        const currentLength = val ? val.length : 0
          if (currentLength > prevLength && Math.floor(currentLength / 10) > Math.floor(prevLength / 10)) {
            this.scrollToBottomStatus = true
          }
        this.mdContentText = val
        this.showFlag = this.isEdit
        this.errorFlag = false
        this.scrollToBottom()
        if (!this.isEdit) {
          this.$nextTick(function () {
            if (
              this.$refs.editor555 &&
              this.$refs.editor555.$el &&
              this.$refs.editor555.$el.querySelector('.vuepress-markdown-body') &&
              this.scrollToBottomStatus
            ) {
              console.log('scrollToBottomStatus')
              if (!this.stopScroll) {
                // 如果点击展示详情，则不需要滚动
                this.$refs.editor555.$el
                  .querySelector('.vuepress-markdown-body')
                  .scrollIntoView({ block: 'end', behavior: 'smooth' })
              }
            }
          })
        }
        // 针对生成数学模型自动滚动
        if (!this.isEdit && this.mathPre) {
          this.$nextTick(function () {
            this.$refs.mathPre.scrollTop = this.$refs.mathPre.scrollHeight
          })
        }
        window.mermaid.parseError = (err) => {
          console.log(
            '转换错误jieguo---',
            err,
            String(err).indexOf('Parse error'),
            '---',
            typeof err,
            err.str
          )
          if (err.str && String(err.str).indexOf('Parse error') > -1) {
            this.errorFlag = true
          } else {
            this.errorFlag = false
          }
        }
      },
      immediate: true,
      deep: true
    },
    isEdit: {
      handler(val) {
        this.showFlag = val
        if (val) {
          this.$nextTick(() => {
            if (this.$refs.editorFin && this.$refs.editorFin.currentMode !== 'edit') {
              this.$refs.editorFin.currentMode = 'edit'
            }
          })
        }
      },
      immediate: true
    },
  },
  created() {
    console.log('0000')
    if (
      this.mdContent.indexOf('mermaid') > -1 ||
      this.mdContent.indexOf('flow') > -1 ||
      this.mdContent.indexOf('graph') > -1 ||
      this.mdContent.indexOf('flowchart') > -1
    ) {
      VMdEditor.use(createMermaidPlugin())
    }
    this.removeKaTeXPlugin()
  },
  mounted() {},
  methods: {
    scrollToBottom() {
      const editor = this.$refs.editor555;
      if (editor) {
        // 获取内部编辑器实例（取决于 v-md-editor 版本）
        const cmInstance = editor.$el.querySelector('.CodeMirror')?.CodeMirror;
        if (cmInstance) {
          // CodeMirror 滚动到底部
          cmInstance.scrollTo(0, cmInstance.getScrollInfo().height);
        } else if (editor.getEditor) {
          // Monaco Editor 滚动到底部
          const monacoEditor = editor.getEditor();
          const model = monacoEditor.getModel();
          const lineCount = model.getLineCount();
          monacoEditor.revealLineInCenter(lineCount);
        }
      }
    },
    handleInput(val) {
      // console.log('更新的内容',val);
      this.$emit('updateContent', val)
    },
    handleFocus() {
      console.log('jujiao的内容')
    },
    loadKaTeXPlugin() {
      // 动态引入 KaTeX 插件
      import('@kangc/v-md-editor/lib/plugins/katex/cdn').then((module) => {
        const createKaTeXPlugin = module.default
        VMdEditor.use(createKaTeXPlugin())
        // 使用 requestAnimationFrame 分帧渲染
        requestAnimationFrame(() => {
          this.renderMath()
        })
      })
    },
    renderMath() {
      // 在这里进行数学公式渲染
      this.$nextTick(() => {
        this.removeKaTeXPlugin()
      })
    },
    removeKaTeXPlugin() {
      VMdEditor.use({ name: 'katex', enhance: null })
    }
  }
}
</script>
<style lang="less" scoped>
.task-card-content {
  //padding: 16px 20px;
  min-height: 200px;
}
/deep/ .vuepress-markdown-body {
  min-height: initial !important;
  height: initial !important;
}
/deep/ .pre-wrapper {
  min-height: initial !important;
  height: initial !important;
}
pre {
  height: 100%;
  overflow: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
