<template>
  <div class="task-card-content" v-loading="loading" element-loading-text="加载中..."
    element-loading-spinner="el-icon-loading">
    <div v-if="(Number(status) !== 0 && Number(status) !== 3) || summarize" style="height:  100%;">
      <!-- v-if="(deployStatus && !errorFlag) || summarize" -->
      <!-- {{ resultData}}{{ typeof resultData }} -->
      <MonacoEditor v-if='resultData' id="codeTest" class="editor" :autoScroll="true" :value="resultData" language="json"
        :scroll-beyond-last-line="true" theme="vs-dark" :diff-editor="false" :options="options" />
      <!-- v-if="errorFlag && !loading && !summarize" -->
      <el-result v-else-if="Number(status) === 2 && initEnd" icon="error" :subTitle="runCode === 'ability_check' ? '代码检查失败' : '代码测试失败'" />
      <!-- <el-result v-else icon="warning" subTitle="代码部署成功后会自动执行代码测试"/> -->
    </div>
    <div v-else>
      <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        ">
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          ">
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import MonacoEditor from '@/components/MonacoEditor'
// import Status from '@/components/Status/index.vue';
import { GetDecision, AbilityTest,updateLogTaskStatus } from '@/api/planGenerateApi.js'
import { OnRuleTest } from '@/api/ruleMarketApi.js'

import { safeStringToJSON, isValidJSON, log } from '@/utils/ThrottledLogger.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  components: { MonacoEditor },
  props: {
    schemeStatus: {
      type: String
    },
    updateTestFlag: {
      type: [Number, String],
      default: 1
    },
    status: {
      type: [String, Number],
      default: ''
    },
    summarize: {
      type: String,
      default: ''
    },
    runCode: {
      type: String,
      required: true
    },
    getTaskByRunCode: {
      type: Function,
      required: true
    },
    currentDetailrunCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      initEnd: false,
      loading: false,
      resultData: '',
      deployStatus: false, // 代码部署状态
      errorFlag: false, // 代码测试状态
      options: {
        selectOnLineNumbers: true,
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: true,
        theme: 'vs-dark',
        language: 'json',
        wordWrap: 'on', // 设置自动换行
        formatOnType: true, // 设置自动格式化
        formatOnPaste: true, // 设置自动格式化
        insertSpaces: true, // 设置缩进方式为插入空格
        tabSize: 2, // 设置缩进大小为2
        minimap: {
          enabled: false // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      }
    }
  },
  created() {
    this.handleInit()
  },
  mounted(){

  },
  watch: {
    resultData: {
      handler(val) {
        log('resultData 111', val);
        if (isValidJSON(val)){
          const JsonVal = safeStringToJSON(val)
          log(JsonVal);
          if (JsonVal?.success === false){
            this.$emit('handleOk', false)
          }
        }
      },
      immediate: true
    },
    summarize: {
      handler(val) {
        // if (Number(this.status) === 1) {
        // }
        console.log("summarize:", val);
        this.resultData = val
      },
      immediate: true
    },
    status: {
      async handler(val) {
        console.log('测试状态:', val)
        if (Number(val) === 3) {
          this.loading = true
        }
        if (Number(val) === 1) {
          this.deployStatus = true
          console.log("为啥this.deployStatus不是true? 3", this.deployStatus);
          if (this.runCode === 'ability_check') {
            if (this.resultData) {
              console.log("ABILITY_CHECK 直接返回");
              this.errorFlag = false
              this.loading = false
              this.$emit('handleOk', true)
              return
            } else {
              this.loading = false
              // this.$emit('handleOk', false)
              return
            }
          }else{
            await GetDecision({ scheme_id: this.$route.query.id, scheme_status: this.schemeStatus }).then(
              async (res) => {
                this.errorFlag = false
                const codeData = res.data.result?.decision_making_content || ''
                console.log('this.schemeStatus wtf 00', this.schemeStatus)

                // 调用接口
                switch (this.schemeStatus) {
                  case 'decision_ability':
                    if (
                      res.data.result?.ext_info?.deploy_status &&
                      res.data.result?.ext_info?.deploy_status === 'deployed'
                    ) {
                      await AbilityTest({
                        code_str: codeData,
                        scheme_id: this.$route.query.id
                      }).then(async (resTest) => {
                        console.log('测试结果3341', resTest)
                        this.loading = false
                        const isTestSuccess = resTest?.data?.result.resp.success
                        let resStr = resTest.data.result?.resp
                          ? JSON.stringify(resTest.data.result?.resp, null, 2)
                          : ''
                        if (resTest?.data?.code !== 200 || isTestSuccess === false) {
                          this.resultData = resStr
                          this.$emit('handleOk', false)
                          this.errorFlag = true
                          // this.$message.error(resTest?.data?.msg || '测试失败');
                        } else {
                          await updateLogTaskStatus({
                            scheme_id: this.$route.query.id,
                            "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
                            "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
                            task_status: 1
                          });
                          this.errorFlag = false
                          this.$emit('handleOk', true)
                          try {
                            this.resultData = resStr
                          } catch (error) {
                            this.resultData = resStr
                          }finally {
                            this.resultData = resStr
                          }
                        }
                      })
                    } else {
                      this.deployStatus = false
                      console.log("哪里设置deployStatus flase的1", this.deployStatus);

                      this.resultData = ''
                    }
                    break

                  case 'rule':
                    await OnRuleTest({
                      rule_content: codeData,
                      scheme_id: this.$route.query.id
                    }).then((resTest) => {
                      console.log('测试结果3342', resTest)
                      this.loading = false
                      if (resTest?.data?.code !== 200) {
                        this.$emit('handleOk', false)
                        this.errorFlag = true
                        // this.$message.error(resTest?.data?.msg || '测试失败');
                      } else {
                        this.errorFlag = false
                        this.$emit('handleOk', true)
                        try {
                          this.resultData = resTest.data.result?.result
                            ? JSON.stringify(resTest.data.result?.result, null, 2)
                            : ''
                        } catch (error) {
                          this.resultData = resTest.data.result?.result || ''
                        }
                      }
                    })
                    break
                  default:
                    // 如果表达式的值不匹配任何 case，则执行这里的代码
                    console.log('都不是 00')
                    break
                }
              }
            )
          }
        }
        if (Number(val) === 2) {
          this.deployStatus = true
          console.log("为啥this.deployStatus不是true? 1", this.deployStatus);
          this.errorFlag = true
          this.loading = false
        }
      },
      immediate: true
    },
    updateTestFlag: {
      handler(val) {
        if (val !== 1) {
          // this.handleInit()
        }
      },
    }
  },
  methods: {
    async handleInit() {
      this.loading = true
      console.log('初始化 summarize:', this.summarize, this.updateTestFlag, this.runCode)
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: this.schemeStatus
      }).then(async (res) => {
        this.deployStatus = true
        console.log("为啥this.deployStatus不是true? 2", this.deployStatus);
        // 调用接口
        const codeData = res.data.result?.decision_making_content || ''
        if (this.runCode === 'ability_check'){
          if (this.resultData) {
            console.log("ABILITY_CHECK 直接返回");
            this.errorFlag = false
            this.loading = false
            this.$emit('handleOk', true)
            return
          }else{
            this.loading = false
            // this.$emit('handleOk', false)
            return
          }
        }else{
          switch (this.schemeStatus) {
            case 'decision_ability':
              if (
                res.data.result?.ext_info?.deploy_status &&
                res.data.result?.ext_info?.deploy_status === 'deployed'
              ) {
                await AbilityTest({
                  code_str: codeData,
                  scheme_id: this.$route.query.id
                }).then(async (resTest) => {
                  console.log('测试结果33411', resTest)
                  this.loading = false
                  const isTestSuccess = resTest?.data?.result.resp.success
                  let resStr = resTest.data.result?.resp
                    ? JSON.stringify(resTest.data.result?.resp, null, 2)
                    : ''
                  if (resTest?.data?.code !== 200 || isTestSuccess === false) {
                    this.resultData = resStr
                    this.$emit('handleOk', false)
                    log('emit flase 不是吗？')
                    this.errorFlag = true
                    // this.$message.error(resTest?.data?.msg || '测试失败');
                  } else {
                    await updateLogTaskStatus({
                    scheme_id: this.$route.query.id,
                    "execute_instruction": this.getTaskByRunCode(this.currentDetailrunCode).execute_instruction,
                    "task_desc": this.getTaskByRunCode(this.currentDetailrunCode).task_desc,
                    task_status: 1
                  });

                    this.errorFlag = false
                    this.$emit('handleOk', true)
                    try {
                      this.resultData = resStr
                    } catch (error) {
                      this.resultData = resStr
                    } finally {
                      this.resultData = resStr
                    }
                  }
                })
              } else {
                this.deployStatus = false
                console.log("哪里设置deployStatus flase的2", this.deployStatus);
                this.resultData = ''
              }
              break

            case 'rule':
              await OnRuleTest({
                rule_content: codeData,
                scheme_id: this.$route.query.id
              }).then((resTest) => {
                console.log('测试结果33422', resTest)
                this.loading = false
                if (resTest?.data?.code !== 200) {
                  this.$emit('handleOk', false)
                  this.errorFlag = true
                  // this.$message.error(resTest?.data?.msg || '测试失败');
                } else {
                  this.errorFlag = false
                  this.$emit('handleOk', true)
                  try {
                    this.resultData = resTest.data.result?.result
                      ? JSON.stringify(resTest.data.result?.result, null, 2)
                      : ''
                  } catch (error) {
                    this.resultData = resTest.data.result?.result || ''
                  }
                }
              })
              break
            default:
              // 如果表达式的值不匹配任何 case，则执行这里的代码
              console.log('都不是 0011')
              break
          }
        }
      }).finally(() => {
        this.loading = false;
      })
      this.initEnd = true
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  // padding: 16px 20px;
  height: 100%;
  .editor {
    height: 100%;
    width: 100%;
    margin-left: 1px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
:deep(.monaco-editor){
  height: 100% !important;
}
</style>
