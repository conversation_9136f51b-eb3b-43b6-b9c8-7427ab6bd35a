<template>
  <div class="task-card-content" id="taskTable">
    <template>
      <div v-loading="loading || taskLoading" class="optScroll" element-loading-text="多模态数据对齐中..."
        element-loading-spinner="el-icon-loading">
        <template v-if="isEdit">
          <el-input id="detail-content" v-model.trim="codeData" type="textarea" :autosize="{ minRows: 2, maxRows: 18 }"
            placeholder="请输入" />
        </template>
        <template>
          <div v-if="treeStatus === 3" style="width: 100%; height: 100%">
            <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                width: 100%;
              ">
              <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
              <div style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: center;
                  margin-top: 16px;
                ">
                对齐失败，请重试
              </div>
            </div>
          </div>
          <div v-else-if="dataStatus === 'failed'" style="margin-top: 20px; width: 100%; height: 100%">
            <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                width: 100%;
              ">
              <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
              <div style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: center;
                  margin-top: 16px;
                ">
                数据抽取失败，请重试
              </div>
            </div>
          </div>
          <div v-else v-loading="['generating', 'init'].includes(dataStatus) || loading || changeloading"
            style="width: 100%; height: 100%" element-loading-text="加载中..." element-loading-spinner="el-icon-loading"
            custom-class="el-loading-spinner2" class="table-box">
            <div ref="tabelRef" class="duiqi-box">
              <!-- <div class="title">库表多模态数据对齐</div> -->
              <el-table v-show="dataStatus !== 'failed' && dataThDatas.length" size="medium"
                :height="displayData.length ? tableHeight : 40" style="width: 100%"
                :header-cell-style="{ background: '#F6F7FB', color: '#323233' }" class="transition-box"
                :data="displayData">
                <el-table-column v-for="(item, index) in dataThDatas" :key="item.field" min-width="170"
                  :prop="item.field" :label="item.name">
                  <template #default="scope">
                    <div v-if="item.field === 'a_param_desc'">
                      <el-input v-model.trim="scope.row[item.field]" :disabled="hasChatingName !== ''"
                        placeholder="请输入"></el-input>
                    </div>
                    <div v-else-if="item.field === 'b_type'">
                      <el-select v-model="scope.row[item.field]" :disabled="hasChatingName !== ''" placeholder="请选择"
                        @change="(val) => changeCType(val, scope.$index)">
                        <el-option v-for="item in configList" :key="item.value" :label="item.name"
                          :value="item.value"></el-option>
                        <!-- <el-option label="API" value="API"></el-option>
                          <el-option label="库表" value="库表"></el-option> -->
                      </el-select>
                    </div>
                    <div v-else-if="item.field === 'c_source'">
                      <el-select v-if="scope.row.b_type === '库表'" v-model="scope.row[item.field]"
                        :disabled="hasChatingName !== ''" placeholder="请选择"
                        @change="(val) => handleFiedsByTable(val, scope.$index)">
                        <el-option v-for="item in tablesList" :key="item.table" :label="item.table" :value="item.table">
                        </el-option>
                      </el-select>
                      <el-select v-else-if="scope.row.b_type === 'RULE'" v-model="scope.row[item.field]"
                        :disabled="hasChatingName !== ''" placeholder="请选择"
                        @change="(val) => handleFiedsByRule(val, scope.$index)">
                        <el-option label="业务规则" value="RULE"></el-option>
                      </el-select>
                      <el-select v-else-if="scope.row.b_type === 'API'" v-model="scope.row[item.field]"
                        :disabled="hasChatingName !== ''" placeholder="请选择"
                        @change="(val) => handleFiedsByAPI(val, scope.$index)">
                        <el-option v-for="apiType in useAPIList" :label="apiType.value"
                          :value="apiType.name"></el-option>
                      </el-select>
                      <el-select v-else v-model="scope.row[item.field]" :disabled="hasChatingName !== ''"
                        placeholder="请选择">
                        <el-option label="人工输入" value="人工输入"></el-option>
                      </el-select>
                    </div>
                    <div v-else-if="item.field === 'd_mapping_field'">

                      <el-select v-if="scope.row.b_type === 'RULE'" :key="index + 'field'"
                        v-model="scope.row[item.field]" :disabled="hasChatingName !== ''" filterable placeholder="请选择"
                        @change="(val) => handleFiledRuleChange(val, scope.$index)">
                        <el-option v-for="(fitem, findex) in scope.row.fieldsList" :key="index + fitem.name + findex"
                          :label="fitem.name" :value="fitem.name">
                          <span style="float: left">{{ fitem.name }}</span>
                          <!-- <span style="float: right; color: #8492a6; font-size: 12px">{{ fitem.COLUMN_COMMENT }}</span> -->
                        </el-option>
                      </el-select>

                      <el-select v-else-if="scope.row.b_type === '库表'" :key="index + 'field'"
                        v-model="scope.row[item.field]" :disabled="hasChatingName !== ''" filterable placeholder="请选择"
                        @change="(val) => handleFiledChange(val, scope.$index)">
                        <el-option v-for="(fitem, findex) in scope.row.fieldsList"
                          :key="index + fitem.COLUMN_NAME + findex" :label="fitem.COLUMN_NAME"
                          :value="fitem.COLUMN_NAME">
                          <span style="float: left">{{ fitem.COLUMN_NAME }}</span>
                          <span style="float: right; color: #8492a6; font-size: 12px">{{
                            fitem.COLUMN_COMMENT
                            }}</span>
                        </el-option>
                      </el-select>

                      <el-select v-else-if="scope.row.b_type === 'API'" v-model="scope.row[item.field]"
                        :disabled="hasChatingName !== ''" filterable placeholder="请选择"
                        @change="(val) => handleFiledApiChange(val, scope.$index)">
                        <el-option v-for="(fitem, findex) in scope.row.fieldsList" :key="fitem.code_func_name + findex"
                          :label="fitem.code_name" :value="fitem.code_func_name">
                        </el-option>
                      </el-select>

                      <el-input v-else v-model.trim="scope.row[item.field]" :disabled="hasChatingName !== ''" clearable
                        placeholder="请输入" />
                    </div>
                    <div v-else>
                      <div v-if="scope.row.b_type === 'API' || scope.row.b_type === '库表'">
                        {{ scope.row[item.field] }}
                      </div>
                      <el-input v-else v-model.trim="scope.row[item.field]" :disabled="hasChatingName !== ''" clearable
                        placeholder="请输入" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="x" fixed="right" label="操作" class-name="no-bor" width="94">
                  <template slot-scope="scope">
                    <el-tooltip v-if="scope.row.b_type === 'API' && scope.row.d_mapping_field" class="item"
                      effect="dark" content="接口详情" placement="top">
                      <el-link :underline="false" icon="el-icon-paperclip" style="margin-right: 12px"
                        @click="handleLink(scope.row, scope.$index)"></el-link>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="删除" placement="top">
                      <el-link :underline="false" :disabled="hasChatingName !== ''" icon="el-icon-remove-outline"
                        @click="handleRemove(scope.$index)"></el-link>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="新增" placement="top">
                      <el-link v-if="isLastRow(scope.$index)" :disabled="hasChatingName !== ''" :underline="false"
                        style="margin-left: 12px" icon="el-icon-circle-plus-outline"
                        @click="handleAddRow(displayData.length)"></el-link>
                      <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                    </el-tooltip>
                    <!-- <el-link type="primary" @click="handleRemove(scope.$index)">删除</el-link> -->
                  </template>
                </el-table-column>
              </el-table>
              <div v-show="dataStatus !== 'failed' && dataThDatas.length && displayData.length === 0"
                style="text-align: right; padding-right: 8px">
                <el-tooltip class="item" effect="dark" content="新增" placement="top">
                  <el-link :underline="false" :disabled="hasChatingName !== ''" style="margin-left: 12px"
                    icon="el-icon-circle-plus-outline" @click="handleAddRow(displayData.length)"></el-link>
                  <!-- <el-link v-if="(gridData.length - 1) === scope.$index" :underline="false" style="margin-left: 12px;" icon="el-icon-circle-plus-outline" @click="handleAddRow(gridData.length)"></el-link> -->
                </el-tooltip>
              </div>
              <div v-if="gridData.length" class="page">
                <el-pagination small :page-size="pageSize" :current-page="pageNo" layout="prev, pager, next"
                  :total="gridData.length" @current-change="handleCurrentChange">
                </el-pagination>
              </div>
              <div v-if="!dataThDatas.length" class="emptyData">
                <img src="@/assets/images/planGenerater/empty.png" />
                <div style="font-size: 14px; color: #323233">暂无</div>
              </div>
            </div>
            <!-- <div style='margin-top:15px;width: 100%;display: flex;flex-direction: column;align-items: center'> -->
            <!-- <el-button v-if="dataThDatas.length" type="primary" :disabled ="saveLoading" @click="handleDone">完成</el-button> -->
            <!-- </div> -->
            <!-- <vue-markdown v-highlight :source="codeData" class="markdown-body"></vue-markdown> -->
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script type="text/javascript">
import { RunCode } from '../constants.js'
import { mapGetters } from 'vuex'
import {
  CodeEdit,
  GetDecision,
  AbilityPublish,
  AbilityTest,
  queryAbilityMapping,
  getTablesList,
  queryFieldsByTable,
  updateAbilityMapping,
  SchemeDetail,
  queryNengliList,
  addCache,
  removeCache,
  queryCache,
  queryDuiqiApiName,
  queryTempIfFromScene,
  querySchemeDetailById,
  queryApiMixFuncList
} from '@/api/planGenerateApi.js'
import { OnRuleTest } from '@/api/ruleMarketApi.js'

import { ruleMarketListApi } from '@/api/ruleMarketApi.js';
// import Status from '@/components/Status/index.vue';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import treeProcess from '../../treeProcess.vue'
import nengliTestReult from '../../nengliTest.vue'
import chooseTable from '../../chooseTable.vue'
import chooseTableNew from '../../chooseTableNew.vue'
import editSql from '../../editSql.vue'
// import mermaid from 'mermaid'
import panzoom from 'panzoom'
import MyEditor from '../../mdEditorPreview.vue'

const {
  KNOWLEDGE_BASE_SEARCH,
  CREATE_QUESTIONS_RESULT,
  SCHEME_OPTIMIZE,
  DECISION_TREE,
  ALIGN_ANALYSIS,
  MODEL_PARAM_EXTRACTION,
  MODELING_INFO_STRUCTURE,
  MATH_MODEL_GENERATE,
  ALIGN_DATA_GENERATE,
  DECISION_MAKING_GENERATE,
  CODE_DEPLOY,
  CODE_TEST,
  CODE_ANALYSIS,
  API_PARAM_ANALYSIS
} = RunCode

export default {
  name: 'ExampleCom',
  // Status, MyEditor
  components: { treeProcess, editSql, chooseTable, chooseTableNew, MyEditor },
  props: {
    schemeStatus: {
      type: String
    },
    agentSenceCode: {
      type: String,
      default: ''
    },
    agentScene: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    sqlStatus: {
      type: Number,
      default: -1
    },
    sqlData: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    dataAlignDataStatus: {
      type: [String, Number],
      default: ''
    },
    dataAlignData: {
      type: Array,
      default: []
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    }),
    displayData() {
      const startIndex = (this.pageNo - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.gridData.slice(startIndex, endIndex)
    },
    displaySuanfaData() {
      const startIndex = (this.pageNo2 - 1) * this.pageSize2
      const endIndex = startIndex + this.pageSize2
      return this.suanfaData.slice(startIndex, endIndex)
    }
  },
  watch: {
    displayData: {
      handler(val) {
        console.log('bro displayData 10', val)
      },
      immediate: true
    },
    dataStatus: {
      handler(val) {
        console.log('bro dataStatus 00', val, this.loading, this.changeloading)
      },
      immediate: true
    },
    treeStatus: {
      handler(val) {
        this.treeStatusLast = val
        if (val === 2) {
          this.taskLoading = false
          this.handleAbilityMapping()
        } else if (val === 0) {
          this.taskLoading = false
        } else if (val === 3) {
          this.taskLoading = false
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.codeData = val
      },
      immediate: true
    },
    sqlStatus: {
      handler(val) {
        this.sqlStatusVal = val
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.codeProcess = val
      },
      immediate: true
    },
    dataAlignDataStatus: {
      handler(val) {
        if (Number(val) === 2 || Number(val) === 3) {
          this.handleInit()
          // this.tableData = this.dataAlignData
          // console.log('渲染', this.dataAlignData);
        } else {
          if (Number(val) !== 0) {
            console.log('数据对齐中', val)
            this.loading = true
            const temp = document.getElementById('taskTable')
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' })
              temp.scrollTop = temp.scrollHeight + 200
            }
          } else {
            console.log('还未对齐过')
          }
        }
      },
      immediate: true
    },
    status: {
      handler(val) {
        console.log('align status 008123', val, this.loading, this.taskLoading)
        if (Number(val) !== 3) {
          this.loading = false
        }
       if (Number(val) === 3) {
          this.loading = true
         console.log('align status 008456', val, this.loading, this.taskLoading)

        }
      },
      immediate: true
    }
  },
  data() {
    return {
      detailContent: '',
      showType: 1,
      loading: false,
      saveLoading: false,
      showSqlModalVisable: false, // 生成sql弹窗
      showChooseTableVisable: false, // 选择数据表定义窗口弹窗
      isSuperAdmin: false, // 是否超级管理员
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      hisDetail: '',
      processContent: { text: '' },
      processList: [],
      taskList: [],
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      currentText: '',
      socket: null,
      systemMessages: '',
      taskStatusText: '',
      agentError: false,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskLoading: false,
      writeFlag: true,
      writeText: '',
      greets: '',
      taskGenType: '',
      sessionId: '',
      treeData: '',
      codeData: '',
      codeProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      hisCode: '', // 编辑能力代码生成代码时的数据
      testVisable: false, // 能力测试窗口
      testReq: '', // 能力测试请求参数
      testResult: '', // 能力测试结果
      treeStatusLast: 0,
      panZoomRef: null,
      gridData: [], // 多模态数据对齐表数据
      dataThs: [],
      dataThDatas: [], // 多模态数据对齐表头数据
      dataStatus: '', // 多模态数据对齐状态
      tablesList: [], // 表数据
      fieldsList: [], // 字段名
      sqlStatusVal: -1,
      tableHeight: '90%', // 表格高度
      samllHeight: '90%', // 表格高度
      changeloading: false,
      redoFlag: false, // 判断是否重新生成过/保存过
      sqlLastData: '',
      suanfaData: [], // 算法公式对齐数据
      biaoDatas: [],
      verifyBiaoDatas: [], // 校验比对
      verifySuanfaData: [], // 校验比对
      suanfaList: [], // 算法列表
      useAPIList: [],
      dataThs2: [],
      dataThDatas2: [], // 多模态数据对齐表头数据
      pageNo: 1,
      pageSize: 10,
      pageNo2: 1,
      pageSize2: 10,
      timer: null,
      isCache: false,
      isCacheDisabled: true,
      initChooseData: { table: [], tag_ids: [] },
      configList: [{ name: '人工', value: '人工' }],
      // configList:
      //   this.agentSenceCode === 'visit_leader_cognition_scene' ||
      //   this.agentSenceCode === 'intelligent_conversation_scene'
      //     ? [
      //         {
      //           name: 'API/函数',
      //           value: 'API'
      //         }
      //       ]
      //     : [
      //         {
      //           name: 'API/函数',
      //           value: 'API'
      //         },
      //         {
      //           name: '库表',
      //           value: '库表'
      //         }
      //       ],
      apiNameMap: {},
      templateId: '',
      loading: false,
      tableData: [],
      dataThDatas: []
    }
  },
  async mounted() {
    console.log('宽度', this.rightWidth)
    this.taskStatus = this.$route.query.status
    const nodeMarkmap = document.getElementById('markmap')
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = ''
    }
    // if (
    //   this.agentSenceCode === 'device_ops_assistant_scene' ||
    //   this.agentSenceCode === 'device_ops_assistant_scene-v1' ||
    //   this.agentSenceCode === 'artificial_handle_scene' ||
    //   this.agentSenceCode === 'visit_leader_cognition_scene' ||
    //   this.agentSenceCode === 'rule_generation_scene' ||
    //   this.agentSenceCode === 'intelligent_conversation_scene'
    // ) {
    //   await this.queryDecision('decision_tree') // 思维树
    // }
    if (this.$refs.tabelRef) {
      console.log('tabelRef', this.$refs.tabelRef, this.$refs.tabelRef.scrollHeight)
      this.tableHeight = this.$refs.tabelRef.scrollHeight - 40
      this.smallHeigth = this.$refs.tabelRef.scrollHeight - 40
    }
    await this.apiAllName()
    await this.handleAbilityMapping() // 多模态数据对齐
    await this.queryTemplate() // 查询数据对齐分析模版ID
    await this.queryPlanDetail()
    // this.handleInit();
    console.log('gridData 00', this.gridData)
    console.log('displayData 00', this.displayData)
  },
  updated(){
    if ((this.status) === 3) {
      this.loading = true;
    }else{
      console.log("this.loading 1122", this.loading);
    }
    console.log('loading:', this.loading);
  },
  beforeDestroy() {
    clearTimeout(this.timer)
    this.timer = null
  },
  methods: {
    // 表中对应的列
    async handleFiedsByRule(tableName, index) {
      // this.changeloading = true;
      const tempData = this.gridData.map(item => {
        return item;
      })
      const rowIndex = (this.pageNo - 1) * this.pageSize + index;
      const params = {
        rule_name: '',
        updated_id: '',
        scene_id: '',
        owner_id: '',
        page_num: 1,
        page_size: 200,
        tag_ids: []
      };
      await ruleMarketListApi(params).then((res) => {
        console.log('规则列表', res);
        if (res.status === 200 && res.data.code === 200) {
          console.log('列数据', res.data.result?.items);
          const temp = res.data.result?.items || [];
          this.fieldsList[index] = temp;
          console.log('fieldsList', this.fieldsList);
          tempData[rowIndex].fieldsList = temp;
          tempData[rowIndex].d_mapping_field = temp[0].name
          const filter = this.fieldsList[rowIndex].filter(item => item.name === temp[0].name);
          tempData[rowIndex].e_mapping_desc = filter[0].description
          this.gridData = tempData;
          this.changeloading = false;
        } else {
          this.changeloading = false;
          this.$message({
            type: 'success',
            message: '接口错误!'
          });
        }
      })

    },
    async queryTemplate() {
      queryTempIfFromScene({ scheme_id: this.$route.query.id }).then((res) => {
        console.log('获取模版ID接口-', res.data)
        this.templateId = res.data?.id || ''
      })
    },
    async apiAllName() {
      queryDuiqiApiName().then((res) => {
        console.log('map', res.data)
        try {
          const apiNames = eval(res.data)
          // console.log('名称映射',apiNames);
          const temp = {}
          apiNames.forEach((item) => {
            temp[item.code] = item.name
          })
          this.apiNameMap = temp
          console.log('名称映射', this.apiNameMap)
        } catch (error) { }
      })
    },
    async queryCacheHandle() {
      queryCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(
        async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result) {
              this.isCache = res.data.result.isCache
              this.isCacheDisabled = false
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        }
      )
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showSikao()
      } else if (command === 'addCache') {
        addCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(
          async (res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                type: 'success',
                message: res.data?.result || '新增成功'
              })
              this.isCache = !this.isCache
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          }
        )
      } else if (command === 'removeCache') {
        removeCache({ scheme_id: this.$route.query.id, ability_name: 'data_tree_generate' }).then(
          async (res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                type: 'success',
                message: res.data?.result || '删除成功'
              })
              this.isCache = !this.isCache
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          }
        )
      }
    },
    changeChart(type) {
      this.showType = type
    },
    isLastRow(rowIndex) {
      const lastindex = (this.pageNo - 1) * this.pageSize + rowIndex
      // console.log(lastindex,'最后一行')
      // const lastRowIndex = (this.pageNo - 2) * this.pageSize + this.displayData.length - 1;
      return lastindex === this.gridData.length - 1
    },
    isLastRow2(rowIndex) {
      const lastindex = (this.pageNo2 - 1) * this.pageSize2 + rowIndex
      // const lastRowIndex = (this.pageNo - 2) * this.pageSize + this.displayData.length - 1;
      return lastindex === this.suanfaData.length - 1
    },
    handleCurrentChange(val) {
      this.pageNo = val
    },
    handleCurrentChange2(val) {
      this.pageNo2 = val
    },
    closeChooseTableEdit(res, tableName, tags, manual_input) {
      console.log('关闭数据表定义', res, '---')
      this.showChooseTableVisable = false
      if (res !== undefined && res !== '') {
        this.codeData = ''
        this.codeProcess = ''
        this.taskLoading = true
        this.redoFlag = true
        if (res === 'close') {
          this.$emit('updateCodeGenerate', {
            table_schema: '',
            align_type: { table: tableName, tag_ids: tags.length ? tags : null, manual_input }
          })
        } else {
          this.$emit('updateCodeGenerate', {
            table_schema: res,
            align_type: { table: tableName, tag_ids: tags.length ? tags : null, manual_input }
          })
        }
      }
    },
    emitSQL() {
      this.redoFlag = false
      this.$emit('updateSQLSteam')
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    // 显示生成sql窗口
    showSqlModal() {
      this.sqlStatusVal = -1
      this.showSqlModalVisable = true
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示', this.codeProcess)
      this.processVisable = true
    },
    closeSikaoRizhi() {
      this.processVisable = false
    },
    // 部署方法
    async bushuFun() {
      console.log('部署')
      if (this.codeData !== '') {
        // 调用接口
        const res = await AbilityPublish({
          code_str: this.codeData,
          scheme_id: this.$route.query.id
        })
        if (res?.data?.code !== 200) {
          this.$message.error(res?.data?.msg || '部署失败')
          return
        }
        this.$message.success('能力代码生成部署成功')
      }
    },
    // 测试方法
    async testFun() {
      if (this.codeData !== '') {
        this.testVisable = true
        let res // 在 switch 语句外部声明 res 变量
        switch (this.schemeStatus) {
          case 'decision_ability':
            res = await AbilityTest({
              code_str: this.codeData,
              scheme_id: this.$route.query.id
            })
            if (res?.data?.code !== 200) {
              this.$message.error(res?.data?.msg || '测试失败')
            } else {
              this.testResult = res.data.result?.resp || ''
            }
            break
          case 'rule':
            res = await OnRuleTest({
              rule_content: this.codeData,
              scheme_id: this.$route.query.id
            })
            if (res?.data?.code !== 200) {
              this.$message.error(res?.data?.msg || '测试失败')
            } else {
              this.testResult = res.data.result?.result || ''
            }
            break
        }
        // 调用接口
      }
    },
    // 关闭保存
    handleCodeSaveClose() {
      this.codeData = this.hisCode
      this.isEdit = false
    },
    // 编辑能力代码生成保存
    async handleCodeSave() {
      this.isEdit = false
      // 调用接口
      const res = await CodeEdit({
        text: this.codeData,
        scheme_status: 'decision_ability',
        scheme_id: this.$route.query.id
      })
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败')
        return
      }
      this.$message.success('编辑成功')
      // 编辑成功后重新查询一次最新能力代码生成代码
      await this.queryDecision(this.schemeStatus) // 能力代码生成
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
      this.$nextTick(() => {
        mermaid.run({
          querySelector: '.language-mermaid'
        })
      })
    },
    thinkingHandle() {
      if (this.treeData) {
        if (
          this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1
        ) {
        } else {
          const transformer = new Transformer()
          const { root } = transformer.transform(this.treeData)
          this.$nextTick(() => {
            Markmap.create('#markmap', null, root)
          })
        }
      }
    },
    validToComparison() {
      // console.log(JSON.stringify(this.gridData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log('verify',JSON.stringify(this.verifyBiaoDatas.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.gridData.map(({fieldsList,isAdd,...rest}) =>rest)) ===  JSON.stringify(this.verifyBiaoDatas.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.suanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log('verify',JSON.stringify(this.verifySuanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      // console.log(JSON.stringify(this.suanfaData.map(({fieldsList,isAdd,...rest}) =>rest)) ===  JSON.stringify(this.verifySuanfaData.map(({fieldsList,isAdd,...rest}) =>rest)))
      return (
        JSON.stringify(this.gridData.map(({ fieldsList, isAdd, ...rest }) => rest)) ===
        JSON.stringify(this.verifyBiaoDatas.map(({ fieldsList, isAdd, ...rest }) => rest)) &&
        JSON.stringify(this.suanfaData.map(({ fieldsList, isAdd, ...rest }) => rest)) ===
        JSON.stringify(this.verifySuanfaData.map(({ fieldsList, isAdd, ...rest }) => rest))
      )
    },

    changeViews(val, type) {
      // if (this.redoFlag && type === 'next') {
      if (type === 'next') {
        this.handleDone()
        // if(!this.validToComparison()){
        //   this.$message.warning('多模态数据对齐数据不一致，请手动保存后再进行下一步！');
        // }else{
        //   this.$emit('updateStep',val)
        // }
      } else {
        this.$emit('updateStep', val)
      }
      this.dataDuiqiZhuGe(type === 'next' ? '下一步' : '上一步')
    },
    regenerate() {
      this.showChooseTableVisable = true
      // this.codeData = ''
      // this.codeProcess = '';
      // this.taskLoading = true;
      // this.redoFlag = true;
      // this.$emit('updateCodeGenerate','decision_ability')
      this.dataDuiqiZhuGe('多模态数据对齐')
    },
    regenerateRengong() {
      this.codeData = ''
      this.codeProcess = ''
      this.taskLoading = true
      this.redoFlag = true
      this.$emit('updateCodeGenerate', { table_schema: '' })
      this.dataDuiqiZhuGe('多模态数据对齐')
    },
    dataDuiqiZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
      })
    },
    async closeSqlEdit() {
      this.showSqlModalVisable = false
      this.loading = false
      await queryAbilityMapping({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.sqlLastData = res.data.result?.sql
            this.loading = false
          } else {
            this.loading = false
          }
        })
        .catch((_err) => {
          this.loading = false
        })
        .finally(() => { })
    },
    handleAbilityMapping() {
      console.log('handleAbilityMapping 01')
      this.saveLoading = true
      this.loading = true
      queryAbilityMapping({ scheme_id: this.$route.query.id })
        .then((res) => {
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.sqlLastData = res.data.result?.sql
            this.codeProcess = res.data.result.sub_content
            this.dataStatus = res.data.result.ability_status
            this.initChooseData = res.data.result?.align_type || {
              table: [],
              tag_ids: [],
              manual_input: false
            }
            if (res.data.result?.align_type) {
              const temp = []
              console.log('tables类型', typeof res.data.result?.align_type?.table)
              if (res.data.result?.align_type?.table) {
                if (typeof res.data.result?.align_type?.table === 'string') {
                  temp.push({ name: '库表', value: '库表' })
                } else {
                  if (res.data.result?.align_type?.table.length) {
                    temp.push({ name: '库表', value: '库表' })
                  }
                }
              }
              if (res.data.result?.align_type?.rule_tag_ids) {
                temp.push({ name: '业务规则', value: 'RULE' })
              }
              if (
                res.data.result?.align_type?.tag_ids &&
                res.data.result?.align_type?.tag_ids?.length
              ) {
                temp.push({ name: 'API/函数', value: 'API' })
              }
              if (res.data.result?.align_type?.manual_input) {
                temp.push({ name: '人工', value: '人工' })
              }
              this.configList = temp
            }
            console.log('多模态数据对齐结果', this.initChooseData)
            const status = res.data.result.ability_status
            const configData = res.data.result?.config || {}
            console.log("res,data", res.data, res.data.result?.config);

            console.log('configData 00', configData)
            this.queryCacheHandle()
            if (status !== 'generating') {
              this.saveLoading = false
              clearTimeout(this.timer)
              this.timer = null
              console.log('configData 111111', configData, '111')
              this.dataThs = configData?.header || {}
              this.dataThDatas = Object.keys(configData?.header || {}).map((item) => {
                return { name: configData?.header[item], field: item }
              })
              console.log(
                'dataThDatas 01',
                JSON.stringify(this.dataThDatas, null, 2),
                this.dataThDatas
              )
              // this.dataThs2 = configData.content?.algorithm_map?.header || {};
              // this.dataThDatas2 = Object.keys(configData.content?.algorithm_map?.header||{}).map(item => {return {name: configData.content?.algorithm_map?.header[item], field: item}})
              // console.log('ziduan', this.dataThDatas2);
              const temp =
                configData?.data?.map((item) => {
                  return {
                    ...item
                  }
                }) || []
              // const temp2 = configData.content?.algorithm_map?.data || [];
              this.biaoDatas = temp
              this.verifyBiaoDatas = [...temp]
              // this.suanfaData = temp2;
              // this.verifySuanfaData = [...temp2];
              if (false) {
                this.gridData = temp
                this.loading = false
              } else {
                console.log('还进不来吗？', temp);

                const resultF = []
                temp.forEach(async (item) => {
                  if (item.b_type === 'RULE') {
                    const params = {
                      rule_name: '',
                      updated_id: '',
                      scene_id: '',
                      owner_id: '',
                      page_num: 1,
                      page_size: 200,
                      tag_ids: []
                    };

                    const promise1 = ruleMarketListApi(params);
                    console.log('规则列表-', promise1)
                    resultF.push(promise1);
                  }
                  else if (item.b_type === 'API') {
                    const promise1 = queryApiMixFuncList({
                      page: 1,
                      page_size: 200,
                      code_status: null,
                      code_type: item.c_source,
                      keyword: '',
                      tag_ids: res.data.result?.align_type?.tag_ids || []
                    })
                    console.log('hhhh', promise1)
                    resultF.push(promise1)
                  } else if (item.b_type !== 'API' && item.c_source) {
                    const promise1 = queryFieldsByTable({
                      tableName: item.c_source,
                      dbType: 'static'
                    })
                    console.log('hhhh', promise1)
                    resultF.push(promise1)
                  }
                  else {
                    resultF.push('')
                  }

                })
                // cosnole.log('resultF', resultF);
                Promise.all(resultF).then((results) => {
                  console.log('results==', results)
                  const rtemp = []
                  results.forEach((resList, rindex) => {
                    if (resList) {
                      if (resList?.config?.url === '/api/code_module/list') {
                        temp[rindex].fieldsList = resList.data
                        temp[rindex].isAdd = false
                        rtemp.push(resList.data || [])
                      } else if(resList?.config?.url === '/api/api_doc/api_doc_type') {
                        temp[rindex].fieldsList = resList.data;
                        temp[rindex].isAdd = false;
                        rtemp.push(resList.data?.data || []);
                      } else if (resList?.config?.url === '/rule/query_page') {
                        temp[rindex].fieldsList = resList.data?.result?.items
                        temp[rindex].isAdd = false;
                        rtemp.push(resList.data?.result?.items || []);
                      } else {
                        temp[rindex].fieldsList = resList.data?.data?.list
                        temp[rindex].isAdd = false
                        rtemp.push(resList.data?.data?.list || [])
                      }
                    } else {
                      rtemp.push([])
                    }
                  })
                  console.log('结果rtemp', rtemp)
                  this.fieldsList = rtemp
                  console.log('fieldsList结果rtemp', this.fieldsList)
                  this.gridData = temp
                  console.log('表格数据', this.gridData)
                  this.loading = false
                  // 对 userData 和 productData 进行数据处理
                })
                this.$nextTick(() => {
                  console.log('nextTick 不执行吗？', this.agentSenceCode)
                  // if (
                  //   this.agentSenceCode === 'device_ops_assistant_scene' ||
                  //   this.agentSenceCode === 'device_ops_assistant_scene-v1' ||
                  //   this.agentSenceCode === 'visit_leader_cognition_scene' ||
                  //   this.agentSenceCode === 'intelligent_conversation_scene' ||
                  //   this.agentSenceCode === 'rule_generation_scene'
                  // ) {
                  //   this.handleAllTables()
                  //   this.handleAllSuanfa(res.data.result?.align_type?.tag_ids || [])
                  // }
                  this.handleAllTables()
                  this.handleAllSuanfa(res.data.result?.align_type?.tag_ids || [])
                })
              }

              this.dialogTableVisible = true
            } else {
              clearTimeout(this.timer)
              this.timer = null
              this.timer = setTimeout(() => {
                this.handleAbilityMapping()
              }, 3000)
            }
          } else {
            this.loading = false
            this.dataStatus = ''
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.loading = false
          this.dataStatus = ''
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
        .finally(() => { })
    },
    handleDone() {
      console.log(this.gridData, '222')
      const tempGridData = JSON.parse(JSON.stringify(this.gridData))
      // const tempGridData2 = JSON.parse(JSON.stringify(this.suanfaData));
      const tempData = tempGridData.map((item) => {
        const tempItem = item
        delete tempItem.fieldsList
        delete tempItem.isAdd
        return tempItem
      })
      // const tempData2 = tempGridData2.map(item => {
      //   const tempItem = item;
      //   delete tempItem.isAdd;
      //   return tempItem;
      // })
      const fields = tempData.reduce((acc, cur) => {
        Object.keys(cur).forEach((key) => {
          if (!acc.includes(key)) {
            acc.push(key)
          }
        })
        return acc
      }, [])

      // const fields2 = tempData2.reduce((acc, cur) => {
      //   Object.keys(cur).forEach((key) => {
      //     if (!acc.includes(key)) {
      //       acc.push(key);
      //     }
      //   });
      //   return acc;
      // }, []);

      const isValid = tempData.every((item) => {
        return fields.every((field) => {
          return item[field] !== ''
        })
      })
      // const isValid2 = tempData2.every((item) => {
      //   return fields2.every((field) => {
      //     return item[field] !== '';
      //   });
      // });
      console.log('tempData====', isValid, fields, tempData)
      const showData = this.gridData.map((item) => {
        return {
          ...item,
          isAdd: false
        }
      })
      // const showData2 = this.suanfaData.map(item => {
      //   return {
      //     ...item,
      //     isAdd: false,
      //   }
      // })
      if (!isValid) {
        this.$message.warning('请检查字段信息是否填写完整')
      } else {
        updateAbilityMapping({
          scheme_id: this.$route.query.id,
          config: {
            business_type:
              this.agentSenceCode === 'artificial_handle_scene' ? 'paramData' : 'dataTableAlgMapV1',
            header: this.dataThs,
            data: tempData
          },
          ability_status: 'finished'
        }).then((res) => {
          if (res.status === 200 && res.data.code === 200 && res.data.result) {
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
            this.redoFlag = true
            this.gridData = showData
            this.verifyBiaoDatas = [...showData]
            // this.suanfaData = showData2;
            // this.verifySuanfaData = [...showData2];
            this.saveLoading = false
            this.$emit('e-reExecTaskAfter', ALIGN_DATA_GENERATE)

            // this.$emit('updateStep', 3)
          } else {
            this.$message({
              type: 'success',
              message: '保存失败!'
            })
            this.saveLoading = false
          }
        })
      }
    },
    async queryDecision(status) {
      await GetDecision({ scheme_id: this.$route.query.id, scheme_status: status }).then((res) => {
        this.treeStatusLast = 2
        if (res.status === 200 && res.data.code === 200) {
          if (status === 'decision_ability' || status === 'rule') {
            this.codeData = res.data.result?.decision_making_content || ''
            this.codeProcess = res.data.result?.sub_content || ''
          } else {
            this.treeData = res.data.result?.decision_making_content || ''
          }
          if (status === 'mind_map' && this.treeData) {
            // 思维图
            this.thinkingHandle()
          } else if (status === 'mind_map' && !this.treeData) {
            const nodeMarkmap = document.getElementById('markmap')
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = ''
            }
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    // 数据表
    async handleAllTables() {
      getTablesList({ dbType: 'static' }).then((res) => {
        if (res.status === 200) {
          const temp = res.data.data?.list || []
          console.log('多模态数据对齐配置的表', this.initChooseData)
          if (
            this.agentSenceCode === 'device_ops_assistant_scene' ||
            this.agentSenceCode === 'visit_leader_cognition_scene' ||
            this.agentSenceCode === 'intelligent_conversation_scene'
          ) {
            if (this.initChooseData?.table) {
              this.tablesList = temp.filter(
                (item) => this.initChooseData?.table.indexOf(item.table) > -1
              )
            } else {
              this.tablesList = []
            }
          } else {
            this.tablesList = temp
          }
          console.log('表数据', this.tablesList)
        } else {
          this.$message({
            type: 'success',
            message: '接口错误!'
          })
        }
      })
    },
    // 数据表
    async handleAllSuanfa(tags) {
      queryApiMixFuncList({
        tag_ids: tags
      }).then((res) => {
        console.log('算法你别吓我', res.data)
        if (res.data) {
          console.log('算法 00', res.data)
          const abTemp = {}
          const temp =
            res.data?.map((item) => {
              abTemp[item.code_type] = this.apiNameMap[item.code_type] || ''
              return {
                code_name: item.code_name,
                code_type: item.code_type,
                code_func_name: item.code_func_name,
                code_type_name: this.apiNameMap[item.code_type] || ''
              }
            }) || []
          this.useAPIList = Object.keys(abTemp).map((item) => {
            return { name: item, value: abTemp[item] }
          })
          this.suanfaList = temp
          console.log('算法列表数据', this.suanfaList, this.useAPIList)
        } else {
          this.$message({
            type: 'success',
            message: '接口错误!'
          })
        }
      })
    },
    async changeAlg(name, index) {
      const tempData = this.gridData.map((item) => {
        return item
      })
      const rowIndex = (this.pageNo - 1) * this.pageSize + index
      const filters = this.suanfaList.filter((item) => item.code_func_name === name)
      if (filters.length) {
        // console.log(JSON.parse(filters[0].algorithm_schema));
        tempData[rowIndex].e_mapping_desc = filters[0].public_description
        // tempData[rowIndex].d_algorithm_url = filters[0].algorithm_url
        this.gridData = tempData
      }
    },
    // 切换匹配类型，库表还是api
    changeCType(val, index) {
      console.log('匹配切换类型', val, index)
      const tempData = this.gridData.map((item) => {
        return item
      })
      const rowIndex = (this.pageNo - 1) * this.pageSize + index
      this.fieldsList[rowIndex] = []
      console.log('fieldsList', this.fieldsList)
      tempData[rowIndex].fieldsList = []
      tempData[rowIndex].c_source = ''
      tempData[rowIndex].d_mapping_field = ''
      tempData[rowIndex].e_mapping_desc = ''
      this.gridData = tempData
      console.log('表数据', this.gridData)
    },
    // 表中对应的列
    async handleFiedsByTable(tableName, index) {
      // this.changeloading = true;
      const tempData = this.gridData.map((item) => {
        return item
      })
      const rowIndex = (this.pageNo - 1) * this.pageSize + index
      await queryFieldsByTable({ tableName, dbType: 'static' }).then((res) => {
        if (res.status === 200 && res.data.success) {
          console.log('列数据', res.data.data)
          const temp = res.data.data?.list || []
          this.fieldsList[rowIndex] = temp
          console.log('fieldsList', this.fieldsList)
          tempData[rowIndex].fieldsList = temp
          tempData[rowIndex].d_mapping_field = temp[0].COLUMN_NAME
          const filter = this.fieldsList[rowIndex].filter(
            (item) => item.COLUMN_NAME === temp[0].COLUMN_NAME
          )
          tempData[rowIndex].e_mapping_desc = filter[0].COLUMN_COMMENT
          // tempData[rowIndex].f_field_type = filter[0].COLUMN_TYPE
          this.gridData = tempData
          this.changeloading = false
        } else {
          this.changeloading = false
          this.$message({
            type: 'success',
            message: '接口错误!'
          })
        }
      })
    },
    // api中对应的列
    async handleFiedsByAPI(name, index) {
      // this.changeloading = true;
      const tempData = this.gridData.map((item) => {
        return item
      })
      const rowIndex = (this.pageNo - 1) * this.pageSize + index
      await queryApiMixFuncList({
        code_type: name,
        tag_ids: this.initChooseData.tag_ids || []
      }).then((res) => {
        if (res.status === 200 && res.data) {
          const temp = res.data || []
          this.fieldsList[rowIndex] = temp
          console.log('fieldsList', this.fieldsList)
          tempData[rowIndex].fieldsList = temp
          // tempData[rowIndex].d_mapping_field = temp[0].code_name
          // const filter =  this.fieldsList[rowIndex].filter(item => item.code_fun_name === temp[0].code_fun_name);
          // tempData[rowIndex].e_mapping_desc = filter[0].public_description
          // tempData[rowIndex].f_field_type = filter[0].COLUMN_TYPE
          if (temp.length) {
            tempData[rowIndex].d_mapping_field = temp[0].code_func_name
            const filter = this.fieldsList[rowIndex].filter(
              (item) => item.code_name === temp[0].code_name
            )
            tempData[rowIndex].e_mapping_desc = filter[0].public_description
          } else {
            tempData[rowIndex].d_mapping_field = ''
            tempData[rowIndex].e_mapping_desc = ''
          }
          this.gridData = tempData
          this.changeloading = false
        } else {
          this.changeloading = false
        }
      })
    },
    handleFiledApiChange(val, index) {
      const temp = this.fieldsList[index]
      const filter = temp.filter((item) => item.code_func_name === val)
      console.log('更改描述信息', filter, index)
      if (filter.length) {
        const temp = this.gridData.map((item, gindex) => {
          if (gindex === index) {
            return { ...item, e_mapping_desc: filter[0].public_description }
          } else {
            return item
          }
        })
        console.log('temp--', temp)
        this.gridData = temp
      }
    },
    handleFiledRuleChange(val, index) {
      const temp = this.fieldsList[index];
      const filter = temp.filter(item => item.name === val);
      console.log('更改描述信息', this.fieldsList, filter, index);
      if (filter.length) {
        const temp = this.gridData.map((item, gindex) => {
          if (gindex === index) {
            // f_field_type: filter[0].COLUMN_TYPE
            return { ...item, e_mapping_desc: filter[0].description }
          } else {
            return item;
          }
        })
        console.log('temp--', temp);
        this.gridData = temp;
      }
    },
    // 切换字段关联描述信息
    handleFiledChange(val, index) {
      const temp = this.fieldsList[index]
      const filter = temp.filter((item) => item.COLUMN_NAME === val)
      console.log('更改描述信息', filter, index)
      if (filter.length) {
        const temp = this.gridData.map((item, gindex) => {
          if (gindex === index) {
            // f_field_type: filter[0].COLUMN_TYPE
            return { ...item, e_mapping_desc: filter[0].COLUMN_COMMENT }
          } else {
            return item
          }
        })
        console.log('temp--', temp)
        this.gridData = temp
      }
    },
    // 查看API详情
    handleLink(row, list) {
      console.log('跳转接口详情', row, list)
      const filter = row.fieldsList.filter(
        (item) =>
          item.code_func_name === row.d_mapping_field || item.code_name === row.d_mapping_field
      )
      if (filter.length) {
        if (filter[0].code_type == 'llm') {
          window.open(
            `${process.env.VUE_APP_AGENT_URL}/apiDocumentDetails/${filter[0].code_type}?id=${filter[0].sub_id}`,
            '_blank'
          )
        } else if (filter[0].code_type === 'func_doc') {
          window.open(
            `${process.env.VUE_APP_AGENT_URL}/funDetails?id=${filter[0].sub_id}`,
            '_blank'
          )
        } else {
          window.open(
            `${process.env.VUE_APP_AGENT_URL}/apiDocumentDetails?id=${filter[0].sub_id}`,
            '_blank'
          )
        }
      }
      //
      // https://agent.fat.ennew.com/apiDocumentDetails?id=1e1b802e-c4d9-4f0c-a911-20aed89d3c2c
    },
    // 删除行
    handleRemove(rowIndex) {
      rowIndex = (this.pageNo - 1) * this.pageSize + rowIndex
      const temp = [...this.gridData]
      temp.splice(rowIndex, 1)
      const res = this.gridData.splice(rowIndex, 1)
      console.log('显示页面条数', (this.pageNo - 1) * this.pageSize, this.pageNo, temp.length)
      const res2 = this.fieldsList.splice(rowIndex, 1)
      console.log('删除行', res, this.gridData, res2, this.fieldsList)
      if ((this.pageNo - 1) * this.pageSize === temp.length) {
        this.pageNo = this.pageNo - 1
      }
    },
    // 增加行
    handleAddRow(rowIndex) {
      rowIndex = (this.pageNo - 1) * this.pageSize + rowIndex
      console.log('行信息', rowIndex, rowIndex % 10)
      // TODO
      const addData = { isAdd: true }
      if (this.agentSenceCode === 'artificial_handle_scene') {
        Object.keys(this.dataThs).forEach((item) => {
          if (item === 'b_type') {
            addData[item] = '人工'
          } else if (item === 'c_source') {
            addData[item] = '人工输入'
          } else {
            addData[item] = ''
          }
        })
        this.displayData.splice(rowIndex, 0, addData)
        this.gridData.splice(rowIndex, 0, addData)
        this.fieldsList.splice(rowIndex, 0, [])
        console.log('增加行', this.gridData)
      } else {
        Object.keys(this.dataThs).forEach((item) => {
          addData[item] = ''
        })
        this.fieldsList.splice(rowIndex, 0, [])
        this.displayData.splice(rowIndex, 0, addData)
        this.gridData.splice(rowIndex, 0, addData)
        console.log('增加行', this.gridData, this.fieldsList)
      }
      if (rowIndex !== 0 && rowIndex % 10 === 0) {
        this.pageNo = this.pageNo + 1
      }
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    getWsID() {
      let workspaceId = ''
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?')
          const params = Object.fromEntries(new URLSearchParams(query))
          workspaceId = params.workspaceId
        } catch (error) {
          console.log('error', error)
        }
      }
      return workspaceId
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = '50%'
        this.$nextTick(() => {
          console.log('退出高度---tabelRef', this.samllHeight)
          this.tableHeight = this.samllHeight
        })
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
        this.$nextTick(() => {
          console.log('高度---tabelRef', this.$refs.tabelRef, this.$refs.tabelRef.scrollHeight)
          this.tableHeight = this.$refs.tabelRef.scrollHeight - 40
        })
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = '50%'
        this.rightWidth = '100%'
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result?.text
        }
      })
    },
    regenerate() {
      this.showChooseTableVisable = true
      // this.codeData = ''
      // this.codeProcess = '';
      // this.taskLoading = true;
      // this.redoFlag = true;
      // this.$emit('updateCodeGenerate','decision_ability')
      this.dataDuiqiZhuGe('多模态数据对齐')
    },
    async handleInit(id) {
      this.loading = true
      console.log('初始化')
      const res = await queryAbilityMapping({ scheme_id: this.$route.query.id })
      this.dataStatus = res.data.result.ability_status
      const configData = res.data.result?.config || {}
      console.log('configData 02', this.configData)
      if (res.data.result.ability_status !== 'generating') {
        this.loading = false
        const dataThs = configData?.header || {}
        this.dataThDatas = Object.keys(dataThs).map((item) => {
          return { name: dataThs[item], field: item }
        })
        console.log('dataThDatas 00', JSON.stringify(this.dataThDatas, null, 2), this.dataThDatas)
        this.tableData =
          configData?.data?.map((item) => {
            return {
              ...item
            }
          }) || []
      } else {
        console.log('生成中,轮询调用此接口进行查询')
        this.loading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.task-card-content {
  padding: 16px 20px;
  min-height: 200px;
}

:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}

::v-deep .el-table::before {
  background-color: transparent;
}

::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}

::v-deep .el-table th.el-table__cell:not(.no-bor)>.cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}

:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}

// 下面都是抄的
.optScroll {
  position: relative;
  height: 620px;
  max-height: 620px;
  // height: calc(100vh - 300px);
  // max-height: calc(100vh - 300px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  display: flex;
  flex-direction: column;

  ::v-deep .el-textarea {
    margin-bottom: 10px;
  }

  .btn {
    position: absolute;
    bottom: 0;
    right: 20px;
  }

  .table-box {
    height: 100%;
    display: flex;
    flex-direction: column;

    .duiqi-box {
      height: calc(100% - 0px);
      overflow: hidden;

      // padding-bottom: 30px;
      .emptyData {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        img {
          width: 50%;
          max-width: 200px;
          height: auto;
        }
      }

      .title {
        margin-bottom: 16px;
        color: #323233;
      }

      .transition-box {
        table {
          height: 100%;
        }
      }

      .page {
        height: 30px;
        justify-content: flex-end;
        display: flex;
        margin-top: 10px;

        ::v-deep .el-pagination:not(.new-paper) button {
          width: 30px;
        }
      }

      .el-pagination {
        height: 20%;
      }
    }
  }
}
</style>
