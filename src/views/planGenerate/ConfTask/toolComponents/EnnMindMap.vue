<template>
  <div class="enn-mindMap" v-loading="isLoading" element-loading-text="生成中..."
    element-loading-spinner="el-icon-loading">
    <!-- <div v-show="!dagangFlag" id="detail-content" class="optContentBox h-full" @mouseenter="fangda" @mouseup="fangda2">
      <MyEditor id="MyEditor" ref="MyEditor" :md-content="executeResult" :is-edit="isEdit"
        @updateContent="handleUpdateContent" />
    </div> -->
    <div style="width: 100%; height: 100%" @mouseenter="fangda">
      <svg v-if="executeResult" id="Ennmarkmap" class="enn-mark-map"></svg>
    </div>
  </div>
</template>

<script>
import { Markmap } from 'markmap-view'
import { Transformer } from 'markmap-lib'

export default {
  name: "EnnMindMap",
  props: {
    executeResult: {
      type: String,
      required: true,
    },
    isLoading:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dagangFlag:false
    }
  },
  watch:{
    isLoading: {
      handler(Newval,oldVal) {
        if (oldVal === true && Newval === false){
          const nodeMarkmap = document.getElementById('Ennmarkmap')
          if (nodeMarkmap) {
            nodeMarkmap.innerHTML = ''
          }
          const transformer = new Transformer()
          const { root } = transformer.transform(this.executeResult)
          this.$nextTick(() => {
            Markmap.create('#Ennmarkmap', null, root)
          })
        }
      },
      immediate: true,
    },
    executeResult: {
      handler(Newval,oldVal) {
        if (Newval != oldVal){
          const nodeMarkmap = document.getElementById('Ennmarkmap')
          if (nodeMarkmap) {
            nodeMarkmap.innerHTML = ''
          }
          const transformer = new Transformer()
          const { root } = transformer.transform(this.executeResult)
          this.$nextTick(() => {
            Markmap.create('#Ennmarkmap', null, root)
          })
        }
      },
      immediate: true,
      deep:true
    },
  },
  async mounted() {
    // const nodeMarkmap = document.getElementById('Ennmarkmap')
    // if (nodeMarkmap) {
    //   nodeMarkmap.innerHTML = ''
    // }
    // const transformer = new Transformer()
    // const { root } = transformer.transform(this.executeResult)
    // this.$nextTick(() => {
    //   Markmap.create('#Ennmarkmap', null, root)
    // })
    // changeDagang()
  },
  methods: {
    // changeDagang() {
    //   this.dagangFlag = !this.dagangFlag
    //   if (!this.dagangFlag) {
    //     const nodeMarkmap = document.getElementById('markmap')
    //     if (nodeMarkmap) {
    //       nodeMarkmap.innerHTML = ''
    //     }
    //   } else {
    //     const transformer = new Transformer()
    //     const { root } = transformer.transform(this.executeResult)
    //     this.$nextTick(() => {
    //       Markmap.create('#markmap', null, root)
    //     })
    //   }
    // },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.enn-mindMap{
  width: 100%;
  height: 100%;
}
.enn-mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}

:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
