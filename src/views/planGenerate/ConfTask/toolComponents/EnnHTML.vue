<template>
  <div class="enn-iframe" v-loading="isLoading" element-loading-text="生成中..." element-loading-spinner="el-icon-loading">
    <iframe
      id="iframeNest"
      :srcdoc="executeResult"
      allow="clipboard-write"
      frameborder="0"
      class="iframe-nest"
    />
  </div>
</template>
<script>
export default {
  name: "EnnHTML",
  components: {
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
    isLoading:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false

    }
  },
  watch: {
    executeResult (val) {
      console.log('watch------------',val)
    }
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.enn-iframe, .iframe-nest {
  width: 100%;
  height: 100%;
}
</style>
