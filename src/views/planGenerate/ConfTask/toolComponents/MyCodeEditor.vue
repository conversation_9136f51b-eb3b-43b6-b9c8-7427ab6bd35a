<template>
 <div
   style=" height: 100%"
   v-loading="!mdContentText && dataAlignStatus === '0' && elBox === 'math_model'"
   element-loading-text="生成中..."
   element-loading-spinner="el-icon-loading"
   class='parent'
 >
   <div v-if="isEdit" style="height: 100%">
     <v-md-editor
       id="editorFin"
       ref="editorFin"
       v-model="mdContentText"
       mode="editable"
       left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link  code"
       right-toolbar="preview"
       height="100%"
       @input="handleInput"
       @focus="handleFocus"
     ></v-md-editor>
   </div>
   <div
     v-if="!isEdit"
     style="height: 100%; overflow: scroll"
     class="editor-show"
   >
     <pre ref="mathPre" v-if="mathPre" class="pre">{{ mdContentText }}</pre>
     <v-md-editor
       v-else
       id="editor555"
       ref="editor555"
       v-model="mdContentText"
       mode="preview"
       right-toolbar="preview"
       height="100%"
     ></v-md-editor>
   </div>
   <!-- <div v-if="!isEdit && errorFlag" style=" height: 100%">
     <div
       style="
         display: flex;
         flex-direction: column;
         align-items: center;
         justify-content: center;
         height: 100%;
         width: 100%;
       "
     >
       <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px; height: auto" />
       <div
         style="
           display: flex;
           flex-direction: row;
           align-items: center;
           justify-content: center;
           margin-top: 16px;
         "
       >
         转换失败，请修正后重试
       </div>
     </div>
   </div> -->
 </div>
</template>

<script>
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import 'katex/dist/katex.min.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn'
import createLineNumbertPlugin from '@kangc/v-md-editor/lib/plugins/line-number/index'
import createCopyCodePlugin from '@kangc/v-md-editor/lib/plugins/copy-code/index'
import createKaTeXPlugin from '@kangc/v-md-editor/lib/plugins/katex/cdn';
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-python'
import hljs from 'highlight.js'
import _ from 'lodash';
import { set } from 'nprogress'

import { handleThrottleLog } from '@/utils/ThrottledLogger.js';
VMdEditor.use(vuepressTheme, {
 Prism,
 extend(md) {
   console.log('ddd', md)
   // md为 markdown-it 实例，可以在此处进行修改配置,并使用 plugin 进行语法扩展
   // md.set(option).use(plugin);
 }
})

VMdEditor.use(createMermaidPlugin())
export default {
 components: {
   VMdEditor
 },
 props: {
   mdContent: {
     type: String,
     default() {
       return ''
     }
   },
   isEdit: {
     type: Boolean,
     default: false
   },
   id: {
     type: String,
     default() {
       return 'test11'
     }
   },
   stopScroll: {
     type: Boolean,
     default: false
   },
   dataAlignStatus: {
     type: String,
     default() {
       return '0'
     }
   },
   elBox: {
     type: String,
     default() {
       return ''
     }
   }
 },
 data() {
   return {
     katexPluginLoaded: false, // 新增标志位
     mathPre: false,
     mdContentText: '',
     showFlag: false,
     errorFlag: false
   }
 },
 beforeCreate() {
   VMdEditor.use(createKaTeXPlugin());
 },
 watch: {
   mdContent: {
     handler(val) {
       requestIdleCallback(()=>{
         handleThrottleLog('window---------',this)
         this.mdContentText = val
         // this.$refs?.editor555?.$el
         //         ?.querySelector('.vuepress-markdown-body')
         //         ?.scrollIntoView({ block: 'end', behavior: 'smooth' })
       })
       this.showFlag = this.isEdit
       this.errorFlag = false
       if (!this.isEdit && this.elBox !== 'math_model') {
         this.$nextTick(function () {
           if (
             this.$refs.editor555.$el &&
             this.$refs.editor555.$el.querySelector('.vuepress-markdown-body')
           ) {
             if (!this.stopScroll) {
               // 如果点击展示详情，则不需要滚动
               this.$refs.editor555.$el
                 .querySelector('.vuepress-markdown-body')
                 .scrollIntoView({ block: 'end', behavior: 'smooth' })
             }
           }
         })
       }
       // 针对生成数学模型自动滚动
       if (!this.isEdit && this.elBox === 'math_model' && this.mathPre) {
         this.$nextTick(function () {
           this.$refs.mathPre.scrollTop = this.$refs.mathPre.scrollHeight
         })
       }
       // window.mermaid.parseError = (err) => {
       //   console.log(
       //     '转换错误jieguo---222',
       //     err,
       //     String(err).indexOf('Parse error'),
       //     '---',
       //     typeof err,
       //     err.str
       //   )
       //   if (err.str && String(err.str).indexOf('Parse error') > -1) {
       //     this.errorFlag = true
       //   } else {
       //     this.errorFlag = false
       //   }
       // }
       handleThrottleLog('mdContent 1111', val)
     },
     immediate: true

   },
   isEdit: {
     handler(val) {
       this.showFlag = val
       if (val) {
         this.$nextTick(() => {
           if (this.$refs.editorFin && this.$refs.editorFin.currentMode !== 'edit') {
             this.$refs.editorFin.currentMode = 'edit'
           }
         })
       }
     },
     immediate: true
   },
   dataAlignStatus: {
     handler(val) {
       if (val) {
         if (val === '0' && this.elBox === 'math_model') {
           this.mathPre = true
           this.removeKaTeXPlugin()
         } else {
           this.mathPre = false
           this.loadKaTeXPlugin()
         }
       }
     },
     immediate: true
   }
 },
 created() {
   this.handleInput = _.debounce(this.handleInput, 300);
   console.log('mdContentText 00002', this.mdContentText, this.isEdit)
   if (
     this.mdContent.indexOf('mermaid') > -1 ||
     this.mdContent.indexOf('flow') > -1 ||
     this.mdContent.indexOf('graph') > -1 ||
     this.mdContent.indexOf('flowchart') > -1
   ) {
     VMdEditor.use(createMermaidPlugin())
   }
   this.removeKaTeXPlugin()
 },
 mounted() {
  console.log('faskdjpfaspfjsap',window)
  this.setupMermaidErrorHandler()
 },
 beforeDestroy() {
   // 清理错误处理函数
   if (window.mermaid) {
     window.mermaid.parseError = null
   }
 },
 // updated() {
 //   console.log('mdContentText 0000', this.mdContentText)

 //   // this.renderContent()
 // },
 methods: {
  setupMermaidErrorHandler() {
     // 确保mermaid已加载
     if (typeof window.mermaid !== 'undefined') {
       // 使用防抖处理错误回调
       window.mermaid.parseError = _.debounce((err) => {
         console.log('Mermaid解析错误:', err)
         
         // 只在错误类型为解析错误时设置标志
         if (err.str && err.str.includes('Parse error')) {
           // 检查错误是否来自当前组件内容
           if (this.mdContentText.includes('mermaid')) {
             this.errorFlag = true
           }
         } else {
           this.errorFlag = false
         }
       }, 300) // 防抖300ms
     } else {
       console.warn('Mermaid未加载，无法设置错误处理器')
     }
   },
   renderContent() {
     // 如果需要强制触发重新渲染，可以使用 $forceUpdate()
     this.$forceUpdate()
   },
 //   handleInput: _.debounce((val) => {
 //   console.log('更新的内容', val, this);
 //   // this.$emit('updateContent', val)
 // }, 300),
   handleInput(val) {
     this.errorFlag = false  // 用户编辑时重置错误状态
     this.$emit('updateContent', val)
     // 如果内容包含mermaid，重新设置错误处理器
     if (val.includes('mermaid')) {
       this.$nextTick(this.setupMermaidErrorHandler)
     }
   },
   handleFocus() {
     console.log('jujiao的内容')
   },
   loadKaTeXPlugin() {
     // // 动态引入 KaTeX 插件
     // if(!this.katexPluginLoaded) {
     //   import('@kangc/v-md-editor/lib/plugins/katex/cdn').then((module) => {
     //     const createKaTeXPlugin = module.default
     //     console.log('res-----------',module)
     //     VMdEditor.use(createKaTeXPlugin())
     //     this.katexPluginLoaded = true;
     //     // 使用 requestAnimationFrame 分帧渲染
     //     requestAnimationFrame(() => {
     //       this.renderMath()
     //     })
     //   })
     // }
   },
   renderMath() {
     // 在这里进行数学公式渲染
     this.$nextTick(() => {
       this.removeKaTeXPlugin()
     })
   },
   removeKaTeXPlugin() {
     if(this.katexPluginLoaded) {
       VMdEditor.use({ name: 'katex', enhance: null })
       this.katexPluginLoaded = false;
     }
   },
   getMdContentText(){
     //获取数据
     return this.mdContentText
   }
 }
}
</script>
<style lang="less" scoped>
/deep/ .vuepress-markdown-body {
 min-height: initial !important;
 height: initial !important;
}
/deep/ .pre-wrapper {
 min-height: initial !important;
 height: initial !important;
}
pre {
 height: 100%;
 overflow: auto;
 word-wrap: break-word;
 overflow-wrap: break-word;
 word-break: break-all;
}
:deep(.el-loading-spinner) {
 width: 130px !important;
 background: none !important;
}
:deep(.vuepress-markdown-body){
 width:inherit;
}
:deep(.vuepress-markdown-body p) {
   word-wrap: break-word;
   white-space: normal;
   width:inherit;
   overflow-x: auto;
 }
:deep(.katex-display) {
 padding:0 20px 10px 0;
 width: inherit;
 overflow-y: hidden;
}
// .parent * {
//   width: inherit; /* 继承直接父元素的宽度 */
//   box-sizing: border-box; /* 确保padding和border在元素的width内计算 */
 
//   /* 处理溢出内容 */
//   word-wrap: break-word; /* 允许长单词或URL地址换行到下一行 */
//   overflow-wrap: break-word; /* 替代word-wrap，适用于更广泛的浏览器支持 */
//   white-space: normal; /* 允许空白符按照普通文本处理，允许自动换行 */
// }
:deep(.parent) {
 width: 100%; /* 继承直接父元素的宽度 */
 box-sizing: border-box; /* 确保padding和border在元素的width内计算 */
 
 /* 处理溢出内容 */
 word-wrap: break-word; /* 允许长单词或URL地址换行到下一行 */
 overflow-wrap: break-word; /* 替代word-wrap，适用于更广泛的浏览器支持 */
 white-space: normal; /* 允许空白符按照普通文本处理，允许自动换行 */
}
:deep(.v-md-editor__main){
 flex: none;
}
:deep(.v-md-editor__right-area){
 flex: none;
 width: inherit; 
}
</style>
