<template>
  <div class="enn-json">
    <div v-loading="loading" class="task-card-content" element-loading-text="代码解析中..."
      element-loading-spinner="el-icon-loading">
      <div class="transition-box" style="overflow-y: hidden; height: 100%">
        <div v-if="executeResultData !== ''" id="EditorId" ref="treeProcess2" style="overflow-y: auto; height: 100%">
          <MonacoEditor id="code_analysis" class="editor" :value="executeResultData" language="json"
            :scroll-beyond-last-line="true" :original="codeDataOri" theme="vs-dark" :diff-editor="false"
            :options="options" @change="change" />
          <!-- <vue-markdown v-highlight id="treeProcessMd" :source="treeProcessData" class="markdown-body"></vue-markdown> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MyEditor from '../../mdEditor.vue'
import MonacoEditor from '@/components/MonacoEditor'
export default {
  name: "EnnJson",
  components: {
    MyEditor,
    MonacoEditor
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      executeResultData: '',
      isEdit: false,
      loading: false,
      codeDataOri: '',
      options: {
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      }

    }
  },
  watch: {
    executeResult: {
      handler(val) {
        if (val) {
          this.executeResultData = val
          this.codeDataOri = val
          this.scrollFn()
        }
      },
      immediate: true
    },
  },
  methods: {
     JSONEnn() {
      try {
        // 解析并美化 localCode
        const obj = JSON.parse(this.executeResultData);
        this.executeResultData = JSON.stringify(obj, null, 4); // 2 表示缩进2个空格
      } catch (e) {
        this.$message({
          type: 'error',
          message: 'JSON格式错误，无法美化！'
        });
      }
    }, 
    change(val) {
      this.executeResultData = val
    },
    scrollFn() {
      const temp = document.getElementById('EditorId')
      if (temp) {
        temp.scrollIntoView({ block: 'end', inline: 'nearest' })
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.task-card-content {
  height: 100%;
}

.editor {
  height: 100%;
  width: 100%;
  margin-left: 1px;
}

:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
.enn-json{
  width: 100%;
  height: 100%;
}
</style>
