<template>
  <div class="EnnMarkdownWithMath">
    <!-- 编辑器视图 -->
    <div v-show="!dagangFlag" id="detail-content" class="optContentBox h-full" @mouseenter="fangda" @mouseup="fangda2">
      <MyEditor id="MyEditor" ref="MyEditor" :md-content="executeResult" :is-edit="isEdit"
        @updateContent="handleUpdateContent" />
    </div>

  </div>
</template>

<script>
import MyEditor from '../../mdEditor.vue'
export default {
  name: "EnnMarkdownWithMath",
  components: {
    MyEditor,
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isEdit: false,
    }
  },
  methods: {
    handleUpdateContent(val) {
      // this.detailContent.text = val
      this.$emit('e-update-executeResult', val)
      console.log('e-update-executeResult', val)
    },
  },
};
</script>

<style lang="scss" scoped>
.optContentBox {
  //height: calc(100% - 340px);
  // max-height: calc(100vh - 340px);
  // max-height: 100%;
  // height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}

.h-full {
  max-height: 100%;
  height: 100%;
}
</style>
