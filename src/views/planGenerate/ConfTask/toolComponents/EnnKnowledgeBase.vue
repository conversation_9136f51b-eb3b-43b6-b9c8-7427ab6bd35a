<template>
  <div class="enn-knowledgebase">
    {{ executeResult }}
  </div>
</template>
<script>
export default {
  name: "EnnKnowledgeBase",
  components: {
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
  },
  data() {
    return {


    }
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.enn-knowledgebase {
  width:100%;
  height:100%;
}
</style>
