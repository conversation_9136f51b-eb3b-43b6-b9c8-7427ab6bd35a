<template>
 <div class="main-area">
  <el-aside v-show="!isClick" min-width="170px" class="sidebar" :class="!isRight ? 'full' : ''">
   <file-tree ref="tree" v-bind="$attrs" @change-activeTab="changeActiveTab" @handle-isEdit="handleIsEdit"
    :rootKeys="rootKeys" @current-click="currentClick" @delete-file="deleteFile" v-on="$listeners"></file-tree>
  </el-aside>
  <div class="editor-container">
   <div v-if="activeTab">
    <div class="jiantou" v-if="!isRight" @click="goBack">
     <i class="el-icon-arrow-left"></i>
     <span>返回</span>
     <div class="line"></div>
     <span class="tab-title">{{ activeTab }}</span>
    </div>
    <el-tabs v-if="isRight" type="card" class="vscode-tabs">
     <el-tab-pane>
      <!-- 标签内容插槽（可选自定义关闭按钮） -->
      <template #label>
       <span class="tab-title">{{ activeTab }}</span>
      </template>
     </el-tab-pane>
    </el-tabs>
   </div>
   <div class="contain" v-loading="codeLoading" element-loading-text="生成中..." element-loading-spinner="el-icon-loading">
    <MonacoEditor
     v-if="!imageUrl && !getComponent(ability.display_component) && monacoText != '该文件不可预览' && !officeServe"
     style="min-height: 100px;" id="optContentBoxMd" class="editor" :value="localCode" :language="ability.language"
     :scroll-beyond-last-line="true" :original="codeDataOri" :diff-editor="false" :options="options"
     @editorDidMount="editorDidMount" @change="change" />
    <img v-if="isImage && imageUrl && !officeServe" :src="imageUrl"
     style="width: 80%; height: auto; object-fit: contain; padding: 0 16px;" />
    <component style="padding: 0 0 0 16px;"
     v-if="optHeader2Tab === ability.name && getComponent(ability.display_component) && !dagangFlagNew && !officeServe"
     v-bind="getComponentProps(localComponent)" :is="getComponent(localComponent.display_component)"
     v-on="getComponentEmitHandlers(localComponent)" :ref="`tab-${ability.name}`" :key="ability.name"
     :item="localComponent" :isCodeMarkdown="true" :isEdit="isEdit" />
    <span v-if="!imageUrl && !getComponent(ability.display_component) && monacoText == '该文件不可预览' && !officeServe"
     style="color: #999; padding: 0 16px;">
     该文件不可预览
    </span>
    <div class="office-serve"
     :class="(currentNode.objectType != 'xlsx' && currentNode.objectType != 'xls') ? 'minWd' : ''" v-if="officeServe">
     <iframe :src="officeUrl" width="100%" height="100%" frameborder="0" />
    </div>
    <div v-if="dagangFlagNew" style="height: 100%;width: 100%;" @mouseenter="fangda">
     <svg id="markmapenn" class="mark-map" style="height: 100%;width: 100%;"></svg>
    </div>
   </div>
   <div class="edit-footer"
    v-if="!isImage && !imageUrl && monacoText !== '该文件不可预览' && currentNode.objectType !== 'pdf' && !officeServe">
    <div class="enn-markdown-div">
     <el-tooltip v-if="!dagangFlagNew &&!isEdit && currentNode.objectType == 'md'" class="item" effect="dark"
      content="脑图" placement="top">
      <el-button type="info" size="mini" @click="changeDagangNew">
       <img src="@/assets/images/planGenerater/ic_naotu.png" />
      </el-button>
     </el-tooltip>
     <el-tooltip v-if="dagangFlagNew && !isEdit" class="item" effect="dark" content="大纲" placement="top">
      <el-button type="info" size="mini" @click="changeDagangNew"><img
        src="@/assets/images/planGenerater/ic_dagang.png" /></el-button>
     </el-tooltip>
     <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
      <el-button type="info" size="mini" @click="handleEdit"><img
        src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
      <el-button type="info" size="mini" @click="newHanderSave"><img
        src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
      <el-button type="info" size="mini" @click="closeCaiNaFun"><img
        src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="复制" placement="top">
      <el-button type="info" size="mini" @click="copyTextNewEnn()"><img
        src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
     </el-tooltip>
    <el-tooltip class="item" effect="dark" content="JSON美化" placement="top" v-if="getFileExtension(optHeader2Tab) == 'json'">
      <el-button type="info" size="mini" @click="JSONEnn()"><img
        src="@/assets/images/planGenerater/JSON.png" /></el-button>
     </el-tooltip>
     <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
      <el-button type="info" size="mini" @click="showSikao()"><img
        src="@/assets/images/planGenerater/ic_guocheng.png" /></el-button>
     </el-tooltip> -->
    </div>
   </div>
  </div>
 </div>
</template>
<script>
import {
 updateCom,
 update_ability_result,
 abilityDel,
 update_file_content
} from '@/api/planGenerateApi.js'
import fileTree from '../components/fileTree.vue'
import MonacoEditor from '@/components/MonacoEditor'
import { mapState, mapMutations } from 'vuex'
import { ComponentRegistry, ComponentAliasMap } from './ComponentRegistry';
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
const languageMap = {
 'js': 'javascript',
 'py': 'python',
 'java': 'java',
 'html': 'html',
 'css': 'css',
 'json': 'json',
 'md': 'markdown',
};
// ['json', 'txt', 'md', 'py', 'sh', 'ipynb', 'png', 'jpg', 'jpeg', 'csv', 'yaml']
const result = Object.entries(languageMap).reduce((acc, [key, lang]) => {
 // 定义特殊键的 display_component 映射
 const specialComponents = {
  html: 'EnnHTML',
  md: 'EnnMarkdown',
  css: 'EnnCSS',
  json: 'EnnJSON'
 };

 // 设置 display_component
 const display_component = specialComponents[key]
  || `Enn${lang.charAt(0).toUpperCase()}${lang.slice(1)}`;

 acc[key] = {
  language: lang,
  executeResult: '',
  isLoading: false,
  display_component: display_component
 };
 return acc;
}, {});
export default {
 name: 'macoEdit',
 props: {
  isRight: {
   type: Boolean,
   default: true
  },
  officeUrl: {
   type: String,
   default: ''
  },
  currentTab: {
   type: Object
  },
  codeLoading: {
   type: Boolean,
   default: false
  },
  isImage: {
   type: Boolean,
   default: false
  },
  imageUrl: {
   type: String,
   default: ''
  },
  flowData: {
   type: Array,
   default: () => []
  },
  rootKeys: {
   type: String,
  }
  // treeData: {
  //  type: Array,
  //  default: () => []
  // }
 },
 components: {
  fileTree,
  MonacoEditor
 },
 data() {
  return {
   isClick: false,
   lastSavedFile: null,     // 上一次保存的文件
   currentEditedFile: null, // 当前编辑的文件
   fileSwitchHistory: [],    // 文件切换历史
   editingFilePath: null,
   editor: null,
   originalContent: '',       // 存储原始内容（用于比较）
   originalMarkdown: '', // 存储原始Markdown内容
   dagangFlagNew: false,
   currentNode: '',
   localComponent: {},
   localCode: '',
   isEdit: false,
   activeTab: '',
   optHeader2Tab: '',
   componentRefs: new Map(),
   language: 'python',
   codeData: "# 自定义python工具，必须以tool命名\ndef tool(goal: str) -> str:\n    pass\n    ",
   codeDataOri: '',
   options: {
    readOnly: true,
    lineNumbers: true,
    fontSize: 15,
    mouseStyle: 'default',
    colorDecorators: true,
    foldingStrategy: 'indentation', // 代码可分小段折叠
    automaticLayout: true, // 自适应布局
    overviewRulerBorder: false, // 不要滚动条的边框
    autoClosingBrackets: true,
    renderLineHighlight: 'all',
    wordWrap: 'on',
    scrollBeyondLastLine: true,
    tabSize: 4, // tab 缩进长度
    minimap: {
     enabled: true // 不要小地图
    },
    fontFamily:
     'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
    folding: true
   },
  }
 },
 computed: {
  ...mapState('maco', ['treeData', 'monacoText', 'ability']),
  codeStrList() {
   if (this.monacoText && this.monacoText.length) {
    return this.monacoText.split('\n');
   } else {
    return [];
   }
  },
  officeServe() {
   const fileType = ['docx', 'doc', 'docm', 'dotm', 'dotx', 'rtf', 'xlsx', 'xls', 'xlsb', 'xlsm', 'pptx', 'ppt']
   if (fileType.includes(this.currentNode.objectType)) {
    return true
   } else {
    return false
   }
  },
 },
 mounted() {
  const result = this.buildFileTree(this.treeData)
 },
 watch: {
  monacoText(newVal) {

   // 当 Vuex 的值变化时更新本地副本（如切换文件）
   // this.localCode = JSON.parse(JSON.stringify(newVal));
  },
  ability: {
   handler(val) {
    this.localComponent = JSON.parse(JSON.stringify(val))
    // 步骤 1：按语言生成默认占位符内容
    let defaultContent = '';
    // this.localCode = ''
    if (!this.ability.language) {
     defaultContent = '// 暂无内容\n'; // 通用注释
    }
    switch (this.ability.language) {
     case 'markdown':
      defaultContent = '// 暂无内容\n'
      // defaultContent = '# 暂无内容\n'; // Markdown 默认标题
      break;
     case 'python':
      // defaultContent = '# 暂无内容\n'; // Python 注释
      break;
     case 'javascript':
      // defaultContent = '// 暂无内容\n'; // JS 注释
      break;
     case 'java':
      // defaultContent = '// 暂无内容\n'; // Java 注释
      break;
     // 其他语言按需补充...
     default:
     // defaultContent = '// 暂无内容\n'; // 通用注释
    }
    this.localCode = this.localComponent.executeResult || defaultContent;
   },
   deep: true,
   immediate: true,
  }
 },
 methods: {
  JSONEnn() {
  try {
    // 解析并美化 localCode
    const obj = JSON.parse(this.localCode);
    this.localCode = JSON.stringify(obj, null, 4); // 2 表示缩进2个空格
  } catch (e) {
    this.$message({
      type: 'error',
      message: 'JSON格式错误，无法美化！'
    });
  }
},
  ...mapMutations('maco', ['setMonacoText', 'REMOVE_TREE_NODE']),
  goBack() {
   this.isClick = false
  },
  changeActiveTab(data, node) {
   if (node && node.isCurrent) {
    this.activeTab = data.label || ''
   }
  },
  handleIsEdit() {
   this.isEdit = true
   this.options.readOnly = false
   if (this.editor && !this.options.readOnly) {
    this.editor.focus();
   }
  },
  async deleteFile(file) {
   try {
    const data = file.objectKey.replace(this.rootKeys, '')
    const params = {
     "scheme_id": this.$route.query.id,
     "obs_suffix_file_key": data
    };
    const lastIndex = file.objectKey.lastIndexOf('/');
    const parentPath = lastIndex === -1 ? '' : file.objectKey.substring(0, lastIndex);
    const res = await abilityDel(params);
    this.REMOVE_TREE_NODE(file.objectKey)
    if (res.data.status === 'success' || res.data.code == 200) {
     this.$message({
      type: 'success',
      message: '删除成功'
     });
     if(file.objectName == '任务.md') {
      this.$emit('handleTask')
     }
     // this.$emit('uploadCapabilityType',parentPath)
    }
   } catch (error) {
    console.error('Error deleting file:', error);
   }
  },
  getComponentEmitHandlers(item) {
   const commonEmitHandlers = this.getCommonEmitHandlers(item);
   const specificEmitHandlers = (() => {
    switch (item.display_component) {
     case "EnnMarkdown":
      return {
       "e-update-content": (newContent) =>
        this.updateMarkdown(item.name, newContent),
      };
     case "EnnTable":
      return {
       "e-update-table": (updatedTable) =>
        this.updateTable(item.name, updatedTable),
      };
     default:
      return {};
    }
   })();
   return { ...commonEmitHandlers, ...specificEmitHandlers };
  },
  // 通用事件处理器
  getCommonEmitHandlers(item) {
   return {
    "e-update-executeResult": (updatedExecuteResult) =>
     this.updateExecuteResult(item.name, updatedExecuteResult),
   };
  },
  // 更新 executeResult 的通用方法
  updateExecuteResult(tabName, updatedResult) {
   // // 查找对应的 tab
   // const targetTab = this.optHeader2TabsList.find(tab => tab.name === tabName);

   // if (targetTab) {
   // 更新 execute_result 值
   this.localComponent.executeResult = updatedResult;
   // } else {
   //   console.warn(`Tab with name "${tabName}" not found.`);
   // }
  },
  // 特定组件更新方法
  updateMarkdown(tabName, newContent) {
  },
  updateTable(tabName, updatedTable) {
  },
  editorDidMount(val) {
   this.editor = val
   this.localCode = this.monacoText;
  },
  change(val) {
   this.localCode = val;
  },
  showSikao() { },
  copyTextNewEnn() {
   let currentComponent = ''
   if (this.currentNode?.objectType === 'md') {
    currentComponent = this.localComponent?.executeResult;
   } else if (this.currentNode?.objectType === 'py') {
    currentComponent = this.localCode
   }else {
    currentComponent = this.localCode
   }
   this.copyTextNew(currentComponent)
  },
  copyTextNew(text) {
   // 获取需要复制的文本
   if (!text.trim()) {
    this.$message({
     type: 'warning',
     message: '复制的内容不能为空！'
    })
    return
   }

   try {
    if (navigator.clipboard && window.isSecureContext) {
     // 使用新的 Clipboard API
     navigator.clipboard.writeText(text)
      .then(() => {
       this.$message({
        type: 'success',
        message: '复制成功！'
       })
      })
      .catch(() => {
       this.fallbackCopyText(text)
      })
    } else {
     // 使用传统方法
     this.fallbackCopyText(text)
    }
   } catch (error) {
    console.error('复制失败:', error)
    this.$message({
     type: 'error',
     message: '复制失败！'
    })
   }
  },
  changeDagangNew() {
   if (this.currentNode?.objectType !== 'md') return
   this.dagangFlagNew = !this.dagangFlagNew
   if (!this.dagangFlagNew) {
    const nodeMarkmap = document.getElementById('markmapenn')
    if (nodeMarkmap) {
     nodeMarkmap.innerHTML = ''
    }
   } else {
    const transformer = new Transformer()
    // const currentTab = this.optHeader2Tab; // 当前 Tab 名称
    const currentComponent = this.$refs[`tab-${this.ability.name}`]; // 获取动态组件实例
    const { root } = transformer.transform(currentComponent.executeResult)
    this.$nextTick(() => {
     Markmap.create('#markmapenn', null, root)
    })
   }
  },
  async handleSave() {
   try {
    const params = {
     scheme_id: this.$route.query.id,
     ability_code: this.currentTab?.name,
     ability_result: this.extractAfterThinkTag(this.localComponent?.executeResult),
     ability_display_component: this.currentTab?.display_component,
    }
    const res = await update_ability_result(params)
    // this.setMonacoText({
    //  ...this.localComponent,
    //  executeResult
    // })
    // if (res.data.status == 'success') {
    //   this.$message({
    //     type: 'success',
    //     message: '更新成功'
    //   });
    //  }
   } catch (error) {

   }
  },
  extractAfterThinkTag(text) {
   // 查找 </think> 标签的索引位置
   const thinkEndIndex = text.indexOf('</think>');
   // 如果找到了 </think> 标签，返回其后的文本
   if (thinkEndIndex !== -1) {
    return text.slice(thinkEndIndex + '</think>'.length).trim();
   }

   // 如果没有找到 </think> 标签，返回空字符串或原始文本
   return text;
  },
  async getUpdate(filePath, content,node) {
   try {
    const params = {
     file_key: filePath,
     content
    }
    // const res = await updateCom(params)
    const res = await update_file_content(params)
    if (res.data.status == 'success' || res.data.code === 200) {
     this.$message({
      type: 'success',
      message: '保存成功'
     });
     if(node.objectName == '任务.md') {
      this.$emit('handleTask')
     }
    }
   } catch (error) {

   }
  },
  handleEdit(name) {
   this.isEdit = true;
   this.options.readOnly = false
   // 保存原始内容用于比较
   if (this.currentNode?.objectType === 'md') {
    this.originalMarkdown = this.localComponent.executeResult;
   } else {
    this.originalContent = this.localCode;
   }
  },
  newHanderSave() {
   this.lastSavedFile = this.currentNode.objectKey;
   let hasChanged = false;
   let contentToSave = '';
   this.editingFilePath = this.currentNode.objectKey
   // this.setMonacoText(this.localCode);
   if (this.currentNode?.objectType === 'md') {
    // 处理Markdown内容
    const processedMarkdown = this.extractAfterThinkTag(this.localComponent?.executeResult);
    const originalProcessed = this.extractAfterThinkTag(this.originalMarkdown);

    // 检查内容是否变化
    if (processedMarkdown !== originalProcessed) {
     hasChanged = true;
     contentToSave = processedMarkdown;
    }
    // this.getUpdate(this.extractAfterThinkTag(this.localComponent?.executeResult))
   } else {
    const imageTypes = ['png', 'jpg', 'jpeg']
    if (!imageTypes?.includes(this.currentNode?.objectType)) {
     // 处理非图片内容
     if (this.localCode !== this.originalContent) {
      hasChanged = true;
      contentToSave = this.localCode;
     }
     // this.getUpdate(this.localCode)
    }
   }
   if (hasChanged) {
    // 只有内容变化时才调用接口
    this.getUpdate(this.currentNode.objectKey, contentToSave,this.currentNode);
   } else {
    // 内容未变化时提示
    // this.$message.warning('内容未修改');
   }
   this.isEdit = false;
   this.options.readOnly = true;
  },
  closeCaiNaFun(name) {
   this.isEdit = false;
   this.localCode = this.monacoText;
   this.localComponent.executeResult = this.ability.executeResult
   this.options.readOnly = true;
  },
  getComponent(componentCode) {
   const mappedComponent = ComponentAliasMap[componentCode] || componentCode; // 优先检查映射表
   return ComponentRegistry[mappedComponent] || null;
  },
  // 根据组件类型动态返回 props（覆盖或扩展通用 props）
  getComponentProps(item) {
   const commonProps = item;
   const specificProps = (() => {
    switch (item.display_component) {
     case "EnnMarkdown":
      return { isEditable: true };
     case "EnnTable":
      return { showHeader: true };
     default:
      return {};
    }
   })();
   return { ...commonProps, ...specificProps };
  },
  // 通用 props
  getCommonProps(item) {
   return {
    executeResult: item.execute_result, // 通用数据
    isLoading: item.isLoading
   };
  },
  currentClick(val) {
   console.log('bbbbbbbbbbbbbbb', this.isRight)
   if (!this.isRight) {
    this.isClick = true
   } else {
    this.isClick = false
   }
   this.fileSwitchHistory = {
    from: this.currentNode ? this.currentNode.objectKey : null,
    to: val.objectKey,
    timestamp: Date.now()
   };
   this.optHeader2Tab = val.label
   this.activeTab = val.label
   this.dagangFlagNew = false
   this.currentEditedFile = val.objectKey;
   if (this.isEdit) {
    this.newHanderSave()
   }
   this.currentNode = val

  },
  buildFileTree(items) {
   return items.map(item => {
    return {
     ...item,
     label: item.objectName,
     children: []
    }
   });
  },

  createDirNode(item) {
   return {
    label: item.objectName,
    isDir: true,
    fullPath: item.objectKey,
    children: [], // 保持空数组
    rawData: item,
    objectType: item.objectType
   };
  },

  createFileNode(item) {
   return {
    label: item.objectName,
    isDir: false,
    fullPath: item.objectKey,
    fileType: item.objectType,
    size: item.objectSize,
    rawData: item,
    objectType: item.objectType
   };
  },
  fangda(e) {
   // console.log('开启缩放', e.target.getElementsByTagName('svg'));
   const svgdoms = e.target.getElementsByTagName('svg')
   const arr = [...svgdoms]
   arr.forEach((svgdom) => {
    if (svgdom.id.indexOf('mermaid') > -1) {
     panzoom(svgdom, {
      smoothScroll: false,
      bounds: true,
      // autocenter: true,
      zoomDoubleClickSpeed: 1,
      minZoom: 0.1,
      maxZoom: 20
     })
    }
   })
  },
  // 获取文件扩展名
  getFileExtension(filename) {
    const parts = filename.split('.');
    return parts.pop() || '';
  }
 }
}
</script>
<style lang="scss" scoped>
.jiantou {
 height: 20px;
 line-height: 20px;
 margin-right: 8px;
 background-size: contain;
 cursor: pointer;
 // border-right: 1px solid #EBECF0;
 // padding-right: 8px;
 // margin-right: 8px;
 display: flex;
 align-items: center;
 margin-bottom: 16px;
 .line {
  width: 1px;
  height: 20px;
  background-color: #EBECF0;
  margin: 0 16px;
 }
 i {
  font-size: 16px;
 }

 span {
  font-size: 16px;
 }
}

.enn-markdown-div .el-button--info {
 background: none !important;
 border: none !important;
}

.office-serve {
 height: calc(100% - 12px);

}

.minWd {
 min-width: 640px;
}

.edit-footer {
 display: flex;
 justify-content: center;
 padding: 12px 0;

 .enn-markdown-div {
  text-align: center;
  background: #fff;
  box-shadow: 0 12px 24px -16px rgba(54, 54, 73, .04),
   0 12px 40px 0 rgba(51, 51, 71, .08),
   0 0 1px 0 rgba(44, 44, 54, .02);
  bottom: 54px;
  display: inline-flex;
  /* 关键修改：改为行内弹性布局 */
  gap: 4px;
  height: 40px;
  padding: 16px;
  border: 1px solid #e8eaf2;
  border-radius: 16px;
  align-items: center;
  /* 移除会强制拉伸宽度的属性 */
  width: auto;
  /* 显式声明宽度自适应 */
  justify-content: space-between;
 }
}

.main-area {
 flex: 1;
 height: 100%;
 display: flex;
 overflow: hidden;

 .full {
  width: 100% !important;
 }

 .vscode-tabs {
  :deep(.el-tabs__item.is-top) {
   color: #333;
  }
  :deep(.el-tabs__header) {
   border-bottom: none;
   margin-bottom: 0;
  }
 }

 .editor {
  height: 100%;
 }

 .sidebar {
  background-color: #fff;
  border-right: 1px solid #ddd;
 }
 :deep(.editor-show) {
  .scrollbar__wrap {
   overflow: inherit !important;
   margin-bottom: 0px !important;
  }
 }
 .editor-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-x: auto;
  position: relative;

  .contain {
   flex: 1;
   overflow-y: auto;
  }
 }
}
</style>