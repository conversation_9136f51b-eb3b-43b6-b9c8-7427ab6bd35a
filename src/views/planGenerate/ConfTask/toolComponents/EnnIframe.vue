<template>
  <div class="enn-iframe" v-loading="isLoading" element-loading-text="生成中..." element-loading-spinner="el-icon-loading">
    <iframe
      v-if="isUrl"
      id="iframeNest"
      :src="executeResult.trim()"
      allow="clipboard-write"
      frameborder="0"
      class="iframe-nest"
      @load="loadingFun"
    ></iframe>
    <pre v-else>{{ executeResult }}</pre>
  </div>
</template>
<script>
export default {
  name: "EnnIframe",
  components: {
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
    isLoading:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      test: 'http://dify.algdev.enncloud.cn/chatbot/gZPtNHQxBZyKAKXs'
    }
  },
  mounted() {
  },
  watch: {
    executeResult (val) {
      console.log('watch------------',val)
    }
  },
  computed: {
    isUrl() {
      if (typeof this.executeResult.trim() !== 'string') {
        return false;
      }
      const urlRegex = /^(https?:\/\/|\/)/;
      if (!urlRegex.test(this.executeResult.trim())) {
        return false;
      }
      try {
        new URL(this.executeResult.trim(), window.location.href);
        return true;
      } catch (_) {
        return false;
      }
    },
  },
  methods: {
    loadingFun(){
      this.$nextTick(()=>{
        this.loading = false
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.enn-iframe, .iframe-nest {
  width: 100%;
  height: calc(100% - 16px);
  overflow: auto;
}
</style>
