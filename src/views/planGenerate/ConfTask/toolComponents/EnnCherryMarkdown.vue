<template>
  <div class="enn-cherry-markdown">
    <div class="knowledge-content">
      <div class="knowledge-edit">
        <div class="editor-section">
          <div class="editor-container">
              <cherry-editor 
                ref="cherryEditor"
                v-model="executeResult"
                @change="handleUpdateContent"
                @focus="handleEditorFocus"
                @blur="handleEditorBlur"
              />
            
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import CherryEditor from '../../cherryEditor.vue'
export default {
  name: "EnnCherryMarkdown",
  components: {
    CherryEditor,
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isEdit: false,
    }
  },
  methods: {
    handleUpdateContent(val) {
      // this.detailContent.text = val
      this.$emit('e-update-executeResult', val)
      console.log('e-update-executeResult', val)
    },
    handleEditorFocus() {
      // this.isEditorFocused = true;
    },
    handleEditorBlur() {
      // this.isEditorFocused = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.enn-cherry-markdown {
  width: 100%;
  height: 100%;
  .knowledge-content {
    width: 100%;
    height: 100%;
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  .knowledge-edit {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  .editor-section {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  .editor-container {
  position: relative;
  flex: 1;
  min-height: 0;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}
}
}
}
}
</style>
