<template>
  <div class="enn-markdown">
    <!-- 编辑器视图 -->
    <div v-show="!dagangFlag && !isCodeMarkdown" id="detail-content" class="optContentBox h-full" @mouseenter="fangda" @mouseup="fangda2">
      <MyEditor id="MyEditor" ref="MyEditor" :md-content="executeResultData" :is-edit="isEdit"
        @updateContent="handleUpdateContent" />
    </div>
    <div v-show="!dagangFlag && isCodeMarkdown" id="detail-content" class="optContentBox h-full" @mouseenter="fangda" @mouseup="fangda2">
      <MyCodeEditor id="MyEditor" ref="MyEditor" :md-content="executeResultData" :is-edit="isEdit"
        @updateContent="handleUpdateContent" />
    </div>

    <!-- 脑图视图 -->
    <div v-show="dagangFlag" @mouseenter="fangda">
      <svg id="markmap" class="mark-map"></svg>
    </div>
    <!-- <div class="enn-markdown-div"> 
      <el-button type="info" size="mini" v-if="!isEdit" @click="() => { isEdit = true }">
        <img src="@/assets/images/planGenerater/bianji.png" />
      </el-button>
      <el-button v-if="isEdit" type="info" size="mini" @click="handleSave">
        <img src="@/assets/images/planGenerater/baocun.png" />
      </el-button>
    </div>  -->
    <!-- 文件链接展示 -->
    <!-- <el-link type="primary">{{ executeResult.file_url }}</el-link> -->
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { queryTaskContent } from '@/api/planGenerateApi.js'
import MyEditor from '../../mdEditor.vue'
import MyCodeEditor from './MyCodeEditor.vue'
export default {
  name: "EnnMarkdown",
  components: {
    MyEditor,
    MyCodeEditor
  },
  props: {
   isCodeMarkdown: {
      type: Boolean,
      default: false,
    },
   optHeader2Tab: {
      type: String,
      default: '',
    },
    executeResult: {
      type: String,
      required: true,
    },
    item:{
      type:Object
    },
    optHeader2TabsList:{
      type:Array
    },
    isEdit:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
     pollingInterval: null,
    dagangFlag: false, // 大纲视图
    executeResultData:'',
    isActive: true,
    }
  },
  watch:{
   optHeader2Tab: {
     async handler(val) {
      if (!this.isActive) return;
      // 清除现有定时器
      this.clearPolling()

      if (val === '任务key') {
        try {
         if (!this.isActive) return;
          // 1. 立即调用并等待完成
          await this.getQueryTaskContent();
          
        } catch (error) {
          console.error("初始化调用失败:", error);
          // 失败时可以选择重试或处理错误
        }
      }
     },
     deep: true,
     immediate: true // 初始时立即执行
   },
    item:{
      handler(val,oldVal) {
          this.executeResultData = this.executeResult
      },
      deep: true,
      immediate:true,
    },
    isRunning: {
   deep: true,
   // immediate: true,
   handler(val) {
    
    if(val) {
     this.startPolling(3000)
    }else {
     this.clearPolling()
    }
    console.log('fajsdfpasjfpasfjpasdfsf111',val,this.isRunning)
   }
  },
  isStop: {
   deep: true,
   // immediate: true,
   handler(val) {
    if(val) {
     this.startPolling(0)
     console.log('停止了111')
    }
   }
  }
  },
  computed: {
  ...mapState('maco', ['isRunning', 'isStop'])
 },
  beforeDestroy() {
   this.isActive = false;
   this.setIsRunning(false)
   this.setIsStop(false)
   this.clearPolling();
  },
  methods: {
   ...mapMutations('maco', ['setIsRunning', 'setIsStop', 'taskContent']),
   async startPolling(time) {
   console.log('fajsdfpasjfpasfjpasdfsf1114444',this.isActive)
   try {
     if (!this.isActive) return;
     this.clearPolling()
     if ( this.optHeader2Tab === '任务key' ) {
      if (!this.isActive) return;
      if (time === 0) {
       await this.getQueryTaskContent();
    } else if( time ) {
     // 2. 等待第一次调用完成后再设置定时器
          this.pollingInterval = setInterval(async () => {
           if (!this.isActive) {
                this.clearPolling();
                return;
              }
            await this.getQueryTaskContent();
          }, time);

    }
     }

    } catch (error) {
     console.error("初始化调用失败:", error);
     // 失败时可以选择重试或处理错误
    }
  },
   clearPolling() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
      }
    },
   async getQueryTaskContent() {
    try {
     if (!this.isActive) return;
     const res = await queryTaskContent({
      scheme_id:this.$route.query.id
     })
     if (!this.isActive) return;
     if(res.data?.code === 200) {
      this.executeResultData = res.data?.result?.task_content || ''
      this.setTaskContent(res.data?.result?.task_content || '')
     }
    } catch (error) {
     
    }
   },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    fangda2(e) {
      if (this.isEdit) {
        const selection = this.$refs.MyEditor.$refs.editorFin?.getCurrentSelectedStr()
        console.log('触发绑定', selection)
        if (selection) {
          this.writeText = selection
          this.insertWriteFlag = true
          this.replaceWriteFlag = false
        } else {
          this.writeText = selection
          this.insertWriteFlag = false
          this.replaceWriteFlag = true
        }
      }
    },
    handleUpdateContent(val) {
      // this.detailContent.text = val
      this.$emit('e-update-executeResult', val)
      console.log('e-update-executeResult', val)
    },
    handleSave(){
      let data = this.optHeader2TabsList.filter(it => it.label == this.item.label)[0]
      this.$emit('handleSave', this.$refs.MyEditor.getMdContentText(),data)
    }
  },
};
</script>

<style lang="scss" scoped>
.enn-markdown{
  width: 100%;
  height: 100%;
  position:relative;
}
.optContentBox {
  //height: calc(100% - 340px);
  // max-height: calc(100vh - 340px);
  // max-height: 100%;
  // height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}

.h-full {
  max-height: 100%;
  height: 100%;
}

.mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}
.enn-markdown-div{
  position:absolute;
  text-align: center;
  background: #fff;
  box-shadow:0 12px 24px -16px rgba(54,54,73,.04),0 12px 40px 0 rgba(51,51,71,.08),0 0 1px 0 rgba(44,44,54,.02);
  bottom: 5px;
  display: flex;
  gap: 12px;
  height: 40px;
  justify-content: space-between;
  left: 50%;
  padding:  16px;
  border: 1px solid #e8eaf2;
  border-radius: 16px;
  align-items: center;
  transform: translateX(-50%);
}
</style>
