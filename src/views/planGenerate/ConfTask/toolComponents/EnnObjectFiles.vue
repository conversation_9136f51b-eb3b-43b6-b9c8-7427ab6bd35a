<template>
 <div style="height:100%;overflow: auto;">
  <macoEdit ref="macoEdit" v-bind="$attrs" :rootKeys="firstUrl" :version="version" :officeUrl="officeUrl"
   v-on="$listeners"
   @handle-download="handleDown" @refreshParentNode="refreshParentNode" :codeLoading="codeLoading" :isImage="isImage"
   :isRight="isRight"
   :imageUrl="imageUrl" @custom-event="handleEvent" @uploadCapabilityType="(val) => { getDirectChildren(val) }"
   :currentTab="item">
  </macoEdit>
 </div>
</template>
<script>
import { queryListFiles, queryObjectKey, querySchemeDetailById } from '@/api/planGenerateApi';
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import _ from 'lodash'
import { mapState, mapMutations } from 'vuex'
import * as monaco from 'monaco-editor';
import { icons } from "@iconify-json/vscode-icons";
import FileTreeManager from '../components/FileTreeManager'
// import icons from "./icons.json";
// import icons from "@iconify-json/vscode-icons/icons.json";

import macoEdit from './macoEdit.vue'
// 可预览格式
const overviewTypes = ['json', 'txt', 'md', 'py', 'sh', 'ipynb', 'png', 'jpg', 'jpeg', 'csv', 'yaml', 'html']
const fileType = ['docx', 'doc', 'docm', 'dotm', 'dotx', 'rtf', 'xlsx', 'xls', 'xlsb', 'xlsm', 'pptx', 'ppt']
const imageTypes = ['png', 'jpg', 'jpeg']
const pdfTypes = ['pdf']
const languageMap = {
 'js': 'javascript',
 'py': 'python',
 'java': 'java',
 'html': 'html',
 'css': 'css',
 'json': 'json',
 'md': 'markdown',
};
const specialComponents = {
 html: 'EnnHTML',
 md: 'EnnMarkdown',
 css: 'EnnCSS',
 json: 'EnnJSON'
};

export default {
 components: {
  macoEdit
 },
 props: {
  isRight:{
    type: Boolean,
    default: true
  },
  firstUrl: {
   type: String,
  },
  version: {
   type: [String, Number]
  },
  item: {
   type: Object
  },
  optHeader2TabsList: {
   type: Array
  },
 },
 data() {
  return {
   isActive: true,
   fileTree: [],
   treeManager: null,
   pollingInterval: null,
   isOffice: false,
   officeUrl: '',
   languages: '',
   // firstUrl: '',
   isPdf: false,
   isImage: false,
   imageUrl: '',
   textMarkDown: '',
   selectedPaths: [],
   modelFiles: [],
   codeStr: '',
   isShowFileList: true,
   codeLoading: false,
  }
 },
 watch: {
  firstUrl: {
   deep: true,
   immediate: true,
   async handler(val) {
    try {
     if (!this.isActive) return;
     this.clearPolling()
     if (val) {
      // 每次值变化时先清除现有定时器 ✨
      this.fileTree = await this.getCapabilityType(val)
      this.setTreeData(this.fileTree)
      this.treeManager.diff(this.fileTree)
      if (!this.isActive) return;
      // this.treeManager?.setInitialTree(this.fileTree || [])
      // if (this.pollingInterval) return;
      // // 2. 等待第一次调用完成后再设置定时器
      // this.pollingInterval = setInterval(async () => {
      //  if (!this.isActive) {
      //   this.clearPolling();
      //   return;
      //  }
      //  // this.pollingInterval = setTimeout(async () => {
      //  // 创建树的深拷贝再进行修改
      //  let newTree = JSON.parse(JSON.stringify(await this.getCapabilityType(val))) || [];
      //  if (!this.isActive) return;
      //  // this.setTreeData(newTree)
      //  // 修改拷贝后的树
      //  // newTree[0].children[0].children[7] = {
      //  //  "isDir": false,
      //  //  "objectKey": "aip-dev/agentgpt-poc/fat/test/8493/code/src/8.py",
      //  //  "objectName": "8.py",
      //  //  "objectSize": 0,
      //  //  "objectType": "py",
      //  //  "label": "8.py",
      //  //  "children": [],
      //  //  "icons": "vscode-icons:file-type-python"
      //  // };
      //  // newTree[0].children[0].children.splice(1,0,{
      //  //  "isDir": false,
      //  //  "objectKey": "aip-dev/agentgpt-poc/fat/test/8493/code/src/9.py",
      //  //  "objectName": "9.py",
      //  //  "objectSize": 0,
      //  //  "objectType": "py",
      //  //  "label": "9.py",
      //  //  "children": [],
      //  //  "icons": "vscode-icons:file-type-python"
      //  // });
      //  // newTree[0].children[0].children = newTree[0].children[0].children.slice(0,2);
      //  // newTree[7].objectKey = 'aip-dev/agentgpt-poc/fat/test/8493/5.py';
      //  // newTree = newTree.slice(0, 5);
      //  // newTree[7].label = '5.py';
      //  // newTree[10] = {
      //  //  "isDir": false,
      //  //  "objectKey": "aip-dev/agentgpt-poc/fat/test/8493/8.py",
      //  //  "objectName": "8.py",
      //  //  "objectSize": 0,
      //  //  "objectType": "py",
      //  //  "label": "8.py",
      //  //  "children": [],
      //  //  "icons": "vscode-icons:file-type-python"
      //  // };
      //  // newTree = null;
      //  // newTree[0].children[0].children[0].label = '5.py';
      //  // newTree[0].children[0].children[0].objectKey = 'aip-dev/agentgpt-poc/fat/test/8493/code/src/5.py';
      //  // newTree[0].children[0].children[0] = newTree[0].children[0].children[3];
      //  // newTree[0].children[0].children[3] = this.fileTree[0].children[0].children[0]
      //  // newTree[0].children[0].children[0] = []

      //  const diffResult = this.treeManager.diff(newTree)
      //  console.log('fjaspdfjspdfjspdfsd', diffResult, newTree)
      //  if (!diffResult) {
      //   console.log('没有变化');
      //   return;
      //  }
      //  if (diffResult.type === 'full') {
      //   // 全量更新
      //   this.treeData = diffResult.data;
      //   console.log('全量更新树数据');
      //   return;
      //  } else {
      //   // 处理增量更新
      //   const { changes } = diffResult;
      //   // 1. 处理删除
      //   if (changes.removed.length > 0) {
      //    changes.removed.forEach(path => {
      //     console.log('删除');
      //     this.REMOVE_TREE_NODE(path)
      //    });

      //   }
      //   // 2. 处理新增
      //   if (changes.added.length > 0) {
      //    changes.added.forEach(node => {
      //     console.log('新增');
      //     this.addNode(node)
      //     // // 获取父节点路径
      //     // const parentPath = node.objectKey.substring(0, node.objectKey.lastIndexOf('/'));
      //     // console.log('新增----------1111222', node,{
      //     //  nodeKey: parentPath,
      //     //  children: node.oldOrder,
      //     //  isRootLevel: parentPath ? false : true
      //     // })
      //     // if (!parentPath) {
      //     //  // 添加到根节点
      //     //  // state.treeData.push(newNode);
      //     // } else {

      //     // }
      //     // this.updateNodeChildren({
      //     //  nodeKey: node.parentPath,
      //     //  children: node.oldOrder,
      //     //  isRootLevel: parentPath ? false : true
      //     // });
      //     //     this.updateNodeChildren({
      //     //  nodeKey: node.parentPath,
      //     //  children: node.oldOrder,
      //     //  isRootLevel: false
      //     // });
      //     // this.updateNodeChildren(node);
      //     // this.addNode(node);
      //    });

      //   }
      //   // 3. 处理更新
      //   if (changes.updated.length > 0) {
      //    console.log('更新11122',changes);
      //    changes.updated?.forEach(update => {
      //     this.updateNode({
      //      path: update.path,
      //      data: update.data
      //     });
      //    });

      //   }
      //  }
      // }, 5000);
     }

    } catch (error) {
     console.error("初始化调用失败:", error);
     // 失败时可以选择重试或处理错误
    }

   }
  },
  isRunning: {
   deep: true,
   // immediate: true,
   handler(val) {

    if (val) {
     this.startPolling(30000)
    } else {
     this.clearPolling()
    }
    console.log('fajsdfpasjfpasfjpasdfsf', val, this.isRunning)
   }
  },
  isStop: {
   deep: true,
   // immediate: true,
   handler(val) {

    if (val) {
     this.startPolling(0)
    }
    console.log('停止了')
   }
  }
 },
 created() {
  // 初始化文件树管理器
  this.treeManager = new FileTreeManager(this)


 },
 computed: {
  ...mapState('maco', ['treeData', 'ability', 'isRunning', 'isStop']),
  codeStrList() {
   if (this.codeStr && this.codeStr.length) {
    return this.codeStr.split('\n');
   } else {
    return [];
   }
  },
  isHideIframe() {
   return !!this.$store.state.planGenerate.isIframeHide
  },
 },
 async mounted() {
  console.log('断点111111', icons, this.isRunning)
  // await this.handleDetailById()

 },
 beforeDestroy() {
  this.isActive = false;
  this.setIsRunning(false)
  this.setIsStop(false)
  this.clearPolling();
  this.setMonacoText({
   language: '',
   executeResult: '',
   isLoading: false,
   display_component: '',
   name: ''
  })
  console.log('beforeDestroyvvv')

 },
 methods: {
  setCurrentRunCode(runCode){
  },
  ...mapMutations('maco', ['setTreeData', 'setMonacoText', 'updateNodeChildren', 'REMOVE_TREE_NODE', 'updateNode', 'addNode', 'setIsRunning', 'setIsStop']),
  // 轮询方法
  async startPolling(time) {
   console.log('fajsdfpasjfpasfjpasdfsf1114444', this.isActive, time)
   try {
    if (!this.isActive) return;
    this.clearPolling()
    if (this.firstUrl) {
     if (!this.isActive) return;
     // // 2. 等待第一次调用完成后再设置定时器
     if (time === 0) {
      await this.executePollingTask(); // 直接等待执行完成
     } else if (time) {
      this.pollingInterval = setInterval(async () => {
       await this.executePollingTask();
      }, time);

     }
    }

   } catch (error) {
    console.error("初始化调用失败:", error);
    // 失败时可以选择重试或处理错误
   }
  },
  async executePollingTask() {
   if (!this.isActive) {
    this.clearPolling();
    return;
   }
   // this.pollingInterval = setTimeout(async () => {
   // 创建树的深拷贝再进行修改
   let newTree = JSON.parse(JSON.stringify(await this.getCapabilityType(this.firstUrl,'interval'))) || [];
   this.$emit('handleTask')
   if (!this.isActive) return;
   const diffResult = this.treeManager.diff(newTree)
   console.log('fjaspdfjspdfjspdfsd', diffResult, newTree)
   if (!diffResult) {
    console.log('没有变化');
    return;
   }
   if (diffResult.type === 'full') {
    // 全量更新
    this.treeData = diffResult.data;
    console.log('全量更新树数据');
    return;
   } else {
    // 处理增量更新
    const { changes } = diffResult;
    // 1. 处理删除
    if (changes.removed.length > 0) {
     changes.removed.forEach(path => {
      console.log('删除');
      this.REMOVE_TREE_NODE(path)
     });

    }
    // 2. 处理新增
    if (changes.added.length > 0) {
     changes.added.forEach(node => {
      console.log('新增');
      this.addNode(node)
     });

    }
    // 3. 处理更新
    if (changes.updated.length > 0) {
     console.log('更新11122', changes);
     changes.updated?.forEach(update => {
      this.updateNode({
       path: update.path,
       data: update.data
      });
     });

    }
   }
  },
  handleDownload(data) {
   console.log('我是下载方法', data)
  },
  getAllFiles(node, basePath = '') {
   let files = [];
   // 当前节点的路径 = 基础路径 + 节点名称
   const currentPath = basePath + node.objectName;

   if (!node.isDir) {
    // 文件：直接添加到列表（携带完整路径）
    files.push({
     ...node,
     relativePath: currentPath // 文件在ZIP中的完整路径
    });
   } else {
    // 文件夹：递归处理子节点，路径后添加斜杠
    const newBasePath = currentPath + '/';
    if (node.children) {
     node.children.forEach(child => {
      files = files.concat(this.getAllFiles(child, newBasePath));
     });
    }
   }
   return files;
  },
  clearPolling() {
   console.log('beforeDestroy1')
   if (this.pollingInterval) {
    console.log('beforeDestroy2')
    clearInterval(this.pollingInterval);
    this.pollingInterval = null;
    console.log('beforeDestroy3')
   }
  },
  uploadNode(newTree, lastTree) {
   const updateNode = (nodes) => {
    if (!nodes) return false;
    for (let i = 0; i < nodes.length; i++) {
     const node = nodes[i];
     if (node.objectKey === nodeKey) {
      // 1. 找出旧数组中匹配的所有节点
      const oldNodes = node.children || [];

      // 2. 创建一个新数组，初始为空
      const newChildren = [];

      // 3. 遍历新children数组
      for (const newItem of lastTree) {
       // 在旧节点中查找匹配项
       const matchedOldNode = oldNodes.find(old =>
        old.objectKey === newItem.objectKey &&
        old.objectName === newItem.objectName
       );

       if (matchedOldNode) {
        // 4. 如果找到匹配项，创建更新后的节点
        newChildren.push({
         ...matchedOldNode,       // 保留旧节点的状态            // 应用更新
         icons: newItem.icons     // 特别更新icons属性
        });
       } else {
        // 5. 如果没有匹配项，直接添加新节点
        console.log('newItem', newItem)
        newChildren.push(newItem);
       }
      }

      // 6. 更新节点的children
      node.children = newChildren;
      return true;
     }
     if (node.children && node.children.length) {
      const found = updateNode(node.children);
      if (found) return true;
     }
    }
    return false;
   };
   updateNode(newTree);
  },
  async handleDetailById() {
   await querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
    this.firstUrl = res.data.result?.result_file_root_path

   })
  },
  // 获取 Monaco Editor 支持的语言列表
  getMonacoLanguages() {
   return monaco.languages.getLanguages().map(lang => ({
    id: lang.id,
    extensions: lang.extensions || [],
    filenames: lang.filenames || [],
    aliases: lang.aliases || []
   }));
  },

  // 获取 Monaco 支持的文件类型
  getMonacoFileType(fileName) {
   const languages = this.getMonacoLanguages();
   const lowerCaseFileName = fileName.toLowerCase();

   // 1. 首先检查文件名是否匹配特定文件
   for (const lang of languages) {
    if (lang.filenames.some(fn => fn.toLowerCase() === lowerCaseFileName)) {
     return lang.id;
    }
   }

   // 2. 检查文件扩展名
   if (fileName.includes('.')) {
    const extension = fileName.split('.').pop().toLowerCase();

    for (const lang of languages) {
     if (lang.extensions.some(ext => ext.toLowerCase() === `.${extension}`)) {
      return lang.id;
     }
    }
   }

   // 3. 检查无扩展名的常见文件
   const commonFiles = ['makefile', 'dockerfile', 'procfile'];
   if (commonFiles.includes(lowerCaseFileName)) {
    return lowerCaseFileName;
   }

   // 4. 默认返回文件名作为类型
   return fileName;
  },
  handleEvent(data) {
   this.codeLoading = true
   // if (overviewTypes.includes(data?.objectType)) {

   if (data?.objectType != 'zip' && data?.objectType != 'pfx') {
    this.imageUrl = '';
    this.isImage = imageTypes.includes(data?.objectType);
    this.isPdf = pdfTypes.includes(data?.objectType);
    this.isOffice = fileType.includes(data?.objectType);
    this.getObjectKey(data?.objectKey, false, data)
   } else {
    this.imageUrl = '';
    this.setMonacoText({
     language: '',
     executeResult: '该文件不可预览',
     isLoading: false,
     display_component: '',
     name: ''
    })
    this.codeLoading = false
   }

   // }
  },
  transformItem(item, executeResult) {
   const { objectType, objectName } = item;
   const language = this.getMonacoFileType(objectName)
   const display_component = specialComponents[objectType]
    || `Enn${language.charAt(0).toUpperCase()}${language.slice(1)}`;
   return {
    language,
    executeResult,
    isLoading: false,
    display_component,
    name: item.label
   };
  },
  async download(files, data) { // 接收带路径的文件列表
   const zip = new JSZip();
   try {
    await this.zipFiles(zip, files);
    zip.generateAsync({ type: 'blob' }).then(content => {
     FileSaver.saveAs(content, `${data.objectName || '文件下载'}.zip`);
    });
   } catch (e) {
    console.error('压缩失败', e);
   }
  },
  handleDown(data) {
   let selected = []
   if (data.isDir) {
    const newFiles = this.getAllFiles(data)
    selected = newFiles || []
    if (selected.length === 0) {
     console.log('空文件夹');
     return;
    }
    Promise.all(selected.map(item =>
     this.generateSignByObjectKey(item.objectKey)
    ))
     .then(urls => {
      // 组合文件路径和下载URL
      const files = selected.map((item, index) => ({
       path: item.relativePath,
       url: urls[index]
      }));
      this.download(files, data); // 传递带路径的文件列表
     })
     .catch(console.error);
   } else {
    this.generateSignByObjectKey(data.objectKey)
     .then(url => {
      this.downloadSingleFile(url, data.objectName);
     })
     .catch(console.error);
    // selected = [{
    //   ...data,
    //   relativePath: data.objectName // 直接使用文件名作为路径
    // }];
   }

  },
  downloadSingleFile(url, fileName) {
  // 1. 通过fetch获取文件内容
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      // 2. 创建Object URL
      const blobUrl = URL.createObjectURL(blob);

      // 3. 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;

      // 4. 强制设置download属性
      link.download = fileName;

      // 5. 模拟点击下载
      document.body.appendChild(link);
      link.click();

      // 6. 清理资源
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      }, 100);
    })
    .catch(error => {
      console.error('文件下载失败:', error);
    });
},
  // 当前url获取blob 对象
  async getBlob(url) {
   return new Promise((resolve) => {
    fetch(url)
     .then((response) => {
      return response.blob();
     })
     .then((res) => {
      const blob = new Blob([res]);
      resolve(blob);
     });
   });
  },
  async zipFiles(zip, files) {
   return new Promise((resolve) => {
    if (files.length === 0) resolve();

    let processed = 0;
    files.forEach(file => {
     this.getBlob(file.url)
      .then(blob => {
       // 关键：使用完整路径创建文件（自动创建父文件夹）
       zip.file(file.path, blob, { binary: true });
      })
      .finally(() => {
       processed++;
       if (processed === files.length) resolve();
      });
    });
   });
  },
  async generateSignByObjectKey(urlKey) {
   try {
    const res = await queryObjectKey({ 'objectKey': urlKey });
    // 明确返回 res.data.result，假设接口响应结构为 { data: { result: ... } }
    return res?.data?.result;
   } catch (err) {
    // 错误处理（可选：抛出错误或返回默认值）
    console.error(`请求 ${urlKey} 失败:`, err);
    throw err; // 保持 Promise 链的 reject 状态
    // 或返回默认值（如 null），避免 Promise.all 整体失败
    // return null;
   }
  },
  async getObjectKey(urlKey, overview = null, obj) {
   try {
    if (!urlKey) return
    const res = await queryObjectKey({
     'objectKey': urlKey,
    })
    if (res.status === 200 || res.data?.status === 200) {
     if (this.isImage) {
      this.imageUrl = res?.data.result;
      this.codeLoading = false;
      return;
     }
     if (this.isPdf) {
      this.codeLoading = false;
      this.setMonacoText({
       language: 'pdf',
       executeResult: res?.data.result,
       isLoading: false,
       display_component: 'EnnIframe',
       name: obj.label
      })
      return
     }
     if (this.isOffice) {

      this.officeUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(res?.data.result)}`;
      this.codeLoading = false;
      return;
     }
     if (overview !== null && res.data?.result) {
      this.FetchContent(res.data?.result, overview, obj)
     } else {
      const result = this.transformItem(obj, '')
      this.setMonacoText(result)
     }
    }
   } catch (error) {
    this.codeLoading = false;

   } finally {
    this.codeLoading = false;
   }
  },
  async getCapabilityType(objectKey) {
   try {
    if (!this.isActive) return;
    const res = await queryListFiles({
     prefix: objectKey || '',
     scheme_id: Number(this.$route.query.id),
     version: this.version
    });
    if (!this.isActive) return;
    if ([200, '200'].includes(res?.status || res?.data?.status)) {
     const nodes = res.data?.result || [];

     // 递归处理子节点
     const processNodes = async (nodeList) => {
      return Promise.all(
       nodeList.map(async (item) => {
        const language = this.getMonacoFileType(item.objectName)
        this.languages = language
        // const icon = `${icons.prefix}:file-type-${language}`
        const fileType = `file-type-${language === 'plaintext' ? 'text' : language}`
        let icon = ''
        if (icons.icons.hasOwnProperty(fileType)) {
         icon = `vscode-icons:${fileType}`
        } else {
         if(imageTypes.includes(item.objectType)) {
          icon = 'vscode-icons:file-type-image'
         }else {
          icon = 'vscode-icons:default-file'
         }
        }
        const treeNode = {
         ...item,
         label: item.objectName,
         isDir: item.isDir,
         objectKey: item.objectKey,
         objectType: item.objectType,
         children: [], // 初始化children
         icons: icon
        };

        // 如果是文件夹则递归查询子节点
        if (item.isDir) {
         treeNode.children = await this.getCapabilityType(item.objectKey);
        }

        return treeNode;
       })
      );
     };

     return await processNodes(nodes);
    }
    return [];
   } catch (error) {
    console.error('查询失败:', error);
    return [];
   }

  },
  // 新增：局部刷新父节点
  async refreshParentNode(parentKey, obj, isRootLevel, newFileKey, isUpload = false, reset = false, node = null) {
   // console.log('fjdapfjdpsjfdsasss',reset,JSON.stringify(node.isCurrent))
   try {
    const children = await this.getDirectChildren(parentKey);
    if(obj.objectName == '任务.md') {
      this.$emit('handleTask')
     }
    // if(!this.isRootLevel) {
    // 调用API获取父节点下最新的子节点列表
    // 更新父节点的子节点列表
    this.updateNodeChildren({
     nodeKey: parentKey,
     children: children,
     isRootLevel: isRootLevel,
     currentNode: obj,
     isUpload
    });

    // 设置选中新创建的文件
    // if(node && node.isCurrent) {
    if (!reset || (node && node.isCurrent)) {
     this.$nextTick(() => {
      if (newFileKey) {
       const that = this.$refs?.macoEdit?.$refs?.tree?.$refs?.fileTree
       that?.setCurrentKey(newFileKey);
       if (isUpload) {
        const node = that?.getNode(newFileKey);
        if (node) {
         this.$refs?.macoEdit?.$refs?.tree?.handleNodeClick(node.data, node);
        }
       }
      }
     });
    }
   } catch (error) {
    console.error('刷新父节点失败:', error);
    this.$message.error('刷新文件列表失败');
   }
  },
  async getDirectChildren(objectKey) {
   try {
    const res = await queryListFiles({
     "prefix": objectKey || '',
     scheme_id: Number(this.$route.query.id),
     version: this.version
    })
    if ([200, '200'].includes(res?.status || res?.data?.status)) {
     return (res.data?.result || []).map(item => {
      const language = this.getMonacoFileType(item.objectName)
      // const icon = `${icons.prefix}:file-type-${language}`
      const fileType = `file-type-${language === 'plaintext' ? 'text' : language}`
      let icon = ''
      if (icons.icons.hasOwnProperty(fileType)) {
       icon = `vscode-icons:${fileType}`
      } else {
       icon = 'vscode-icons:default-file'
      }
      return {
       ...item,
       label: item.objectName,
       isDir: item.isDir,
       objectKey: item.objectKey,
       objectType: item.objectType,
       children: item.isDir ? [] : undefined,
       icons: icon
      }
     });
    }
    return [];
   } catch (error) {
    return [];
   }
  },
  clickByLevel(level, object) {
   this.isShowFileList = true

   let objectKey = []
   this.selectedPaths.forEach((item, index) => {
    if (index <= level) {
     objectKey.push(item)
    }
   })
   if (level == 0) {
    this.selectedPaths = [this.selectedPaths[0]]
    this.getCapabilityType(this.firstUrl)
    return false
   } else {
    let data = []
    this.selectedPaths.forEach((item, index) => {
     if (index <= level) {
      data.push(item)
     }
    })
    this.selectedPaths = data
    this.getCapabilityType(objectKey.join(''))
    return false
   }
   // this.getFileList(objectKey)
   // this.getFileList('uploader/model/chinese_alpaca_plus_combine_7b/')
  },
  getFileList(objectName) {
   this.modelFiles = []
   this.readmeKey = 'uploader/model/chinese_alpaca_plus_combine_7b/README.md'
   // listObjects(objectName).then(res => {
   //  this.modelFiles = res.data.data
   //  this.readmeKey = _.find(res.data.data, item => item.objectName === 'README.md')?.objectKey
   // })
  },
  FetchContent(url, overview, obj) {
   fetch(url)
    .then(response => response.text())
    .then(data => {
     // data就是文件的内容
     this.textLoading = false
     this.codeLoading = false
     if (overview) {
      this.textMarkDown = data
     } else {
      const result = this.transformItem(obj, data)
      this.setMonacoText(result)
      this.codeStr = data
     }
    })
    .catch(error => {
     this.codeLoading = false
    });
  },
  getFileExtension(filename) {
   const parts = filename.split('.');
   return parts.pop() || '';
  },
  clickByObject(object) {
   // 拦截不可编辑的文件
   if (this.getFileExtension(object?.objectName) == 'png' || this.getFileExtension(object?.objectName) == 'jpg' || this.getFileExtension(object?.objectName) == 'jpeg' || this.getFileExtension(object?.objectName) == 'csv') {
    this.$message.warning('该文件不可预览')
    return false
   }
   if (object.isDir) { // 文件夹
    this.selectedPaths.push(object.objectName)
    this.objectKeySaveName = ''
    this.getCapabilityType(this.selectedPaths.join('/'))
   } else {
    this.codeStr = ''
    this.imageUrl = '';
    this.isShowFileList = false
    if (overviewTypes.includes(object?.objectType)) {
     this.codeLoading = true
     this.isImage = imageTypes.includes(object?.objectType);
     this.objectKeySaveName = object?.objectName
     this.getObjectKey(object?.objectKey, false)
    } else {
     this.codeLoading = false
    }
   }
  }
 }
}
</script>
<style lang="scss" scoped>
.a-class {
 cursor: pointer;

 &:hover {
  color: #0f55fa;
  text-decoration: underline;
 }
}

.div-code {
 padding: 20px;

 code {
  all: initial;
  /*清除继承样式*/
  display: block;
  /*设置布局流，避免换行导致的错误布局*/
  white-space: nowrap;
  overflow: auto;
  margin: 6px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 20px;

  pre {
   margin: 0;
   padding: 0;
   display: inline-block;

   &.span-num {
    padding: 0 20px 0 30px;
   }
  }
 }
}

.div-item {
 padding: 8px 16px;
 font-size: 14px;
 font-family: PingFangSC-Regular, PingFang SC;
 font-weight: 400;
 color: #343a40;
 line-height: 22px;
 cursor: pointer;

 &:not(:last-child) {
  border-bottom: 1px solid #e7e9ea;
 }

 img {
  width: 14px;
  height: auto;
 }

 img,
 span {
  vertical-align: middle;
 }
}

.div-item img {
 width: 14px;
 height: auto;
}

.p-file-name {
 font-size: 14px;
 font-family: PingFangSC-Regular, PingFang SC;
 font-weight: 400;
 color: #343a40;
 line-height: 20px;
 cursor: pointer;
}

:deep(.box-card) {
 .slot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
 }

 .el-card__header {
  background: #f0f4ff;
  border-bottom: 1px solid #e7e9ea;
  padding: 8px 16px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #6c757d;
  line-height: 22px;
 }

 .el-select .el-input.is-focus .el-input__inner,
 .el-select .el-input__inner:focus,
 .el-input--suffix .el-input__inner {
  border-radius: 4px;
  border: 1px solid #ced4da;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #343a40;
  line-height: 22px;
 }
}
</style>
