<template>
  <div class="enn-mindtree" v-loading="isLoading" element-loading-text="生成中..."
    element-loading-spinner="el-icon-loading">
    <div class="optContentBox" @mouseenter="fangda">
      <MyEditorPreview id="MyEditor4" ref="MyEditor4" :md-content="executeResult"></MyEditorPreview>
    </div>
  </div>
</template>

<script>
import MyEditorPreview from '../../mdEditorPreview.vue'
import panzoom from 'panzoom'

export default {
  name: "EnnMindTree",
  components: {
    MyEditorPreview
  },
  // watch: {
  // isLoading: {
  //   handler(Newval, oldVal) {
  //     console.log('isLoading Newval',Newval);
  //   },
  //   immediate: true,
  // },
  // },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
    isLoading:  {
      type: Boolean,
    }
  },
  data() {
    return {
      loading: false,

    }
  },
  methods: {
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.enn-mindtree {
  width: 100%;
  height: 100%;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
