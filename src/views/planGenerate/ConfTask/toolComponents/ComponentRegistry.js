// ComponentRegistry.js
import EnnMarkdown from './EnnMarkdown.vue';
import EnnMindMap from './EnnMindMap.vue';
import EnnCode from './EnnCode.vue';
import EnnTable from './EnnTable.vue';
import EnnBiForm from './EnnBiForm.vue';
import EnnMarkdownWithMath from './EnnMarkdownWithMath.vue';
import Enn<PERSON>son from './EnnJson.vue';
import EnnMindTree from './EnnMindTree.vue';
import EnnDataSourceTable from './EnnDataSourceTable.vue';
import EnnObjectFiles from './EnnObjectFiles.vue';


import EnnDataSourceSelect from './EnnDataSourceSelect.vue';
import EnnKnowledgeBase from './EnnKnowledgeBase.vue';
import EnnIframe from './EnnIframe.vue';
import EnnHTML from './EnnHTML.vue';
import EnnCherryMarkdown from './EnnCherryMarkdown.vue';

export const ComponentRegistry = {
  EnnMarkdown,
  EnnMarkdownWithMath,
  EnnCode,
  EnnJson,
  EnnTable,
  EnnMindTree,
  EnnMindMap,
  EnnBiForm,
  EnnDataSourceSelect,
  EnnDataSourceTable,
  EnnKnowledgeBase,
  EnnObjectFiles,
  EnnIframe,
  EnnHTML,
  EnnCherryMarkdown
};

export const ComponentAliasMap = {
  EnnMarkdownWithMath: 'EnnMarkdown',
  EnnTable: 'EnnMarkdown',
  EnnBiForm: 'EnnMarkdown',
  EnnDataSourceSelect: 'EnnJson',
};

{
  /* <template>
  <div class="enn-mindtree">

  </div>
</template>

<script>
export default {
  name: "EnnMindTree",
  components: {
  },
  props: {
    executeResult: {
      type: String,
      required: true,
    },
  },
  data() {
    return {


    }
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.enn-mindtree {}
</style> */
}
