// constants.js
export const RunCode = {
  KNOWLEDGE_BASE_SEARCH: 'knowledge_base_search',
  CREATE_QUESTIONS_RESULT: 'create_questions_result',
  SCHEME_OPTIMIZE: 'scheme_optimize',
  DECISION_TREE: 'decision_tree',
  ALIGN_ANALYSIS: 'align_analysis',
  MODEL_PARAM_EXTRACTION: 'model_param_extraction',
  MODELING_INFO_STRUCTURE: 'modeling_info_structure',
  MATH_MODEL_GENERATE: 'math_model_generate',
  ALIGN_DATA_GENERATE: 'align_data_generate',
  DECISION_MAKING_GENERATE: 'decision_making_generate',
  CODE_DEPLOY: 'code_deploy',
  CODE_DEPLOY_NEW:'code_custom_deploy',
  CODE_TEST: 'code_test',
  CODE_CUSTOM_TEST: 'code_custom_test',
  ABILITY_CHECK: 'ability_check',
  CODE_ANALYSIS: 'code_analysis',
  API_PARAM_ANALYSIS: 'api_param_analysis'
  // Add other codes as needed
};

// """
// 任务类型定义
// 注意：此枚举有顺序要求
// """
// KNOWLEDGE_BASE_SEARCH = 'knowledge_base_search'
// CREATE_QUESTIONS_RESULT = 'create_questions_result'
// SCHEME_OPTIMIZE = 'scheme_optimize'
// DECISION_TREE = 'decision_tree'
// ALIGN_ANALYSIS = 'align_analysis'
// MODEL_PARAM_EXTRACTION = 'model_param_extraction'
// MODELING_INFO_STRUCTURE = 'modeling_info_structure'
// MATH_MODEL_GENERATE = 'math_model_generate'
// ALIGN_DATA_GENERATE = 'align_data_generate'
// DECISION_MAKING_GENERATE = 'decision_making_generate'
// CODE_DEPLOY = 'code_deploy'
// CODE_TEST = 'code_test'
// CODE_ANALYSIS = 'code_analysis'
// API_PARAM_ANALYSIS = 'api_param_analysis'
