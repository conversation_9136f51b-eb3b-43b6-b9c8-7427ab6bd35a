<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide
        ? 'chatContainerTest chatContainerTestFrame'
        : 'chatContainerTest'
    "
  >
    <div
      :class="
        $store.state.planGenerate.isIframeHide
          ? 'containerBox2 containerBox2IFrame'
          : 'containerBox2'
      "
      style="width: 100%"
    >
      <div
        id="left-content"
        :style="{
          width: leftWidth,
          maxWidth: leftWidth,
          marginRight: !rightFullFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: 'relative'
        }"
        :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'"
      >
        <div
          v-if="
            [
              'device_ops_assistant_scene',
              'artificial_handle_scene',
              'visit_leader_cognition_scene',
              'intelligent_conversation_scene',
              'sop_scene',
              'digital_twin_assistant_scene'
            ].indexOf(agentSenceCode) > -1
          "
          class="optContent"
        >
          <div class="optHeader">
            <div class="rightTitle">{{ showTestFlag ? '思维树' : '仿真设备情况' }}</div>
            <div class="rightTitleOpt">
              <el-tooltip
                v-show="showTestFlag"
                class="item"
                effect="dark"
                :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'"
                placement="top"
              >
                <el-button type="info" size="mini" @click="changeShowType"
                  ><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/markdown.png"
                /></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="rightFullFlag ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="rightFullFlag ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowFull"
                  ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                /></el-button>
              </el-tooltip>
            </div>
          </div>
          <div :class="miniFlag ? 'optScroll optScrollMini' : 'optScroll'">
            <div :class="miniFlag ? 'optContentBox optContentBoxMini' : 'optContentBox'">
              <div v-if="!showTestFlag" class="optContentBox">
                <iframe :src="simulationUrl" width="100%" height="100%" />
              </div>
              <div v-else-if="jueceYulanFlag" class="optContentBox" @mouseenter="fangda">
                <MyEditor id="MyEditorLast" ref="MyEditorLast" :md-content="treeData"></MyEditor>
                <!-- <pre v-if="(treeData && treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
                <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
              </div>
              <div v-else>
                <pre>{{ treeData }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="planDetailShow && !rightFullFlag"
        id="resize"
        class="resize"
        title="收缩侧边栏"
        @mousedown="startDrag"
      >
        <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
        <div class="el-two-column__trigger-icon">
          <SvgIcon name="dragborder" class="process-icon" />
        </div>
        <div class="el-two-column__icon-bottom">
          <div class="el-two-column__icon-bottom-bar"></div>
        </div>
      </div>
      <div
        id="right-content"
        :style="{
          width: rightWidth,
          marginRight: '16px',
          transition: isDragging ? 'none' : 'width 0.2s',
          userSelect: isDragging ? 'none' : 'auto'
        }"
        :class="!planDetailShow ? 'chatRight chatRightFull' : 'chatRight'"
      >
        <div
          v-if="
            [
              'device_ops_assistant_scene',
              'artificial_handle_scene',
              'visit_leader_cognition_scene',
              'intelligent_conversation_scene',
              'sop_scene',
              'digital_twin_assistant_scene'
            ].indexOf(agentSenceCode) > -1
          "
          :class="miniFlag ? 'optContent optContentMini' : 'optContent'"
        >
          <div class="optHeader">
            <div class="rightTitle">智能能力测试与迭代</div>
            <div class="rightTitleOpt">
              <el-tooltip
                v-if="!rightFullFlag"
                class="item"
                effect="dark"
                :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="!planDetailShow ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowRight"
                >
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                  />
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-loading="taskLoading"
            :class="miniFlag ? 'optScroll optScrollMini' : 'optScroll'"
            element-loading-text="测试中..."
            element-loading-spinner="el-icon-loading"
            cl
          >
            <div :class="{ top: true, 'top-simulation': !showTestFlag }">
              <div
                class="searchFilter"
                :style="{
                  justifyContent:
                    [
                      'artificial_handle_scene',
                      'visit_leader_cognition_scene',
                      'intelligent_conversation_scene',
                      'sop_scene',
                      'digital_twin_assistant_scene'
                    ].indexOf(agentSenceCode) > -1
                      ? 'flex-end'
                      : 'space-between'
                }"
              >
                <div>
                  <div v-if="showTestFlag" class="font-style">能力输入参数:</div>
                  <div v-else>
                    <!--                    <span class="font-style">设备选择</span>-->
                    <el-select
                      v-model="deviceVal"
                      size="medium"
                      placeholder="请选择设备"
                      style="width: 200px; margin-left: 10px"
                      @change="handleDeviceChange"
                    >
                      <el-option
                        v-for="item in deviceOptions"
                        :key="item.deviceCode"
                        :label="item.deviceName"
                        :value="item.deviceCode"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <el-button
                  class="button-last"
                  :style="{ marginLeft: '8px' }"
                  type="primary"
                  :disabled="!showTestFlag && !detailDeviceInfoList.length"
                  @click="showTestFlag ? onTest() : onSimulationTest()"
                  >测试</el-button
                >
              </div>
              <el-table
                v-if="showTestFlag"
                :max-height="400"
                style="width: 100%; margin-top: 16px"
                size="medium"
                :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
                :data="tableData"
              >
                <el-table-column min-width="170" prop="param_key" label="名称"></el-table-column>
                <el-table-column min-width="170" prop="param_desc" label="描述"></el-table-column>
                <el-table-column min-width="100" prop="param_dataType" label="类型">
                  <template #default="scope">
                    <el-select v-model="scope.row.param_dataType" placeholder="请选择">
                      <el-option key="str" label="String" value="String"></el-option>
                      <el-option key="int" label="Number" value="Number"></el-option>
                      <el-option key="object" label="Object" value="Object"></el-option>
                      <el-option key="Array" label="Array" value="Array"></el-option>
                      <el-option key="bol" label="Boolean" value="bool"></el-option>
                      <el-option key="file" label="File" value="File"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column min-width="170" prop="param_value" class-name="no-bor" label="值">
                  <template #default="scope">
                    <div v-if="scope.row.param_dataType === 'File'" style="width: 160px">
                      <shardUploaderTool
                        :accept="''"
                        upload-tip="小于20M"
                        :limit="20"
                        :multiple="false"
                        :cover="true"
                        :index-ref="scope.$index"
                        :shard-limit="1"
                        @onFilesChange="uploadScriptCallback"
                        @onFilesStatus="uploadShardStatusCb"
                        @on-remove="removeFile"
                      />
                    </div>
                    <div
                      v-else-if="
                        scope.row.param_dataType === 'bool' ||
                        scope.row.param_dataType === 'Boolean'
                      "
                      style="width: 160px"
                    >
                      <el-select v-model="scope.row.param_value" placeholder="请选择">
                        <el-option key="1" value="true">true</el-option>
                        <el-option key="2" value="false">false</el-option>
                      </el-select>
                    </div>
                    <el-input
                      v-else
                      v-model.trim="scope.row.param_value"
                      placeholder="请输入"
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div :class="{ bottom: true, 'bottom-simulation': !showTestFlag }">
              <div class="test-result">
                <div class="font-style">
                  能力输出参数：<span v-if="tried" class="test-result-warn"
                    >如果对测试结果不满意，可返回到之前的步骤重新调整</span
                  >
                </div>
                <JsonViewer
                  v-if="tried && !streamFlag && showTestFlag"
                  class="json-content"
                  :value="jsonData"
                  :expand-depth="5"
                ></JsonViewer>
                <div
                  v-if="streamFlag && showTestFlag"
                  style="color: #323233; width: 100%; max-height: 400px"
                >
                  {{ jsonData }}
                </div>
                <div v-if="tried && !streamFlag && !showTestFlag">
                  <JsonViewer
                    v-for="item in jsonDataList"
                    class="json-content"
                    :value="item"
                    :expand-depth="5"
                  ></JsonViewer>
                </div>
                <!-- <MyEditor v-if="streamFlag" id="jsonData" ref="jsonData" :md-content="jsonData" style="width: 100%;max-height: 500px"></MyEditor> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="optFooter">
      <el-button
        class="button-last"
        type="info"
        :disabled="developFlag ? treeStatusLast === 1 || treeStatusLast === 0 : false"
        @click="
          () => {
            !developFlag ? changeViews(0) : changeViews(2)
          }
        "
        >上一步</el-button
      >
      <el-button v-if="!developFlag" class="button-last" type="info" @click="showTask()"
        >查看任务进度</el-button
      >
      <el-button v-if="!developFlag" class="button-last" type="info" @click="developFn"
        >转研发</el-button
      >
      <el-button
        v-if="showSimulationTest()"
        class="button-last"
        type="info"
        @click="handleSimulationState"
        >{{ showTestFlag ? '仿真测试' : '传统测试' }}</el-button
      >
      <el-button
        class="button-last"
        :disabled="
          treeStatusLast === 1 ||
          treeStatusLast === 0 ||
          loading ||
          taskLoading ||
          lastLoading ||
          hasChatingName !== ''
        "
        type="primary"
        @click="handlePublish"
        >发布</el-button
      >
      <!-- <el-button class="button-last" type="info" @click="handleCancel">取消</el-button> -->
    </div>

    <el-dialog
      custom-class="last-dialog"
      :visible.sync="dialogVisible2"
      title="智能能力信息"
      :show-close="!lastLoading"
    >
      <el-form
        ref="lastFormRef"
        element-loading-spinner="el-icon-loading"
        v-loading="dialogContentLoading"
        :model="lastFormModal"
        label-width="120px"
        :rules="lastFormRules"
      >
        <!-- <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="lastFormModal.name"
            disabled
            maxlength="150"
            placeholder="请输入内容"
          />
        </el-form-item> -->
        <el-form-item label="智能能力图标:" style="margin-top: 0px" prop="abilityIcon">
          <div style="display: flex; height: 60px">
            <el-upload
              ref="uploadBtn"
              class="upload-demo"
              v-loading="formVisible.uploadBtn"
              :action="uploadUrl"
              list-type="picture-card"
              :data="uploadParam"
              :limit="1"
              :file-list="fileList"
              :multiple="false"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
              :before-upload="beforeUpload"
              :on-success="modelUploadSuccess"
              :on-exceed="uploadExceed"
            >
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{ file }">
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-delete" @click="clearImage">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
            <div style="display: flex; justify-content: flex-start; align-items: flex-end">
              <el-button
                type="text"
                icon="el-icon-magic"
                @click="generateAbilityIcon"
                style="background-color: transparent; border: none"
              >
                <svg
                  t="1724402472978"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5972"
                  id="mx_n_1724402472978"
                  width="17"
                  height="17"
                >
                  <path
                    d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                    fill="#4068d4"
                    p-id="5973"
                  ></path>
                  <path
                    d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                    fill="#4068d4"
                    p-id="5974"
                  ></path>
                </svg>
                AI生成
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-for="(domain, index) in lastFormModal.domains"
          :key="domain.key"
          :label="domain.label"
          :prop="'domains.' + index + '.value'"
          :rules="{
            required: true,
            message: `${domain.label}不能为空`,
            trigger: 'change'
          }"
        >
          <el-input v-if="domain.type === 'input'" v-model.trim="domain.value" />
          <el-cascader
            v-if="domain.type === 'ability'"
            :ref="'domainsSelect' + index"
            v-model="domain.value"
            :options="domain.domainsOptions"
            style="width: 100%"
          />
        </el-form-item>
        <!-- <el-form-item label="是否是物联" prop="iot_type">
          <el-radio-group v-model="lastFormModal.iot_type">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->

        <el-form-item
          v-if="!publishAbility"
          label="部署配置"
          prop="engine_service_id"
          :rules="{
            required: true,
            message: `请选择部署配置`,
            trigger: 'change'
          }"
        >
          <el-select
            v-model="lastFormModal.engine_service_id"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option v-for="item in shiliList" :label="item.name" :value="item.id"></el-option>
            <!-- <el-option label="否" value="0"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="模式选择" prop="modeSelection">
          <el-radio-group v-model="lastFormModal.modeSelection">
            <el-radio label="dialogue" :disabled="!enableDialogModal">对话模式</el-radio>
            <el-radio label="task">任务模式</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="lastFormModal.modeSelection === 'dialogue'"
          label="开场白"
          prop="openingStatement"
        >
          <el-input
            v-loading="formVisible.openingStatement"
            v-model="lastFormModal.openingStatement"
            :autosize="{ minRows: 2, maxRows: 6 }"
            type="textarea"
            maxlength="250"
            placeholder="请输入内容"
          />
          <el-button
            type="text"
            icon="el-icon-magic"
            style="
              position: absolute;
              right: 0;
              bottom: 0;
              background-color: transparent;
              border: none;
            "
            @click="aiOpeningStatement"
          >
            <svg
              t="1724402472978"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="5972"
              id="mx_n_1724402472978"
              width="17"
              height="17"
            >
              <path
                d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                fill="#4068d4"
                p-id="5973"
              ></path>
              <path
                d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                fill="#4068d4"
                p-id="5974"
              ></path>
            </svg>
            AI生成
          </el-button>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model.trim="lastFormModal.description"
            type="textarea"
            maxlength="250"
            placeholder="请输入内容"
            resize="none"
          />
        </el-form-item>

        <div class="info-container" @click="toggleOtherInfo">
          <i
            class="el-icon-arrow-right"
            style="margin-left: 22px"
            :class="{ rotate: lastFormModal.isOtherInfoVisible }"
          ></i>
          其他信息
        </div>
        <div v-show="lastFormModal.isOtherInfoVisible">
          <el-form-item label="智能能力描述:" prop="abilityValue">
            <el-input
              v-loading="formVisible.abilityDesc"
              v-model="lastFormModal.abilityValue"
              type="textarea"
              placeholder="请输入智能能力描述及价值信息"
            ></el-input>
            <el-button
              type="text"
              style="
                position: absolute;
                right: 0;
                bottom: 0;
                background-color: transparent;
                border: none;
              "
              @click="abilityDes"
            >
              <svg
                t="1724402472978"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="5972"
                id="mx_n_1724402472978"
                width="17"
                height="17"
              >
                <path
                  d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                  fill="#4068d4"
                  p-id="5973"
                ></path>
                <path
                  d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                  fill="#4068d4"
                  p-id="5974"
                ></path>
              </svg>
              AI生成
            </el-button>
          </el-form-item>
          <el-form-item label="匹配意图:" prop="abilityIntents">
            <el-input
              v-loading="formVisible.abilityIntents"
              v-model="lastFormModal.abilityIntents"
              type="textarea"
              placeholder="请输入支持的使用方式，例如：用户风险感知模型检测到吗？"
            ></el-input>
            <el-button
              type="text"
              style="
                position: absolute;
                right: 0;
                bottom: 0;
                background-color: transparent;
                border: none;
              "
              @click="aiMeMatch"
            >
              <svg
                t="1724402472978"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="5972"
                id="mx_n_1724402472978"
                width="17"
                height="17"
              >
                <path
                  d="M213.376 933.12a122.496 122.496 0 0 1-86.613333-209.066667l554.666666-554.666666a122.496 122.496 0 0 1 173.226667 173.226666l-554.666667 554.666667a121.642667 121.642667 0 0 1-86.613333 35.84z m554.666667-735.573333a58.069333 58.069333 0 0 0-41.344 17.066666l-554.666667 554.666667a57.941333 57.941333 0 0 0-17.066667 41.344 57.344 57.344 0 0 0 17.066667 41.386667 59.008 59.008 0 0 0 82.773333 0l554.666667-554.666667a58.453333 58.453333 0 0 0-41.386667-99.84z"
                  fill="#4068d4"
                  p-id="5973"
                ></path>
                <path
                  d="M745.770667 406.229333l-128-128a32 32 0 0 1 45.269333-45.226666l128 128a32.170667 32.170667 0 0 1 0 45.226666 31.914667 31.914667 0 0 1-45.226667 0zM426.666667 234.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L362.666667 216.746667l-58.026667 17.066666a22.058667 22.058667 0 0 1-5.973333 0.896 20.309333 20.309333 0 0 1-14.933334-6.357333 21.034667 21.034667 0 0 1-5.546666-20.906667l17.024-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 21.589333 21.589333 0 0 1 15.232-6.357333 20.309333 20.309333 0 0 1 5.674666 0.810666l58.069334 17.066667 58.026666-17.066667a22.144 22.144 0 0 1 5.973334-0.810666 21.333333 21.333333 0 0 1 15.061333 6.357333 21.333333 21.333333 0 0 1 5.546667 20.906667l-17.237334 58.069333 17.066667 58.026667a20.736 20.736 0 0 1-5.546667 20.906666 20.010667 20.010667 0 0 1-14.890666 6.4zM362.666667 173.226667a22.186667 22.186667 0 0 1 4.778666 0.64l1.237334 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109333-0.213334A22.442667 22.442667 0 0 1 362.666667 173.226667zM256 490.709333a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L192 472.746667l-58.026667 17.066666a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069333-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810666l58.069333 17.066667 58.026667-17.066667a20.309333 20.309333 0 0 1 5.632-0.810666 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906666 20.010667 20.010667 0 0 1-14.890667 6.4zM192 429.226667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453334a18.645333 18.645333 0 0 1 0-11.946666l7.68-26.453334-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453334a19.2 19.2 0 0 1 0 11.946666l-7.68 26.453334 26.453333-7.68 1.109334-0.213334a22.442667 22.442667 0 0 1 4.821333-0.682666zM896 704.042667a23.893333 23.893333 0 0 1-5.034667-0.64h-0.981333L832 686.08l-58.026667 17.066667a21.845333 21.845333 0 0 1-5.973333 0.896 21.632 21.632 0 0 1-20.565333-27.264l17.109333-58.069334-17.066667-58.069333a20.650667 20.650667 0 0 1 5.546667-20.906667 23.210667 23.210667 0 0 1 15.573333-6.357333 19.029333 19.029333 0 0 1 5.333334 0.810667l58.069333 17.066666 58.026667-17.066666a20.309333 20.309333 0 0 1 5.632-0.810667 21.76 21.76 0 0 1 15.232 6.357333 21.333333 21.333333 0 0 1 5.546666 20.906667l-17.066666 58.069333 17.066666 58.026667a20.736 20.736 0 0 1-5.546666 20.906667 20.010667 20.010667 0 0 1-14.890667 6.4z m-64-61.482667a22.186667 22.186667 0 0 1 4.778667 0.64l1.237333 0.256 26.453333 7.68-7.68-26.453333a18.645333 18.645333 0 0 1 0-11.946667l7.68-26.453333-26.453333 7.68a19.242667 19.242667 0 0 1-11.946667 0l-26.453333-7.68 7.68 26.453333a19.2 19.2 0 0 1 0 11.946667l-7.68 26.453333 26.453333-7.68 1.109334-0.213333a22.442667 22.442667 0 0 1 4.821333-0.682667z"
                  fill="#4068d4"
                  p-id="5974"
                ></path>
              </svg>
              AI生成
            </el-button>
          </el-form-item>
          <el-form-item label="标签关键词:" style="width: 100%">
            <div style="display: flex; justify-content: end">
              <el-select
                ref="tagsSelect"
                v-model="lastFormModal.tag_ids"
                multiple
                filterable
                placeholder="请选择标签"
                :filter-method="handleTagFilter"
                clearable
                style="width: 100%"
                @keyup.native.enter="addBizTag"
                @change="changeTags"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </div>
            <div style="flex: 1">
              <selectItem
                :array-items="
                  lastFormModal.ability_market_tags?.map((ability_market_tags) => {
                    return { key: ability_market_tags.id, label: ability_market_tags.name }
                  })
                "
                :maxLength="10"
              ></selectItem>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <div style="margin-top: 20px; display: flex; justify-content: end">
        <el-button type="primary" :loading="lastLoading" @click="onPublish">确认</el-button>
        <el-button :disabled="lastLoading" @click="cancel2">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      custom-class="last-dialog"
      :visible.sync="dialogVisible"
      title="时序数据"
      :show-close="!lastLoading"
    >
      <div>
        <el-row :gutter="20" style="margin-bottom: 16px">
          <el-col :span="12">设备名称：{{ searchData.equipName }}</el-col>
          <el-col :span="12">设备ID：{{ searchData.equipId }}</el-col>
        </el-row>
        <el-table
          :max-height="300"
          style="width: 100%"
          size="medium"
          :header-cell-style="{ background: '#F6F7FB', color: '#323233' }"
          :data="displayData"
        >
          <el-table-column min-width="170" prop="time" label="时间"></el-table-column>
          <el-table-column
            min-width="170"
            prop="value"
            class-name="no-bor"
            label="值"
          ></el-table-column>
        </el-table>
        <div class="page">
          <el-pagination
            small
            :page-size="dpsData.pageSize"
            :current-page="dpsData.pageNo"
            layout="prev, pager, next"
            :total="dpsData.list.length"
            @current-change="handleDpsCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
      <div style="margin-top: 20px; display: flex; justify-content: end">
        <el-button :disabled="lastLoading" @click="() => (dialogVisible = false)">关闭</el-button>
      </div>
    </el-dialog>
    <devlopModal
      :is-visible="devlopModalVisable"
      :cur-id="curId"
      :dev-person="devPersonInfo"
      @close="handleClose"
    />
  </div>
</template>

<script>
import {
  GetDecision,
  SchemeDetail,
  allEquipList,
  querySequenceData,
  queryAbilityMapping,
  SchemeSaveKnow,
  MarketAbilityPublish,
  CodePublish,
  UpdateSchemeStatus,
  OnCodeTest,
  queryOneEquipDetail,
  queryCodeParams,
  querySchemeDetailById,
  getPublishAbilityDich,
  getExecuteSync,
  getAbilityEngineServiceList,
  querySimulationPlatformEquipmentList,
  querySimulationPlatformEquipmentInfoByCode,
  keywordTag,
  abilityValue,
  abilityIconInterface,
  matchableIntent,
  openingStatement,
  queryTagsMarket,
  addTagMarket,
  bindTagMarket
} from '@/api/planGenerateApi.js'
import panzoom from 'panzoom'
import dayjs from 'dayjs'
import service from '@/axios'
import JsonViewer from 'vue-json-viewer'
import devlopModal from './devlopModal.vue'
import MyEditor from '../mdEditorPreview.vue'
import shardUploaderTool from '@/components/Tools/SegmentUploadZhushou.vue'
import selectItem from '@/views/planGenerate/selectItem.vue'
const chatXKey = {
  dev: '3mAatDrr5DBBTgzcgrbLyjpdw2mwwALs',
  fat: 'uXnSpC7JDP6mNC6SFyAqNG1r45apCJPd',
  production: 'dZl9xUDmcJlRc9eLTm68P7R8qNWRzKM1'
}

export default {
  components: {
    selectItem,
    JsonViewer,
    MyEditor,
    shardUploaderTool,
    devlopModal
  },
  props: {
    agentSenceCode: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    modelParamExtractionStatus: {
      type: Number,
      default: -1
    },
    sqlStatus: {
      type: Number,
      default: -1
    },
    sqlData: {
      type: String,
      default: ''
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    sessionId: {
      type: String,
      default: '1'
    },
    miniFlag: {
      type: Boolean,
      default: false
    },
    developFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const openingStatementValidator = (rule, value, callback) => {
      if (this.lastFormModal.modeSelection === 'dialogue' && value === '') {
        callback(new Error('请输入开场白'))
      } else {
        callback()
      }
    }
    const abilityIconValidator = (rule, value, callback) => {
      if (this.lastFormModal.abilityIcon === '') {
        callback(new Error('请配置能力图标'))
      } else {
        callback()
      }
    }
    return {
      lastFormRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        iot_type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        modeSelection: [{ required: true, message: '请选择模式', trigger: 'change' }],
        openingStatement: [
          { required: true, validator: openingStatementValidator, trigger: 'blur' }
        ],
        abilityIcon: [{ required: true, validator: abilityIconValidator, trigger: 'blur' }],
        abilityValue: [{ required: true, message: '请输入能力价值', trigger: 'blur' }],
        abilityIntents: [{ required: true, message: '请输入能力意图', trigger: 'blur' }]
      },
      fileList: [],
      formVisible: {
        abilityDesc: false,
        abilityIntents: false,
        openingStatement: false,
        uploadBtn: false
      },
      dialogContentLoading: false,
      loading: false,
      loading2: false,
      devlopModalVisable: false,
      saveLoading: false,
      tableData: [],
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      planDetailShow: true,
      rightFullFlag: false,
      taskLoading: false,
      jueceYulanFlag: true,
      treeData: '',
      searchData: {
        time: '',
        equipId: '',
        equipName: '',
        equipList: [],
        globalList: []
      },
      pageNo: 1,
      pageSize: 10,
      dialogVisible2: false, // 发布到集市标志
      toMessage: { content: '', image_key: '', image_path: '' },
      uploadUrl: '',
      uploadParam: {},
      tagList: [],
      allTagList: [],
      enableDialogModal: true,
      lastFormModal: {
        tag_ids: [],
        tag: [],
        ability_market_tags: [],
        name: this.$route.query.name,
        description: '',
        iot_type: 0,
        domains: [],
        engine_service_id: '',
        modeSelection: 'task', // 模式选择
        openingStatement: '', // 开场白
        isOtherInfoVisible: true,
        abilityValue: '', // 能力价值
        abilityIntents: '', //  能力意图
        abilityIcon: '', //  能力发布图标  这是默认的图标 URL
        iconType: 'local',
        keyword: '',
        abilityKeywords: [] // 关键词标签
      },
      lastLoading: false,
      dialogVisible: false, // 时序数据显示
      dpsData: {
        list: [],
        pageNo: 1,
        pageSize: 10
      }, // 时序数据
      tried: false,
      jsonData: {},
      fixFiled: {},
      gridData: [],
      dataThs: [],
      dataThsDatas: [],
      filesList: [],
      newTableData: [],
      schemeInfo: {},
      streamFlag: false,
      devPersonInfo: {},
      curId: '',
      shiliList: [], // 部署配置列表
      publishAbility: false,
      scheme_detail: '', // 方案明细
      showTestFlag: true,
      deviceVal: '',
      deviceOptions: [],
      simulationUrl: '',
      iframeUrlObj: {
        dev: '',
        fat: 'https://web.fanneng.com/graph-view/#/view?id=t000183',
        uat: '',
        production:
          'https://web.fanneng.com/fnfz-editor/#/cfGraph?id=1828714781615132673&m=t000081&t=t000082&tid=66cee2fa355e5e02ae8bff82'
      },
      scene_id: {
        dev: [],
        fat: ['b5201004-ecbe-4d89-9918-d970bdc86863'],
        uat: [],
        production: ['551a72b0-3f06-40ad-be45-27087b186431', 'f6a3b4fe-1b3c-4f5e-b4a7-5bd819551fc4']
      },

      deviceObj: {},
      detailDeviceInfoList: [],
      jsonDataList: []
    }
  },
  computed: {
    displayData() {
      const startIndex = (this.dpsData.pageNo - 1) * this.dpsData.pageSize
      const endIndex = startIndex + this.dpsData.pageSize
      return this.dpsData.list.slice(startIndex, endIndex)
    },
    pickerOptions1() {
      return {
        disabledDate(time) {
          const maxDate = Date.parse(
            dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
          )
          return time.getTime() > maxDate
        }
      }
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        this.treeStatusLast = val
        if (val === 2) {
          this.taskLoading = false
          // this.handleAbilityMapping()
        } else if (val === 0) {
          this.taskLoading = false
        } else if (val === 3) {
          this.taskLoading = false
        }
      },
      immediate: true
    },
    modelParamExtractionStatus: {
      handler(val) {
        this.treeStatusLast = val
        if (val === 2) {
          this.taskLoading = false
          // this.handleAbilityMapping()
        } else if (val === 0) {
          this.taskLoading = false
        } else if (val === 3) {
          this.taskLoading = false
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.codeData = val
      },
      immediate: true
    }
  },
  async created() {},
  beforeDestroy() {},
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    console.log('devedevdee', process.env.VUE_APP_ENV)
    this.simulationUrl = this.getQueryToken(this.iframeUrlObj[process.env.VUE_APP_ENV])
    this.schemeDetailById()
    this.queryShili()
    this.taskStatus = this.$route.query.status
    const nodeMarkmap = document.getElementById('markmap')
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = ''
    }
    const abc = dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.$set(this.searchData, 'time', abc || '')
    // console.log('tabelRef', this.$refs.tabelRef,this.$refs.tabelRef.scrollHeight );
    // this.tableHeight = this.$refs.tabelRef.scrollHeight - 0;
    await this.queryDecision('decision_tree') // 思维树
    await this.handleAbilityMapping() // 数据对齐
    await this.queryPlanDetail()
    await this.searchTags2()
  },
  methods: {
    // 测试仿真数据
    showSimulationTest() {
      console.log('场景信息：', this.schemeInfo)
      return this.scene_id[process.env.VUE_APP_ENV].includes(this.schemeInfo.agent_scene_id)
    },
    // url地址上的token参数
    getQueryToken(url) {
      return this.authSdk?.transformToAuthUrl(url, 'local')
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        // console.log(res, '000');
        if (res.status === 200 && res.data.code === 200) {
          this.publishAbility = res.data.result?.publish_ability
          this.scheme_detail = res.data.result?.text
        }
      })
    },
    // 部署配置列表
    queryShili() {
      getAbilityEngineServiceList().then((res) => {
        console.log('实例', res)
        if (res.status === 200 && res.data?.status === 200) {
          this.shiliList = res.data.data
        } else {
          this.shiliList = []
        }
      })
    },
    // 查看任务进度
    showTask() {
      this.$emit('showTaskModal')
    },
    handleClose(val) {
      if (val) {
        this.schemeDetailById()
      }
      this.devlopModalVisable = false
    },
    developFn() {
      this.devPersonInfo = this.schemeInfo.developer
      this.curId = this.$route.query.id
      this.devlopModalVisable = true
    },
    handleSimulationState() {
      console.log('window.DEVICE_TYPE:', window.DEVICE_TYPE)
      if (window.DEVICE_TYPE !== 'GSB') {
        this.$message.warning('暂无相关设备仿真数据')
        return
      }
      this.showTestFlag = !this.showTestFlag
      if (!this.showTestFlag) {
        this.deviceVal = ''
        this.detailDeviceInfoList = []
        this.handleQuerySimulationPlatformEquipmentList()
        this.leftWidth = '70%'
      } else {
        this.leftWidth = '50%'
      }
      this.jsonData = {}
      this.tried = false
      this.streamFlag = false
    },
    queryEqu() {
      this.loading = true
      this.lastFormModal.domains = []
      getPublishAbilityDich({ scene_id: this.schemeInfo.agent_scene })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data?.result || []
            if (result.length > 0) {
              for (const item of result) {
                const domainItem = {
                  label: item.field_name,
                  key: item.field_code,
                  value: '',
                  type: item.field_val.type || '',
                  domainsOptions: []
                }
                if (item.field_val.type === 'ability') {
                  const res = await getExecuteSync({
                    scene_instance_id: this.$route.query.id,
                    ability_id: item.field_val.ability_id,
                    name: item.field_val.ability_id,
                    goal: ''
                  })
                  if (res.data && res.data.length > 0) {
                    const resultList = res.data
                    if (resultList && resultList.length > 0) {
                      resultList.forEach((item) => {
                        if (item.children && item.children.length > 0) {
                          item.children.forEach((it) => {
                            if (it.children && it.children.length === 0) {
                              delete it.children
                            }
                          })
                        }
                      })
                      domainItem.domainsOptions = resultList
                    }
                  }
                  this.lastFormModal.domains.push(domainItem)
                } else {
                  this.lastFormModal.domains.push(domainItem)
                }
              }
            }
          }
          this.dialogVisible2 = true
        })
        .finally(() => {
          this.loading = false
        })
    },

    uploadScriptCallback(fileData, fileList, index) {
      console.log('文件上传成功过', fileData, index)
      this.filesList[index] = fileData.path
      console.log(this.filesList)
    },
    uploadShardStatusCb(status) {},
    removeFile(fileId, filelist, index) {
      console.log('删除的行', index)
      this.filesList[index] = ''
    },
    handlePublish() {
      this.queryEqu()
      this.abilityTestZhuGe('获取')
      this.handlerDialogModal()
    },
    handlerDialogModal() {
      if (
        this.tableData.length === 3 &&
        this.tableData.filter((item) => item?.param_key === 'ability_id').length > 0 &&
        this.tableData.filter((item) => item?.param_key === 'userMessage').length > 0 &&
        this.tableData.filter((item) => item?.param_key === 'session_id').length > 0
      ) {
        this.enableDialogModal = true
      } else {
        this.enableDialogModal = false
      }
    },
    abilityTestZhuGe(btnName) {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
        this.lastFormModal.abilityIcon = res.data.result.ext_info?.abilityIcon || ''
        this.lastFormModal.iconType = res.data.result.ext_info?.iconType || ''
        this.lastFormModal.abilityValue = res.data.result.ext_info?.abilityValue || ''
        this.lastFormModal.abilityIntents = res.data.result.ext_info?.abilityIntents || ''
        this.lastFormModal.abilityKeywords = res.data.result.ext_info?.abilityKeywords || []
        this.lastFormModal.modeSelection = res.data.result.ext_info?.mode_selection || 'task'
        this.lastFormModal.openingStatement = res.data.result.ext_info?.opening_statement || ''
        this.lastFormModal.ability_market_tags = res.data.result.ability_market_tags
        this.lastFormModal.tag_ids = res.data.result.ability_market_tags.map((item) => item.id)
        this.uploadUrl = res.data.result.ext_info?.abilityIcon
        if (res.data.result.ext_info?.abilityIcon || '' !== '') {
          this.fileList = [
            {
              url: res.data.result.ext_info?.abilityIcon,
              name: res.data.result.ext_info?.abilityIcon.split('/').pop().split('?')[0]
            }
          ]
        }
        this.getAbilityKeywords()
      })
    },
    handleDeviceChange(val) {
      this.handleQuerySimulationPlatformEquipmentInfoByCode(val)
    },
    // 获取仿真平台设备列表
    handleQuerySimulationPlatformEquipmentList() {
      querySimulationPlatformEquipmentList().then((res) => {
        if (res.status === 200 || res.data.code === 200) {
          this.deviceOptions = res.data.data
        }
      })
    },
    // 根据仿真平台设备code获取设备详情
    handleQuerySimulationPlatformEquipmentInfoByCode(val) {
      querySimulationPlatformEquipmentInfoByCode({ deviceList: [val] }).then((res) => {
        if (res.status === 200 || res.data.code === 200) {
          const _res = res.data.data
          this.detailDeviceInfoList = Object.entries(_res.result[val]).map(([key, value]) => ({
            detection_time: parseFloat(key),
            device_id: val,
            ...value
          }))
          this.detailDeviceInfoList.sort((a, b) => a.detection_time - b.detection_time)
          this.detailDeviceInfoList.splice(0, 1)
        }
      })
    },
    async filterEquip(val) {
      if (val) {
        this.loading2 = true
        await allEquipList({ deviceId: val, scheme_id: this.$route.query.id }).then((res) => {
          this.loading2 = false
          if (res.status === 200 && res.data.code === 200) {
            this.searchData.equipList = res.data.result || []
            this.searchData.globalList = res.data.result || []
          }
        })
      } else {
        this.searchData.equipList = this.searchData.globalList
      }
    },
    getSence() {
      this.dpsData.pageNo = 1
      this.dpsData.list = []
      querySequenceData({
        deviceId: this.searchData.equipId,
        detectionTime: dayjs(this.searchData.time).format('YYYY-MM-DD HH:mm:ss')
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result?.dps) {
              this.dpsData.list = Object.keys(res.data.result?.dps).map((item) => {
                return {
                  time: dayjs(item).format('YYYY-MM-DD HH:mm:ss'),
                  value: res.data.result?.dps[item]
                }
              })
              console.log('所有', this.dpsData.list)
            }
          } else {
            this.dpsData.list = []
          }
          this.dialogVisible = true
        }
      })
    },
    getCodeParams() {
      queryCodeParams({ scheme_id: this.$route.query.id, session_id: this.sessionId }).then(
        (res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('测试参数', res.data.result)
            this.newTableData = res.data.result || []
          } else {
            this.newTableData = []
          }
          const temp = []
          const temp2 = {}
          const files = []
          res.data.result?.forEach((item) => {
            temp2[item.param_key] = ''
            if(Object.prototype.toString.call(item.param_value) === '[object Array]' || Object.prototype.toString.call(item.param_value) === '[object Object]') {
                  item.param_value = JSON.stringify(item.param_value,null, 2)
                }
            temp.push({ ...item, param_value: item.param_value || '' })
            files.push('')
          })
          this.filesList = files
          this.tableData = temp
        }
      )
    },
    // 查询所有设备
    getAllEquip() {
      allEquipList({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.searchData.equipList = res.data.result || []
          this.searchData.globalList = res.data.result || []
          if (res.data.result && res.data.result?.length) {
            this.searchData.equipId = res.data.result?.[0].deviceId + '' || ''
            this.searchData.equipName = res.data.result?.[0].device_combination_name || ''
            // console.log('所有设备', this.searchData.equipList);
            this.changeEquip(res.data.result?.[0].deviceId + '')
          } else {
            const temp = []
            const temp2 = {}
            const files = []
            // this.newTableData
            this.newTableData.forEach((item) => {
              // 过滤掉deviceId和detectionTime
              if (item.param_key !== 'deviceId' && item.param_key !== 'detectionTime') {
                temp2[item.param_key] = ''
                if(Object.prototype.toString.call(item.param_value) === '[object Array]' || Object.prototype.toString.call(item.param_value) === '[object Object]') {
                  item.param_value = JSON.stringify(item.param_value,null, 2)
                }
                temp.push({ ...item, param_value: '' })
                files.push('')
              }
            })
            this.filesList = files
            this.tableData = temp
          }
        }
      })
    },
    changeEquip(val) {
      if (val) {
        this.tableData = []
        const filtes = this.searchData.equipList.filter((item) => item.deviceId == val)
        const curRow = filtes[0]
        console.log('当前行', val)
        this.searchData.equipName = curRow.device_combination_name || ''
        this.fixFiled = {}
        queryOneEquipDetail({
          scheme_id: this.$route.query.id,
          detectionTime: this.searchData.time
            ? dayjs(this.searchData.time).format('YYYY-MM-DD HH:mm:ss')
            : '',
          device_type_code: curRow.device_type_code,
          deviceId: val
        }).then((res) => {
          const temp = []
          const temp2 = {}
          const files = []
          // this.newTableData
          this.newTableData.forEach((item) => {
            // 过滤掉deviceId和detectionTime
            if (item.param_key !== 'deviceId' && item.param_key !== 'detectionTime') {
              const filters = res.data?.result.filter(
                (ritem) => ritem.device_attribute_code === item.param_key
              )
              if(Object.prototype.toString.call(item.param_value) === '[object Array]' || Object.prototype.toString.call(item.param_value) === '[object Object]') {
                  item.param_value = JSON.stringify(item.param_value,null, 2)
                }
              if (filters.length) {
                temp2[item.param_key] = filters[0].device_attribute_value
                // item['param_value'] = filters[0].device_attribute_value;
                temp.push({ ...item, param_value: filters[0].device_attribute_value })
              } else {
                temp2[item.param_key] = ''
                temp.push({ ...item, param_value: '' })
              }
              files.push('')
            }
          })
          this.filesList = files
          this.tableData = temp
          console.log('最后的数据--tableData', this.tableData)
          this.fixFiled = temp2
        })
      }
    },
    // 平台测试
    onTest() {
      this.abilityTestZhuGe('测试')
      // 物联场景
      if (this.agentSenceCode === 'device_ops_assistant_scene') {
        const datas = {}
        this.tableData.forEach((item, index) => {
          if (item.param_dataType !== 'File') {
            if (item.param_dataType == 'Boolean' || item.param_dataType == 'bool') {
              console.log('进入这里', item.param_dataType)
              const temp = item.param_value === 'true'
              datas[item.param_key] = temp
            } else if (item.param_dataType == 'Array') {
              try {
                const temp = JSON.parse(item.param_value)
                datas[item.param_key] = temp
              } catch (error) {
                const temp = item.param_value
                datas[item.param_key] = temp
              }
            } else if (item.param_dataType == 'Object') {
              try {
                const temp = JSON.parse(item.param_value)
                datas[item.param_key] = temp
              } catch (error) {
                const temp = item.param_value
                datas[item.param_key] = temp
              }
            } else if (item.param_dataType == 'String') {
              datas[item.param_key] = item.param_value
            } else if (item.param_dataType == 'Number') {
              const temp = Number(item.param_value)
              if (isNaN(temp)) {
                datas[item.param_key] = item.param_value
              } else {
                datas[item.param_key] = temp
              }
            } else {
              if (item.param_value !== '') {
                datas[item.param_key] = item.param_value
              } else {
                console.log('不传')
                // datas[item.name] = item.value
              }
            }
          } else {
            datas[item.param_key] = this.filesList[index]
          }
        })
        const params = {
          scheme_id: this.$route.query.id,
          // 'deviceId': this.searchData.equipId,
          // 'detectionTime': dayjs(this.searchData.time).format('YYYY-MM-DD HH:mm:ss'),
          params: datas
        }
        this.taskLoading = true
        const url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/executev1'
          : process.env.VUE_APP_PLAN_API + '/code/executev1'
        console.log('url', url)
        const userInfo = sessionStorage.getItem('USER_INFO')
          ? JSON.parse(sessionStorage.getItem('USER_INFO'))
          : {}
        this.$axios
          .post(
            url,
            {
              header: {
                tenant_id: userInfo.tenantId || 'str',
                user_id: userInfo.userId || 'str',
                session_id: '1',
                work_space_id: this.$route.query.workspaceId + ''
              },
              data: params || {}
            },
            {
              responseType: 'stream',
              timeout: 2000 * 120,
              baseURL: process.env.VUE_APP_PLAN_API,
              headers: {
                affinitycode: userInfo.userId || ''
              },
              onDownloadProgress: (event) => {
                this.taskLoading = false
                this.streamFlag = true
                const xhr = event.target
                const { responseText } = xhr

                this.$nextTick(() => {
                  console.log('----流----', responseText)
                  this.streamFlag = true
                  this.jsonData = responseText
                })
              },
              onError: function (error) {
                // 处理流错误
                console.error(error)
                this.taskLoading = false
              }
            }
          )
          .then(async (res) => {
            // 关闭数据流
            console.log('数据流', res)
            this.taskLoading = false
            if (res.status === 200 && res.data.code === 200) {
              this.streamFlag = false
              this.jsonData = res.data.result.resp || {}
              await UpdateSchemeStatus({
                scheme_id: this.$route.query.id,
                scheme_status: 'ability_generate_and_debugging'
              })
              this.tried = true
            } else {
              if (res.status === 200) {
                this.$nextTick(() => {
                  const newStr = res.data.replace(/[\r\n]/g, '')
                  console.log('----流----', res.data, newStr)
                  this.streamFlag = true
                  this.jsonData = newStr
                })
                await UpdateSchemeStatus({
                  scheme_id: this.$route.query.id,
                  scheme_status: 'ability_generate_and_debugging'
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            }
          })
          .catch(() => {
            this.taskLoading = false
          })
      } else {
        const datas = {}
        this.tableData.forEach((item, index) => {
          if (item.param_dataType !== 'File') {
            if (item.param_dataType == 'Boolean' || item.param_dataType == 'bool') {
              console.log('进入这里', item.param_dataType)
              const temp = item.param_value === 'true'
              datas[item.param_key] = temp
            } else if (item.param_dataType == 'Array') {
              try {
                const temp = JSON.parse(item.param_value)
                datas[item.param_key] = temp
              } catch (error) {
                const temp = item.param_value
                datas[item.param_key] = temp
              }
            } else if (item.param_dataType == 'Object') {
              try {
                const temp = JSON.parse(item.param_value)
                datas[item.param_key] = temp
              } catch (error) {
                const temp = item.param_value
                datas[item.param_key] = temp
              }
            } else if (item.param_dataType == 'String') {
              datas[item.param_key] = item.param_value
            } else if (item.param_dataType == 'Number') {
              const temp = Number(item.param_value)
              if (isNaN(temp)) {
                datas[item.param_key] = item.param_value
              } else {
                datas[item.param_key] = temp
              }
            } else {
              if (item.param_value !== '') {
                datas[item.param_key] = item.param_value
              } else {
                console.log('不传')
                // datas[item.name] = item.value
              }
            }
          } else {
            datas[item.param_key] = this.filesList[index]
          }
        })
        const params = {
          scheme_id: this.$route.query.id,
          params: datas
        }
        console.log('测试数据', datas)
        this.taskLoading = true
        const url = process.env.VUE_APP_PLAN_API.startsWith('/')
          ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute'
          : process.env.VUE_APP_PLAN_API + '/code/execute'
        console.log('url', url)
        const userInfo = sessionStorage.getItem('USER_INFO')
          ? JSON.parse(sessionStorage.getItem('USER_INFO'))
          : {}
        this.$axios
          .post(
            url,
            {
              header: {
                tenant_id: userInfo.tenantId || 'str',
                user_id: userInfo.userId || 'str',
                session_id: '1',
                work_space_id: this.$route.query.workspaceId + ''
              },
              data: params || {}
            },
            {
              timeout: 2000 * 120,
              responseType: 'stream',
              baseURL: process.env.VUE_APP_PLAN_API,
              headers: {
                affinitycode: userInfo.userId || ''
              },
              onDownloadProgress: (event) => {
                this.taskLoading = false
                this.streamFlag = true
                const xhr = event.target
                const { responseText } = xhr
                this.$nextTick(() => {
                  console.log('----流----', responseText)
                  this.streamFlag = true
                  this.jsonData = responseText
                })
                // this.$nextTick(() => {
                //   console.log('----流----',responseText)
                // })
              },
              onError: function (error) {
                // 处理流错误
                console.error(error)
                this.taskLoading = false
              }
            }
          )
          .then(async (res) => {
            // 关闭数据流
            console.log('数据流', res)
            this.taskLoading = false
            if (res.status === 200 && res.data.code === 200) {
              this.streamFlag = false
              this.jsonData = res.data.result.resp || {}
              await UpdateSchemeStatus({
                scheme_id: this.$route.query.id,
                scheme_status: 'ability_generate_and_debugging'
              })
              this.tried = true
            } else {
              if (res.status === 200) {
                this.$nextTick(() => {
                  const newStr = res.data.replace(/[\r\n]/g, '')
                  console.log('----流----', res.data, newStr)
                  this.streamFlag = true
                  this.jsonData = newStr
                })
                await UpdateSchemeStatus({
                  scheme_id: this.$route.query.id,
                  scheme_status: 'ability_generate_and_debugging'
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                })
              }
            }
          })
          .catch(() => {
            this.taskLoading = false
          })
      }
    },
    // 仿真测试
    async onSimulationTest() {
      // 先校验原始的params参数是否都在新的参数里面
      if (!this.checkKeysExist(this.newTableData, this.detailDeviceInfoList)) {
        this.$message.warning('能力入参与设备测点字段不匹配')
        // return;
      }
      this.jsonDataList = []
      for (const item of this.detailDeviceInfoList) {
        await this.callApi({
          params: { ...item, detection_time: item.detection_time.toFixed(2) },
          scheme_id: this.$route.query.id
        })
      }
    },
    async callApi(params) {
      this.taskLoading = true
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/code/execute'
        : process.env.VUE_APP_PLAN_API + '/code/execute'
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {}
      return this.$axios
        .post(
          url,
          {
            header: {
              tenant_id: userInfo.tenantId || 'str',
              user_id: userInfo.userId || 'str',
              session_id: '1',
              work_space_id: this.$route.query.workspaceId + ''
            },
            data: params || {}
          },
          {
            timeout: 2000 * 120,
            responseType: 'stream',
            baseURL: process.env.VUE_APP_PLAN_API,
            headers: {
              affinitycode: userInfo.userId || ''
            },
            onDownloadProgress: (event) => {
              this.taskLoading = false
              this.streamFlag = true
              const xhr = event.target
              const { responseText } = xhr
              this.$nextTick(() => {
                console.log('----流----', responseText)
                this.streamFlag = true
                // this.jsonDataList.push(responseText);
              })
            },
            onError: function (error) {
              console.error(error)
              this.taskLoading = false
            }
          }
        )
        .then(async (res) => {
          console.log('数据流', res)
          this.taskLoading = false
          if (res.status === 200 && res.data.code === 200) {
            this.streamFlag = false
            const jsonData = {
              request: params,
              response: res.data.result.resp
            }
            this.jsonDataList.push(jsonData || {})
            await UpdateSchemeStatus({
              scheme_id: this.$route.query.id,
              scheme_status: 'ability_generate_and_debugging'
            })
            this.tried = true
          } else {
            if (res.status === 200) {
              this.$nextTick(() => {
                const newStr = res.data.replace(/[\r\n]/g, '')
                console.log('----流----', res.data, newStr)
                this.streamFlag = true
                this.jsonDataList.push(newStr)
              })
              await UpdateSchemeStatus({
                scheme_id: this.$route.query.id,
                scheme_status: 'ability_generate_and_debugging'
              })
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              })
            }
          }
        })
        .catch(() => {
          this.taskLoading = false
        })
    },
    // 比较key
    checkKeysExist(A, B) {
      const keys = A.map((item) => item.param_key)
      for (let i = 0; i < B.length; i++) {
        const obj = B[i]
        const objKeys = Object.keys(obj)
        let flag = true
        for (let j = 0; j < keys.length; j++) {
          if (!objKeys.includes(keys[j])) {
            flag = false
            break
          }
        }
        if (flag) {
          continue
        } else {
          return false
        }
      }
      return true
    },
    handleAbilityMapping() {
      this.loading = true
      console.log('请求')
      queryAbilityMapping({ scheme_id: this.$route.query.id })
        .then(async (res) => {
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.sqlLastData = res.data.result?.sql
            this.codeProcess = res.data.result.sub_content
            this.dataStatus = res.data.result.ability_status
            const status = res.data.result.ability_status
            const configData = res.data.result?.config || {}
            console.log(configData, '111')
            // this.dataThs = configData.content?.table_map?.header || {};
            // this.dataThDatas = Object.keys(configData.content?.table_map.header||{}).map(item => {return {name: configData.content?.table_map?.header[item], field: item}})
            const temp = configData?.data || []
            this.gridData = temp
            this.tableData = []
            await this.getCodeParams()
            this.loading = false
          } else {
            this.loading = false
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((_err) => {
          this.loading = false
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 查询思维树
    queryDecision() {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_tree'
      }
      this.loading = true
      GetDecision(params)
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$emit('handleUpdateTreeData', res.data.result?.decision_making_content || '')
            this.treeData = res.data.result?.decision_making_content || ''
            this.treeDataProcess = res.data.result?.sub_content || ''
            this.loading = false
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 分页切换
    handleCurrentChange(val) {
      this.pageNo = val
    },
    handleDpsCurrentChange(val) {
      this.dpsData.pageNo = val
    },
    async schemeDetailById() {
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('详情', res.data.result)
            this.schemeInfo = res.data.result || { ...this.$route.query }
            this.lastFormModal.name = res.data.result?.name
          }
        })
        .catch((_err) => {
          this.$message({
            type: 'error',
            message: _err.data?.msg || '接口异常!'
          })
        })
    },
    // 确定发布
    onPublish() {
      console.log('发布到集市')
      this.$refs.lastFormRef.validate(async (validate) => {
        if (validate) {
          this.lastLoading = true
          const temp = []
          this.newTableData.forEach((item) => {
            if (item.param_key === 'deviceId' || item.param_key === 'detectionTime') {
              temp.push(item)
            }
          })
          this.tableData.forEach((item, index) => {
            if (item.param_dataType !== 'File') {
              if (item.param_dataType == 'Boolean' || item.param_dataType == 'bool') {
                temp.push({ ...item, param_value: item.param_value === 'true' })
              } else if (item.param_dataType == 'Array') {
                try {
                  const tempjson = JSON.parse(item.param_value)
                  temp.push({ ...item, param_value: tempjson })
                } catch (error) {
                  temp.push(item)
                }
              } else if (item.param_dataType == 'Object') {
                try {
                  const tempjson = JSON.parse(item.param_value)
                  temp.push({ ...item, param_value: tempjson })
                } catch (error) {
                  temp.push(item)
                }
              } else {
                if (item.param_value !== '') {
                  const tempvalue = Number(item.param_value)
                  if (isNaN(temp)) {
                    temp.push({ ...item })
                  } else {
                    temp.push({ ...item, param_value: tempvalue })
                  }
                } else {
                  temp.push({ ...item })
                }
              }
            } else {
              temp.push({ ...item, param_value: this.filesList[index] })
            }
          })
          const outputFields = this.lastFormModal.domains.map((field) => {
            const obj = {}
            obj.field_name = field.label
            obj.field_code = field.key
            if (field.type === 'input') {
              obj.field_val = { value: field.value, label: field.value }
            } else {
              if (field.value && field.value.length === 1) {
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(field.value[0])
                )
                obj.field_val = {
                  value: selectedOptions.value,
                  label: selectedOptions.label,
                  parentValue: selectedOptions.parentValue
                }
              } else if (field.value && field.value.length === 2) {
                const selectedValues = field.value
                const selectedOptions = field.domainsOptions.find((option) =>
                  option.value.includes(selectedValues[0])
                )
                const selectedSubOptions = selectedOptions?.children?.find(
                  (child) => child.value === selectedValues[1]
                )
                obj.field_val = {
                  value: selectedSubOptions.value,
                  label: selectedSubOptions.label,
                  parentValue: selectedSubOptions.parentValue
                }
              }
            }
            return obj
          })
          console.log(outputFields)
          const filterdd = this.shiliList.filter(
            (item) => item.id === this.lastFormModal.engine_service_id
          )
          const all = [
            SchemeSaveKnow({
              scheme_id: this.$route.query.id,
              op_type: 'publish',
              iot_type: Number(this.lastFormModal.iot_type),
              scheme_detail_name: this.schemeInfo.name || this.schemeInfo.scheme_detail_name || ''
            }),
            MarketAbilityPublish({
              ext_info: {
                abilityIcon: this.lastFormModal.abilityIcon,
                iconType: this.lastFormModal.iconType,
                abilityValue: this.lastFormModal.abilityValue,
                abilityIntents: this.lastFormModal.abilityIntents,
                abilityKeywords: this.lastFormModal.abilityKeywords,
                mode_selection: this.lastFormModal.modeSelection,
                opening_statement: this.lastFormModal.openingStatement
              },
              scheme_id: Number(this.$route.query.id),
              description: this.lastFormModal.description,
              iot_type: Number(this.lastFormModal.iot_type),
              code_params: temp,
              ext_data_info: outputFields,
              engine_service_id: !this.publishAbility ? this.lastFormModal.engine_service_id : null,
              engine_request_url_prefix: !this.publishAbility
                ? filterdd.length
                  ? filterdd[0].requestUrlPrefix
                  : ''
                : null,
              engine_service_name: !this.publishAbility
                ? filterdd.length
                  ? filterdd[0].name
                  : ''
                : null,
              tag_ids: this.lastFormModal.tag_ids
            })
          ]
          try {
            await Promise.all(all)
            const results1 = await CodePublish({ scheme_id: this.$route.query.id })
            if (results1.data.result) {
              this.abilityTestZhuGe('发布')
              this.$message({
                type: 'success',
                message: '发布成功',
                duration: 1500,
                onClose: () => {
                  this.lastLoading = false
                }
              })
              this.lastLoading = false
              // 向外层页面传递消息
              console.log('向上发布消息', { success: true, result: results1.data.result })
              window.parent.postMessage(
                JSON.stringify({ success: true, result: results1?.data?.result }),
                '*'
              )
              // 发布成功之后不再跳转页面
              this.dialogVisible2 = false
              // this.$router.push({ name: 'targetList' })
            } else {
              this.$message.error(results1.data?.message || '发布失败')
              this.lastLoading = false
            }
          } catch (e) {
            this.$message.error(e.message || e)
            this.lastLoading = false
          }
        }
      })
    },
    // 关闭发布窗口
    cancel2() {
      this.dialogVisible2 = false
      this.lastFormModal.description = ''
      this.lastFormModal.name = this.schemeInfo.name
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    // 发布
    async bushuFun() {
      console.log('发布')
    },
    // 测试方法
    async testFun() {
      console.log('测试')
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
    },
    handleCancel() {
      this.$router.push({
        path: '/planGenerate/index',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      })
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name
      })
    },

    changeViews(val, type) {
      this.$emit('updateStep', val)
      this.abilityTestZhuGe('上一步')
    },
    // 两栏布局拖拽
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = !this.showTestFlag ? '70%' : '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = !this.showTestFlag ? '70%' : '50%'
        this.rightWidth = '100%'
      }
    },
    toggleOtherInfo() {
      console.log('this.lastFormModal.isOtherInfoVisible', this.lastFormModal.isOtherInfoVisible)
      this.lastFormModal.isOtherInfoVisible = !this.lastFormModal.isOtherInfoVisible
    },
    // AI 生成开场白
    async aiOpeningStatement() {
      this.formVisible.openingStatement = true
      const param = {
        query: this.scheme_detail
      }
      const res = await openingStatement(param)
      if (res.status === 200 || res.data.code === 200) {
        this.lastFormModal.openingStatement = res.data.result.answer
        console.log('生成开场白', res)
      }
      this.formVisible.openingStatement = false
    },
    // Ai 生成能力描述和价值
    async abilityDes() {
      this.formVisible.abilityDesc = true
      const param = {
        query: this.scheme_detail
      }
      const res = await abilityValue(param)
      console.log('能力价值', res)
      if (res.status === 200 || res.data.code === 200) {
        this.lastFormModal.abilityValue = res.data.result.answer
      }
      this.formVisible.abilityDesc = false
    },
    // AI匹配意图
    async aiMeMatch() {
      this.formVisible.abilityIntents = true
      const param = {
        query: this.scheme_detail
      }
      const res = await matchableIntent(param)
      if (res.status === 200 || res.data.code === 200) {
        this.lastFormModal.abilityIntents = res.data.result.answer
        console.log('匹配意图接口', res)
      }
      this.formVisible.abilityIntents = false
    },
    // AI生成能力图标
    async generateAbilityIcon() {
      this.formVisible.uploadBtn = true
      const schemeId = Number(this.$route.query.id)
      abilityIconInterface({ scheme_id: schemeId })
        .then((res) => {
          if (res.status === 200 && res.data.code === 200) {
            const result = res.data.result
            this.lastFormModal.abilityIcon = result?.icon_url
            this.lastFormModal.iconType = 'ai'
            console.log('图标地址：', this.lastFormModal.abilityIcon)
            this.fileList = [{ url: result?.icon_url, name: result?.icon_url }]
          }
        })
        .finally((res) => {
          this.formVisible.uploadBtn = false
        })
    },
    // 关键字标签
    async getAbilityKeywords() {
      console.log('----', this.lastFormModal.tag_ids.length)
      console.log('关键字标签', this.lastFormModal.tag_ids.length > 0)
      if (this.lastFormModal.tag_ids.length > 0) {
        return
      }
      const param = {
        query: this.scheme_detail
      }
      await keywordTag(param)
        .then(async (res) => {
          if (res.status === 200 || res.data.code === 200) {
            const tags = []
            try {
              this.lastFormModal.abilityKeywords = JSON.parse(res.data.result.answer)
              for (const name of this.lastFormModal.abilityKeywords) {
                const res = await addTagMarket({ name: name })
                if (res.data) {
                  tags.push({ id: res.data, name: name })
                }
              }
              await this.handleTagFilter('')
              this.lastFormModal.tag_ids = tags.map((item) => item.id)
              this.lastFormModal.ability_market_tags = tags
            } catch (error) {
              this.lastFormModal.abilityKeywords = []
            }
          }
        })
        .finally((res) => {})
    },

    async beforeUpload(file) {
      try {
        const res = await this.$axios.post(this.baseUrl + '/obsfs/commonFile/generateSign', {
          fileType: this.$fileUtil.getFileSuffix(file.name)
        })
        if (res.data.status === 200) {
          this.uploadUrl = res.data.data.obsUrl
          this.toMessage.image_key = res.data.data.key
          this.uploadParam = {
            key: res.data.data.key,
            accessKeyId: res.data.data.accessKeyId,
            signature: res.data.data.signature,
            policy: res.data.data.policy
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error('获取签名出错！')
      }
    },
    uploadExceed() {
      this.$message.warning('仅能上传一张图片')
    },
    modelUploadSuccess(response, file) {
      this.uploadStatus = file.status
      if (this.uploadStatus === 'success') {
        const fileName = this.$fileUtil.getFileName(file.raw.name)
        const fileSize = file.raw.size / 1024
        const fileType = this.$fileUtil.getFileSuffixWithSpot(file.raw.name)
        const fileKey = this.uploadParam.key
        this.toMessage.image_key = fileKey
        this.$axios
          .post(this.baseUrl + '/file/add', {
            fileKey: fileKey,
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            storagePlatform: 'Obs'
          })
          .then((res) => {
            if (res.data.status === 200) {
              this.toMessage.image_path = res.data.data.path
              this.lastFormModal.abilityIcon = res.data.data.path
              this.lastFormModal.iconType = 'local'
            }
          })
      } else {
        this.$message.warning(`图片上传状态为:${this.uploadStatus}`)
      }
    },
    clearImage() {
      this.$confirm('此操作将删除图片，是否继续?', '删除图片', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$refs.uploadBtn.clearFiles()
          this.toMessage.image_path = ''
          this.toMessage.image_key = ''
          this.lastFormModal.abilityIcon = ''
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async searchTags(keyword) {
      await queryTagsMarket({
        keyword
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data
          if (keyword === '') {
            this.allTagList = res.data
          }
        } else {
          this.tagList = []
        }
      })
    },
    async addBizTag() {
      if (this.tagKeyword !== '') {
        await addTagMarket({ name: this.tagKeyword })
        await this.searchTags('')
      }
    },
    async handleTagFilter(keyword) {
      this.tagKeyword = keyword
      await this.searchTags(keyword)
    },
    async changeTags(tagIds) {
      const tags = []
      tagIds.forEach((tagId) => {
        const filters = this.allTagList.filter((item) => item.id === tagId)
        if (filters.length > 0) {
          tags.push({ id: tagId, name: filters[0].name })
        }
      })
      this.lastFormModal.ability_market_tags = tags
    },
    searchTags2() {
      queryTagsMarket({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          console.log('数据', res.data)
          this.tagList = res.data
          this.allTagList = res.data
          const temp = []
          console.log('变价回显', this.lastFormModal.ability_market_tags)
          this.lastFormModal.ability_market_tags?.forEach((titem) => {
            const filter = res.data.filter((item) => item.id === titem.id)
            if (filter.length) {
              temp.push(titem.id)
            }
          })
          this.lastFormModal.tag_ids = temp
        } else {
          this.tagList = []
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-icon {
  font-size: 20px;
  color: #999;
}
.info-container {
  width: 100%;
  margin-bottom: 30px;
  background-color: #f0f0f0; /* 浅灰色 */
  padding: 10px;
  cursor: pointer;
}
.rotate {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}
:deep(.el-button--mini) {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
:deep(.upload-demo) {
  flex-direction: column;
}
:deep(.el-upload) {
  display: inline-block;
}
:deep(.el-upload--picture-card) {
  height: 60px;
  width: 60px;
  line-height: 65px;
}
:deep(.el-upload-list__item) {
  height: 60px;
  width: 60px;
  border-radius: 0;
  border: none;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 40px;
}
:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
}
:deep(.el-button:not(.el-button--text), .el-button.el-button--primary) {
  line-height: 20px !important;
}
:deep(.el-date-editor--datetime .el-input__prefix) {
  left: calc(100% - 30px);
}
:deep(.el-input__icon) {
  line-height: 30px;
}
:deep(.el-date-editor--datetime .el-input__suffix) {
  right: 25px;
}
:deep(.el-input--prefix .el-input__inner) {
  padding-left: 10px;
}
:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}
.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }
    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }
    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }
    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }
    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }
}
.page {
  height: 30px;
  justify-content: flex-end;
  display: flex;
  margin-top: 10px;
  ::v-deep .el-pagination:not(.new-paper) button {
    width: 30px;
  }
}
.el-pagination {
  height: 20%;
}
.test-result {
  // border-top: 1px solid #EBECF0;
  margin-top: 16px;
  // padding-top: 16px;
  margin-bottom: 36px;
  .test-result-warn {
    margin-top: 2px;
    font-size: 12px;
    color: red;
  }
}
.searchFilter {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  .demonstration {
    color: #646566;
    word-break: keep-all;
    margin-right: 4px;
  }
}
.chatContainerTest {
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  max-height: calc(100vh - 100px);
  position: relative;
  &.chatContainerTestFrame {
    height: calc(100vh - 104px);
    max-height: calc(100vh - 104px);
  }
  .optFooter {
    z-index: 3;
    height: 54px;
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 20px;
    min-height: 54px;
  }

  .containerBox2 {
    display: flex;
    flex-direction: row;
    height: calc(100% - 54px);
    max-height: calc(100% - 54px);
    overflow-y: hidden;
    position: relative;
    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;
      &:hover {
        background: #3455ad;
      }
      &:active {
        background: #264480;
      }
      img {
        width: 12px;
        height: auto;
      }
    }
    .containerCard {
      overflow-y: hidden;
      overflow-x: hidden;
      margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      background-color: #fff;
      margin-left: 16px;
      height: calc(100% - 16px);
      max-height: calc(100% - 16px);
      &.containerCardFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        max-height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        background: #fff;
        .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }
        .optContentBox {
          width: 100%;
          min-height: calc(100vh - 150px) !important;
        }
      }
      .optContentBox {
        width: 100%;
        min-height: calc(100vh - 330px);
        display: flex;
        &.optContentBoxMini {
          min-height: calc(100vh - 380px);
        }
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 330px);
        max-height: calc(100vh - 330px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        &.optScrollMini {
          height: calc(100vh - 380px);
          max-height: calc(100vh - 380px);
        }
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .optContent {
        height: 100%;

        // max-height: calc(100% - 60px);
        overflow-y: hidden;
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
      .chatHeader {
        font-size: 14px;
        color: #323233;
        line-height: 24px;
        font-weight: bold;
        background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
    }
    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      height: calc(100% - 16px);
      max-height: calc(100% - 16px);
      overflow-y: hidden;
      margin-top: 16px;
      position: relative;
      &.chatRightFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 102px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        .optScroll {
          height: calc(100vh - 140px) !important;
          max-height: calc(100vh - 140px) !important;
        }
        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }
        .optContentBox {
          height: calc(100vh - 180px) !important;
        }
      }
      .optContentBox {
        height: calc(100vh - 340px);
        width: 100%;
        position: relative;
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 320px);
        max-height: calc(100vh - 320px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        &.optScrollMini {
          height: calc(100vh - 380px);
          max-height: calc(100vh - 380px);
        }
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
        .table-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          .duiqi-box {
            height: 50%;
            overflow: hidden;
            padding-bottom: 30px;
            .title {
              margin-bottom: 16px;
            }
            .transition-box {
              table {
                height: 100%;
              }
            }
            .page {
              height: 30px;
              justify-content: flex-end;
              display: flex;
              margin-top: 10px;
              ::v-deep .el-pagination:not(.new-paper) button {
                width: 30px;
              }
            }
            .el-pagination {
              height: 20%;
            }
          }
        }
      }
      .optContent {
        max-height: calc(100% - 10px);
        overflow-y: hidden;
        &.optContentMini {
          max-height: calc(100% - 60px);
        }
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }
  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:hover {
      background: #e0e6ff;
      .process-icon {
        color: #3455ad !important;
      }
    }
    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;
      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }
    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;
      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }
    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;
      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }
  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    line-height: 20px !important;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button.el-button--primary {
    line-height: 20px;
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    line-height: 20px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
}
.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}
::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
.mytest {
  font-size: 14px;
  .el-button--text {
    display: none !important;
  }
}
.top {
  height: 50%;
  overflow-y: scroll;
}
.top-simulation {
  height: 20%;
  border-bottom: 1px solid #ebecf0;
}
.bottom-simulation {
  height: 100% !important;
}
.bottom {
  margin-top: 10px;
  height: 50%;
  overflow-y: scroll;
}
.font-style {
  font-weight: 700;
  color: #323233;
}
</style>
