<template>
  <div id="chatContainer" :class="
      $store.state.planGenerate.isIframeHide ? 'chatContainer chatContainerFrame' : 'chatContainer'
    ">
    <!-- {{editVisible}}2222222222222222222{{ $store.state.operations.editVisible}} -->
    <!-- <div class="headerBox">
      <div class="headerStep" :style="{ width: '100%' }">
        <el-steps :active="activeStep" finish-status="success" simple class="myStep">
          <el-step title="方案明细">
            <img slot="icon" :src="
                activeStep !== 0
                  ? require('@/assets/images/icon/1_icon.png')
                  : require('@/assets/images/icon/1_icon_success.png')
              " class="empty-space" />
          </el-step>
          <el-step title="智能能力测试与迭代">
            <img slot="icon" :src="
                activeStep !== 1
                  ? require('@/assets/images/icon/2_icon.png')
                  : require('@/assets/images/icon/2_icon_success.png')
              " class="empty-space" />
          </el-step>
        </el-steps>
        <div :class="progressData === 0 ? ' myProgress progressEmpty' : 'myProgress'">
          <el-progress :text-inside="true" :stroke-width="12" :percentage="progressData"></el-progress>
        </div>
      </div>
    </div>
    <div v-if="miniFlag" class="stepBox">
      <div class="stepWrap">
        <div style="font-weight: bold; margin-right: 8px">任务进行中：</div>
        <div style="width: 70%">
          <stepFlow :flow-data="flowData" />
        </div>
      </div>
      <el-tooltip class="item" effect="dark" content="最大化" placement="top">
        <div @click="showTaskModal">
          <img class="full" src="@/assets/images/planGenerater/full.png" />
        </div>
      </el-tooltip>
    </div> -->
    <planChat v-if="activeStep * 1 === 0"
      :getNextTask="getNextTask"
      :isReadOnly="isReadOnly"
      :queryTaskList="queryTaskList"
      :queryBatchTasksResult="queryBatchTasksResult"
      :kickLoading="kickLoading" :isOccupied="isOccupied" :handleUpdateKickedLoading="handleUpdateKickedLoading"
      :startPolling="startPolling" :hasChatingName="hasChatingName" :process-content="processContent"
      :process-running-content="processRunningContent"
      :eventSource="eventSource" :schemeInfo="schemeInfo" :replaceWriteFlag="replaceWriteFlag"
      :insert-write-flag="insertWriteFlag" :history-chat="historyChat" :history-chat2="historyChat2" :session-id="sessionId"
      :usingSession_id="usingSession_id" ref="planChatRef" :flow-data="flowData"
      :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
      :has-chating-name="hasChatingName" :agent-error="agentError" :plan-data-val="planData"
      :plan-process-val="optionDataProcess" :plan-status="planStatus" :tree-status="treeStatus"
      :tree-data-val="treeData" :tree-process-val="treeDataProcess" :is-expert="isExpert" :mini-flag="miniFlag"
      :change-run-task-status="runTaskStatus" :system-messages="systemMessages" :emergentScene="emergentScene"
      @updateSystemMessage="handleUpdateSystemMessage" @queryDetailTrigger="queryDetail"
      @updateTreeStatus="handleTreeStatusUpdate" @processTreeDetail="handleProcessTreeDetail"
      @updateSessionId="handleSessionIdUpdate" @update-scheme-info="handleSchemeInfoUpdate"
      @systemMessage="handleSystemMessage" @executeQueryTask="queryTask" @updateSendMsg="handleSendMsg"
      @updateStep="handleUpdateStep" @handleReStart="handleReStart" @showTaskModal="showTaskModal"
      @handleUpdateScheme="handleUpdateScheme" @handleUpdateTreeData="handleUpdateTreeData"
      @updateDeviceId="updateDeviceId" @initFlowData="handleInitFlowData"
      @handleMessage="handleMessage"
      ></planChat>
    <template v-if="activeStep * 1 === 1">
      <!-- <div v-if="schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
      " class="containerBox">
        <zhushouChatBi :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
          :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
          :tree-process-val="abilityDataProcess" :tree-status="treeStatus" @updateStep="handleUpdateStep"
          @updateCodeGenerate="handleCodeGenerate"></zhushouChatBi>
      </div> -->
      <!-- <HqwAbilityTest v-if="schemeInfo.agent_scene_code === 'operational_optimization_scene'"
        :activeStep="activeStep" :flow-data="flowData" :agent-sence-code="schemeInfo.agent_scene_code"
        :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-status="treeStatus"
        :mini-flag="miniFlag" @showTaskModal="showTaskModal" @updateStep="handleUpdateStep"
        @handleUpdateTreeData="handleUpdateTreeData"></HqwAbilityTest> -->
      <!-- <div v-else-if="schemeInfo.agent_scene_code === 'rule_generation_scene'" class="containerBox">
        <zhushouChatRuleTest :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
          :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
          :tree-status="treeStatus" :session-id="sessionId" @updateStep="handleUpdateStep" />
      </div> -->
      <!-- <div v-else-if="schemeInfo.agent_scene_code === 'device_ops_assistant_scene-v1'" class="containerBox">
        <zhushouChatForm :activeStep="activeStep" :agent-sence-code="schemeInfo.agent_scene_code"
          :agent-scene="schemeInfo.agent_scene" :has-chating-name="hasChatingName" :tree-data-val="treeData"
          :tree-status="treeStatus" @updateStep="handleUpdateStep" @updateGenerate="handleGenerate" />
      </div> -->
      <zhushouChatTest :activeStep="activeStep" :plan-data-val="planData"
        :agent-sence-code="schemeInfo.agent_scene_code" :agent-scene="schemeInfo.agent_scene"
        :has-chating-name="hasChatingName" :tree-data-val="treeData" :tree-status="treeStatus" :session-id="sessionId"
        :publish-ability="detailContent.publish_ability" @updateStep="handleUpdateStep" :CodeTestData="CodeTestData" />
    </template>
    <RunTask v-if="taskModalVisable && flowData.length > 0" ref="RunTaskRef"
      @e-updateAllsuccess="handleUpdateAllsuccess" :queryBatchTasksResult="queryBatchTasksResult" :formData="formData"
      :schemeStatus="schemeStatus" :schemeInfo="schemeInfo" :setModelData="setModelData" :optimizeData="optimizeData"
      :schemeOptimizeProcessData="schemeOptimizeProcessData" :mathModelGenerateStatus="mathModelGenerateStatus"
      :modelingInfoStructureStatus="modelingInfoStructureStatus"
      :modelParamExtractionStatus="modelParamExtractionStatus" :is-visible="taskModalVisable" :session-id="sessionId"
      :flow-data="flowData" :run-stream-list="runStreamList" :all-success="allSuccess" :template-id="templateId"
      :data-align-data-status="dataAlignDataStatus" :data-align-data="dataAlignData" :process-status="processStatus"
      :code-data="codeData" :code-process-data="codeProcessData" :code-data-status="codeDataStatus" :planStatusNewBuShu="planStatusNewBuShu"
      :update-develop-flag="updateDevelopFlag" :dev-person="schemeInfo.developer" :code-analysis-data="codeAnalysisData" :planStatusNewTest="planStatusNewTest"
      :code-analysis-process-data="codeAnalysisProcessData" :code-analysis-data-status="codeAnalysisDataStatus"
      :tree-data-val="treeData" :tree-process-val="treeDataProcess" :tree-status="treeStatus"
      :align-process-data="alignProcessData" :get-task-by-run-code="getTaskByRunCode"
      :update-task-status="updateTaskStatus" :get-tasks-before="getTasksBefore"
      :update-task-statuses-before="updateTaskStatusesBefore" :get-tasks-after="getTasksAfter"
      :update-task-statuses-after="updateTaskStatusesAfter" :update-previous-task-status="updatePreviousTaskStatus"
      :update-next-task-status="updateNextTaskStatus" :get-next-task="getNextTask" :get-previous-task="getPreviousTask"
      :agentSenceCode="schemeInfo.agent_scene_code" @e-generalExec="execfromRunCode"
      @e-updateDevelop="handleUpdateDevelopFlag" @updateTaskModal="updateTaskModal"
      @updateTaskMiniModal="updateTaskMiniModal" @handleReStart="handleReStart" @stopAbilityGen="stopAbilityGen"
      @alignDataAns="queryStart" @reAlignData="reAlignData"></RunTask>
    <editModal :is-visible="editVisible" :flow-data="flowData" @updateEditModal="updateEditModal"></editModal>
    <DataAlignPreDialog :is-visible="alignPreVisable" :all-success="allSuccess2" :run-stream-list="runStreamList2"
      @close="handelCofirmAlignPre"></DataAlignPreDialog>
  </div>
</template>
<script>
import {
  chatDisconnect,
  getbatchTasksResult,
  getTasksRes,
  generalExecute,
  getTaskListBySchemeId,
  SchemeTasks,
  UpdateScheme,
  SchemeConversationDetail,
  queryGreeting,
  // above from yeahz
  queryChatIsUseing,
  saveSimpleSchemeGenerate,
  startStopThinking,
  startAlignDataGenerate,
  startConversation,
  querySchemeDetailById,
  queryTaskByTemp,
  saveAlignType,
  queryTempIfFromScene,
  queryTempIfFromScene2,
  queryAbilityMapping,
  queryDuiqiTags,
  getSchemePhaseTasks,
  queryDeviceIds,
  queryPointsByDeviceId,
  GetDecision,
  startTask,
  checkByDeviceId,
  queryAgentInfoDetail,
  get_preset_question,
} from '@/api/planGenerateApi.js'
import { mockData } from './mock/mockData.js'
import { RunCode } from './constants.js'
import { flattenObject } from './yeahzutils.js'
import planChat from './planChat.vue'
import abilityTest from './abilityTest.vue'
import RunTask from './runTaskNew.vue'
import stepFlow from './stepFlow.vue'
import editModal from './editModal.vue'
import DataAlignPreDialog from './dataAlignPreModal.vue'
import Recorder from 'js-audio-recorder'
import dayjs from 'dayjs'
import { mapGetters,mapState, mapMutations } from 'vuex'
import axios from 'axios'
import Bus from '../../../components/bus'
import { listLogFile } from '@/api/aiServiceDeploy.js'
import zhushouChatTest from '../zhushouChatTest.vue'
import zhushouChatTestGeneral from '../zhushouChatTestGeneral.vue'
import zhushouChatRuleTest from '../zhushouChatRuleTest.vue'
import zhushouChatForm from '../zhushouChatForm.vue'
import zhushouChatBi from '../zhushouChatBi.vue'
import {throttle } from 'lodash'
const cancelToken = axios.CancelToken
let source = cancelToken.source()
import { handleThrottleLog } from '@/utils/ThrottledLogger.js';
// import HqwAbilityTest from '../hqw/abilityTest.vue'
import HqwAbilityTest from '../hqw/abilityTest.vue'
// align_data_stream_message: 'handleAlignData', // 数据对齐流消息，没有这玩意
// 这个方法废弃了
const eventHandlers = {
  decision_tree_stream_message: 'handleProcessTreeDetail', // 思维树生成
  decision_tree_process_stream_message: 'handleProcessTreeData', // 思维树生成过程
  scheme_stream_message: 'handleTaskDetail', // 方案任务
  process_stream_message: 'handleProcessDetail', // 流程处理
  ability_generate_stream_message: 'handleAbilityDetail', // 能力生成
  ability_process_stream_message: 'handleCodeProcesData', // 能力生成过程
  scheme_process_stream_message: 'handleProcessSchemeData', // 方案生成过程
  sql_stream_message: 'handleSQLData', // SQL 流消息
  align_data_process_stream_message: 'handleAlignProcessData', // 数据对齐过程流
  scheme_optimize_stream_message: 'handleOptimizeData', // 方案优化
  code_analysis_stream_message: 'handleCodeAnalysisData', // 代码分析
  code_analysis_process_stream_message: 'handleCodeAnalysisProcessData' // 代码分析过程
  // rule_generate_stream_message: 'handleRuleDetail', // 规则生成
  // rule_generate_process_stream_message: 'handleProcessRuleData' // 规则生成过程
}

// 这里有一些内容的对应函数我还没写好，比如方案优化process和SQL的process, 而且这个函数应该是没有的，我得弄一下。
const messageRoleToHandlersMap = {
  decision_tree_generate: 'handleProcessTreeDetail', // 思维树生成
  decision_tree_gen_process: 'handleProcessTreeData', // 思维树生成过程
  scheme_generate: 'handleTaskDetail', // 方案明细的流式消息
  scheme_gen_process: 'handleProcessSchemeData', // 方案明细的过程流式消息
  gen_process: 'handleProcessDetail', // 聊天的生成过程的流式消息
  ability_generate: 'handleAbilityDetail', // 能力生成
  ability_gen_process: 'handleCodeProcesData', // 能力生成过程
  code_custom_deploy: 'handleOptimizeDataNewBuShu', // 新部署
  code_custom_deploy_gen_process: 'handleOptimizeProcessDataNewBuShu', // 新部署过程
  code_custom_test: 'handleOptimizeDataNewTest', // 新测试
  code_custom_test_gen_process: 'handleOptimizeProcessDataNewTest', // 新测试过程
  ability_check: 'handleCodeTestData', // 代码测试
  sql_generate: 'handleSQLData', // SQL 流消息
  sql_gen_process: '',
  align_data_gen_process: 'handleAlignProcessData', // 数据对齐过程流，不过通用监听那个不从这里返回了。
  scheme_optimize: 'handleOptimizeData', // 方案优化
  scheme_optimize_gen_process: 'handleOptimizeProcessData', // 方案优化过程
  code_analysis: 'handleCodeAnalysisData', // 代码分析
  code_analysis_gen_process: 'handleCodeAnalysisProcessData', // 代码分析过程
  rule_generate: 'handleAbilityDetail', // 规则生成,这里直接采用代码生成的吧
  rule_generate_gen_process: 'handleCodeProcesData', // 规则生成过程,这里直接采用代码生成的吧
  model_param_extraction: 'modelData', //模型参数识别
  model_param_extraction_process: 'modelData', //模型参数识别过程
  modeling_info_structure: 'modelData', // 结构化建模信息
  modeling_info_structure_process: 'modelData', // 结构化建模信息过程
  math_model_generate: 'modelData', //生成数学模型
  math_model_generate_process: 'modelData', //生成数学模型过程
  ability_test_call: 'handleAbilityTestData', // 能力测试
}

// ABILITY_CHECK
const {
  KNOWLEDGE_BASE_SEARCH,
  CREATE_QUESTIONS_RESULT,
  SCHEME_OPTIMIZE,
  DECISION_TREE,
  ALIGN_ANALYSIS,
  MODEL_PARAM_EXTRACTION,
  MODELING_INFO_STRUCTURE,
  MATH_MODEL_GENERATE,
  ALIGN_DATA_GENERATE,
  DECISION_MAKING_GENERATE,
  CODE_DEPLOY,
  CODE_DEPLOY_NEW,
  CODE_TEST,
  CODE_CUSTOM_TEST,
  ABILITY_CHECK,
  CODE_ANALYSIS,
  API_PARAM_ANALYSIS
} = RunCode

const RunCodeToAPiHandleMap = {
  [KNOWLEDGE_BASE_SEARCH]: '',
  [CREATE_QUESTIONS_RESULT]: '',
  [SCHEME_OPTIMIZE]: 'generalSSEfnExec',
  [DECISION_TREE]: 'generalSSEfnExec',
  [ALIGN_ANALYSIS]: 'queryStart',
  [MODEL_PARAM_EXTRACTION]: 'generalSSEfnExec',
  [MODELING_INFO_STRUCTURE]: 'generalSSEfnExec',
  [MATH_MODEL_GENERATE]: 'generalSSEfnExec',
  [ALIGN_DATA_GENERATE]: 'reAlignData',
  [DECISION_MAKING_GENERATE]: 'generalSSEfnExec',
  [CODE_DEPLOY]: 'handleUpdateDevelopFlag',
  [CODE_DEPLOY_NEW]: 'generalSSEfnExec',
  [CODE_TEST]: 'child:handleRedoCodeTest',
  [CODE_CUSTOM_TEST]: 'generalSSEfnExec',
  [ABILITY_CHECK]: 'generalSSEfnExec',
  [CODE_ANALYSIS]: 'generalSSEfnExec',
  [API_PARAM_ANALYSIS]: ''
}

export default {
  name: 'ConfTaskPlanChat',
  components: {
    HqwAbilityTest,
    planChat,
    abilityTest,
    RunTask,
    editModal,
    stepFlow,
    DataAlignPreDialog,
    zhushouChatTest,
    zhushouChatTestGeneral,
    zhushouChatRuleTest,
    zhushouChatForm,
    zhushouChatBi
  },
props: {
 isReadOnly: {
   type: Boolean,
   default: false
 },
},
  data() {
    // modelingInfoStructureData: '', // 结构化建模信息
    // modelParamExtractionProcess: '', // 模型参数识别生成过程
    // modelParamExtractionData: '', // 模型参数识别data
    // mathModelGenerateProcessData: '', // 数学模型过程信息
    // mathModelGenerateData: '', // 数学模型结果信息
    return {
     schimeId: this.$route.query.id,
      CodeTestData: {
        status: 0,
        data: ''
      },
      kickLoading: false,
      isOccupied: false,
      usingSession_id: '',
      schemeStatus: '',
      phase_tasks: [], // 用于任务状态为1的task_name的回显 ,多个为字符串数组
      mathModelGenerateStatus: '', // 数学模型状态
      modelingInfoStructureStatus: -1, // 结构化建模信息状态
      modelParamExtractionStatus: -1, // 模型参数识状态code
      knowledgeTheme: '',
      treeDataProcessNodes: [], // 有分组的生成过程
      abilityDataProcess: '', // 生成过程
      abilityDataProcessStatus: '', // 生成过程状态
      codeAnalysisData: '', // 代码分析
      codeAnalysisDataStatus: '', // 代码分析过程状态
      codeAnalysisProcessData: '', // 代码分析过程
      codeAnalysisProcessDataStatus: '', // 代码分析过程状态
      schemeOptimizeProcessData: '', // 方案优化分析过程
      schemeOptimizeProcessDataStatus: '', // 方案优化分析过程状态
      eventSource: null,
      phoneFlag: false,
      agentAvatorInfoMap: {}, // 角色信息map
      currentRoleInfo: {}, // 当前角色id
      agentAvatorInfo: {},
      replaceWriteFlag: true,
      writeText: '',
      insertWriteFlag: true,
      greets: '',
      historyChat: {
        conversation_id: '',
        messages: []
      },
      historyChat2: {
        conversation_id: '',
        messages: []
      },
      processContent: { text: '' },
      processRunningContent: '', // 最后一条正在思考的过程内容
      // above from yeahz
      isExpert: true,
      planData: '', // 方案明细内容
      schemeInfo: {},
      activeStep: 0,
      hasChatingName: '', // 开始会话的时候，判断当前有没有被其他人使用中
      systemMessages: '',
      optionDataProcess: '',
      planStatus: -1, // 方案优化状态code
      planStatusNewBuShu: -1, // 新部署状态code
      planStatusNewTest: -1, // 新测试状态code
      optimizeData: '', // 方案优化流数据
      treeData: '', // 思维树内容
      treeDataProcess: '', // 生成过程
      treeStatus: -1,

      agentError: false,
      // flowData的status 0:待执行，1:执行成功，2:执行失败，3:执行中
      flowData: [],
      taskModalVisable: false,
      dataAlignData: [], // 数据对齐内容
      dataAlignDataStatus: '', // 数据对齐状态
      codeData: '', // 代码生成内容
      codeDataStatus: '', // 代码生成状态
      codeProcessData: '', // 代码生成过程内容
      processStatus: '', // 代码生成日志状态
      // 数据对齐分析字段--开始
      allSuccess: false, // 数据对齐分析状态
      runStreamList: [], // 数据对齐分析内容
      tablesList: [],
      templateId: '',
      tagList: [],
      allTagList: [],
      allRunTasks: [], // 所有需要执行的任务
      runId: '', // 执行需要的id,
      updateDevelopFlag: 1,
      editModalVisable: false,
      // progressData: 0, // 当前进度值
      miniFlag: false, // 任务执行最小化标识
      sessionId: '',
      alignPreVisable: false, // 数据对齐预处理窗口
      deviceId: '', // 绑定的设备ID
      preTemplateId: '', // 数据对齐前过程模版ID,
      pointsData: [], // 设备测点数据

      // 数据对齐分析前字段--开始
      allSuccess2: false, // 数据对齐分析状态
      runStreamList2: [], // 数据对齐分析内容
      allRunTasks2: [], // 所有需要执行的任务
      runId2: '', // 执行需要的id,
      // 数据对齐分析前字段--结束
      runTaskStatus: 0,
      alignProcessData: '', // 数据对齐过程数据
      alignProcessDataStatus: '', // 数据对齐过程状态
      emergentScene: '',
      speakingFlag: 'start',
      phoneStatus: 'start',
      qaBoxLoading: false,
      contentEditor: '',
      qaList: [],
      agentRoleFlag: false,
      planSearchFlag: false,
      planSearch: '',
      testdata:
        'graph TD\nA([开始]) --> B1{检查电源插座是否有电}\nB1 -->|有电| B2[检查家里主电源开关是否打开]\nB2 -->|打开| C1[检查家庭电路中断器是否跳闸]\nC1 -->|跳闸| E1[复位中断器-然后检查电是否恢复]\nE1 -->|电恢复| END([结束])\nE1 -->|电未恢复| F[联系专业电工进行故障排查和维修]\nC1 -->|没有跳闸| D1[检查电路配线是否受损]\nD1 -->|受损| E2[修复或更换电路线路-然后检查电是否恢复]\nE2 -->|电未恢复| F\nD1 -->|未受损| F\nB2 -->|没有打开| B3[打开电源开关-然后检查电是否恢复]\nB3 -->|电未恢复| F\nB1 -->|没有电| C1',
      isSuperAdmin: false,
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      tableLoading: false,
      detailContent: { text: '', file_url: '' },
      hisDetail: '',
      taskList: [],
      tableData: {
        list: [],
        page: 1,
        pageSize: 10,
        total: 0
      },
      currentText: '',
      socket: null,
      taskStatusText: '',
      timer: null,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskGenLoading: false,
      showGuess: false,
      guessList: [],
      panZoomRef: null,
      processVisable: false,
      sqlData: '',
      sqlStatus: -1,
      chendianVisable: false,
      chendianVisable2: false,
      globalChendianList: [],
      chendianList: [], // 假设这个列表为空，具体内容依然取决于业务逻辑
      youhuaVisable: false,
      lastClickTime: 0,
      inputHeight: 60,
      navType: '',
      agentRoleList: [],
      agentAvatorInfoList: [],
      ansBoxLoading: false,
      ansRole: '',
      audioChunks: [],
      yuyinText: '',
      shibieLoading: false,
      websocket: null,
      audioContext: null,
      websocketPara: {
        URI: 'wss://vop.baidu.com/realtime_asr',
        APPKEY: 'zrhz2KGQLCxgVrIPdvcUa9c2',
        DEV_PID: 15372,
        sample: 16000,
        CHUNK_SIZE: 1024,
        APPID: 60519323
      },
      uploadUrl: '',
      uploadParam: {},
      formData: {
        cangku: false,
        chooseData: [],
        tag_ids: [],
        api: false,
        manual_input: true
      }
    }
  },
  computed: {
    ...mapState('operations', ['editVisible']),
    ...mapGetters({
      initSengMsg: 'planGenerate/getInitSengMsg'
    }),
    progressData() {
      if (!Array.isArray(this.flowData) || this.flowData.length === 0) return 0;
      const maxCount = this.flowData.length;
      const count = this.flowData.filter((item) => item.status === 1).length;
      return Math.ceil((count / maxCount) * 100);
    },
    schemeOptimize() {
      return this.flowData.find((f) => f.runCode === SCHEME_OPTIMIZE)
    },
    treeTask() {
      return this.flowData.find((f) => f.runCode === DECISION_TREE)
    },
    alignAnalysisTask() {
      return this.flowData.find((f) => f.runCode === ALIGN_ANALYSIS)
    },
    modelParamExtraction() {
      return this.flowData.find((f) => f.runCode === MODEL_PARAM_EXTRACTION)
    },
    modelingInfoStructure() {
      return this.flowData.find((f) => f.runCode === MODELING_INFO_STRUCTURE)
    },
    mathModelGenerate() {
      return this.flowData.find((f) => f.runCode === MATH_MODEL_GENERATE)
    },
    alignTask() {
      return this.flowData.find((f) => f.runCode === ALIGN_DATA_GENERATE)
    },
    codeAbilityTask() {
      return this.flowData.find((f) => f.runCode === DECISION_MAKING_GENERATE)
    },
    codeDeployTask() {
      return this.flowData.find((f) => f.runCode === CODE_DEPLOY)
    },
    codeTestTask() {
      return this.flowData.find((f) => f.runCode === CODE_TEST)
    },
    codeTestNewTask() {
      return this.flowData.find((f) => f.runCode === ABILITY_CHECK)
    },
    codeAnalysisTask() {
      return this.flowData.find((f) => f.runCode === CODE_ANALYSIS)
    },
    RunCodeToCNTitleMap() {
      return {
        [KNOWLEDGE_BASE_SEARCH]: '' || KNOWLEDGE_BASE_SEARCH,
        [CREATE_QUESTIONS_RESULT]: '' || CREATE_QUESTIONS_RESULT,
        [SCHEME_OPTIMIZE]: '方案优化',
        [DECISION_TREE]:
          this.schemeInfo.agent_scene_code === 'custom_cognition_assistant_scene'
            ? '思维图生成'
            : '思维树生成',
        [ALIGN_ANALYSIS]: '数据对齐分析',
        [MODEL_PARAM_EXTRACTION]: '模型参数识别',
        [MODELING_INFO_STRUCTURE]: '结构化建模信息',
        [MATH_MODEL_GENERATE]: '生成数学模型',
        [ALIGN_DATA_GENERATE]: '数据对齐',
        [DECISION_MAKING_GENERATE]: '代码生成',
        [CODE_DEPLOY]: '代码部署',
        [CODE_DEPLOY_NEW]: '代码部署',
        [CODE_CUSTOM_TEST]: '代码测试',
        [CODE_TEST]: '代码测试',
        [ABILITY_CHECK]: '代码检查',
        [CODE_ANALYSIS]: '能力参数分析',
        [API_PARAM_ANALYSIS]: '' || API_PARAM_ANALYSIS
      }
    }
  },
  watch: {
    treeStatus: {
      handler(newVal, oldVal) {
        console.log('treeStatus watch 00', newVal)
      },
      immediate: true
    },
    treeDataProcess: {
      handler(newVal, oldVal) {
        console.log('treeDataProcess 111', newVal)
      },
      immediate: true
    },
    // flowData: {
    //   handler(newVal, oldVal) {
    //     if (!Array.isArray(newVal) || !Array.isArray(oldVal)) return;

    //     // 遍历数组，检查 status 是否发生变化
    //     newVal.forEach((newItem, index) => {
    //       const oldItem = oldVal[index];
    //       if (newItem?.status !== oldItem?.status) {
    //         console.log(`RunCode: ${newItem.runCode} status changed to: ${newItem.status}`);
    //       }
    //     });
    //   },
    //   immediate: true, // 确保页面加载时触发一次
    //   deep: true // 深度监听，确保内部属性变化也能被捕获
    // }
    // flowData: {
    //   handler(newVal) {
    //     if (!Array.isArray(newVal)) return;

    //     // 为每一项的 status 创建监听
    //     newVal.forEach((item, index) => {
    //       if (!item._watched) {
    //         this.$set(item, "_watched", true); // 添加标记，避免重复监听

    //         this.$watch(
    //           () => item.status, // 监听 status 的变化
    //           (newStatus, oldStatus) => {
    //             if (newStatus !== oldStatus) {
    //               console.log(`RunCode: ${item.runCode} status changed to: ${newStatus}`);
    //               // 在这里执行你的操作逻辑
    //             }
    //           }
    //         );
    //       }
    //     });
    //   },
    //   immediate: true,
    //   deep: false // 不需要深度监听整个数组
    // }
  },
  // created() {
  //  if (window.top !== window.self) {
  //   window.addEventListener('message', (event) => {
  //     try {
  //       const datajson = JSON.parse(event.data)
  //       this.setEmbed(datajson.embed)
  //       console.log('顶部收到空间信息2222', event.data);
  //     } catch (error) {

  //     }
  //   });
  // }
  // },
  async mounted() {
   console.log("mounted33333555555555", );
  //  if (window.top !== window.self) {
  //   window.addEventListener('message', (event) => {
  //     try {
  //       const datajson = JSON.parse(event.data)
  //       this.setEmbed(datajson.embed)
  //       console.log('顶部收到空间信息2222', event.data);
  //     } catch (error) {

  //     }
  //   });
  // }
    this.isReadOnly = Boolean(this.$route.query.isReadOnly) || false
    console.log('mounted in this.formData', typeof this.isReadOnly)

    Bus.$on('operations-research-changeDisplayType', (data) => {
      this.changeDisplayType()
    })
    Bus.$on('operations-research-edit', (data) => {
      this.editFn()
    })
    await this.schemeDetailById()
    await this.queryTemplate()
    await this.queryTaskList()
    // await this.getAbilitiesAndResult()
    // const mockData = this.generateMockData()
    // this.flowData = this.generateFlowDataFromApi(mockData)
    // await this.querySchcemTask()
    await this.queryBatchTasksResult()
    console.log('场景code this.schemeInfo.agent_scene_code', this.schemeInfo.agent_scene_code)
    console.log('this.templateId 00', this.templateId)
    // this.throttledHandleSystemMessage = _.throttle((data) => {
    //     this.handleSystemMessage(data);
    // }, 90, { leading: true, trailing: false });
    // this.queryAlignData()
  },
  async beforeRouteEnter(to, from, next) {
    console.log("from xxx1", from);
    console.log("to xxx1", to);
    if (from.path === "/planGenerate/planchat") {
      console.log("form xxx333", from.path);
      next();
      return
    }
    try {
      // 在 next 回调之外执行异步操作
      if( to.query.id != null && to.query.id != undefined && to.query.id != '' ) {
       const res = await querySchemeDetailById({ scheme_id: Number(to.query.id) });
       if (['digital_twin_assistant_scene'].includes(res.data.result.agent_scene_code)) {
         next();
         return
       }
      //  if (['dev_assistant_scene', 'other_assistant_scene'].includes(res.data.result.agent_scene_code)) {
      //    next({ path: '/planGenerate/planchat', query: to.query });
      //    return
      //  }
      }

      // 根据异步操作的结果来决定是否重定向

      next();
      // if (res.status === 200 && res.data.code === 200) {
      //   if (res.data.result.version === 'v2' || (from.path === "/planGenerate/index")) {
      //     console.log("正常版本 1", res.data.result.version);
      //     // 版本是 'v1'，不需要重定向，所以直接调用 next() 并传递 to 对象
      //     next();
      //   } else {
      //     console.log("跳转版本 1", res.data.result.version);
      //     // 版本不是 'v1'，需要重定向，同时保留 to 的查询参数
      //     next({ path: '/planGenerate/planchat', query: to.query });
      //   }
      // }
    } catch (error) {
      // 处理错误，可能需要重定向到错误页面或者显示错误消息
      console.error(error);
      next(); // 假设有一个错误页面的路由
    }
  },
  beforeDestroy() {
    Bus.$off('operations-research-edit')
    source.cancel()
    source = cancelToken.source()
    this.eventSource && this.eventSource.close()
  },
  methods: {
   ...mapMutations('operations',['researchEdit','setEmbed']),
   ...mapMutations('maco', ['setIsRunning']),
    processJSON(value) {
    // 如果是字符串，直接返回，不处理
    if (typeof value === "string") {
      return value;
    }

    // 如果是对象或数组，格式化为 JSON 字符串
    if (typeof value === "object" && value !== null) {
      return JSON.stringify(value, null, 2); // 缩进 2 个空格
    }

    // 对于其他类型，直接返回
    return value;
    },
    optimizeButtonClick(){

    },
    // throttledLog: throttle(function (...args) {
    //   console.log(...args); // 控制日志频率
    // }, 2000), // 每秒一次

    // // 处理日志的高频方法
    // handleThrottleLog(text='',data) {
    //   this.throttledLog('ThrottleLog:',text,data); // 使用节流的日志方法
    // },
    async getCodeDataResult(){
      try {
        const res = await GetDecision({
          scheme_id: this.$route.query.id,
          scheme_status: this.schemeStatus
        })
        const codeData = res.data.result?.decision_making_content || ''
        console.log("codeData 234", codeData);
        return codeData
      } catch (error) {
        console.error('Error fetching data:', error);
        return ''
      }
      },
    handleUpdateKickedLoading(val){
      this.kickLoading = val
    },
    isValidJson(str) {
      // 确保输入是字符串
      if (typeof str !== 'string') return false;
      str = str.trim(); // 去掉字符串前后的空格
      // 检查是否以 '{' 或 '[' 开头并以 '}' 或 ']' 结尾
      return (str.startsWith('{') && str.endsWith('}')) || (str.startsWith('[') && str.endsWith(']'));
    },
    alertKicked() {
      this.$alert('对话已被他人占用', '提示', {
        confirmButtonText: '确定',
        customClass: 'my-message-box1',
        showClose: false,
        callback: action => {
          // this.$message({
          //   type: 'info',
          //   message: `action: ${action}`
          // });
          if (action === 'confirm') {
            let fromMenu = this.$route.query.fromMenu
            if(fromMenu === '1'){
              // urlPath= '/planGenerate/first'
              // label= '专家生产'
              this.$router.push({
                path: '/planGenerate/first',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '3'){
              // label= '研发生产'
              // urlPath= '/planGenerate/index'
              this.$router.push({
                path: '/planGenerate/index',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '2'){
              // label= '任务规划'
              // urlPath= '/planGenerate/taskRd'
              this.$router.push({
                path: '/planGenerate/taskRd',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }else if(fromMenu === '4'){
              // label= '训练与验证'
              // urlPath= '/planGenerate/validation'
              this.$router.push({
                path: '/planGenerate/validation',
                query: { ...this.$route.query }  // 将现有的 query 参数传递到新路由
             });
            }
          }
        }
      });
    },
    async startPolling() {
      const poll = async () => {
        try {
          const res = await queryChatIsUseing({ scheme_id: this.$route.query.id });
          if (res && res.data.result.is_using === false) {
            console.log('Polling stopped as the condition met.');
            await this.getChatIsUseing()
            this.kickLoading = false
            return; // 停止轮询
          } else {
            await chatDisconnect({ session_id: this.sessionId, scheme_id: this.$route.query.id })
          }
        } catch (error) {
          console.error('Error during polling:', error);
          // 根据需求决定是否停止轮询或继续
        }
        // 在操作完成后，等待2秒再进行下一次轮询
        setTimeout(poll, 4000);
      };

      // 启动第一次轮询
      poll();
    },
    async handleUpdateHasChatingName(val) {
      // this.hasChatingName = val
      await this.getChatIsUseing()
    },
    handleUpdateAllsuccess(val) {
      console.log("啥时候执行的啊？00", val);
      this.allSuccess = val
    },
    setModelData(name, data) {
      this[name] = data
    },
    async generalSSEfnExec(runCode) {
      console.log(`触发generalSSEfnExec ${runCode}`)
      if (runCode === DECISION_TREE) {
        this.treeData = ''
        this.treeProcess = ''
      }
      if (runCode === MODEL_PARAM_EXTRACTION) {
        this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).process = ''
        this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize = ''
      }
      const execute_instruction = this.getTaskByRunCode(runCode)?.execute_instruction
      const instruction_param = this.$refs.planChatRef.getInstructionParam()
      console.log(instruction_param,'instruction_param')
      console.log('this.sessionId execute_instruction', this.sessionId, execute_instruction)
      let params
      if (runCode === ABILITY_CHECK){
        const codeDatafromApi = await this.getCodeDataResult()
        console.log("codeDatafromApi:", codeDatafromApi);
        console.log("this.codeData:", this.codeData);

        params = {
          session_id: this.sessionId,
          instruction_code: execute_instruction,
          instruction_param: {
            goal: this.codeData || codeDatafromApi,
            ...instruction_param
          }
        }
      }else{
        params = {
          session_id: this.sessionId,
          instruction_code: execute_instruction,
          instruction_param: {
            ...instruction_param
          }
        }
      }
      console.log(params,'params')
      await generalExecute(params)
    },
    handleUpdateDevelopFlag(val) {
      if(val === 1){
        this.updateDevelopFlag = val
      }else{
        this.updateDevelopFlag++
      }
      // 该变量会触发的代码部署
      console.log('现在的updateDevelopFlag值为：', this.updateDevelopFlag)
    },
    handleUpdateSystemMessage(val) {
      this.systemMessages = val
    },
    getTaskByRunCode(runCode) {
      return this.flowData.find((f) => f.runCode === runCode)
    },
    updateTaskStatus(runCode, newStatus) {
      const task = this.getTaskByRunCode(runCode)
      if (task) {
        // task.status = newStatus
        this.$set(task, 'status', newStatus); // 替代直接赋值
      }
      // this.updateTaskStatus(DECISION_TREE, diy)
      // this.updateTaskStatus(ALIGN_ANALYSIS, diy)
      // this.updateTaskStatus(ALIGN_DATA_GENERATE, diy)
      // this.updateTaskStatus(DECISION_MAKING_GENERATE, diy)
      // this.updateTaskStatus(CODE_DEPLOY, diy)
      // this.updateTaskStatus(CODE_TEST, diy)
      // this.updateTaskStatus(CODE_ANALYSIS, diy)
    },
    updateTaskProcess(runCode, text) {
      const task = this.getTaskByRunCode(runCode)
      if (task) {
        task.process = text
      }
      // this.updateTaskProcess(DECISION_TREE, diy)
      // this.updateTaskProcess(ALIGN_ANALYSIS, diy)
      // this.updateTaskProcess(ALIGN_DATA_GENERATE, diy)
      // this.updateTaskProcess(DECISION_MAKING_GENERATE, diy)
      // this.updateTaskProcess(CODE_DEPLOY, diy)
      // this.updateTaskProcess(CODE_TEST, diy)
      // this.updateTaskProcess(CODE_ANALYSIS, diy)
    },
    updateTaskSummarize(runCode, text) {
      const task = this.getTaskByRunCode(runCode)
      if (task) {
        task.summarize = text
      }
      // this.updateTaskProcess(DECISION_TREE, diy)
      // this.updateTaskProcess(ALIGN_ANALYSIS, diy)
      // this.updateTaskProcess(ALIGN_DATA_GENERATE, diy)
      // this.updateTaskProcess(DECISION_MAKING_GENERATE, diy)
      // this.updateTaskProcess(CODE_DEPLOY, diy)
      // this.updateTaskProcess(CODE_TEST, diy)
      // this.updateTaskProcess(CODE_ANALYSIS, diy)
    },
    // 获取当前位置的下一个task
    getNextTask(runCode) {
      const taskIndex = this.flowData.findIndex((task) => task.runCode === runCode)
      if (taskIndex !== -1 && taskIndex < this.flowData.length - 1) {
        return this.flowData[taskIndex + 1] // 返回下一个任务
      }
      return null // 没有下一个任务，返回 null
    },
    // 获取当前位置的上一个task
    getPreviousTask(runCode) {
      const taskIndex = this.flowData.findIndex((task) => task.runCode === runCode)
      if (taskIndex > 0) {
        return this.flowData[taskIndex - 1] // 返回上一个任务
      }
      return null // 没有上一个任务，返回 null
    },

    // 获取当前位置往后的所有tasks 若有的话
    getTasksAfter(runCode) {
      const taskIndex = this.flowData.findIndex((f) => f.runCode === runCode)

      // 如果找不到任务，返回空数组
      if (taskIndex === -1) return []

      // 返回从当前任务索引之后的任务列表
      return this.flowData.slice(taskIndex + 1)
    },
    // 获取当前位置往前的所有tasks 若有的话
    getTasksBefore(runCode) {
      const taskIndex = this.flowData.findIndex((f) => f.runCode === runCode)

      if (taskIndex === -1) return []

      // 返回从当前任务索引之前的任务列表
      return this.flowData.slice(0, taskIndex)
    },
    // 更新当前任务下一个的status
    updateNextTaskStatus(runCode, status) {
      const nextTask = this.getTasksAfter(runCode)[0]
      if (nextTask) {
        // nextTask.status = status
        this.$set(nextTask, 'status', status);
      }
    },
    // 更新当前任务上一个的status
    updatePreviousTaskStatus(runCode, status) {
      const tasksBefore = this.getTasksBefore(runCode)
      // 检查是否有前面的任务
      if (tasksBefore.length > 0) {
        const previousTask = tasksBefore[tasksBefore.length - 1]
        if (previousTask) {
          // previousTask.status = status
          this.$set(previousTask, 'status', status);
        }
      }
    },
    // 批量更新当前任务后面的内容的status
    updateTaskStatusesAfter(runCode, status) {
      const tasksAfter = this.getTasksAfter(runCode)

      // 遍历后续任务，将每个任务的  status 设置为传入的 status
      if (tasksAfter.length > 0) {
        tasksAfter.forEach((task) => {
          // task.status = status
          this.$set(task, 'status', status);
        })
      }
    },
    // 批量更新当前任务前面内容的status
    updateTaskStatusesBefore(runCode, status) {
      const tasksBefore = this.getTasksBefore(runCode)
      if (tasksBefore.length > 0) {
        tasksBefore.forEach((task) => {
          // task.status = status
          this.$set(task, 'status', status);
        })
      }
    },
    MyaddEventListener(eventName, handler) {
      this.eventSource.addEventListener(eventName, (event) => {
        // console.log(`${eventName}:`, event.data)
        const parsedData = JSON.parse(event.data)
        handler.call(this, parsedData) // 使用 call 来绑定 this 上下文
      })
    },
    initEventListenersFromHandlers() {
      Object.keys(eventHandlers).forEach((eventName) => {
        const handlerName = eventHandlers[eventName]
        if (this[handlerName]) {
          this.MyaddEventListener(eventName, this[handlerName])
        } else {
          console.error(`Handler ${handlerName} is not defined in the component.`)
        }
      })
    },
    RunfirstStepTask() {
      const firstRunCode = this.flowData[0].runCode
      console.log(`目前的第一个任务将会是：${firstRunCode}`)
      // console.log('目前这里写死了第一任务是思维树 DECISION_TREE， 后续任务状态设置为0')
      this.updateTaskStatus(firstRunCode, 3)
      this.updateTaskStatusesAfter(firstRunCode, 0)
      this.execfromRunCode(firstRunCode)
    },
    async execfromRunCode(code) {
      for (const runCode of Object.keys(RunCodeToAPiHandleMap)) {
        // 使用 for...of 循环
        if (runCode === code) {
          const handlerName = RunCodeToAPiHandleMap[runCode]

          if (handlerName.startsWith('child:')) {
            const childMethod = handlerName.split(':')[1]
            if (this.$refs.RunTaskRef && this.$refs.RunTaskRef[childMethod]) {
              await this.$refs.RunTaskRef[childMethod](runCode) // 支持异步子方法
            }
          } else if (this[handlerName]) {
            await this[handlerName](runCode) // 直接 await，支持异步
          } else {
            console.error(`No handler found for instruction code: ${code}`)
          }
        }
      }
    },
    async handleSSEMessagebyRole(message) {
      // handleThrottleLog('handleSSEMessagebyRole 0', message.role)
      for (const role of Object.keys(messageRoleToHandlersMap)) {
        if (role === message.role) {
          const handlerName = messageRoleToHandlersMap[role]
          if (this[handlerName]) {
            await this[handlerName](message) // 直接 await，支持异步
          } else {
            console.error(`No handler found for this role: ${role}`)
          }
        }
      }
    },
    generateFlowDataFromApi(taskConfig) {
      return taskConfig.map((task) => {
        return {
          icon: 'fangan', // 任务图标
          order: task.task_order,
          title: this.RunCodeToCNTitleMap[task.task_name], // 任务描述
          execute_param: task.execute_param || {},
          status: Number(task.task_status), // 任务状态
          runCode: task.task_name, // 任务标识符
          process: task.process || '', // 任务执行过程
          summarize: task.summarize || '', // 任务执行结果
          is_universal: task.is_universal,
          execute_instruction: task.execute_instruction, // 执行指令
          task_desc: task.task_desc, // 任务标识符
          process_message_code: task.process_message_code, // 过程消息码
          summarize_message_code: task.summarize_message_code, // 总结消息码
          align_fields: task.align_fields || {},
          agent_template_id: task.agent_template_id,
        }
      })
    },
    modelData(data) {
      //模型参数识别，模型参数识别的state我得和思维树的code分开，虽然主要的差别就在回显那个地方
      if (data.role === 'model_param_extraction_process') {
        if (data.action === 'start') {
          this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).process = data.data
        } else {
          this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).process += data.data
        }
      }

      // need to check, 这个地方也得和思维树分开
      if (data.role === 'model_param_extraction') {
        if (data.action !== 'end') {
          this.modelParamExtractionStatus = 0
          // this.thinkingTreeStatus = 0
        }
        if (data.action === 'end') {
          this.modelParamExtractionStatus = 2
          // this.thinkingTreeStatus = 2
        }
        if (data.action === 'start') {
          this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize = data.data
        } else {
          this.getTaskByRunCode(MODEL_PARAM_EXTRACTION).summarize += data.data
        }
      }
      if (data.role === 'modeling_info_structure_process') {
        if (data.action === 'start') {
          // this.runStreamVal = data.data
          this.updateTaskProcess(MODELING_INFO_STRUCTURE, data.data)
        } else {
          this.updateTaskProcess(
            MODELING_INFO_STRUCTURE,
            this.modelingInfoStructure.process + data.data
          )
          // this.runStreamVal = this.runStreamVal + data.data
        }
        if (data.action === 'end') {
          console.log(this.modelingInfoStructure.process, '结构化建模最终数据')
        }
      }

      if (data.role === 'modeling_info_structure') {
        if (data.action !== 'end') {
          this.modelingInfoStructureStatus = 0
        }
        if (data.action === 'end') {
          this.modelingInfoStructureStatus = 2
          this.getTaskByRunCode(MATH_MODEL_GENERATE).summarize = ''
          this.getTaskByRunCode(MATH_MODEL_GENERATE).process = ''
          this.mathModelGenerateStatus = 0
          console.log('结构化code建模最终数据')
        }
        if (data.action === 'start') {
          // regenerateTree()
          this.getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize = data.data
          // this.modelingInfoStructureData = data.data
        } else {
          this.getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize += data.data
          // this.modelingInfoStructureData = this.modelingInfoStructureData + data.data
          // this.runStreamList = this.runStreamList + data.data
        }
      }
      if (data.role === 'math_model_generate_process') {
        if (data.action === 'start') {
          this.getTaskByRunCode(MATH_MODEL_GENERATE).process = data.data
        } else {
          this.getTaskByRunCode(MATH_MODEL_GENERATE).process += data.data
        }
      }

      if (data.role === 'math_model_generate') {
        if (data.action !== 'end') {
          this.mathModelGenerateStatus = 0
        }
        if (data.action === 'end') {
          this.mathModelGenerateStatus = 2
        }
        if (data.action === 'start') {
          this.getTaskByRunCode(MATH_MODEL_GENERATE).summarize = data.data
        } else {
          this.getTaskByRunCode(MATH_MODEL_GENERATE).summarize += data.data
        }
      }
      // math_model_generate
      // if (message.action === 'start') {
      //   this.alignProcessData = message.data;
      //   this.alignProcessDataStatus = '1';
      // } else {
      //   this.alignProcessData = this.alignProcessData + message.data;
      //   this.alignProcessDataStatus = '1';
      // }
      // if (message.action === 'end') {
      //   console.log('数据对齐生成过程的数据======', this.alignProcessData);
      //   this.alignProcessDataStatus = '2';
      // }
    },
    handleTaskDetail(message) {
      if (message.action === 'start') {
        this.detailContent.text = message.data
      } else {
        this.detailContent.text = this.detailContent.text + message.data
      }
      if (message.action === 'end') {
        this.taskStatus = 1
      }
    },
    handleSQLData(message) {
      if (message.action === 'start') {
        this.sqlData = message.data
      } else {
        this.sqlData = this.sqlData + message.data
      }
      if (message.action === 'end') {
        console.log('sql流信息结束')
        this.treeStatus = 2
      }
    },
    // 方案优化过程消息处理
    handleOptimizeProcessData(message) {
      console.log('方案优化有SSE消息吗？', message)
      if (message.action === 'start') {
        this.getTaskByRunCode(SCHEME_OPTIMIZE).process = message.data
        this.planStatus = 1
      } else {
        this.getTaskByRunCode(SCHEME_OPTIMIZE).process =
          this.getTaskByRunCode(SCHEME_OPTIMIZE).process + message.data
        this.planStatus = 1
      }
      if (message.action === 'end') {
        console.log('方案优化分析生成过程的数据======', this.schemeOptimizeProcessData)
        this.planStatus = 2
      }
    },
    // 新部署过程消息处理
    handleOptimizeProcessDataNewBuShu(message) {
      console.log('方案优化有SSE消息吗？', message)
      if (message.action === 'start') {
        this.getTaskByRunCode(CODE_DEPLOY_NEW).process = message.data
        this.planStatusNewBuShu = 1
      } else {
        this.getTaskByRunCode(CODE_DEPLOY_NEW).process =
          this.getTaskByRunCode(CODE_DEPLOY_NEW).process + message.data
        this.planStatusNewBuShu = 1
      }
      if (message.action === 'end') {
        console.log('方案优化分析生成过程的数据======', this.schemeOptimizeProcessData)
        this.planStatusNewBuShu = 2
      }
    },
    // 新测试过程消息处理
    handleOptimizeProcessDataNewTest(message) {
      console.log('方案优化有SSE消息吗？', message)
      if (message.action === 'start') {
        this.getTaskByRunCode(CODE_CUSTOM_TEST).process = message.data
        this.planStatusNewTest = 1
      } else {
        this.getTaskByRunCode(CODE_CUSTOM_TEST).process =
          this.getTaskByRunCode(CODE_CUSTOM_TEST).process + message.data
        this.planStatusNewTest = 1
      }
      if (message.action === 'end') {
        console.log('方案优化分析生成过程的数据======', this.schemeOptimizeProcessData)
        this.planStatusNewTest = 2
      }
    },
    handleOptimizeData(message) {
      if (message.action === 'start') {
        // this.optimizeData = message.data
        this.getTaskByRunCode(SCHEME_OPTIMIZE).summarize = message.data
      } else {
        // this.optimizeData = this.optimizeData + message.data
        this.getTaskByRunCode(SCHEME_OPTIMIZE).summarize =
          this.getTaskByRunCode(SCHEME_OPTIMIZE).summarize + message.data
      }
      if (message.action === 'end') {
        console.log('方案优化流信息结束')
        this.planStatus = 2
        // this.treeStatus = 2 // 这里为啥要this.treeStatus = 2 先注释掉吧，用planStatus
      }
    },
    handleOptimizeDataNewBuShu(message) {
      if (message.action === 'start') {
        // this.optimizeData = message.data
        this.getTaskByRunCode(CODE_DEPLOY_NEW).summarize = message.data
      } else {
        // this.optimizeData = this.optimizeData + message.data
        this.getTaskByRunCode(CODE_DEPLOY_NEW).summarize =
          this.getTaskByRunCode(CODE_DEPLOY_NEW).summarize + message.data
      }
      if (message.action === 'end') {
        console.log('方案优化流信息结束')
        this.planStatusNewBuShu = 2
        // this.treeStatus = 2 // 这里为啥要this.treeStatus = 2 先注释掉吧，用planStatus
      }
    },
    handleOptimizeDataNewTest(message) {
      if (message.action === 'start') {
        // this.optimizeData = message.data
        this.getTaskByRunCode(CODE_CUSTOM_TEST).summarize = message.data
      } else {
        // this.optimizeData = this.optimizeData + message.data
        this.getTaskByRunCode(CODE_CUSTOM_TEST).summarize =
          this.getTaskByRunCode(CODE_CUSTOM_TEST).summarize + message.data
      }
      if (message.action === 'end') {
        console.log('方案优化流信息结束')
        this.planStatusNewTest = 2
        // this.treeStatus = 2 // 这里为啥要this.treeStatus = 2 先注释掉吧，用planStatus
      }
    },
    handleProcessRuleData(message) {
      if (message.action === 'start') {
        this.abilityDataProcess = message.data
        this.abilityDataProcessStatus = '1'
      } else {
        this.abilityDataProcess = this.abilityDataProcess + message.data
        this.abilityDataProcessStatus = '1'
      }
      if (message.action === 'end') {
        console.log('规则生成过程的数据======', this.abilityDataProcess)
        this.abilityDataProcessStatus = '2'
      }
    },
    handleRuleDetail(message) {
      if (message.action !== 'end') {
        this.treeStatus = 0
        console.log('this.treeStatus Conf 00', this.treeStatus)
      }
      if (message.action === 'start') {
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      // if (message.action === 'end') {
      //   this.treeStatus = 2
      // }
    },
    // handleProcessAbilityData(message) {
    //   if (message.action === 'start') {
    //     console.log('代码生成---start', this.abilityDataProcess)
    //     this.abilityDataProcess = message.data
    //     this.abilityDataProcessStatus = '1'
    //   } else {
    //     this.abilityDataProcess = this.abilityDataProcess + message.data
    //     this.abilityDataProcessStatus = '1'
    //   }
    //   if (message.action === 'end') {
    //     console.log('代码生成---end', this.abilityDataProcess)
    //     this.abilityDataProcessStatus = '2'
    //   }
    // },
    async getChatIsUseing() {
      console.log('getChatIsUseing res', this.$route.query.id)
      const that = this
      queryChatIsUseing({ scheme_id: this.schimeId })
        .then(async (res) => {
          console.log('queryChatIsUs1eing res', res,this.$route.query.id,this.schimeId)
          if (res.status === 200 && res.data.code === 200) {
            console.log('判断是否可用', res.data)
            if (res.data.result.is_using) {
              this.hasChatingName = res.data.result?.using_user?.nickName
              this.usingSession_id = res.data.result?.using_user?.session_id
              // if ('wtf' === 'wtf') {
              //   this.hasChatingName = 'somebody'
              //   console.log('有用户正在使用')
            } else {
              this.hasChatingName = ''
              this.sessionId = res.data.result.session_id || '1'
              if (
                this.historyChat.messages.length > 0 &&
                this.historyChat.messages[this.historyChat.messages.length - 1].id
              ) {
                this.systemMessages = 'process waiting'
              }
              const url = process.env.VUE_APP_PLAN_API.startsWith('/')
                ? window.location.origin + process.env.VUE_APP_PLAN_API + '/stream'
                : process.env.VUE_APP_PLAN_API + '/stream'
              console.log('url', url)
              const userInfo = sessionStorage.getItem('USER_INFO')
                ? JSON.parse(sessionStorage.getItem('USER_INFO'))
                : {}

              this.eventSource = new EventSource(
                url +
                  '?scheme_id=' +
                  this.schimeId +
                  '&user_id=' +
                  (userInfo.userId || 'str') +
                  '&work_space_id=' +
                  (this.$route.query.workspaceId + '' || '1') +
                  '&tenant_id=' +
                  (userInfo.tenantId || 'str') +
                  '&session_id=' +
                  (res.data.result.session_id || '1')
              )

              this.eventSource.addEventListener('open', () => {
                console.log('连接已建立')
                this.agentError = false
              })

              this.eventSource.addEventListener('system', async (event) => {
                console.log('event.data 001', event.data)
                if (event.data === 'occupied') {
                  this.eventSource.close()
                  this.isOccupied = true
                  this.alertKicked()
                  // await queryChatIsUseing({ scheme_id: this.$route.query.id }).\
                  // this.getChatIsUseing()
                }
              })


              this.eventSource.addEventListener('messages', (event) => {
                console.log('messages收到消息：', event.data)
                this.handleMessage(JSON.parse(event.data))
              })
              this.eventSource.addEventListener('scheme_stream_message', (event) => {
                this.handleProcessPlanDetail(JSON.parse(event.data));
              });
              // this.eventSource.addEventListener('stream_message', (event) => {
              //   const message = JSON.parse(event.data)
              //   console.log('stream_message收到消息：', event.data)
              //   this.$refs.planChatRef.handleMessageStream(JSON.parse(event.data))
              //   if (message.action === 'end') {
              //     if (this.phoneFlag) {
              //       this.startWeb()
              //     }
              //   }
              // })
              this.eventSource.addEventListener('system_message', (event) => {
                console.log('system_message', JSON.parse(event.data))
                this.handleSystemMessage(JSON.parse(event.data));
                // this.throttledHandleSystemMessage(JSON.parse(event.data));
              })

              if (this.$refs.planChatRef) {
                console.log('planChatRef 123456 listenSteam', this.$refs.planChatRef);
                this.$refs.planChatRef.listenSteam()
              }
              this.eventSource.addEventListener('task_message', (event) => {
                console.log('task_messages', event.data)
                const message = JSON.parse(event.data)
                this.taskGeneratorStatus = message.data
                if (message.data !== 'task generating') {
                  this.queryTask()
                }
                if (message.data === 'task generating completed') {
                  this.taskStatus = 1
                  this.treeStatus = 2
                  this.taskGenLoading = false
                }
                if (message.data === 'task generating error') {
                  this.taskGenLoading = false
                  this.treeStatus = 2
                  this.$message({
                    type: 'error',
                    message: this.taskGenType === 'override' ? '任务覆盖失败' : '任务追加失败!'
                  })
                }
              })
              this.eventSource.addEventListener('align_data_process_stream_message', (event) => {
                const message = JSON.parse(event.data)
                console.log('Received align_data_process_stream_message:', message)
                this.handleAlignProcessData(message)
              })
              this.eventSource.addEventListener('process_stream_message', (event) => {
                console.log('process_stream_message', event.data)
                this.systemMessages = 'process_stream_message'
                this.handleProcessDetail(JSON.parse(event.data))
              })
              // general runTask部分的is_universal为true的流式数据返回，要取代initEventListenersFromHandlers的逻辑
              this.eventSource.addEventListener('common_stream_message', (event) => {
                // console.log('common_stream_message 0', JSON.parse(event.data))
                // handleThrottleLog('common_stream_message 0', JSON.parse(event.data))
                this.handleSSEMessagebyRole(JSON.parse(event.data))
                // this.handleSystemMessage(JSON.parse(event.data))
              })
              //  部署新消息 sure_template_stream_message
              this.eventSource.addEventListener('sure_template_stream_message', (event) => {
                // console.log('common_stream_message 0', JSON.parse(event.data))
                // handleThrottleLog('common_stream_message 0', JSON.parse(event.data))
                this.handleSSEMessagebyRole(JSON.parse(event.data))
                // this.handleSystemMessage(JSON.parse(event.data))
              })
              // this.initEventListenersFromHandlers()
              this.eventSource.addEventListener('error', () => {
                console.log('连接出错')
                this.agentError = true
                // this.treeStatus = 3;
                // this.abilityDataProcessStatus = 3;
                // 回到第一步
                this.stepIndex = 0
                this.systemMessages = ''
                this.changeViews(0)
              })
              this.eventSource.addEventListener('disconnect', () => {
                console.log('连接断开')
                this.agentError = true
                this.systemMessages = ''
              })
            }
          } else {
            this.hasChatingName = ''
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            })
          }
        })
        .catch((err) => {
          console.log('err xxx', err)
        })
    },
    handleTreeStatusUpdate(newStatus) {
      this.treeStatus = newStatus // 更新父组件中的 treeStatus 状态
    },
    queryTask() {
      SchemeTasks({ scheme_id: this.$route.query.id, order: 'updated' }).then((res) => {
        // console.log(res, '000');
        if (res.status === 200 && res.data.code === 200) {
          this.taskList = res.data.result.items
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    handleSystemMessage(message) {
      console.log('系统消息', message)
      this.systemMessages = message.data
      // 弹窗不开 不执行下一步
      // if (!this.taskModalVisable) {
      //   return
      // }
      if (message.data === 'ability_check failed') {
        console.log('ability_check failed 12345')
        this.updateTaskStatus(ABILITY_CHECK, 2)
      }
      if (message.data === 'append_history completed') {
        console.log('append_history completed', message.data)
        this.queryDetail()
      }
      if (message.data === 'append_history error') {
        console.log('append_history error', '增加会话历史记录失败')
      }
      if (message.data === 'scheme generating error') {
        this.taskStatusText = 'scheme generating error'
        this.treeStatus = 2
      }
      if (message.data === 'illegal agent_id') {
        this.agentError = true
        this.queryTask()
      } else {
        this.agentError = false
        this.queryTask()
      }
      // 清空聊天记录成功
      if (message.data === 'clear history completed' || message.data === 'clear history error') {
        console.log('xxxxxxxxx')
        this.historyChat.messages = []
        this.$refs.planChatRef.clearChat(message.data === 'clear history completed')
      }

      if (message.data === 'process running') {
        console.log('----正在会话中')
      }
      if (
        message.data === 'process completed' ||
        message.data === 'process error' ||
        message.data === 'scheme generating error'
      ) {
        console.log('结束----')
        this.queryTask()
        this.setIsRunning(false)
      }
      if (message.data === '403 error') {
        this.refresh()
        const lastMessage = this.historyChat.messages[this.historyChat.messages.length - 1].content
        this.qaList = []
        startConversation({
          messages: lastMessage.parts,
          agent_role_id: this.agentAvatorInfo.id,
          session_id: this.sessionId
        })
      }
      if (message.data === 'process completed') {
        this.taskStatus = 1
        this.queryTask()
      }
      if (message.data === 'process waiting') {
        this.scrollToBottom()
      }
      if (message.data === 'scheme generating completed') {
        this.treeStatus = 2
      }

      // sssssssssssss
      if (message.data === 'model_param_extraction generating completed') {
        this.modelParamExtractionStatus = 2
        this.getTaskByRunCode(MODELING_INFO_STRUCTURE).summarize = ''
        // this.modelingInfoStructureData = ''
        // this.thinkingTreeStatus = 2;
        this.updateTaskStatus(MODEL_PARAM_EXTRACTION, 1)
        const nextTask = this.getNextTask(MODEL_PARAM_EXTRACTION)
        if (nextTask) {
          nextTask.status = 3
          this.execfromRunCode(nextTask.runCode)
        }
        console.log('出发发发发发发发')
      }
      if (message.data === 'model_param_extraction generating') {
        this.modelParamExtractionStatus = 1
        // this.thinkingTreeStatus = 1;
        this.updateTaskStatus(MODEL_PARAM_EXTRACTION, 3)

        console.log('this.modelParamExtractionStatus', this.modelParamExtractionStatus)
      }
      if (message.data === 'model_param_extraction error') {
        this.modelParamExtractionStatus = 3
        // this.thinkingTreeStatus = 3;
        this.updateTaskStatus(MODEL_PARAM_EXTRACTION, 2)
      }
      // // 结构化建模信息分析
      // if (message.data === 'modeling_info_structure_process completed') {
      //   this.runStreamStatus = 2;
      // }
      if (message.data === 'modeling_info_structure generating completed') {
        this.modelingInfoStructureStatus = 2
        this.updateTaskStatus(MODELING_INFO_STRUCTURE, 1)
        const nextTask = this.getNextTask(MODELING_INFO_STRUCTURE)
        if (nextTask) {
          nextTask.status = 3
          this.execfromRunCode(nextTask.runCode)
        }
      }
      if (message.data === 'modeling_info_structure generating') {
        this.modelingInfoStructureStatus = 1
        this.updateTaskStatus(MODELING_INFO_STRUCTURE, 3)
      }
      if (message.data === 'modeling_info_structure error') {
        this.updateTaskStatus(MODELING_INFO_STRUCTURE, 2)
      }

      if (message.data === 'math_model_generate generating completed') {
        this.mathModelGenerateStatus = 2
        this.updateTaskStatus(MATH_MODEL_GENERATE, 1)
        const nextTask = this.getNextTask(MATH_MODEL_GENERATE)
        if (nextTask) {
          nextTask.status = 3
          this.execfromRunCode(nextTask.runCode)
        }
      }
      if (message.data === 'math_model_generate') {
        this.mathModelGenerateStatus = 1
        this.updateTaskStatus(MATH_MODEL_GENERATE, 3)
      }
      if (
        message.data === 'math_model_generate error' ||
        message.data === 'math_model_generate generating error'
      ) {
        this.updateTaskStatus(MATH_MODEL_GENERATE, 2)
      }

      // eeeeeeeeeeeeeeeeeee

      if (message.data === 'decision tree generating completed') {
        console.log('决策树生成  completed')
        this.treeStatus = 2
        this.updateTaskStatus(DECISION_TREE, 1)
        const nextTask = this.getNextTask(DECISION_TREE)
        setTimeout(() => {
          if (nextTask) {
            nextTask.status = 3
            this.updateTaskStatusesAfter(nextTask.runCode, 0)
            this.execfromRunCode(nextTask.runCode)
          }
        }, 2000);
      }
      if (message.data === 'decision tree generating') {
        this.treeStatus = 1
        this.updateTaskStatus(DECISION_TREE, 3)
        this.updateTaskStatusesAfter(DECISION_TREE, 0)
        console.log('this.treeStatus思维树', this.treeStatus)
      }
      if (message.data === 'decision tree generating error') {
        this.treeStatus = 3
        this.updateTaskStatus(DECISION_TREE, 2)
        this.updateTaskStatusesAfter(DECISION_TREE, 0)
      }
      if (
        message.data === 'ability generating completed' ||
        message.data === 'rule generating completed'
      ) {
        this.codeDataStatus = 2
        console.log('ability generating completed BI-CODE', message.data)
        this.updateTaskStatus(DECISION_MAKING_GENERATE, 1)
        const nextTask = this.getNextTask(DECISION_MAKING_GENERATE)
        if (nextTask) {
          nextTask.status = 3
          this.execfromRunCode(nextTask.runCode)
          console.log("这里触发了代码部署吧？ CODE_DEPLOY");

        }
        // 触发自动部署
      }
      // if (
      //   message.data === 'ability generating completed' ||
      //   message.data === 'rule generating completed'
      // ) {
      //   this.codeDataStatus = 2
      //   console.log('ability generating completed BI-CODE', message.data)
      //   this.updateTaskStatus(DECISION_MAKING_GENERATE, 1)
      //   const nextTask = this.getNextTask(DECISION_MAKING_GENERATE)
      //   if (nextTask) {
      //     nextTask.status = 3
      //     this.execfromRunCode(nextTask.runCode)
      //     console.log("这里触发了代码部署吧？ CODE_DEPLOY");

      //   }
      //   // 触发x新自动部署
      // }
      if (message.data === 'ability generating' || message.data === 'rule generating') {
        this.codeDataStatus = 1
        this.updateTaskStatus(DECISION_MAKING_GENERATE, 3)
        // {
        //   icon: 'fangan',
        //   title: '思维树生成',
        //   status: 1,
        //   runCode: DECISION_TREE
        // },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐分析',
        //     status: 1,
        //     runCode: ALIGN_ANALYSIS
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐',
        //     status: 1,
        //     runCode: ALIGN_DATA_GENERATE
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码生成',
        //     status: 3,
        //     runCode: DECISION_MAKING_GENERATE
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码部署',
        //     status: 0,
        //     runCode: CODE_DEPLOY
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码测试',
        //     status: 0,
        //     runCode: CODE_TEST
        //   },
        //   {
        //   icon: 'fangan',
        //   title: '能力参数分析',
        //   status: 0,
        //   runCode: CODE_ANALYSIS
        // },
        // ];
      }
      if (message.data === 'ability generating error' || message.data === 'rule generating error') {
        this.codeDataStatus = 3
        this.updateTaskStatus(DECISION_MAKING_GENERATE, 2)
        console.log('ability generating error BI-CODE', message.data)
        // {
        //   icon: 'fangan',
        //   title: '思维树生成',
        //   status: 1,
        //   runCode: DECISION_TREE
        // },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐分析',
        //     status: 1,
        //     runCode: ALIGN_ANALYSIS
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '数据对齐',
        //     status: 1,
        //     runCode: ALIGN_DATA_GENERATE
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码生成',
        //     status: 2,
        //     runCode: DECISION_MAKING_GENERATE
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码部署',
        //     status: 0,
        //     runCode: CODE_DEPLOY
        //   },
        //   {
        //     icon: 'fangan',
        //     title: '代码测试',
        //     status: 0,
        //     runCode: CODE_TEST
        //   },
        //   {
        //   icon: 'fangan',
        //   title: '能力参数分析',
        //   status: 0,
        //   runCode: CODE_ANALYSIS
        // },
        // ];
      }
      // 数据对齐完成
      if (message.data === 'align_data generating completed') {
        this.dataAlignDataStatus = 2
        this.updateTaskStatus(ALIGN_DATA_GENERATE, 1)
        console.log('align_data generating completed数据对齐完成 ======', message.data)
        // 数据对齐完成执行代码生成
        // startAbilityGenerate({ ability_type: 'decision_ability', session_id: this.sessionId })
        const nextTask = this.getNextTask(ALIGN_DATA_GENERATE)
        if (nextTask) {
          nextTask.status = 3
          this.execfromRunCode(nextTask.runCode)
        }
      }
      if (message.data === 'align_data generating') {
        this.dataAlignDataStatus = 1
        this.updateTaskStatus(ALIGN_DATA_GENERATE, 3)
      }
      if (message.data === 'align_data generating error') {
        this.dataAlignDataStatus = 3
        this.updateTaskStatus(ALIGN_DATA_GENERATE, 2)
        console.log('22222222222222 queryAlignData')

        this.queryAlignData()
      }
      //need to check
      if (message.data === 'scheme optimize completed') {
        // 这里判断任务的执行情况影响流程的逻辑从system_message移动到那个新接口各自的处理函数了。
        console.log('scheme optimize completed方案优化完成 ======', message.data)
        this.planStatus = 2
        this.updateTaskStatus(SCHEME_OPTIMIZE, 1)
        const nextTask = this.getNextTask(SCHEME_OPTIMIZE)
        if (nextTask) {
          nextTask.status = 3
          this.updateTaskStatusesAfter(nextTask.runCode, 0)
          this.execfromRunCode(nextTask.runCode)
        }
      }
      if (message.data === 'scheme optimize generating') {
        this.planStatus = 1
        this.updateTaskStatus(SCHEME_OPTIMIZE, 3)
        this.updateTaskStatusesAfter(SCHEME_OPTIMIZE, 0)
      }
      if (message.data === 'scheme optimize error') {
        this.planStatus = 3
        this.updateTaskStatus(SCHEME_OPTIMIZE, 2)
        this.updateTaskStatusesAfter(SCHEME_OPTIMIZE, 0)
      }
      //  新部署
      if (message.data === 'code_custom_deploy completed') {
        // 这里判断任务的执行情况影响流程的逻辑从system_message移动到那个新接口各自的处理函数了。
        console.log('code_custom_deploy completed方案优化完成 ======', message.data)
        this.planStatusNewBuShu = 2
        this.updateTaskStatus(CODE_DEPLOY_NEW, 1)
        const nextTask = this.getNextTask(CODE_DEPLOY_NEW)
        if (nextTask) {
          nextTask.status = 3
          this.updateTaskStatusesAfter(nextTask.runCode, 0)
          this.execfromRunCode(nextTask.runCode)
        }
      }
      //  新测试
      if (message.data === 'code_custom_test completed') {
        // 这里判断任务的执行情况影响流程的逻辑从system_message移动到那个新接口各自的处理函数了。
        console.log('code_custom_deploy completed方案优化完成 ======', message.data)
        this.planStatusNewTest = 2
        this.updateTaskStatus(CODE_CUSTOM_TEST, 1)
        const nextTask = this.getNextTask(CODE_CUSTOM_TEST)
        if (nextTask) {
          nextTask.status = 3
          this.updateTaskStatusesAfter(nextTask.runCode, 0)
          this.execfromRunCode(nextTask.runCode)
        }
      }
      if (message.data === 'code_analysis generating error') {
        console.log('code_analysis generating error')
        this.codeAnalysisDataStatus = '3'
        this.updateTaskStatus(CODE_ANALYSIS, 2)
      }
      if (message.data === 'code_analysis generating completed') {
        console.log('代码分析完成')
        this.codeAnalysisDataStatus = '2'
        this.updateTaskStatus(CODE_ANALYSIS, 1)
      }
      if (message.data === 'code_analysis generating') {
        console.log('代码分析中。。。')
        this.codeAnalysisDataStatus = '1'
        this.updateTaskStatus(CODE_ANALYSIS, 3)
      }
    },
    handleSessionIdUpdate(newSessionId) {
      this.sessionId = newSessionId // 更新父组件的 sessionId
    },

    handleSchemeInfoUpdate(val) {
      this.schemeInfo = { ...val }
    },
    // above add from yeahz

    // 切换模式
    async changeDisplayType() {
      this.taskGenLoading = true
      const newdisplay_type = this.displayType === 1 ? 2 : 1
      const share_infos =
        this.schemeInfo.share_infos?.map((el) => {
          return el.user_id
        }) || []
      const params = {
        id: this.schemeInfo.id + '',
        display_type: newdisplay_type + '',
        name: this.schemeInfo.name,
        scheme_detail_name: this.schemeInfo.scheme_detail_name || this.schemeInfo.name,
        description: this.schemeInfo.description,
        agent_scene: this.schemeInfo.agent_scene,
        agent_scene_code: this.schemeInfo.agent_scene_code,
        agent_id: this.schemeInfo.agent_id || '',
        contributors: this.schemeInfo.contributors,
        tag_ids: this.schemeInfo.tag_ids,
        ext_data_info: this.schemeInfo.ext_data_info,
        visibility: this.schemeInfo.visibility,
        share_userids: share_infos
      }
      UpdateScheme(params)
        .then(async (res) => {
          this.taskGenLoading = false
          if (res.status === 200 && res.data.code * 1 === 200) {
            this.schemeInfo = { ...this.schemeInfo, display_type: newdisplay_type }
            this.displayType = newdisplay_type
            Bus.$emit('operations-displayType', this.displayType)
          }
        })
        .finally(() => {
          this.taskGenLoading = false
        })
    },

    async queryGreets() {
      const res = await queryAgentInfoDetail({ scheme_id: this.$route.query.id })
        this.historyChat.messages = [
            {
              author: { role: 'agent' },
              auto: true,
              agent_role_id: this.currentRoleInfo?.id,
              content: {
                chat_message: {
                  content: res?.data[0].prologue || '请问有什么我可以帮您？',
                  preset_question: [''] //res?.data[0].preset_question
                }
              },
              create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
          ]
        const res2 = await get_preset_question({ scheme_id: this.$route.query.id });
        this.historyChat.messages[0].content.chat_message.preset_question = res2.data
    },
    async queryDetail() {
        SchemeConversationDetail({ conversation_id: this.$route.query.id,"mode": "chat" }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.historyChat = res.data.result || {
            messages: []
          }
          this.$refs.planChatRef.queryAgentAvator()
          if (res.data.result.messages?.length) {
            this.systemMessages = 'process waiting'
          } else {
            await this.queryGreets()
          }
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
       SchemeConversationDetail({ conversation_id: this.$route.query.id,"mode": "builder" }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.historyChat2 = res.data.result || {
            messages: []
          }
        }
      })
    },
    // above form yeahz
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.planChatRef) {
          this.$refs.planChatRef.scrollToBottom()
        }
      })
    },
    handleProcessDetail(message) {
      requestIdleCallback(()=>{
      if (message.action === 'start') {
        console.log('思考结束开始')
        this.processRunningContent = message.data
        this.processContent.text = message.data
      } else {
        this.processRunningContent = this.processRunningContent + message.data
        this.processContent.text = this.processContent.text + message.data
      }
      if (message.action === 'end') {
        console.log('思考结束s ')
        this.processRunningContent = this.processRunningContent + message.data
        this.processContent.text = this.processContent.text + message.data
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    })
    },
    handleMessage(message) {
      console.log('你怎么能给我返回空的message? 00', message)
      const userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {}

      console.log('message 为空我可就不push了', message)
      if (
        (message?.content && message?.content.trim()) ||
        (message?.data && message?.data.trim())
      ) {
        this.historyChat.messages.push({
          author: { role: message.role },
          auto: message.role === 'auto' || message.data === '请您发送反馈信息',
          content: {
            // parts: message?.content || message?.data,
            chat_message_type:
              message?.image_key !== undefined && message?.image_key !== '' ? 'img_text' : 'text',
            chat_message: {
              content: message?.content || message?.data,
              image_path: message?.image_path,
              image_key: message?.image_key,
              rel_files: message?.rel_files
            },
            nickName: message.role === 'user_proxy' ? userInfo.nickName : '',
            username: message.role === 'user_proxy' ? userInfo.username : ''
          },
          agent_role_id: this.currentRoleInfo?.id,
          create_time: dayjs().format('YYYY-MM-DD HH:mm:ss')
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // above from yeahz
    // async handleInit() {
    //   console.log('获取思维树、数据对齐内容')
    //   await GetDecision({
    //     scheme_id: this.$route.query.id,
    //     scheme_status: DECISION_TREE
    //   }).then((res) => {
    //     this.treeData = res.data.result?.decision_making_content || ''
    //     this.treeDataProcess = res.data.result?.sub_content || ''
    //     console.log('this.treeDataProcess 7', this.treeDataProcess)
    //     if (res.data.result?.decision_making_content) {
    //       // 源代码赋值写成判断了？ need to check 参照expertChat.vue
    //       this.updateTaskStatus(DECISION_TREE, 1)
    //       this.updateTaskProcess(DECISION_TREE, res.data.result?.sub_content)
    //     }
    //   })
    //   await GetDecision({
    //     scheme_id: this.$route.query.id,
    //     scheme_status: 'decision_ability'
    //   }).then((res) => {
    //     const codeData = res.data.result?.decision_making_content || ''
    //     if (
    //       res.data.result?.ext_info?.deploy_status &&
    //       res.data.result?.ext_info?.deploy_status === 'deployed'
    //     ) {
    //       if (this.alignTask.status === 1) {
    //         this.updateTaskStatus(CODE_DEPLOY, 1)
    //         this.updateTaskStatus(CODE_TEST, 1)
    //       }
    //       if (
    //         res.data.result.ext_info.code_analysis_status ||
    //         res.data.result.ext_info.code_params
    //       ) {
    //         console.log('1已经有参数了')
    //         this.updateTaskStatus(CODE_ANALYSIS, 1)
    //         this.updateTaskProcess(CODE_ANALYSIS, res.data.result.ext_info.sub_content || '')
    //       } else {
    //         console.log('1没有参数，触发流信息')
    //         this.updateTaskStatus(CODE_ANALYSIS, 0)
    //         this.updateTaskProcess(CODE_ANALYSIS, '')
    //       }
    //     }
    //     if (codeData) {
    //       if (this.alignTask.status === 1) {
    //         console.log('代码生成结果处理', res.data.result?.sub_content)
    //         const nextTask = this.getNextTask(ALIGN_DATA_GENERATE)
    //         if (nextTask) {
    //           if (nextTask === this.codeAbilityTask) {
    //             nextTask.status = 1
    //             nextTask.process = res.data.result?.sub_content
    //           }
    //         }
    //       }
    //     } else {
    //       this.updateTaskStatusesAfter(ALIGN_DATA_GENERATE, 0)
    //     }
    //   })
    // },
    handleInitFlowData() {
      this.flowData.forEach((item) => {
        item.status = 0
      })
    },
    saveSchemeGenerateFn(val) {
      saveSimpleSchemeGenerate({ session_id: this.sessionId, messages: val })
    },
    editFn() {
      this.editModalVisable = true
    },
    // 显示任务执行窗口
    showTaskModal() {
      this.taskModalVisable = true
    },
   async updateEditModal(val) {
      // this.schemeDetailById()
      // Bus.$emit('operations-research',val || { ...this.$route.query })
      // await this.queryDetail()
      // await this.getChatIsUseing()
      // this.editModalVisable = false
    },
    // 关闭任务执行窗口，同时将最小化窗口也关闭显示
    updateTaskModal(val) {
      this.miniFlag = false
      this.taskModalVisable = false
      if (val === 'finish') {
        this.$refs.planChatRef && this.$refs.planChatRef.changeViews(1)
        // this.activeStep = 1
      }
    },
    // 显示最小化任务执行进度
    updateTaskMiniModal() {
      this.miniFlag = true
      this.taskModalVisable = false
    },
    handleSendMsg(val) {
      console.log(val, '发送的信息')
      this.saveSchemeGenerateFn(val)
    },
    // handleGenerate() {
    //   // startDecisionTreeGenerateRequest({ session_id: this.sessionId })

    //   generalExecute({
    //     session_id: this.sessionId,
    //     instruction_code: this.treeTask.execute_instruction
    //   })
    // },
    // regenerateTree() {
    //   this.treeData = ''
    //   this.treeProcess = ''
    // },
    // 2. 下一步跳转逻辑：
    // 2.1. 任务执行未触发时: 触发按任务执行，打开‘执行任务’弹窗。
    // 2.2. 当所有任务都执行完成且成功时，跳转至能力测试页面。
    // 2.3. 任务未全部执行成功且未全部完成时，打开‘执行任务’弹窗。
    handleUpdateStep(v) {
      // need to check, 这里清空是啥意思啊？我要参加hqw的吧，hqw没有清空，那我也不清空了
      // this.treeData = ''
      this.changeViews(v)
    },
    changeViews(val) {
      // 向外层页面传递消息
      console.log('当前在第几步', val)
      this.codeAnalysisDataStatus = '0'
      window.parent.postMessage(JSON.stringify({ stepIndex: val }), '*')
      this.activeStep = val
    },
    async schemeDetailById() {
     if( this.$route.query.id == null || this.$route.query.id == undefined || this.$route.query.id == '' ) return
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.schemeInfo = res.data.result || { ...this.$route.query }
            console.log('详情xxxxxxx', this.schemeInfo)
            // this.schemeInfo.agent_scene_code = 'digital_twin_assistant_scene'
            this.emergentScene = res.data.result?.agent_scene_id
            this.eventSource && this.eventSource.close()
            this.eventSource = null
            this.researchEdit(this.schemeInfo)
            // Bus.$emit('operations-research', this.schemeInfo)
            this.totalWidth = document.getElementById('chatContainer').getBoundingClientRect().width
            this.leftWidth = document.getElementById('left-content').getBoundingClientRect().width
            this.rightWidth = document.getElementById('right-content').getBoundingClientRect().width
            console.log('宽度xxxxx', this.rightWidth)
            this.taskStatus = this.$route.query.status
            await this.queryTask()

            // below from yeahz
            // await this.queryAgentAvator();
            console.log('queryAgentAvator xxxx')
            console.log('val--------------2')
            await this.queryDetail()
            console.log('hello queryDetail')
            // above from yeahz
            await this.getChatIsUseing()
            console.log('操作系统', window.navigator.userAgent)
          }
        })
        .catch((_err) => {
          // this.$message({
          //   type: 'error',
          //   message: _err.data?.msg || '接口异常!'
          // });
          console.log(_err.data?.msg || '接口异常!')
        })
    },

    handleProcessSchemeData(message) {
      if (message.action === 'start') {
        this.optionDataProcess = message.data
      } else {
        this.optionDataProcess = this.optionDataProcess + message.data
      }
      if (message.action === 'end') {
        console.log('方案生成过程的数据======', this.optionDataProcess)
      }
    },

    handleProcessTreeData(message) {
      if (message.action === 'start') {
        this.treeDataProcess = message.data
        console.log('this.treeDataProcess 8', this.treeDataProcess)
        this.treeDataProcessNodes = [{ node_id: '1', data: message.data || '' }]
        console.log('this.treeDataProcessNodes 1', this.treeDataProcessNodes)
        console.log('message 1', message)
      } else {
        // 如果有node_id代表我分组任务流程过程
        if (message.node_id) {
          const filtersData = this.treeDataProcessNodes.filter(
            (item) => item.node_id === message.node_id
          )
          if (filtersData.length) {
            const newData = filtersData[0].data + message.data
            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === message.node_id) {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            this.treeDataProcessNodes = temp
            console.log('this.treeDataProcessNodes 2', this.treeDataProcessNodes)
          } else {
            this.treeDataProcessNodes.push({ node_id: message.node_id, data: message.data })
          }
          // console.log('分组数据', this.treeDataProcessNodes);

          const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
          // console.log('拼接后的', conData);
          this.treeDataProcess = conData
          console.log('this.treeDataProcess 9', this.treeDataProcess)
        } else {
          const filtersData = this.treeDataProcessNodes.filter((item) => item.node_id === '1')
          if (filtersData.length) {
            console.log('this.treeDataProcessNodes', this.treeDataProcessNodes)
            console.log('this.filtersData', filtersData)
            const newData = filtersData[0].data + message.data
            console.log('message.data 1111', message.data)
            console.log('newData 000', newData)

            const temp = this.treeDataProcessNodes.map((item) => {
              if (item.node_id === '1') {
                return {
                  node_id: item.node_id,
                  data: newData
                }
              } else {
                return item
              }
            })
            console.log('temp 000', temp)
            this.treeDataProcessNodes = temp
            console.log('this.treeDataProcessNodes 3', this.treeDataProcessNodes)

            console.log('this.treeDataProcessNodes 22222', this.treeDataProcessNodes)
            // need to check, 后面可能要判断下，是否不需要这里的join, 感觉是完全一样的。
            const conData = this.treeDataProcessNodes.map((item) => item.data).join('')
            console.log('conData 111', conData)
            // console.log('拼接后的', conData);
            // console.log('分组数据', this.treeDataProcessNodes);
            this.treeDataProcess = conData
            console.log('this.treeDataProcess 4', this.treeDataProcess)
            console.log('conData', conData)
          } else {
            this.treeDataProcess = this.treeDataProcess + message.data
          }
        }
      }
      if (message.action === 'end') {
        console.log('思维树生成过程的数据======', this.treeDataProcess)
      }
    },
    handleProcessPlanDetail(message) {
      // this.planStatus = 1
      if (message.action === 'start') {
        this.planData = message.data
      } else {
        this.planData = this.planData + message.data
      }
      if (message.action === 'end') {
        // this.planStatus = 2
      }
    },
    handleProcessTreeDetail(message) {
      if (message.action !== 'end') {
        this.treeStatus = 0
      }
      if (message.action === 'start') {
        console.log('思维树生成-----start')
        this.treeData = message.data
      } else {
        this.treeData = this.treeData + message.data
      }
      if (message.action === 'end') {
        console.log('思维树生成-----end')
        this.treeStatus = 2
        //思维树过程结束，开始下一个任务yeahz
        this.updateTaskStatus(DECISION_TREE, 1)
        const nextTask = this.getNextTask(DECISION_TREE)
        setTimeout(() => {
          if (nextTask) {
            this.updateNextTaskStatus(DECISION_TREE, 3)
          }
        }, 2000);
      }
    },
    handleAbilityTestData(message){
      if (message.action === 'start') {
        this.CodeTestData.status = 3
        this.CodeTestData.data = message.data
      }
      if (message.action === 'running') {
        this.CodeTestData.data += message.data
      }
      if (message.action === 'end') {
        this.CodeTestData.status = 1
        console.log('this.CodeTestData.data', this.CodeTestData.data);
      }
    },
    handleCodeTestData(message){
      console.log("啊？？啊啊 handleCodeTestData",);
      if (message.action === 'start') {
        console.log('CodeTest_New-----start')
        this.updateTaskSummarize(ABILITY_CHECK, message.data)
      } else {
        this.updateTaskSummarize(ABILITY_CHECK, this.codeTestNewTask.summarize +  message.data)
        console.log('CodeTest_New-----data', this.codeTestNewTask.summarize);
      }
      if (message.action === 'end') {
        if (this.isValidJson(this.codeTestNewTask.summarize)) {
          // 如果是有效的 JSON 字符串，解析并格式化
          const parsedData = JSON.parse(this.codeTestNewTask.summarize);
          this.updateTaskSummarize(ABILITY_CHECK, JSON.stringify(parsedData, null, 2))
        } else {
          console.warn('无效的 JSON 数据:', this.codeTestNewTask.summarize);
          // 可选择保留原始数据或做其他处理
        }
        console.log('CodeTest_New-----end',this.codeTestNewTask.summarize)
        //思维树过程结束，开始下一个任务yeahz
        this.updateTaskStatus(ABILITY_CHECK, 1)
        const nextTask = this.getNextTask(ABILITY_CHECK)
        if (nextTask) {
          this.updateTaskStatus(nextTask.runCode, 3)
          this.generalSSEfnExec(nextTask.runCode)
        }
      }
    },
    handleAbilityDetail(message) {
      if (message.action !== 'end') {
        // this.treeStatus = 0
      }
      if (message.action === 'start') {
        console.log('代码生成-----start')
        this.codeData = message.data
      } else {
        this.codeData = this.codeData + message.data
      }
    },
    updateDeviceId(val) {
      this.deviceId = val
      try {
        if (val) {
          // 根据方案查询设备id
          queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.tablesList = res.data?.result || []
              console.log('设备仓库列表', this.tablesList)
              if (this.tablesList.length === 0) {
                this.tablesList = []
              } else {
                queryPointsByDeviceId({
                  device_id: res.data?.result[0].device_id,
                  scheme_id: this.$route.query.id
                }).then((pres) => {
                  this.pointsData = pres.data.result || []
                })
              }
            } else {
              this.tablesList = []
            }
          })
        }
        // 查询此设备的测点数据
      } catch (error) {}
    },
    // 获取模版ID，用于数据对齐分析
    async queryTemplate() {
      queryTempIfFromScene({ scheme_id: this.$route.query.id }).then((res) => {
        console.log('获取模版ID接口-', res.data, this.updateStart)
        this.templateId = res.data?.id || ''
      })
      queryTempIfFromScene2({ scheme_id: this.$route.query.id }).then((res) => {
        console.log('获取模版ID接口-', res.data, this.updateStart)
        this.preTemplateId = res.data?.id || ''
      })
      try {
        checkByDeviceId({ scheme_id: this.$route.query.id }).then((gres) => {
          if (gres.status === 200 && gres.data.code === 200) {
            // 根据方案查询设备id
            queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                this.tablesList = res.data?.result || []
                console.log('设备仓库列表', this.tablesList)
                if (this.tablesList.length === 0) {
                  this.tablesList = []
                } else {
                  queryPointsByDeviceId({
                    device_id: res.data?.result[0].device_id,
                    scheme_id: this.$route.query.id
                  }).then((pres) => {
                    this.pointsData = pres.data.result || []
                  })
                }
              } else {
                this.tablesList = []
              }
            })
          }
        })
      } catch (error) {}

      queryDuiqiTags({
        keyword: ''
      }).then((res) => {
        if (res.data) {
          this.tagList = res.data
          this.allTagList = res.data
          console.log('api列表', res.data)
        } else {
          this.tagList = []
          this.allTagList = []
        }
      })
    },
    // 方案明细变更后传递到外层处理的
    async handleUpdateScheme(text) {
      this.planData = text
    },
    // 这个和modelParamExtraction没关系
    async handleUpdateTreeData(text) {
      console.log('接收最新的思维树内容', text)
      this.treeData = text
    },
    // 更新任务列表数据,主要用在代码部署、代码测试更新状态
    // 终止流任务
    stopAbilityGen() {
      this.miniFlag = false
      this.taskModalVisable = false
      startStopThinking({ session_id: this.sessionId })
      console.log('---source', source)
      source.cancel()
      source = cancelToken.source()
      this.eventSource && this.eventSource.close()
      for (const task of this.flowData){
        if (task.status === 3){
          this.getTaskByRunCode(task.runCode)
          this.updateTaskStatus(task.runCode, 2)
          this.updateTaskStatusesAfter(task.runCode, 0)
        }
      }
      // if (this.treeTask && this.treeTask.status === 3) {
      //   // this.updateTaskStatus(DECISION_TREE, 2)
      //   this.updateTaskStatusesAfter(DECISION_TREE, 0)
      // }
      // // 终止的是数据分析
      // if (this.alignAnalysisTask && this.alignAnalysisTask.status === 3) {
      //   this.updateTaskStatus(ALIGN_ANALYSIS, 2)
      //   this.updateTaskStatusesAfter(this.alignAnalysisTask, 0)
      //   const temp3 = []
      //   console.log('根据设备id查询测点数据', this.tablesList)
      //   this.tablesList
      //     .filter((item) => this.formData.chooseData.includes(item.device_name))
      //     .map(async (item) => {
      //       temp3.push(item?.model_type ? item.device_id : '')
      //     })
      //   console.log('家人们，到底走的哪个地方啊？saveAlignType 01')

      //   saveAlignType({
      //     align_type: {
      //       table: temp3,
      //       status: 2,
      //       tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
      //       manual_input: this.formData.manual_input,
      //       ansData: JSON.stringify(this.runStreamList)
      //     },
      //     scheme_id: this.$route.query.id
      //   })
      //   console.log('停止数据分析', this.flowData)
      // }
      // // 终止的是数据对齐
      // if (this.alignTask && this.alignTask.status === 3) {
      //   this.updateTaskStatus(ALIGN_DATA_GENERATE, 2)
      //   this.updateTaskStatusesAfter(ALIGN_DATA_GENERATE, 0)
      //   console.log('停止数据对齐', this.flowData)
      // }
      // // 终止的是代码生成
      // if (this.codeAbilityTask && this.codeAbilityTask.status === 3) {
      //   this.updateTaskStatus(DECISION_MAKING_GENERATE, 2)
      //   this.updateTaskStatusesAfter(DECISION_MAKING_GENERATE, 0)
      //   console.log('停止代码生成', this.flowData)
      // }
      // // 终止的是代码部署
      // if (this.codeDeployTask && this.codeDeployTask.status === 3) {
      //   this.updateTaskStatus(CODE_DEPLOY, 2)
      //   this.updateTaskStatusesAfter(CODE_DEPLOY, 0)
      //   console.log('停止代码部署', this.flowData)
      // }
      // if (this.codeTestTask && this.codeTestTask.status === 3) {
      //   this.updateTaskStatus(CODE_TEST, 2)
      //   this.updateTaskStatusesAfter(CODE_TEST, 0)
      //   console.log('停止代码测试', this.flowData)
      // }
      // // 终止的是代码分析
      // if (this.codeAnalysisTask && this.codeAnalysisTask.status === 3) {
      //   this.updateTaskStatus(CODE_ANALYSIS, 2)
      //   this.updateTaskStatusesAfter(CODE_ANALYSIS, 0)
      //   console.log('停止代码生成', this.flowData)
      // }
      this.processStatus = 0
      setTimeout(() => {
        this.getChatIsUseing()
        // this.querySchcemTask()
        this.queryBatchTasksResult()
      }, 1000)
    },
    // 这个也是wills的。这是简易生产的
    async reAlignData() {
      const temp2 = []
      const temp3 = []
      console.log('根据设备id查询测点数据', this.tablesList)
      this.tablesList
        .filter((item) => this.formData.chooseData.includes(item.device_name))
        .map(async (item) => {
          const promise1 = queryPointsByDeviceId({
            device_id: item.device_id,
            scheme_id: this.$route.query.id
          })
          temp3.push(item?.model_type ? item.device_id : '')
          temp2.push(promise1)
        })
      // cosnole.log('resultF', resultF);
      const table = [...temp3]
      let rtemp = []
      Promise.all(temp2).then((results) => {
        console.log('获取测试数据方法results==', results, table)
        results.forEach((resList, rindex) => {
          if (resList) {
            const newarr = rtemp.concat(resList.data?.result || [])
            rtemp = newarr
          }
        })
        this.$nextTick(async () => {
          console.log('rtemp', rtemp)
          console.log('table---', table, this.formData.tag_ids)
          console.log('家人们，到底走的哪个地方啊？saveAlignType 02')

          await saveAlignType({
            align_type: {
              table: table,
              status: 1,
              tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
              table: this.formData.chooseData.length && this.alignAnalysisTask.align_fields.includes('table') ? this.formData.chooseData : null,
              rule_tag_ids: this.formData.chooseData.length && this.alignAnalysisTask.align_fields.includes('rule_api') ? this.formData.chooseData : null,
              manual_input: this.formData.manual_input,
              ansData: JSON.stringify(this.runStreamList)
            },
            scheme_id: this.$route.query.id
          })
          // 自动执行数据对齐
          const params = {
            // table: JSON.stringify(temp2),
            // tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
            // manual_input: this.formData.manual_input,
            session_id: this.sessionId,
            table_schema: rtemp.length ? JSON.stringify(rtemp) : '',
            align_type: {
              table: table,
              status: 1,
              tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
              table: this.formData.chooseData.length && this.alignAnalysisTask.align_fields.includes('table') ? this.formData.chooseData : null,
              rule_tag_ids: this.formData.chooseData.length && this.alignAnalysisTask.align_fields.includes('rule_api') ? this.formData.chooseData : null,
              manual_input: this.formData.manual_input,
              ansData: JSON.stringify(this.runStreamList)
            }
          }
          console.log('params 00', params);
          await startAlignDataGenerate(params)
        })
      })
    },
    // 从数据对齐分析开始执行
    handleReStart(val, deviceid) {
      // 通知后端这是第一次执行, 打开任务执行分窗口
      if (val === 'startrun') {
        const filter = this.flowData.filter((item) => item.status)
        console.log("filter",filter);
        if (filter.length === 0) {
          // 显示数据预处理窗口结果
          // this.alignPreVisable = true;
          // this.queryDataPreStart();
          this.handelCofirmAlignPre(true)
        } else {
          this.taskModalVisable = true
          console.log('任务正在执行中')
        }
      } else {
        this.RunfirstStepTask()
      }
    },
    // 触发自动对齐分析  且hqw是不要这个步骤的，所以这里和modelParamExtraction啥的五官
    async queryStart() {
      // 根据方案查询设备id
      console.log('设备仓库列表1111111111111111', this.tablesList)
      await queryDeviceIds({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.tablesList = res.data?.result || []
          console.log('设备仓库列表', this.tablesList)
          if (this.tablesList.length === 0) {
            this.tablesList = []
          } else {
            queryPointsByDeviceId({
              device_id: res.data?.result[0].device_id,
              scheme_id: this.$route.query.id
            }).then((pres) => {
              this.pointsData = pres.data.result || []
            })
          }
        } else {
          this.tablesList = []
        }
      })

      this.loading = true
      this.runStreamList = []
      await queryTaskByTemp({
        template_id: this.templateId,
        scheme_detail: this.planData, // 方案明细
        mind_tree: this.treeData, // 思维树内容
        scheme_detail_optimize: this.getTaskByRunCode(SCHEME_OPTIMIZE)?.summarize,
        device_info: JSON.stringify(this.tablesList[0])
      }).then(async (res) => {
        console.log('需执行的任务列表', res.data)
        this.allRunTasks = res.data.new_tasks || []
        this.runId = res.data.run_id || ''
        res.data.new_tasks.forEach(async (item, index) => {
          this.runStreamList.push({
            name: item.name,
            content: '',
            status: 'start',
            type: 1,
            ...item
          })
        })
      })
      const temp = []
      console.log('runStreamListxxxx', this.runStreamList)
      if (this.runStreamList.length) {
        this.allSuccess = false
        await this.runExcute(this.runStreamList[0].id, this.runStreamList[0].order, 0)
      }
      console.log('promise1', temp)
    },
    // 这个函数目前是wills的
    async runExcute(id, order, index) {
      const url = process.env.VUE_APP_AGENT_API.startsWith('/')
        ? window.location.origin +
          process.env.VUE_APP_AGENT_API +
          '/api/agent/v2/manual_task/execute'
        : process.env.VUE_APP_AGENT_API + '/api/agent/v2/manual_task/execute'
      console.log('url', url)
      await this.$axios
        .post(
          url,
          {
            template_id: this.templateId,
            run_id: this.runId,
            agent_id: this.runId,
            task_id: id,
            order: order
          },
          {
            responseType: 'stream',
            baseURL: process.env.VUE_APP_AGENT_API,
            headers: {
              whiteuservalidate: 'False'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target
              const { responseText } = xhr
              this.$nextTick(() => {
                // console.log('----流----',this.runStreamList[index])
                this.runStreamList[index].status = 'running'
                this.runStreamList[index].content = responseText
                const temp = document.getElementById('taskBox')
                if (temp) {
                  temp.scrollIntoView({ block: 'end', inline: 'nearest' })
                  temp.scrollTop = temp.scrollHeight + 200
                }
              })
            },
            onError: function (error) {
              // 处理流错误
              console.error(error)
              this.runStreamList[index].status = 'error'
              this.runStreamList[index].content = ''
              this.updateTaskStatus(ALIGN_ANALYSIS, 2)
            }
          }
        )
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流', response)
          this.runStreamList[index].status = 'success'
          this.$nextTick(() => {
            const temp = document.getElementById('taskBox')
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' })
              temp.scrollTop = temp.scrollHeight + 700
            }
          })
          if (this.runStreamList[index].name === '数据仓库对齐') {
            if (this.runStreamList[index].content) {
              const valTemp = eval(this.runStreamList[index].content)
              if (valTemp && valTemp.length) {
                this.formData.cangku = true
                const filters = this.tablesList
                  .filter((item) => valTemp.includes(item.device_id))
                  .map((item) => item.device_name)
                this.formData.chooseData = filters
                console.log('库表的选择', valTemp, filters)
                this.runStreamList[index].content = JSON.stringify(filters)
                // this.$set(this.formData, 'chooseData', filters || []);
              } else {
                this.formData.cangku = false
                this.formData.chooseData = []
              }
            } else {
              this.formData.cangku = false
              this.formData.chooseData = []
            }
          }
          if (this.runStreamList[index].name === '规则对齐标签') {
            if (this.runStreamList[index].content) {
              const valTemp = eval(this.runStreamList[index].content)
              if (valTemp && valTemp.length) {
                this.formData.cangku = true;
                this.formData.chooseData = valTemp;
                console.log('库表的选择', valTemp);
                console.log('库表的选择 ', valTemp);
                this.$set(this.formData, 'chooseData', valTemp || []);
              } else {
                this.formData.cangku = false;
                this.formData.chooseData = []
              }
            } else {
              this.formData.cangku = false;
              this.formData.chooseData = []
            }
          }
          if (this.runStreamList[index].name === '算法API对齐') {
            if (this.runStreamList[index].content) {
              let valTemp = []
              try {
                valTemp = JSON.parse(this.runStream2List[index].content)
                console.log('runStream2List--- 00', valTemp, runStream2List)
              } catch (error) {
                valTemp = [this.runStreamList[index].content]
                console.log('valTemp---', valTemp, this.runStreamList)
              }
              console.log('valTemp---', valTemp)
              console.log('this.allTagList', this.allTagList)

              // 这里valTemp不是空，但this.allTagList没有与之一致的
              if (valTemp && valTemp.length) {
                const filters = []
                console.log(
                  'filters valTemp--- 00',
                  filters,
                  'valTemp:',
                  valTemp,
                  'this.allTagList:',
                  this.allTagList
                )
                if (Array.isArray(valTemp)) {
                  console.log('filters valTemp--- 01', filters, valTemp, this.allTagList)
                  const normalizedValTemp = JSON.parse(valTemp)
                  normalizedValTemp?.forEach((teItem) => {
                    const ffilter = this.allTagList.filter((tag) => {
                      return tag.name === teItem
                    })
                    if (ffilter && ffilter.length) {
                      filters.push(ffilter[0].id)
                    }
                  })
                } else {
                  const ffilter = this.allTagList.filter((tag) => tag.name === valTemp)
                  if (ffilter && ffilter.length) {
                    filters.push(ffilter[0].id)
                  }
                }

                this.formData.api = true

                this.formData.tag_ids = [...filters] || []

                this.$set(this.formData, 'tag_ids', filters || '')
                console.log('少选出的', filters, this.formData.tag_ids)
              } else {
                this.formData.api = false
                this.formData.tag_ids = []
              }
            } else {
              this.formData.api = false
              this.formData.tag_ids = []
            }
          }
          if (index + 1 < this.runStreamList.length) {
            this.$nextTick(async () => {
              await this.runExcute(
                this.runStreamList[index + 1].id,
                this.runStreamList[index + 1].order,
                index + 1
              )
            })
          } else {
            this.allSuccess = true
            this.loading = false
            console.log('最后的结果', this.runStreamList)
            console.log('this.allSuccess 00', this.allSuccess)
            this.formData.manual_input = true
            // need to check
            this.updateTaskStatus(ALIGN_ANALYSIS, 1)
            this.$nextTick(() => {
              const temp = document.getElementById('taskBox')
              if (temp) {
                temp.scrollIntoView({ block: 'end', inline: 'nearest' })
                temp.scrollTop = temp.scrollHeight + 700
              }
              this.reAlignData()
            })
          }
        })
        .catch(async (err) => {
          console.log('这里收到错误了123', err)
          this.allSuccess = true
          this.loading = false
          this.runStreamList[index].status = 'error'
          this.runStreamList[index].content = ''
          console.log('收到错误的结果', this.runStreamList)
          this.$nextTick(async () => {
            const temp = document.getElementById('taskBox')
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' })
              temp.scrollTop = temp.scrollHeight + 700
            }
            const temp3 = []
            console.log('根据设备id查询测点数据', this.tablesList)
            this.tablesList
              .filter((item) => this.formData.chooseData.includes(item.device_name))
              .map(async (item) => {
                temp3.push(item?.model_type ? item.device_id : '')
              })
            console.log('家人们，到底走的哪个地方啊？saveAlignType 00')

            await saveAlignType({
              scheme_id: this.$route.query.id,
              align_type: {
                table: temp3,
                status: 2,
                tag_ids: this.formData.tag_ids.length ? this.formData.tag_ids : null,
                manual_input: this.formData.manual_input,
                ansData: JSON.stringify(this.runStreamList)
              }
            })
            // await updateLogTaskStatus({
            //   scheme_id: this.$route.query.id,
            //   task_name: ALIGN_ANALYSIS,
            //   task_status: 2
            // });
            this.formData.manual_input = true
            this.updateTaskStatus(ALIGN_ANALYSIS, 2)
            // need to check
            const tempStatus = this.codeAnalysisTask?.status
            this.updateTaskStatusesAfter(ALIGN_ANALYSIS, 0)
            if (tempStatus) {
              this.updateTaskStatus(CODE_ANALYSIS, tempStatus)
            }
          })
        })
    },
    async queryAlignData() {
      await queryAbilityMapping({ scheme_id: this.$route.query.id }).then((res) => {
        if (res.status === 200 && res.data.code * 1 === 200) {
          console.log('数据对齐结果', res)
          const configData = res.data.result?.config || {}
          const status = res.data.result.ability_status
          if (status === 'init') {
            console.log('数据对齐生成中')
            if (res.data.result?.align_type?.ansData) {
              try {
                this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData)
                console.log('对齐分析结果 00', this.runStreamList)
                if (this.runStreamList.filter((item) => item.status === 'error').length) {
                  this.updateTaskStatus(ALIGN_ANALYSIS, 2)
                } else {
                  this.updateTaskStatus(ALIGN_ANALYSIS, 1)
                }
              } catch (error) {
                this.runStreamList = []
                this.updateTaskStatus(ALIGN_ANALYSIS, 0)
              }
            } else {
              this.updateTaskStatus(ALIGN_ANALYSIS, 0)
            }
            // const tempStatus = this.codeAnalysisTask?.status

            this.updateTaskStatusesAfter(ALIGN_ANALYSIS, 0)
            // need to check ，这里修改了原来的逻辑，这里不再保留对codeAnalysisTask任务的保留，也设为0
            // if (tempStatus) {
            //   this.codeAnalysisTask = tempStatus
            // }
          } else if (status !== 'generating') {
            // if (res.data.result?.align_type?.ansData) {
            //   this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData) || []
            // }
            console.log('hello? ', res.data)
            if (res.data.result.align_type?.manual_input) {
              console.log('hello? 2222', res.data)
              this.formData.manual_input = true
            } else {
              console.log('hello? 333', this, this.formData)
              this.formData.manual_input = false
            }
            console.log('hello? 111', res.data)
            // 这里返回的tag_ids为null
            const filters = this.tablesList
              .filter((item) => res.data.result?.align_type?.table.includes(item.device_id))
              .map((item) => item.device_name)
            this.formData.chooseData = filters
            const tagtemp = []
            // 循环判断标签是否有被删除，被删除的就不显示了
            res.data.result?.align_type?.tag_ids?.forEach((titem) => {
              const filter = this.allTagList.filter((item) => item.id === titem)
              if (filter.length) {
                tagtemp.push(filter[0].id)
              }
            })
            this.formData.tag_ids = tagtemp


            try {
              this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData)
              console.log('对齐分析结果 01', this.runStreamList)
              if (this.runStreamList.filter((item) => item.status === 'error').length) {
                this.updateTaskStatus(ALIGN_ANALYSIS, 2)
              } else {
                this.updateTaskStatus(ALIGN_ANALYSIS, 1)
              }
            } catch (error) {
              this.runStreamList = []
              this.updateTaskStatus(ALIGN_ANALYSIS, 0)
            }
            // try {
            //   this.runStreamList = JSON.parse(res.data.result?.align_type?.ansData);
            //   console.log('对齐飞兮结果', this.runStreamList);
            // } catch (error) {
            //   this.runStreamList = [];
            // }
            const dataThs = configData?.header || {}
            const dataThDatas = Object.keys(dataThs).map((item) => {
              return { name: dataThs[item], field: item }
            })
            if (dataThDatas.length) {
              const temp =
                configData?.data?.map((item) => {
                  return {
                    ...item
                  }
                }) || []
              console.log('对齐数据结果', temp)
              this.dataAlignData = temp
              this.dataAlignDataStatus = 2

              console.log('对齐飞兮结果333', res.data.result?.align_type)
              if (this.alignAnalysisTask && this.alignAnalysisTask.status !== 2) {
                this.updateTaskStatus(ALIGN_ANALYSIS, 1)
                this.updateTaskStatus(ALIGN_DATA_GENERATE, 1)
                this.updateTaskProcess(ALIGN_DATA_GENERATE, res.data.result?.sub_content)
              } else {
                this.updateTaskStatus(ALIGN_ANALYSIS, 2)
                this.updateTaskProcess(ALIGN_DATA_GENERATE, res.data.result?.sub_content)
              }
            } else {
              this.dataAlignData = []
              this.dataAlignDataStatus = 0
              this.updateTaskProcess(ALIGN_DATA_GENERATE, res.data.result?.sub_content)
            }
          }
        }
      })
    },
    // 回显任务process和结果
    async queryBatchTasksResult() {
      this.phase_tasks = this.flowData
        .filter((item) => item.status === 1)
        .map((item) => item.runCode)
      console.log('this.phase_tasks 0', this.phase_tasks)
      await getbatchTasksResult({
        session_id: this.sessionId,
        scheme_id: this.$route.query.id,
        phase_tasks: this.phase_tasks
      }).then((res) => {
        console.log('queryBatchTasksResult res', res)
        if (res.status === 200 && res.data.code === 200) {
          for (const runCode in res.data.result) {
            console.log('res runCode', runCode)
            if (res.data.result[runCode]) {
              this.flowData.forEach((it, index) => {
              if (it.task_desc === runCode) {
                this.$set(this.flowData[index], 'status', res.data.result[runCode].status)
                this.$set(this.flowData[index], 'summarize_message',this.processJSON(res.data.result[runCode].summarize_message))
              }
            })
              const StringProcess_message = this.processJSON(res.data.result[runCode].process_message)
              const StringProcess_summarize = this.processJSON(res.data.result[runCode].summarize_message)
              this.updateTaskProcess(runCode, StringProcess_message)
              this.updateTaskSummarize(runCode, StringProcess_summarize)
            }
          }
          if (this.$refs.planChatRef && this.$refs.planChatRef.AllAbility){
            this.$refs.planChatRef.updateOptHeader2TabsList(this.$refs.planChatRef.AllAbility)
          }
        }
      })
    },
    // getbatchTasksResult
    async queryTaskList() {
      await getTaskListBySchemeId({
        session_id: this.sessionId,
        scheme_id: this.$route.query.id
      }).then((res) => {
        console.log('getTaskListBySchemeId res', res)
        if (res.status === 200 && res.data.code === 200) {
          // 目前这里去掉了第一项
          this.flowData = this.generateFlowDataFromApi(res.data.result) || []
          console.log('getTaskListBySchemeId flowdata', JSON.stringify(this.flowData, null, 2))
          try{
          this.schemeStatus = this.codeAbilityTask.execute_instruction
            switch (this.codeAbilityTask.execute_instruction) {
            case 'ability_generate':
              this.schemeStatus = 'decision_ability'
              break

            case 'rule_generate':
              this.schemeStatus = 'rule'
              break

            default:
              // 如果表达式的值不匹配任何 case，则执行这里的代码
              this.schemeStatus = ''
              break
          }
          console.log('this.schemeStatus 00', this.schemeStatus)
          }catch{

          }
        }
      })
    },
    // 回显任务状态
    // async querySchcemTask() {
    //   await getSchemePhaseTasks({ scheme_id: this.$route.query.id }).then(async (res) => {
    //     if (res.status === 200 && res.data.code === 200) {
    //       const mapData = {}
    //       res.data.result.forEach((item) => {
    //         mapData[item.task_name] = item.task_status
    //       })
    //       // 遍历当前的 flowData，根据 task_name 来匹配设置状态

    //       this.flowData.forEach(async (task) => {
    //         const taskStatus = mapData[task.runCode] || 0 // 根据 task_name (runCode) 设置状态
    //         task.status = taskStatus
    //       })

    //       if (mapData.model_param_extraction === 1) {
    //         console.log('22222222222222222333222')
    //         getTasksRes({
    //           scheme_id: this.$route.query.id,
    //           biz_type: 'model_param_extraction'
    //         }).then((res) => {
    //           this.modelParamExtractionData = res.data.content
    //           this.modelParamExtractionProcess = res.data.sub_content
    //           this.modelParamExtractionStatus = 2
    //           // 这部分的修改可以参考hqw, 之前他写的和思维树混在一起了，现在我将其变量分开了
    //           // this.flowData[0].status = 1;
    //           console.log(res, 'rrrrrrrrrrrrrrrrrr')
    //         })
    //       }

    //       if (mapData.modeling_info_structure === 1) {
    //         getTasksRes({
    //           scheme_id: this.$route.query.id,
    //           biz_type: 'modeling_info_structure'
    //         }).then((res) => {
    //           if (this.modelingInfoStructure) {
    //             this.modelingInfoStructure.process = res.data.sub_content
    //             this.modelingInfoStructureData = res.data.content
    //             this.modelingInfoStructureStatus = 2
    //             // this.flowData[1].status = 1;
    //           }
    //         })
    //       }

    //       if (mapData.math_model_generate === 1) {
    //         getTasksRes({
    //           scheme_id: this.$route.query.id,
    //           biz_type: 'math_model_generate'
    //         }).then((res) => {
    //           this.mathModelGenerateProcessData = res.data.sub_content
    //           this.mathModelGenerateData = res.data.content
    //           this.mathModelGenerateStatus = 2
    //           // this.flowData[2].status = 1;
    //         })
    //       }

    //       if (this.codeAnalysisTask?.status === 1) {
    //         this.updateTaskStatus(DECISION_TREE, 1)
    //       }
    //       if (mapData.align_data_generate === 1 || mapData.align_data_generate === 2) {
    //         // 处理数据对齐分析ƒ
    //         this.updateTaskStatus(ALIGN_ANALYSIS, 1) // 动态设置 align_data_generate 的状态为 1
    //         console.log('11111111111 queryAlignData')
    //         await this.queryAlignData() // 异步处理数据对齐分析
    //       }

    //       // 处理不同任务的特殊逻辑
    //       if (mapData.align_analysis === 1 && mapData.align_data_generate === 1) {
    //         await GetDecision({
    //           scheme_id: this.$route.query.id,
    //           scheme_status: DECISION_TREE
    //         }).then((res) => {
    //           this.treeData = res.data.result?.decision_making_content || ''
    //           this.treeDataProcess = res.data.result?.sub_content || ''
    //           console.log('this.treeDataProcess 5', this.treeDataProcess)
    //           if (res.data.result?.decision_making_content) {
    //             this.updateTaskStatus(DECISION_TREE, 1)
    //             this.updateTaskProcess(DECISION_TREE, res.data.result?.sub_content)
    //           }
    //         })
    //       } else {
    //         console.log(
    //           '==== Object alignAnalysisTask newVal ====',
    //           JSON.stringify(this.flowData, null, 2)
    //         )
    //         this.updateTaskStatusesAfter(ALIGN_DATA_GENERATE, 0)
    //         console.log(
    //           '==== Object alignAnalysisTask111111 newVal ====',
    //           JSON.stringify(this.flowData, null, 2)
    //         )
    //       }
    //       if (mapData.decision_tree === 2 || mapData.decision_tree === 0) {
    //         this.updateTaskStatusesAfter(DECISION_TREE, 0)
    //       }
    //       // 检查所有关键任务的状态是否为 1 来决定后续任务
    //       // if (
    //       //   mapData.decision_tree === 1 &&
    //       //   mapData.align_analysis === 1 &&
    //       //   mapData.code_analysis === 1
    //       // ) {
    //       //   // 检查 code_analysis 的内容是否存在
    //       //   await GetDecision({
    //       //     scheme_id: this.$route.query.id,
    //       //     scheme_status: this.schemeStatus
    //       //   }).then((res) => {
    //       //     const codeData = res.data.result?.decision_making_content || ''
    //       //     if (
    //       //       res.data.result?.ext_info?.deploy_status &&
    //       //       res.data.result?.ext_info?.deploy_status === 'deployed'
    //       //     ) {
    //       //       // 如果 codeData 存在，并且 align_data_generate 的状态为 1，则继续处理后续任务

    //       //       if (this.alignTask?.status === 1) {
    //       //         this.updateTaskStatus(CODE_DEPLOY, 1)
    //       //         this.updateTaskStatus(CODE_TEST, 1)
    //       //       }

    //       //       if (
    //       //         res.data.result.ext_info.code_analysis_status ||
    //       //         res.data.result.ext_info.code_params
    //       //       ) {
    //       //         this.updateTaskStatus(CODE_ANALYSIS, 1)
    //       //         this.updateTaskProcess(CODE_ANALYSIS, res.data.result.ext_info.sub_content || '')
    //       //       } else {
    //       //         this.updateTaskStatus(CODE_ANALYSIS, 0)
    //       //         this.updateTaskProcess(CODE_ANALYSIS, '')
    //       //       }
    //       //     }
    //       //     if (codeData) {
    //       //       if (this.alignTask.status === 1) {
    //       //         // console.log('代码生成结果处理', res.data.result?.sub_content);
    //       //         this.updateTaskStatus(DECISION_MAKING_GENERATE, 1)
    //       //         this.updateTaskProcess(DECISION_MAKING_GENERATE, res.data.result?.sub_content)
    //       //       }
    //       //     } else {
    //       //       // 如果 codeData 不存在，重置后续任务的状态
    //       //       this.updateTaskStatus(DECISION_MAKING_GENERATE, 0)
    //       //       this.updateTaskStatus(CODE_DEPLOY, 0)
    //       //       this.updateTaskStatus(CODE_TEST, 0)
    //       //       this.updateTaskStatus(CODE_ANALYSIS, 0)
    //       //     }
    //       //   })
    //       // }
    //       if (this.treeTask) {
    //         // 处理决策树的任务
    //         if (this.treeTask.status === 1) {
    //           await GetDecision({
    //             scheme_id: this.$route.query.id,
    //             scheme_status: DECISION_TREE
    //           }).then((res) => {
    //             this.treeData = res.data.result?.decision_making_content || ''
    //             this.treeDataProcess = res.data.result?.sub_content || ''
    //             console.log('this.treeDataProcess 6', this.treeDataProcess)
    //             this.updateTaskProcess(DECISION_TREE, res.data.result?.sub_content || '')
    //           })
    //         }
    //       }
    //       // console.log(
    //       //   '通过接口同步的 flowData:',
    //       //   this.flowData,
    //       //   JSON.stringify(this.flowData, null, 2)
    //       // )
    //     }
    //   })
    // },
    // 关闭数据预处理窗口
    handelCofirmAlignPre(val) {
      if (val) {
        // this.updateTaskStatus(DECISION_TREE, 3)
        startTask({ scheme_id: this.$route.query.id })
        this.taskModalVisable = true
        this.runTaskStatus = 1
        // 变为触发生成思维树
        this.RunfirstStepTask()
        // this.queryStart();
      }
      this.alignPreVisable = false
    },
    // 对齐分析前，数据确认窗口数据获取
    // hqw也不执行这个步骤，  modelParamExtraction相关的和这里没有关系
    async queryDataPreStart() {
      this.loading = true
      this.runStreamList2 = []
      await queryTaskByTemp({
        template_id: this.preTemplateId,
        scheme_detail: this.planData, // 方案明细
        mind_tree: this.treeData, // 思维树内容
        metrics_info: JSON.stringify(this.pointsData)
      }).then(async (res) => {
        console.log('数据确认需执行的任务列表', res.data)
        this.allRunTasks2 = res.data.new_tasks || []
        this.runId2 = res.data.run_id || ''
        res.data.new_tasks.forEach(async (item, index) => {
          this.runStreamList2.push({
            name: item.name,
            content: '',
            status: 'start',
            type: 1,
            ...item
          })
        })
      })
      const temp = []
      if (this.runStreamList2.length) {
        this.allSuccess = false
        await this.runPreExcute(this.runStreamList2[0].id, this.runStreamList2[0].order, 0)
      }
      console.log('promise1', temp)
    },
    async runPreExcute(id, order, index) {
      const url = process.env.VUE_APP_AGENT_API.startsWith('/')
        ? window.location.origin +
          process.env.VUE_APP_AGENT_API +
          '/api/agent/v2/manual_task/execute'
        : process.env.VUE_APP_AGENT_API + '/api/agent/v2/manual_task/execute'
      console.log('url', url)
      await this.$axios
        .post(
          url,
          {
            template_id: this.preTemplateId,
            run_id: this.runId2,
            agent_id: this.runId2,
            task_id: id,
            order: order
          },
          {
            responseType: 'stream',
            baseURL: process.env.VUE_APP_AGENT_API,
            headers: {
              whiteuservalidate: 'False'
            },
            cancelToken: source.token,
            onDownloadProgress: (event) => {
              const xhr = event.target
              const { responseText } = xhr
              this.$nextTick(() => {
                // console.log('----流----',this.runStreamList[index])
                this.runStreamList2[index].status = 'running'
                this.runStreamList2[index].content = responseText
              })
            },
            onError: function (error) {
              // 处理流错误
              console.error(error)
              this.runStreamList2[index].status = 'error'
              this.runStreamList2[index].content = ''
            }
          }
        )
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流', response)
          this.runStreamList2[index].status = 'success'
          if (index + 1 < this.runStreamList2.length) {
            this.$nextTick(async () => {
              await this.runPreExcute(
                this.runStreamList2[index + 1].id,
                this.runStreamList2[index + 1].order,
                index + 1
              )
            })
          } else {
            this.allSuccess2 = true
            this.loading = false
            console.log('最后的结果', this.runStreamList2)
            // 显示数据预处理窗口结果
            this.alignPreVisable = true
          }
        })
        .catch((err) => {
          console.log('这里收到错误了', err)
          this.allSuccess2 = true
          this.loading = false
          this.runStreamList2[index].status = 'error'
          this.runStreamList2[index].content = ''
          console.log('最后的结果', this.runStreamList2)
        })
    },
    // 代码生成过程数据  need to check, 这里是要采用哪个，采用wills的还是zhushouchat的代码过程？先采用wills的吧
    handleCodeProcesData(message) {
      if (message.action === 'start') {
        this.codeProcessData = message.data
      } else {
        this.codeProcessData = this.codeProcessData + message.data
      }
      if (message.action === 'end') {
        console.log('代码生成生成过程的数据======', this.codeProcessData)
      }
      if (message.action !== 'end') {
        this.processStatus = 1
      } else {
        this.processStatus = 2
      }
    },
    // 数据对齐过程数据
    handleAlignProcessData(message) {
      console.log('handleAlignProcessData 00')
      if (message.action === 'start') {
        this.alignProcessData = message.data
        this.alignProcessDataStatus = '1'
      } else {
        this.alignProcessData = this.alignProcessData + message.data
        this.alignProcessDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('数据对齐生成过程的数据======', this.alignProcessData)
        this.alignProcessDataStatus = '2'
      }
    },

    //   handleUpdateCodeAnalysis() {
    //   console.log('触发代码分析')
    //   startCodeAnalysis({ session_id: this.sessionId })
    // },
    // 代码分析
    // () {
    //   console.log('触发代码分析')
    //   generalExecute({
    //     session_id: this.sessionId,
    //     instruction_code: this.codeAnalysisTask.execute_instruction
    //   })
    // },

    handleCodeAnalysisProcessData(message) {
      if (message.action === 'start') {
        this.codeAnalysisProcessData = message.data
        this.codeAnalysisProcessDataStatus = '1'
      } else {
        this.codeAnalysisProcessData = this.codeAnalysisProcessData + message.data
        this.codeAnalysisProcessDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据======', this.codeAnalysisProcessData)
        this.codeAnalysisProcessDataStatus = '2'
      }
    },
    // 代码分析消息处理
    handleCodeAnalysisData(message) {
      if (message.action === 'start') {
        this.codeAnalysisData = message.data
        this.codeAnalysisDataStatus = '1'
      } else {
        this.codeAnalysisData = this.codeAnalysisData + message.data
        this.codeAnalysisDataStatus = '1'
      }
      if (message.action === 'end') {
        console.log('代码分析生成过程的数据前======', this.codeAnalysisData)
        if (this.isValidJson(this.codeAnalysisData)) {
          // 如果是有效的 JSON 字符串，解析并格式化
          const parsedData = JSON.parse(this.codeAnalysisData);
          this.codeAnalysisData = JSON.stringify(parsedData, null, 2);
        } else {
          console.warn('无效的 JSON 数据:', this.codeAnalysisData);
          // 可选择保留原始数据或做其他处理
        }
        console.log('代码分析生成过程的数据后======', this.codeAnalysisData)
        this.codeAnalysisDataStatus = '2'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.chatContainer {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  position: relative;
  flex-direction: column;
  .headerBox {
    background-color: #fff;
    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;
      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }
      .sence-tag {
        margin-left: 16px;
        padding: 0 8px;
        height: 24px;
        border-radius: 2px;
        max-width: calc(100vw - 380px);
        /* 设置文本溢出时的行为为省略号 */
        text-overflow: ellipsis;

        /* 设置超出容器的内容应该被裁剪掉 */
        overflow: hidden;

        /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
        white-space: nowrap;
        background: #ebf9ff;
        color: #318db8;
      }
      .right {
        .model-expert {
          margin-right: 10px;
          font-size: 14px;
          color: #4068d4;
          font-weight: bold;
        }
      }
    }
    .headerStep {
      position: relative;
      .myStep {
        background: #fff;
        padding: 13px 20%;
        :deep(.el-step__arrow) {
          margin: 0 16px;
          &::before {
            // content: '';
            // position: static;
            // height: 1px;
            // width: 100%;
            // background: #c8c9cc;
            // transform: none;
            // display: block;
            display: none;
          }
          &::after {
            display: none;
          }
        }
        :deep(.is-process) {
          color: #4068d4;
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: none;
            }
          }
        }
        :deep(.is-success) {
          color: #000;
          border-color: #4068d4;
          .el-icon-check {
            color: #4068d4;
          }
          .el-step__icon {
            color: #4068d4;
            &.is-text {
              border: 1px solid #4068d4;
            }
          }
        }
        .empty-space {
          width: 100%;
          height: 100%;
        }
      }
      .myProgress {
        padding: 13px 20%;
        width: 100%;
        position: absolute;
        z-index: 999;
        top: 0;
        :deep(.el-progress) {
          margin: 0 194px 0 110px;
        }
        :deep(.el-progress-bar__inner) {
          background: linear-gradient(149deg, #b1c6ff 0%, #2056ea 100%);
        }
        :deep(.el-progress-bar__innerText) {
          margin-top: -5px;
        }
      }
      .progressEmpty {
        :deep(.el-progress-bar__innerText) {
          color: #406bd4;
        }
      }
    }
  }
  &.chatContainerFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }
  }
  ::v-deep .el-button--mini {
    line-height: 0px !important;
    padding: 8px 6px !important;

    img {
      height: 16px;
      margin-top: -2px;
    }
  }
}
.stepBox {
  background: #ebeffa;
  border-radius: 2px;
  border: 1px solid #4068d4;
  padding: 8px 20px;
  margin: 16px 16px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #323233;
  .stepWrap {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .item {
    display: flex;
    align-items: center;
  }
  .full {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;

  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }

  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }

  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
</style>
<style lang="scss">
.my-message-box1 {
  :deep(.el-message-box__btns .el-button) {
    width: 62px !important;
  }

  :deep(.el-message-box__header .el-message-box__status) {
    display: block !important;
  }

  .el-message-box__content {
    padding: 10px 30px !important;
  }
}
</style>
