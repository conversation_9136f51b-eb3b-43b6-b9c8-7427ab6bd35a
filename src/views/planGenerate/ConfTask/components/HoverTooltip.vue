<!-- HoverTooltip.vue -->
<template>
 <div 
   v-show="visible"
   class="vscode-hover"
   :style="positionStyle"
 >
   <div class="hover-header">
     <!-- <span class="filename">{{ fileName }}</span> -->
     <span class="file-path">{{ filePath }}</span>
   </div>
   <!-- <div class="hover-content">
     <div>修改时间: {{ lastModified }}</div>
     <div>大小: {{ fileSize }}</div>
   </div> -->
 </div>
</template>

<script>
export default {
 props: ['fileName', 'filePath', 'lastModified', 'fileSize', 'position'],
 computed: {
   visible() {
     return !!this.fileName
   },
   positionStyle() {
     return {
       left: `${this.position.x}px`,
       top: `${this.position.y}px`,
       opacity: this.visible ? 1 : 0
     }
   }
 }
}
</script>

<style lang="scss">
.vscode-hover {
 position: fixed;
 color: #616161;
 background: #f3f3f3;
 border: 1px solid #E0E0E0 ;
 border-radius: 3px;
 padding: 0px 8px;
 box-shadow: 0 2px 8px rgba(0,0,0,0.15);
 z-index: 9999;
 line-height: 24px;
 transition: opacity 0.15s;

 .hover-header {
   display: flex;
   // gap: 8px;
   // margin-bottom: 6px;

   .filename {
     font-weight: 600;
     color: var(--text-primary);
   }

   .file-path {
     color: var(--text-secondary);
     font-size: 0.9em;
   }
 }

 .hover-content {
   color: var(--text-secondary);
   font-size: 0.85em;
   line-height: 1.4;
 }
}
</style>