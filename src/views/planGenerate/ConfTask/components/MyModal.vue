<template>
  <div>
    <el-dialog title="提示" :visible.sync="visible" width="30%" :before-close="handleClose">
      <span>此操作将【somebody】强制退出，是否继续</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MyModal',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹框打开时的逻辑
        console.log('弹框已打开')
      } else {
        // 弹框关闭时的逻辑
        console.log('弹框已关闭')
      }
    }
  },
  data() {},
  methods: {
    handleClose() {
      console.log('visible', this.visible)

      this.$emit('update:visible', false)
      console.log('visible', this.visiblevisible)
    },
    handleConfirm() {
      console.log('visible', this.visiblevisible)

      // 确定按钮的处理逻辑
      this.$emit('update:visible', false)
    }
  },
  mounted() {
    console.log('打开弹窗了吗')
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  padding: 24px 24px;
  border-bottom: 1px solid #e4e7ed;
}
</style>
