<template>
  <div class="flex">
  <div v-if="!showStatus" class="table_b">
    <el-table
      :data="tableData.data"
      style="max-width: calc(100% - 40px); overflow: auto;width: calc(100% - 40px)"
      :header-cell-style="{ background: '#F6F7FB', color: '#323233'}"
    >
      <el-table-column type="index" label="序号" width="70" fixed="left" />
      <el-table-column prop="version" label="版本号" />
      <el-table-column prop="create_date" label="创建时间" />
      <el-table-column prop="update_date" label="发布时间" />
      <el-table-column
        prop="status"
        label="实例状态"
      >
        <template slot-scope="{ row }">
          <Status
            :text="exampleStatusTypeMap[row?.status]?.text"
            :bg-color="exampleStatusTypeMap[row?.status]?.bgColor"
            :dot-color="exampleStatusTypeMap[row?.status]?.dotColor"
          />
        </template>
      </el-table-column>
      <el-table-column prop="updated_name" label="发布人" />
      <el-table-column
        prop=""
        label="操作"
        fixed="right"
        width="140"
      >
        <template slot-scope="{ row }">
          <el-link type="primary" :underline="false" @click="handleReload(row)">版本详情</el-link>
          <el-link type="primary" :underline="false" @click="handleStatus(row)">{{ row.status === 'offline' ? '上线' : '下线' }}</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData?.data?.length >0"  style="text-align: right; padding-top: 16px;width: calc(100% - 40px)">
        <el-pagination
          class="new-paper"
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[12, 24, 36, 48, 60]"
          :current-page.sync="tableData.page"
          :page-size="tableData.pageSize"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
  </div>
  <div v-else style="width:100%">
    <el-page-header style="margin-bottom:20px"  @back="() => showStatus = false" content=""></el-page-header>
    <ModelFile :schemeId="tableSearch.scheme_id" :version="version" ></ModelFile>
  </div>
</div>
</template>
<script type="text/javascript">
import Status from '@/components/Status/index.vue'
import {queryAbilityMarket,queryAbilityVersionNew,onLineStatus,offlineStatus} from '@/api/planGenerateApi.js'
import ModelFile from './modelFile.vue'
export default {
  name: 'version',
  components: { Status,ModelFile},
  data() {
    return {
      showStatus:false,
      tableSearch: {
        page:1,
        page_size:12,
        scheme_id: Number(this.$route.query.id),
      },
      version:'',
      tableData: {
        data: [],
        page: 1,
        pageSize: 12,
        total: 0,
      },
      exampleStatusTypeMap : {
        'offline': { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线'},
        'online': { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线'},
      }
    }
  },

  created() {
  },
  mounted() {
    this.queryAbilityVersionList()
  },
  methods: {
    handleReload(row) {
      this.version = row.version
      this.showStatus = true
    },
    handleStatus (v) {
      this.$confirm(
        `此操作将${v.status === 'offline' ? '上线' : '下线'}该版本, 是否继续?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          if (v.status === 'offline') {
            onLineStatus({
              version_id: v.id
            }).then((res) => {
              if(res?.data?.code === 200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败!'
                })
              }
            })
          } else {
            offlineStatus({
              version_id:v.id
            }).then((res) => {
              if(res?.data?.code ===200){
                this.$emit('updateDetail');
                this.queryAbilityVersionList()
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                })
              }else{
                this.$message({
                  type: 'error',
                  message: '操作失败: ' + res?.data?.msg
                })
              }
            })
          }

        })
        .catch(() => {

        })
    },
    handleSizeChange(val) {
      this.tableSearch.page = 1;
      this.tableSearch.page_size = val;
      this.queryAbilityVersionList();
    },
    handleCurrentChange(val) {
      this.tableSearch.page = val;
      this.queryAbilityVersionList();
    },
    // 实例信息
    async queryAbilityVersionList() {
      await queryAbilityVersionNew(this.tableSearch).then(res => {
        this.tableLoading = false
        if (res?.data?.code === 200) {
          this.tableData = { ...res?.data.result };
          this.tabelData.pageSize = this.tableSearch.page_size;
        } else {
          this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
        }
      }).catch(() => {
      })
    }
  }
}
</script>
<style lang="less" scoped>
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
:deep(.el-table__body-wrapper) .el-table__cell {
  cursor: pointer;
}
.flex{
    padding:20px;
    width:100%;
}
.table_b {
  position: absolute;
  width: 100%;
}
</style>
