<template>
  <div :style="!isHideIframe ? 'height: calc(100vh - 250px)' : 'height: calc(100vh - 210px)'">
    <div v-if="showList">
      <div @click="expandedFun" class="flex">
        <i v-if="expanded" class="el-icon-arrow-up"></i>
        <i v-else class="el-icon-arrow-down"></i>
        <img src="@/assets/images/planGenerater/notes.png" alt="" width="24" height="24" class="flex_img">
        <span>Notes</span>
      </div>
      <div v-if="!expanded" v-for="(item, index) in list" :key="index" class="note-item" @click="showData(item)"  @mouseenter="hoveredIndex = index" @mouseleave="hoveredIndex = null">
        <img src="@/assets/images/planGenerater/md.png" alt="" width="24" height="24" class="flex_img">
        {{ item.objectName }}
        <!--  -->
        <div v-show="hoveredIndex === index" class="ab_flex">
          <el-tooltip class="item" effect="dark" content="删除当前文件" placement="top">
            <i class="el-icon-delete" @click="(e) => shanchu(e,item)"></i>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div v-else :style="!isHideIframe ? 'height: calc(100vh - 250px)' : 'height: calc(100vh - 210px)'">
      <div style="display: flex; align-items: center; cursor: pointer;" @click="goback">
          <i class="el-icon-arrow-left" style="margin-right: 5px;"></i>
          <span>返回</span>
        </div>
      <MonacoEditor
      id="codeCreated"
      class="editor"
      :value="codeStr"
      language="python"
      :scroll-beyond-last-line="true"
      :original="codeStr"
      theme="vs-dark"
      :diff-editor="false"
      :autoScroll="true"
      :options="options"
      @change="change"
    />
    <div class="enn-markdown-div">
     <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
      <el-button type="info" size="mini" @click="handleEdit"><img
        src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
      <el-button type="info" size="mini" @click="newHanderSave"><img
        src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
      <el-button type="info" size="mini" @click="closeCaiNaFun"><img
        src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
     </el-tooltip>
     <el-tooltip class="item" effect="dark" content="复制" placement="top">
      <el-button type="info" size="mini" @click="copyTextNewEnn()"><img
        src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
     </el-tooltip>
    </div>
    </div>
</div>
</template>
<script>
import MonacoEditor from '@/components/MonacoEditor'
import { queryListFiles, queryObjectKey, notes_delete_file, update_file_content } from '@/api/planGenerateApi.js'
export default {
  props: {
    firstUrl: {
      type: String,
      default: ''
    },
    abilitys: {
      type: Object,
      default: () => {}
    }
  },
  components: { MonacoEditor },
  computed: {
    isHideIframe() {
      return !!this.$store.state.planGenerate.isIframeHide
    },
  },
  data() {
    return {
      objectKey:'',
      isEdit: false,
      dagangFlagNew: false,
      options: {
        theme: 'vs',  // 使用默认浅色主题
        readOnly: true,
        lineNumbers: true,
        fontSize: 15,
        mouseStyle: 'default',
        colorDecorators: true,
        foldingStrategy: 'indentation', // 代码可分小段折叠
        automaticLayout: true, // 自适应布局
        overviewRulerBorder: false, // 不要滚动条的边框
        autoClosingBrackets: true,
        renderLineHighlight: 'all',
        wordWrap: 'on',
        scrollBeyondLastLine: true,
        tabSize: 4, // tab 缩进长度
        minimap: {
          enabled: true // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      },
      hoveredIndex: null,
      codeStr: '',
      expanded: false,
      list: [],
      showList:true,
    }
  },
  watch: {
    firstUrl: {
      async handler(newVal) {
        this.getData()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    copyTextNewEnn() {
        this.copyTextNew(this.codeStr)
    },
    copyTextNew(text) {
      // 获取需要复制的文本
      if (!text) {
        this.$message({
          type: 'warning',
          message: '复制的内容不能为空！'
        })
        return
      }

      try {
        if (navigator.clipboard && window.isSecureContext) {
          // 使用新的 Clipboard API
          navigator.clipboard.writeText(text)
            .then(() => {
              this.$message({
                type: 'success',
                message: '复制成功！'
              })
            })
            .catch(() => {
              this.fallbackCopyText(text)
            })
        } else {
          // 使用传统方法
          this.fallbackCopyText(text)
        }
      } catch (error) {
        console.error('复制失败:', error)
        this.$message({
          type: 'error',
          message: '复制失败！'
        })
      }
    },
    async  newHanderSave(){
      const res = await  update_file_content({
      "file_key": this.objectKey,
      "content":this.codeStr
      })
      this.isEdit = !this.isEdit
      this.options.readOnly = !this.options.readOnly
      if(res.data.code == 200){
        this.$message({
            type: 'success',
            message: '保存成功！'
          })
      }else{
        this.$message({
          type: 'error',
          message: '保存失败！'
        })
      }
    },
    handleEdit(){
      this.isEdit = !this.isEdit
      this.options.readOnly = !this.options.readOnly
    },
    closeCaiNaFun(){
      this.codeStr = this.codeStrOld
      this.isEdit = !this.isEdit
      this.options.readOnly = !this.options.readOnly
    },
    change(val) {
      this.codeStr = val;
    },
    goback() {
      this.showList = true
    },
    async  shanchu(e,item){
      e.stopPropagation()
      const  res =  await  notes_delete_file({
        "file_key": item.objectKey
      })
      if(res.data.code == 200){
        this.getData()
      }
    },
    async expandedFun() {
      if (this.expanded) {
        await this.getData()
      }
      this.expanded = !this.expanded
    },
    async getData() {
      const listRes = await queryListFiles({
        scheme_id: Number(this.$route.query.id),
        prefix: this.firstUrl + 'notes/'
      })
      if (listRes.data.code == 200) {
        this.list = listRes.data.result
      } else {
        this.list = []
      }
    },
    async showData(item) {
      const res = await queryObjectKey({
        'objectKey': item.objectKey,
      })
      fetch(res.data.result)
        .then(response => response.text())
        .then(data => {
          this.codeStr = data
          this.codeStrOld = data
          this.objectKey = item.objectKey
          this.showList = false
        })
        .catch(error => {
        });
    }
  }
}
</script>
<style scoped>
.note-item {
  color: rgba(15, 21, 40, 0.82);
  padding-left: 24px;
  height: 32px;
  position: relative;
  &:hover {
    background-color: rgba(87, 104, 161, .08)
  }
}

.flex_img {
  margin: auto 5px;
}

.flex {
  display: flex;
  align-items: center;
}
.ab_flex{
  position: absolute;
  right: 20px;
  height:24px;
  top: 0;
}
.editor{
  height: calc(100% - 40px);
}
.enn-markdown-div {
  text-align: center;
  background: #fff;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 12px 24px -16px rgba(54,54,73,.04),
              0 12px 40px 0 rgba(51,51,71,.08),
              0 0 1px 0 rgba(44,44,54,.02);
  bottom: 54px;
  display: inline-flex; /* 关键修改：改为行内弹性布局 */
  gap: 4px;
  height: 40px;
  padding: 16px;
  border: 1px solid #e8eaf2;
  border-radius: 16px;
  align-items: center;
  /* 移除会强制拉伸宽度的属性 */
  width: auto; /* 显式声明宽度自适应 */
  justify-content: space-between;
 }
</style>
