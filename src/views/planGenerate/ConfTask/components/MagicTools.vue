<template>
  <div class="popWrap" ref="popWrapRef">
    <el-button slot="reference" class="rainbow-text-button" @click="showToolDrawer">
      <SvgIcon name="magicTool" /> 生产工具
    </el-button>
    <el-drawer :title="'AI助手'" :modal="false" :visible.sync="TooldialogVisible" direction="rtl" size="364px"
      custom-class="custom-drawer">
      <template #title>
        <div class="drawer-title">
          <SvgIcon name="magicTool" class="ai-assistant-icon" /> <!-- 使用 SvgIcon 组件 -->
          <span>AI助手</span> <!-- 文字 -->
        </div>
      </template>
      <el-form ref="form" :model="currentToolForm" label-width="80px" label-position="top" class="custom-tool-form">
        <el-form-item class="custom-form-item">
          <div class="label-select-container">
            <span class="custom-label">帮我生成-</span>
            <el-select v-model="currentToolForm.selectedTool" placeholder="请选择工具" filterable class="custom-select"
              @change="handleSelectChange">  
              <el-option v-for="tool in tools" :key="tool.name" :label="tool.Toolname" :value="tool.name">
              </el-option>
            </el-select>
          </div>
          <div class="textarea-container">
            <el-input type="textarea" v-model="currentToolForm.desc" :autosize="{ minRows: 5, maxRows: 10 }"
              placeholder="请输入要创作的内容: 示例:需要一张锅炉拓扑图，包括锅炉本体、燃烧器、给水系统、蒸汽输出管道的。燃烧器在锅炉本体左侧...." class="custom-input"
              @keydown.enter.native="handleEnterKey"></el-input>
            <div class="floating-btn-wrap">
              <el-button :disabled="currentToolForm.desc == ''" type="primary" @click="submitForm"
                class="floating-btn"></el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="关联资料">
          <template #label >
            <div class="label-container">
              <span class="title">关联资料:</span>
              <i class="el-icon-plus flex_add cur" @click="showCascader = true"></i>
              <div class="upload-icon-container cur" @click="uploadFun">
                <img :src="upload" alt="" width="24" height="24">
              </div>
            </div>
          </template>
          <div class="flex_all">
            <div v-for="(its,ic) in getTreeData()" :key="ic" class="label_c">
            <template>{{ its.label }}</template>
            <i class="el-icon-close cur" style="margin-left: 8px;" @click="del(its.value)"></i>
          </div>
         
          <div class="flex_magic position" v-if="showCascader" v-click-outside="close">
            <div class="flex_magic">
            <div class="flex_first">
              <div v-for="(item, index) in cascaderOptions" :key="index" @mouseover="choseFirst(item)"  @click="choseFirst(item)" class="flex_hover">
                {{ item.label }}
              </div>
            </div>
          </div>
          <div class="flex_magic">
            <div class="flex_first">
              <div v-for="(itemSecond, vt) in optionsSecond" :key="vt" @change="val => updateSelection(itemSecond)" class="flex_hover">
                <el-checkbox :value="itemSecond.isSelect">{{ itemSecond.label }}</el-checkbox> 
              </div>
            </div>
          </div>
          </div>
          <div class="zslist" v-for="(item, index) in content.files" :key="item.fileId">
              <span class="card-text">{{ item?.name }}</span>
              <i class="el-icon-close cur" @click="delUp(index)"></i>
          </div>
          </div>
        </el-form-item>
      </el-form>
      <span class="title" style="margin-bottom: 10px;">&nbsp;常用推荐工具:</span>
      <div class="card-list">
        <div class="card-item" v-for="(tool, index) in tools.slice(0, 3)" :key="tool.name" @click="onCardClick(tool)"
          :class="{ 'selected-card': selectedToolName === tool.name }">
          <el-tooltip effect="dark" placement="top" :content="tool.Toolname">
            <h4>{{ tool.Toolname }}</h4>
          </el-tooltip>
        </div>
      </div>
    </el-drawer>
    <input type="file" ref="fileInput" style="display:none" @change="inputChange">
  </div>
</template>

<script>
import {
  generalExecute,
  get_scheme_materialsFetch,
  update_scheme_materialsFetch,
  create_scheme_materialsFetch
}
  from '@/api/planGenerateApi.js'
  import vClickOutside from 'v-click-outside';
export default {
  name: "MagicTools",
  props: {
    sessionId: {
      type: String,
      default: '1'
    },
    tools: {
      type: Array,
      required: true
    },
    optHeader2TabsList: {
      type: Array,
      required: true
    }
  },
  directives: {
    clickOutside: vClickOutside.directive
  },
  data() {
    return {
      showCascader:false,
      cascaderOptions:[
        {
          value: 1,
          label: '生产产物',
          children: this.optHeader2TabsList.map(item => this.transformItem(item))
        },
        {
          value: 2,
          label: '资料产物',
          children: [
            // 这里可以添加其他静态或动态的选项
            { value: 'materials.需求说明', label: '需求说明',isSelect: false, },
            { value: 'materials.关联文件', label: '关联文件',isSelect: false, }
          ]
        }
      ],
      optionsSecond: [],
      content: {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      },
      requirement: '',
      id: '',
      upload: require('@/assets/images/planGenerater/expert-upload.png'),
      selectedToolName: '', // 用于存储当前选中的工具名称
      CascaderProps: {
        multiple: true,
        expandTrigger: 'hover',
      },
      TooldialogTitle: '',
      toolForms: {},
      clickedTool: {},
      TooldialogVisible: false,
      visible: false,
      popoverWidth: 500,
      commandCenterVisible: false, // 新增指令中心的可见性状态
      commands: [], // 添加指令数据
      currentToolForm: {
        selectedTool: '', // 确保这里有一个初始值
        cascaderVal: [],
        desc: '',
      },
    };
  },
  mounted() {
    this.getData()
    this.$nextTick(() => {
      this.updatePopoverWidth();
      window.addEventListener("resize", this.updatePopoverWidth);
    })
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updatePopoverWidth);
  },
  methods: {
    delUp(index) {
      this.content.files.splice(index, 1)
      this.save()
    },
    close() {
      this.showCascader = false
    },
    updateSelection(item) {
      // 使用Vue.set确保响应式更新
      this.$set(item, 'isSelect', !item.isSelect);
    },
    del(targetValue) {
      this.updateChildIsSelectByValue(this.cascaderOptions, targetValue);
    },
    updateChildIsSelectByValue(arr, targetValue) {
      arr.forEach((group, index) => {
        if (group.children) {
          const updatedChildren = group.children.map(child => {
            if (child.value === targetValue) {
              // 使用Vue.set确保响应性
              // return { ...child, isSelect: false };
              this.$set(child, 'isSelect', false);
            }
            return child;
          });
          // 如果需要可以重新赋值children
          this.$set(this.cascaderOptions[index], 'children', updatedChildren);
        }
      });
    },
    getTreeData(){
      return this.cascaderOptions.flatMap(group =>
        group.children ? 
          group.children.filter(item => item.isSelect) : 
          []
      );
    },
    choseFirst(item) {
      this.optionsSecond = item.children
    },
    async getData() {
      const res = await get_scheme_materialsFetch({
        "scheme_id": Number(this.$route.query.id),
      })
      this.requirement = res.data.result?.requirement || ''
      this.id = res.data.result?.id || ''
      this.content = res.data.result?.content || {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      }
    },
    uploadFun() {
      this.$refs.fileInput.click();
    },
    async inputChange(event) {
      const files = event.target.files;
      const maxSize = 100 * 1024 * 1024  // 10MB
      let breakFlag = false
      const allowedTypes = [
        'application/xlsx', 'application/xls',
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > maxSize) {
          this.$refs.fileInput.value = '';
          this.$message({
            type: 'error',
            message: '文件大小不能超过100MB!'
          });
          breakFlag = true
          break
        }
      }
      if (breakFlag) {
        return false
      }
      console.log(files, 'files')
      if (files) {
        for (let i = 0; i < files.length; i++) {
          const formData = new FormData();
          try {
            const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
              fileType: this.$fileUtil.getFileSuffix(files[i].name)
            })
            if (res.data.status === 200) {
              formData.append('key', res.data.data.key)
              formData.append('accessKeyId', res.data.data.accessKeyId)
              formData.append('signature', res.data.data.signature)
              formData.append('policy', res.data.data.policy)
              formData.append('file', files[i])
            }
            const res1 = await this.$axios.post(res.data.data.obsUrl, formData)
            const fileName = this.$fileUtil.getFileName(files[i].name)
            const fileSize = files[i].size / 1024
            const fileType = this.$fileUtil.getFileSuffixWithSpot(files[i].name)
            const fileKey = res.data.data.key
            await this.$axios
              .post('/file/add', {
                fileKey: fileKey,
                fileName: fileName,
                fileSize: fileSize,
                fileType: fileType,
                storagePlatform: 'Obs',

              })
              .then((res) => {
                if (res.data.status === 200) {
                  console.log(res.data.data, '文件id')
                  this.content.files.push(Object.assign({}, { ...res.data.data }, { name: fileName, url: res.data.data.path }))
                }
              })
            this.save()
          } catch (e) {
            console.log(e)
            this.$message.error('获取签名出错！')
          }
        }
      }
      this.$refs.fileInput.value = '';
    },
    async save() {
      // this.requirement = this.requirementEdit;
      this.content.knowledges = this.content.knowledges.map(item => {
        return {
          id: Number(item.id),
          name: item.name
        }
      })
      this.content.abilitys = this.content.abilitys.map(item => {
        return {
          id: Number(item.id),
          name: item.name,
          ext_info: {
            abilityIcon: item?.ext_info?.abilityIcon
          }
        }
      })
      //
      let res = {}
      if (this.id == '') {
        res = await create_scheme_materialsFetch({
          content: this.content,
          requirement: this.requirement,
          schemeId: Number(this.$route.query.id),
        })
      } else {
        res = await update_scheme_materialsFetch({
          content: this.content,
          requirement: this.requirement,
          id: this.id,
          scheme_id: Number(this.$route.query.id)
        })
      }
      if (res.data.result) {
        await this.getData()
        await this.$emit('getShenCData')
      } else {
        this.$message({
          type: 'error',
          message: '保存失败!'
        });
      }
      this.nlShow = false
      this.zsShow = false
      this.checkFlag = true

    },
    showToolDrawer() {
      this.TooldialogVisible = true; // 打开抽屉

      // 初始化 clickedTool
      if (this.currentToolForm.selectedTool) {
        const selectedTool = this.tools.find(tool => tool.name === this.currentToolForm.selectedTool);
        if (selectedTool) {
          this.clickedTool = selectedTool; // 赋值 clickedTool
        }
      }

      this.selectedToolName = ''; // 清除选中状态
    },
    handleClose() {
      this.TooldialogVisible = false;
      this.selectedToolName = ''; // 清除选中状态
    },
    handleMouseEnter() {
      this.$emit('setActiveZl')
    },
    updatePopoverWidth() {
      // 动态获取父容器宽度
      const parent = this.$refs.popWrapRef;
      if (parent) {
        const width = parent.offsetWidth;

        this.popoverWidth = width < 500 ? 500 : width; // 如果宽度小于500，则设置为500
      }
    },
    handleEnterKey(event) {
      // 检查是否按下了 Enter 键，且没有按下 Shift 键
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault(); // 阻止默认换行行为
        this.submitForm(); // 调用提交方法
      }
    },
    getSelectedValues(data) {
      let selectedValues = [];

      data.forEach(group => {
        if (group.children) {
          group.children.forEach(child => {
            if (child.isSelect) {
              if(child.value == '方案详情key'){
                selectedValues.push('方案详情');
              }else{
                selectedValues.push(child.value);
              }
            }
          });
        }
      });
      return selectedValues;
    },
    async submitForm() {
      // 请求SSE
      console.log(this.getSelectedValues(this.cascaderOptions))
      if (!this.clickedTool?.instruction_code) {
        this.$message({
          type: 'warning',
          message: '请选择工具!'
        });
        return
      }
      const tabsArr = this.getSelectedValues(this.cascaderOptions)
      console.log("tabsArr111", tabsArr);
      const params = {
        instruction_code: this.clickedTool?.instruction_code,
        instruction_param: {
          agent_template_id: this.clickedTool?.agent_template_id,
          goal: this.currentToolForm.desc,
          task_type: this.clickedTool.name,
          context_codes: tabsArr || [],
          name: this.clickedTool?.uuid,
        },
        session_id: this.sessionId
      }
      console.log("generalExecute params", params);
      await generalExecute(params)

      this.$emit('tab-update', this.clickedTool?.name)
      this.clickedTool = {}
      this.TooldialogVisible = false;
    },
    handleSelectChange(selectedToolName) {
      const selectedTool = this.tools.find(tool => tool.name === selectedToolName);
      if (selectedTool) {
        this.handleToolSelection(selectedTool);
      }
    },
    handleToolSelection(tool) {
      this.clickedTool = tool;
      this.TooldialogTitle = tool.Toolname || '';
      const hasTool = this.optHeader2TabsList.some(item => item.name === tool.name);
      const productionOption = this.cascaderOptions.find(option => option.label === '生产产物');

      if (!this.toolForms[tool.name]) {
        this.$set(this.toolForms, tool.name, {
          cascaderVal: [productionOption && hasTool ? [productionOption.value, tool.name] : []],
          desc: tool?.default_chat_message || ''
        });
      }

      this.currentToolForm = this.toolForms[tool.name];
      this.currentToolForm.selectedTool = tool.name;
      this.TooldialogVisible = true;
      this.selectedToolName = tool.name;

      this.$nextTick(() => {
        console.log('currentToolForm.tabs:', this.currentToolForm.tabs);
        console.log('optHeader2TabsList:', this.optHeader2TabsList);
      });
    },
    async onCardClick(tool) {
      this.handleToolSelection(tool);
    },
    closePopover() {
      this.visible = false; // 手动关闭弹出层
    },
    openCommandCenter() {
      this.commandCenterVisible = true; // 打开指令中心
    },
    onCommandClick(command) {
      this.$root.$emit('command-selected', command.content); // 发送事件
      this.commandCenterVisible = false; // 关闭指令中心
    },
    isOverflowing(el) {
      return el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth;
    },
    transformItem(item) {
      const transformed = {
        ...item,
        value: item.name,
        label: item.label,
        isSelect: false
      };
      return transformed;
    },
  },
  computed: {
    titleOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.titleRef[index];
        return this.isOverflowing(el);
      });
    },
    contentOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.contentRef[index];
        return this.isOverflowing(el);
      });
    },
  },
  watch:{
    TooldialogVisible:{
      handler(val,oldVal){
        if(val){
          this.getData()
        if (this.tools.length > 0) {
        this.currentToolForm.selectedTool = this.tools[0].name;
        this.handleSelectChange(this.tools[0].name)
        }

      }
      },
      immediate:true,
      deep:true
    }
  }
};
</script>

<style scoped lang="scss">
:deep(.el-drawer__header) {
  text-align: left; // 标题整体靠左对齐
  padding: 16px 20px; // 调整标题的内边距
  margin-bottom: 0; // 去掉默认的 margin-bottom
  border-bottom: 1px solid #ebeef5; // 添加底部边框
}

.drawer-title {
  display: flex;
  align-items: center; // 图标和文字垂直居中
}

.ai-assistant-icon {
  width: 16px;
  height: 16px;
  color: #409eff; // 修改图标颜色
  margin-right: 8px; // 调整图标和文字的间距
}

.popWrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  /* 修改这一行，使按钮在一排 */
  gap: 10px;
  /* 设置按钮之间的间距 */
  margin-right: 20px;
}

.custom-popover {
  padding: 0px;

  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px 20px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }

    .close-button {
      border: none !important;
      background-color: unset !important;
    }
  }

  .popover-content {
    padding: 17px 20px;
    max-height: 450px;
    overflow-y: auto;
  }

  .card-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 三列布局 */
    gap: 10px;
  }

  .card-item {
    height: 110px;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    /* 调整圆角 */
    cursor: pointer;
    transition: box-shadow 0.3s, transform 0.3s, border-color 0.3s;
    /* 添加平滑过渡效果 */
    background: #ffffff;
    /* 设置背景色为白色 */
    color: #333;
    /* 设置字体颜色为深灰色 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    /* 添加阴影 */
  }

  .card-item:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1);
    /* 增强悬停时的阴影效果 */
    transform: translateY(-5px);
    /* 悬停时向上移动 */
    border-color: #0061FF;
  }

  .card-item h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
  }

  .card-item p {
    margin: 5px 0 0;
    font-size: 12px;
    color: #666;
    /* 设置描述文字颜色为中灰色 */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.drawer-wrapper {
  margin: 24px;
}

.el-drawer__header:first-child {
  padding: 10px 20px;
  /* 调整header的padding */
  font-size: 16px;
  /* 调整header的字体大小 */
  border-bottom: 1px solid #ebeef5;
  /* 添加底部边框 */
  margin-bottom: 0;
  /* 将 margin-bottom 设置为 0 */
}

.el-drawer__header> :first-child {
  font-weight: 600 !important;
  /* 调整header的字体粗细 */
}

.drawer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 添加这一行，使卡片居中对齐 */
  gap: 10px;
  /* 设置卡片之间的间距 */
}

.command-card {
  padding: 10px;
  width: 272px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #f3f2ff;
  transition: box-shadow 0.3s, transform 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 垂直居中 */
}

.command-card:hover {
  border: 1px solid #323ff0;
  cursor: pointer;
}

.command-card h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif';
  /* 设置更好的字体 */
}

.command-card p {
  margin: 5px 0 0;
  font-size: 14px;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif';
  /* 设置更好的字体 */
  color: #585a73;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制最多显示3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 20px;
  /* 设置最小高度为一行字的高度 */
}

.rainbow-text-button {
  border-radius: 12px !important;
}

@keyframes shimmer {
  0% {
    left: -150%;
    /* 起点 */
  }

  50% {
    left: 50%;
    /* 光效经过按钮中间 */
  }

  100% {
    left: 150%;
    /* 光效消失在按钮右侧 */
  }
}

.custom-tool-form {
  margin-top: 10px;
  position: relative;

  :deep(.el-form-item__label) {
    padding-bottom: 0px !important;
    /* 调整 label 与内容的间距 */
    font-size: 14px;
    /* 调整字体大小 */
    font-weight: 600;
    /* 增加一点加粗效果 */
    color: #606266;
    /* 使用更舒适的颜色 */
  }

  .custom-select {
    width: 100%;
  }
}

.textarea-container {
  width: 100%;
  position: relative;
  display: inline-block;
  /* 确保按钮和 textarea 能一起缩放 */

  .custom-input {
    width: 100%;
    /* 让 textarea 撑满父容器 */
    resize: both;

    :deep(.el-textarea__inner) {
      border: 0 !important; // 去掉边框
      padding: 0px 0px;
    }

    /* 启用缩放 */
  }


  .floating-btn-wrap {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 10;
    line-height: normal;

    .floating-btn {
      font-size: 10px;
      color: #969799;
      width: 32px;
      height: 32px;
      border-radius: 0;
      background-size: cover;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: center;
      background-image: url("~@/assets/images/planGenerater/active-send.png");
      border-radius: 4px;
      border: transparent;

      &.is-disabled {
        background-image: url("~@/assets/images/planGenerater/expert-send.png");
      }

      img {
        width: 32px;
      }
    }

    /* 去掉多余的 img 样式 */
  }
}

.tool-dialog {
  :deep(.el-dialog__body) {
    padding: 0px 20px;
  }

  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ebeef5;
    /* 添加分隔线 */
    padding-bottom: 12px;
    /* 调整分隔线与标题的间距 */
    margin-bottom: 16px;
    /* 增加标题与内容的间距 */
    font-weight: 600;
    /* 加粗标题 */
    font-size: 16px;
    /* 调整标题字体大小 */
    color: #303133;
    /* 调整标题颜色 */
  }
}
</style>
<style lang="scss">
.custom-drawer {
  .el-drawer__body {
    padding: 0px 20px;
  }
}

.custom-cascader {
  width: 100%;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* 三列布局 */
  gap: 10px;
  /* 设置卡片之间的间距 */
}

.card-item {
  height: 50px;
  /* 调整卡片高度 */
  padding: 5px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  /* 调整圆角 */
  cursor: pointer;
  background: #ffffff;
  /* 设置背景色为白色 */
  color: #333;
  /* 设置字体颜色为深灰色 */
  display: flex;
  /* 添加这一行 */
  justify-content: center;
  /* 添加这一行，使内容水平居中 */
  align-items: center;
  /* 添加这一行，使内容垂直居中 */
  transition: border-color 0.3s, box-shadow 0.3s; // 添加过渡效果
}

.card-item:hover {
  font-weight: bold;
  border-color: #409eff;
}

.card-item.selected-card {
  border-color: #409eff; // 选中时边框颜色变为蓝色
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.5); // 添加阴影效果 
  color: #409eff; // 选中时文字颜色变为蓝色
}

.card-item h4 {
  margin: 0;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: inherit; // 继承父元素的颜色
  text-align: center;
  /* 添加这一行，使字体居中 */
}
</style>

<style scoped lang="scss">
.custom-tool-form {
  .custom-form-item {
    margin-bottom: 0; // 去掉默认的 margin-bottom

    :deep(.el-form-item__content) {
      display: flex;
      flex-direction: column;
      width: 100%;
      border: 1px solid #dcdfe6; // 添加边框
      border-radius: 4px; // 添加圆角
      padding: 10px; // 内边距
      background-color: #fff; // 背景色
    }

    &:hover :deep(.el-form-item__content) {
      border-color: #409eff; // 悬停时边框颜色变为蓝色
    }
  }

  .label-select-container {
    display: flex;
    align-items: center; // 让 label 和 select 垂直居中
    margin-bottom: 8px; // 调整 label-select 和 textarea 的间距

    .custom-label {
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px; // 调整 label 和 select 的间距
      white-space: nowrap; // 防止 label 换行
      background: linear-gradient(90deg, hsl(238, 100%, 69%), #db08e7); // 渐变色
      -webkit-background-clip: text; // 将背景裁剪为文字
      background-clip: text;
      color: transparent; // 将文字颜色设置为透明，以显示背景渐变
    }

    .custom-select {
      flex: 1; // 让 select 占据剩余空间
    }
  }

  .textarea-container {
    width: 100%;
    position: relative;
    background-color: #fff; // 背景色

    .custom-input {
      width: 100%;
      border: none; // 去掉 textarea 的边框
      padding: 0; // 去掉 textarea 的内边距
      resize: vertical; // 允许垂直调整大小
    }

    .floating-btn-wrap {
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 10;
      line-height: normal;

      .floating-btn {
        font-size: 10px;
        color: #969799;
        width: 32px;
        height: 32px;
        border-radius: 0;
        background-size: cover;
        background-repeat: no-repeat;
        background-size: 100%;
        background-position: center;
        background-image: url("~@/assets/images/planGenerater/active-send.png");
        border-radius: 4px;
        border: transparent;

        &.is-disabled {
          background-image: url("~@/assets/images/planGenerater/expert-send.png");
        }
      }
    }
  }
}
</style>
<style scoped lang="scss">
.flex_first {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  flex: 1;
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 80px;
  box-sizing: border-box;
  color: #606266;
  border-right: 1px solid #e4e7ed;
  cursor: pointer;
}

.flex_magic {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 5px 0;
  font-size: 14px;
  background: #fff;
}
.flex_hover:hover{
  color: #409eff;
  font-weight: 700;
}
.flex_hover{
  width: 100%;
}

.zslist {
    width: auto;
    display: flex;
    align-items: center;
    height: 24px;
    padding: 2px 4px;
    border: 1px solid #DCDDE0;
    margin-right: 8px;
}

.card-text {
    display: inline-block;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 80px;
    height: 24px;
    line-height: 24px;
}
.flex_all{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  gap: 5px;
}
.flex_add{
  font-size: 16px;
  border: 1px solid #DCDDE0;
  margin-right:10px;
}
.position{
  align-items: flex-start;
  position: absolute;
  top: 45px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}
.label_c{
  display: flex;
  align-items: center;
  position: relative;
  height: 24px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #DCDDE0;
  margin-right: 8px;
  padding: 2px 4px;
  &:hover{
    border: 1px solid #409eff;
  }
}
.cur{
  cursor: pointer;
}
</style>
<style scoped lang="scss">
.label-container {
  display: flex;
  align-items: center; // 确保元素垂直居中
  gap: 8px; // 设置元素之间的间距
  font-size: 14px; // 统一字体大小
  margin-top: 16px; // 增加与上方 form-item 的距离
  margin-bottom: 8px; // 调整与下方内容的距离

  .title {
    font-weight: bold; // 加粗
    color: #000; // 黑色
  }

  .flex_add {
    font-size: 16px; // 调整加号图标大小
    border: 1px solid #DCDDE0; // 添加边框
    border-radius: 4px; // 圆角
    padding: 4px; // 内边距
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果

    &:hover {
      background-color: #f5f7fa; // 悬停时背景色
      border-color: #409eff; // 悬停时边框颜色
    }
  }

  .upload-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #DCDDE0; // 添加边框
    border-radius: 4px; // 圆角
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果

    &:hover {
      background-color: #f5f7fa; // 悬停时背景色
      border-color: #409eff; // 悬停时边框颜色
    }

    img {
      transition: opacity 0.3s; // 添加过渡效果

      &:hover {
        opacity: 0.8; // 悬停时降低透明度
      }
    }
  }
}

.title {
  font-weight: bold; // 加粗
  color: #000; // 黑色
  margin-bottom: 10px; // 调整与下方内容的距离
  line-height: 30px;
  height: 20px;
}
</style>