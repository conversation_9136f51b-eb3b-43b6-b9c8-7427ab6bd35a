<template>
    <div class="cardContent">
      <div v-show="!phoneFlag" class="chatHeader">
        <div class="rightTitle">
        </div>
        <div class="rightTitleOpt" v-if="!ModalType">
          <el-tooltip class="item" effect="dark" :content="rightFullFlag ? '退出全屏' : '全屏'" placement="top">
            <div :class="rightFullFlag ? 'rightBtn rightBtnBlue' : 'rightBtn'" @click="changeShowFull">
              <img v-if="rightFullFlag" src="@/assets/images/planGenerater/tuichuquanping.png" /><img v-else
                src="@/assets/images/planGenerater/full.png" />
            </div>
          </el-tooltip>
        </div>
      </div>
      <template v-if="chatActiveTab === 'first'">
        <div ref="chatBox" class="chatScroll" :style="{ userSelect: isDragging ? 'none' : 'auto' }">
          <div v-for="(item, index) in historyChat2.messages" :key="index" style="position: relative;"  @mouseenter="hoveredIndex = index" @mouseleave="hoveredIndex = null">
            <div v-if="item.content" class="gptAnsWrap">
              <div v-if="item.author?.role !== 'user_proxy'" class="gptAvator" style="margin-right: 6px">
                <img v-if="item.agent_role_id && agentAvatorInfoMap[item.agent_role_id]?.icon"
                  :src="agentAvatorInfoMap[item.agent_role_id]?.icon" />
                <img v-else-if="agentAvatorInfo.icon" :src="agentAvatorInfo.icon" />
                <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
              </div>
              <div style="flex: 1">
                <div :class="item.author?.role === 'user_proxy' ? 'chat-time chat-time-user' : 'chat-time'
                  ">
                  <div v-if="item.author?.role !== 'user_proxy'" class="user">
                    <span class="time">{{ getHours(item.create_time) }}</span>
                    <span v-if="
                      item.author?.role !== 'user_proxy' &&
                      item.agent_role_id &&
                      agentAvatorInfoMap[item.agent_role_id]?.alias
                    " class="agent-mark">{{ agentAvatorInfoMap[item.agent_role_id]?.alias }}</span>
                    <span v-else-if="item.author?.role !== 'user_proxy' && agentAvatorInfo.alias" class="agent-mark">{{
                      agentAvatorInfo.alias }}</span>
                    <span v-if="item.agent_role_id && agentAvatorInfoMap[item.agent_role_id]?.name">{{
                      agentAvatorInfoMap[item.agent_role_id].name }}</span>
                    <span v-else-if="agentAvatorInfo.name">{{ agentAvatorInfo.name }}</span>
                    <span v-else>
                      <template v-if="item.content.nickName">{{ item.content.nickName }}
                        {{
                        item.content.username ? '（' + item.content.username + '）' : ''
                        }}</template>
                    </span>
                  </div>
                  <div v-else class="user">
                    <span class="time">{{ getHours(item.create_time) }} </span>
                    {{ item.content.nickName }}
                    {{ item.content.username ? '（' + item.content.username + '）' : '' }}
                  </div>
                </div>
                <div :class="item.author?.role === 'user_proxy' ? 'gptAnsBox gptUserBox' : 'gptAnsBox'
                  ">
                  <div :style="{
                    maxWidth: item.author?.role === 'user_proxy'  ? 'calc(100% - 40px)' : `calc(${leftWidth} - 115px)`,
                    flex: item.author?.role === 'user_proxy' ? 'none' : 1
                  }">
                    <div :class="item.author?.role === 'user_proxy' ? 'gptAns gptUser' : 'gptAns'">
                      <pre v-if="
                        !item.id &&
                        systemMessages !== 'process waiting' &&
                        systemMessages !== 'scheme generating' &&
                        (item.content?.chat_message.content.indexOf('graph') > -1 ||
                          (item.content?.chat_message.content.indexOf('flowchart') > -1 &&
                            item.content?.chat_message.content.indexOf('mermaid') > -1))
                      ">{{ item.content?.chat_message.content }}</pre>
                      <template v-else>
                        <!-- v-if="!ModalType" -->
                        <MyEditorPreview :id="item.id + index + ''" :ref="item.id + index" :chat-message="item.content"
                          :showButton="showButton" @open-modal="openModal" @modal-code-generate="handleModalCodeGenerate"
                          :ModalType="ModalType">
                        </MyEditorPreview>
                        <!-- <MyEditorPreview v-if="ModalType" :id="item.id + index + ''" :ref="item.id + index"
                          :chat-message="item.content" :showButton="showButton" @open-modal="openModal"
                          @modal-code-generate="handleModalCodeGenerate" :ModalType="ModalType">
                        </MyEditorPreview> -->
                        <!-- <MyEditorPreviewCode v-if="ModalType" :id="item.id + index + ''" :ref="item.id + index"
                          :chat-message="item.content" :showButton="showButton" @open-modal="openModal"
                          @modal-code-generate="handleModalCodeGenerate" :ModalType="ModalType">
                        </MyEditorPreviewCode> -->
                      </template>
                    </div>
                  </div>
                  <div v-if="item.author?.role === 'user_proxy'" class="file_flex">
                      <div v-for="(itemvxt,vxt) in  item.content?.chat_message.rel_files">
                      {{ itemvxt.file_name }}
                      </div>
                    </div>
                </div>
                <div
                  v-if="historyChat2.messages.length == 1 && item?.content?.chat_message?.preset_question?.length > 0 && item?.content?.chat_message?.preset_question[0] != ''">
                  <div class="gptAnsBox" style="padding: 5px"
                    v-for="(itemIvt, ivt) in item.content?.chat_message.preset_question" :key="ivt">
                    <div :class="{ 'cust-disabled': isReadOnly }" class="gptAns" @click="sendMessage2(true, itemIvt)" style="color:#4068d4;cursor: pointer">
                      {{ itemIvt }}
                    </div>
                  </div>
                </div>

                <div v-show="item.author?.role !== 'user_proxy' && !item.auto" class="think-wrap">
                  <div v-show="hoveredIndex === index && !(!doneSend || isReadOnly)" style="height:24px;margin-left: auto;margin-right: 45px;">
                    <el-tooltip class="item" effect="dark" content="复制" placement="top">
                      <i class="el-icon-document-copy" style="margin-right:10px" @click="copy(item.content?.chat_message.content)"></i>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="删除当前聊天" placement="top">
                      <i class="el-icon-delete" @click="shanchu(index)"></i>
                    </el-tooltip>
                  </div>
                </div>
                <div v-show="!(systemMessages == 'process running' ||
            systemMessages == 'process stream' ||
            systemMessages == 'process_stream_message' ||
            systemMessages.indexOf('received feedback') >-1 ||
            systemMessages == 'scheme generating' ||
            systemMessages == 'process stream running' ||
            systemMessages == 'clear_history' ||
            systemMessages == 'clear history completed') && hoveredIndex === index &&item.author?.role === 'user_proxy'" style="height:24px;display: flex; justify-content: flex-end; margin-top:5px;    position: absolute;
      right: 50px;">
                  <el-tooltip class="item" effect="dark" content="复制" placement="top">
                      <i class="el-icon-document-copy" style="margin-right:10px" @click="copy(item.content?.chat_message.content)"></i>
                    </el-tooltip>
                  <el-tooltip class="item" effect="dark" content="删除当前聊天" placement="top">
                      <i  class="el-icon-delete" @click="shanchu(index)"></i>
                  </el-tooltip>
                  <!-- hoveredIndex === index &&  -->
              </div>
              </div>
              <div v-if="item.author?.role === 'user_proxy'" class="gptAvator" style="margin-left: 6px">
                <div class="userAvator">{{ item.content.nickName?.slice(-2) }}</div>
                <!-- <img src="@/assets/images/planGenerater/user-chat-icon.png" style="margin-left: 3px"/> -->
              </div>
            </div>
          </div>
          <div v-if="
            systemMessages !== 'process running' &&
            systemMessages !== 'process stream' &&
            systemMessages !== 'process_stream_message' &&
            systemMessages.indexOf('received feedback') < 0 &&
            systemMessages !== 'scheme generating' &&
            systemMessages !== 'process stream running' &&
            systemMessages !== 'clear_history' &&
            qaList.length &&
            systemMessages !== 'clear history completed'
          " class="qaBox">
            <div class="qaflex">
              <div v-for="item in qaList" class="qaitem" @click="handleAsk(item)">{{ item }}</div>
            </div>
          </div>
          <div v-if="
            qaBoxLoading &&
            systemMessages !== 'clear_history' &&
            systemMessages !== 'clear history completed'
          " class="qa-loading-spinner"></div>
          <!-- v-if="systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' || systemMessages.indexOf('received feedback') > -1" -->
          <div v-if="ansBoxLoading" class="qa-loading-spinner3"></div>
          <div class="gptAnsBox" style="flex-direction: column;margin-left:40px" v-if="systemMessages === 'scheme generating' || systemMessages === 'process running' ||
            systemMessages === 'process stream' ||
            systemMessages === 'process_stream_message' ||
            systemMessages.indexOf('received feedback') > -1">
          <div class="gptAns2" >
            <div class="qa-loading-spinner2"></div>
          </div>
          </div>
          <el-alert v-if="taskStatusText === 'scheme generating error'" :closable="false" title="方案生成失败 "
            type="error"></el-alert>
          <el-alert v-if="systemMessages === 'process error'" :closable="false" title="会话失败！ " type="error"></el-alert>
          <el-alert v-if="agentError" :closable="false" type="error">
            <span slot="title">人机对话连接失败请刷新后再进行对话 &nbsp; &nbsp;<el-link type="error" text
                :style="{ verticalAlign: 'baseline' }" @click="refresh">刷新</el-link></span>
          </el-alert>
          <KickFeat v-if="(hasChatingName || isOccupied) && !isEmbedTool && !isReadOnly && !noKickFeat" class="kickfeat-component"
            :kickLoading="kickLoading" :isOccupied="isOccupied" :hasChatingName="hasChatingName"
            @e-update:kickedloading="handleUpdateKickedLoading" @e-update:kicked="startPolling" />
        </div>
        <div :style="{ height: thinkFlag ? '225px' : '0px', borderWidth: thinkFlag ? '1px' : '0px' }" :class="thinkFullFlag
          ? rightFullFlag
            ? 'thinkContent thinkContentFullFull'
            : globalNavigatorStatus
              ? 'thinkContent thinkContentFull'
              : 'thinkContent thinkContentFullSmall'
          : 'thinkContent'
          ">
          <div class="thinkHeader">
            <div class="title"><img src="@/assets/images/planGenerater/think.png" />生成过程</div>
            <div class="thinkOpt">
              <el-tooltip class="item" effect="dark" :content="thinkFullFlag ? '退出全屏' : '全屏'" placement="top">
                <div :class="thinkFullFlag ? 'think-btn think-btn-blue' : 'think-btn'" @click="changeThinkFull">
                  <img v-if="thinkFullFlag" src="@/assets/images/planGenerater/tuichuquanping.png" /><img v-else
                    src="@/assets/images/planGenerater/full.png" />
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="关闭" placement="top">
                <div class="think-btn" @click="closechangeThinkWrap">
                  <i class="el-icon-close" style="color: #4068d4"></i>
                </div>
              </el-tooltip>
            </div>
          </div>
          <div class="thinkWrap">
            <div class="thinkItem">
              <div class="itemContent">
                <MyEditorPreview id="MyFanganEditorSikao" ref="MyFanganEditorSikao" :md-content="processContent.text">
                </MyEditorPreview>
                <!-- <vue-markdown v-highlight :source="processContent.text"  class="markdown-body chat-markdown"></vue-markdown> -->
              </div>
            </div>
          </div>
        </div>
        <div v-if="!thinkFullFlag" v-show="!phoneFlag"
          :class="inputHeight > 80 ? 'clearChatBtnBox clearChatBtnBoxHigh' : 'clearChatBtnBox'">
        </div>
        <div v-show="!phoneFlag" class="chatFooter">
          <!-- systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' -->
          <div style="display: flex; align-items: center;">
            <el-tooltip style="margin-bottom: 10px;" class="item" effect="dark" content="清空聊天记录" placement="top">
              <div :class="[
                'process stream',
                'process stream running',
                'process running',
                'processing',
                'scheme generating',
                'clear_history',
                'process_stream_message'
              ].indexOf(systemMessages) > -1 ||
                hasChatingName || isOccupied ||
                qaBoxLoading
                ? 'think-btn-disabled'
                : 'clear'
                " @click="() => {
                 if(isReadOnly) return
                if (
                  [
                    'process stream',
                    'process stream running',
                    'process running',
                    'processing',
                    'scheme generating',
                    'clear_history',
                    'process_stream_message'
                  ].indexOf(systemMessages) < 0 &&
                  !hasChatingName &&
                  !qaBoxLoading
                ) {
                  clearHistory()
                }
              }
              ">
                <!-- <SvgIcon name="del" class="icon clear-icon" /> -->
                <img :class="{ 'cust-disabled': isReadOnly }" src="@/assets/images/planGenerater/ic_clear.png" alt="">
              </div>
            </el-tooltip>
            <span class="vertical-line"></span>
            <MagicApi
            :disabledStatus="[
              'process stream',
              'process stream running',
              'process running',
              'processing',
              'scheme generating',
              'clear_history',
              'process_stream_message'
            ].indexOf(systemMessages) > -1"
            :commands="localCommands"
            :tags="localTags"
            :isReadOnly="isReadOnly"
            @tag-selected-change="tagSelectedChange"
            @tag-update="tagUpdate" ref="MagicApiRef">
            </MagicApi>
          </div>
          <div class="chatFooterTextInput">

            <div id="myInputText" class="chatInput">
              <!-- 角色选择面板 -->
              <div v-if="showSelectVisable3" :class="inputHeight > 80 ? 'selectRole selectRoleHigh setyleRole' : 'selectRole setyleRole'"
                v-click-outside="onClickOutside3">
                <div v-for="(el, index) in getRoleList" :key="index" class="new_flex" @click="choseClick3(el)">
                  <span>{{ el.label }}</span>
                </div>
              </div>
              <!-- 文件列表 -->
              <div v-if="showSelectVisable" style="padding: 10px;" :class="inputHeight > 80 ? 'selectRole selectRoleHigh' : 'selectRole'"
                v-click-outside="onClickOutside">
                <AbilitysFile
                  :checkedFilesData="checkedFiles"
                  :firstUrl="firstUrl"
                  :fileData="abilitys"
                  @checkedChange="handleCheckedFiles"
                />
              </div>
              <div v-if="showSelectVisable2"
                :class="inputHeight > 80 ? 'selectRole selectRoleHigh selectRole2' : 'selectRole selectRole2'"
                v-click-outside="onClickOutside2">
                <div v-for="(el, index) in getList" :key="index" class="new_flex" @click="choseClick2(el)">
                  <span>{{ el.label }}</span>
                </div>
              </div>
              <div class="chatFooterImage">
                <div v-if="ischuanzuoShow" class="flex_chuangzuo">
                  <div class="flex_chuangzuo_div">
                    <img src="@/assets/images/planGenerater/ic_zuozhe.png" width="12" height="12"/>
                    <div>聚焦模式</div>
                  </div>
                </div>
                <div class="new_chuangzuo" v-if="choseData.label || toMessage?.image_path || '' != '' ||checkedFiles?.length > 0 ">
                  <div class="uploaded-image" v-if="choseData.label" style="margin-right: 4px;width:auto">
                    <div class="image-wrapper">
                      <div class="right">
                        <span style="font-size:12px">{{ choseData.label }}</span>
                        <i class="el-icon-close cursor" @click="clearChose"></i>
                      </div>
                    </div>
                  </div>
                  <div class="image-wrapper" v-if="toMessage?.image_path || '' != ''">
                    <el-image class="uploaded-image" style="width:24px" :src="toMessage.image_path" fit="scale-down"
                      :preview-src-list="[toMessage.image_path]"></el-image>
                    <div class="right">
                      <el-tooltip class="item" effect="dark" :content="toMessage.image_name " placement="top">
                        <span>{{ toMessage.image_name }}</span>
                      </el-tooltip>
                      <i class="el-icon-close cursor" @click="clearImage"></i>
                    </div>
                    <!-- <el-button class="delete-image-btn" icon="el-icon-delete" circle @click="clearImage"></el-button> -->
                  </div>
                  <!-- checkedFiles 展示文件的列表 -->
                  <div class="image-wrapper" v-if="checkedFiles?.length > 0" v-for="(ivt,invt) in checkedFiles" :key="invt">
                    <i class="el-icon-paperclip"></i>
                    <div class="right">
                      <el-tooltip class="item" effect="dark" :content="ivt.name " placement="top">
                        <span>{{ ivt.name }}</span>
                      </el-tooltip>
                      <i class="el-icon-close cursor" @click="clearFile(invt)"></i>
                    </div>
                  </div>
                </div>
              </div>
              <el-input v-if="speakingFlag === 'running' || shibieLoading" ref="myChatInputText" v-model.trim="yuyinText"
                clearable readonly type="textarea" resize="none" class="typetarea" :autosize="{ minRows: 3, maxRows: 3 }"
                :disabled="([
                  'process stream',
                  'process stream running',
                  'process running',
                  'processing',
                  'scheme generating',
                  'clear_history',
                  'process_stream_message'
                ].indexOf(systemMessages) > -1 &&
                  !historyChat2.messages[historyChat2.messages.length - 1].id) ||
                  hasChatingName !== '' || isOccupied
                  " style="width: 100%" placeholder="">
              </el-input>
              <el-input v-else ref="myChatInputText" v-model="toMessage.content" clearable type="textarea"
                class="typetarea" id="saveImgPlan" resize="none" :autosize="{ minRows: 3, maxRows: 3 }" :disabled="([
                  'process stream',
                  'process stream running',
                  'process running',
                  'processing',
                  'scheme generating',
                  'clear_history',
                  'process_stream_message'
                ].indexOf(systemMessages) > -1 &&
                  !historyChat2.messages[historyChat2.messages.length - 1].id) ||
                  hasChatingName !== '' || isOccupied ||isReadOnly
                  " style="width: 100%" :placeholder="navType === 'Mac'
                          ? '请输入你的问题，可通过cmd+回车换行'
                          : '请输入你的问题，可通过Ctrl+回车换行'
                          " @input="handleInput" @keydown.native="carriageReturn($event)">
              </el-input>
            </div>
            <div class="send-btn">
              <div class="flex_body">
                <div v-if="agentRoleList.length > 0">
                  <el-popover v-model="localChendianVisable2" :disabled="[
                    'process stream',
                    'process stream running',
                    'process running',
                    'processing',
                    'scheme generating',
                    'clear_history',
                    'process_stream_message'
                  ].indexOf(systemMessages) > -1 || isReadOnly" placement="top-start" :visible-arrow="false" :offset="0"
                    :popper-class="inputHeight > 80 ? 'chendianfanganHigh popperRole' : 'popperRole'" trigger="click">
                      <div v-for="(el, index) in adminRole.length > 0 ? adminRole : agentRoleList" :key="index"
                        :value="el.name" :label="el.name" :class="selectAgentRoleNew.id == el.id ? ' AcotherRole Role' : 'Role'">

                        <div class="guessPovper ">
                          <div>
                            <div @click="handleChangeRole(el);localChendianVisable2 = false">
                              <div class="otherRole">
                                <img v-if="el.icon" :src="el.icon" />
                                <img v-else src="@/assets/images/planGenerater/chat-icon.png" />
                                <div style="flex: 1">{{ el.name }}</div>
                                <div class="mark-tag" style="margin-left: 16px">{{ el.alias }}</div>
                              </div>
                            </div>
                            <div v-if="agentRoleList.length < 1">
                              <span style="font-size: 12px">暂无其他角色</span>
                            </div>
                          </div>
                        </div>
                      </div>

                    <template slot="reference">
                      <div v-if="agentRoleList.length > 0" class="selectRoleDetail" :class="{
                        'disabledRole': [
                          'process stream',
                          'process stream running',
                          'process running',
                          'processing',
                          'scheme generating',
                          'clear_history',
                          'process_stream_message'
                        ].indexOf(systemMessages) > -1
                      }">
                        <img v-if="selectAgentRoleNew.icon" :src="selectAgentRoleNew.icon" />
                        <div class="tag-name">{{ selectAgentRoleNew.name }}</div>
                      </div>
                    </template>
                  </el-popover>
                </div>
                <div :class="{ 'cust-disabled': [
              'process stream',
              'process stream running',
              'process running',
              'processing',
              'scheme generating',
              'clear_history',
              'process_stream_message'
            ].indexOf(systemMessages) > -1 }"  class="icon_plus cursor"  @click="addChose">
                    # 引用
                </div>
              </div>
              <div style="display: flex;align-items: center;">
                <el-upload ref="uploadBtn" :action="uploadUrl" :show-file-list="false" f :data="uploadParam" :limit="1" style="height: 32px"
                  accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG" :before-upload="beforeUpload" :on-success="modelUploadSuccess">
                  <el-tooltip class="item" effect="dark" :content="instanceInfo?.enable_image_text_msg
                    ? '支持图片上传，基于图文问答'
                    : '当前能力不支持图文消息'
                    ">
                    <el-button :disabled="(adminRole.length > 0 ? !instanceInfo?.enable_image_text_msg : !selectAgentRoleNew.enable_image_text_msg) || ([
                      'process stream',
                      'process stream running',
                      'process running',
                      'processing',
                      'scheme generating',
                      'clear_history',
                      'process_stream_message'
                    ].indexOf(systemMessages) > -1 &&
                      !historyChat2.messages[historyChat2.messages.length - 1].id) || isReadOnly" class="expert-upload">
                    </el-button>
                  </el-tooltip>
                </el-upload>
                <div :class="([
                  'process stream',
                  'process stream running',
                  'process running',
                  'processing',
                  'scheme generating',
                  'clear_history',
                  'process_stream_message'
                ].indexOf(systemMessages) > -1 &&
                  !historyChat2.messages[historyChat2.messages.length - 1].id) ||
                  hasChatingName !== '' ||
                  isOccupied ||
                  shibieLoading
                  ? 'yuyinBtn yuyinBtnDisabled'
                  : 'yuyinBtn'
                  ">
                  <img :class="{ 'cust-disabled': isReadOnly }" class='yuyinstart' v-if="speakingFlag === 'start'"
                    src="@/assets/images/planGenerater/expert-voice.png" @click="startRecording" />
                  <img v-if="speakingFlag === 'running'" src="@/assets/images/planGenerater/yuyin.gif"
                    @click="stopRecording" />
                  <img v-if="speakingFlag === 'end'" src="@/assets/images/planGenerater/zhuanhuan-loading.gif" />
                </div>
                <!-- <div class="send-desc">
                      <img src="@/assets/images/planGenerater/back.png" style="margin-right: 4px" />发送
                    </div> -->
                <el-tooltip class="item" effect="dark"
                 v-if="toMessage.content == ''"
                 :content="(toMessage.content === '' && !([
                  'process stream',
                  'process running',
                  'process stream running',
                  'processing',
                  'scheme generating',
                  'process_stream_message',
                  'process stream running',
                  'clear_history1'
                ].indexOf(systemMessages) > -1 &&
                !historyChat2.messages[historyChat2.messages.length - 1].id)) ? '请输入你的问题' : '终止生成'"
                 >
                <el-button
                :class="['send-desc ',
                toMessage.content === ''? 'is-disabled':'',
                ([
                  'process stream',
                  'process running',
                  'process stream running',
                  'processing',
                  'scheme generating',
                  'process_stream_message',
                  'process stream running',
                  'clear_history1'
                ].indexOf(systemMessages) > -1 &&
                !historyChat2.messages[historyChat2.messages.length - 1].id)
                ?
                'stop-desc' :'']"
                 @click="stopThink()" :disabled="isReadOnly">

                  </el-button>
                </el-tooltip>
                <el-button
                v-else
                :class="['send-desc ',
                ([
                  'process stream',
                  'process running',
                  'process stream running',
                  'processing',
                  'scheme generating',
                  'process_stream_message',
                  'process stream running',
                  'clear_history1'
                ].indexOf(systemMessages) > -1 &&
                !historyChat2.messages[historyChat2.messages.length - 1].id) &&
                hasChatingName !== '' ||
                  isOccupied ||
                  speakingFlag === 'running' ||
                  shibieLoading
                 ?
                'stop-desc' :''
                ]"
                :disabled="isReadOnly"
                 @click="sendMessage2(false)">
                  </el-button>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </template>

  <script>
  import vClickOutside from 'v-click-outside';
  import MyEditorPreview from '../../mdEditorPreview.vue'
  import MyEditorPreviewCode from '../../mdEditorPreviewCode.vue'
  import { mapGetters } from 'vuex'
  import KickFeat from '../components/KickFeat.vue'
  import MagicApi from './MagicApi.vue'
  import AbilitysFile from './abilitysFile.vue';
  export default {
    name: 'chat',
    directives: {
      clickOutside: vClickOutside.directive
    },
    components: {
      MyEditorPreview,
      MyEditorPreviewCode,
      KickFeat,
      AbilitysFile,
      MagicApi
    },
    props: {
      firstUrl:{
        type: String
      },
      abilitys:{
        type: Object
      },
      shanchu: {
       type: Function,
      },
      jjRole:{
        type: String
      },
      getRoleList:{
        type: Array,
      },
      leftWidth:{
        type: String
      },
      noKickFeat:{
        type: Boolean,
        default: false
      },
      choseClick2: Function,
      choseClick3: Function,
      onClickOutside2: Function,
      onClickOutside3: Function,
      selectAgentRoleNew: Object,
      adminJueSe: Boolean,
      showSelectVisable2: Boolean,
      showSelectVisable3: Boolean,
      ModalType: {
        type: Boolean,
        default() {
          return false
        }
      },
      handleModalCodeGenerate: Function,
      localCommands: Array,
      localTags:Array,
      tagSelectedChange:Function,
      tagUpdate:Function,
      realTime: Function,
      clearImage: Function,
      stopRecording: Function,
      yuyinText: String,
      handleChangeRole: Function,
      clearHistory: Function,
      choseClick: Function,
      agentAvatorInfoMap: {
        type: Object,
        default: () => {
          return {}
        }
      },
      startWeb: Function,
      closechangeThinkWrap: Function,
      thinkFlag: Boolean,
      changeThinkWrap: Function,
      changeThinkFull: Function,
      startPolling: {
        type: Function,
      },
      handleUpdateKickedLoading: Function,
      kickLoading: {
        type: Boolean,
        default: false
      },
      isEmbedTool: {
        type: Boolean,
      },
      currentRoleInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      carriageReturn: Function,
      checkRole: String,
      chendianVisable2: Boolean,
      startRecording: Function,
      selectAgentRole: Object,
      adminRole: Array,
      instanceInfo: Object,
      modelUploadSuccess: Function,
      beforeUpload: Function,
      uploadUrl: {
        type: String,
        default: ''
      },
      uploadParam: Object,
      agentRoleList: Array,
      handleInput: Function,
      speakingFlag: String,
      toMessage: Object,
      clearChose: Function,
      choseData: Object,
      addChose: Function,
      closeCz: Function,
      openCz: Function,
      ischuanzuoShow: Boolean,
      showSelectVisable: Boolean,
      currentRoleInfoTemp: {
        type: Object
      },
      agentError: {
        type: Boolean,
        default: false
      },
      handleAsk: Function,
      qaList: Array,
      phoneFlag: {
        type: Boolean,
        default: false
      },
      rightFullFlag: Boolean,
      systemMessages: String,
      historyChat2: {
        type: Object
      },
      isOccupied: {
        type: Boolean,
        default: false
      },
      hasChatingName: {
        type: String
      },
      shibieLoading: {
        type: Boolean,
      },
      qaBoxLoading: Boolean,
      isDragging: Boolean,
      ansBoxLoading: Boolean,
      thinkFullFlag: Boolean,
      showButton: Boolean,
      openModal: Function,
      sendMessage2: Function,
      stopThink: Function,
      processContent: {
        type: Object
      },
      onClickOutside: Function,
      doneSend: Boolean,
      handleSave: Function,
      handleCreate: Function,
      taskStatusText: String,
      saveImg: Function,
      changeShowFull: Function,
      agentAvatorInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      isReadOnly: {
     type: Boolean,
     default: false
   }
    },
    data() {
      return {
        checkedFiles:[],
        hoveredIndex: null,
        chatActiveTab: 'first',
        inputHeight: 60,
        navType: '',
        isdown: false,
        localChendianVisable2: this.chendianVisable2,
        localCheckRole: this.checkRole,
        hoveredIndex: null,
      }
    },
    computed: {
      ...mapGetters({
        isAdmin: 'common/getIsAdminGetter',
        globalNavigatorStatus: 'common/getMenuCollapseGetter',
        getJujiaoStatus: 'common/getJujiaoStatus',
        getList: 'production/getList'
      }),
    },
    watch:{
      chendianVisable2: {
        handler(val, oldVal) {
          this.localChendianVisable2 = val
        },
        immediate: true
      },
      checkRole: {
        handler(val, oldVal) {
          this.localCheckRole = val
        },
        immediate: true
      }
    },
    async mounted() {
      this.saveImg()
      this.$nextTick(() => {
        setTimeout(() => {
          this.scrollToBottom();
        }, 50);
      })
    },
    methods: {
      clearFile(index){
        this.checkedFiles.splice(index,1)
      },
      gethandleCheckedFiles(){
        return this.checkedFiles.map(it =>{
          return {
            file_key: it.objectKey,
            file_name: it.name,
          }
        } )
      },
      clearCheckedFiles(){
        this.checkedFiles = []
      },
      handleCheckedFiles(checkedFiles) {
        // 这里可以获取到所有选中的文件数据
        console.log('选中的文件：', checkedFiles)
        this.checkedFiles = checkedFiles
      },

      showJJ(){
        if(this.isReadOnly) return
        if (!this.doneSend){
          return false
        }
        this.showSelectVisable3 =  true
      },
      copy(data) {
        const vm = this;
        vm.$copyText(data).then(
          function (e) {
            vm.$message({
              showClose: true,
              message: '复制成功',
              type: 'success'
            });
          },
          function (e) {
            vm.$message({
              showClose: true,
              message: '复制失败，请手动复制',
              type: 'error'
            });
          }
        );
      },

      getHours(v) {
        const date = new Date(v)
        const hours = date.getHours()
        const minutes = date.getMinutes()
        return `${hours}:${minutes < 10 ? '0' + minutes : minutes}`
      },

      scrollToBottom() {
        if (this.$refs.chatBox) {
          this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 100
          console.log('有没有向下滚动啊？scrollHeight');
        }
      },

    }
  }

  </script>



  <style lang="scss" scoped>
  .cust-disabled {
   cursor: not-allowed !important;
   opacity: 0.6;
  }
  .checkRole {
    .guessPovper {
      max-width: 500px;
      min-width: 88px;

      .guess-header {
        font-size: 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 4px 8px;
        border-bottom: 1px solid #ebecf0;

        .title {
          color: #646566;
          font-size: 12px;
        }

        .huanyihuan {
          font-size: 12px;
          margin-left: 2px;
          color: #4068d4;
          cursor: pointer;

          &:hover {
            color: #3455ad;
          }

          &:active {
            color: #264480;
          }
        }

        .plansearch {
          cursor: pointer;

          &:hover {
            color: #3455ad;
          }

          &:active {
            color: #264480;
          }
        }
      }

      .otherRole {
        display: flex;
        flex-direction: row;
        align-items: center;

        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 4px;
        }

        .mark-tag {
          background: #eff3ff;
          border-radius: 2px;
          padding: 0px 8px;
          color: #4068d4 !important;
          line-height: 17px;
          font-size: 12px;
          margin-left: 4px;
          display: inline-block;
          max-width: 260px;
          /* 设置文本溢出时的行为为省略号 */
          text-overflow: ellipsis;

          /* 设置超出容器的内容应该被裁剪掉 */
          overflow: hidden;

          /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
          white-space: nowrap;
        }
      }

      li {
        cursor: pointer;

        span {
          display: block;
          line-height: 32px;
          padding: 0 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }

        &:hover {
          background: #eff3ff;
        }

        &:active {
          background: #eff3ff;
        }
      }
    }

    .el-select-dropdown__item {
      height: 46px !important;
      line-height: 46px !important;
    }
  }
  .cardContent {
    display: flex;
    flex-direction: column;
    height: calc(100%);
    max-height: calc(100%);
    position: relative;

    .chatHeader {

      line-height: 46px;
      font-weight: bold;
      // background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0px 20px;

      :deep(.el-tabs__item) {
        font-weight: 500;
        font-size: 16px;
        color: #323233;
      }

      :deep(.el-tabs__header) {
        margin: 0px;
      }

      :deep(.el-tabs__nav-wrap::after) {
        //不要下面那条杠
        content: none;
        /* 移除伪元素内容 */
        background: none;
        /* 移除背景 */
      }

      .rightTitle {
        font-size: 16px;
        font-weight: bold;
        color: #323233;
        line-height: 22px;
        padding: 12px 0px;

        >img {
          width: 20px;
          height: 20px;
          content: url('@/assets/images/ai-loading.png');
        }
      }

      .rightTitleOpt {
        padding: 8px 0px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .rightBtn {
          // background: #F2F3F5;
          border-radius: 2px;
          width: 30px;
          height: 30px;
          color: #4068d4;
          margin-left: 8px;
          text-align: center;
          line-height: 28px;
          cursor: pointer;

          &:hover {
            background: #ebecf0;
          }

          &:active {
            background: #dcdde0;
          }

          &.rightBtnDisabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
            /* 阻止事件 */
          }

          &.rightBtnBlue {
            background-color: #406bd4;

            &:hover {
              background: #3455ad;
            }

            &:active {
              background: #264480;
            }
          }

          img {
            width: 16px;
            height: auto;
          }
        }
      }
    }

    .chatScroll {
      // max-height: calc(100vh -68.8);
      flex: 1;
      overflow-y: auto;
      padding: 0px 16px 16px;
      transition: all 0.3s ease-out;
      overflow-x: hidden;
      padding-top: 16px;

      &::-webkit-scrollbar {
        display: none;
      }

      .gptAnsWrap {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding-bottom: 18px;
        max-width: 100%;

        &:hover {
          .chat-time {
            .time {
              opacity: 1 !important;
            }
          }
        }

        .chat-time {
          height: 17px;
          line-height: 17px;
          // margin: 4px 0px;
          margin-bottom: 4px;
          display: flex;
          opacity: 1;
          justify-content: flex-start;
          width: calc(100% - 0px);

          &.chat-time-user {
            justify-content: flex-end;

            .user {
              flex-direction: row;
            }
          }

          .user {
            color: #646566;
            font-size: 12px;
            display: flex;
            flex-direction: row-reverse;

            .time {
              font-size: 12px;
              opacity: 0;
              padding: 0 5px;
            }

            span {
              color: #646566;
              font-size: 12px;
            }
          }

          .agent-mark {
            background: #eff3ff;
            border-radius: 2px;
            padding: 0px 8px;
            color: #4068d4 !important;
            line-height: 17px;
            font-size: 12px;
            margin-left: 4px;
            display: inline-block;
            max-width: 260px;
            /* 设置文本溢出时的行为为省略号 */
            text-overflow: ellipsis;

            /* 设置超出容器的内容应该被裁剪掉 */
            overflow: hidden;

            /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
            white-space: nowrap;
          }
        }

        .gptAnsBox {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &.gptUserBox {
            justify-content: flex-end;
            align-items: flex-end;
            flex-direction: column;
          }
        }

        .gptAvator {
          width: 34px;
          height: 34px;
          margin-right: 3px;

          img {
            width: 34px;
            height: 34px;
          }

          .userAvator {
            width: 34px;
            height: 34px;
            border-radius: 50%;
            background: #e6ecff;
            color: #2856b3;
            line-height: 34px;
            text-align: center;
            font-size: 12px;
            font-weight: normal;
          }
        }

        .gptAns {
          background: #fff;
          border-radius: 4px;
          color: #323232;
          line-height: 20px;
          position: relative;
          padding: 8px 12px;
          display: flex;
          align-items: center;

          &.gptUser {
            background: rgba(194, 210, 255, 0.56);
          }

          pre {
            font-family: SYZT, SimHei, SimSun, serif, PingFangSC-Regular, PingFang SC;
            font-size: 14px;
            line-height: 20px;
            color: #1f2328 !important;
            word-break: break-word;
            font-weight: 400;
            vertical-align: baseline;
          }
        }

        .gptAns2 {
          tab-size: 2;
          background: #fff;
          border-radius: 6px;
          color: #4068d4;
          line-height: 20px;
          position: relative;
          //padding: 8px 12px;
          display: flex;
          align-items: center;
        }

        .think-wrap {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          cursor: pointer;
          margin-top: 8px;

          // margin-left: 37px;
          .think-btn {
            border-radius: 2px;
            border: 1px solid rgba(64, 107, 212, 0.4);
            padding: 2px 8px;
            font-weight: 500;
            color: #4068d4;
            line-height: 18px;
            margin-right: 8px;
            word-break: keep-all;
            height: 24px;
            margin-left: 0px;
            font-size: 12px !important;
            background: transparent;
            :deep(span) {
              font-size: 12px !important;
            }

            &:hover {
              background: #f6f7fb;
            }

            &:active {
              background: #eff3ff;
            }

            &.think-btn-disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .dialog-dropdown {
            :deep(.el-button-group) {
              .el-button {
                border-radius: 2px;
                border: 1px solid rgba(64, 107, 212, 0.4);
                padding: 2px 8px;
                font-weight: 500;
                color: #4068d4;
                line-height: 18px;
                word-break: keep-all;
                height: 24px;
                margin-left: 0px;
                font-size: 12px !important;
                span {
                  font-size: 12px !important;
                }
              }

              .el-button:nth-of-type(1) {}

              .el-button:nth-of-type(2) {
                padding: 0px;
              }
            }
          }
        }
      }

      .qaBox {
        margin-left: 40px;

        .qatitle {
          font-size: 12px;
          color: #696a6f;
          line-height: 20px;
        }

        .qaflex {
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          max-width: 100%;
          overflow-x: auto;

          .qaitem {
            background: #f6f8fb;
            border-radius: 2px;
            color: #4068d4;
            font-size: 14px;
            margin-bottom: 6px;
            padding: 4px 12px;
            cursor: pointer;
            word-break: keep-all;
            white-space: nowrap;

            &:hover {
              background: rgba(194, 210, 255, 0.56);
            }
          }
        }
      }
    }


    .thinkContent {
      margin-left: 16px;
      width: calc(100% - 32px);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      max-height: 225px;
      height: 225px;
      overflow-y: auto;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dcdde0;
      transition: height 0.1s;

      &.thinkContentFull {
        position: fixed !important;
        left: 60px;
        width: calc(100vw - 90px) !important;
        height: calc(100vh - 150px) !important;
        overflow: hidden;
        z-index: 999 !important;
        max-height: calc(100vh - 150px) !important;
        top: 210px;
      }

      &.thinkContentFullFull {
        position: fixed !important;
        left: 0px;
        margin-left: 0px;
        width: 100vw !important;
        height: calc(100vh - 50px) !important;
        overflow: hidden;
        z-index: 999 !important;
        max-height: calc(100vh - 50px) !important;
        top: 50px;
      }

      &.thinkContentFullSmall {
        position: fixed !important;
        left: 180px;
        width: calc(100vw - 200px) !important;
        height: calc(100vh - 150px) !important;
        overflow: hidden;
        z-index: 999 !important;
        max-height: calc(100vh - 150px) !important;
        top: 210px;
      }

      .thinkHeader {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 12px 12px;

        .title {
          color: #323233;
          line-height: 20px;
          display: flex;
          align-items: center;

          img {
            height: 24px;
            width: 24px;
            margin-right: 4px;
          }
        }

        .thinkOpt {
          display: flex;

          .think-btn {
            font-size: 12px;
            margin-left: 4px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 22px;
            font-weight: bold;

            &.think-btn-blue {
              background-color: #4068d4 !important;
              border-radius: 4px;

              &:hover {
                background: #3455ad !important;
              }

              &:active {
                background: #264480;
              }
            }

            &:hover {
              background-color: #ebecf0;
              border-radius: 4px;
            }

            img {
              width: 12px;
              height: 12px;
            }
          }
        }
      }

      .thinkWrap {
        background: #ffffff;
        padding: 0px 12px 12px 36px;
        max-height: calc(100% - 40px);
        overflow-y: auto;

        .thinkItem {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          justify-content: start;
          padding: 8px 12px;
          border-radius: 4px;
          border: 1px solid #dcdde0;
          margin-top: 12px;

          &:first-child {
            margin-top: 0px;
          }
        }

        .itemContent {
          color: #646566;
          line-height: 22px;
          flex: 1;
          margin-left: 8px;
        }
      }
    }

    .clearChatBtnBox {
      // position: absolute;
      cursor: pointer;
      bottom: 78px;
      margin-left: 48px;
      // width: 100%;
      background: #ffffff;
      z-index: 2;
      display: flex;
      align-items: center;

      &.clearChatBtnBoxHigh {
        bottom: 100px !important;
      }

      .guess {
        margin-bottom: 5px;
        background: #fff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
        border-radius: 2px;
        max-width: 500px;
        min-width: 88px;
        padding: 5px;
        max-height: 150px;
        overflow-y: auto;
        position: absolute;
        bottom: 102px;
        left: 45px;
        z-index: 7;

        &.guessHigh {
          bottom: 132px !important;
        }

        .guess-header {
          font-size: 12px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 4px 8px;
          border-bottom: 1px solid #ebecf0;

          .title {
            color: #646566;
            font-size: 12px;
          }

          .huanyihuan {
            font-size: 12px;
            margin-left: 2px;
            color: #4068d4;
            cursor: pointer;

            &:hover {
              color: #3455ad;
            }

            &:active {
              color: #264480;
            }
          }

          .plansearch {
            cursor: pointer;

            &:hover {
              color: #3455ad;
            }

            &:active {
              color: #264480;
            }
          }
        }

        li {
          span {
            display: block;
            line-height: 32px;
            padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &:hover {
            background: #eff3ff;
          }

          &:active {
            background: #eff3ff;
          }
        }
      }
    }

    .selectRole {
      position: absolute;
      z-index: 99;
      width: calc(100% - 30px);
      left: 15px;
      bottom: calc(100% + 10px);
      display: flex;
      justify-content: center;
      align-items: center;
      max-height: 400px;
      overflow-y: auto;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 4px;
      flex-direction: column;

      &.selectRoleHigh {
        bottom: calc(100% + 10px) !important;
      }

      .roleItem {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 2px 4px;
        cursor: pointer;

        &:hover {
          background: #eff3ff;
        }

        &:active {
          background: #eff3ff;
        }

        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 4px;
        }

        .name {
          cursor: pointer;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
        }

        .mark-tag {
          background: #eff3ff;
          border-radius: 2px;
          padding: 0px 8px;
          color: #4068d4 !important;
          line-height: 17px;
          font-size: 12px;
          margin-left: 4px;
          display: inline-block;
          max-width: 260px;
          /* 设置文本溢出时的行为为省略号 */
          text-overflow: ellipsis;

          /* 设置超出容器的内容应该被裁剪掉 */
          overflow: hidden;

          /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
          white-space: nowrap;
        }
      }
    }

    .chatFooter {
      padding: 12px 16px;
      bottom: 0px;
      left: 0px;

      .chatFooterTextInput {
        display: flex;
        position: relative;
        width: 100%;
        background: #ffffff;
        justify-content: flex-start;
        align-items: center;


      }

      // .chatFooterImage {
      //   margin-left: 31px;
      // }
      .chatFooterImage {
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        // position: absolute;
        left: 15px;
        top: 6px;
        // bottom: 4px;
        // top: 50%;
        // right: 4px;
        // transform: translateY(-50%);
        display: flex;
        align-items: center;
        z-index: 10;
        /* 确保图片位于其他元素之上 */
      }

      .typetarea {
        :deep(textarea.el-textarea__inner) {
          // padding-top: 43px !important;
          transition: padding-top 0.3s ease;
        }

        :deep(textarea) {
          //height: 105px !important;
          transition: height 0.3s ease;
        }
      }

      .think-btn-disabled {
        width: 25px;
        height: 25px;
        cursor: pointer;
        line-height: 25px;
        margin-right: 6px;
        text-align: center;
        opacity: 0.5;
        cursor: not-allowed;
      }

      .clear {
        width: 25px;
        height: 25px;
        cursor: pointer;
        line-height: 25px;
        margin-right: 6px;
        text-align: center;

        &:hover {
          background: #EFF3FF;
        }

        &:active {
          background: #EFF3FF;
        }

        .clear-icon {
          width: 20px;
          height: 20px;
          margin-top: 3px;
        }
      }

      .chatInput {
        flex: 1;
        border-radius: 4px;
        // border: 1px solid #c8c9cc;
        position: relative;

        border: 2px solid;
        border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;


        .chatInputHeader {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #c8c9cc !important;
        }

        .clearChat {
          cursor: pointer;
          font-size: 14px;
          display: inline-block;
          font-weight: 500;
          font-size: 12px;
          color: #4068d4;
          word-break: break-all;
          height: 32px;
          border: none;

          &:hover {
            background: #f6f7fb;
          }

          &.think-btn-disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .roleTag {
          padding: 2px 4px;
          // background: #E6ECFF;
          // color: #3455AD;
          // font-size: 12px;
          // padding: 2px 4px;
          // border-radius: 2px;
        }
      }

      .send-btn {
        width: calc(100% - 35px);
        position: absolute;
        right: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-top: 6px;
        font-size: 12px;
        bottom: 4px;
        gap: 4px;

        .selectRoleDetail {
          cursor: pointer;
          display: flex;
          // align-items: center;
          max-width: 280px;
          flex-wrap: nowrap;
          // overflow-x: auto;
          height: 24px;
          align-items: center;
          border: 1px solid #dcdde0;
          padding: 2px 4px;
          border-radius: 6px;
          &:hover {
            background-color: #ebecf0;
          }

          img {
            margin-right: 4px;
            width: 20px;
            height: 20px;
          }

          .tag-item {
            margin-left: 8px;
            padding: 0 4px;
            border-radius: 2px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #4068D4;
            height: 18px;
            line-height: 18px;
            background: #E6ECFF;
          }
        }

        .disabledRole {
          opacity: 0.6;
          /* 降低透明度 */
          cursor: not-allowed;
          /* 鼠标指针变为禁用状态 */
          /* 可以根据需要添加其他样式，例如背景色变灰 */
          background-color: #f5f5f5;
        }

        .expert-upload {
          width: 32px;
          height: 32px;
          // margin-right: 8px;
          border-radius: 0;
          background-size: cover;
          background-repeat: no-repeat;
          background-size: 100%;
          background-position: center;
          background-image: url("~@/assets/images/planGenerater/expert-upload.png");
          border: none;

          &:hover,
          &:active {
            background-image: url("~@/assets/images/planGenerater/hover-upload.png");
          }
        }

        .send-desc {
          border: none;
          font-size: 10px;
          color: #969799;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-size: cover;
          background-repeat: no-repeat;
          background-size: 100%;
          background-position: center;
          background-image: url("~@/assets/images/planGenerater/active-send.png");

          &.is-disabled {
            background-image: url("~@/assets/images/planGenerater/expert-send.png");
          }

          // }
          img {
            width: 32px;
          }
        }

        .yuyinBtn {
          cursor: pointer;

          &.yuyinBtnDisabled {
            cursor: not-allowed;
          }

          img {
            width: 32px;
            height: 32px;


          }

          &:hover .yuyinstart {
            content: url("~@/assets/images/planGenerater/hover-voice.png"); // 替换为悬停图片
          }
        }

        // :deep(.el-button) {
        //   width: 20px;
        //   height: 20px;
        //   padding: 0px;
        //   background: linear-gradient(180deg, #69a0ff 0%, #375fcb 100%);
        //   border-radius: 12px !important;
        //   border: none;
        //   margin-left: 12px;
        //   margin-right: 8px;
        //   line-height: 20px;

        //   i {
        //     font-size: 12px;
        //     margin-left: -1px;
        //   }
        // }
      }

      .upload-demo {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .updloadBtn {
        width: 18px;
        height: 16px;
        background: url(@/assets/images/planGenerater/upload-icon.png) no-repeat;
        background-size: contain;
      }

      :deep(.el-input__inner) {
        border-radius: 4px;
        border-color: #c8c9cc;
        color: #323233;

        &:focus {
          border-color: #406bd4;
        }
      }

      :deep(.el-textarea__inner) {
        // border-radius: 4px;
        border: none !important;
        color: #323233;
        // padding-right: 114px;
        // margin-top: 32px;
        padding-top: 5px;
        margin-bottom: 34px;

        // margin-top: 45px;
        // border-top: 1px solid #c8c9cc!important;
        &:focus {
          border-color: #406bd4;
        }
      }
    }

  }

  .qa-loading-spinner {
    width: 42px;
    height: 36px;
    margin-left: 40px;
    background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
    background-size: 100% 100%;
    position: relative;
    border-radius: 6px;
  }

  .qa-loading-spinner2 {
    width: 42px;
    height: 36px;
    margin-left: 2px;
    background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
    background-size: 100% 100%;
    position: relative;
    border-radius: 6px;
  }

  .qa-loading-spinner3 {
    width: 42px;
    height: 36px;
    margin-left: 40px;
    background: url(@/assets/images/planGenerater/qa-loading.gif) no-repeat #f6f8fb;
    background-size: 100% 100%;
    position: relative;
    border-radius: 6px;
  }

  .kickfeat-component {
    position: absolute;
    z-index: 1001;
    bottom: 166px;
    left: 50px;
  }
  </style>
  <style lang="postcss" scoped>
  .new_flex {
    width: 132px;
    height: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #323233;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    cursor: pointer;
    margin-left: 8px;
    display: inline-block;
    /* 设置固定的宽度，根据实际情况调整 */
    /* 强制内容不换行 */
    white-space: nowrap;
    /* 超出部分隐藏 */
    overflow: hidden;
    /* 文字超出宽度时显示省略号 */
    text-overflow: ellipsis;
  }

  .new_flex:hover {
    color: #3455AD !important;
  }

  .icon_plus {
    border-radius: 6px;
    border: 1px solid #dcdde0;
    margin-right: 4px;
    height: 24px;
    /* width: 24px; */
    padding: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    border-radius: 6px;
    &:hover {
      /* border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2; */
      background-color: #ebecf0;
    }
  }

  .chuangzuo {
    margin-left: 0px;
    margin-bottom: 9px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 29px;
    width: 94px;
    border-radius: 2px;
    border: 1px solid rgba(64,107,212,0.4);
    cursor: pointer;
    &:hover{
        background: rgba(64, 107, 212, 0.40);
    }

    i {
      margin-left: 12px;
      color: #4068D4;
    }

    div {
      margin-left: 4px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #4068D4;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  .flex_chuangzuo {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    height: 31px;
    background: #EFF3FF;
    border-radius: 6px;
    /* border: 1px solid; */
    /* border-image: linear-gradient(135deg, rgba(172, 0, 255, 1), rgba(64, 104, 212, 1), rgba(0, 178, 255, 1)) 1 1; */
  }

  .cursor {
    cursor: pointer;
  }

  .image-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    height: 24px;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid #DCDDE0;
    &:hover {
      border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;
    }
    .el-image.uploaded-image {
      width: fit-content;

      :deep(img) {
        border-radius: 2px;
      }
    }

    .right {
      /* margin-left: 8px; */
      display: flex;
      align-items: center;
      span{
        width: auto;
        max-width: 100px;
        text-align: left;
        margin-left: 8px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      i {
        cursor: pointer;
        margin-left: 8px;
      }
    }

   /* 作为占位背景色，防止无图片时突兀 */

   @media (max-width: 600px) {
     width: 30px;
     height: 30px;
   }

   .uploaded-image {
     width: 100%;
     height: 100%;
     object-fit: cover;
     /* 保持图片比例并覆盖容器 */
     cursor: pointer;
   }

   .delete-image-btn {
     position: absolute;
     top: -5px;
     right: -5px;
     background-color: rgba(255, 255, 255, 0.8);
     color: red;
     font-size: 10px;
     /* 更小的字体大小 */
     width: 16px;
     height: 16px;
     padding: 0;
     display: none;
     /* 默认隐藏 */
     transition: opacity 0.3s, transform 0.3s;
     border: none;
     cursor: pointer;

     /* 使用更小的按钮尺寸 */
     .el-icon-delete {
       font-size: 10px;
     }

     &:hover {
       background-color: rgba(255, 255, 255, 1);
       transform: scale(1.2);
       /* 放大按钮，增强交互感 */
     }
   }

   &:hover {
     .delete-image-btn {
       display: block;
       opacity: 1;
     }
   }
   }

   .flex_chuangzuo_div {
     margin-left: 18px;
     display: flex;
     align-items: center;

    .icon-rotate {
      transform: rotate(180deg) !important;
      transition: transform 0.3s ease;
    }

    .el-icon-arrow-down {
      display: inline-block;
      transition: transform 0.3s;

      &.icon-rotate {
        transform: rotate(180deg) !important;
      }
    }

     i {
       color: #3455AD;
       margin-top: 3px;
     }

     div {
       margin-left: 9px;
       font-family: PingFangSC, PingFang SC;
       font-weight: 400;
       font-size: 12px;
       color: #3455AD;
       line-height: 20px;
       text-align: left;
       font-style: normal;
     }
   }
   .new_chuangzuo > *:nth-child(n+1) {
    margin-left: 8px;
  }
   .new_chuangzuo {
     display: flex;
     justify-content: flex-start;
     width: 100%;
     padding: 8px 0 8px 0;
     min-height: 40px;
     flex-wrap: wrap;

     .select {
       height: 21px;
       width: 120px;
       margin: auto 8px;
     }

     >>>.el-input__inner {
       height: 21px !important;
     }

     >>>.el-input__icon {
       line-height: 21px !important;
     }

     i {
       /* margin-left: 18px; */
     }
   }

   .dialog-dropdown :deep(.el-button) {
     border-radius: 2px;
     border: 1px solid rgba(64, 107, 212, 0.4);
     padding: 2px 8px;
     font-weight: 500;
     color: #4068d4;
     line-height: 18px;
     margin-right: 8px;
     word-break: keep-all;
     height: 24px;
     margin-left: 0px;
     font-size: 12px !important;
     /* background-color: #fff; */

     background: transparent!important;
   }

   .dialog-dropdown>>>span {
     font-size: 12px !important;
   }
   .stop-desc{
    background: #4068d4!important;
    background-image: none!important;
    cursor: pointer!important;;
  }
  .stop-desc::after{
    content: ""; /* 添加此行以确保伪元素显示 */
    width: 12px;
    height: 12px;
    display: block;
    background: #fff;
    margin-left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
  }
  .guessPovper {
      max-width: 500px;
      min-width: 88px;

      .guess-header {
        font-size: 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 4px 8px;
        border-bottom: 1px solid #ebecf0;

        .title {
          color: #646566;
          font-size: 12px;
        }

        .huanyihuan {
          font-size: 12px;
          margin-left: 2px;
          color: #4068d4;
          cursor: pointer;

          &:hover {
            color: #3455ad;
          }

          &:active {
            color: #264480;
          }
        }

        .plansearch {
          cursor: pointer;

          &:hover {
            color: #3455ad;
          }

          &:active {
            color: #264480;
          }
        }
      }

      .otherRole {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 46px;
        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 4px;
        }

        .mark-tag {
          background: #eff3ff;
          border-radius: 2px;
          padding: 0px 8px;
          color: #4068d4 !important;
          line-height: 17px;
          font-size: 12px;
          margin-left: 4px;
          display: inline-block;
          max-width: 260px;
          /* 设置文本溢出时的行为为省略号 */
          text-overflow: ellipsis;

          /* 设置超出容器的内容应该被裁剪掉 */
          overflow: hidden;

          /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
          white-space: nowrap;
        }
      }

      li {
        cursor: pointer;

        span {
          display: block;
          line-height: 32px;
          padding: 0 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }

        &:hover {
          background: #eff3ff;
        }

        &:active {
          background: #eff3ff;
        }
      }
    }
    .AcotherRole{
      background-color: #f5f7fa;

    }
    .Role{
      padding: 0 20px;
      &:hover{
        background-color: #f5f7fa;
      }
    }
    .disabledStatus{
      cursor: not-allowed;
    }
    .magicApi{
      margin-left: 8px;
      width: 84px;
      justify-content: center;
    }
    .vertical-line {
      width: 1px;
      height: 14px;
      background-color: #ccc;
      margin-right: 11px;
      margin-bottom: 10px;
  }
  .setyleRole{
    left: auto !important;
    right: 16px;
    .new_flex{
      width: 65px;
    }
  }
  .flex_body{
    display: flex;
    align-items: center;
  }
  .file_flex{
    display: flex;
    flex-wrap: wrap;
    gap: 0px 10px;
    max-width: calc(100% - 40px);
    div{
      font-size: 12px;
      color: #000;
    }
  }
  </style>
  <style>
  .popperRole{
    padding: 0px;
  }
  </style>
