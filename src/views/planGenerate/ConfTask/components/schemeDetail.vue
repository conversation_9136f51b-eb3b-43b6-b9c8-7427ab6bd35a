<template>
    <div>
        <el-select v-model="rel_ioc_card_code" placeholder="请选择" @change="update" filterable remote reserve-keyword
            :remote-method="remoteMethod" :loading="loading">
            <el-option v-for="item in options" :key="item.id" :label="item.name + '(' + item.version + ')'"
                :value="item.id">
                <el-tooltip placement="top">
                    <div slot="content"><img :src="item.thumbnail" alt="" width="200" ></div>
                    <div>
                        <img :src="item.thumbnail" alt="" width="34" height="34">
                        <span style="margin-left: 20px;">{{ item.name + '(' + item.version + ')' }}</span>
                    </div>

                </el-tooltip>
            </el-option>
        </el-select>
    </div>

</template>
<script>
import {
    getIocCardList, setRelIocCardCode
} from '@/api/planGenerateApi.js';
export default {
    props: {
        schemeDetailData: {
            type: Object
        }
    },
    data() {
        return {
            rel_ioc_card_code: '',
            options: [],
            loading: false
        }
    },
    watch: {
        schemeDetailData: {
            handler(newVal, oldVal) {
                if (newVal != oldVal) {
                    this.getData({
                        keywords: ''
                    })
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        async remoteMethod(query) {
            if (query !== '') {
                this.loading = true;
                await this.getData({
                    keywords: query
                })
                this.loading = false;
            } else {
                this.options = [];
            }
        },
        async getData(params) {
            const res = await getIocCardList(params)
            this.options = res?.data?.result?.rows.map(item => {
                return {
                    ...item,
                    id: item.id.toString()
                }
            }) || []
            this.rel_ioc_card_code = this.schemeDetailData.rel_ioc_card_code
        },
        async update(item) {
            const res = await setRelIocCardCode({
                "ability_id": this.schemeDetailData.ability_id,
                "ioc_card_code": item.toString()
            })
        }
    }
}
</script>
<style lang="postcss" scoped></style>