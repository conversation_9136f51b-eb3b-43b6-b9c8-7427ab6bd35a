<template>
  <el-dialog :visible.sync="visible" title="关联上下文" width="700px" @closed="setVisible" append-to-body
    custom-class="task_body">
    <div>
      <el-form ref="form" :model="currentToolForm" label-width="80px" label-position="top" class="custom-tool-form">
        <!-- <el-form-item label="关联生产上下文">
                    <template #label>
                        <div class="label-container">
                            <span class="title">关联生产上下文:</span>
                        </div>
                    </template>
<div class="flex_all flex_all_new">
  <el-cascader :options="newCascaderOptions" v-model="newCascaderOptionsValue" :props="{ multiple: true }" collapse-tags
    style="width:623px" clearable></el-cascader>
</div>

</el-form-item>
<el-form-item label="关联产物">
  <template #label>
                        <div class="label-container">
                            <span class="title">关联产物:</span>
                        </div>
                    </template>
  <div class="flex_all">
    <el-cascader :options="cascaderOptions" v-model="cascaderOptionsValue" :props="{ multiple: true }" collapse-tags
      style="width:623px" clearable></el-cascader>
  </div>
</el-form-item> -->
        <el-form-item label="关联文件">
          <template #label>
            <div class="label-container">
              <span class="title">关联文件:</span>
              <el-tooltip class="item" effect="dark" content="选择文件" placement="top">
                <i class="el-icon-plus cursor" style="font-size: 20px;" @click="addChose"></i>
              </el-tooltip>
            </div>
          </template>
          <div class="flex_all">
            <div class="image-wrapper" v-if="checkedFiles?.length > 0" v-for="(ivt, invt) in checkedFiles" :key="invt">
              <i class="el-icon-paperclip"></i>
              <div class="right">
                <el-tooltip class="item" effect="dark" :content="ivt.name" placement="top">
                  <span>{{ ivt.name }}</span>
                </el-tooltip>
                <i class="el-icon-close cursor" @click="clearFile(invt)"></i>
              </div>
            </div>
            <div v-if="showSelectVisable" style="padding: 10px;" :class="'selectRole'"
              v-click-outside="onClickOutside2">
              <AbilitysFile :checkedFilesData="checkedFiles" :firstUrl="firstUrl" :fileData="abilitys"
                styleOver="overflow: hidden;" @checkedChange="handleCheckedFiles" />
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="setVisible">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import {
  get_scheme_materialsFetch,
  get_upd_agent_context,
  add_upd_agent_context
}
  from '@/api/planGenerateApi.js'
import AbilitysFile from './abilitysFile.vue';
import vClickOutside from 'v-click-outside';
export default {
  components: {
    AbilitysFile
  },
  directives: {
    clickOutside: vClickOutside.directive
  },
  props: {
    abilitys: {
      type: Object,
      default: () => {}
    },
    firstUrl: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    tools: {
      type: Array,
      required: true
    },
    optHeader2TabsList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      showSelectVisable: false,
      checkedFiles: [],
      content: {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      },
      cascaderOptionsValue: [],
      cascaderOptions: [
        {
          value: 1,
          label: '生产产物',
          children: this.optHeader2TabsList.map(item => this.transformItem(item))
        }
      ],
      requirement: '',
      id: '',
      newCascaderOptions: [],
      newCascaderOptionsValue: [],
      upload: require('@/assets/images/planGenerater/expert-upload.png'),
      currentToolForm: {
        selectedTool: '', // 确保这里有一个初始值
        cascaderVal: [],
        desc: '',
      },
    }
  },
  methods: {
    clearFile(index) {
      this.checkedFiles.splice(index, 1)
    },
    onClickOutside2() {
      this.showSelectVisable = false
    },
    handleCheckedFiles(checkedFiles) {
      // 这里可以获取到所有选中的文件数据
      console.log('选中的文件：', checkedFiles)
      this.checkedFiles = checkedFiles
    },
    addChose() {
      this.showSelectVisable = true
    },
    setVisible() {
      this.$emit('setVisible', false)
    },
    async submitForm() {
      const tabsArr = [];
      // let result = 'materials.关联文件.'
      // this.newCascaderOptionsValue.forEach(item => {
      //   if (item[0] == 3) {
      //     result += item[1].toString() + ','
      //   } else {
      //     tabsArr.push(item[1] ?? item[0]);
      //   }
      // });
      // if (result == 'materials.关联文件.') {
      //   result = ''
      // }
      // this.cascaderOptionsValue.forEach(item => {
      //   if (item[0] == 3) {
      //     result += item[1].toString() + ','
      //   } else {
      //     tabsArr.push(item[1] ?? item[0]);
      //   }
      // });
      // console.log("tabsArr111", tabsArr);
      // if (result != 'materials.关联文件.') {
      //   result = result.replace(/,$/, '')
      // }
      // tabsArr.push(result)
      let result_new = 'file.'
        this.checkedFiles.forEach(item => {
          result_new += item.objectKey.toString() + ','
        })
        if (this.checkedFiles.length > 0) {
          tabsArr.push(result_new)
        }
      const params = {
        context_codes: tabsArr.filter(it => it != '') || [],
      }
      await add_upd_agent_context({
        "scheme_id": this.$route.query.id,
        "agent_template_id": "api_generate_ability",
        "context_codes": tabsArr.filter(it => it != '') || [],
      })
      this.$router.push({
        name: 'planGenerateRunTask',
        params: {
          startRun: true
        },
        query: {
          ...this.$route.query,
          backStatus: 1,
          instruction_param: JSON.stringify(params)
        }
      })
      console.log("generalExecute params", params);
    },
    transformItem(item) {
      const transformed = {
        ...item,
        value: item.name,
        label: item.label,
        isSelect: false
      };
      return transformed;
    },
    async getData(status, data) {
      const res = await get_scheme_materialsFetch({
        "scheme_id": Number(this.$route.query.id),
      })
      this.requirement = res.data.result?.requirement || ''
      this.id = res.data.result?.id || ''
      this.content = res.data.result?.content || {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      };
      this.newCascaderOptions = [
        {
          value: 0,
          label: '需求说明',
          children: [
            {
              value: 'materials.背景',
              label: '背景',
            },
            {
              value: 'materials.目标',
              label: '目标',
            },
          ]
        },
        {
          value: 3,
          label: '关联文件',
          children: this.content.files.map(it => {
            return {
              value: it.fileId, label: it.name,
            }
          })
        }
      ]
      if (status) {
        if (this.newCascaderOptionsValue.length > 0 && this.newCascaderOptionsValue[0][0] == 'materials.需求说明') {
          console.log(this.content.files, 'this.content.files')
          this.content.files.forEach((it, index) => {
            if (it.fileId == data) {
              this.$set(this.newCascaderOptionsValue, 1 + index, [3, data])
            }
          })
        } else {
          console.log(this.content.files, 'this.content.files')
          this.content.files.forEach((it, index) => {
            if (it.fileId == data) {
              this.newCascaderOptionsValue.push([3, data])
              this.$forceUpdate()
              // this.$set(this.newCascaderOptionsValue,this.newCascaderOptionsValue.length,[3,data])
            }
          })
        }
      }
      console.log(this.cascaderOptionsValue, this.newCascaderOptionsValue, 'this.newCascaderOptionsValue')
    },
  },
  watch: {
    visible: {
      async handler(val, oldVal) {
        if (val) {
          const res = await get_upd_agent_context(
            {
              "scheme_id": this.$route.query.id,
              "agent_template_id": "api_generate_ability"
            }
          )
          if (res.data.result.length > 0) {
            const context_codes = res.data.result
            let first = []
      let second = []
      let third = []
      let four = []
      context_codes?.forEach(it => {
        if (it.indexOf('materials.背景') > -1) {
          first.push(it)
        }
        else if (it.indexOf('materials.目标') > -1) {
          first.push(it)
        } else if (it.indexOf('materials.关联文件.') > -1) {//materials.关联文件
          let data = it.split('materials.关联文件.')
          console.log(data, 'data')
          let newData = data[1].split(',')
          // second = newData.map(it => Number(it))
          this.content.files.forEach((it) => {
            newData.forEach(item => {
              if (it.fileId == item) {
                second.push(it.fileId)
              }
            })
          })
        } else if (it.indexOf('file.') > -1) {//materials.关联文件
          let data = it.split('file.')
          console.log(data, 'data')
          four = data[1].split(',')
        } else {
          third.push(it)
        }
      })
      let secondData = []
      if (first.length > 0) {
        first.forEach((it, index) => {
          secondData.push([0, it])
        })
        second.forEach((it, index) => {
          secondData.push([3, it])
        })
      } else {
        second.forEach((it, index) => {
          secondData.push([3, it])
        })
      }
      let thirdData = []
      third.forEach((it, index) => {
        thirdData.push([1, it])
      })
      this.newCascaderOptionsValue = secondData
      this.cascaderOptionsValue = thirdData
      this.checkedFiles = four.filter(it => it).map(it => {
        return {
          isDir: false,
          name: it.substring(it.lastIndexOf('/') + 1),
          objectKey: it,
          objectSize: 0,
          objectType: "",
          scheme_id: this.$route.query.id,
          checked: false
        }
      })

          this.getData()
        }
      }
      },
      immediate: true,
      deep: true
    },
    optHeader2TabsList: {
      handler(val, oldVal) {
        this.cascaderOptions = [
          {
            value: 1,
            label: '生产产物',
            children: val.filter(item => item.name != '文件key' && item.name != 'api_generate_ability.代码生成').map(item => this.transformItem(item))
          }
        ]
      },
      immediate: true,
      deep: true
    }
  }
}
</script>
<style scoped lang="scss">
.custom-tool-form {
  position: relative;

  :deep(.el-form-item__label) {
    padding-bottom: 0px !important;
    /* 调整 label 与内容的间距 */
    font-size: 14px;
    /* 调整字体大小 */
    font-weight: 600;
    /* 增加一点加粗效果 */
    color: #606266;
    /* 使用更舒适的颜色 */
  }

  .custom-select {
    width: 100%;
  }
}

.title {
  font-weight: bold; // 加粗
  color: #000; // 黑色
  margin-bottom: 10px; // 调整与下方内容的距离
  line-height: 30px;
  height: 20px;
}

.flex_all {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  gap: 5px;
}

.flex_all_new {
  margin-bottom: 10px;
  gap: 0px
}

.upload-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #DCDDE0; // 添加边框
  border-radius: 4px; // 圆角
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果
  width: 34px;
  height: 34px;
  border-radius: 2px;
  border: 1px solid #C8C9CC;
  margin-left: 4px;

  &:hover {
    background-color: #f5f7fa; // 悬停时背景色
    border-color: #409eff; // 悬停时边框颜色
  }

  img {
    transition: opacity 0.3s; // 添加过渡效果

    &:hover {
      opacity: 0.8; // 悬停时降低透明度
    }
  }
}

.cur {
  cursor: pointer;
}

.label-container {
  display: flex;
  align-items: center; // 确保元素垂直居中
  gap: 8px; // 设置元素之间的间距
  font-size: 14px; // 统一字体大小
  margin-top: 10px; // 增加与上方 form-item 的距离
  margin-bottom: 8px; // 调整与下方内容的距离

  .title {
    font-weight: bold; // 加粗
    color: #000; // 黑色
  }

  .flex_add {
    font-size: 16px; // 调整加号图标大小
    border: 1px solid #DCDDE0; // 添加边框
    border-radius: 4px; // 圆角
    padding: 4px; // 内边距
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果

    &:hover {
      background-color: #f5f7fa; // 悬停时背景色
      border-color: #409eff; // 悬停时边框颜色
    }
  }
}
.image-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  height: 24px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #DCDDE0;

  &:hover {
    border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;
  }

  .el-image.uploaded-image {
    width: fit-content;

    :deep(img) {
      border-radius: 2px;
    }
  }

  .right {
    /* margin-left: 8px; */
    display: flex;
    align-items: center;

    span {
      width: auto;
      max-width: 100px;
      text-align: left;
      margin-left: 8px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    i {
      cursor: pointer;
      margin-left: 8px;
    }
  }

  /* 作为占位背景色，防止无图片时突兀 */

  @media (max-width: 600px) {
    width: 30px;
    height: 30px;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 保持图片比例并覆盖容器 */
    cursor: pointer;
  }

  .delete-image-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(255, 255, 255, 0.8);
    color: red;
    font-size: 10px;
    /* 更小的字体大小 */
    width: 16px;
    height: 16px;
    padding: 0;
    display: none;
    /* 默认隐藏 */
    transition: opacity 0.3s, transform 0.3s;
    border: none;
    cursor: pointer;

    /* 使用更小的按钮尺寸 */
    .el-icon-delete {
      font-size: 10px;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 1);
      transform: scale(1.2);
      /* 放大按钮，增强交互感 */
    }
  }

  &:hover {
    .delete-image-btn {
      display: block;
      opacity: 1;
    }
  }
}
.flex_all {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  gap: 5px;
}
.right {
    /* margin-left: 8px; */
    display: flex;
    align-items: center;

    span {
      width: auto;
      max-width: 100px;
      text-align: left;
      margin-left: 8px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    i {
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .selectRole {
  position: absolute;
  width: 330px;
  z-index: 99;
  top: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px;
  display: flex;
  flex-direction: column;

  .selectRoleHigh {
    bottom: 102px !important;
  }

  .roleItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 2px 4px;
    cursor: pointer;

    &:hover {
      background: #eff3ff;
    }

    &:active {
      background: #eff3ff;
    }

    img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 4px;
    }

    .name {
      cursor: pointer;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }

    .mark-tag {
      background: #eff3ff;
      border-radius: 2px;
      padding: 0px 8px;
      color: #4068d4 !important;
      line-height: 17px;
      font-size: 12px;
      margin-left: 4px;
      display: inline-block;
      max-width: 260px;
      /* 设置文本溢出时的行为为省略号 */
      text-overflow: ellipsis;

      /* 设置超出容器的内容应该被裁剪掉 */
      overflow: hidden;

      /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
      white-space: nowrap;
    }
  }
}
</style>
<style lang="scss">
.task_body {
  .el-dialog__body {
    padding: 0px 20px;
  }
}
</style>
