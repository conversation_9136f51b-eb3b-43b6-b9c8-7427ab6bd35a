<template>
    <div class="optScroll">
        <!-- 头部 -->
        <div class="header-container">
            <div class="header-left">
                <div class="title-wrap">
                    <span class="title">{{ getJujiaoStatus?.data?.label }}</span>
                </div>
                <el-dropdown >
                  <div class="solid_class"><i class="el-icon-more"></i> </div>
                      <el-dropdown-menu slot="dropdown">
                        <!-- 直接渲染所有标签 -->
                        <el-dropdown-item
                          v-for="tag in optHeader2TabsList"
                          :key="tag.name"
                          :command="tag.name"
                          class="tag-selected"
                          @click.native="()=>{changeTab(tag)}"
                        >
                          {{ tag.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
            </div>
            <div class="header-right">
                <div @click="back">
                    <span class="title" style="margin-right: 8px">{{  '退出聚焦模式' }}</span>
                    <i class="el-icon-close"></i>
                </div>
            </div>
        </div>
        <div style="overflow: auto;    padding: 0px 20px  0px  20px!important;"  v-if="getJujiaoStatus.data?.name === '方案详情key'" :key="getJujiaoStatus.data?.name">
            <div v-show="!dagangFlag" id="detail-content" class="optContentBox" @mouseenter="fangda"
                @mouseup="fangda2">
                <MyEditor id="MyEditor" ref="MyEditorRef" :md-content="detailContent.text" :is-edit="isEdit"
                    @updateContent="handleUpdateContent"></MyEditor>
            </div>
            <div v-show="dagangFlag && !isEdit" style="width: 100%; height: 100%;position: absolute;"
                @mouseenter="fangda">
                <svg id="markmap" class="mark-map"></svg>
            </div>
            <div class="enn-markdown-div" v-if="getJujiaoStatus.data?.name === '方案详情key'">
                <!-- <el-tooltip v-if="!dagangFlag && !isEdit && !isEmbedTool" class="item" effect="dark" content="方案优化"
                    placement="top">
                    <el-button
                        :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                        type="info" size="mini" @click="() => { SOdialogVisible = true }">
                        <img src="@/assets/images/planGenerater/ic_fangan.png" />
                    </el-button>
                </el-tooltip> -->
                <el-tooltip v-if="!dagangFlag && !isEdit" class="item" effect="dark" content="脑图" placement="top">
                    <el-button
                        :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                        type="info" size="mini" @click="changeDagang">
                        <img src="@/assets/images/planGenerater/ic_naotu.png" />
                    </el-button>
                </el-tooltip>
                <el-tooltip v-if="dagangFlag && !isEdit" class="item" effect="dark" content="大纲" placement="top">
                    <el-button
                        :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                        type="info" size="mini" @click="changeDagang"><img
                            src="@/assets/images/planGenerater/ic_dagang.png" /></el-button>
                </el-tooltip>
                <el-tooltip v-if="!isEdit && !isEmbedTool" class="item" effect="dark" content="编辑" placement="top">
                    <el-button
                        :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                        type="info" size="mini" @click="() => {
                            ChangedetailContentFun()
                        }
                        "><img src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
                    <el-button type="info" size="mini" @click="handleDetailSave"><img
                            src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
                    <el-button type="info" size="mini" @click="handleDetailSaveClose"><img
                            src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="复制" placement="top" v-if='!isEmbedTool'
                    :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                    <el-button :disabled="isReadOnly" type="info" size="mini"
                        @click="copyTextNew(detailContent.text)"><img
                            src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="生成过程" placement="top" v-if='!isEmbedTool'
                    :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                    <el-button :disabled="isReadOnly" type="info" size="mini" @click="showSikao()"><img
                            src="@/assets/images/planGenerater/ic_guocheng.png" /></el-button>
                </el-tooltip>
            </div>
            <el-link type="primary">{{ detailContent.file_url }}</el-link>
        </div>
        <template v-else-if="getComponent(getJujiaoStatus?.data?.display_component)">
            <div v-if="!dagangFlagNew" style="width:100%;    height: 100%;overflow: auto;    padding: 0px 20px  0px  20px!important;">
                <!-- <EnnEmptyContent v-if="!getJujiaoStatus.data.execute_result && !isEmpty" -->
                    <!-- :key="`empty-${getJujiaoStatus.data?.name}`" /> -->
                        <component :ref="`tab-${getJujiaoStatus.data?.name}`"
                        :is="getComponent(getJujiaoStatus?.data?.display_component)" :key="getJujiaoStatus.data?.name"
                        :item="getJujiaoStatus.data" :optHeader2TabsList="optHeader2TabsList" @handleSave="handleSave"
                        v-bind="getComponentProps(getJujiaoStatus.data)"
                        :firstUrl="firstUrl"
                        v-on="getComponentEmitHandlers(getJujiaoStatus.data)" v-loading="getJujiaoStatus.data.isLoading"
                        element-loading-text="生成中..." element-loading-spinner="el-icon-loading" :isEdit="isEdit" />  
            </div>
            <div v-if="getJujiaoStatus.data?.name !== '方案详情key' && getJujiaoStatus.data?.name !== '文件key' ">
                <div class="enn-markdown-div">
                    <el-tooltip
                        v-if="!dagangFlagNew && !isEdit && getJujiaoStatus?.data?.display_component == 'EnnMarkdown'"
                        class="item" effect="dark" content="脑图" placement="top">
                        <el-button
                            :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                            type="info" size="mini" @click="changeDagangNew">
                            <img src="@/assets/images/planGenerater/ic_naotu.png" />
                        </el-button>
                    </el-tooltip>
                    <el-tooltip
                        v-if="dagangFlagNew && !isEdit && getJujiaoStatus?.data?.display_component == 'EnnMarkdown'"
                        class="item" effect="dark" content="大纲" placement="top">
                        <el-button
                            :disabled="systemMessages === 'scheme generating' || ((hasChatingName !== '' || isOccupied || isReadOnly) && !isEmbedTool)"
                            type="info" size="mini" @click="changeDagangNew"><img
                                src="@/assets/images/planGenerater/ic_dagang.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip
                        v-if="!isEdit && !isEmbedTool && getJujiaoStatus?.data?.display_component == 'EnnMarkdown'"
                        class="item" effect="dark" content="编辑" placement="top">
                        <el-button
                            :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied || isReadOnly"
                            type="info" size="mini" @click="() => {
                               changeEditFun()
                            }
                            "><img src="@/assets/images/planGenerater/ic_bianji.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="保存" placement="top" v-if="isEdit">
                        <el-button type="info" size="mini" @click="() => { newHanderCaiNa(getJujiaoStatus.data?.name) }"><img
                                src="@/assets/images/planGenerater/ic_baocun.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="取消" placement="top" v-if="isEdit">
                        <el-button type="info" size="mini" @click="() => { closeCaiNa()}"><img
                                src="@/assets/images/planGenerater/ic_quxiao.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="复制" placement="top" v-if='!isEmbedTool'
                        :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                        <el-button :disabled="isReadOnly" type="info" size="mini" @click="copyTextNewEnn()"><img
                                src="@/assets/images/planGenerater/ic_fuzhi.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="生成过程" placement="top" v-if='!isEmbedTool'
                        :disabled="systemMessages === 'scheme generating' || hasChatingName !== '' || isOccupied">
                        <el-button type="info" size="mini" @click="showSikao()"><img
                                src="@/assets/images/planGenerater/ic_guocheng.png" /></el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="JSON美化" placement="top" v-if="getJujiaoStatus?.data?.display_component == 'EnnJson'">
                        <el-button type="info" size="mini" @click="JSONEnn(getJujiaoStatus?.data?.name)"><img
                        src="@/assets/images/planGenerater/JSON.png" /></el-button>
                    </el-tooltip>
                </div>
            </div>
        </template>
        <div v-else-if="dagangFlagNew" style="width: 100%; height: 100%;position: absolute;    padding: 0px 20px  0px  20px!important;" @mouseenter="fangda">
            <svg id="markmapenn" class="mark-map"></svg>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions, mapState } from 'vuex'
import MyEditor from '../../mdEditor.vue'
import EnnEmptyContent from '../toolComponents/EnnEmptyContent.vue';
export default {
    data() {
        return {

        }
    },
    components: {
        MyEditor,
        EnnEmptyContent
    },
    computed: {
        ...mapGetters({
            getJujiaoStatus: 'common/getJujiaoStatus',
            gettaskJujiaoStatus: 'common/gettaskJujiaoStatus',
        }),
    },
    props: {
          firstUrl: {
          type: String,
        },
        optHeader2TabsList: Array,
        isEdit: Boolean,
        isEmbedTool: Boolean,
        getComponent: Function,
        getComponentProps: Function,
        getComponentEmitHandlers: Function,
        detailContent: Object,
        dagangFlag: Boolean,
        dagangFlagNew: Boolean,
        isReadOnly: Boolean,
        isOccupied: Boolean,
        fangda2: Function,
        fangda: Function,
        handleUpdateContent: Function,
        handleSave: Function,
        handleDetailSave: Function,
        showSikao: Function,
        changeDagang: Function,
        changeDagangNew: Function,
        hasChatingName: String,
        systemMessages: String,
        hisDetail: String,
        getInstructionParam:Function,
        ChangedetailContent:Function,
        changeEdit: Function,
        isDisabled:Boolean,
    },
    methods: {
        JSONEnn(name){
            let data = `tab-${name}`
            this.$refs[data].JSONEnn()
        },
        closeCaiNa(){
            this.$emit('changeEdit')
            this.$emit('closeCaiNaFunc',this.getJujiaoStatus.data,)
        },
        changeEditFun(){
            this.$emit('changeEdit')
        },
        ChangedetailContentFun(){
            this.$emit('ChangedetailContent',this.detailContent.text)
        },
        newHanderCaiNa() {
            let text = this.$refs[`tab-${this.getJujiaoStatus.data?.name}`].executeResultData
            this.$emit('handleSave',text,this.getJujiaoStatus.data, true, false) 
            this.$emit('changeEdit')
        },
        handleDetailSaveClose() {
            console.log('handleDetailSaveClose')
            this.$emit('handleDetailSaveCloseNew')
            // this.$refs.MyEditorRef.$refs.editorFin.text = this.hisDetail
        },
        back(){
            if(this.gettaskJujiaoStatus.status){
                let query = this.getInstructionParam
                this.$router.push({path:'/planGenerate/runTask',query:{
                    ...this.$route.query,
                    // backStatus:1,
                    instruction_param: JSON.stringify(query)
                }})
                this.$store.commit('common/setTaskJujiaoStatus', {
                    status: false,
                    data: {}
                })
                return
            }
            this.$store.commit('common/setJujiaoStatus', {
                data: {},
                status:false
            })
            this.$emit('closeJJ')
        },
        changeTab(tag) {
            let data =  this.optHeader2TabsList.find((item) => item.name === tag.name)
            this.$store.commit('common/setJujiaoStatus', {
                data: data,
                status:true
            })
        },
        copyTextNewEnn(){
            try {
                let text = this.$refs.MyEditorRef.$refs.editorFin.text
                this.copyTextNew(text) 
            }catch (error) {
                const currentComponent = this.$refs[`tab-${this.getJujiaoStatus.data?.name}`]; // 获取动态组件实例
                this.copyTextNew(currentComponent.executeResultData)
            }
            
        },
        copyTextNew(text) {
      // 获取需要复制的文本
      console.log(text,'text')
      if (!text) {
        this.$message({
          type: 'warning',
          message: '复制的内容不能为空！'
        })
        return
      }

      try {
        if (navigator.clipboard && window.isSecureContext) {
          // 使用新的 Clipboard API
          navigator.clipboard.writeText(text)
            .then(() => {
              this.$message({
                type: 'success',
                message: '复制成功！'
              })
            })
            .catch(() => {
              this.fallbackCopyText(text)
            })
        } else {
          // 使用传统方法
          this.fallbackCopyText(text)
        }
      } catch (error) {
        console.error('复制失败:', error)
        this.$message({
          type: 'error',
          message: '复制失败！'
        })
      }
    }
    },
}    
</script>
<style lang="scss" scoped>
.optScroll {
    position: relative;
    overflow-y: hidden;
    overflow-x: hidden;
    padding: 0px!important;
    display: flex;
    flex-direction: column;
    height: 100%!important;
    padding-bottom:0px!important;
    ::v-deep .el-textarea {
      margin-bottom: 10px;
    }

    :deep(.scrollbar__bar.is-vertical) {
      top: 2px;
      width: 4px;
      /* width: 6px; */
    }

    .btn {
      position: absolute;
      bottom: 0;
      right: 20px;
    }
  }
  .optContentBox {
        //height: calc(100% - 340px);
        // max-height: calc(100vh - 340px);
        // max-height: 100%;
        // height: 100%;
        // overflow-y: auto;
        width: 100%;
        position: relative;
        background: transparent !important;
      }
      
      .enn-markdown-div{
  position:absolute;
  z-index: 1100;
  text-align: center;
  background: #fff;
  box-shadow:0 12px 24px -16px rgba(54,54,73,.04),0 12px 40px 0 rgba(51,51,71,.08),0 0 1px 0 rgba(44,44,54,.02);
  bottom: 54px;
  display: flex;
  gap: 4px;
  height: 40px;
  justify-content: space-between;
  left: 50%;
  padding:  16px;
  border: 1px solid #e8eaf2;
  border-radius: 16px;
  align-items: center;
  transform: translateX(-50%);
}
</style>
<style lang="postcss">
.enn-markdown-div .el-button--info{
  background:none!important;
  border: none!important;
}
.center_flex{
  .el-tabs--top{
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="scss" scoped>
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding:  0px 0px 12px 0;  // 增加左右内边距
    margin-bottom: 16px;
    box-shadow: inset 0px -1px 0px 0px rgba(0,0,0,0.09);
    background: #fff;

    .header-left {
        display: flex;
        align-items: center;
        padding-left: 20px!important;
        .title-wrap {
            display: flex;
            align-items: center;
            gap: 8px;

            .title {
                font-size: 16px;
                font-weight: 500;
                color: #333;
                line-height: 24px;
                margin-right: 16px;  // 增加标题右侧间距从12px到16px
            }
        }

        .solid_class {
            cursor: pointer;
            padding: 4px 12px;  // 增加内边距从8px到12px
            margin-left: 8px;   // 添加左侧间距
            &:hover {
                background: rgba(0,0,0,0.04);
                border-radius: 4px;
            }
        }
    }

    .header-right {
        padding-right: 20px!important;
        .el-button {
            display: flex;
            align-items: center;
            gap: 8px;  // 图标和文字间距
            padding: 0;
            font-size: 14px;
            color: #333;

            .title {
                margin-right: 4px;  // 文字和图标的间距
            }

            i {
                font-size: 16px;
            }

            &:hover {
                color: #4068d4;
            }
        }
    }
}
.mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}
</style>

