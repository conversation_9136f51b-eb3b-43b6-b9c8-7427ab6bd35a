<template>
  <el-drawer :withHeader="false" title="" :visible.sync="isShow" :direction="'btt'" :modal-append-to-body="false"
    :destroy-on-close="true" close-delay="240" size="97%" :before-close="handleClose" @closed="HandleModalClosed"
    :show-close="true" @open="handleOpen">
    <el-button icon="el-icon-close" circle style="float: right; border: none;" class="CloseButton" @click="()=>isShow = false"></el-button>
    <div class="modal-content">
      <!-- 左侧部分: Chat 组件 -->
      <div id="modal-chat-content">
        <slot></slot> <!-- 在此渲染 Chat 组件 -->
      </div>

      <!-- 右侧部分: 代码区域 -->
      <div class="right-side">
        <!-- Header区域，包含模式切换 -->
        <div class="right-side-header">
          <div class="switch-btn" v-if="codeState.Modalcodetype === 'html'">
            <el-switch v-model="isPreviewMode" active-text="预览" inactive-text="代码" />
          </div>
        </div>
        <!-- 代码展示或预览区 -->
        <div class="code-preview">
          <div :class="['preview-mode', { active: isPreviewMode }]">
            <!-- 预览模式内容 -->
            <!-- <p>这里展示预览内容</p> -->
            <myIframe :iframeContent="localCode" />
          </div>
          <div :class="['code-mode', { active: !isPreviewMode }]">
            <!-- 代码展示内容 -->
            <!-- <MyEditorPreview style="" :fromModalnotCode="true"  :mdContent="`~~~${codeState.Modalcodetype}\n${localCode}\n~~~`"
              :showButton="false" :id="'yeahztest'">
            </MyEditorPreview> -->
            <div v-html="md.render(`~~~${codeState.Modalcodetype}\n${localCode}\n~~~`)" />
            <!-- <MyEditor style="" :mdContent="`~~~${codeState.Modalcodetype}\n${localCode}\n~~~`"
              :showButton="false" :id="'yeahztest'">
            </MyEditor> -->
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import MyEditorPreview from '../../mdEditorPreview.vue'
import MyEditor from '../../mdEditor.vue';
import MarkdownIt from 'markdown-it';
import myIframe from '../../myIframe.vue';
import hljs from 'highlight.js';
import { done } from 'nprogress';
// import { ElIconButton } from '@enn/ency-design'
// const md = MarkdownIt();
export default {
  components: {
    MyEditorPreview,
    MyEditor,
    myIframe
  },
  props: {
    value: { // 用于v-model同步父组件的显示状态
      type: Boolean,
      default: false,
    },
    splitScreenCode: {
      type: String,
      default: '',
    },
    codeState:{
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isPreviewMode: false,  // 默认是预览模式
      localCode: '',
      md: MarkdownIt({
        highlight: (str, lang) => {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang }).value}</code></pre>`;
            } catch (_) { }
          }
          return '';
        }
      })
    };
  },
  watch: {
    // splitScreenCode: {
    //   handler(val, oldVal) {
    //     this.localCode = val
    //     this.isPreviewMode = false
    //   },
    //   immediate: true
    // },
    isPreviewMode: {
      handler(val, oldVal) {
        if (val === true) {
          this.$emit('clickShowFlase',false);
        }
      },
    },
    codeState: {
      handler(val, oldVal) {
        console.log('val codeState', val.generateing, val.clickShow);
        this.localCode = val?.currentModalCode
        if (val?.generateing === true) {
          this.isPreviewMode = false
        }
        if (val?.generateing === false) {
          this.isPreviewMode = true
        }
        if (val.clickShow === true ){
          this.isPreviewMode = false
          console.log('val.clickShow', val.clickShow, this.isPreviewMode);
        }
      },
      deep: true
    },
  },
  mounted() {
  },
  computed: {
    isShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  methods: {
    handleOpen(){
      this.isPreviewMode = true
    },
    HandleModalClosed() {
      this.$emit('ModalClosed',false);
      window.fenceruleChanged = false
    },
    handleClose(done){
      console.log('this.codeState.generateing', this.codeState.generateing);
      if (this.codeState.generateing){
        return
      }
      done()
    }
  }
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  // overflow-y: hidden;
  // ::-webkit-scrollbar {
  //   display: none;
  // }
}

:deep(.el-drawer_body) {
  overflow: hidden;

}

.modal-content {
  display: flex; // 使用 flexbox 实现左右布局
  justify-content: space-between; // 两部分分开布局
  height: 100%; // 确保内容区域满高

  /* 左侧部分: Chat 组件 */
  #modal-chat-content {
    flex: 1; // 左侧占满剩余空间
    padding: 20px;
    overflow-y: auto; // 防止内容溢出
  }

  /* 右侧部分: 代码展示区域 */
  .right-side {
    height: 100%;
    width: 50%; // 设置右侧宽度为 30%
    display: flex;
    flex-direction: column; // 纵向布局
    justify-content: flex-start;
    padding: 20px;
    background-color: #fff;
    border-left: 1px solid #ddd; // 给右侧部分添加分割线
      ::-webkit-scrollbar {
          display: none;
        }
    /* Header 区域 */
    .right-side-header {
      display: flex;
      justify-content: space-between; // 切换按钮和标题的左右布局
      align-items: center;
      margin-bottom: 20px;

      /* 切换按钮样式 */
      .switch-btn {
        display: flex;
        align-items: center;
        cursor: pointer;

        label {
          margin-left: 8px;
        }
      }
    }

    /* 代码展示区 */
    .code-preview {
      display: flex;
      flex: 1;
      flex-direction: column;
      background-color: #fafafa; // 可以自定义背景色
      overflow-y: auto;
      height: 100%;

      /* 模式切换后，展示代码区或预览区 */
      .preview-mode,
      .code-mode {
        display: none;
      }

      // :deep(.vuepress-markdown-body ){
      //   height: 100%;
      //    div:first-of-type{
      //     height: 100%;
      //   }
      // }
      /* 在预览模式下显示预览 */
      .preview-mode.active {
        height: 100%;
        flex: 1;
        display: block;
        // overflow: auto;
      }

      /* 在代码模式下显示代码 */
      .code-mode.active {
        flex: 1;
        display: block;
        background-color: #1c1c1c;
        // overflow: auto;
      }
    }
  }


}
</style>
