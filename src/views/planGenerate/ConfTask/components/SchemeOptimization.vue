<template>
  <el-dialog :visible="visible" title="方案AI助手" width="500px" @close="handleClose" class="custom-dialog"
    :modal-append-to-body="false">
    <el-form :model="form" :rules="rules" label-width="120px" label-position="top" ref="form" class="custom-form">
      <el-form-item label="内容生成" prop="content">
        <el-input clearable v-model="form.content" type="textarea" placeholder="请输入要创建的内容提示" maxlength="1000"
          show-word-limit>
        </el-input>
      </el-form-item>
      <!-- 添加更多表单项根据需求 -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :disabled="!isFormValid" @click="handleConfirm">生成</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SchemeOptimization',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        content: '',
        // 根据需要添加更多字段
      },
      rules: {
        content: [
          { required: true, message: '创建提示内容为必填项', trigger: 'blur' },
          { min: 1, message: '创建提示内容至少为1个字符', trigger: 'blur' },
          // 可以根据需求添加更多验证规则
        ]
      },
      isFormValid: false
    }
  },
  watch: {
    form: {
      handler() {
        this.$refs.form.validate(valid => {
          this.isFormValid = valid
        })
      },
      deep: true
    }
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('confirm', { ...this.form })
          this.handleClose()
        } else {
          this.$message.error('请正确填写表单内容')
          return false
        }
      })
    },
    handleCancel() {
      this.handleClose()
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.resetForm()
    },
    resetForm() {
      this.form.content = ''
      // 重置更多表单字段
      this.isFormValid = false
    }
  }
}
</script>

<style scoped>
.custom-dialog >>> .el-dialog__body {
  padding: 2px 20px !important; /* 调整这个值来减少表单和标题之间的间距 */
}
.custom-form {
  margin-top: 0px; /* 调整这个值来减少表单和标题之间的间距 */
}
/* 根据需要添加自定义样式 */
</style>
