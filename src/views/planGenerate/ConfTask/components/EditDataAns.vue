<template>
    <div>
        <div>
            <!-- <div class="headerTip">
                <div class="tipIcon"><img slot="icon" src="@/assets/images/planGenerater/jiqiren.png"></div>
                <div class="tipDesc" v-if="activeStep === 0 && !allSuccess">将为您自动分析对齐范围：{{ runStreamList.map(item =>
                    item.name).join('、') }}，请稍后...</div>
                <div class="tipDesc" v-else>点击确定，将保存对齐范围。</div>
            </div> -->
            <!-- <div style="margin-top: 16px;" class="taskBox" id="taskBox">
                <div class="taskCard">
                    <div class="title"><img src="@/assets/images/planGenerater/start.png" />开始一个新目标：<span
                            class="subTitle">{{ runStreamList.map(item => item.name).join('、') }}</span></div>
                </div>
                <div v-for="(item, index) in runStreamList" :key="index">
                    <div v-if="item.status !== 'start'" class="taskCard">
                        <div class="title">
                            <div style="display: flex;align-items:center;"><img
                                    :src="item.status === 'running' ? require('@/assets/images/planGenerater/task-running.png') : (item.status === 'error' ? require('@/assets/images/planGenerater/task-error.png') : require('@/assets/images/planGenerater/task-success.png'))">
                            </div>
                            <div class="title">{{ item.status === 'running' ? '任务执行中' : '完成任务' }}: <span
                                    class="subTitle">【{{ item.name }}】</span></div>
                        </div>
                        <div class="desc">{{ item.content }}</div>
                    </div>
                </div>
                <div v-if="allSuccess">
                    <div class="taskCard">
                        <div class="title"><span class="subTitle">所有任务完成</span></div>
                    </div>
                </div>
            </div> -->
            <div style="margin-top: 16px">
                <!-- 只有k场景有库表选择 -->
                <div v-if="dataAnsTask.align_fields.includes('table')"
                    style="display:flex;flex-direction: column;align-items: flex-start;margin-bottom: 16px;">
                    <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox
                            v-model="formData.cangku" @change="changeCangku">数据仓库对齐：</el-checkbox></div>
                    <el-select v-model="formData.chooseData" clearable :multiple="true" style="width: 100%;flex:1"
                        placeholder="请选择">
                        <el-option v-for="item in tablesList" :key="item.table" :label="item.table" :value="item.table">
                            <span style="float: left">{{ item.table }}</span>
                            <span style="float: right; color: #8492a6; font-size: 12px">{{ item.description
                                }}</span>
                        </el-option>
                    </el-select>
                </div>
                <div v-if="dataAnsTask.align_fields.includes('api')"
                    style="display:flex;flex-direction: column;align-items: flex-start;margin-bottom: 16px;">
                    <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox v-model="formData.api"
                            @change="changeAPI">算法API对齐：</el-checkbox></div>
                    <el-select ref="tagsSelect" v-model="formData.tag_ids" style="width:100%;flex:1" multiple filterable
                        remote placeholder="请选择" :remote-method="searchTags" clearable :popper-append-to-body="false">
                        <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </div>
                <div v-if="dataAnsTask.align_fields.includes('rule_api')"
                    style="display:flex;flex-direction: column;align-items: flex-start;margin-bottom: 16px;">
                    <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox
                            v-model="formData.cangku" @change="changeCangku">规则对齐标签：</el-checkbox></div>
                    <el-select v-model="formData.chooseData" multiple remote filterable :remote-method="queryAllTag"
                        resver-keyword clearable placeholder="请搜索标签" style="width: 100%">
                        <el-option v-for="item in tablesList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </div>
                <div v-if="dataAnsTask.align_fields.includes('manual')"
                    style="display:flex;flex-direction: row;align-items: center;margin-bottom: 16px;">
                    <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox
                            v-model="formData.manual_input">人工输入</el-checkbox></div>
                </div>
            </div>
        </div>
        <!-- <div v-else>
                <el-form ref="form" label-position="right" label-width="99px" :model="formData" :rules="rules">
                    <el-form-item label="数据表:" prop="chooseData">
                        <el-select v-model="formData.chooseData" :multiple="true" style="width: 100%" placeholder="请选择">
                            <el-option v-for="item in tablesList" :key="item.table" :label="item.table"
                                :value="item.table">
                                <span style="float: left">{{ item.table }}</span>
                                <span style="float: right; color: #8492a6; font-size: 12px">{{ item.description
                                    }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div> -->
        <!-- <div slot="footer" class="dialog-footer">
            <el-button type="primary" v-if="activeStep === 0" :disabled="!allSuccess || loading"
                @click="nextStep">人工修改</el-button>
            <el-button type="primary" :disabled="!allSuccess || loading" @click="createSubmit">确定</el-button>
            <el-button type="info" :loading="loading" v-if="activeStep === 1 && templateId" :disabled="loading"
                @click="redo">重新对齐范围</el-button>
            <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">取消</el-button>
        </div> -->
    </div>
</template>
<script>
import { getTablesList, queryDuiqiTags, queryTaskByTemp, SchemeDetail, queryAbilityMapping } from '@/api/planGenerateApi.js';
import { queryUseTagsRuleMarket } from '@/api/ruleMarketApi.js';

export default {
    name: 'EditDataAns',
    props: {
        scheme_detail_optimizeData: {
        type: String,
        default: ''
        },
        dataAnsTask: {
            type: Object,
            required: true
        },
        agentSenceCode: {
            type: String,
            default: ''
        },
        // isVisible: {
        //     type: Boolean,
        //     default: false
        // },
        conSelect: {
            type: String,
            default: ''
        },
        // editData: {
        //     type: Object,
        //     default: () => {
        //         return { "table": "", "tag_ids": [], manual_input: false }
        //     }
        // },
        templateId: {
            type: String,
            default: ''
        },
        treeData: {
            type: String,
            default: ''
        },
        formData: {
            type: Object,
            required: true
        },
        allSuccess: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            detailContent: '',
            showFlag: false,
            tablesList: [],
            rules: {
                chooseData: [{ required: true, message: '请选择数据表', trigger: 'blur' }],
            },
            loading: false,
            tagList: [],
            allTagList: [],
            allRunTasks: [], // 所有需要执行的任务
            runId: '', // 执行需要的id,
            runStreamList: [],
            editData: { "table": "", "tag_ids": [], manual_input: true }
        };
    },
    async created() {
    },
    async mounted() {
        // await this.queryPlanDetail();
        // await this.redo()
    },
    watch: {
        formData: {
            handler(val) {
                console.log("formData val", JSON.stringify(val, null, 2));

            },
            immediate: true,
            deep: true
        },
        dataAnsTask: {
            async handler(val) {
                console.log("dataAnsTask 001", val);
                if (val.align_fields.length) {

                    await this.handleAbilityMapping()
                    // this.$emit('e-switchloading', true)

                    if (val.align_fields.includes('rule_api')) {
                        await this.queryDataInfoRule();
                    }
                    else {
                        await this.queryDataInfo();
                    }
                    await this.searchTags2('');

                    console.log('回显数据', this.templateId, this.editData, this.editData.manual_input, this.editData.tag_ids, this.editData.table);

                    const flag = this.editData.manual_input || (this.editData.tag_ids && this.editData.tag_ids.length) || this.editData.table;
                    console.log('flag', flag);
                    if (this.editData.manual_input) {
                        this.formData.manual_input = true
                    } else {
                        this.formData.manual_input = false
                    }
                    // this.allSuccess = true;
                    this.$emit('e-updateAllsuccess', true)
                }
            }
            , immediate: true,
            deep: true
        },
        // isVisible: {
        //     handler(val) {
        //         if (val) {
        //             this.showFlag = val


        //         } else {
        //             this.showFlag = false;
        //         }
        //     },
        //     immediate: true, deep: true
        // }
    },
    methods: {
        async handleAbilityMapping() {
            await queryAbilityMapping({ scheme_id: this.$route.query.id })
                .then((res) => {
                    if (res.status === 200 && res.data.code * 1 === 200) {
                        this.editData = res.data.result?.align_type || { 'table': [], 'tag_ids': [], manual_input: true };
                    }
                })
                .catch((_err) => {
                    this.$message({
                        type: 'error',
                        message: _err.data?.msg || '接口异常!'
                    });
                })
                .finally(() => {
                });
        },
        async queryPlanDetail() {
            await SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
                if (res.status === 200 && res.data.code === 200) {
                    this.detailContent = res.data.result?.text;
                    console.log("？？this.detailContent", res.data.result?.text);

                }
            });
        },
        async runExcute(id, order, index) {
            const url = process.env.VUE_APP_AGENT_API.startsWith('/') ? window.location.origin + process.env.VUE_APP_AGENT_API + "/api/agent/v2/manual_task/execute" : process.env.VUE_APP_AGENT_API + "/api/agent/v2/manual_task/execute"
            console.log('url', url)
            await this.$axios.post(url, {
                template_id: this.templateId,
                run_id: this.runId,
                agent_id: this.runId,
                task_id: id,
                order: order
            }, {
                responseType: "stream",
                baseURL: process.env.VUE_APP_AGENT_API,
                headers: {
                    whiteuservalidate: 'False'
                },
                onDownloadProgress: (event) => {
                    const xhr = event.target;
                    const { responseText } = xhr;
                    this.$nextTick(() => {
                        // console.log('----流----',this.runStreamList[index])
                        this.runStreamList[index].status = 'running';
                        this.runStreamList[index].content = responseText;
                        const temp = document.getElementById('taskBox');
                        if (temp) {
                            temp.scrollIntoView({ block: 'end', inline: 'nearest' });
                            temp.scrollTop = temp.scrollHeight + 200;
                        }
                    })
                },
                onError: function (error) {
                    // 处理流错误
                    console.error(error);
                    this.runStreamList[index].status = 'error';
                    this.runStreamList[index].content = '';
                }
            }).then(async response => {
                // 关闭数据流
                console.log('关闭数据流 数据流', response);
                // 这里返回的内容，就是左侧的内容数据对齐分析
                this.runStreamList[index].status = 'success';
                this.$nextTick(() => {
                    const temp = document.getElementById('taskBox');
                    if (temp) {
                        temp.scrollIntoView({ block: 'end', inline: 'nearest' });
                        temp.scrollTop = temp.scrollHeight + 700;
                    }
                })
                if (this.runStreamList[index].name === '规则对齐标签' && this.dataAnsTask.align_fields.includes('rule_api')) {
                    if (this.runStreamList[index].content) {
                        const valTemp = eval(this.runStreamList[index].content)
                        if (valTemp && valTemp.length) {
                            this.formData.cangku = true;
                            this.formData.chooseData = valTemp;
                            console.log('库表的选择', valTemp);
                            console.log('库表的选择 ', valTemp);
                            this.$set(this.formData, 'chooseData', valTemp || []);
                        } else {
                            this.formData.cangku = false;
                            this.formData.chooseData = []
                        }
                    } else {
                        this.formData.cangku = false;
                        this.formData.chooseData = []
                    }
                }
                if (this.runStreamList[index].name === '数据仓库对齐' && this.dataAnsTask.align_fields.includes('table')) {
                    if (this.runStreamList[index].content) {
                        const valTemp = eval(this.runStreamList[index].content)
                        if (valTemp && valTemp.length) {
                            this.formData.cangku = true;
                            this.formData.chooseData = valTemp;
                            console.log('库表的选择 数据仓库对齐', valTemp);
                            this.$set(this.formData, 'chooseData', valTemp || []);
                        } else {
                            this.formData.cangku = false;
                            this.formData.chooseData = [];
                        }
                    } else {
                        this.formData.cangku = false;
                        this.formData.chooseData = [];
                    }
                }
                if (this.runStreamList[index].name === '算法API对齐') {
                    if (this.runStreamList[index].content) {
                        const valTemp = eval(this.runStreamList[index].content)
                        if (valTemp && valTemp.length) {
                            const filters = [];
                            valTemp.forEach(teItem => {
                                const ffilter = this.allTagList.filter(tag => tag.name === teItem)
                                if (ffilter && ffilter.length) {
                                    filters.push(ffilter[0].id)
                                }
                            })
                            this.formData.api = true;

                            // this.formData.tag_ids = [...filters] || [];
                            this.$set(this.formData, 'tag_ids', filters || '');
                            console.log('少选出的', filters, this.formData.tag_ids);
                        } else {
                            this.formData.api = false;
                            this.formData.tag_ids = [];
                        }
                    } else {
                        this.formData.api = false;
                        this.formData.tag_ids = [];
                    }
                }
                if (index + 1 < this.runStreamList.length) {
                    this.$nextTick(async () => {
                        await this.runExcute(this.runStreamList[index + 1].id, this.runStreamList[index + 1].order, index + 1)
                    })
                } else {
                    // this.allSuccess = true;
                    this.$emit('e-updateAllsuccess', true)
                    this.loading = false;
                    console.log('最后的结果', this.runStreamList);
                    this.formData.manual_input = true;
                    this.$nextTick(() => {
                        const temp = document.getElementById('taskBox');
                        if (temp) {
                            temp.scrollIntoView({ block: 'end', inline: 'nearest' });
                            temp.scrollTop = temp.scrollHeight + 700;
                        }
                    })
                }
            }).catch(() => {
                // this.allSuccess = true;
                this.$emit('e-updateAllsuccess', true)
                this.loading = false;
                this.runStreamList[index].status = 'error';
                this.runStreamList[index].content = '';
            })
        },
        queryAllTag(keyword) {
            queryUseTagsRuleMarket({ keyword: keyword })
                .then((res) => {
                    if (res.data) {
                        this.tablesList = res.data
                    }
                })
                .finally(() => {
                });
        },
        nextStep() {
            this.activeStep = 1;
        },
        redo() {
            // this.allSuccess = false;
            this.$emit('e-updateAllsuccess', false)
            this.runStreamList = [];
            this.activeStep = 0;
            this.formData.api = false;
            this.formData.tag_ids = [];
            this.formData.cangku = false;
            this.formData.chooseData = [];
            this.formData.manual_input = true;
            this.queryStart();
        },
        async queryStart() {
            this.loading = true;
          console.log(`queryTaskByTemp内容00 ：, 1: ${this.templateId}, 2: ${this.detailContent}, 3: ${this.treeData} 4: ${this.scheme_detail_optimizeData}`);
            await queryTaskByTemp({
                template_id: this.templateId,
                scheme_detail: this.detailContent,
                mind_tree: this.treeDatascheme_detail_optimize,
                scheme_detail_optimize: this.scheme_detail_optimizeData
            }).then(async (res) => {
                console.log('需执行的任务列表', res.data,);
                this.allRunTasks = res.data.new_tasks || [];
                this.runId = res.data.run_id || '';
                res.data.new_tasks.forEach(async (item, index) => {
                    this.runStreamList.push({
                        name: item.name,
                        content: '',
                        status: 'start',
                        type: 1,
                        ...item
                    });
                })
            })
            console.log("this.runStreamList", JSON.stringify(this.runStreamList, null, 2));

            const temp = [];
            if (this.runStreamList.length) {
                // this.allSuccess = false;
                this.$emit('e-updateAllsuccess', false)
                await this.runExcute(this.runStreamList[0].id, this.runStreamList[0].order, 0)
            }
            console.log('promise1', temp);
        },
        queryDataInfoRule() {
            this.loading = true;
            queryUseTagsRuleMarket({ keyword: '' }).then((res) => {
                this.loading = false;
                if (res.data) {
                    this.tablesList = res.data || [];
                    console.log('0000', this.editData, JSON.stringify(this.formData, null, 2))
                    if (this.editData && this.editData.rule_tag_ids) {
                        this.formData.cangku = true;
                        const temp = [];
                        // 循环判断标签是否有被删除，被删除的就不显示了
                        this.editData.rule_tag_ids?.forEach(titem => {
                            const filter = res.data.filter(item => item.id === titem)
                            if (filter.length) {
                                temp.push(filter[0].id);
                            }
                        });
                        this.formData.chooseData = temp;
                    } else {
                        this.formData.cangku = false;
                        this.formData.chooseData = [];
                    }
                }
            }).finally(() => {
                this.loading = false;
            });;
        },
        queryDataInfo() {
            this.loading = true;
            const params = { dbType: 'static' };
            // if (this.agentSenceCode === 'custom_cognition_assistant_scene') {
            //     params.dbType = 'got'
            // }
            // if (this.agentSenceCode === 'device_ops_assistant_scene' || this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'rule_generation_scene' || this.agentSenceCode === 'intelligent_conversation_scene') {
            //     params.dbType = 'static'
            // }
            // if (this.agentSenceCode === 'device_ops_assistant_scene-v1') {
            //     params.dbType = 'run'
            // }
            console.log('选择表数据', params);
            getTablesList(params).then((res) => {
                this.loading = false;
                if (res.status === 200 && res.data.success) {
                    this.tablesList = res.data.data?.list || [];
                    console.log('0000', this.editData)
                    if (this.editData && this.editData.table) {
                        if (typeof this.editData.table === 'string') {
                            this.formData.cangku = true;
                            this.formData.chooseData = [this.editData.table];
                        } else {
                            if (this.editData.table.length) {
                                this.formData.cangku = true;
                                this.formData.chooseData = this.editData.table;
                            }
                        }

                    } else {
                        this.formData.cangku = false;
                        this.formData.chooseData = []
                    }
                }
            }).finally(() => {
                this.loading = false;
            });;
        },
        searchTags(keyword) {
            queryDuiqiTags({
                keyword
            }).then(res => {
                if (res.data) {
                    this.tagList = res.data;
                    if (keyword === '') {
                        this.allTagList = res.data;
                    }
                } else {
                    this.tagList = [];
                }
            })
        },
        searchTags2(keyword) {
            queryDuiqiTags({
                keyword
            }).then(res => {
                if (res.data) {
                    this.tagList = res.data;
                    console.log("列表呢？兄弟 00", res.data);
                    if (keyword === '') {
                        this.allTagList = res.data;
                        if (this.editData && this.editData.tag_ids?.length) {
                            console.log("进 if还是else啊？");

                            this.formData.api = true;
                            const temp = [];
                            // 循环判断标签是否有被删除，被删除的就不显示了
                            this.editData.tag_ids?.forEach(titem => {
                                const filter = res.data.filter(item => item.id === titem)
                                if (filter.length) {
                                    temp.push(filter[0].id);
                                }
                            });
                            this.formData.tag_ids = temp;
                            // this.$emit('e-switchloading', false)

                        } else {
                            console.log('进else?', this.editData, JSON.stringify(this.formData, null, 2))
                            this.formData.api = false;
                            this.formData.tag_ids = []
                            // this.$emit('e-switchloading', false)

                            console.log('进else 11?', this.editData, JSON.stringify(this.formData, null, 2))

                        }
                    }
                } else {
                    this.tagList = [];
                }
            })
        },
        changeCangku(val) {
            if (!val) {
                this.formData.chooseData = []
            }
        },
        changeAPI(val) {
            if (!val) {
                this.formData.tag_ids = [];
            }
        },
        onClose() {
            this.formData.chooseData = []
            this.formData.tag_ids = [];
            this.formData.cangku = false;
            this.formData.api = false;
            this.loading = false;
            this.showFlag = false;
            // this.allSuccess = false;
            this.$emit('e-updateAllsuccess', false)
            this.runStreamList = [];
            this.activeStep = 0;
            this.formData.manual_input = true;
            if (this.agentSenceCode !== 'device_ops_assistant_scene' && this.agentSenceCode !== 'visit_leader_cognition_scene' && this.agentSenceCode !== 'rule_generation_scene' && this.agentSenceCode !== 'intelligent_conversation_scene') {
                this.$refs.form.clearValidate();
            }
            this.$emit('close');
        },
        createSubmit() {
            if (this.agentSenceCode === 'device_ops_assistant_scene' || this.agentSenceCode === 'visit_leader_cognition_scene' || this.agentSenceCode === 'rule_generation_scene' || this.agentSenceCode === 'intelligent_conversation_scene') {
                console.log(this.formData);
                if (this.formData.api || this.formData.cangku) {
                    if ((this.formData.api && this.formData.tag_ids.length) || (this.formData.cangku && this.formData.chooseData?.length)) {
                        const temp = {}
                        const filtered = this.tablesList.filter(item => this.formData.chooseData.includes(item.table)).map(item => {
                            temp[item.table] = JSON.parse(item.ddl)
                            return item.ddl;
                        });
                        // const filters = this.tablesList.filter(table => table.table === this.formData.chooseData)
                        if (filtered.length) {
                            this.$emit('close', JSON.stringify(temp), this.formData.chooseData, this.formData.tag_ids, this.formData.manual_input);
                        } else {
                            this.$emit('close', 'close', this.formData.chooseData, this.formData.tag_ids, this.formData.manual_input);
                        }
                        // this.allSuccess = false;
                        this.$emit('e-updateAllsuccess', false)
                        this.runStreamList = [];
                        this.activeStep = 0;
                        this.formData.api = false;
                        this.formData.tag_ids = [];
                        this.formData.cangku = false;
                        this.formData.chooseData = []
                        this.formData.manual_input = true;
                    } else {
                        this.$message({
                            type: 'error',
                            message: '请选择配置并配置数据源后再进行对齐!'
                        });
                    }
                } else {
                    if (this.formData.manual_input) {
                        this.$emit('close', 'close', this.formData.chooseData, this.formData.tag_ids, this.formData.manual_input);
                    } else {
                        this.$message({
                            type: 'error',
                            message: '请选择任意一个配置后再进行对齐!'
                        });
                    }
                }
            } else {
                this.$refs.form.validate((validate) => {
                    if (validate) {
                        if (this.conSelect === 'multiple') {
                            // console.log(this.formData.chooseData)
                            // console.log(this.tablesList)
                            const filtered = this.tablesList.filter(item => this.formData.chooseData.includes(item.table)).map(item => item.ddl);
                            if (filtered.length) {
                                this.formData.chooseData = '';
                                this.$refs.form.clearValidate();
                                this.$emit('close', JSON.stringify(filtered));
                            }
                        } else {
                            const filters = this.tablesList.filter(table => table.table === this.formData.chooseData)
                            console.log('filters[0].ddl', filters[0].ddl);
                            if (filters.length) {
                                this.formData.chooseData = '';
                                this.$refs.form.clearValidate();
                                this.$emit('close', filters[0].ddl);
                            }
                        }

                    } else {
                        this.loading = false;
                    }
                });
            }
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-select-dropdown__item) {
    max-width: 700px;
    /* 设置文本溢出时的行为为省略号 */
    text-overflow: ellipsis;

    /* 设置超出容器的内容应该被裁剪掉 */
    overflow: hidden;

    /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
    white-space: nowrap;
}

.myStep {
    background: #fff;
    padding: 13px 20px;

    :deep(.el-step__arrow) {
        margin: 0 16px;

        &::before {
            content: '';
            position: static;
            height: 1px;
            width: 100%;
            background: #C8C9CC;
            ;
            transform: none;
            display: block
        }

        &::after {
            display: none;
        }
    }

    :deep(.is-process) {
        color: #4068D4;

        .el-step__icon {
            color: #4068D4;

            &.is-text {
                border: none;
            }
        }
    }

    :deep(.is-success) {
        color: #000;
        border-color: #4068D4;

        .el-icon-check {
            color: #4068D4;
        }

        .el-step__icon {
            color: #4068D4;

            &.is-text {
                border: 1px solid #4068D4;
            }
        }
    }

    .empty-space {
        width: 100%;
        height: 100%;
    }
}

.headerTip {
    display: flex;
    align-items: center;

    .tipIcon {
        width: 48px;
        height: 48px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .tipDesc {
        flex: 1;
        background: #EFF3FF;
        margin-left: 13px;
        position: relative;
        padding: 8px 16px;
        font-size: 14px;
        line-height: 20px;
        color: #323233;
        border-radius: 6px;

        &::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 50%;
            transform: translateY(-50%);
            border-top: 5px solid transparent;
            /*左边透明*/
            border-bottom: 5px solid transparent;
            /*右边透明*/
            border-right: 8px solid #EFF3FF;
            /*底部为黑色线条*/
        }
    }
}

::v-deep(.el-checkbox__label) {
    font-weight: bold;
    color: #1D2129;
}

.taskBox {
    max-height: 100%;
    height: 100%;
    overflow-y: auto;
}

.taskCard {
    border: 1px solid #DCDDE0;
    border-radius: 4px;
    padding: 10px 12px;
    margin-bottom: 12px;

    .title {
        color: #323233;
        font-weight: bold;
        line-height: 20px;
        display: flex;
        align-items: center;

        img {
            width: 14px;
            height: 14px;
            margin-right: 10px;
        }

        .subTitle {
            font-weight: normal !important;
        }
    }

    .desc {
        color: #646566;
        margin-top: 12px;
        line-height: 22px;
    }
}
</style>
<style lang="scss">
.last-dialog {
    border-radius: 8px;

    .el-dialog__header {
        padding: 12px 20px;
        border-bottom: 1px solid #ebecf0;

        .el-dialog__title {
            font-size: 16px;
            color: #323233;
            line-height: 24px;
        }

        .el-dialog__headerbtn {
            top: 14px;

            .el-dialog__close {
                font-size: 18px;
            }
        }
    }

    .el-message-box__header {
        padding: 12px 20px;
        border-bottom: 1px solid #ebecf0 !important;

        .el-message-box__title {
            font-size: 16px;
            color: #323233;
            line-height: 24px;
        }

        .el-message-box__headerbtn {
            top: 14px;

            .el-message-box__close {
                font-size: 18px;
            }
        }
    }

    .el-message-box__content {
        padding: 16px 20px;

        .el-message-box__message {
            padding-left: 20px !important;
            padding-right: 20px !important;
        }
    }

    .el-message-box__btns {
        padding: 0px 20px;

        button {
            width: 60px !important;
        }

        .el-button {
            line-height: 20px !important;
        }
    }

    .el-dialog__body {
        padding: 16px 20px;
        max-height: 600px;
        overflow-y: auto;
    }

    &.small-last-dialog {
        .el-dialog__body {
            padding: 16px 20px;
            height: auto !important;
            max-height: 340px;
            overflow-y: auto;
        }
    }

    .el-dialog__footer {
        padding: 16px 20px;

        .el-button {
            line-height: 20px;
        }
    }

    .el-input__inner {
        border-radius: 2px;
    }
}
</style>
