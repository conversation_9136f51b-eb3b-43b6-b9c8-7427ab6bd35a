<template>
  <div class="custom-tabs">
    <!-- 使用 el-tabs -->
    <el-tabs v-model="currentTab" @tab-click="handleTabClick">
      <!-- 动态生成 el-tab-pane -->
      <el-tab-pane v-for="(tab, index) in tabsList" :key="tab.name || index" :name="tab.name">
        <!-- 支持自定义 tab 标签内容 -->
        <template #label>
          <slot name="customTitleTab" :item="tab">
            {{ tab.label }}
          </slot>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "CustomTabs",
  props: {
    value: {
      type: String,
      required: true,
    },
    tabsList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      currentTab: this.value, // 当前激活的 tab，与父组件同步
    };
  },
  watch: {
    value(newValue) {
      this.currentTab = newValue; // 同步父组件传入的值
    },
    currentTab(newValue) {
      this.$emit("input", newValue); // 触发父组件的 v-model 更新
    },
  },
  methods: {
    handleTabClick(tab) {
      this.$emit("tab-click", tab); // 触发 tab-click 事件
    },
  },
};
</script>

<style scoped>
.custom-tabs {
  width: 100%;
}
</style>
