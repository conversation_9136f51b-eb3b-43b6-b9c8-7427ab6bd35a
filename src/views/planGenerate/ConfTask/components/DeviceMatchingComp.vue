<template>
  <div id="top-content" :style="{
    height: '100%',
    maxHeight: '100%',
    width: '100%',
    userSelect: isDragging ? 'none' : 'auto',
    transition: isDragging ? 'none' : 'width 0.2s',
    position: thinkFullFlag ? '' : 'relative'
  }" :class="!planDetailTopShow ? 'optContent chatRightFull' : 'optContent'">
    <div class="optHeader">
      <div class="rightTitle">
        设备匹配
        <el-popover placement="top-start" trigger="hover" width="240">
          <div>可根据具体设备进行方案设计</div>
          <i slot="reference" class="el-icon-warning-outline" />
        </el-popover>
      </div>
      <div class="rightTitleOpt">
        <span class="twin" @click="handleJumpToDigitalTwin">{{ topologyData.title }}</span>
        <!-- <el-divider direction="vertical"></el-divider> -->
        <!-- <el-tooltip v-if="!rightFullFlag" class="item" effect="dark" :content="!planDetailShow ? '退出全屏' : '全屏'"
          placement="top">
          <el-button :type="!planDetailShow ? 'primary' : 'info'" size="mini" @click="changeShowTopRight">
            <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img v-else
              src="@/assets/images/planGenerater/tuichuquanping.png" />
          </el-button>
        </el-tooltip> -->
      </div>
    </div>
    <div id="iframeBox" v-loading="deviceLoading" class="topContent" element-loading-text="设备加载中..."
      element-loading-spinner="el-icon-loading">
      <iframe v-if="iframeSrc" id="iframe" ref="iframe" :src="iframeSrc" frameborder="0" width="100%" height="100%"
        allow="fullscreen"></iframe>
    </div>
  </div>
</template>

<script>
import {
  querySchemeDetailById,
  getExecuteSync,
  updateByDeviceId
} from '@/api/planGenerateApi.js';
export default {
  name: 'DeviceMatchingComp',
  props: {},
  data() {
    return {
      planDetailShow: true,
      isDragging: false,
      planDetailTopShow: true,
      topHeight: '100%',
      topologyData: {
        title: '找不到拓扑关系？'
      },
      rightFullFlag: false,
      deviceLoading: false,
      iframeSrc: '',
      thinkFullFlag: false,
      env: {
        dev: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=5197&paperId=14649&sceneCode=1666686413389905975&editorMode=function-energy-topology&isFromIMP=true`,
        fat: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=396&paperId=1066&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`,
        uat: '',
        production: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=129&paperId=4201&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`
      },
    }
  },
  async mounted() {
    window.addEventListener('message', (e) => {
      // 真实物联孪生项目
      const typeList = [
        'IOTtoIMPClick',
        'virtualStandardDevice',
        'modeTypeChange',
        'IOTtoIMPSelectList'
      ];
      if (typeList.includes(e.data.type)) {
        console.log('--数字孪生透传数据--', e.data);
        this.deviceObj = e.data;
        this.updateDeviceId(e.data);
      }
    });
    await this.handleGetAbilityId();
  },
  methods: {
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    },
    async handleGetSchemeDescription() {
      await querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        this.schemeDescription = this.schemeDescription || res.data.result?.description;
      });
    },
    handleJumpToDigitalTwin() {
      console.log('444', process.env.VUE_APP_ENV);
      window.open(
        `https://air${process.env.VUE_APP_ENV === 'production' ? '.fat' : '.' + process.env.VUE_APP_ENV
        }.ennew.com/dt-manage/project/list`
      );
    },
    changeShowTopRight() {
      this.planDetailShow = !this.planDetailShow;
      this.planDetailTopShow = !this.planDetailTopShow;
      if (this.planDetailShow) {
        this.rightWidth = '';
        this.leftWidth = '40%';
        this.topHeight = '50%';
        this.bottomHeight = '50%';
      } else {
        this.rightWidth = '';
        this.leftWidth = '0px';
        this.topHeight = '';
        this.bottomHeight = '0px';
      }
    },
    getQueryToken(url) {
      setTimeout(() => {
        this.deviceLoading = false;
      }, 2000);
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    async handleGetAbilityId() {
      this.deviceLoading = true;
      const vm = this;
      const res = await vm.$axios.get(
        `${vm.baseUrl}/platform/conf/getApolloVal?key=gpts.twin_iot_model_analysis_ability.id`
      );
      if (res && res.data && res.data.status === 200) {
        this.ability_id = res.data.data;
        await this.handleGetModeTypeAndModeCode();
      }
    },
    async handleGetModeTypeAndModeCode() {
      await this.handleGetSchemeDescription();
      const res = await getExecuteSync({
        scene_instance_id: this.$route.query.id,
        ability_id: this.ability_id,
        name: this.generateUUID(),
        goal: this.schemeDescription
      });
      if (res?.status === 200) {
        this.modelObj = res.data;
        // 处理加工iframe的url
        const path = `${this.env[process.env.VUE_APP_ENV]}&modeType=${this.modelObj.modelType
          }&modelCode=${this.modelObj.modelCode}`;
        this.iframeSrc = this.getQueryToken(path);
      }
    },
    updateDeviceId(obj) {
      this.deviceObj = obj;
      window.DEVICE_OBJ = this.deviceObj;
      const params = {
        scheme_id: this.$route.query.id,
        device_id: obj.deviceId || '',
        device_name: obj.deviceName || obj.modelName || '',
        model_type: obj.modeType,
        model_code: obj.modelCode || ''
      };
      // TODO 多选透传传参数据格式改变，带后端确认
      if (obj.type === 'IOTtoIMPSelectList') {
      }
      // 多选透传不需要更新iframe的url
      if (obj.type !== 'IOTtoIMPSelectList') {
        const path = `${this.env[process.env.VUE_APP_ENV]}&modeType=${obj.modeType}${obj.modelCode ? '&modelCode=' + obj.modelCode : ''
          }`;
        this.iframeSrc = this.getQueryToken(path);
      }
      // 请求增加条件限制
      if ((obj.modeType === '2' && !obj.modelCode) || (obj.modeType === '1' && !obj.deviceId)) {
        return;
      }
      updateByDeviceId(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('更新设备完成', res.data);
          this.$emit('updateDeviceId', res.data.result?.device_id);
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.topContent {
  height: calc(100% - 47px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .el-empty {
    height: 100%;
    display: flex;
    padding: 10px 0;

    :deep(.el-empty__image) {
      height: 50%;
      margin-top: -15%;
    }

    :deep(.el-empty__description) {
      margin-top: 5%;
      height: 10%;
    }

    :deep(.el-empty__bottom) {
      height: 10%;
      margin-top: 10%;
    }
  }
}

.optHeader {
  padding: 0px 20px;
  border-bottom: 1px solid #ebecf0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .rightTitle {
    font-size: 14px;
    font-weight: bold;
    color: #323233;
    line-height: 22px;
    padding: 12px 0px;
  }

  .rightTitleOpt {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .twin {
      color: #4068d4;
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: #3c60bf;
      }
    }

    .rightTextBtn {
      background-color: #406bd4;
      font-size: 12px;
      color: #fff;
      padding: 0px 6px;
      height: 24px;
      line-height: 24px;
      border-radius: 2px;
      margin-left: 8px;
      cursor: pointer;

      &:hover {
        background: #3455ad;
      }

      &:active {
        background: #264480;
      }
    }

    .rightBtn {
      // background: #F2F3F5;
      border-radius: 2px;
      width: 30px;
      height: 30px;
      color: #4068d4;
      margin-left: 8px;
      text-align: center;
      line-height: 28px;
      cursor: pointer;

      &:hover {
        background: #ebecf0;
      }

      &:active {
        background: #dcdde0;
      }

      &.rightBtnBlue {
        background-color: #406bd4;

        &:hover {
          background: #3455ad;
        }

        &:active {
          background: #264480;
        }
      }

      img {
        width: 16px;
        height: auto;
      }
    }
  }
}
</style>
