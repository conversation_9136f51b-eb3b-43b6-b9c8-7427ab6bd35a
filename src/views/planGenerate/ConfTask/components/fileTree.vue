<template>
 <div class="file-tree">
  <el-tree ref="fileTree" node-key="objectKey" :data="treeData" class="tree" :props="defaultProps"
   @node-click="handleNodeClick">
   <template slot-scope="{ node, data }">

    <div class="tree-node" @mouseenter="handleMouseEnter($event, data)" @mouseleave="handleMouseLeave(data)">
     <span :class="editingNodeKey === data.objectKey ? 'ellipsis' : 'node-lf'">
      <Icon v-if="data.icons"
        class="file-icon"
        :icon="
        data.isDir 
          ? (node.expanded 
              ? 'vscode-icons:default-folder-opened' 
              : 'vscode-icons:default-folder')
          : data.icons" 
      />
      <!-- 输入框：仅当节点是临时新建节点时显示 -->
      <el-input 
        @click.native.stop 
        v-if="data.isNew || editingNodeKey === data.objectKey" 
        v-model="data.objectName" 
        @blur="data.isNew ? confirmCreateFile(data) : confirmRename(data)"
        @keyup.enter.native="(e) => { 
          e.preventDefault(); 
          e.target.blur(); 
        }"
        class="new-file-input"
        :ref="`newFileInput_${data.objectKey}`" />
      <span v-else class="label-text">{{ node.label }}</span>
     </span>
     <el-dropdown v-if="!data.isNew && editingNodeKey !== data.objectKey" trigger="click" class="el-dropdown-rg" placement="bottom"
      @visible-change="(visible) => handleDropdownVisible(visible, data)">
      <i v-show="nodeHoverState[data.objectKey]" style=" cursor: pointer;" class="el-icon-more" @click.stop></i>
      <el-dropdown-menu slot="dropdown">
       <el-dropdown-item v-if="data.isDir"><el-link type="primary" class="card" :underline="false"
         @click="nodeChange('put', node, data)">新增</el-link></el-dropdown-item>
       <el-dropdown-item v-if="data.isDir">
        <el-link type="primary" class="card" :underline="false"
        @click="(e) => { addFileFun(e,data,node) }">上传</el-link>
        </el-dropdown-item>
        <el-dropdown-item v-if="!data.isDir"><el-link type="primary" class="card" :underline="false"
         @click="nodeChange('reset', node, data)">重命名</el-link></el-dropdown-item>
         <el-dropdown-item>
        <el-link type="primary" class="card" :underline="false"
        @click="nodeChange('download', node, data)">下载</el-link>
        </el-dropdown-item>
       <el-dropdown-item v-if="!data.isDir"><el-link type="primary" class="card" :underline="false"
         @click="nodeChange('delete', node, data)">删除</el-link></el-dropdown-item>
      </el-dropdown-menu>
     </el-dropdown>
    </div>
   </template>
  </el-tree>
  <div class="bottom" @click="handleCreateFile('',treeData,true)">
   <span>新建文件</span>
   <i class="el-icon-plus"></i>
  </div>
  <hover-tooltip :fileName="hoverData.name" :filePath="hoverData.path" :lastModified="hoverData.modified"
   :fileSize="hoverData.size" :position="hoverPosition" />
 </div>
</template>
<script>
import { Icon,Iconify } from "@iconify/vue2";
import HoverTooltip from './HoverTooltip.vue'
import { mapState, mapMutations } from 'vuex'
import { generate_sign_url, queryListFiles, handleRenameFile } from '@/api/planGenerateApi.js'
export default {
 name: 'fileTree',

 components: { HoverTooltip,Icon },
 props: {
  rootKeys:{
      type:String
    },
    isRight:{
    type: Boolean,
    default: true
  }
 },
 data() {
  return {
   editingNodeKey: null,
   isRootLevel: false,
   currentTempNodeParent:null,
   newFileKey: null,
   nodeHoverState: {},
   dropdownOpenState: {},
   editFileName: '',
   isCreating: false,
   hoverTimer: null,
   hoverData: {},
   hoverPosition: { x: 0, y: 0 },
   fileTreeData: [
    {
     label: 'src',
     children: [
      {
       label: 'components',
       children: [
        { label: 'HelloWorld.vue' },
        { label: 'App.vue' }
       ]
      }
     ]
    },
    {
     label: 'public',
     children: [
      { label: 'index.html' },
      { label: 'favicon.ico' }
     ]
    }
   ],
   defaultProps: {
    children: 'children',
    label: 'label'
   },
  }
 },
 computed: {
  ...mapState('maco', ['treeData']),
 },
 watch: {
  treeData: {
      deep: true,
      handler() {
        // 清空状态确保与新数据同步
        this.nodeHoverState = {};
        this.dropdownOpenState = {};
      }
    }
 },
 created() { },
 beforeDestroy() { },
 // 生命周期 - 挂载完成（访问DOM元素）
 async mounted() {

  },
 methods: {
  ...mapMutations('maco',['setTreeData','updateNodeChildren']),
  addFileFun(e, item,node) {
      // e.stopPropagation()
      // 创建一个隐藏的文件输入框
      const fileInput = document.createElement('input')
      fileInput.type = 'file'
      fileInput.style.display = 'none'

      // 监听文件选择事件
      fileInput.addEventListener('change', async (event) => {
        const file = event.target.files[0]
        if (!file) return
        const formData = new FormData();
        try {
          const res = await generate_sign_url({
            file_key: item.objectKey + file.name
          });
          if (res.data.code === 200) {
            formData.append('key', item.objectKey + file.name);
            formData.append('accessKeyId', res.data.result.accessKeyId);
            formData.append('signature', res.data.result.signature);
            formData.append('policy', res.data.result.policy);
            formData.append('file', file);
            const res1 = await this.$axios.post(res.data.result.obs_url, formData);
            if (res1.status === 204) { // 上传成功
              this.$message({
                type: 'success',
                message: '文件上传成功'
              });

              // 获取当前文件夹的路径
              const currentPath = item.objectKey
              this.$emit('refreshParentNode',currentPath,{
               objectKey: `${item.objectKey}${file.name}`,
               objectName: file.name
              },false,item.objectKey + file.name,true)
            }
          }

        } catch (e) {
          console.log(e);
          this.$message.error('获取签名出错！');
        }


        // 清理文件输入框
        document.body.removeChild(fileInput)
      })

      // 将文件输入框添加到 body 并触发点击
      document.body.appendChild(fileInput)
      fileInput.click()
    },
  // 新增：根据key查找节点
  findNodeByKey(nodes, key) {
    if (!nodes) return null;
    for (let node of nodes) {
      if (node.objectKey === key) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = this.findNodeByKey(node.children, key);
        if (found) return found;
      }
    }
    return null;
  },
   // 新增：局部刷新父节点
   async refreshParentNode(parentKey,isRootLevel) {
    try {
     const children = await this.getDirectChildren(parentKey);
     // if(!this.isRootLevel) {
      // 调用API获取父节点下最新的子节点列表
      // 更新父节点的子节点列表
      this.updateNodeChildren({
        nodeKey: parentKey,
        children: children,
        isRootLevel: isRootLevel
      });
    } catch (error) {
      console.error('刷新父节点失败:', error);
      this.$message.error('刷新文件列表失败');
    }
  },
  async getDirectChildren(objectKey) {
   try {
    const res = await queryListFiles({
     "prefix": objectKey || '',
     scheme_id: Number(this.$route.query.id),
     version: this.version
    })
    if ([200, '200'].includes(res?.status || res?.data?.status)) {
        return (res.data?.result || []).map(item => ({
          ...item,
          label: item.objectName,
          isDir: item.isDir,
          objectKey: item.objectKey,
          objectType: item.objectType,
          children: item.isDir ? [] : undefined
        }));
      }
      return [];
   } catch (error) {
    return [];
   }
     },
   // 验证文件名有效性
   isValidFilename(fileName) {
    // 不能以点开头（隐藏文件除外）或以点结尾
    if (fileName.startsWith('.') && fileName.length === 1) return false;
    if (fileName.endsWith('.')) return false;
    
    // 不能包含非法字符
    const invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    return !invalidChars.some(char => fileName.includes(char));
  },
  // 创建具有完整属性的 File 对象
createFileObject(fileName) {
  // 确定 MIME 类型（根据文件扩展名）
  const mimeType = this.getMimeType(fileName);
  
  // 创建文件内容（0字节空文件）
  const blob = new Blob([], { type: mimeType });
  
  // 创建完整的 File 对象
  return new File([blob], fileName, {
    type: mimeType,
    lastModified: Date.now()
  });
},
// 根据扩展名获取 MIME 类型
getMimeType(fileName) {
  const extension = fileName.includes('.') 
    ? fileName.split('.').pop().toLowerCase()
    : '';
  
  const mimeMap = {
    txt: 'text/plain',
    json: 'application/json',
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    html: 'text/html',
    htm: 'text/html',
    css: 'text/css',
    js: 'application/javascript',
    // 添加更多类型...
  };
  
  return mimeMap[extension] || '';
},
async getHandleRenameFile(params) {
  try {
    const res = await handleRenameFile(params);
    if (res.data.code === 200) {
      return res.data;  // 成功时返回数据
    } else {
      throw new Error(res.data.message || "操作失败"); // 非200状态抛出错误
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error; // 重新抛出错误供外部捕获
  }
},
  async uploadUrl(obj) {
   try {
    const formData = new FormData();
    const file = this.createFileObject(obj.objectName);
    const res = await generate_sign_url({
     file_key: obj.objectKey
    });
    if (res.data.code === 200) {
     formData.append('key', obj.objectKey);
     formData.append('accessKeyId', res.data.result.accessKeyId);
     formData.append('signature', res.data.result.signature);
     formData.append('policy', res.data.result.policy);
     formData.append('file', file);
     const res1 = await this.$axios.post(res.data.result.obs_url, formData);
     if (res1.status === 204) { // 上传成功
      this.$emit('handle-isEdit')
      // this.$emit('uploadCapabilityType')
      return res1;
      // 获取当前文件夹的路径
      const currentPath = item.objectKey
     }
    }else {
       // 如果generate_sign_url返回的code不是200，抛出错误
       throw new Error(res.data.message || '获取上传签名失败');
    }
   } catch (error) {

   }
  },
  // 点击"新增"时触发
  handleCreateFile(folderNode, folderData,isRootLevel=false) {
   this.isRootLevel = isRootLevel
    // 当前点击的节点就是父节点（文件夹节点）
    let parentData = {};
    if(!isRootLevel) {
     parentData = folderData;
    }
    // 检查并移除现有的临时节点
    if (this.currentTempNodeParent) {
      const existingIndex = this.currentTempNodeParent.children.findIndex(
        child => child.isNew
      );
      if (existingIndex > -1) {
        this.currentTempNodeParent.children.splice(existingIndex, 1);
      }
    }
    // 创建新临时节点
    const newTempNode = {
      objectName: '',
      label: '',
      isNew: true,
      isDir: false,
      objectType: '',
      // objectKey: `temp_${Date.now()}` // 唯一临时标识
    };
    let targetParent;
    if (isRootLevel) {
     targetParent = { children: this.treeData };
     this.currentTempNodeParent = { children: this.treeData };
     this.currentTempNodeParent.objectKey = this.rootKeys
    }else {
    // 非根层级：操作父节点的children
    if (!parentData.children) {
      this.$set(parentData, 'children', []);
    }
    targetParent = parentData;
    this.currentTempNodeParent = parentData;
  }

    // 确保父节点有children数组
    if (!targetParent.children) {
      this.$set(targetParent, 'children', []);
    }
    // 关键修改：找到文件插入位置
    let insertPosition = 0;
    // 遍历父节点的所有子节点
    for (let i = 0; i < targetParent.children.length; i++) {
        const child = targetParent.children[i];
        
        // 如果遇到文件夹，继续往后找
        if (child.isDir) {
          insertPosition = i + 1;
        } 
        // 如果遇到文件，就停在这个位置（文件部分的顶部）
        else {
          break;
        }
      }
      // 在找到的位置插入新节点
      targetParent.children.splice(insertPosition, 0, newTempNode);
    // // 插入到父节点children的第一位（最顶部）
    // parentData.children.unshift(newTempNode);

    // 更新当前临时节点的父节点引用
    // this.currentTempNodeParent = parentData;

    // 等待DOM更新后聚焦输入框
    this.$nextTick(() => {
      const refName = `newFileInput_${newTempNode.objectKey}`;
      if (this.$refs[refName]) {
        this.$refs[refName].focus();
      }
    });
  },
  // 根目录新建文件
  handleRooterFile() {
   if(Array.isArray(this.treeData)) {
    const fileDate = JSON.parse(JSON.stringify(this.treeData))
    const index = fileDate?.findIndex(item => !item.isDir)
    if(index !== -1) {
     this.setTreeData(fileDate.splice(index, 0, newTempNode))
    }
   }
  },
  // 确认创建文件
 async confirmCreateFile(tempNodeData) {
    if (!tempNodeData || !tempNodeData.isNew) return;

    const fileName = tempNodeData.objectName.trim();
    if (!fileName) {
      // 空文件名，移除临时节点
      const index = this.currentTempNodeParent?.children?.findIndex(
        node => node === tempNodeData
      );
      if (index > -1) {
        this.currentTempNodeParent?.children?.splice(index, 1);
      }
      this.currentTempNodeParent = null;
      return;
    }
     // === 新增：文件名重复检查 ===
  if (this.currentTempNodeParent?.children) {
    const isDuplicate = this.currentTempNodeParent.children.some(child => {
      // 排除当前正在编辑的临时节点
      if (child === tempNodeData) return false;
      return child.label === fileName || child.objectName === fileName;
    });
    
    if (isDuplicate) {
      this.$message.error('文件名重复，请使用其他名称');
      return; // 阻止创建重复文件
    }
  }
    // 检查文件名是否包含有效的文件类型（这里我们只关心是否有扩展名，不关心是否在有效列表中）
    const hasExt = this.hasFileExtension(fileName);
    // 验证文件名有效性
    if (!this.isValidFilename(fileName)) {
      this.$message.error('文件名无效，请使用有效文件名');
      return;
    }
    // 保存原始临时 key
    const originalKey = tempNodeData.objectKey;
    const newKey = `${this.currentTempNodeParent.objectKey}${fileName}`;
    // 设置文件类型
    const extension = fileName.includes('.') 
      ? fileName.split('.').pop().toLowerCase()
      : '';
    tempNodeData.objectType = extension;
    // 2. 创建新节点对象（不要直接修改原节点）
    const newNode = {
      ...tempNodeData,
      objectName: fileName,
      label: fileName,
      isNew: false,
      objectKey: newKey,
      objectType: extension
    };
    // 3. 替换父节点中的临时节点
    const parentChildren = this.currentTempNodeParent.children;
    const index = parentChildren.findIndex(n => n.objectKey === originalKey);
    if (index !== -1) {
      parentChildren.splice(index, 1, newNode); // 关键：替换而非修改
    }
     // 5. 设置选中状态
    this.$nextTick(() => {
      this.$refs.fileTree.setCurrentKey(newKey);
      const node = this.$refs.fileTree.getNode(newKey);
      if (node) {
        this.handleNodeClick(newNode, node);
      }
    });
    
    // 更新节点状态
    tempNodeData.isNew = false;
    tempNodeData.label = fileName;
    try {
        // 调用上传API
        await this.uploadUrl(newNode);
        // 显示成功消息
        this.$message.success('文件创建成功');
        this.newFileKey = newKey;
         // 局部刷新父节点
         this.$emit('refreshParentNode',this.currentTempNodeParent.objectKey,newNode,this.isRootLevel,this.newFileKey)
      // await this.refreshParentNode(this.currentTempNodeParent.objectKey,newNode);
      } catch (error) {
        console.error('文件创建失败:', error);
        // 创建失败时移除临时节点
        const index = this.currentTempNodeParent.children.findIndex(
          node => node === tempNodeData
        );
        if (index > -1) {
          this.currentTempNodeParent.children.splice(index, 1);
        }
        this.$message.error('文件创建失败');
      }finally {
      // 重置状态
      this.currentTempNodeParent = null;
      this.newFileKey = null;
    }
    // 重置状态
    this.currentTempNodeParent = null;
  },
  // 辅助方法：获取父节点路径（若需后端交互时用，需根据实际数据结构实现）
  getParentPath(parentData) {
   let path = '';
   let current = parentData;
   while (current.parent) { // 假设节点有parent引用（Element Tree的node.parent）
    path = `${current.label}/${path}`;
    current = current.parent.data;
   }
   return path;
  },
  // 判断文件名是否有扩展名
  hasFileExtension(fileName) {
   if (!fileName && !fileName.includes('.')) return false;
   // 如果以点开头（隐藏文件）认为有扩展名
   if (fileName.startsWith('.')) return true;
   const lastDotIndex = fileName.lastIndexOf('.');
   // 点不能在开头，也不能在最后（即点后面必须有内容）
   return lastDotIndex > 0 && lastDotIndex < fileName.length - 1;
  },
  nodeChange(command, node, data) {
   switch (command) {
    case 'put':
     this.handleCreateFile(node, data)
     break
    case 'delete':
     this.$emit('delete-file', data)
     break
    case 'download':
     this.$emit('handle-download', data)
     break
     case 'reset':
     this.handleRename(data);
     break
   }
  },
   // 新增：处理重命名
   handleRename(data) {
   // 设置当前编辑节点
   this.editingNodeKey = data.objectKey;
   
   
   this.$nextTick(() => {
     const inputRef = this.$refs[`newFileInput_${data.objectKey}`];
     console.log('fjdapfjaspfjspdfs',inputRef)
     if (inputRef && inputRef.$el) {
       const inputEl = inputRef.$el.querySelector('input');
       if (inputEl) {
         inputEl.focus();
         // 自动选中文件名部分（排除扩展名）
         const fileName = data.objectName;
         const dotIndex = fileName.lastIndexOf('.');
         if (dotIndex > 0) {
           inputEl.setSelectionRange(0, dotIndex);
         } else {
           inputEl.select();
         }
       }
     }
   });
  },
   // 新增：确认重命名
 async confirmRename(data) {
  console.log('fjdapfjdspfjs几次1111')
   // 验证文件名
   const fileName = data.objectName.trim();
   if (!fileName) {
     this.cancelRename();
     return;
   }

   // 检查文件名有效性
   if (!this.isValidFilename(fileName)) {
     this.$message.error('文件名无效，请使用有效文件名');
     return;
   }

   // 检查重复
   const parent = this.$refs.fileTree.getNode(data.objectKey).parent;
   if (parent) {
    let siblings = parent.data.children || [];
    if( !parent.parent ) {
     siblings = parent.data || []
    }
     const isDuplicate = siblings.some(
      item => item !== data && item.objectName === fileName
     );
     // this.currentTempNodeParent = { children: this.treeData };
     // this.currentTempNodeParent.objectKey = this.rootKeys
     // console.log('fjdapfjasdpfjasdpfsjpdf',siblings,isDuplicate,data.objectKey,data,parent)
     if (isDuplicate) {
       this.$message.error('文件名重复，请使用其他名称');
       return;
     }
   }

   try {
     // 调用API更新文件名
     
     // await this.renameFileApi(data);
     
     
     // 更新节点显示
     const node = this.$refs.fileTree.getNode(data.objectKey);
     if (node) {
      const lastIndex = data.objectKey.lastIndexOf('/');
      const parentPath = lastIndex === -1 ? '' : data.objectKey.substring(0, lastIndex);
      const rootPath = !parent.parent ? true : false
      const destKey = !parent.parent ? this.rootKeys : parent?.data?.objectKey
      const params = {
       source_key: data.objectKey,
       dest_key: `${destKey}${fileName}`
      }
      node.data.label = fileName;
      node.data.objectKey = `${destKey}${fileName}`;
      console.log('fjiadspfajsdfpjasdfpasdf',parent, params,node)
      if(params.source_key === params.dest_key) return
      const res = await this.getHandleRenameFile(params);
      if(res.code === 200) {
       this.$message.success( res?.result?.message || '重命名成功');
       this.$emit('change-activeTab', node.data,node)
       this.$emit('refreshParentNode',destKey,node.data,rootPath,destKey + fileName,false,true,node)
       // this.$emit('refreshParentNode',this.currentTempNodeParent.objectKey,newNode,this.isRootLevel,this.newFileKey)
      }
       // console.log('fjapfjdsfpsjdpfs',res)
      //  // await this.uploadUrl(data);
      //  try {
      //   // 调用上传API
        
      //    // 局部刷新父节点
      //    // this.$emit('refreshParentNode',this.currentTempNodeParent.objectKey,newNode,this.isRootLevel,this.newFileKey)
      // } catch (error) {

      // }finally {

    // }
     }
   } catch (error) {
     console.error('重命名失败:', error);
     this.$message.error('重命名失败');
   } finally {
     this.editingNodeKey = null;
   }
 },

  // 新增：取消重命名
  cancelRename() {
   this.editingNodeKey = null;
  },

  // // 新增：模拟重命名API调用
  // async renameFileApi(data) {
  //  // 这里应该是实际调用后端接口的代码
  //  console.log(`重命名文件: ${data.objectName}`);
  //  // 模拟API调用
  //  return new Promise(resolve => setTimeout(resolve, 500));
  // },
  handleDropdownVisible(visible, data) {
   // 更新下拉菜单状态
   this.$set(this.dropdownOpenState, data.objectKey, visible);

   // 关闭时检查鼠标是否仍在节点上
   if (!visible && !data.showDropdown) {
    data.showDropdown = false
   }
  },
  handleMouseEnter(event, data) {
   // console.log('鼠标进入节点:', data);
   // 显示操作按钮
   this.$set(this.nodeHoverState, data.objectKey, true);
   // 清除之前的定时器
   if (this.hoverTimer) clearTimeout(this.hoverTimer);

   // 获取鼠标位置
   const mouseX = event.clientX;
   const mouseY = event.clientY;

   // 设置延迟显示
   this.hoverTimer = setTimeout(async () => {
    this.hoverPosition = {
     x: mouseX + 15, // 向右偏移15px
     y: mouseY - 10  // 向上偏移10px
    };

    // 异步加载文件信息
    this.hoverData = await this.loadFileMeta(data.objectKey);
   }, 500);
  },

  handleMouseLeave(data) {
   // 清除定时器并重置数据
   clearTimeout(this.hoverTimer);
   // 只有当下拉菜单未打开时才隐藏操作按钮
   if (!this.dropdownOpenState[data.objectKey]) {
        this.$set(this.nodeHoverState, data.objectKey, false);
      }
   this.hoverData = {};
  },

  async loadFileMeta(path) {
  // 增强处理：确保路径存在且有效
  if (path && typeof path === 'string') {
    // 安全处理路径分割
    const parts = path.split('/');
    const name = parts.length > 0 ? parts[parts.length - 1] : '';
    
    return {
      name: name,
      path: path,
      modified: new Date().toLocaleString(),
      size: '1KB'
    }
  }
  
  // 返回安全的空数据
  return {
    name: '',
    path: '',
    modified: '',
    size: ''
  }
},
  getTooltipContent(data) {
  },
  handleNodeClick(data, node, el) {
   if (node.isLeaf) {
    this.$emit('custom-event', data)
    this.$emit('current-click', data)
   }
  },
 }
}
</script>
<style lang="scss" scoped>
.file-tree {
 display: flex;
 flex-direction: column;
 height: 100%;
 overflow: hidden;
 .bottom {
    line-height: 32px;
    color: #616161;
    border-top: 1px solid rgb(235, 235, 235);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 0;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative; /* 为z-index生效 */
    
    i {
      margin-left: 8px;
      transition: transform 0.2s;
    }
    
    &:hover {
      box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05), 
                  0 2px 6px rgba(0, 0, 0, 0.05); /* 上下对称阴影 */
                  color: #0060c0; /* 与主题色保持一致 */
      i {
        transform: scale(1.1);
      }
    }
  }
 .new-file-input {
  :deep(.el-input__inner) {
   height: 22px;
   line-height: 22px;
  }
 }

 .tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  .ellipsis {
   line-height: 26px;
   height: 26px;
  }
  .node-lf {
   line-height: 26px;
   height: 26px;
   overflow: hidden;
   text-overflow: ellipsis;
   white-space: nowrap;
  }
  .file-icon {
   vertical-align: middle;
   margin-right: 4px;
   line-height:14px;
  }
  .label-text {
   vertical-align: middle;
  }
  .el-dropdown-rg {
   padding-right: 12px;
  }
 }

 :deep(.tree) {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  color: #616161;

  .el-dropdown-rg {
   i {
    color: #616161;
   }
  }

  .el-icon-caret-right:before {}

  .el-tree-node__content:hover {
   background-color: #e8e8e8;
  }


  .el-tree-node.is-current>.el-tree-node__content {
   background-color: #e4e6f1;
  }

  .el-tree-node:focus>.el-tree-node__content {
   background-color: #0060c0;
   color: #fff;

   .el-dropdown-rg {
    i {
     color: #fff;
    }
   }
  }

  .el-tree-node.is-expanded.is-current.is-focusable {
   .el-tree-node__content {
    user-select: none;
   }
  }

  // .el-tree-node.is-expanded.is-current.is-focusable {
  //  background-color: rgb(243, 243, 243);
  //  >.el-tree-node__content {
  //   background-color: #e4e6f1;
  //   color: #616161;
  //  }

  // }
 }
}
</style>
