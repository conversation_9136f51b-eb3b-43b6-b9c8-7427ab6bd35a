<template>
  <div class="popWrap" ref="popWrapRef">
    <div :title="'AI工具'" custom-class="custom-drawer" style="width:100%">
      <div style="border-bottom: 1px solid #dcdfe6;" class="popWrapSolid">
        <div class="drawer-title">
          <SvgIcon name="magicTool" class="ai-assistant-icon" /> <!-- 使用 SvgIcon 组件 -->
          <span>AI工具</span> <!-- 文字 -->
        </div>
      </div>
      <el-form ref="form" :model="currentToolForm" label-width="80px" label-position="top" class="custom-tool-form">
        <el-form-item>
          <template #label>
            <div class="label-container">
              <span class="title">当前使用工具:</span>
            </div>
          </template>
          <!-- <div class="label-select-container"> -->
          <!-- <span class="custom-label">帮我生成-</span> -->
          <el-select v-model="currentToolForm.selectedTool" :disabled="isReadOnly" placeholder="请选择工具" filterable
            class="custom-select" @change="handleSelectChange">
            <el-option v-for="tool in tools" :key="tool.name" :label="tool.Toolname" :value="tool.name">
            </el-option>
          </el-select>
          <!-- </div> -->
        </el-form-item>
        <div style="color:#4d70ce">请输入需要生成的内容及可能关联的文件</div>
        <el-form-item label="关联文件">
          <template #label>
            <div class="label-container">
              <span class="title">关联文件:</span>
              <el-tooltip class="item" effect="dark" content="选择文件" placement="top">
                  <i class="el-icon-plus cursor" style="font-size: 20px;" @click="addChose"></i>
              </el-tooltip>
            </div>
          </template>
          <div class="flex_all">
            <div class="image-wrapper" v-if="checkedFiles?.length > 0" v-for="(ivt, invt) in checkedFiles" :key="invt">
              <i class="el-icon-paperclip"></i>
              <div class="right">
                <el-tooltip class="item" effect="dark" :content="ivt.name" placement="top">
                  <span>{{ ivt.name }}</span>
                </el-tooltip>
                <i class="el-icon-close cursor" @click="clearFile(invt)"></i>
              </div>
            </div>
            <div v-if="showSelectVisable" style="padding: 10px;" :class="'selectRole'"
              v-click-outside="onClickOutside2">
              <AbilitysFile :checkedFilesData="checkedFiles" :firstUrl="firstUrl" :fileData="abilitys"
                styleOver="overflow: hidden;" @checkedChange="handleCheckedFiles" />
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <template #label>
            <div class="label-container">
              <span class="title">生成内容描述:</span>
            </div>
          </template>
          <div class="textarea-container">
            <el-input type="textarea" :disabled="isReadOnly" v-model="currentToolForm.desc"
              :autosize="{ minRows: 5, maxRows: 10 }" placeholder="请输入具体生成指令" class="custom-input"
              @keydown.enter.native="handleEnterKey"></el-input>
            <div class="floating-btn-wrap">
              <el-button v-if="!endStatus" :disabled="currentToolForm.desc == ''" type="primary" @click="submitForm"
                class="floating-btn"></el-button>
              <el-button v-else :disabled="currentToolForm.desc == ''" type="primary" @click="stopThink"
                class="floating-btn stop-desc"></el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <input type="file" ref="fileInput" style="display:none" @change="inputChange">
  </div>
</template>

<script>
import {
  generalExecute,
  get_scheme_materialsFetch,
  update_scheme_materialsFetch,
  startStopThinking,
  create_scheme_materialsFetch
}
  from '@/api/planGenerateApi.js'
import vClickOutside from 'v-click-outside';
import AbilitysFile from './abilitysFile.vue';
export default {
  name: "MagicToolsNew",
  props: {
    abilitys: {
      type: Object,
      default: () => {}
    },
    firstUrl: {
      type: String,
      default: ''
    },
    isReadOnly: {
      type: Boolean,
      default: false
    },
    sessionId: {
      type: String,
      default: '1'
    },
    tools: {
      type: Array,
      required: true
    },
    optHeader2TabsList: {
      type: Array,
      required: true
    },
    userAbility: {
      type: Array,
      required: true
    },
    endStatus: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  directives: {
    clickOutside: vClickOutside.directive
  },
  components: {
    AbilitysFile
  },
  data() {
    return {
      checkedFiles: [],
      showSelectVisable: false,
      newCascaderOptions: [],
      newCascaderOptionsValue: [],
      showCascader: false,
      cascaderOptionsValue: [],
      cascaderOptions: [
        {
          value: 1,
          label: '生产产物',
          children: this.optHeader2TabsList.map(item => this.transformItem(item))
        }
      ],
      optionsSecond: [],
      content: {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      },
      requirement: '',
      id: '',
      upload: require('@/assets/images/planGenerater/expert-upload.png'),
      selectedToolName: '', // 用于存储当前选中的工具名称
      CascaderProps: {
        multiple: true,
        expandTrigger: 'hover',
      },
      TooldialogTitle: '',
      toolForms: {},
      clickedTool: {},
      TooldialogVisible: false,
      visible: false,
      popoverWidth: 500,
      commandCenterVisible: false, // 新增指令中心的可见性状态
      commands: [], // 添加指令数据
      currentToolForm: {
        selectedTool: '', // 确保这里有一个初始值
        cascaderVal: [],
        desc: '',
      },
    };
  },
  mounted() {
    this.getData()
    this.$nextTick(() => {
      this.updatePopoverWidth();
      window.addEventListener("resize", this.updatePopoverWidth);
    })
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updatePopoverWidth);
  },
  methods: {
    clearFile(index) {
      this.checkedFiles.splice(index, 1)
    },
    onClickOutside2() {
      this.showSelectVisable = false
    },
    handleCheckedFiles(checkedFiles) {
      // 这里可以获取到所有选中的文件数据
      console.log('选中的文件：', checkedFiles)
      this.checkedFiles = checkedFiles
    },
    addChose() {
      this.showSelectVisable = true
    },
    getInstructionParam() {
      const tabsArr = [];
      let result = 'materials.关联文件.'
      this.newCascaderOptionsValue.forEach(item => {
        if (item[0] == 3) {
          result += item[1].toString() + ','
        } else {
          tabsArr.push(item[1] ?? item[0]);
        }
      });
      if (result == 'materials.关联文件.') {
        result = ''
      }
      this.cascaderOptionsValue.forEach(item => {
        if (item[0] == 3) {
          result += item[1].toString() + ','
        } else {
          tabsArr.push(item[1] ?? item[0]);
        }
      });
      console.log("tabsArr111", tabsArr);
      if (result != 'materials.关联文件.') {
        result = result.replace(/,$/, '')
      }
      tabsArr.push(result)
      return {
        context_codes: tabsArr.filter(it => it != '') || []
      }
    },
    setRef(data) {
      console.log(data, 'data')
      if (this.newCascaderOptionsValue.length > 0 && this.newCascaderOptionsValue[0][0] == 'materials.需求说明') {
        console.log(this.content.files, 'this.content.files')
        this.content.files.forEach((it, index) => {
          if (it.fileId == data) {
            this.$set(this.newCascaderOptionsValue, 1 + index, [3, data])
          }
        })
      } else {
        console.log(this.content.files, 'this.content.files')
        this.content.files.forEach((it, index) => {
          if (it.fileId == data) {
            this.newCascaderOptionsValue.push([3, data])
            this.$forceUpdate()
            // this.$set(this.newCascaderOptionsValue,this.newCascaderOptionsValue.length,[3,data])
          }
        })
      }
    },
    stopThink() {
      startStopThinking({ session_id: this.sessionId })
    },
    delUp(index) {
      this.content.files.splice(index, 1)
      this.save()
    },
    close() {
      this.showCascader = false
    },
    updateSelection(item) {
      // 使用Vue.set确保响应式更新
      this.$set(item, 'isSelect', !item.isSelect);
    },
    del(targetValue) {
      this.updateChildIsSelectByValue(this.cascaderOptions, targetValue);
    },
    updateChildIsSelectByValue(arr, targetValue) {
      arr.forEach((group, index) => {
        if (group.children) {
          const updatedChildren = group.children.map(child => {
            if (child.value === targetValue) {
              // 使用Vue.set确保响应性
              // return { ...child, isSelect: false };
              this.$set(child, 'isSelect', false);
            }
            return child;
          });
          // 如果需要可以重新赋值children
          this.$set(this.cascaderOptions[index], 'children', updatedChildren);
        }
      });
    },
    getTreeData() {
      return this.cascaderOptions.flatMap(group =>
        group.children ?
          group.children.filter(item => item.isSelect) :
          []
      );
    },
    choseFirst(item) {
      this.optionsSecond = item.children
    },
    async getData(status, data) {
      const res = await get_scheme_materialsFetch({
        "scheme_id": Number(this.$route.query.id),
      })
      this.requirement = res.data.result?.requirement || ''
      this.id = res.data.result?.id || ''
      this.content = res.data.result?.content || {
        abilitys: [],
        files: [],
        knowledges: [],
        relatedUrls: []
      };
      this.newCascaderOptions = [
        {
          value: 0,
          label: '需求说明',
          children: [
            {
              value: 'materials.背景',
              label: '背景',
            },
            {
              value: 'materials.目标',
              label: '目标',
            },
          ]
        },
        {
          value: 3,
          label: '关联文件',
          children: this.content.files.map(it => {
            return {
              value: it.fileId, label: it.name,
            }
          })
        }
      ]
      if (status) {
        if (this.newCascaderOptionsValue.length > 0 && this.newCascaderOptionsValue[0][0] == 'materials.需求说明') {
          console.log(this.content.files, 'this.content.files')
          this.content.files.forEach((it, index) => {
            if (it.fileId == data) {
              this.$set(this.newCascaderOptionsValue, 1 + index, [3, data])
            }
          })
        } else {
          console.log(this.content.files, 'this.content.files')
          this.content.files.forEach((it, index) => {
            if (it.fileId == data) {
              this.newCascaderOptionsValue.push([3, data])
              this.$forceUpdate()
              // this.$set(this.newCascaderOptionsValue,this.newCascaderOptionsValue.length,[3,data])
            }
          })
        }
      }
      console.log(this.cascaderOptionsValue, this.newCascaderOptionsValue, 'this.newCascaderOptionsValue')
    },
    uploadFun() {
      if (this.isReadOnly) return

      this.$refs.fileInput.click();
    },
    async inputChange(event) {
      const files = event.target.files;
      const maxSize = 100 * 1024 * 1024  // 10MB
      let breakFlag = false
      const allowedTypes = [
        'application/xlsx', 'application/xls',
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > maxSize) {
          this.$refs.fileInput.value = '';
          this.$message({
            type: 'error',
            message: '文件大小不能超过100MB!'
          });
          breakFlag = true
          break
        }
      }
      if (breakFlag) {
        return false
      }
      console.log(files, 'files')
      if (files) {
        for (let i = 0; i < files.length; i++) {
          const formData = new FormData();
          try {
            const res = await this.$axios.post('/obsfs/commonFile/generateSign', {
              fileType: this.$fileUtil.getFileSuffix(files[i].name)
            })
            if (res.data.status === 200) {
              formData.append('key', res.data.data.key)
              formData.append('accessKeyId', res.data.data.accessKeyId)
              formData.append('signature', res.data.data.signature)
              formData.append('policy', res.data.data.policy)
              formData.append('file', files[i])
            }
            const res1 = await this.$axios.post(res.data.data.obsUrl, formData)
            const fileName = this.$fileUtil.getFileName(files[i].name)
            const fileSize = files[i].size / 1024
            const fileType = this.$fileUtil.getFileSuffixWithSpot(files[i].name)
            const fileKey = res.data.data.key
            let fileId = ''
            await this.$axios
              .post('/file/add', {
                fileKey: fileKey,
                fileName: fileName,
                fileSize: fileSize,
                fileType: fileType,
                storagePlatform: 'Obs',

              })
              .then((res) => {
                if (res.data.status === 200) {
                  console.log(res.data.data, '文件id')
                  fileId = res.data.data.fileId
                  this.content.files.push(Object.assign({}, { ...res.data.data }, { name: fileName, url: res.data.data.path }))
                }
              })
            this.save(true, fileId)
          } catch (e) {
            console.log(e)
            this.$message.error('获取签名出错！')
          }
        }
      }
      this.$refs.fileInput.value = '';
    },
    async save(status, data) {
      // this.requirement = this.requirementEdit;
      this.content.knowledges = this.content.knowledges.map(item => {
        return {
          id: Number(item.id),
          name: item.name
        }
      })
      this.content.abilitys = this.content.abilitys.map(item => {
        return {
          id: Number(item.id),
          name: item.name,
          ext_info: {
            abilityIcon: item?.ext_info?.abilityIcon
          }
        }
      })
      //
      let res = {}
      if (this.id == '') {
        res = await create_scheme_materialsFetch({
          content: this.content,
          requirement: this.requirement,
          schemeId: Number(this.$route.query.id),
        })
      } else {
        res = await update_scheme_materialsFetch({
          content: this.content,
          requirement: this.requirement,
          id: this.id,
          scheme_id: Number(this.$route.query.id)
        })
      }
      if (res.data.result) {
        await this.getData(status, data)
        await this.$emit('getShenCData')
      } else {
        this.$message({
          type: 'error',
          message: '保存失败!'
        });
      }
      this.nlShow = false
      this.zsShow = false
      this.checkFlag = true

    },
    showToolDrawer() {
      this.TooldialogVisible = true; // 打开抽屉

      // 初始化 clickedTool
      if (this.currentToolForm.selectedTool) {
        const selectedTool = this.tools.find(tool => tool.name === this.currentToolForm.selectedTool);
        if (selectedTool) {
          this.clickedTool = selectedTool; // 赋值 clickedTool
        }
      }

      this.selectedToolName = ''; // 清除选中状态
    },
    handleClose() {
      this.TooldialogVisible = false;
      this.selectedToolName = ''; // 清除选中状态
    },
    handleMouseEnter() {
      this.$emit('setActiveZl')
    },
    updatePopoverWidth() {
      // 动态获取父容器宽度
      const parent = this.$refs.popWrapRef;
      if (parent) {
        const width = parent.offsetWidth;

        this.popoverWidth = width < 500 ? 500 : width; // 如果宽度小于500，则设置为500
      }
    },
    handleEnterKey(event) {
      // 检查是否按下了 Enter 键，且没有按下 Shift 键
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault(); // 阻止默认换行行为
        this.submitForm(); // 调用提交方法
      }
    },
    getSelectedValues(data) {
      let selectedValues = [];

      data.forEach(group => {
        if (group.children) {
          group.children.forEach(child => {
            if (child.isSelect) {
              if (child.value == '方案详情key') {
                selectedValues.push('方案');
              } else {
                selectedValues.push(child.value);
              }
            }
          });
        }
      });
      return selectedValues;
    },

    async submitForm() {
      // 请求SSE
      console.log(this.cascaderOptionsValue, 'ceshi')
      if (!this.clickedTool?.instruction_code) {
        this.$message({
          type: 'warning',
          message: '请选择工具!'
        });
        return
      }
      // const tabsArr = this.getSelectedValues(this.cascaderOptionsValue)
      const tabsArr = [];
      // if (!this.$route.query.isTask) {
      //   let result = 'materials.关联文件.'
      //   this.newCascaderOptionsValue.forEach(item => {
      //     if (item[0] == 3) {
      //       result += item[1].toString() + ','
      //     } else {
      //       tabsArr.push(item[1] ?? item[0]);
      //     }
      //   });
      //   if (result == 'materials.关联文件.') {
      //     result = ''
      //   }
      //   this.cascaderOptionsValue.forEach(item => {
      //     if (item[0] == 3) {
      //       result += item[1].toString() + ','
      //     } else {
      //       tabsArr.push(item[1] ?? item[0]);
      //     }
      //   });
      //   console.log("tabsArr111", tabsArr);
      //   if (result != 'materials.关联文件.') {
      //     result = result.replace(/,$/, '')
      //   }
      //   tabsArr.push(result)
      // } else {
        let result_new = 'file.'
        this.checkedFiles.forEach(item => {
          result_new += item.objectKey.toString() + ','
        })
        if (this.checkedFiles.length > 0) {
          tabsArr.push(result_new)
        }
      // }
      const params = {
        instruction_code: this.clickedTool?.instruction_code,
        instruction_param: {
          agent_template_id: this.clickedTool?.agent_template_id,
          goal: this.currentToolForm.desc,
          task_type: this.clickedTool.name,
          context_codes: tabsArr.filter(it => it != '') || [],
          name: this.clickedTool?.uuid,
        },
        session_id: this.sessionId
      }
      console.log("generalExecute params", params);
      this.$emit('ChangeEndStatus')
      await generalExecute(params)
      this.$emit('getAbilitiesAndResultNew')
      this.$emit('tab-update', this.clickedTool?.name)
      // this.clickedTool = {}
      this.TooldialogVisible = false;
    },
    handleSelectChange(selectedToolName) {
      const selectedTool = this.tools.find(tool => tool.name === selectedToolName);
      const userAbility = this.userAbility.find(it => it.name === selectedTool.Toolname);
      let first = []
      let second = []
      let third = []
      let four = []
      userAbility?.context_codes?.forEach(it => {
        if (it.indexOf('materials.背景') > -1) {
          first.push(it)
        }
        else if (it.indexOf('materials.目标') > -1) {
          first.push(it)
        } else if (it.indexOf('materials.关联文件.') > -1) {//materials.关联文件
          let data = it.split('materials.关联文件.')
          console.log(data, 'data')
          let newData = data[1].split(',')
          // second = newData.map(it => Number(it))
          this.content.files.forEach((it) => {
            newData.forEach(item => {
              if (it.fileId == item) {
                second.push(it.fileId)
              }
            })
          })
        } else if (it.indexOf('file.') > -1) {//materials.关联文件
          let data = it.split('file.')
          console.log(data, 'data')
          four = data[1].split(',')
        } else {
          third.push(it)
        }
      })
      let secondData = []
      if (first.length > 0) {
        first.forEach((it, index) => {
          secondData.push([0, it])
        })
        second.forEach((it, index) => {
          secondData.push([3, it])
        })
      } else {
        second.forEach((it, index) => {
          secondData.push([3, it])
        })
      }
      let thirdData = []
      third.forEach((it, index) => {
        thirdData.push([1, it])
      })
      this.newCascaderOptionsValue = secondData
      this.cascaderOptionsValue = thirdData
      this.checkedFiles = four.filter(it => it).map(it => {
        return {
          isDir: false,
          name: it.substring(it.lastIndexOf('/') + 1),
          objectKey: it,
          objectSize: 0,
          objectType: "",
          scheme_id: this.$route.query.id,
          checked: false
        }
      })
      console.log(this.cascaderOptionsValue, this.newCascaderOptionsValue, 'this.newCascaderOptionsValue')
      this.$forceUpdate()
      if (selectedTool) {
        this.handleToolSelection(selectedTool);
      }
    },
    handleToolSelection(tool) {
      this.clickedTool = tool;
      this.TooldialogTitle = tool.Toolname || '';
      const hasTool = this.optHeader2TabsList.some(item => item.name === tool.name);
      const productionOption = this.cascaderOptions.find(option => option.label === '生产产物');

      if (!this.toolForms[tool.name]) {
        this.$set(this.toolForms, tool.name, {
          cascaderVal: [productionOption && hasTool ? [productionOption.value, tool.name] : []],
          desc: tool?.default_chat_message || ''
        });
      }

      this.currentToolForm = this.toolForms[tool.name];
      this.currentToolForm.selectedTool = tool.name;
      // this.TooldialogVisible = true;
      this.selectedToolName = tool.name;
    },
    async onCardClick(tool) {
      this.handleToolSelection(tool);
    },
    closePopover() {
      this.visible = false; // 手动关闭弹出层
    },
    openCommandCenter() {
      this.commandCenterVisible = true; // 打开指令中心
    },
    onCommandClick(command) {
      this.$root.$emit('command-selected', command.content); // 发送事件
      this.commandCenterVisible = false; // 关闭指令中心
    },
    isOverflowing(el) {
      return el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth;
    },
    transformItem(item) {
      const transformed = {
        ...item,
        value: item.name,
        label: item.label,
        isSelect: false
      };
      return transformed;
    },
  },
  computed: {
    titleOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.titleRef[index];
        return this.isOverflowing(el);
      });
    },
    contentOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.contentRef[index];
        return this.isOverflowing(el);
      });
    },
  },
  watch: {
    TooldialogVisible: {
      handler(val, oldVal) {
        if (val) {
          this.getData()
          if (this.tools.length > 0) {
            this.currentToolForm.selectedTool = this.tools[0].name;
            this.handleSelectChange(this.tools[0].name)
          }

        }
      },
      immediate: true,
      deep: true
    },
    optHeader2TabsList: {
      handler(val, oldVal) {
        this.cascaderOptions = [
          {
            value: 1,
            label: '生产产物',
            children: val.filter(item => item.name != '文件key' && item.name != 'api_generate_ability.代码生成').map(item => this.transformItem(item))
          }
        ]
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style scoped lang="scss">
.cust-disabled {
  cursor: not-allowed !important;
  opacity: 0.6;
}

:deep(.el-drawer__header) {
  text-align: left; // 标题整体靠左对齐
  padding: 16px 20px; // 调整标题的内边距
  margin-bottom: 0; // 去掉默认的 margin-bottom
  border-bottom: 1px solid #ebeef5; // 添加底部边框
}

.drawer-title {
  display: flex;
  padding-left: 20px;
  align-items: center; // 图标和文字垂直居中
}

.ai-assistant-icon {
  width: 16px;
  height: 16px;
  color: #409eff; // 修改图标颜色
  margin-right: 8px; // 调整图标和文字的间距
}

.popWrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  /* 修改这一行，使按钮在一排 */
  gap: 10px;
  /* 设置按钮之间的间距 */
  // margin-right: 20px;
  padding: 10px 20px 0 20px;
  background: #fff;
  height: 100%;
  position: relative;
  cursor: default;
}

.custom-popover {
  padding: 0px;

  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px 20px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }

    .close-button {
      border: none !important;
      background-color: unset !important;
    }
  }

  .popover-content {
    padding: 17px 20px;
    max-height: 450px;
    overflow-y: auto;
  }

  .card-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 三列布局 */
    gap: 10px;
  }

  .card-item {
    height: 110px;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    /* 调整圆角 */
    cursor: pointer;
    transition: box-shadow 0.3s, transform 0.3s, border-color 0.3s;
    /* 添加平滑过渡效果 */
    background: #ffffff;
    /* 设置背景色为白色 */
    color: #333;
    /* 设置字体颜色为深灰色 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    /* 添加阴影 */
  }

  .card-item:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1);
    /* 增强悬停时的阴影效果 */
    transform: translateY(-5px);
    /* 悬停时向上移动 */
    border-color: #0061FF;
  }

  .card-item h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
  }

  .card-item p {
    margin: 5px 0 0;
    font-size: 12px;
    color: #666;
    /* 设置描述文字颜色为中灰色 */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.drawer-wrapper {
  margin: 24px;
}

.el-drawer__header:first-child {
  padding: 10px 20px;
  /* 调整header的padding */
  font-size: 16px;
  /* 调整header的字体大小 */
  border-bottom: 1px solid #ebeef5;
  /* 添加底部边框 */
  margin-bottom: 0;
  /* 将 margin-bottom 设置为 0 */
}

.el-drawer__header> :first-child {
  font-weight: 600 !important;
  /* 调整header的字体粗细 */
}

.drawer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 添加这一行，使卡片居中对齐 */
  gap: 10px;
  /* 设置卡片之间的间距 */
}

.command-card {
  padding: 10px;
  width: 272px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #f3f2ff;
  transition: box-shadow 0.3s, transform 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 垂直居中 */
}

.command-card:hover {
  border: 1px solid #323ff0;
  cursor: pointer;
}

.command-card h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif';
  /* 设置更好的字体 */
}

.command-card p {
  margin: 5px 0 0;
  font-size: 14px;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif';
  /* 设置更好的字体 */
  color: #585a73;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制最多显示3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 20px;
  /* 设置最小高度为一行字的高度 */
}

.rainbow-text-button {
  border-radius: 12px !important;
}

@keyframes shimmer {
  0% {
    left: -150%;
    /* 起点 */
  }

  50% {
    left: 50%;
    /* 光效经过按钮中间 */
  }

  100% {
    left: 150%;
    /* 光效消失在按钮右侧 */
  }
}

.custom-tool-form {
  margin-top: 36px;
  position: relative;

  :deep(.el-form-item__label) {
    padding-bottom: 0px !important;
    /* 调整 label 与内容的间距 */
    font-size: 14px;
    /* 调整字体大小 */
    font-weight: 600;
    /* 增加一点加粗效果 */
    color: #606266;
    /* 使用更舒适的颜色 */
  }

  .custom-select {
    width: 100%;
  }
}

.textarea-container {
  width: 100%;
  position: relative;
  display: inline-block;
  /* 确保按钮和 textarea 能一起缩放 */

  .custom-input {
    width: 100%;
    /* 让 textarea 撑满父容器 */
    resize: both;

    :deep(.el-textarea__inner) {
      // border: 0 !important; // 去掉边框
      // padding: 0px 0px;
    }

    /* 启用缩放 */
  }


  .floating-btn-wrap {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 10;
    line-height: normal;

    .floating-btn {
      font-size: 10px;
      color: #969799;
      width: 32px;
      height: 32px;
      border-radius: 50% !important;
      background-size: cover;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: center;
      background-image: url("~@/assets/images/planGenerater/active-send.png");
      border-radius: 4px;
      border: transparent;

      &.is-disabled {
        background-image: url("~@/assets/images/planGenerater/expert-send.png");
      }

      img {
        width: 32px;
      }
    }

    /* 去掉多余的 img 样式 */
  }
}

.tool-dialog {
  :deep(.el-dialog__body) {
    padding: 0px 20px;
  }

  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ebeef5;
    /* 添加分隔线 */
    padding-bottom: 12px;
    /* 调整分隔线与标题的间距 */
    margin-bottom: 16px;
    /* 增加标题与内容的间距 */
    font-weight: 600;
    /* 加粗标题 */
    font-size: 16px;
    /* 调整标题字体大小 */
    color: #303133;
    /* 调整标题颜色 */
  }
}
</style>
<style lang="scss">
.custom-drawer {
  .el-drawer__body {
    padding: 0px 20px;
  }
}

.custom-cascader {
  width: 100%;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* 三列布局 */
  gap: 10px;
  /* 设置卡片之间的间距 */
}

.card-item {
  height: 50px;
  /* 调整卡片高度 */
  padding: 5px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  /* 调整圆角 */
  cursor: pointer;
  background: #ffffff;
  /* 设置背景色为白色 */
  color: #333;
  /* 设置字体颜色为深灰色 */
  display: flex;
  /* 添加这一行 */
  justify-content: center;
  /* 添加这一行，使内容水平居中 */
  align-items: center;
  /* 添加这一行，使内容垂直居中 */
  transition: border-color 0.3s, box-shadow 0.3s; // 添加过渡效果
}

.card-item:hover {
  font-weight: bold;
  border-color: #409eff;
}

.card-item.selected-card {
  border-color: #409eff; // 选中时边框颜色变为蓝色
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.5); // 添加阴影效果
  color: #409eff; // 选中时文字颜色变为蓝色
}

.card-item h4 {
  margin: 0;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: inherit; // 继承父元素的颜色
  text-align: center;
  /* 添加这一行，使字体居中 */
}
</style>

<style scoped lang="scss">
.custom-tool-form {
  .custom-form-item {
    margin-bottom: 0; // 去掉默认的 margin-bottom

    :deep(.el-form-item__content) {
      display: flex;
      flex-direction: column;
      width: 100%;
      border: 1px solid #dcdfe6; // 添加边框
      border-radius: 4px; // 添加圆角
      padding: 10px; // 内边距
      background-color: #fff; // 背景色
    }

    &:hover :deep(.el-form-item__content) {
      border-color: #409eff; // 悬停时边框颜色变为蓝色
    }
  }

  .label-select-container {
    display: flex;
    align-items: center; // 让 label 和 select 垂直居中
    margin-bottom: 8px; // 调整 label-select 和 textarea 的间距

    .custom-label {
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px; // 调整 label 和 select 的间距
      white-space: nowrap; // 防止 label 换行
      background: linear-gradient(90deg, hsl(238, 100%, 69%), #db08e7); // 渐变色
      -webkit-background-clip: text; // 将背景裁剪为文字
      background-clip: text;
      color: transparent; // 将文字颜色设置为透明，以显示背景渐变
    }

    .custom-select {
      flex: 1; // 让 select 占据剩余空间
    }
  }

  .textarea-container {
    width: 100%;
    position: relative;
    background-color: #fff; // 背景色

    .custom-input {
      width: 100%;
      border: none; // 去掉 textarea 的边框
      padding: 0; // 去掉 textarea 的内边距
      resize: vertical; // 允许垂直调整大小
    }

    .floating-btn-wrap {
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 10;
      line-height: normal;

      .floating-btn {
        font-size: 10px;
        color: #969799;
        width: 32px;
        height: 32px;
        border-radius: 0;
        background-size: cover;
        background-repeat: no-repeat;
        background-size: 100%;
        background-position: center;
        background-image: url("~@/assets/images/planGenerater/active-send.png");
        border-radius: 4px;
        border: transparent;

        &.is-disabled {
          background-image: url("~@/assets/images/planGenerater/expert-send.png");
        }
      }
    }
  }
}
</style>
<style scoped lang="scss">
.flex_first {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  flex: 1;
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 80px;
  box-sizing: border-box;
  color: #606266;
  border-right: 1px solid #e4e7ed;
  cursor: pointer;
}

.flex_magic {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 5px 0;
  font-size: 14px;
  background: #fff;
}

.flex_hover:hover {
  color: #409eff;
  font-weight: 700;
}

.flex_hover {
  width: 100%;
}

.zslist {
  width: auto;
  display: flex;
  align-items: center;
  height: 24px;
  padding: 2px 4px;
  border: 1px solid #DCDDE0;
  margin-right: 8px;
}

.card-text {
  display: inline-block;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 80px;
  height: 24px;
  line-height: 24px;
}

.flex_all {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  gap: 5px;
}

.flex_add {
  font-size: 16px;
  border: 1px solid #DCDDE0;
  margin-right: 10px;
}

.position {
  align-items: flex-start;
  position: absolute;
  top: 45px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.label_c {
  display: flex;
  align-items: center;
  position: relative;
  height: 24px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #DCDDE0;
  margin-right: 8px;
  padding: 2px 4px;

  &:hover {
    border: 1px solid #409eff;
  }
}

.cur {
  cursor: pointer;
}
</style>
<style scoped lang="scss">
.label-container {
  display: flex;
  align-items: center; // 确保元素垂直居中
  gap: 8px; // 设置元素之间的间距
  font-size: 14px; // 统一字体大小
  margin-top: 10px; // 增加与上方 form-item 的距离
  margin-bottom: 8px; // 调整与下方内容的距离

  .title {
    font-weight: bold; // 加粗
    color: #000; // 黑色
  }

  .flex_add {
    font-size: 16px; // 调整加号图标大小
    border: 1px solid #DCDDE0; // 添加边框
    border-radius: 4px; // 圆角
    padding: 4px; // 内边距
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果

    &:hover {
      background-color: #f5f7fa; // 悬停时背景色
      border-color: #409eff; // 悬停时边框颜色
    }
  }
}

.title {
  font-weight: bold; // 加粗
  color: #000; // 黑色
  margin-bottom: 10px; // 调整与下方内容的距离
  line-height: 30px;
  height: 20px;
}

.popWrapSolid {
  border-bottom: 1px solid #dcdfe6;
  position: absolute;
  width: 100%;
  left: 0px;
  top: 8.5px;
  height: 38px;
}

.flex_all_new {
  margin-bottom: 10px;
  gap: 0px
}

.upload-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #DCDDE0; // 添加边框
  border-radius: 4px; // 圆角
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s; // 添加过渡效果
  width: 34px;
  height: 34px;
  border-radius: 2px;
  border: 1px solid #C8C9CC;
  margin-left: 4px;

  &:hover {
    background-color: #f5f7fa; // 悬停时背景色
    border-color: #409eff; // 悬停时边框颜色
  }

  img {
    transition: opacity 0.3s; // 添加过渡效果

    &:hover {
      opacity: 0.8; // 悬停时降低透明度
    }
  }
}

.stop-desc {
  background: #4068d4 !important;
  background-image: none !important;
  cursor: pointer !important;
  ;
}

.stop-desc::after {
  content: "";
  /* 添加此行以确保伪元素显示 */
  width: 12px;
  height: 12px;
  display: block;
  background: #fff;
  margin-left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

:deep(.custom-tool-form .el-form-item) {
  margin-bottom: 0px !important;
}

.image-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  height: 24px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #DCDDE0;

  &:hover {
    border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2;
  }

  .el-image.uploaded-image {
    width: fit-content;

    :deep(img) {
      border-radius: 2px;
    }
  }

  .right {
    /* margin-left: 8px; */
    display: flex;
    align-items: center;

    span {
      width: auto;
      max-width: 100px;
      text-align: left;
      margin-left: 8px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    i {
      cursor: pointer;
      margin-left: 8px;
    }
  }

  /* 作为占位背景色，防止无图片时突兀 */

  @media (max-width: 600px) {
    width: 30px;
    height: 30px;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 保持图片比例并覆盖容器 */
    cursor: pointer;
  }

  .delete-image-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(255, 255, 255, 0.8);
    color: red;
    font-size: 10px;
    /* 更小的字体大小 */
    width: 16px;
    height: 16px;
    padding: 0;
    display: none;
    /* 默认隐藏 */
    transition: opacity 0.3s, transform 0.3s;
    border: none;
    cursor: pointer;

    /* 使用更小的按钮尺寸 */
    .el-icon-delete {
      font-size: 10px;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 1);
      transform: scale(1.2);
      /* 放大按钮，增强交互感 */
    }
  }

  &:hover {
    .delete-image-btn {
      display: block;
      opacity: 1;
    }
  }
}

.selectRole {
  position: absolute;
  width: 330px;
  z-index: 99;
  top: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px;
  display: flex;
  flex-direction: column;

  .selectRoleHigh {
    bottom: 102px !important;
  }

  .roleItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 2px 4px;
    cursor: pointer;

    &:hover {
      background: #eff3ff;
    }

    &:active {
      background: #eff3ff;
    }

    img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-right: 4px;
    }

    .name {
      cursor: pointer;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }

    .mark-tag {
      background: #eff3ff;
      border-radius: 2px;
      padding: 0px 8px;
      color: #4068d4 !important;
      line-height: 17px;
      font-size: 12px;
      margin-left: 4px;
      display: inline-block;
      max-width: 260px;
      /* 设置文本溢出时的行为为省略号 */
      text-overflow: ellipsis;

      /* 设置超出容器的内容应该被裁剪掉 */
      overflow: hidden;

      /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
      white-space: nowrap;
    }
  }
}

.icon_plus {
  border-radius: 6px;
  border: 1px solid #dcdde0;
  margin-right: 4px;
  height: 24px;
  /* width: 24px; */
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  margin-bottom: 10px;

  &:hover {
    /* border-image: linear-gradient(156deg, #ce8cff, #759aff, #4e7ef5, #64c4ff) 2; */
    background-color: #ebecf0;
  }
}

.cursor {
  cursor: pointer;
}
</style>
