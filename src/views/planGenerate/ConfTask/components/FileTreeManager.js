export default class OrderedTreeDiff {
 constructor() {
   this.lastTree = null;
   this.nodeIndex = new Map();
   this.parentIndex = new Map(); // 存储节点的父节点路径
   this.positionIndex = new Map(); // 存储节点在父节点中的位置
 }

 // 构建节点索引：objectKey -> 节点
 buildPathIndex(tree, parentPath = null) {
   const nodeIndex = new Map();
   const parentIndex = new Map();
   const positionIndex = new Map();

   const traverse = (nodes, parent = null) => {
    nodes.forEach((node, index) => {
     nodeIndex.set(node.objectKey, node);
     // 记录父节点路径
     parentIndex.set(node.objectKey, parent ? parent.objectKey : null);
     // 记录在父节点中的位置
     positionIndex.set(node.objectKey, index);
     
     // console.log('fasdjfjapsdfjapsfas',node.children)
       if (node.children && node.children.length > 0) {
         traverse(node.children,node);
       }
     });
   };
   
   traverse(tree);
   return { nodeIndex, parentIndex, positionIndex };
 }

 diff(newTree) {
   if (!this.lastTree) {
    const { nodeIndex, parentIndex, positionIndex } = this.buildPathIndex(newTree);
     this.lastTree = newTree;
     this.nodeIndex = nodeIndex;
     this.parentIndex = parentIndex;
     this.positionIndex = positionIndex;
     // this.nodeIndex = this.buildPathIndex(newTree);
     return { type: 'full', data: newTree };
   }

   // 构建新树的索引
    const { 
      nodeIndex: newIndex, 
      parentIndex: newParentIndex,
      positionIndex: newPositionIndex
    } = this.buildPathIndex(newTree);
   // const newIndex = this.buildPathIndex(newTree);
   // console.log('fdjafpadsjfpdsjfdspjfsdjfsdf',this.nodeIndex,newIndex,this.lastTree,newTree)
   const changes = { 
     added: [], 
     removed: [], 
     updated: [],
     moved: []  // 记录移动的节点及其新位置
   };

   // 1. 检测删除节点
   this.nodeIndex.forEach((_, path) => {
     if (!newIndex.has(path)) {
       changes.removed.push(path);
     }
   });

   // 2. 检测新增和更新节点
   newIndex.forEach((newNode, newPath) => {
     const oldNode = this.nodeIndex.get(newPath);
      // console.log('fjaspfjdspfjdpfjdsfpdsss',oldNode)
     if (!oldNode) {
       // 新增节点
        changes.added.push({
          node: this.cloneNode(newNode),
          parentPath: newParentIndex.get(newPath), // 父节点路径
          position: newPositionIndex.get(newPath)   // 在父节点中的位置
        });
     } else if (!this.isNodeEqual(oldNode, newNode)) {
       // 节点属性变化
       changes.updated.push({
         path: newPath,
         data: this.extractNodeData(newNode)
       });
     }
   });

   // 3. 检测节点移动（顺序变化）
   const detectMoves = (oldParent, newParent, parentPath) => {
     if (!oldParent || !newParent || !oldParent.children || !newParent.children) return;
     
     const oldChildren = oldParent.children || [];
     const newChildren = newParent.children || [];
      // 比较顺序是否变化（使用 objectKey 作为唯一标识）
  const oldOrder = oldChildren.map(n => n.objectKey);
  const newOrder = newChildren.map(n => n.objectKey);
  
  // 检测顺序变化（即使内容相同）
  if (JSON.stringify(oldOrder) !== JSON.stringify(newOrder)) {
    changes.moved.push({
      parentPath,
      oldOrder,
      newOrder
    });
  }
     // console.log('fjdspafjasdpfjsdnewChildren',oldKeys,newKeys)
     
     // // 比较顺序是否变化
     // const oldKeys = oldChildren.map(n => n.objectKey);
     // const newKeys = newChildren.map(n => n.objectKey);
     
     // if (oldKeys.join(",") !== newKeys.join(",")) {
     //   changes.moved.push({
     //     parentPath,
     //     newOrder: newKeys
     //   });
     // }
     
     // 递归检测子节点
     newChildren.forEach(newChild => {
       const oldChild = oldChildren.find(c => c.objectKey === newChild.objectKey);
       if (oldChild) {
         detectMoves(oldChild, newChild, newChild.objectKey);
       }
     });
   };
   
   // 从根节点开始检测移动
   detectMoves(
     { children: this.lastTree }, 
     { children: newTree }, 
     ""
   );

   // 更新缓存
   this.lastTree = newTree;
   this.nodeIndex = newIndex;
   this.parentIndex = newParentIndex;
   this.positionIndex = newPositionIndex;
   
   return Object.values(changes).some(arr => arr.length > 0)
     ? { type: 'partial', changes }
     : null;
 }

 // 辅助函数：深度克隆节点
 cloneNode(node) {
   return JSON.parse(JSON.stringify(node));
 }

 // 提取节点核心数据（排除children）
 extractNodeData(node) {
   return {
     ...node,
     isDir: node.isDir,
     objectSize: node.objectSize,
     objectType: node.objectType,
     label: node.label,
     icons: node.icons
   };
 }

 // 节点比较（排除children）
 isNodeEqual(a, b) {
   return (
     a.isDir === b.isDir &&
     a.objectSize === b.objectSize &&
     a.objectType === b.objectType &&
     a.label === b.label &&
     a.icons === b.icons
   );
 }

 // 直接从树中查找节点（完整路径匹配）
 getNodeByPath(tree, path) {
   let result = null;
   
   const traverse = (nodes) => {
     for (const node of nodes) {
       if (node.objectKey === path) {
         result = node;
         return true;
       }
       if (node.children && traverse(node.children)) {
         return true;
       }
     }
     return false;
   };
   
   traverse(tree);
   return result ? this.cloneNode(result) : null;
 }
}