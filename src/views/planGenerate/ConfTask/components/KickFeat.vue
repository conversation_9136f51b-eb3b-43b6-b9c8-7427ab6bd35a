<template>
    <div>
        <el-alert v-if="hasChatingName" :closable="false" type="error" class="chatAlert">
            <span slot="title">正在与【{{ hasChatingName }}】对话中</span>
            <el-button type="text" color="danger" @click="showKickMessageBox" :disabled="kickLoading">强制退出</el-button>
        </el-alert>
        <el-alert v-if="isOccupied" :closable="false" type="error" class="chatAlert">
            <span slot="title">对话已被他人占用</span>
        </el-alert>
    </div>
</template>

<script>
import {
    chatDisconnect,
} from '@/api/planGenerateApi.js'

export default {
    name: 'KickFeat',
    data() {
        return {

        }
    },
    props: {
        kickLoading: {
            type: Boolean,
            default: false
        },
        isOccupied: {
            type: Boolean,
            default: false
        },
        hasChatingName: {
            type: String
        },
    },
    methods: {
        showKickMessageBox() {
            this.$confirm(`此操作将【${this.hasChatingName}】强制退出,是否继续?`, '提示', {
                cancelButtonText: '',
                confirmButtonText: '确定',
                type: 'warning',
                customClass: 'my-message-box'
            })
                .then(async () => {
                    const res = await chatDisconnect({ session_id: this.sessionId, scheme_id: this.$route.query.id })
                    this.$emit('e-update:kickedloading', true)
                    if (res.data.code === 200) {
                        this.$message.success('操作成功');
                        setTimeout(() => {
                            this.$emit('e-update:kicked', '')
                        }, 1000)
                    } else {
                        console.warn('操作异常');
                    }
                })
                .catch(() => {
                    console.warn('操作异常');
                })
        },

    },
}


</script>


<style lang="scss" scoped>
.chatAlert {
    ::v-deep .el-alert__content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-alert__description {
            font-size: 12px;
            margin: 0px 0 0 0;

            .el-button {
                border-color: transparent;
                color: #f56c6c;
                background-color: #fef0f0;

                &:hover {
                    background-color: #f6f7fb;
                }
            }
        }
    }
}

.my-message-box {
    :deep(.el-message-box__btns .el-button) {
        width: 62px !important;
    }

    :deep(.el-message-box__header .el-message-box__status) {
        display: block !important;
    }

    .el-message-box__content {
        padding: 10px 30px !important;
    }

    .el-message-box__message {
        padding: 0px !important
    }

}
</style>
