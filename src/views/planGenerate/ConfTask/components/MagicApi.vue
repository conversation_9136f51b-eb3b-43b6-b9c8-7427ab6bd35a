<template>
  <div ref="popWrapRef" class="popWrap ">
    <!-- 指令中心按钮 -->
    <div :class="{ 'cust-disabled': isReadOnly }" @click="openCommandCenter" class="popWrapFont chuangzuo">
      <img src="@/assets/images/planGenerater/ic_zhiling.png" width="14" height="12" style="margin-right: 4px;"/> 指令中心
    </div>

    <!-- 指令中心抽屉 -->
    <el-drawer
      title="指令中心"
      :visible.sync="commandCenterVisible"
      :modal-append-to-body="false"
      direction="rtl"
      size="320px"
      :wrapper-classes="['drawer-wrapper']"
      @click.self="resetSearch"
    >
      <div class="drawer-content">
        <!-- 新增搜索框和新建按钮的切换 -->
        <div class="search-create-container">
          <el-button
            v-if="!showSearch"
            type="secondary"
            class="create-button"
            @click="createNewCommand"
            icon="el-icon-plus"
            >新建指令</el-button
          >
          <el-input
            v-if="showSearch"
            v-model="searchQuery"
            placeholder="搜索指令"
            class="search-input"
            @input="handleSearchInput"
            @blur="resetSearch"
            ref="searchInputRef"
          >
            <template #prefix>
              <i class="el-icon-search search-icon" />
            </template>
          </el-input>
          <el-button v-if="!showSearch" class="toggle-button" @click="toggleSearch">
            <i class="el-icon-search" />
          </el-button>
        </div>
        <!-- 标签列表 -->
        <div class="tag-list" v-if="!searchQuery">
          <div class="tags-container">
            <el-tag
              v-for="(tag, index) in visibleTags"
              :key="index"
              class="tag-item"
              :class="{ 'tag-selected': selectedTagId === tag.id }"
              @click="handleTagClick(tag, index)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
          <el-dropdown @command="handleTagCommand">
            <el-tag class="tag-item more-tags"><i class="el-icon-s-fold"></i></el-tag>
            <el-dropdown-menu slot="dropdown">
              <!-- 直接渲染所有标签 -->
              <el-dropdown-item
                v-for="tag in tags"
                :key="tag.id"
                :command="tag.id"
                :class="{ 'tag-selected': selectedTagId === tag.id }"
              >
                {{ tag.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>

        <!-- 指令卡片 -->
        <!-- 包装指令卡片的滚动容器 -->
        <div class="commands-wrapper">
          <div v-for="(command, index) in commands" :key="index" class="command-card">
            <div class="card-content" @click="onCommandClick(command)">
              <el-tooltip class="item" effect="dark" :content="command.title" placement="top">
              <h4 ref="titleRef" >{{ command.title }}</h4>
            </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="command.content" placement="top">
              <p ref="contentRef">{{ command.content }}</p>
            </el-tooltip>
            </div>
            <div v-if="selectedTagId === '-1'" class="icons-wrapper">
              <i class="el-icon-edit-outline" @click="editCommand(command)"></i>

              <i class="el-icon-delete" @click="deleteCommand(command)"></i>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
    <el-dialog :visible.sync="zsShow" title="新建指令" width="700px" append-to-body @closed="handleDialogClosed">
      <div>
        <el-form
          ref="formRef"
          :model="formValue"
          label-width="80px"
          label-position="right"
          :rules="rules"
        >
          <el-form-item prop="title" label="指令标题">
            <el-input
              v-model="formValue.title"
              type="text"
              :autosize="{ minRows: 1 }"
              placeholder="请输入指令标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item prop="content" label="指令内容">
            <el-input
              v-model="formValue.content"
              type="textarea"
              :autosize="{ minRows: 3 }"
              placeholder="请输入指令内容"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div>
          <el-button type="primary" @click="handleOk"> 确定 </el-button>
          <el-button @click="zsShow = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createRefprompts,
  updateRefprompts,
  deleteRefprompts,
  getRefpromptsList,
  queryRefpromptUseTags,
  bindTag
} from '@/api/planGenerateApi.js';
import { v4 as uuidv4 } from 'uuid';
export default {
  name: 'MagicApi',
  props: {
   isReadOnly: {
   type: Boolean,
   default: false
 },
    commands: {
      type: Array,
      required: true
    },
    tags: {
      type: Array,
      required: true
    },
    disabledStatus:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      zsShow: false,
      visible: false,
      popoverWidth: 500,
      commandCenterVisible: false, // 新增指令中心的可见性状态
      searchQuery: '', // 新增搜索框的绑定数据
      showSearch: false, // 控制搜索框和新建按钮的显示
      maxVisibleTags: 4, // 控制可见标签的最大数量
      visibleStartIndex: 0, // 当前显示的标签的起始索引
      localTags: [], // 添加指令数据
      formValue: {},
      formRef: null,
      selectedTagId: null, // 添加选中的tag id
      rules: {
      title: [
        { required: true, message: '请输入指令标题', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入指令内容', trigger: 'blur' }
      ]
    }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.updatePopoverWidth();
      window.addEventListener('resize', this.updatePopoverWidth);
    });
    // this.fetchCommands(); // 调用方法获取指令列表
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updatePopoverWidth);
  },
  methods: {
    handleSearchInput(content) {
      if (content) {
        this.$emit('tag-selected-change', 0, content);
      } else {
        this.$emit('tag-selected-change', this.selectedTagId, content);
      }
    },

    handleTagCommand(tagId) {
      this.selectedTagId = tagId; // 设置选中的tag
      const tagIndex = this.tags.findIndex((tag) => tag.id === tagId);
      if (tagIndex !== -1) {
        const [tag] = this.tags.splice(tagIndex, 1);
        this.tags.unshift(tag);
        this.visibleStartIndex = 0; // Reset the start index to show the newly moved tag
        this.$emit('tag-selected-change', tag.id, '');
      }
    },
    createNewCommand() {
      console.log('Creating new command');
      this.formValue = {
        id: null,
        title: '',
        content: ''
      };
      this.zsShow = true;
      // 在这里实现新建指令的逻辑
    },
    toggleSearchBar() {
      this.$message.success('搜索按钮被点击！');
      this.searchActive = !this.searchActive; // 切换搜索框状态
      if (this.searchActive) {
        this.$nextTick(() => {
          const input = document.querySelector('.el-input__inner');
          input?.focus(); // 聚焦到输入框
        });
      }
    },
    collapseSearchBar() {
      if (!this.searchValue) {
        this.searchActive = false; // 如果没有输入内容，收起搜索框
      }
    },
    handleTagClick(tag, index) {
      this.selectedTagId = tag.id; // 设置选中的tag
      // 如果点击的是最左边的标签，且当前不是第一个标签
      if (index === 0 && this.visibleStartIndex > 0) {
        this.visibleStartIndex -= 1; // 标签向右移动
      }
      // 如果点击的是最右边的标签，且后面还有更多标签
      else if (index === this.visibleTags.length - 1 && this.hasHiddenTags) {
        this.visibleStartIndex += 1; // 标签向左移动
      }
      // 确保 visibleStartIndex 不会超出范围
      this.visibleStartIndex = Math.max(
        0,
        Math.min(this.visibleStartIndex, this.tags.length - this.maxVisibleTags)
      );

      console.log('Clicked tab info:', tag);
      this.$emit('tag-selected-change', tag.id);
    },
    handleMouseEnter() {
      this.$emit('setActiveZl');
    },
    updatePopoverWidth() {
      // 动态获取父容器宽度
      const parent = this.$refs.popWrapRef;
      if (parent) {
        const width = parent.offsetWidth;

        this.popoverWidth = width < 500 ? 500 : width; // 如果宽度小于500，则设置为500
      }
    },
    closePopover() {
      this.visible = false; // 手动关闭弹出层
    },
    openCommandCenter() {
      if(this.isReadOnly) return // 如果只读状态则不打开指令中心
      if(this.disabledStatus) return false
      this.commandCenterVisible = true; // 打开指令中心
      this.fetchTags();
    },
    async fetchCommands() {
      try {
        const response = await getRefpromptsList({ scheme_id: this.$route.query.id });
        if (response.status === 200 && response.data.code === 200) {
          const commandList = response.data.result ? response.data.result : [];
          this.commands = commandList.map((command) => ({
            title: command.title,
            content: command.content
          }));
        } else {
          console.error(response.data?.msg || 'getRefpromptsList 接口异常!');
        }
      } catch (error) {
        console.error('fetchCommands error:', error);
      }
    },
    async fetchTags() {
      try {
        const response = await queryRefpromptUseTags({ scheme_id: this.$route.query.id });
        if (response.status === 200) {
          const tagList = response.data ? response.data : [];
          this.localTags = tagList;
          this.$nextTick(() => {
            console.log('this.$refs.conditionTag2', this.$refs.conditionTag2);
            this.$refs.conditionTag2.observeResize();
          });
        } else {
          console.error('queryRefpromptUseTags 接口异常!');
        }
      } catch (error) {
        console.error('queryRefpromptUseTags error:', error);
      }
    },

    selectTag(tag) {
      this.$emit('tag-selected-change', tag.id);
    },
    onCommandClick(command) {
      this.$root.$emit('command-selected', command.content); // 发送事件
      this.commandCenterVisible = false; // 关闭指令中心
    },
    toggleSearch(event) {
      event.stopPropagation(); // 阻止事件冒泡
      this.showSearch = true; // 显示搜索框
      this.$nextTick(() => {
        this.$refs.searchInputRef.focus(); // 聚焦搜索框
        this.$refs.searchInputRef.$el.classList.add('slide-in-right');
      });
    },
    resetSearch() {
      this.showSearch = false; // 隐藏搜索框，显示新建指令按钮
      this.searchQuery = ''; // 清空搜索框内容

      if (this.$refs.searchInputRef) {
        this.$refs.searchInputRef.$el.classList.remove('slide-in-right');
      }
    },
    isOverflowing(el) {
      return el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth;
    },
    adjustTabScroll(tab) {
      const tabsContainer = this.$refs.tabs1.$el.querySelector('.el-tabs__nav-wrap');
      const tabElement = this.$refs.tabs1.$el.querySelector(`.el-tabs__item.is-active`);
      const containerWidth = tabsContainer.clientWidth;
      const tabLeft = tabElement.offsetLeft;
      const tabWidth = tabElement.clientWidth;

      // 获取当前的 translateX 值
      const transform = window.getComputedStyle(
        tabsContainer.querySelector('.el-tabs__nav')
      ).transform;
      const matrix = new WebKitCSSMatrix(transform);
      const translateX = matrix.m41;

      if (tabLeft + tabWidth > containerWidth - translateX) {
        tabsContainer.querySelector('.el-tabs__nav').style.transform = `translateX(${
          translateX - (tabLeft + tabWidth - containerWidth)
        }px)`;
      } else if (tabLeft < -translateX) {
        tabsContainer.querySelector('.el-tabs__nav').style.transform = `translateX(${
          translateX + tabLeft
        }px)`;
      }
    },
    handleOk(formRef) {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            const response = await (this.formValue.id
              ? updateRefprompts(this.formValue)
              : createRefprompts({
                  ...this.formValue,
                  tags: ['-1']
                }));
            await bindTag({
              tag_ids: ['-1'],
              biz_id: response.data.id
            })
            if (response.status === 200) {
              this.$message({
                type: 'success',
                message: '保存成功!'
              });
              this.zsShow = false;
              this.$emit('tag-update',this.selectedTagId)
            } else {
              console.error(response.data?.msg || 'getRefpromptsList 接口异常!');
            }
          } catch (error) {
            console.error('fetchCommands error:', error);
          }
          this.$message({
            type: 'success',
            message: '提交成功!'
          });
          this.zsShow = false;
        }
      });
    },
    editCommand(command) {
      this.formValue = {
        id: command.id,
        title: command.title,
        content: command.content
      };
      this.zsShow = true;
    },
    async deleteCommand(command) {
      try {
        await this.$confirm('确认删除该指令?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const response = await deleteRefprompts(command.id);
        if (response.status === 200) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.$emit('tag-update', this.selectedTagId);
        } else {
          this.$message({
            type: 'error',
            message: '删除失败!'
          });
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除指令失败:', error);

          this.$message({
            type: 'error',
            message: '操作失败，请稍后重试!'
          });
        }
      }
    },
    handleDialogClosed() {
      // 清除校验状态
      this.$refs.formRef?.clearValidate()
      // 重置表单数据（如果需要）
      this.formValue = {}
    }
  },
  computed: {
    visibleTags() {
      return this.tags.slice(this.visibleStartIndex, this.visibleStartIndex + this.maxVisibleTags);
    },
    hasHiddenTags() {
      return this.tags.length > this.maxVisibleTags + this.visibleStartIndex;
    },
    hiddenTags() {
      return this.tags.slice(this.visibleStartIndex + this.maxVisibleTags);
    },
    titleOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.titleRef[index];
        return this.isOverflowing(el);
      });
    },
    contentOverflow() {
      return this.commands.map((command, index) => {
        const el = this.$refs.contentRef[index];
        return this.isOverflowing(el);
      });
    },
    filteredCommands() {
      return this.commands.filter(
        (command) =>
          command.title.includes(this.searchQuery) || command.content.includes(this.searchQuery)
      );
    }
  }
};
</script>

<style lang="postcss" scoped>
.cust-disabled {
 cursor: not-allowed !important;
 opacity: 0.6;
}
.popWrap {
  display: flex;
  justify-content: center;
  align-items: center;
}
.popWrapFont{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #4068D4;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
.tabs-container {
  display: inline-block;
  width: 300px; /* Adjust based on your layout */
  overflow: hidden;
}

.tabs-container .el-tabs__item {
  border: 2px solid #007bff;
  border-radius: 50px; /* This creates the rounded ends */
  margin: 5px;
  background-color: white;
  color: #007bff;
  cursor: pointer;
  height: 30px;
  line-height: 26px;
  transition: background-color 0.3s, color 0.3s;
}

.tabs-container .el-tabs__item.is-active {
  background-color: #007bff !important;
  color: white !important;
}

.tabs-container .el-tabs__item:hover {
  background-color: #007bff;
  color: white;
}
.tabs-container .el-tabs__active-bar {
  display: none !important;
}

.custom-popover {
  padding: 0px;
  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px 20px;
    border-bottom: 1px solid #ebeef5;
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }
    .close-button {
      border: none !important;
      background-color: unset !important;
    }
  }

  .popover-content {
    padding: 17px 20px;
    max-height: 450px;
    overflow-y: auto;
  }

  .card-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 三列布局 */
    gap: 10px;
  }

  .card-item {
    height: 110px;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 8px; /* 调整圆角 */
    cursor: pointer;
    transition: box-shadow 0.3s, transform 0.3s, border-color 0.3s; /* 添加平滑过渡效果 */
    background: #ffffff; /* 设置背景色为白色 */
    color: #333; /* 设置字体颜色为深灰色 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); /* 添加阴影 */
  }

  .card-item:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1); /* 增强悬停时的阴影效果 */
    transform: translateY(-5px); /* 悬停时向上移动 */
    border-color: #0061ff;
  }

  .card-item h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
  }

  .card-item p {
    margin: 5px 0 0;
    font-size: 12px;
    color: #666; /* 设置描述文字颜色为中灰色 */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.drawer-wrapper {
  margin: 24px;
}
:deep(.el-drawer__header){
  margin-bottom: 0;
}
:deep(.el-drawer__body){
  padding-bottom: 20px;
}
.el-drawer__header:first-child {
  padding: 10px 20px; /* 调整header的padding */
  font-size: 16px; /* 调整header的字体大小 */
  border-bottom: 1px solid #ebeef5; /* 添加底部边框 */
  margin-bottom: 0;
}

.el-drawer__header > :first-child {
  font-weight: 600 !important; /* 调整header的字体粗细 */
}

.el-drawer {
  height: 100vh; /* 确保抽屉的高度是视口高度 */
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%; /* 使 drawer-content 撑满上级容器 */
  padding: 0;
  gap: 10px;
  width: 100%;
  overflow-x: hidden; /* 禁止横向滚动 */
}

/* 新增命令卡片容器样式 */
.commands-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  margin-right: -8px; /* 为滚动条预留空间 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #dcdfe6 #f5f7fa; /* Firefox */
  overflow-x: hidden; /* 禁止横向滚动 */
}

/* Webkit浏览器的滚动条样式 */
.commands-wrapper::-webkit-scrollbar {
  width: 6px;
}

.commands-wrapper::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.commands-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
.search-create-container {
  margin-top: 10px; /* 减小与卡片列表的间距 */
  flex-shrink: 0;
  margin-bottom: 0px; /* 减小与标签列表的间距 */
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 0 20px;
}

.command-card {
  padding: 10px;
  width: 100%; /* 确保卡片宽度不会超出父容器 */
  max-width: 100%; /* 确保卡片宽度不会超出父容器 */
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #f3f2ff;
  transition: box-shadow 0.3s, transform 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 15px;
  margin-top: 10px;
}

.command-card:hover {
  border: 1px solid #323ff0;
  cursor: pointer;
}

.command-card h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  /* display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis; */
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif'; /* 设置更好的字体 */
}

.command-card p {
  margin: 5px 0 0;
  font-size: 14px;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif'; /* 设置更好的字体 */
  color: #585a73;
  /* display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis; */
  min-height: 20px; /* 设置最小高度为一行字的高度 */
   
}

.rainbow-text-button {
  background: #f2f3f5;
  border-radius: 12px !important;
}

.search-input {
  flex: 1; /* 使搜索框占据剩余空间 */
  opacity: 1; /* 初始状态为可见 */
  transform: translateX(0); /* 初始状态为正常位置 */
  transition: opacity 0.3s ease-out, transform 0.3s ease-out; /* 添加过渡效果 */
}

.search-icon {
  color: #909399; /* 设置放大镜图标颜色 */
  font-size: 16px; /* 设置放大镜图标大小 */
  line-height: 2; /* 确保图标与文本的行高一致 */
  vertical-align: middle; /* 垂直居中对齐 */
}

.create-button {
  flex: 2; /* 使新建按钮占据更多空间 */
  border-radius: 12px !important;
  background-color: #fff !important;
  border: 1px solid #dcdfe6 !important;
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.create-button:hover {
  background-color: #f5f7fa !important;
  border-color: #409eff !important;
  color: #409eff !important;
}

.toggle-button {
  margin-bottom: 0; /* 重置切换按钮的下边距 */
  border: 1px solid #dcdfe6 !important;
  border-radius: 15px !important;
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.toggle-button:hover {
  background-color: #f5f7fa !important;
  border-color: #409eff !important;
  color: #409eff !important;
}

@keyframes shimmer {
  0% {
    left: -150%; /* 起点 */
  }
  50% {
    left: 50%; /* 光效经过按钮中间 */
  }
  100% {
    left: 150%; /* 光效消失在按钮右侧 */
  }
}
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
.slide-in-right {
  animation: slideInFromRight 0.3s ease-out forwards; /* 添加 forwards 保留最后一帧 */
}
.tag-list {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px; /* 减小与指令卡片的间距 */
  padding: 0 20px;
  overflow: hidden;
  white-space: nowrap;
}

.tags-container {
  display: flex;
  gap: 4px; /* 设置标签之间的间距 */
  flex: 1; /* 让标签容器占据剩余空间 */
}

.tag-item {
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  white-space: nowrap; /* 防止标签文字换行 */
  flex-shrink: 0; /* 防止标签被挤压 */
}

.tag-item:hover {
  background-color: #409eff;
  color: white;
}

.more-tags {
  background-color: #f4f4f5;
  color: #909399;
  white-space: nowrap; /* 防止"更多"按钮文字换行 */
  flex-shrink: 0; /* 防止"更多"按钮被挤压 */
}

.more-tags:hover {
  background-color: #409eff;
  color: white;
}

.tag-selected {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.el-dropdown-menu__item.tag-selected {
  color: #409eff !important;
  background-color: #ecf5ff !important;
}

.card-content {
  flex: 1;
  margin-right: 10px;
  cursor: pointer;
}
.card-content p {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}
.card-content h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', 'Arial', 'sans-serif';
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.icons-wrapper {
  display: flex;
  gap: 8px; /* 设置图标之间的间距 */
  align-items: center;
}

.icons-wrapper i {
  cursor: pointer;
  transition: color 0.3s;
}

.icons-wrapper i:hover {
  color: #409eff;
}
.chuangzuo {
  margin-left: 8px;
  margin-bottom: 9px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 29px;
  width: 84px;
  border-radius: 6px;
  border: 1px solid #e5e9f5;
  cursor: pointer;
  &:hover{
      /* background: rgba(64, 107, 212, 0.40); */
      background-color: #ebecf0;
  }
}
</style>
