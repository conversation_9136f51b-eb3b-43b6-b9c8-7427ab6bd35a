<template>
    <div class="flex">
        <div class="flex_info" v-show="!isFullFlat && !isFullFlatView" >
            <div class="input_flex">
                <div class="flex_mor">
                    <div :class="mor_ac ? 'flex_mor_ac' :''" @click="change(true)">所有记忆({{ biz1_total }})</div>
                    <div :class="!mor_ac ? 'flex_mor_ac' :''" @click="change(false)">选中记忆({{ biz2_total }})</div>
                </div>
                <el-input placeholder="请输入内容" :disabled="isReadOnly" v-model="keywords" class="input-with-select" clearable>
                    <el-button :disabled="isReadOnly" slot="append" icon="el-icon-search" @click="queryfun"></el-button>
                </el-input>
            </div>
            <div v-if="dataAll?.length > 0"
                :class="[index === activeIndex ? 'info activeIndexClassboder' : 'info', (item.usage_status && mor_ac) ? 'activeIndexClassbg' : '']"
                v-for="(item, index) in dataAll" :key="index" @click="(e) => {acFun(e,index, item)}"
                @mouseenter="item.showImage = true; activeIndex = index"
                @mouseleave="item.showImage = false; activeIndex = ''">
                <div class="check_flex" style="overflow: hidden;">
                    <el-checkbox :value="item.usage_status" class="check_div" v-if="mor_ac" @click="(e) => {e.stopPropagation()}"></el-checkbox>
                    <div class="sort_class" v-if="!mor_ac">{{ index + 1 }}</div>
                    <v-md-editor :mode="item.isEdit ? 'editable' : 'preview'" v-model="item.content"  :height="item.full_status ? 'auto' : '104px'"
                    left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link  code"
                    right-toolbar="preview"></v-md-editor>
                </div>
                <div class="qh_class" @click="(e)=>{qhchange(e,index)}">{{item.full_status ? '收起':'全文'}}</div>
                <div class="opClass" v-show="item.showImage && !isReadOnly" @click="(e) => { e.stopPropagation() }">
                    <img @click="fullviewFun(item)" style="margin-right: 5px;" src="@/assets/images/planGenerater/full.png"  width="14" height="14" />
                    <i  class="el-icon-edit color " style="margin-right: 5px;"
                        @click="(e) => { edit(e, index, item) }"></i>
                    <i class="el-icon-delete color" style="margin-right: 10px" @click="(e) =>{del(e,item)}"></i>
                </div>
            </div>
            <InfiniteLoading  ref="infiniteLoading" @infinite="loadMore" :infinite-scroll-disabled="isInitialLoading"
            spinner="waveDots">
                <span slot="no-more">没有更多数据</span>
                <span slot="no-results">暂无数据</span>
            </InfiniteLoading>
        </div>
        <div v-show="isFullFlat" style="position: relative;    overflow: auto;width: 100%;">
            <div class="tools_info">
                <div class="tools_info_div" style="margin-right: 5px;">
                    <i class="el-icon-check" @click="updateData(isFullData)"></i>
                </div>
                <div class="tools_info_div" style="margin-right: 5px;">
                    <i class="el-icon-error" @click="isFullFlat = !isFullFlat"></i>
                </div>
            </div>
            <v-md-editor :mode="isFullFlat ? 'editable' : 'preview'" v-model="isFullData.content"
                :height="'calc(100vh - 220px)'"
                left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link  code"
                right-toolbar="preview"></v-md-editor>
        </div>
        <div  v-show="isFullFlatView" style="position: relative;    overflow: auto;width: 100%;">
            <div class="tools_info_full">
                <div class="tools_full" style="margin-right: 5px;">
                    <img @click="isFullFlatView = !isFullFlatView"  src="@/assets/images/planGenerater/tuichuquanping.png"  width="14" height="14" />
                </div>
            </div>
            <v-md-editor mode="preview" v-model="isFullData.content" style="margin-top: 40px;"
                height="calc(100vh - 320px)"
                left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link  code"
                right-toolbar="preview"></v-md-editor>
        </div>
    </div>
</template>
<script>
import {
    scheme_memory,
    scheme_memory_del,
    scheme_memory_update,
    update_usage_status
} from '@/api/planGenerateApi.js'
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import 'katex/dist/katex.min.css'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn'
import '@kangc/v-md-editor/lib/plugins/copy-code/copy-code.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-python'
import _ from 'lodash';
import InfiniteLoading from 'vue-infinite-loading';
VMdEditor.use(vuepressTheme, {
    Prism,
    extend(md) {
        console.log('ddd', md)
        // md为 markdown-it 实例，可以在此处进行修改配置,并使用 plugin 进行语法扩展
        // md.set(option).use(plugin);
    }
})

VMdEditor.use(createMermaidPlugin())
export default {
    components: {
        VMdEditor,
        InfiniteLoading
    },
    props: {
 isReadOnly: {
   type: Boolean,
   default: false
 },
},
    data() {
        return {
            processingClick: false,
            isFullFlatView:false,
            PageData: {
                page_num: 1,
                page_size: 10,
                total: 0,
            },
            keywords: '',
            activeIndex: '',
            mdContentText: '',
            dataAll: [],
            isFullFlat: false,
            isFullData: {},
            biz1_total:0,
            biz2_total:0,
            isInitialLoading: true,
            mor_ac: true
        }
    },
    mounted() {
        this.loadMore()
    },
    methods: {
        setMor_ac(){
            this.mor_ac = true
        },
        qhchange(e,index){
            e.stopPropagation()
            this.dataAll.forEach((item,vt)=>{
                if(vt == index){
                    item.full_status = !item.full_status
                }
            })
        },  
        change(val){
            this.mor_ac = val
            this.dataAll = []
            this.PageData.page_num = 1
            this.$refs.infiniteLoading.stateChanger.reset();
        },
        fullviewFun(val){
            this.isFullFlatView = true
            this.isFullData = val
        },
        async updateData(item) {
            const res = await scheme_memory_update({
                "content": item.content,
                "id": item.id,
            })
            if (res.status == 200) {
                // this.queryfun()
                this.$message.success('编辑成功')
            } else {
                this.$message.error('编辑失败')
            }
        },
        queryfun() {
            this.dataAll = []
            this.PageData.page_num =1
            this.$refs.infiniteLoading.stateChanger.reset();
        },
        async acFun(e,index, item) {
            if (this.processingClick) return; // 如果正在处理点击，则直接返回
            this.processingClick = true; // 标记为正在处理点击
            e.stopPropagation()
            this.activeIndex = index
            const res = await update_usage_status([{
                usage_status: !item.usage_status,
                id: item.id
            }])
            this.processingClick = false;
            if (res.status == 200) {
                this.dataAll = []
                this.PageData.page_num = 1
                this.$refs.infiniteLoading.stateChanger.reset();
                this.$message.success(!item.usage_status?  '记忆已启用' : '记忆已停用')
            } else {
                this.$message.error('更新状态失败')
            }
        },
        async del(e,item) {
            e.stopPropagation()
            const res = await scheme_memory_del(item.id)
            if (res.status == 200) {
                this.$message.success('删除成功')
                this.queryfun()
            } else {
                this.$message.error('删除失败')
            }
        },
        edit(e, index, item) {
            e.stopPropagation()
            this.isFullFlat = true
            this.isFullData = item
        },
        async loadMore($state) {
            try {
                const res = await scheme_memory({
                    "page": this.PageData.page_num,
                    "page_size": 10,
                    "scheme_id": this.$route.query.id,
                    "usage_status": this.mor_ac ? undefined : true,
                    "keywords": this.keywords
                })
                this.biz1_total = res.data.biz1_total
                this.biz2_total = res.data.biz2_total
                if (res.data.data.length) {
                // 如果有新数据，将其添加到 items 数组中
                let newItems= res.data.data.map(it => {
                    return {
                        ...it,
                        showImage: false,
                        isEdit: false,
                        full_status:false
                    }
                })
                if (this.isInitialLoading) {
                    this.isInitialLoading = false;
                }
                this.dataAll.push(...newItems);
                this.PageData.page_num++;
                $state.loaded();  // 告诉 InfiniteLoading 组件数据已加载
                } else {
                // 如果没有更多数据，标记为完成
                $state.complete();
                }
            } catch (error) {
                // 处理错误
                $state.error();
            }
        }
    }
}
</script>
<style lang="postcss" scoped>
.flex {
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 20px 20px 70px 20px;
    position: relative;
    height: auto;
}

.flex_info {
    width: 100%;
    height: auto;
    background-color: #ffffff;
    overflow-y: auto;

}

.input-with-select {
    width: 200px;
}

.input_flex {
    display: flex;
    width: 100%;
    justify-content:space-between;
    align-items: center;
    height: 57px;
}

.info {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 20px;
    position: relative;
    box-shadow: 0px 1px 10px 1px rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    border: 1px solid #DCDDE0;
    cursor: pointer;
}

.opClass {
    position: absolute;
    width: 70px;
    height: 30px;
    right: 0px;
    z-index: 11111;
    top: 5px;
    background: #ccc;
    padding: 5px;
    display: flex;
    align-items: center;
}

.activeIndexClassbg {
    background: #EFF3FF;
}

.activeIndexClassboder {
    border: 1px solid #4068D4;
}

.editor-container {
    position: relative;
}

.resize-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 10px;
    cursor: ns-resize;
    background-color: #ddd;
}

.fullFalse {
    background: #e8e8e8;
    width: 16px;
    height: 16px;
}

.tools_info {
    position: absolute;
    right: 4px;
    top: 6px;
    display: flex;
    align-items: center;
}

.tools_info_div {
    background: #e8e8e8;
    width: 28px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.tools_info_full{
    position: absolute;
    right: 0px;
    top: 6px;
    display: flex;
    align-items: center; 
}
.color{
    color: #7591db;
}
.tools_full{
    background: #4068d4;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
    cursor: pointer;
}
.flex_mor{
    display: flex;
    align-items: center;
    height: 30px;
    div{
        background: #F2F3F5;
        border-radius: 0px 2px 2px 0px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #406BD4;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        cursor: pointer;
    }
}
.flex_mor_ac{
    background: #4068D4!important;
    color: #FFFFFF!important;
}
.check_flex{
    display: flex;
    align-items: center;
}
.check_div{
    margin-right: 16px;
}
.qh_class{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #406BD4;
    text-align: left;
    font-style: normal;
    margin-left: 30px;
    margin-top: 5px;
    cursor: pointer;
}
.sort_class{
    width: 25px;
    height: 25px;
    background: #4068D4;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    text-align: right;
    font-style: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 6px;
}
:deep(.check_flex .scrollbar__view){
    overflow: hidden;
}
.flex :deep(.v-md-icon-preview){
    margin-right: 70px;
}
</style>
<style>
.v-md-textarea-editor pre, .v-md-textarea-editor textarea {
    overflow: auto !important;
}
</style>