<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide
        ? 'chatContainerTest chatContainerTestFrame'
        : 'chatContainerTest'
    "
  >
    <div
      :class="
        $store.state.planGenerate.isIframeHide
          ? 'containerBox2 containerBox2IFrame'
          : 'containerBox2'
      "
      style="width: 100%"
    >
      <div
        id="left-content"
        v-loading="planLoading"
        element-loading-text="方案生成中..."
        element-loading-spinner="el-icon-loading"
        :style="{
          width: leftWidth,
          maxWidth: leftWidth,
          marginRight: !rightFullFlag ? '0px' : '16px',
          userSelect: isDragging ? 'none' : 'auto',
          transition: isDragging ? 'none' : 'width 0.2s',
          position: thinkFullFlag ? '' : 'relative'
        }"
        :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'"
      >
        <div class="optContent">
          <div class="optHeader">
            <div class="rightTitle">方案明细</div>
            <div class="rightTitleOpt">
              <el-tooltip v-if="!isEdit" class="item" effect="dark" content="编辑" placement="top">
                <el-button
                  type="info"
                  :disabled="hasChatingName !== ''"
                  size="mini"
                  @click="
                    () => {
                      hisDetail = detailContent.text;
                      isEdit = true;
                    }
                  "
                  ><img src="@/assets/images/planGenerater/bianji.png"
                /></el-button>
              </el-tooltip>
              <template v-if="isEdit">
                <el-tooltip class="item" effect="dark" content="保存" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSave"
                    ><img src="@/assets/images/planGenerater/baocun.png"
                  /></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="取消" placement="top">
                  <el-button type="info" size="mini" @click="handleDetailSaveClose"
                    ><img src="@/assets/images/planGenerater/quxiao.png"
                  /></el-button>
                </el-tooltip>
              </template>
              <el-tooltip
                class="item"
                effect="dark"
                :content="rightFullFlag ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="rightFullFlag ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowFull"
                  ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                /></el-button>
              </el-tooltip>
              <el-dropdown
                :disabled="systemMessages === 'scheme generating' || hasChatingName !== ''"
                style="margin-left: 10px"
                @command="handleSchemeCommand"
              >
                <el-button type="info" size="mini"
                  ><img src="@/assets/images/planGenerater/more.png"
                /></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
                  <el-dropdown-item command="history">历史记录</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div :class="miniFlag ? 'optScroll optScrollMini' : 'optScroll'">
            <div class="optContentBox customMd" @mouseenter="fangda">
              <MyEditor
                id="MyEditor2"
                ref="MyEditor2"
                :md-content="detailContent.text"
                :is-edit="isEdit"
                @updateContent="handleUpdateContent"
              ></MyEditor>
            </div>
            <el-alert v-if="hasChatingName" :closable="false" type="error">
              <span slot="title">正在与【{{ hasChatingName }}】对话中</span>
            </el-alert>
            <el-alert v-if="agentError" :closable="false" type="error">
              <span slot="title"
                >人机对话连接失败请刷新后再进行对话 &nbsp; &nbsp;<el-link
                  type="error"
                  text
                  :style="{ verticalAlign: 'baseline' }"
                  @click="refresh"
                  >刷新</el-link
                ></span
              >
            </el-alert>
          </div>

          <div class="chatFooter">
            <!-- systemMessages === 'process running' || systemMessages === 'process stream' || systemMessages === 'process_stream_message' -->
            <div id="myInputText" class="chatInput">
              <el-input
                v-if="speakingFlag === 'running' || shibieLoading || disfangan"
                ref="myChatInputText"
                v-model.trim="yuyinText"
                clearable
                readonly
                type="textarea"
                resize="none"
                :autosize="{ minRows: 3, maxRows: 3 }"
                :disabled="
                  [
                    'process stream',
                    'process stream running',
                    'process running',
                    'processing',
                    'scheme generating',
                    'clear_history',
                    'process_stream_message'
                  ].indexOf(systemMessages) > -1 || hasChatingName !== ''
                "
                style="width: 100%"
                placeholder=""
              >
              </el-input>
              <el-input
                v-else
                ref="myChatInputText"
                v-model.trim="currentText"
                clearable
                type="textarea"
                resize="none"
                :autosize="{ minRows: 3, maxRows: 3 }"
                :disabled="
                  [
                    'process stream',
                    'process stream running',
                    'process running',
                    'processing',
                    'scheme generating',
                    'clear_history',
                    'process_stream_message'
                  ].indexOf(systemMessages) > -1 ||
                  hasChatingName !== '' ||
                  disfangan
                "
                style="width: 100%"
                placeholder="请输入您的问题/调整方向，系统将根据您的描述重新生成方案内容"
                @keydown.native="carriageReturn($event)"
              >
              </el-input>
            </div>
            <div class="send-btn">
              <div
                :class="
                  [
                    'process stream',
                    'process stream running',
                    'process running',
                    'processing',
                    'scheme generating',
                    'clear_history',
                    'process_stream_message'
                  ].indexOf(systemMessages) > -1 ||
                  hasChatingName !== '' ||
                  disfangan ||
                  shibieLoading
                    ? 'yuyinBtn yuyinBtnDisabled'
                    : 'yuyinBtn'
                "
              >
                <img
                  v-if="speakingFlag === 'start'"
                  src="@/assets/images/planGenerater/shuohua.png"
                  @click="startRecording"
                />
                <img
                  v-if="speakingFlag === 'running'"
                  src="@/assets/images/planGenerater/yuyin.gif"
                  @click="stopRecording"
                />
                <img
                  v-if="speakingFlag === 'end'"
                  src="@/assets/images/planGenerater/zhuanhuan-loading.gif"
                />
              </div>
              <el-button
                type="info"
                :disabled="
                  [
                    'process stream',
                    'process running',
                    'process stream running',
                    'processing',
                    'scheme generating',
                    'process_stream_message',
                    'process stream running',
                    'clear_history'
                  ].indexOf(systemMessages) > -1 ||
                  hasChatingName !== '' ||
                  currentText === '' ||
                  speakingFlag === 'running' ||
                  disfangan ||
                  shibieLoading
                "
                @click="sendMessage"
                >生成方案</el-button
              >
            </div>
          </div>
          <treeProcess
            :is-visible="processVisable"
            :tree-process-val="optionDataProcess"
            @close="closeSikaoRizhi"
          />
          <historyProcess
            :is-visible="historyVisable"
            :history-data="historyData"
            @updateData="historyDataFn"
            @close="closeHistory"
          />
        </div>
      </div>
      <div
        v-if="planDetailShow && !rightFullFlag"
        id="resize"
        class="resize"
        title="收缩侧边栏"
        @mousedown="startDrag"
      >
        <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
        <div class="el-two-column__trigger-icon">
          <SvgIcon name="dragborder" class="process-icon" />
        </div>
        <div class="el-two-column__icon-bottom">
          <div class="el-two-column__icon-bottom-bar"></div>
        </div>
      </div>
      <div
        id="right-content"
        :style="{
          width: rightWidth,
          marginRight: '16px',
          transition: isDragging ? 'none' : 'width 0.2s',
          userSelect: isDragging ? 'none' : 'auto'
        }"
        :class="miniFlag ? 'chatRight chatRightMini' : 'chatRight'"
      >
        <div
          id="bottom-content"
          :style="{
            height: bottomHeight,
            transition: isDragging ? 'none' : 'width 0.2s',
            userSelect: isDragging ? 'none' : 'auto'
          }"
          :class="!planDetailBottomShow ? 'optContent chatRightFull' : 'optContent'"
        >
          <div class="optHeader">
            <div class="rightTitle">思维树</div>
            <div class="rightTitleOpt">
              <!-- <el-button
                type="info"
                size="mini"
                :disabled="
                  hasChatingName !== '' ||
                  planLoading ||
                  disTree ||
                  disfangan ||
                  [
                    'process stream',
                    'process running',
                    'process stream running',
                    'processing',
                    'scheme generating',
                    'process_stream_message',
                    'process stream running',
                    'clear_history'
                  ].indexOf(systemMessages) > -1
                "
                @click="regenerate('resetTree')"
                >生成思维树</el-button
              > -->
              <el-tooltip
                class="item"
                effect="dark"
                :content="jueceYulanFlag ? '脑图' : '文本模式'"
                placement="top"
              >
                <el-button type="info" size="mini" @click="changeShowType"
                  ><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/naotu.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/markdown.png"
                /></el-button>
              </el-tooltip>
              <el-tooltip
                v-if="!rightFullFlag"
                class="item"
                effect="dark"
                :content="!planDetailShow ? '退出全屏' : '全屏'"
                placement="top"
              >
                <el-button
                  :type="!planDetailShow ? 'primary' : 'info'"
                  size="mini"
                  @click="changeShowBottomRight"
                >
                  <img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img
                    v-else
                    src="@/assets/images/planGenerater/tuichuquanping.png"
                  />
                </el-button>
              </el-tooltip>
              <el-dropdown style="margin-left: 10px" @command="handleCommand">
                <el-button type="info" :disabled="hasChatingName !== ''" size="mini"
                  ><img src="@/assets/images/planGenerater/more.png"
                /></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :disabled="isCacheDisabled || treeStatus == 1 || treeStatus == 0"
                    :command="isCache ? 'removeCache' : 'addCache'"
                    >{{ isCache ? '取消缓存' : '缓存' }}</el-dropdown-item
                  >
                  <el-dropdown-item :disabled="hasChatingName !== ''" command="sikao"
                    >生成过程</el-dropdown-item
                  >
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div
            v-loading="taskLoading"
            class="bottomScroll"
            element-loading-text="思维树生成中..."
            element-loading-spinner="el-icon-loading"
          >
            <el-dialog
              :visible.sync="dialogTableVisible"
              :modal-append-to-body="false"
              title="数据来源配置"
              :before-close="handleClose"
            >
              <el-table
                v-show="dataStatus !== 'failed'"
                v-loading="dataStatus === 'generating'"
                element-loading-text="生成中..."
                element-loading-spinner="el-icon-loading"
                custom-class="el-loading-spinner2"
                class="transition-box"
                :data="gridData"
                max-height="400"
              >
                <el-table-column
                  v-for="item in dataThDatas"
                  :key="item.field"
                  :prop="item.field"
                  :label="item.name"
                >
                  <template #default="scope">
                    <div v-if="item.field === 'param_key' || item.field === 'param_name'">
                      <el-input
                        v-model.trim="scope.row[item.field]"
                        placeholder="请输入内容"
                      ></el-input>
                    </div>
                    <div v-else>{{ scope.row[item.field] }}</div>
                  </template>
                </el-table-column>
              </el-table>
              <div
                v-if="dataStatus === 'failed'"
                style="margin-top: 20px; width: 100%; height: 100%"
              >
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    width: 100%;
                  "
                >
                  <img
                    src="@/assets/images/planGenerater/runerror.png"
                    style="width: 180px; height: auto"
                  />
                  <div
                    style="
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      justify-content: center;
                      margin-top: 16px;
                    "
                  >
                    数据抽取失败，请<el-link
                      style="color: #4068d4"
                      :underline="false"
                      :disabled="hasChatingName !== ''"
                      @click="regenerate('resetTree')"
                      >重试</el-link
                    >
                  </div>
                </div>
                <!-- <el-result icon="error" title="数据抽取失败！"><el-link :underline="false" @click="regenerate()">重试</el-link></el-result> -->
              </div>
              <div
                style="
                  margin-top: 15px;
                  width: 100%;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                "
              >
                <el-button
                  v-if="dataThDatas.length"
                  type="primary"
                  :disabled="saveLoading"
                  @click="handleDone"
                  >完成</el-button
                >
              </div>
            </el-dialog>
            <div v-if="treeStatus == 1 || treeStatus == 0" class="optContentBox">
              <vue-markdown :source="treeData"></vue-markdown>
            </div>
            <div v-else-if="treeStatus == 3" style="width: 100%; height: 100%">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100%;
                  width: 100%;
                "
              >
                <img
                  src="@/assets/images/planGenerater/runerror.png"
                  style="width: 180px; height: auto"
                />
                <div
                  style="
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    margin-top: 16px;
                  "
                >
                  思维树生成失败，请<el-link
                    style="color: #4068d4"
                    :underline="false"
                    :disabled="hasChatingName !== ''"
                    @click="regenerate('resetTree')"
                    >重试</el-link
                  >
                </div>
              </div>
              <!-- <el-alert :closable="false" type="error">
                    <span slot="title">思维树生成失败<el-link :underline="false" @click="regenerate()">重试</el-link></span>
                  </el-alert> -->
            </div>
            <div v-else class="optContentBox" style="width: 100%; height: 100%">
              <div
                v-if="jueceYulanFlag"
                class="optContentBox"
                @mouseenter="fangda"
                style="width: 100%; height: 100%"
              >
                <!-- <MyEditorPreview
                  id="MyEditor4"
                  ref="MyEditor4"
                  :md-content="treeData"
                ></MyEditorPreview> -->
                <svg id="markmap" class="mark-map"></svg>
              </div>
              <div v-else>
                <pre>{{ treeData }}</pre>
              </div>
            </div>
            <el-link type="primary">{{ detailContent.file_url }}</el-link>
          </div>
        </div>
      </div>
    </div>
    <div class="optFooter">
      <!-- ‘查看任务进度’按钮初始状态不显示。用户点过下一步触发过任务后才显示。翻译过来就是第一个任务状态不是未执行就代表此按钮可以显示 -->
      <el-button
        v-if="!developFlag && runTaskStatus"
        class="button-last"
        :disabled="hasChatingName !== '' || nextLoading"
        type="info"
        @click="showTask()"
        >查看任务进度</el-button
      >
      <el-button
        v-if="!developFlag"
        class="button-last"
        type="primary"
        :disabled="hasChatingName !== '' || nextLoading || ploading || disTree"
        @click="changeViews(1)"
        >下一步3</el-button
      >
      <el-button
        v-if="developFlag"
        class="button-last"
        type="primary"
        :disabled="
          treeData === '' ||
          treeStatus === 1 ||
          hasChatingName !== '' ||
          nextLoading ||
          disTree ||
          disfangan
        "
        @click="changeViews(1)"
        >下一步</el-button
      >
      <!-- <el-button class="button-last" type="info" @click="handleComplete()">取消</el-button> -->
    </div>
    <treeProcess
      :is-visible="processTreeVisable"
      :tree-process-val="treeDataProcess"
      @close="closeSikaoRizhi"
    />
    <codeAnalysis
      ref="chatScrollBox2"
      :is-visible="codeAnalysisProcessStatus"
      :tree-process-val="codeAnalysisProcess"
      :tree-process-val-first="codeAnalysisProcessFirst"
      :title-val="'代码分析日志'"
      @close="handleNext"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  updateAbilityMapping,
  SchemeDetail,
  GetDecision,
  queryAbilityMapping,
  PlanTaskEdit,
  queryCache,
  addCache,
  removeCache,
  querySchemeDetailById,
  SchemeSaveKnow,
  updateByDeviceId,
  checkByDeviceId,
  saveSimpleSchemeGenerate,
  getTaskStatus
} from '@/api/planGenerateApi.js';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import treeProcess from '../treeProcess.vue';
import historyProcess from './historyProcess.vue';
import panzoom from 'panzoom';
import MyEditor from '../mdEditor.vue';
import MyEditorPreview from '../mdEditorPreview.vue';
import codeAnalysis from '../codeAnalysis.vue';
import Recorder from 'js-audio-recorder';
const parameter = {
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
};
export default {
  components: {
    treeProcess,
    MyEditor,
    MyEditorPreview,
    historyProcess,
    codeAnalysis
  },
  props: {
    agentSenceCode: {
      type: String,
      default: ''
    },
    planDataVal: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    planProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    planStatus: {
      type: Number,
      default: -1
    },
    hasChatingName: {
      type: String,
      default: ''
    },
    displayType: {
      type: Number,
      default: 1
    },
    miniFlag: {
      type: Boolean,
      default: false
    },
    agentError: {
      type: Boolean,
      default: false
    },
    developFlag: {
      type: Boolean,
      default: false
    },
    flowData: {
      type: Array,
      default() {
        return [];
      }
    },
    systemMessages: {
      type: String,
      default: ''
    },
    changeRunTaskStatus: {
      type: [Number, String],
      default: ''
    },
    codeAnalysisData: {
      type: String,
      default: ''
    },
    codeAnalysisDataStatus: {
      type: String | Number,
      default: ''
    },
    codeAnalysisProcessData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      iframeSrc: '',
      schemeInfo: {},
      optimizeData: '', // 方案优化流数据
      currentText: '',
      optionDataProcess: '',
      speakingFlag: 'start',
      yuyinText: '',
      shibieLoading: false,
      recorderEx: new Recorder(parameter),
      startY: '',
      startHeight: '',
      contentEditor: '',
      saveLoading: false,
      isSuperAdmin: false, // 是否超级管理员
      dialogTableVisible: false,
      gridData: [],
      tableLoading: false, // 加载状态
      detailContent: { text: '', file_url: '' },
      hisDetail: '',
      processContent: { text: '' },
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      timer: null,
      isDragging: false,
      leftWidth: '50%',
      topHeight: '65%',
      rightWidth: '',
      bottomHeight: '100%',
      totalWidth: 1000,
      totalHeight: 1000,
      isEdit: false,
      planDetailShow: true,
      planDetailTopShow: true,
      planDetailBottomShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskLoading: false,
      disfangan: false,
      deviceLoading: false,
      planLoading: false,
      writeFlag: true,
      writeText: '',
      treeData: '',
      treeDataProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      processTreeVisable: false, // 思维树生成过程弹窗标志
      panZoomRef: null,
      dataThDatas: [],
      dataStatus: '',
      checkNum: 0,
      isCache: false,
      isCacheDisabled: true,
      historyVisable: false,
      historyData: [],
      runTaskStatus: 0,
      env: {
        dev: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=5197&paperId=14649&sceneCode=1666686413389905975&editorMode=function-energy-topology&isFromIMP=true`,
        fat: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=396&paperId=925&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`,
        uat: '',
        production: `${process.env.VUE_APP_IMP_URL}/editorforlot/#/viewer?projectId=55&paperId=2053&sceneCode=131&editorMode=function-energy-topology&isFromIMP=true`
      },
      nextLoading: false,
      disTree: false,
      codeAnalysisProcessStatus: false,
      codeAnalysisProcess: '',
      codeAnalysisProcessFirst: '',
      ploading: false,
      reast: false,
      hasMarkmapInstance: false
    };
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    }),
    taskAllComplete() {
      const successTask = [];
      this.flowData.forEach((item) => {
        if (item.status === 1) {
          successTask.push(1);
        }
      });
      console.log('任务进度', successTask);
      return successTask.length === 7;
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成');
          this.taskLoading = false;
          this.disfangan = false;
          this.jueceYulanFlag = true;
          if(this.hasMarkmapInstance){
            this.hasMarkmapInstance = false
            return
          }
          this.handleRenderBrainMaps();
          setTimeout(() => {
            this.queryCacheHandle();
          }, 2000);
        } else if (val === 1) {
          this.taskLoading = true;
          this.disfangan = true;
        } else if (val === 0) {
          this.taskLoading = false;
          this.disfangan = true;
        } else if (val === 3) {
          // 生成失败
          this.taskLoading = false;
          this.disfangan = false;
        }
      },
      immediate: true
    },
    planStatus: {
      handler(val) {
        if (val === 0) {
          console.log('准备接收完方案信息');
          this.planLoading = true;
          this.disTree = true;
        } else if (val === 1) {
          this.disTree = true;
          this.planLoading = false;
        } else {
          if (val === 2) {
            // 输出完方案 触发任务流程
            this.$emit('handleUpdateScheme', this.detailContent.text);
            if (this.disTree) {
              this.regenerate('resetTree');
              this.disTree = false;
            }
          }
          console.log('方案信息接收', val);
          this.planLoading = false;
          if (Number(val) === 2) {
            this.disTree = false;
          }
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.treeData = val;
      },
      immediate: true
    },
    planDataVal: {
      handler(val) {
        this.detailContent.text = val;
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.treeDataProcess = val;
      },
      immediate: true
    },
    planProcessVal: {
      handler(val) {
        this.optionDataProcess = val;
      },
      immediate: true
    },
    changeRunTaskStatus: {
      handler(val) {
        console.log('222222222222');
        this.runTaskStatus = val;
      },
      immediate: true
    },
    codeAnalysisData: {
      handler(val) {
        console.log('代码分析流', val);
        this.codeAnalysisProcess = val;
      },
      immediate: true
    },
    codeAnalysisProcessData: {
      handler(val) {
        this.codeAnalysisProcessFirst = val;
      },
      immediate: true
    },
    codeAnalysisDataStatus: {
      handler(val) {
        console.log('代码分析状态状变化', val);
        if (Number(val) === 1) {
          this.ploading = false;
          this.codeAnalysisProcessStatus = true;
        } else {
          this.codeAnalysisProcessStatus = false;
        }
        if (Number(val) === 2 || Number(val) === 3) {
          this.ploading = false;
          this.handleNext();
        }
      },
      immediate: true
    }
  },
  async created() {},
  beforeDestroy() {
    clearInterval();
    clearInterval(this.timer);
    this.timer = null;
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    console.log('宽度', this.rightWidth, this.bottomHeight);
    const nodeMarkmap = document.getElementById('markmap');
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = '';
    }
    const path = this.env[process.env.VUE_APP_ENV];
    this.iframeSrc = this.getQueryToken(path);
    window.addEventListener('message', (e) => {
      if (e.data && e.data.type === 'IOTtoIMPClick') {
        console.log(e.data.deviceId, '外层传进来的设备id');
        this.updateDeviceId({ deviceId: e.data.deviceId, deviceName: e.data.deviceName });
      }
    });
    await this.initTaskStatus();
    await this.queryDecision();
    await this.queryPlanDetail();
  },
  methods: {
    handleNext() {
      console.log('下一步');
      this.codeAnalysisProcessStatus = false;
      this.$emit('updateStep', 1);
    },
    // url地址上的token参数
    getQueryToken(url) {
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    updateDeviceId(obj) {
      const params = {
        scheme_id: this.$route.query.id,
        device_id: obj.deviceId,
        device_name: obj.deviceName
      };
      updateByDeviceId(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          console.log('更新设备完成', res.data);
          this.$emit('updateDeviceId', res.data.result?.device_id);
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    // 回车发送消息
    carriageReturn(event) {
      console.log('oioio');
      // const e = window.event || arguments[0];
      if (event.key === 'Enter' && event.code === 'Enter' && event.keyCode == 13) {
        console.log('event1', event);
        if (!event.metaKey && !event.ctrlKey) {
          console.log('event2', event);
          event.preventDefault();
          this.$nextTick(() => {
            if (this.currentText) {
              this.sendMessage();
            }
          });
        } else {
          console.log('event3', event);
          if (event.ctrlKey || event.metaKey) {
            this.currentText += '\n';
            const target = document.getElementById('myInputText');
            if (target) {
              this.$nextTick(() => {
                target.scrollTop = target.scrollHeight + 50;
                // console.log('滚动下高度', target.scrollTop, target.scrollHeight);
              });
            }
          } else {
            event.preventDefault();
          }
        }
      }
      // 英文下｜中文下： 13 Enter Enter
      // 中文下有文字没进入输入框情况是：299 Enter Enter
      // if (e.key === 'Enter' && e.code === 'Enter' && e.keyCode === 13) {
      //   // console.log('this.text', this.currentText);
      //   this.$nextTick(() => {
      //     if (this.currentText) {
      //       this.sendMessage();
      //     }
      //   });
      // }
    },
    async sendMessage() {
      // 正在生成方案明细时，不能再次发送
      if (this.disTree) {
        this.$message({
          type: 'error',
          message: '正在生成方案明细，请稍后发送！'
        });
        return;
      }
      this.reast = true;
      // 将消息发送到服务器
      console.log('发送消息', this.currentText);
      this.planLoading = true;
      this.$emit('updateSendMsg', this.currentText);
      this.treeData = '';
      this.currentText = '';
      this.speakingFlag = 'start';
      this.yuyinText = '';
    },
    async startRecording() {
      if (
        [
          'process stream',
          'process stream running',
          'process running',
          'processing',
          'scheme generating',
          'clear_history',
          'process_stream_message'
        ].indexOf(this.systemMessages) > -1 ||
        this.hasChatingName !== '' ||
        this.shibieLoading
      ) {
        return false;
      } else {
        try {
          this.recorderEx.start();
          this.speakingFlag = 'running';
          this.yuyinText = '正在语音中...';
        } catch (err) {
          console.error('无法获取媒体流:', err);
          this.speakingFlag = 'start';
        }
      }
    },
    stopRecording() {
      this.speakingFlag = 'end';
      this.recorderEx.stop();
      setTimeout(() => {
        const wavBlob = this.recorderEx.getWAVBlob(); // blob格式
        console.log('this.audioChunks33', wavBlob);
        this.yuyinText = '正在转换文字中...';
        this.runExcute(wavBlob);
      });
    },
    async runExcute(audioBlob) {
      this.shibieLoading = true;
      this.currentText = '';
      const url = process.env.VUE_APP_PLAN_API.startsWith('/')
        ? window.location.origin + process.env.VUE_APP_PLAN_API + '/voice/conversion/text'
        : process.env.VUE_APP_PLAN_API + '/voice/conversion/text';
      // const url = 'http://10.20.50.95:80/voice/conversion/text'
      console.log('url', url, audioBlob);
      await this.$axios
        .post(url, audioBlob, {
          responseType: 'stream',
          baseURL: process.env.VUE_APP_PLAN_API,
          headers: {
            'Content-Type': 'application/octet-stream'
          },
          onDownloadProgress: (event) => {
            const xhr = event.target;
            const { responseText } = xhr;
            console.log('流信息', responseText);
            let chunk = '';
            let dataArray;
            const lastIndex = responseText.lastIndexOf('\n', responseText.length - 2);
            if (lastIndex !== -1) {
              chunk = responseText.slice(0, lastIndex);
              dataArray = chunk.match(/(.*(\n\n\")?\})(\n\n)?/g);
              const lastText = JSON.parse(dataArray[dataArray.length - 2]?.replace('data:', ''));
              console.log('lastTextlastTextlastText', lastText);
              if (lastText) {
                this.currentText = lastText?.message;
              }
            }
          },
          onError: function (error) {
            // 处理流错误
            console.error(error);
            this.speakingFlag = 'start';
            this.shibieLoading = false;
          }
        })
        .then(async (response) => {
          // 关闭数据流
          console.log('数据流结束', response);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log('识别接口错误', err);
          this.speakingFlag = 'start';
          this.shibieLoading = false;
        });
    },
    // 查看任务进度
    showTask() {
      this.$emit('showTaskModal');
    },
    async initTaskStatus() {
      getTaskStatus({ scheme_id: this.$route.query.id }).then((gres) => {
        console.log('任务是否需要触发', gres);
        this.runTaskStatus = gres.data?.result?.task_status || 0;
      });
    },
    async changeViews(val) {
      this.nextLoading = true;
      console.log(this.taskAllComplete, this.developFlag, this.runTaskStatus);
      await checkByDeviceId({
        scheme_id: this.$route.query.id
      })
        .then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            console.log('判断');
            // 如果是专家场景，需要判断任务状态来决定是否进入下一步，还是触发自动任务执行
            if (!this.developFlag) {
              if (this.taskAllComplete) {
                this.$emit('updateStep', val);
              } else {
                console.log('任务没有全部执行完', this.runTaskStatus);
                // 如果任务执行还未执行，触发自动执行，否则直接进入下一步
                if (this.runTaskStatus === 0 || this.reast) {
                  this.$emit('handleReStart', 'startrun', res.data.result?.device_id);
                } else {
                  this.showTask();
                }
                this.reast = false;
              }
            } else {
              // 进入下一步
              this.$emit('updateStep', val);
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '设备未绑定成功'
            });
          }
        })
        .finally(() => {
          this.nextLoading = false;
        });
    },
    handleComplete() {
      this.$router.push({
        path: '/planGenerate/index',
        query: {
          workspaceId: this.$route.query.workspaceId,
          workspaceName: this.$route.query.workspaceName
        }
      });
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name;
      });
    },
    async queryCacheHandle() {
      queryCache({ scheme_id: this.$route.query.id, ability_name: 'decision_tree_generate' }).then(
        async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            if (res.data.result) {
              this.isCache = res.data.result.isCache;
              this.isCacheDisabled = false;
            }
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        }
      );
    },

    // 知识保存接口
    saveKnowladge() {
      SchemeSaveKnow({
        scheme_id: this.$route.query.id,
        scheme_name: this.schemeInfo.name,
        scheme_content: this.detailContent.text
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$message({
            type: 'success',
            message: '知识保存成功！'
          });
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    handleSchemeCommand(command) {
      if (command === 'sikao') {
        this.showSikao();
      } else if (command === 'zhishi') {
        // this.saveKnowladge();
      } else if (command === 'history') {
        this.showHistory();
      } else {
        navigator.clipboard
          .writeText(this.detailContent.text)
          .then(() => {
            this.$message({
              type: 'success',
              message: '复制成功！'
            });
          })
          .catch((error) => {
            this.$message({
              type: 'error',
              message: '复制失败！'
            });
          });
      }
    },
    handleCommand(command) {
      if (command === 'sikao') {
        this.showTreeSikao();
      } else if (command === 'addCache') {
        addCache({ scheme_id: this.$route.query.id, ability_name: 'decision_tree_generate' }).then(
          async (res) => {
            if (res.status === 200 && res.data.code === 200) {
              this.$message({
                type: 'success',
                message: res.data?.result || '新增成功'
              });
              this.isCache = !this.isCache;
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          }
        );
      } else if (command === 'removeCache') {
        removeCache({ scheme_id: this.$route.query.id, ability_name: name }).then(async (res) => {
          if (res.status === 200 && res.data.code === 200) {
            this.$message({
              type: 'success',
              message: res.data?.result || '删除成功'
            });
            this.isCache = !this.isCache;
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        });
      } else {
        this.copyText();
      }
    },
    async historyDataFn() {
      await this.queryPlanDetail();
    },
    copyText() {
      // 获取需要复制的文本
      const text = this.treeData;
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message({
            type: 'success',
            message: '复制成功！'
          });
        })
        .catch((error) => {
          this.$message({
            type: 'error',
            message: '复制失败！'
          });
        });
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg');
      const arr = [...svgdoms];
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          });
        }
      });
    },
    // 显示历史记录
    showHistory() {
      this.historyVisable = true;
    },
    closeHistory() {
      this.historyVisable = false;
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示');
      this.processVisable = true;
    },
    showTreeSikao() {
      console.log('思维树生成过程显示');
      this.processTreeVisable = true;
    },
    closeSikaoRizhi() {
      this.processVisable = false;
      this.processTreeVisable = false;
    },
    handleClose() {
      this.dialogTableVisible = false;
      this.saveLoading = false;
      clearInterval(this.timer);
      this.timer = null;
    },
    handleDone() {
      console.log(this.gridData, '222');
      updateAbilityMapping({
        scheme_id: this.$route.query.id,
        config: {
          header: this.dataThs,
          data: this.gridData
        },
        ability_status: 'finished'
      }).then((res) => {
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          this.$message({
            type: 'success',
            message: '更新完成!'
          });

          this.dialogTableVisible = false;
          this.saveLoading = false;
        } else {
          this.$message({
            type: 'success',
            message: '更新失败!'
          });
          this.dialogTableVisible = false;
          this.saveLoading = false;
        }
      });
    },

    handleAbilityMapping() {
      this.saveLoading = true;
      if (this.timer != null) {
        return;
      }
      this.timer = setInterval(() => {
        queryAbilityMapping({ scheme_id: this.$route.query.id })
          .then((res) => {
            if (res.status === 200 && res.data.code === 200 && res.data.result) {
              this.dataStatus = res.data.result.ability_status;
              const status = res.data.result.ability_status;
              let configData = {};
              try {
                configData = JSON.parse(res.data.result?.config) || {};
              } catch (error) {
                configData = res.data.result?.config || {};
              }
              if (status !== 'generating') {
                this.saveLoading = false;
                clearInterval(this.timer);
                this.timer = null;
              }
              console.log(configData, '111');
              this.dataThs = configData.header || {};
              this.dataThDatas = Object.keys(configData.header || {}).map((item) => {
                return { name: configData.header[item], field: item };
              });
              this.gridData = configData.data || [];
              //   this.gridData = configData.map((item)=>{
              //   return {
              //     param_name: item.param_name,
              //     dataset: item.dataset,
              //     param_key: item.param_key,
              //     test_point:item.test_point,
              //     frequency: item.frequency,
              //     calculation_formula: item.calculation_formula,
              //   }
              // })
              this.dialogTableVisible = true;
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .catch((_err) => {
            clearInterval(this.timer);
            this.timer = null;
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {});
      }, 1000);
    },

    // 思维图渲染模式切换
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag;
      this.handleRenderBrainMaps();
    },
    // 渲染脑图
    handleRenderBrainMaps() {
      if (!this.jueceYulanFlag) {
        const nodeMarkmap = document.getElementById('markmap');
        if (nodeMarkmap) {
          nodeMarkmap.innerHTML = '';
        }
      } else {
        const transformer = new Transformer();
        const { root } = transformer.transform(this.treeData);
        if (!root.children || root.children.length === 0) {
          return;
        }
        this.$nextTick(() => {
          Markmap.create('#markmap', null, root);
        });
      }
    },

    regenerate(val) {
      this.treeData = '';
      this.treeProcess = '';
      this.taskLoading = true;
      if (val) {
        this.jueceYulanFlag = false;
      }
      this.$emit('updateGenerate', val);
      querySchemeDetailById({ scheme_id: Number(this.$route.query.id) }).then((res) => {
        const name = res.data.result.name;
      });
    },
    queryDecision() {
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_tree'
      };
      GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$emit('handleUpdateTreeData', res.data.result?.decision_making_content || '');
          this.treeData = res.data.result?.decision_making_content || '';
          this.treeDataProcess = res.data.result?.sub_content || '';
          // this.optionDataProcess = res.data.result?.sub_content
          if (this.treeData) {
            // 思维脑图
            this.hasMarkmapInstance = true;
            this.handleRenderBrainMaps();
          } else {
            const nodeMarkmap = document.getElementById('markmap');
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = '';
            }
          }
          this.queryCacheHandle();
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },
    async handleDetailSave() {
      const { id, ...rest } = this.detailContent;
      const res = await PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败');
        return;
      }
      this.$message.success('编辑成功');
      this.isEdit = false;
      this.queryPlanDetail();
      // 编辑成功自动生成思维树
      this.regenerate('resetTree');
    },
    handleUpdateContent(val) {
      this.detailContent.text = val;
    },
    handleDetailSaveClose() {
      this.isEdit = false;
      this.detailContent.text = this.hisDetail;
    },
    goToDetail(task) {
      task.adjust_url && window.open(task.adjust_url);
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true;
        this.startX = event.clientX;
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width;
        this.startWidth = leftWidth;
        document.addEventListener('mousemove', this.onDrag);
        document.addEventListener('mouseup', this.stopDrag);
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX;
        const widthLeft = this.startWidth + deltaX;
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px';
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px';
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    startTopDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true;
        this.startY = event.clientY;
        const topHeight = document.getElementById('top-content').getBoundingClientRect().height;
        this.startHeight = topHeight;
        document.addEventListener('mousemove', this.onTopDrag);
        document.addEventListener('mouseup', this.stopTopDrag);
      }
    },
    onTopDrag(event) {
      if (this.isDragging) {
        const deltaY = event.clientY - this.startY;
        const topHeight = this.startHeight + deltaY;
        this.topHeight = topHeight + 'px';
        this.bottomHeight = this.totalHeight - topHeight - 30 + 'px';
      }
    },
    stopTopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onTopDrag);
      document.removeEventListener('mouseup', this.stopTopDrag);
    },
    getWsID() {
      let workspaceId = '';
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId;
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId;
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?');
          const params = Object.fromEntries(new URLSearchParams(query));
          workspaceId = params.workspaceId;
        } catch (error) {
          console.log('error', error);
        }
      }
      return workspaceId;
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10;
    },
    changeShowTopRight() {
      this.planDetailShow = !this.planDetailShow;
      this.planDetailTopShow = !this.planDetailTopShow;
      if (this.planDetailShow) {
        this.rightWidth = '';
        this.leftWidth = '50%';
        this.topHeight = '65%';
        this.bottomHeight = '35%';
      } else {
        this.rightWidth = '';
        this.leftWidth = '0px';
        this.topHeight = '';
        this.bottomHeight = '0px';
      }
    },
    changeShowBottomRight() {
      this.planDetailShow = !this.planDetailShow;
      this.planDetailBottomShow = !this.planDetailBottomShow;
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag;
      if (this.rightFullFlag) {
        this.leftWidth = '100%';
        this.rightWidth = '0';
      } else {
        this.leftWidth = '50%';
        this.rightWidth = '100%';
      }
    },
    closechangeThinkWrap() {
      this.thinkFlag = !this.thinkFlag;
      this.thinkFullFlag = false;
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
      }
    },
    // 显示思考过程
    changeThinkWrap(data) {
      this.thinkFlag = !this.thinkFlag;
      this.thinkFullFlag = false;
      if (this.thinkFlag) {
        this.$refs.chatBox.style.height = 'calc(100vh - 530px)';
      } else {
        this.$refs.chatBox.style.height = 'calc(100vh - 300px)';
      }
      if (data) {
        this.processContent.text = data;
      } else {
        this.processContent.text = this.processContent.text || '';
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag;
    },
    saveFangan() {
      console.log('修改的值', this.contentEditor.getValue());
      if (this.hasChatingName === '') {
        this.detailContent.text = this.contentEditor.getValue();
        const { id, ...rest } = this.detailContent;
        PlanTaskEdit({ ...rest, scheme_id: this.$route.query.id });
      }
    },
    async queryPlanDetail() {
      SchemeDetail({ scheme_id: this.$route.query.id }).then(async (res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.detailContent = res.data.result;
          this.$emit('handleUpdateScheme', this.detailContent.text);
          this.$emit('handlePublishAbility', this.detailContent.publish_ability);
          this.optionDataProcess = this.detailContent.sub_content;
          // this.detailContent.text = '```mermaid\ngraph LR\nA[变压器运维方案]\nB[日常维护]\nC[定期检修]\nD[长期检修]\nE[变压器故障]\nF[常见故障]\nG[故障诊断]\nH[故障解决]\nA-->B\nB-->B1(检测冷却设备)\nB-->B2(检查油位)\nB-->B3(检查温度)\nA-->C\nC-->C1(3个月-检查油样)\nC-->C2(6个月或1年-检验绝缘电阻)\nC-->C3(1到2年-进行大修)\nA-->D\nD-->D1(长期维护节点-沟通制造商)\nA-->E\nE-->F\nF-->F1(变压器热故障)\nF-->F2(绝缘破损)\nF-->F3(电极磨损)\nF-->F4(机械故障)\nF-->F5(油位故障)\nF-->F6(油质故障)\nF-->F7(电磁故障)\nE-->G\nG-->G1(观察法)\nG-->G2(试验法)\nG-->G3(分析法)\nG-->G4(维护记录法)\nG-->G5(无损检测)\nG-->G6(红外热像技术)\nG-->G7(振动检测)\nE-->H\nH-->H1(调整变压器油位)\nH-->H2(修复冷却系统)\nH-->H3(修复绝缘破损)\nH-->H4(修理机械故障)\nH-->H5(更换电极部件)\nH-->H6(红外热像处理)\nH-->H7(无损检测和振动检测)\n```';
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      });
    },

    startWriteOpt() {
      const content = document.getElementById('detail-content');
      content.addEventListener('mouseup', () => {
        const selection = window.getSelection();
        console.log('selection', selection);
        if (selection.toString() !== '') {
          // 用户选中了文本，执行相应的动作
          console.log('用户选中了文本：' + selection.toString());
          this.writeText = selection.toString();
          const range = selection.getRangeAt(0);
          this.range = range;
          this.writeFlag = false;
        } else {
          this.writeText = '';
          this.writeFlag = true;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
:deep(.el-loading-spinner2) {
  width: 130px !important;
  background: none !important;
  margin-top: 20px;
}
.chatContainerTest {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
.cardEmpty {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.topContent {
  height: calc(100% - 47px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .el-empty {
    height: 100%;
    display: flex;
    padding: 10px 0;
    :deep(.el-empty__image) {
      height: 50%;
      margin-top: -15%;
    }
    :deep(.el-empty__description) {
      margin-top: 5%;
      height: 10%;
    }
    :deep(.el-empty__bottom) {
      height: 10%;
      margin-top: 10%;
    }
  }
}

.optHeader {
  padding: 0px 20px;
  border-bottom: 1px solid #ebecf0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .rightTitle {
    font-size: 14px;
    font-weight: bold;
    color: #323233;
    line-height: 22px;
    padding: 12px 0px;
  }
  .rightTitleOpt {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .rightTextBtn {
      background-color: #406bd4;
      font-size: 12px;
      color: #fff;
      padding: 0px 6px;
      height: 24px;
      line-height: 24px;
      border-radius: 2px;
      margin-left: 8px;
      cursor: pointer;
      &:hover {
        background: #3455ad;
      }
      &:active {
        background: #264480;
      }
    }
    .rightBtn {
      // background: #F2F3F5;
      border-radius: 2px;
      width: 30px;
      height: 30px;
      color: #4068d4;
      margin-left: 8px;
      text-align: center;
      line-height: 28px;
      cursor: pointer;
      &:hover {
        background: #ebecf0;
      }
      &:active {
        background: #dcdde0;
      }
      &.rightBtnBlue {
        background-color: #406bd4;
        &:hover {
          background: #3455ad;
        }
        &:active {
          background: #264480;
        }
      }
      img {
        width: 16px;
        height: auto;
      }
    }
  }
}
.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 260px) !important;
      max-height: calc(100vh - 260px) !important;
    }
    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }

    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }
    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
  }
  display: flex;
  flex-direction: row;
  height: calc(100vh - 205px);
  overflow-y: hidden;
  position: relative;
  .containerCard {
    //height: calc(100% - 18px);
    // max-height: calc(100vh - 210px);
    overflow-y: hidden;
    overflow-x: hidden;
    margin: 16px 16px 0px 0px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background-color: #fff;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    &.containerCardFull {
      position: fixed !important;
      top: 32px;
      z-index: 2005;
      height: calc(100% - 50px);
      max-height: calc(100% - 50px);
      width: 100%;
      left: 0px;
      width: 100%;
      margin-left: 0px !important;
      .chatScroll {
        max-height: calc(100vh - 220px) !important;
      }
      .optScroll {
        height: calc(100vh - 190px) !important;
        max-height: calc(100vh - 190px) !important;
      }
      .optContentBox {
        height: calc(100vh - 210px) !important;
        max-height: calc(100vh - 210px) !important;
      }
    }

    .optContentBox {
      // height: calc(100vh - 340px);
      max-height: 100%;
      width: 100%;
      position: relative;
      overflow-y: auto;
    }
    .optContent {
      position: relative;
      display: flex;
      flex-direction: column;
      flex: 1;
      .optScroll {
        position: relative;
        // max-height: calc(100vh - 450px);
        max-height: calc(100vh - 360px);
        overflow-y: hidden;
        overflow-x: hidden;
        padding: 10px 20px;
        display: flex;
        flex: 1;
        flex-direction: column;
        &.optScrollMini {
          max-height: calc(100vh - 400px);
        }
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .chatFooter {
        position: relative;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0px 16px;
        .chatInput {
          flex: 1;
          border-radius: 4px;
          ::v-deep .el-textarea__inner {
            border-radius: 4px;
          }
        }
        .send-btn {
          position: absolute;
          right: 30px;
          display: flex;
          flex-direction: row;
          align-items: flex-end;
          // margin-top: 6px;
          font-size: 12px;
          bottom: 10px;
          .yuyinBtn {
            cursor: pointer;
            margin-right: 8px;
            &.yuyinBtnDisabled {
              cursor: not-allowed;
              pointer-events: none;
            }
            img {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }
  }
  .chatRight {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    height: calc(100% - 18px);
    max-height: calc(100% - 18px);
    overflow-y: hidden;
    margin-top: 16px;
    position: relative;
    &.chatRightMini {
      height: calc(100% - 68px);
      max-height: calc(100% - 68px);
    }
    .optContent {
      background: #ffffff;
      &.chatRightFull {
        position: fixed !important;
        top: 52px;
        z-index: 2005;
        height: calc(100% - 50px);
        width: 100%;
        height: 100vh;
        left: 0px;
        margin-left: 0px !important;
      }
      .bottomScroll {
        height: calc(100% - 47px);
        padding: 10px;
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
}
.topResize {
  cursor: row-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  height: 10px;
  width: 100%;
  color: #c3cadd;
  display: flex;
  flex-direction: row;
  align-items: center;
  &:hover {
    background: #e0e6ff;
    .process-icon {
      color: #3455ad !important;
    }
  }
  .el-two-column__icon-top {
    width: 50%;
    height: 4px;
    display: flex;
    flex-direction: row-reverse;
    .el-two-column__icon-top-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(right, #d5dbed, #e6eafb) no-repeat;
    }
  }
  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }
  .el-two-column__trigger-icon_shu {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      height: 10px;
      color: #c3cadd;
    }
  }
  .el-two-column__icon-bottom {
    width: 50%;
    height: 4px;
    .el-two-column__icon-bottom-bar {
      width: 50%;
      height: 4px;
      background: -webkit-linear-gradient(left, #d5dbed, #e6eafb) no-repeat;
    }
  }
}
.resize {
  cursor: col-resize;
  background-color: #f4f5f9;
  padding: 0px 8px;
  width: 10px;
  color: #c3cadd;
  display: flex;
  flex-direction: column;
  align-items: center;
  &:hover {
    background: #e0e6ff;
    .process-icon {
      color: #3455ad !important;
    }
  }
  .el-two-column__icon-top {
    height: 50%;
    width: 4px;
    display: flex;
    flex-direction: column-reverse;
    .el-two-column__icon-top-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
    }
  }
  .el-two-column__trigger-icon {
    width: 25px;
    height: 25px;
    color: #c3cadd;
    .process-icon {
      width: 25px;
      color: #c3cadd;
    }
  }
  .el-two-column__icon-bottom {
    height: 50%;
    width: 4px;
    .el-two-column__icon-bottom-bar {
      height: 50%;
      width: 4px;
      background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
    }
  }
}
.optFooter {
  position: fixed;
  bottom: 0px;
  left: 0px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 20px;
  min-height: 54px;
}
::v-deep .el-button--info {
  background-color: #f2f3f5;
  color: #4068d4;
  border-color: #f2f3f5;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}
::v-deep .el-button--text {
  background-color: #fff;
  color: #4068d4;
  border-color: #fff;
  padding: 6px 16px;
  border-radius: 2px;
  &.is-disabled {
    opacity: 0.4;
    background-color: #f2f3f5 !important;
    color: #4068d4;
    border-color: #f2f3f5 !important;
  }
  &:hover {
    background-color: #ebecf0;
    border-color: #ebecf0;
    color: #4068d4;
  }
  &:active {
    background-color: #dcdde0;
    border-color: #dcdde0;
  }
}

.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
.mark-map {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  position: relative;
  background: transparent !important;
}
</style>
