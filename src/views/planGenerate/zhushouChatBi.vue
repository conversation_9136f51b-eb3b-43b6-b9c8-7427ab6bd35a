<template>
  <div
    :class="
      $store.state.planGenerate.isIframeHide ? 'containerBox2 containerBox2IFrame' : 'containerBox2'
    "
    style="width: 100%"
  >
    <div
      id="left-content"
      :style="{
        width: leftWidth,
        maxWidth: leftWidth,
        marginRight: !rightFullFlag ? '0px' : '16px',
        userSelect: isDragging ? 'none' : 'auto',
        transition: isDragging ? 'none' : 'width 0.2s',
        position: thinkFullFlag ? '' : 'relative'
      }"
      :class="rightFullFlag ? 'containerCard containerCardFull' : 'containerCard'"
    >
      <div v-if="agentSenceCode === 'custom_cognition_assistant_scene'" class="optContent">
        <div class="optHeader">
          <div class="rightTitle">思维图</div>
          <div class="rightTitleOpt">
            <el-tooltip
              class="item"
              effect="dark"
              :content="jueceYulanFlag ? '文本模式' : 'Markdown模式'"
              placement="top"
            >
              <el-button type="info" size="mini" @click="yuLan()"
                ><img v-if="jueceYulanFlag" src="@/assets/images/planGenerater/text.png" /><img
                  v-else
                  src="@/assets/images/planGenerater/markdown.png"
              /></el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="rightFullFlag ? '退出全屏' : '全屏'"
              placement="top"
            >
              <el-button
                :type="rightFullFlag ? 'primary' : 'info'"
                size="mini"
                @click="changeShowFull"
                ><img v-if="!rightFullFlag" src="@/assets/images/planGenerater/full.png" /><img
                  v-else
                  src="@/assets/images/planGenerater/tuichuquanping.png"
              /></el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="optScroll">
          <div class="optContentBox">
            <div v-if="jueceYulanFlag" style="height: 100%; width: 100%" @mouseenter="fangda">
              <template
                v-if="
                  (treeData && treeData?.indexOf('graph') > -1) ||
                  treeData?.indexOf('flowchart') > -1 ||
                  treeData?.indexOf('mermaid') > -1
                "
              >
                <MyEditor id="MyEditorBI" ref="MyEditorBI" :md-content="treeData"></MyEditor>
                <!-- <pre v-if="(treeData && treeData?.indexOf('graph') > -1 || treeData?.indexOf('flowchart') > -1) && treeData?.indexOf('mermaid')<0&&treeData?.indexOf('```')<0"><div class="language-mermaid">{{treeData}}</div></pre>
                  <vue-markdown v-else v-highlight :source="treeData" class="markdown-body"></vue-markdown> -->
              </template>
              <template v-else>
                <svg id="markmap" class="optContentBox"></svg>
              </template>
            </div>
            <div v-else>
              <pre>{{ treeData }}</pre>
            </div>
          </div>
        </div>
      </div>
      <div class="optFooter">
        <el-button
          class="button-last"
          :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName !== ''"
          type="primary"
          @click="regenerate()"
          >{{
            treeStatusLast === 1 || treeStatusLast === 0
              ? '生成中...'
              : codeData !== ''
              ? '重新生成'
              : '生成'
          }}</el-button
        >
      </div>
    </div>
    <div
      v-if="planDetailShow && !rightFullFlag"
      id="resize"
      class="resize"
      title="收缩侧边栏"
      @mousedown="startDrag"
    >
      <div class="el-two-column__icon-top"><div class="el-two-column__icon-top-bar"></div></div>
      <div class="el-two-column__trigger-icon">
        <SvgIcon name="dragborder" class="process-icon" />
      </div>
      <div class="el-two-column__icon-bottom">
        <div class="el-two-column__icon-bottom-bar"></div>
      </div>
    </div>
    <div
      id="right-content"
      :style="{
        width: rightWidth,
        marginRight: '16px',
        transition: isDragging ? 'none' : 'width 0.2s',
        userSelect: isDragging ? 'none' : 'auto'
      }"
      :class="!planDetailShow ? 'chatRight chatRightFull' : 'chatRight'"
    >
      <div v-if="agentSenceCode === 'custom_cognition_assistant_scene'" class="optContent">
        <div class="optHeader">
          <div class="rightTitle">智能能力测试与迭代</div>
          <div class="rightTitleOpt">
            <!-- <el-tooltip class="item" effect="dark" content="生成过程" placement="top">
                <el-button type="info" @click="showSikao">生成过程</el-button>
              </el-tooltip> -->
            <el-tooltip
              v-if="!rightFullFlag"
              class="item"
              effect="dark"
              :content="!planDetailShow ? '退出全屏' : '全屏'"
              placement="top"
            >
              <el-button
                :type="!planDetailShow ? 'primary' : 'info'"
                size="mini"
                @click="changeShowRight"
                ><img v-if="planDetailShow" src="@/assets/images/planGenerater/full.png" /><img
                  v-else
                  src="@/assets/images/planGenerater/tuichuquanping.png"
              /></el-button>
            </el-tooltip>
            <el-dropdown style="margin-left: 10px" @command="handleCommand">
              <el-button type="info" :disabled="hasChatingName !== ''" size="mini"
                ><img src="@/assets/images/planGenerater/more.png"
              /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="sikao">生成过程</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div
          v-loading="taskLoading"
          class="optScroll"
          element-loading-text="能力生成中..."
          element-loading-spinner="el-icon-loading"
        >
          <div v-if="treeStatus == 1" style="width: 100%">
            <div>{{ codeData }}</div>
          </div>
          <div v-if="treeStatus == 3" style="width: 100%; height: 100%">
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                width: 100%;
              "
            >
              <img
                src="@/assets/images/planGenerater/runerror.png"
                style="width: 180px; height: auto"
              />
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: center;
                  margin-top: 16px;
                "
              >
                能力生成失败，请<el-link
                  style="color: #4068d4"
                  :underline="false"
                  :disabled="hasChatingName !== ''"
                  @click="regenerate()"
                  >重试</el-link
                >
              </div>
            </div>
            <!-- <el-alert :closable="false" type="error">
                <span slot="title">能力生成失败<el-link :underline="false" :disabled="hasChatingName !==''" @click="regenerate()">重试</el-link></span>
              </el-alert> -->
          </div>
          <div v-else style="width: 100%" @mouseenter="fangda">
            <vue-markdown v-highlight :source="codeData" class="markdown-body"></vue-markdown>
          </div>
        </div>
      </div>
      <div class="optFooter">
        <el-button
          v-if="
            !isEdit &&
            (agentSenceCode === 'device_ops_assistant_scene' ||
              agentSenceCode === 'device_ops_assistant_scene-v1')
          "
          type="info"
          :disabled="treeStatusLast === 1 || treeStatusLast === 0 || hasChatingName !== ''"
          @click="
            () => {
              hisCode = codeData
              isEdit = true
            }
          "
          >编辑</el-button
        >
        <template
          v-if="
            isEdit &&
            (agentSenceCode === 'device_ops_assistant_scene' ||
              agentSenceCode === 'device_ops_assistant_scene-v1')
          "
        >
          <el-button type="primary" @click="handleCodeSave">保存</el-button>
          <el-button type="info" @click="handleCodeSaveClose">取消</el-button>
        </template>
        <el-button
          class="button-last"
          :disabled="treeStatusLast === 1 || treeStatusLast === 0"
          type="primary"
          @click="changeViews(activeStep - 1)"
          >上一步</el-button
        >
        <!-- TODO disabled 加 ||codeData ==='' -->
      </div>
    </div>
    <treeProcess
      :is-visible="processVisable"
      :tree-process-val="codeProcess"
      @close="closeSikaoRizhi"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { CodeEdit, GetDecision } from '@/api/planGenerateApi.js'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import 'highlight.js/styles/stackoverflow-dark.css'
import '@/style/github-markdown.css'
import treeProcess from './treeProcess.vue'
import MyEditor from './mdEditorPreview.vue'
import panzoom from 'panzoom'
export default {
  components: {
    treeProcess
  },
  props: {
    activeStep: {
      type: Number,
      required: true
    },
    agentSenceCode: {
      type: String,
      default: ''
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    treeProcessVal: {
      type: String,
      default: ''
    },
    treeStatus: {
      type: Number,
      default: -1
    },
    hasChatingName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isSuperAdmin: false, // 是否超级管理员
      rules: {
        name: [{ required: true, message: '数据集名称不能为空', trigger: 'blur' }]
      },
      hisDetail: '',
      processContent: { text: '' },
      processList: [],
      taskList: [],
      tableData: {
        list: [], // 表格数据
        page: 1,
        pageSize: 10,
        total: 0
      },
      currentText: '',
      socket: null,
      systemMessages: '',
      taskStatusText: '',
      agentError: false,
      taskStatus: 0,
      isDragging: false,
      leftWidth: '50%',
      rightWidth: '',
      totalWidth: 1000,
      isEdit: false,
      taskGeneratorStatus: '',
      planDetailShow: true,
      rightFullFlag: false,
      thinkFlag: false,
      thinkFullFlag: false,
      taskLoading: false,
      writeFlag: true,
      writeText: '',
      greets: '',
      taskGenType: '',
      sessionId: '',
      treeData: '',
      codeData: '',
      codeProcess: '',
      jueceYulanFlag: true,
      processVisable: false, // 思考过程弹窗标志
      hisCode: '', // 编辑能力代码生成代码时的数据
      treeStatusLast: 0,
      panZoomRef: null
    }
  },
  computed: {
    ...mapGetters({
      isAdmin: 'common/getIsAdminGetter'
    })
  },
  watch: {
    treeStatus: {
      handler(val) {
        this.treeStatusLast = val
        if (val === 2) {
          this.taskLoading = false
        } else if (val === 0) {
          this.taskLoading = false
        } else if (val === 3) {
          this.taskLoading = false
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.codeData = val
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.codeProcess = val
      },
      immediate: true
    }
  },
  async created() {},
  beforeDestroy() {},
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    console.log('宽度', this.rightWidth)
    this.taskStatus = this.$route.query.status
    const nodeMarkmap = document.getElementById('markmap')
    if (nodeMarkmap) {
      nodeMarkmap.innerHTML = ''
    }
    if (this.agentSenceCode === 'device_ops_assistant_scene') {
      await this.queryDecision('decision_tree') // 思维树
    }
    if (this.agentSenceCode === 'custom_cognition_assistant_scene') {
      await this.queryDecision('mind_map') // 思维图 // bi_report
    }
    await this.queryDecision('bi_report') // 能力代码生成
  },
  methods: {
    handleCommand(command) {
      if (command === 'sikao') {
        this.showSikao()
      }
    },
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg')
      const arr = [...svgdoms]
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          })
        }
      })
    },
    // 显示生成过程
    showSikao() {
      console.log('生成过程显示', this.codeProcess)
      this.processVisable = true
    },
    closeSikaoRizhi() {
      this.processVisable = false
    },
    // 关闭保存
    handleCodeSaveClose() {
      this.codeData = this.hisCode
      this.isEdit = false
    },
    // 编辑能力代码生成保存
    async handleCodeSave() {
      this.isEdit = false
      // 调用接口
      const res = await CodeEdit({
        text: this.codeData,
        scheme_status: 'decision_ability',
        scheme_id: this.$route.query.id
      })
      if (res?.data?.code !== 200) {
        this.$message.error(res?.data?.msg || '编辑失败')
        return
      }
      this.$message.success('编辑成功')
      // 编辑成功后重新查询一次最新能力代码生成代码
      await this.queryDecision('decision_ability') // 能力代码生成
    },
    // 预览
    yuLan() {
      this.jueceYulanFlag = !this.jueceYulanFlag
      if (
        this.treeData &&
        (this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1)
      ) {
        console.log('')
      } else {
        if (this.jueceYulanFlag) {
          this.thinkingHandle()
        }
      }
    },
    changeShowType() {
      this.jueceYulanFlag = !this.jueceYulanFlag
    },
    thinkingHandle() {
      if (this.treeData) {
        if (
          this.treeData.indexOf('mermaid') > -1 ||
          this.treeData.indexOf('graph') > -1 ||
          this.treeData.indexOf('flowchart') > -1
        ) {
          console.log('')
        } else {
          const transformer = new Transformer()
          const { root } = transformer.transform(this.treeData)
          this.$nextTick(() => {
            Markmap.create('#markmap', null, root)
          })
        }
      }
    },
    changeViews(val) {
      this.$emit('updateStep', val)
    },
    regenerate() {
      this.codeData = ''
      this.codeProcess = ''
      this.taskLoading = true
      this.$emit('updateCodeGenerate', 'bi_report')
    },
    async queryDecision(status) {
      await GetDecision({ scheme_id: this.$route.query.id, scheme_status: status }).then((res) => {
        this.treeStatusLast = 2
        if (res.status === 200 && res.data.code === 200) {
          if (status === 'decision_ability' || status === 'bi_report') {
            this.codeData = res.data.result?.decision_making_content || ''
            this.codeProcess = res.data.result?.sub_content || ''
          } else {
            this.treeData = res.data.result?.decision_making_content || ''
          }
          if (status === 'mind_map' && this.treeData) {
            // 思维图
            this.thinkingHandle()
          } else if (status === 'mind_map' && !this.treeData) {
            const nodeMarkmap = document.getElementById('markmap')
            if (nodeMarkmap) {
              nodeMarkmap.innerHTML = ''
            }
          }
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      })
    },
    startDrag(event) {
      if (!this.isDragging) {
        this.isDragging = true
        this.startX = event.clientX
        // console.log('this.startX', this.startX, this.rightWidth);
        const leftWidth = document.getElementById('left-content').getBoundingClientRect().width
        this.startWidth = leftWidth
        document.addEventListener('mousemove', this.onDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },
    onDrag(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.startX
        const widthLeft = this.startWidth + deltaX
        // console.log('widthLeft', widthLeft, deltaX, this.startWidth );
        this.leftWidth = widthLeft + 'px'
        this.rightWidth = this.totalWidth - widthLeft - 30 + 'px'
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    getWsID() {
      let workspaceId = ''
      // console.log('ceshi', router?.currentRoute?.query)
      if (this.$store?.state.workSpace.currentWorkSpace.workspaceId) {
        workspaceId = this.$store?.state.workSpace.currentWorkSpace.workspaceId
      } else {
        workspaceId = this.$router?.currentRoute?.query.workspaceId
      }
      if (!workspaceId) {
        try {
          const [hash, query] = window.location.href.split('#')[1].split('?')
          const params = Object.fromEntries(new URLSearchParams(query))
          workspaceId = params.workspaceId
        } catch (error) {
          console.log('error', error)
        }
      }
      return workspaceId
    },
    scrollToBottom() {
      this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight + 10
    },
    changeShowRight() {
      this.planDetailShow = !this.planDetailShow
      if (this.planDetailShow) {
        this.rightWidth = ''
        this.leftWidth = '50%'
      } else {
        this.rightWidth = ''
        this.leftWidth = '0px'
      }
    },
    changeShowFull() {
      this.rightFullFlag = !this.rightFullFlag
      if (this.rightFullFlag) {
        this.leftWidth = '100%'
        this.rightWidth = '0'
      } else {
        this.leftWidth = '50%'
        this.rightWidth = '100%'
      }
    },
    changeThinkFull() {
      this.thinkFullFlag = !this.thinkFullFlag
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
.containerBox2 {
  &.containerBox2IFrame {
    height: 100%;
    .containerBox {
      height: calc(100vh - 104px) !important;
      max-height: calc(100vh - 104px) !important;
    }
    .containerCardFull {
      top: -16px !important;
      height: calc(100% - 0px) !important;
      max-height: calc(100% - 0px) !important;
    }
    .fanganyouhua {
      background: #fff;
      top: 0px !important;
      height: calc(100vh - 0px) !important;
      max-height: calc(100vh - 0px) !important;
    }
    .chatRightFull {
      top: -16px !important;
      height: 100vh !important;
      max-height: 100vh !important;
    }
    .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
    }
    .chatRight .optScroll {
      height: calc(100vh - 220px) !important;
      max-height: calc(100vh - 220px) !important;
      .optContentBox {
        height: calc(100vh - 220px) !important;
      }
    }
  }
}
.chatContainer {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  .headerBox {
    background-color: #fff;
    .headerTitle {
      padding: 14px 20px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebecf0;
      .title {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
      }
    }
  }

  .containerBox2 {
    display: flex;
    flex-direction: row;
    height: calc(100%);
    max-height: calc(100%);
    overflow-y: hidden;
    position: relative;
    .showRightFix {
      position: absolute;
      right: 6px;
      top: 24px;
      width: 30px;
      height: 30px;
      background: #4068d4;
      border-radius: 2px;
      text-align: center;
      line-height: 27px;
      z-index: 2;
      color: #fff;
      cursor: pointer;
      &:hover {
        background: #3455ad;
      }
      &:active {
        background: #264480;
      }
      img {
        width: 12px;
        height: auto;
      }
    }
    .containerCard {
      //height: calc(100% - 18px);
      // max-height: calc(100vh - 210px);
      overflow-y: hidden;
      overflow-x: hidden;
      margin: 16px 16px 0px 0px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: #fff;
      margin-left: 16px;
      &.containerCardFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        max-height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        .optScroll {
          height: calc(100vh - 150px) !important;
          max-height: calc(100vh - 150px) !important;
        }
        .optContentBox {
          width: 100%;
          min-height: calc(100vh - 150px) !important;
        }
      }
      .optContentBox {
        width: 100%;
        min-height: calc(100vh - 330px);
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 330px);
        max-height: calc(100vh - 330px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        display: flex;
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
      .chatHeader {
        font-size: 14px;
        color: #323233;
        line-height: 24px;
        font-weight: bold;
        background: url(@/assets/images/planGenerater/chat-bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .thinkContent {
        margin-left: 16px;
        width: calc(100% - 32px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        max-height: 225px;
        height: 225px;
        overflow-y: auto;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dcdde0;
        transition: height 0.1s;
        &.thinkContentFull {
          position: absolute !important;
          left: 0px;
          width: calc(100vw - 214px) !important;
          height: calc(100vh - 150px) !important;
          overflow: hidden;
          z-index: 2;
          max-height: calc(100vh - 150px) !important;
          top: 0px;
        }
        .thinkHeader {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 12px 12px;
          .title {
            color: #323233;
            line-height: 20px;
            display: flex;
            align-items: center;
            img {
              height: 24px;
              width: 24px;
              margin-right: 4px;
            }
          }
          .thinkOpt {
            display: flex;
            .think-btn {
              font-size: 14px;
              margin-left: 4px;
              cursor: pointer;
              width: 24px;
              height: 24px;
              text-align: center;
              line-height: 22px;
              font-weight: bold;
              &.think-btn-blue {
                background-color: #4068d4 !important;
                border-radius: 4px;
                &:hover {
                  background: #3455ad !important;
                }
                &:active {
                  background: #264480;
                }
              }
              &:hover {
                background-color: #ebecf0;
                border-radius: 4px;
              }
              img {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
        .thinkWrap {
          background: #ffffff;
          padding: 0px 12px 12px 36px;
          max-height: calc(100% - 40px);
          overflow-y: auto;
          .thinkItem {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-start;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dcdde0;
            margin-top: 12px;
            &:first-child {
              margin-top: 0px;
            }
          }
          .itemContent {
            color: #646566;
            line-height: 22px;
            flex: 1;
            margin-left: 8px;
          }
        }
      }
    }
    .chatRight {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      height: calc(100% - 18px);
      max-height: calc(100% - 18px);
      overflow-y: hidden;
      margin-top: 16px;
      position: relative;
      &.chatRightFull {
        position: fixed !important;
        top: 32px;
        z-index: 2005;
        height: calc(100% - 50px);
        width: 100%;
        left: 0px;
        width: 100%;
        margin-left: 0px !important;
        .optScroll {
          height: calc(100vh - 140px) !important;
          max-height: calc(100vh - 140px) !important;
        }
        .optScroll2 {
          height: calc(100vh - 110px) !important;
          max-height: calc(100vh - 110px) !important;
        }
        .optContentBox {
          height: calc(100vh - 180px) !important;
        }
      }
      .optContentBox {
        // height: calc(100vh - 340px);
        height: 100%;
        max-height: 100%;
        width: 100%;
        position: relative;
      }
      .optHeader {
        padding: 0px 20px;
        border-bottom: 1px solid #ebecf0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .rightTitle {
          font-size: 14px;
          font-weight: bold;
          color: #323233;
          line-height: 22px;
          padding: 12px 0px;
        }
        .rightTitleOpt {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          .rightTextBtn {
            background-color: #406bd4;
            font-size: 12px;
            color: #fff;
            padding: 0px 6px;
            height: 24px;
            line-height: 24px;
            border-radius: 2px;
            margin-left: 8px;
            cursor: pointer;
            &:hover {
              background: #3455ad;
            }
            &:active {
              background: #264480;
            }
          }
          .rightBtn {
            // background: #F2F3F5;
            border-radius: 2px;
            width: 30px;
            height: 30px;
            color: #4068d4;
            margin-left: 8px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            &:hover {
              background: #ebecf0;
            }
            &:active {
              background: #dcdde0;
            }
            &.rightBtnBlue {
              background-color: #406bd4;
              &:hover {
                background: #3455ad;
              }
              &:active {
                background: #264480;
              }
            }
            img {
              width: 16px;
              height: auto;
            }
          }
        }
      }
      .optScroll {
        position: relative;
        height: calc(100vh - 330px);
        max-height: calc(100vh - 330px);
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        ::v-deep .el-textarea {
          margin-bottom: 10px;
        }
        .btn {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
      }
      .optContent {
        max-height: calc(100% - 60px);
        overflow-y: hidden;
      }
      .optFooter {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 20px;
        min-height: 54px;
      }
    }
  }
  .resize {
    cursor: col-resize;
    background-color: #f4f5f9;
    padding: 0px 8px;
    width: 10px;
    color: #c3cadd;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:hover {
      background: #e0e6ff;
      .process-icon {
        color: #3455ad !important;
      }
    }
    .el-two-column__icon-top {
      height: 50%;
      width: 4px;
      display: flex;
      flex-direction: column-reverse;
      .el-two-column__icon-top-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(bottom, #d5dbed, #e6eafb) no-repeat;
      }
    }
    .el-two-column__trigger-icon {
      width: 25px;
      height: 25px;
      color: #c3cadd;
      .process-icon {
        width: 25px;
        color: #c3cadd;
      }
    }
    .el-two-column__icon-bottom {
      height: 50%;
      width: 4px;
      .el-two-column__icon-bottom-bar {
        height: 50%;
        width: 4px;
        background: -webkit-linear-gradient(top, #d5dbed, #e6eafb) no-repeat;
      }
    }
  }
  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
}
.descriptionTd {
  max-width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-button--mini {
  line-height: 0px !important;
  padding: 8px 6px !important;
  img {
    height: 16px;
    margin-top: -2px;
  }
}
</style>
