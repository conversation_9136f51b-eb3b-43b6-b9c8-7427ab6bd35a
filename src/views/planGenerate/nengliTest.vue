<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="能力测试结果"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="50%"
    >
      <div style="height: 160px">
        <div v-if="treeProcessData == ''" v-loading="loading" style="overflow-y: auto;">
          请求调用中。。。
        </div>
        <div v-if="treeProcessData !== ''" v-loading="loading" style="overflow-y: auto;padding-bottom: 30px;">
          请求状态: {{ treeProcessData?.success ? "成功" : "失败" }}
        </div>
        <div v-if="treeProcessData !== '' && treeProcessData.success" v-loading="loading" style="overflow-y: auto;padding-bottom: 30px;">
          返回结果: {{treeProcessData?.result}}
        </div>
        <div v-if="treeProcessData !== '' && !treeProcessData.success" v-loading="loading" style="overflow-y: auto;padding-bottom: 30px;">
          错误描述: {{treeProcessData?.message}}
        </div>
      </div>
<!--      <div v-else style="height: 300px;max-height: 300px;overflow-y: auto;">
        <el-empty description="结果内容为空"></el-empty>
      </div>-->
      <div slot="footer" class="dialog-footer">
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { UpdateTask, TaskTypeList, CreateTask } from '@/api/planGenerateApi.js';

export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    treeProcessVal: {
      type: String,
      default: null
    },

  },
  data() {
    return {
      treeProcessData: '',
      loading: false,
      showFlag: false
    };
  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          this.showFlag = val
          this.treeProcessData = this.treeProcessVal
        } else {
          this.showFlag = false;
          this.treeProcessData = ''
        }
      },
      immediate: true
    },
    treeProcessVal: {
      handler(val) {
        this.treeProcessData = val
      },
      immediate: true
    },

  },
  methods: {
    queryDataInfo() {
      console.log('d');
    },
    onClose() {
      this.treeProcessData = '';
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
