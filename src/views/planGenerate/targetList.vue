<template>
  <div class='page-container'>
    <div class='page-header'>
      <div style='flex: 1;min-width: 120px'>
        <el-radio-group v-model="tabPosition" size="small" @input='handleChange'>
          <el-radio-button label="1">全部</el-radio-button>
          <el-radio-button label="2">我的</el-radio-button>
        </el-radio-group>
      </div>
      <el-select
        v-model="createUserId"
        style='margin-right: 30px'
        placeholder="请选择最后更新人"
        :remote-method="searchUser"
        clearable
        filterable
        remote
        @change='handleUserChange'
      >
        <el-option
          v-for="item in userList"
          :key="item.id"
          :label="`${item.nickname}(${item.loginName})`"
          :value="item.id"
        />
      </el-select>
      <el-cascader v-model="agent_scene" clearable style='width: 200px;margin-right: 30px' placeholder='请选择场景' :props="props" @change='handleCascaderChange'></el-cascader>
      <el-input v-model.trim="pageParams.abilityName" style='width: 200px;margin-right: 30px' placeholder="请输入能力名称">
        <el-button
slot="append" icon="el-icon-search" @click="()=>{
          pageParams.pageNum = 1
          getQueryPageList(+tabPosition === 2?userInfo.userId:'')
        }"></el-button>
      </el-input>
      <el-radio-group v-model="tabPosition2" style='margin-right: 15px;;min-width: 90px' size="small">
        <el-radio-button label="1">
          <i class="el-icon-s-operation"></i>
        </el-radio-button>
        <el-radio-button label="2">
          <i class="el-icon-menu"></i>
        </el-radio-button>
      </el-radio-group>
    </div>
    <div class='page-table-items'>
      <div v-for='(item, index) in tableData' v-if="tabPosition2 === '1' && tableData.length > 0" :key='index' class='page-table-item'>
        <div style='width: 35%;display: flex;flex-direction: row;align-items: flex-start;min-width: 380px'>
          <el-image
            style="width: 40px; height: 40px"
            src="/img/icon-ability.jpeg"
            :fit="fit"></el-image>
          <div style='margin-left: 10px'>
            <div>{{item.abilityName}}</div>
            <div style='width: 100%;overflow: hidden'>{{item.abilityDesc || '--'}}</div>
          </div>
        </div>
        <div style='width: 35%;min-width: 380px'>
          <div style='color: gray'>场景类型:</div>
          <div><el-tag size="small">{{ids[item.sceneType] || '--'}}</el-tag></div>
        </div>
        <div style='width: 20%;min-width: 200px'>
          <div style='color: gray'>最后更新人:</div>
          <div>{{item.updatedUserName || '--'}}</div>
        </div>
        <div style='width: 20%;min-width: 200px'>
          <div style='color: gray'>更新时间:</div>
          <div>{{item.updatedDate || '--'}}</div>
        </div>
        <div style='width: 10%;display: flex;flex-direction: row;'>
          <el-button style='float: right;' type="primary" @click="handleNav(item)"> 能力设计 </el-button>
          <el-button style='float: right;' type="primary" @click="handleShow(item)"> 查看 </el-button>
        </div>
      </div>
      <div v-for='(item, index) in tableData' v-if="tabPosition2 === '2'  && tableData.length > 0" :key='index' class='page-table-item2' @click="handleNav(item)">
        <div :title='item.abilityName' style='padding: 15px;border-bottom: 1px solid #DCDDE0;font-size: 18px;font-weight: 700;width: 100%' class='hidden-sc'>
          {{item.abilityName}}
        </div>
        <div style='padding: 15px;width: 100%'>
          <div style='display: flex;flex-direction: row;align-items: center;width: 100%'>场景类型：<el-tag size="small" class='hidden-sc' :title="ids[item.sceneType] || '--'">{{ids[item.sceneType] || '--'}}</el-tag></div>
          <div style='margin-top: 6px'>最后更新人：{{item.updatedUserName || '--'}}</div>
          <div style='margin-top: 6px'>更新时间：{{item.updatedDate || '--'}}</div>
          <div style='margin-top: 6px'>描述：{{item.abilityDesc || '--'}}</div>
        </div>
      </div>
      <div v-if='tableData.length === 0' style='width: 100%;height: 100%;display: flex;flex-direction: row;justify-content: center'>
        <el-empty description="暂无数据"></el-empty>
      </div>
    </div>
    <div class='page-footer'>
      <el-pagination
        prev-text="上一页"
        next-text="下一页"
        background
        style='float: right;margin-right: 30px'
        :current-page="pageParams.pageNum"
        :page-size="pageParams.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, prev, pager, next"
        :total="PageTotal"
        @size-change="getQueryPageList(+tabPosition === 2?userInfo.userId:'')"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog :visible.sync="dialogTableVisible" :modal-append-to-body='false' title="调用说明" :before-close="handleClose">
      <div style='font-size: 16px;font-weight: 700;margin-bottom: 10px'>请求地址</div>
      <div>{{url}}</div>
      <div style='font-size: 16px;font-weight: 700;margin: 15px 0 10px 0'>请求类型</div>
      <div>POST</div>
      <div style='font-size: 16px;font-weight: 700;margin: 15px 0 10px 0'>请求参数</div>
      <el-table v-loading="isLoading" element-loading-text="生成中..." element-loading-spinner="el-icon-loading" custom-class="el-loading-spinner2" class="transition-box" :data="gridData" max-height="400">
        <el-table-column
          prop="d_mapping_field"
          label="参数名称" />
        <el-table-column
          prop="e_field_desc"
          label="参数说明" />
        <el-table-column
          prop="isUpload"
          label="是否必传"
          width="180"/>
        <el-table-column
          prop="f_field_type"
          label="数据类型" />
      </el-table>
      <div style='font-size: 16px;font-weight: 700;margin: 15px 0 10px 0'>响应参数</div>
      <el-table v-loading="isLoading" element-loading-text="生成中..." element-loading-spinner="el-icon-loading" custom-class="el-loading-spinner2" class="transition-box" :data="tableColumn" max-height="400">
        <el-table-column
          prop="d_mapping_field"
          label="参数名称"
          width="180"/>
        <el-table-column
          prop="e_field_desc"
          label="参数说明"
          width="180"/>
        <el-table-column
          prop="f_field_type"
          label="类型"
          width="180"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import {
  queryDictConfig, agentSenceList, SchemeList, getByIds, QueryAbilityData
} from '@/api/planGenerateApi.js';
import '@/style/github-markdown.css'
import mermaid from 'mermaid'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import { getQueryPage } from '@/api/queryPage';
import innerAxios from 'axios';

export default {
  data() {
    return {
      userInfo: {},
      ids: {},
      url: '',
      createUserId:'',
      agent_scene:'',
      PageTotal:0,
      isLoading: false,
      dialogTableVisible: false,
      tabPosition: '1',
      tabPosition2: '1',
      tableData: [],
      gridData: [],
      tableColumn: [{
        d_mapping_field: 'success',
        e_field_desc: '是否成功',
        f_field_type: 'boolean',
      },{
        d_mapping_field: 'code',
        e_field_desc: '返回code',
        f_field_type: 'string',
      },{
        d_mapping_field: 'message',
        e_field_desc: '返回消息',
        f_field_type: 'string',
      }],
      userList: [],
      pageParams: {
        pageNum: 1,
        pageSize: 10,
        abilityName: '',
        sceneType: ''
      },
      props: {
        lazy: true,
        lazyLoad (node, resolve) {
          const { level } = node;
          if (level === 0) {
            queryDictConfig({business_type: 'scene_type'}).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                const result = res.data.result?.config.map(item => {
                  return {
                    value: item.code,
                    label: item.name
                  }}
                );
                resolve( result || []);
              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            });
          } else {
            agentSenceList({name: '', scene_type: node.data.value}).then((res) => {
              if (res.status === 200 && res.data.code === 200) {
                const result = res.data.result
                if(result && result.length >0){
                  const options = result.map(item => {
                    return {
                      value: item.id,
                      label: item.name,
                      leaf: true
                    }}
                  );
                  resolve( options);
                  console.log('result', options);
                }else{
                  resolve( [{
                    value: '',
                    label:'暂无',
                    leaf: true,
                    disabled:true
                  }]);
                }

              } else {
                this.$message({
                  type: 'error',
                  message: res.data?.msg || '接口异常!'
                });
              }
            });
          }

        }
      }
    };
  },
  // 生命周期 - 挂载完成（访问DOM元素）
  async mounted() {
    this.userInfo = sessionStorage.getItem('USER_INFO') ? JSON.parse(sessionStorage.getItem('USER_INFO')) : {}
    this.handleChange(this.tabPosition);
  },
  methods: {
    handleCurrentChange(event){
      this.pageParams.pageNum = event
      this.getQueryPageList(+this.tabPosition === 2?this.userInfo.userId:'')
    },
    handleCascaderChange(event){
      this.pageParams.sceneType = event[event.length - 1]
      this.pageParams.pageNum = 1
      this.getQueryPageList(+this.tabPosition === 2?this.userInfo.userId:'')
    },
    searchUser(userName, callback) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data;
        if (callback) {
          this.$nextTick(callback);
        } else {
          this.createUserName = userName;
        }
      });
    },
    handleUserChange(){
      this.pageParams.pageNum = 1
      this.getQueryPageList(this.createUserId)
    },
    handleClose(){
      this.dialogTableVisible = false
    },
    handleChange(event){
      if (+event === 1 ) {
        this.userList = []
        this.createUserId = ''
        this.pageParams.pageNum = 1
        this.getQueryPageList()
      } else {
        this.userList = [
          {
            id: sessionStorage.getItem('userId'),
            nickname: sessionStorage.getItem('LOGIN_Name'),
            loginName: sessionStorage.getItem('loadItcode')
          }
        ];
        this.createUserId = sessionStorage.getItem('userId')
        this.pageParams.pageNum = 1
        this.getQueryPageList(sessionStorage.getItem('userId'))
      }
    },
    async getQueryPageList (userId = '') {
      try {
        this.isLoading = true
        const mechanismXKey = {
          dev: 'nNgvOaFowxDRtttq8G74oIUTwdAxUjrM',
          fat: 'P48VHX7vPz1ORjkizxRlmtq1EJo33l6D',
          production: 'LDPOUdvLalz50A63v6dWAC6CIuvGzknF'
        }
        const request = axios.create({
          baseURL: `${process.env.VUE_APP_LLM_API}/mechanism/ability/queryPage`,
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-GW-AccessKey': mechanismXKey[process.env.VUE_APP_ENV],
          },
        })
        request({
          method: 'POST',
          url: '',
          data: {
            ...this.pageParams,
            userId: userId
          },
        }).then(async (res) => {
          if (res.status === 200 && +res.data.code === 0) {
            const ids = res.data.data.map((item)=>{return item.sceneType})
            await this.getByIds(ids)
            this.tableData = res.data.data
            this.PageTotal = res.data.totalCount
          } else {
            this.$message({
              type: 'error',
              message: res.data?.msg || '接口异常!'
            });
          }
        }).finally(()=>{
          this.isLoading = false
        });
      } catch (error) {
        throw new Error(error)
      }
    },
    async getByIds (ids) {
      const rdfaUrl = process.env.VUE_APP_AGENT_API;
      const request = axios.create({
        baseURL: `${rdfaUrl}/api/scene/getByIds`,
        headers: {
          'Content-Type': 'application/json',
          'X-GW-AccessKey': process.env.VUE_APP_GATEWAY_KEY,
          whiteuservalidate: 'False'
        },
      })
      request({
        method: 'POST',
        url: '',
        data: {
          ids:ids
        },
      }).then(async (res) => {
        console.log(res);
        if (res.status === 200) {
           res.data.forEach((item)=>{
             this.ids[item.id] = `${item.scene_type_str}-${item.name}`
          })
          this.ids = JSON.parse(JSON.stringify(this.ids))
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      }).finally(()=>{
        this.isLoading = false
      });
    },
    async handleShow(event){
      this.url = event.apiUrl
      this.dialogTableVisible = true
      const result = await QueryAbilityData({
        scheme_id: event.schemeId
      })
      if (result.data.result?.config) {
        const config = result.data.result.config
        this.gridData = [];
        const temp = []
        if (config?.business_type === 'dataTableAlgMapV1') {
          // 临时改动，适配新结构
          config.data.forEach((item) => {
            if (item.d_mapping_field && item.b_type !== 'API') {
              temp.push({
                d_mapping_field: item.d_mapping_field,
                e_field_desc: item.e_mapping_desc || item.e_field_desc,
                f_field_type: item.f_field_type,
                isUpload: 'true'
              });
            }
          })
          // dataTableAlgMap类型的固定追加这两个字段
          temp.push({
            d_mapping_field: 'deviceId',
            e_field_desc: '设备id',
            f_field_type: 'string',
            isUpload: 'true'
          });
          temp.push({
            d_mapping_field: 'detectionTime',
            e_field_desc: '检测时间',
            f_field_type: 'dateTime',
            isUpload: 'true'
          });
          this.gridData = temp;
          console.log('数据', temp);
        }else if (config?.business_type === 'dataTableAlgMap') {
          // 临时改动，适配新结构
          config.content?.table_map?.data?.forEach((item) => {
            if (item.d_mapping_field) {
              temp.push({
                d_mapping_field: item.d_mapping_field,
                e_field_desc: item.e_mapping_desc || item.e_field_desc,
                f_field_type: item.f_field_type,
                isUpload: 'true'
              });
            }
          })
          // dataTableAlgMap类型的固定追加这两个字段
          temp.push({
            d_mapping_field: 'deviceId',
            e_field_desc: '设备id',
            f_field_type: 'string',
            isUpload: 'true'
          });
          temp.push({
            d_mapping_field: 'detectionTime',
            e_field_desc: '检测时间',
            f_field_type: 'dateTime',
            isUpload: 'true'
          });
          this.gridData = temp;
          console.log('数据', temp);
        }else{
          result.data.result.config.data.map(item => {
            if (item.d_mapping_field) {
              temp.push({
                d_mapping_field:item.d_mapping_field,
                e_field_desc:item.e_mapping_desc || item.e_field_desc,
                f_field_type:item.f_field_type,
                isUpload: 'true'
              });
            }
          })
          this.gridData = temp;
        }
      }
    },
    async handleNav(event){
      const param = {
        'offset': 1,
        'limit': 12,
        'sort_field': 'create_time',
        'order': 'desc',
        'status': '',
        'agent_scene': '',
        'name': '',
        'agent_scene_code': '',
        'user_id': '',
        'nickName': this.userInfo.nickName,
        'username': this.userInfo.username,
        scheme_id: event.schemeId
      };
      const result = await SchemeList(param)
      if (result.status === 200 && result.data.code === 200) {
        if (result.data.result.items.length === 0) {
          this.$message({
            type: 'error',
            message: '未查询到数据!'
          });
        } else {
          const item = result.data.result.items[0]
          this.$router.push({path: '/planGenerate/ConfTaskPlanchat', query: {
              ...this.$route.query, name: item.name, id: item.id, agent_id: item.agent_id, agent_scene_name: item.agent_scene_name, type: item.type, create_time: item.create_time, desc: item.description, status: item.status,agent_scene_code_name:item.agent_scene_code_name, agent_scene: item.agent_scene,agent_scene_code:item.agent_scene_code
            }})
        }
      } else {
        this.$message({
          type: 'error',
          message: result.data?.msg || '接口异常!'
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.page-container{
  width: calc(100% - 40px);
  height: calc(100% - 40px);
  background-color: #ffffff;
  margin: 20px;
  & .page-header{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 15px;
  }
  & .page-table-items {
    width: 100%;
    height: calc(100% - 120px);
    padding: 15px;
    overflow: auto;
    & .page-table-item{
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15px;
      border-top: 1px solid #ebecf0;
    }
    & .page-table-item2{
      width: calc(25% - 15px);
      float: left;
      margin-right: 15px;
      border-radius: 4px;
      cursor: pointer;
      min-width: 275px;
      margin-bottom: 15px;
      border: 1px solid #DCDDE0;
      &:hover {
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
      }
    }
  }
}
.hidden-sc{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 70%;
}
</style>

