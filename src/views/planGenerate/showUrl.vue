<template>
    <div class="full">
        <div class=" icon">
            <i class="el-icon-close font" @click="close"></i>
        </div>
        <iframe :src="urlPath" frameborder="0" ></iframe>
    </div>
</template>

<script>
export default {
    props: {
        urlPath: {
            type: String,
            required: true
        }
    },
    data() {
        return {
        };
    },
    methods: {
        close() {
            this.$emit('close')
        }
    },
 
}
</script>

<style lang="postcss" scoped>
.full {
    width: 100%;
    height: calc(100vh - 261px);
    position: absolute;
    overflow: auto;
    /* background: rgba(0, 0, 0, 0.5); */
    display: flex;
    justify-content: center;
    align-items: center;
}

iframe {
    position: absolute;
    /* top: 20px; */
    width: 100%;
    /* 撑满父容器的宽度 */
    height: 100%;
    /* 撑满父容器的高度 */
    border: none;
    background: #fff;
    /* 去掉边框 */
}

.icon {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 11;
    font-size: 22px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
}

.font {
    color: #fff;
    font-size: 20px;
}
</style>