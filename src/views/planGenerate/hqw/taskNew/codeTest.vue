<template>
  <div
    class="task-card-content"
    v-loading="loading"
    element-loading-text="加载中..."
    element-loading-spinner="el-icon-loading"
  >
    <div v-if="status !== 0">
      <MonacoEditor
        id="codeTest"
        class="editor"
        v-if="deployStatus && !errorFlag"
        :value="resultData"
        language="json"
        :scroll-beyond-last-line="true"
        theme="vs-dark"
        :diff-editor="false"
        :options="options"
      />
      <el-result v-if="errorFlag && !loading" icon="error" subTitle="代码测试失败" />
      <!-- <el-result v-else icon="warning" subTitle="代码部署成功后会自动执行代码测试"/> -->
    </div>
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import MonacoEditor from '@/components/MonacoEditor';
// import Status from '@/components/Status/index.vue';
import { GetDecision, AbilityTest } from '@/api/planGenerateApi.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  components: { MonacoEditor },
  props: {
    updateTestFlag: {
      type: [Number, String],
      default: 1
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    status: {
      async handler(val) {
        console.log('测试状态', val);
        if (Number(val) === 3) {
          this.loading = true;
        }
        if (Number(val) === 1) {
          this.deployStatus = true;
          this.errorFlag = false;
          this.loading = true;
          GetDecision({ scheme_id: this.$route.query.id, scheme_status: 'decision_ability' }).then(
            async (res) => {
              if (
                res.data.result?.ext_info?.deploy_status &&
                res.data.result?.ext_info?.deploy_status === 'deployed'
              ) {
                // 调用接口
                const codeData = res.data.result?.decision_making_content || '';
                await AbilityTest({
                  code_str: codeData,
                  scheme_id: this.$route.query.id
                }).then((resTest) => {
                  console.log('测试结果33', resTest);
                  this.loading = false;
                  if (resTest?.data?.code !== 200) {
                    this.$emit('handleOk', false);
                    this.errorFlag = true;
                    // this.$message.error(resTest?.data?.msg || '测试失败');
                  } else {
                    this.errorFlag = false;
                    this.$emit('handleOk', true);
                    try {
                      this.resultData = resTest.data.result?.resp
                        ? JSON.stringify(resTest.data.result?.resp, null, 2)
                        : '';
                    } catch (error) {
                      this.resultData = resTest.data.result?.resp || '';
                    }
                  }
                });
              } else {
                this.deployStatus = false;
                this.resultData = '';
              }
            }
          );
        }
        if (Number(val) === 2) {
          this.deployStatus = true;
          this.errorFlag = true;
        }
      },
      immediate: true
    },
    updateTestFlag: {
      handler(val) {
        if (val !== 1) {
          this.handleInit();
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      loading: false,
      resultData: '',
      deployStatus: false, // 代码部署状态
      errorFlag: true, // 代码测试状态
      options: {
        selectOnLineNumbers: true,
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: true,
        theme: 'vs-dark',
        language: 'json',
        wordWrap: 'on', // 设置自动换行
        formatOnType: true, // 设置自动格式化
        formatOnPaste: true, // 设置自动格式化
        insertSpaces: true, // 设置缩进方式为插入空格
        tabSize: 2, // 设置缩进大小为2
        minimap: {
          enabled: false // 不要小地图
        },
        fontFamily:
          'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
        folding: true
      }
    };
  },
  methods: {
    async handleInit() {
      this.loading = true;
      console.log('初始化');
      await GetDecision({
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_ability'
      }).then(async (res) => {
        if (
          res.data.result?.ext_info?.deploy_status &&
          res.data.result?.ext_info?.deploy_status === 'deployed'
        ) {
          this.deployStatus = true;
          // 调用接口
          const codeData = res.data.result?.decision_making_content || '';
          await AbilityTest({
            code_str: codeData,
            scheme_id: this.$route.query.id
          }).then((resTest) => {
            console.log('测试结果33', resTest);
            this.loading = false;
            if (resTest?.data?.code !== 200) {
              this.$emit('handleOk', false);
              this.errorFlag = true;
              // this.$message.error(resTest?.data?.msg || '测试失败');
            } else {
              this.errorFlag = false;
              this.$emit('handleOk', true);
              this.resultData = resTest.data.result?.resp
                ? JSON.stringify(resTest.data.result?.resp, null, 2)
                : '';
            }
          });
        } else {
          this.deployStatus = false;
          this.resultData = '';
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.task-card-content {
  // padding: 16px 20px;
  height: 100%;
  // min-height: 504px;

  .editor {
    min-height: calc( 70vh - 144px - 96px );

    height: 100%;
    width: 100%;
    margin-left: 1px;
  }
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
