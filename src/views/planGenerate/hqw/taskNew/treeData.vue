<template>
  <div
    class="task-card-content"
    id="taskTable"
    v-loading="loading"
    element-loading-text="思维树生成中..."
    element-loading-spinner="el-icon-loading"
  >
    <div class="transition-box" v-if="status !== 0">
      <div v-if="treeStatus == 1 || treeStatus == 0" class="optContentBox">
        <vue-markdown :source="treeData"></vue-markdown>
      </div>
      <div v-else-if="treeStatus == 3 || status===2" style="width: 100%; height: 100%">
        <div
          style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
          "
        >
          <img
            src="@/assets/images/planGenerater/runerror.png"
            style="width: 180px; height: auto"
          />
          <div
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              margin-top: 16px;
            "
          >
            思维树生成失败
          </div>
        </div>
      </div>
      <div v-else class="optContentBox">
        <div class="optContentBox" @mouseenter="fangda">
          <MyEditorPreview id="MyEditor4" ref="MyEditor4" :md-content="treeData"></MyEditorPreview>
        </div>
      </div>
    </div>
    <div v-else-if="status === 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
    <div v-else>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        "
      >
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px; height: auto" />
        <div
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-top: 16px;
          "
        >
          暂无
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import { GetDecision } from '@/api/planGenerateApi.js';
import panzoom from 'panzoom';
import MyEditorPreview from '../../mdEditorPreview.vue';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  components: { MyEditorPreview },
  props: {
    treeStatus: {
      type: Number,
      default: -1
    },
    treeDataVal: {
      type: String,
      default: ''
    },
    status: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    treeStatus: {
      handler(val) {
        if (val === 2) {
          console.log('重新生成完成');
          this.taskLoading = false;
        } else if (val === 1) {
          this.taskLoading = true;
        } else if (val === 0) {
          this.taskLoading = false;
        } else if (val === 3) {
          // 生成失败
          this.taskLoading = false;
        }
      },
      immediate: true
    },
    treeDataVal: {
      handler(val) {
        this.treeData = val;
      },
      immediate: true
    },
    status: {
      handler(val) {
        console.log('tree status', val);
        if (Number(val) !== 3) {
          this.loading = false;
        }
        if(Number(val) === 1) {
          this.handleInit();
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      dataThDatas: [],
      taskLoading: false,
      treeData: '',
      treeDataProcess: '',
    };
  },
  async mounted() {
    
  },
  methods: {
    fangda(e) {
      // console.log('开启缩放', e.target.getElementsByTagName('svg'));
      const svgdoms = e.target.getElementsByTagName('svg');
      const arr = [...svgdoms];
      arr.forEach((svgdom) => {
        if (svgdom.id.indexOf('mermaid') > -1) {
          panzoom(svgdom, {
            smoothScroll: false,
            bounds: true,
            // autocenter: true,
            zoomDoubleClickSpeed: 1,
            minZoom: 0.1,
            maxZoom: 20
          });
        }
      });
    },
    async handleInit(id) {
      this.loading = true;
      const params = {
        scheme_id: this.$route.query.id,
        scheme_status: 'decision_tree'
      };
      await GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.$emit('handleUpdateTreeData', res.data.result?.decision_making_content || '');
          this.treeData = res.data.result?.decision_making_content || '';
          this.treeDataProcess = res.data.result?.sub_content || '';
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.task-card-content {
  padding: 16px 20px;
  min-height: 200px;
}
:deep(.el-table--medium .el-table__cell) {
  padding: 8px 0px !important;
}
::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table .el-table__cell.gutter {
  background: #f6f7fb;
  border-bottom: 1px solid #ebecf0;
}
::v-deep .el-table th.el-table__cell:not(.no-bor) > .cell::after {
  content: '';
  position: absolute;
  right: 0;
  top: 4px;
  height: 16px;
  width: 1px;
  background: #dcdde0;
}
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
