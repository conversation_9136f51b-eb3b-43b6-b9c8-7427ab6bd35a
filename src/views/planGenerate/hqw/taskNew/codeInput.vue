<template>
  <div class="editor">
    <el-input
      v-model="showCodeData"
      :disabled="options.readOnly"
      type="textarea"
      :rows="2"
      placeholder="请输入内容"
      ref="myTextarea"
    >
    </el-input>
  </div>
</template>

<script>
export default {
  props: {
    runStreamList: String
  },
  data() {
    return {
      showCodeData: '',
      options: {
        readOnly: true
      }
    };
  },
  watch: {
    runStreamList: {
      handler(val) {
        this.showCodeData = val;
        this.$nextTick(() => {
          const textarea = this.$refs.myTextarea.$el.querySelector('textarea');
          textarea.scrollTop = textarea.scrollHeight;
        });
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="scss" scoped>
.editor {
  padding: 16px 20px;
  ::v-deep .el-textarea__inner {
    height: 504px;
  }
}
</style>
