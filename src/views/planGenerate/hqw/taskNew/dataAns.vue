<template>
    <div class="task-card-content" v-if="templateId && status !== 0" v-loading="loading" element-loading-text="多模态数据对齐分析中..." element-loading-spinner="el-icon-loading">
      <div class="headerTip">
        <div class="tipIcon"><img slot="icon" src="@/assets/images/planGenerater/jiqiren.png"></div>
        <div class="tipDesc" v-if="!allSuccess">将为您自动分析对齐范围：{{taskList.map(item => item.name).join('、')}}，请稍后...</div>
        <div class="tipDesc" v-else>已为您成功分析完成对齐范围：{{taskList.map(item => item.name).join('、')}}</div>
      </div>
      <div style="margin-top: 16px;" class="taskBox" id="taskBox">
        <div class="taskCard">
          <div class="title"><img src="@/assets/images/planGenerater/start.png"/>开始一个新目标：<span class="subTitle">{{taskList.map(item => item.name).join('、')}}</span></div>
        </div>
        <div v-for="(item, index) in taskList" :key="index">
          <div v-if="item.status !=='start'" class="taskCard">
            <div class="title">
              <div style="display: flex;align-items:center;"><img :src="item.status === 'running' ? require('@/assets/images/planGenerater/task-running.png'):(item.status === 'error' ? require('@/assets/images/planGenerater/task-error.png'):require('@/assets/images/planGenerater/task-success.png'))"></div>
              <div class="title">{{item.status === 'running' ? '任务执行中': '完成任务'}}: <span class="subTitle">【{{item.name}}】</span></div>
            </div>
            <div class="desc">{{item.content}}</div>
          </div>
        </div>
        <div v-if="allSuccess">
          <div class="taskCard">
            <div class="title"><span class="subTitle">所有任务完成</span></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="status !== 0">
      <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
        <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto"/>
        <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">对齐分析模版ID为空，请转研发进行处理。</div>
      </div>
    </div>
    <div v-else-if="status === 0">
      <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px;height: auto"/>
        <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">暂无</div>
      </div>
    </div>
    <div v-else>
      <div style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
        <img src="@/assets/images/planGenerater/empty.png" style="width: 180px;height: auto"/>
        <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">暂无</div>
      </div>
    </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue';
import {  queryTempIfFromScene, queryAbilityMapping} from '@/api/planGenerateApi.js';
// import panzoom from 'panzoom';
// import MyEditor from '../../mdEditor.vue';

export default {
  name: 'ExampleCom',
  // components: { Status, MyEditor },
  props: {
    runStreamList: {
      type: Array,
      default: []
    },
    allSuccess: {
      type: Boolean,
      default: false
    },
    templateId:  {
      type: [String, Number],
      default: ''
    },
    status:  {
      type: [String, Number],
      default: ''
    },
  },
  watch: {
    runStreamList: {
      handler(val) {
        console.log('流式变化',val);
        this.taskList = val
        const temp = document.getElementById('taskBox');
        if (temp) {
          temp.scrollIntoView({ block: 'end', inline: 'nearest' });
        }
      },
      immediate: true
    },
    status: {
      handler(val) { 
        console.log('第一次333',this.runStreamList);
        if (Number(val) === 1) {
          this.taskList = this.runStreamList
          const temp = document.getElementById('taskBox');
          if (temp) {
            temp.scrollIntoView({ block: 'end', inline: 'nearest' });
          }
        } 
      },
      immediate: true
    },
  },
  async mounted() {
    console.log('第一次',this.runStreamList);
  },
  data() {
    return {
      loading: false,
      taskList: [],
    };
  },
  methods: {
  }
};
</script>
<style lang="scss" scoped>
    .task-card-content {
      // padding: 16px 20px;
    }
    .headerTip {
      display: flex;
      align-items: center;
      .tipIcon {
        width: 48px;
        height: 48px;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .tipDesc {
        flex: 1;
        background: #EFF3FF;
        margin-left: 13px;
        position: relative;
        padding: 8px 16px; 
        font-size: 14px;
        line-height: 20px;
        color: #323233;
        border-radius: 6px;
        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          border-top: 5px solid transparent; /*左边透明*/
          border-bottom: 5px solid transparent; /*右边透明*/
          border-right: 8px solid #EFF3FF; /*底部为黑色线条*/
        }
      }
    }
    .taskCard {
      border: 1px solid #DCDDE0;
      border-radius: 4px;
      padding: 10px 12px;
      margin-bottom: 12px;
      .title {
        color: #323233;
        font-weight: bold;
        line-height: 20px;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
          margin-right: 10px;
        }
        .subTitle {
          font-weight: normal !important;
        }
      }
      .desc{
        color: #646566;
        margin-top: 12px;
        line-height: 22px;
      }
    }
:deep(.el-loading-spinner) {
  width: 130px !important;
  background: none !important;
}
</style>
