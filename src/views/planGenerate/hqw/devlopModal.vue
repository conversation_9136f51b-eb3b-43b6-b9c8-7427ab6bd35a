<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      :title="isPersonFlag ?'转研发工单':'选择人员'"
      :visible.sync="showFlag"
      :before-close="onClose"
      :close="onClose"
      append-to-body
      width="56%"
    >
      <div :loading="loading">
        <div v-if="isPersonFlag">根据方案及场景，推荐交由以下研发人员处理，也可以<a @click="() => {isPersonFlag = false; validateForm.ownerId = ''}">重新选择</a></div>
        <div v-if="isPersonFlag" style="display: flex;align-items: center;margin: 16px 0px;">
          <el-avatar src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
          <div style="margin-left: 8px">{{contributors.nick_name+'('+contributors.user_name+')'  }}</div>
        </div>
        <div style="margin: 16px 0px;display: flex;align-items: center;">
          <el-form ref="validateForm" :model="validateForm" :rules="rules" style="width:100%">
            <el-form-item
              v-if="!isPersonFlag"
              prop="ownerId"
            >
              <el-select
                ref="onwerSelectRef"
                v-model="validateForm.ownerId"
                filterable
                style="width:100%"
                remote
                placeholder="请选择用户"
                :remote-method="searchUser"
                clearable
                @change="changeUser"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="`${item.nickname}${item.loginName ? ('('+item.loginName+')') : ''}`"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="isPersonFlag" prop="remark">
              <el-input
                v-model="validateForm.remark"
                type="textarea"
                maxlength="500"
                :autosize="{ minRows: 6, maxRows: 10}"
                placeholder="可录入其他补充说明">
              </el-input>
            </el-form-item>
        </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isPersonFlag" type="primary" :loading="loading" :disabled="loading" @click="() => onSubmitPerson()">确定</el-button>
        <el-button v-if="!isPersonFlag" type="info" :loading="loading" :disabled="loading" @click="cancel">取消</el-button>
        <el-button v-if="isPersonFlag" type="primary" :loading="loading" :disabled="loading" @click="saveRelHandle">提交</el-button>
        <el-button v-if="isPersonFlag" type="info" :loading="loading" :disabled="loading" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  saveRel,
} from '@/api/planGenerateApi.js';
import { cloneDeep,isEmpty } from 'lodash';
export default {
  name: 'RunTaskDialog',
  components: {},
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    devPerson:{
      type: Object,
      default: ()=>{
        return {}
      }
    },
    curId:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      showFlag: false,
      validateForm:{
        ownerId: null,
        remark: '',
      },
      userList: [],
      contributors:{},
      personData:{},
      isPersonFlag:false,
      rules: {
        ownerId:[{ required: true, message: '用户不能为空'}]
      }
    };

  },
  watch: {
    isVisible: {
      handler(val) {
        if (val) {
          console.log(this.devPerson,'this.devPerson',this.isPersonFlag)
          if(!isEmpty(this.devPerson)){
            this.personData = cloneDeep(this.devPerson)
            this.contributors={...this.personData}
            this.validateForm.remark = this.personData.remark
            this.isPersonFlag = true
          }
          console.log('flag',this.isPersonFlag)
          this.showFlag = val;
        } else {
          this.showFlag = false;
        }
      },
      immediate: true
    },
  },
  methods: {
    cancel(){
      if(isEmpty(this.devPerson)){
        this.$emit('close', false)
      } else {
        this.$refs.validateForm.clearValidate('ownerId');
        this.isPersonFlag = true
      }
    },
    onSubmitPerson(){
      this.$refs.validateForm.validate((valid) => {
        if (valid) {
          this.isPersonFlag = true
          this.validateForm.remark = ''

        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    async saveRelHandle(){
      console.log(this.contributors,'save')
      this.loading = true;
      await saveRel({
        biz_id:this.curId,
        biz_type:'task',
        scheme_name:this.curName,
        developer:this.contributors.user_name,
        remark:this.validateForm.remark

      }).then((res) => {
        console.log(res, '000');
        if (res.status === 200 && res.data.code === 200 && res.data.result) {
          this.isPersonFlag = false
          
          this.$emit('close', {
            nick_name: this.contributors.nick_name,
            remark: this.validateForm.remark,
            user_id: this.validateForm.ownerId,
            user_name: this.contributors.user_name
          });
          this.contributors = {}
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    onClose() {
      this.$emit('close', false);
    },

    searchUser(userName) {
      this.$post('/user/getAllUserListByUserName', {
        userName: userName
      }).then((data) => {
        this.userList = data
      })
    },
    changeUser(val) {
      console.log('val', val);
      this.$nextTick(()=>{
        const newData = this.$refs.onwerSelectRef?.cachedOptions.filter(item => item.value === val)||[]
        if(newData.length > 0){
          const curDev = newData[0]
          this.contributors = {
            nick_name:curDev.currentLabel.slice(0, curDev.currentLabel.indexOf('(')),
            user_name:curDev.currentLabel.match(/\((.+)\)/)[1],
            id:curDev.currentValue,
          }
        }
      })
    },
  }
};
</script>
<style lang="scss">

</style>
