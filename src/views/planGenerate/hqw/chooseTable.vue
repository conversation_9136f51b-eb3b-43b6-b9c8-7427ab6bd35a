<template>
  <div>
    <el-dialog
      custom-class="last-dialog"
      title="AI对齐范围分析"
      :visible.sync="showFlag"
      :before-close="onClose"
      width="40%"
    >
      <div>
        <div class="headerTip">
          <div class="tipIcon"><img slot="icon" src="@/assets/images/planGenerater/jiqiren.png"></div>
          <div class="tipDesc" v-if="activeStep === 0 && !allSuccess">将为您自动分析对齐范围：{{runStreamList.map(item => item.name).join('、')}}，请稍后...</div>
          <div class="tipDesc" v-else>点击确定，将保存对齐范围。</div>
        </div>
        <div style="margin-top: 16px;" class="taskBox" id="taskBox" v-if="activeStep === 0">
          <div class="taskCard">
            <div class="title"><img src="@/assets/images/planGenerater/start.png"/>开始一个新目标：<span class="subTitle">{{runStreamList.map(item => item.name).join('、')}}</span></div>
          </div>
          <div v-for="(item, index) in runStreamList" :key="index">
            <div v-if="item.status !=='start'" class="taskCard">
              <div class="title">
                <div style="display: flex;align-items:center;"><img :src="item.status === 'running' ? require('@/assets/images/planGenerater/task-running.png'):(item.status === 'error' ? require('@/assets/images/planGenerater/task-error.png'):require('@/assets/images/planGenerater/task-success.png'))"></div>
                <div class="title">{{item.status === 'running' ? '任务执行中': '完成任务'}}: <span class="subTitle">【{{item.name}}】</span></div>
              </div>
              <div class="desc">{{item.content}}</div>
            </div>
          </div>
          <div v-if="allSuccess">
            <div class="taskCard">
              <div class="title"><span class="subTitle">所有任务完成</span></div>
            </div>
          </div>
        </div>
        <div style="margin-top: 16px" v-if="activeStep === 1">
          <!-- 只有物联诊断场景有库表选择 -->
          <div style="display:flex;flex-direction: column;align-items: flex-start;margin-bottom: 16px;">
            <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox v-model="formData.cangku" @change="changeCangku">数据仓库对齐：</el-checkbox></div>
            <el-select v-model="formData.chooseData" clearable :multiple="true" style="width: 100%;flex:1" placeholder="请选择">
              <el-option
                v-for="item in tablesList"
                :key="item.device_id"
                :label="item.device_name"
                :value="item.device_name">
                <span style="float: left">{{ item.device_name }}</span>
                <!-- <span style="float: right; color: #8492a6; font-size: 12px">{{ item.description }}</span> -->
              </el-option>
            </el-select>
          </div>
          <div style="display:flex;flex-direction: column;align-items: flex-start;margin-bottom: 16px;">
            <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox v-model="formData.api" @change="changeAPI">算法API对齐：</el-checkbox></div>
            <el-select
                ref="tagsSelect"
                v-model="formData.tag_ids"
                style="width:100%;flex:1"
                multiple
                filterable
                remote
                placeholder="请选择"
                :remote-method="searchTags"
                clearable
                :popper-append-to-body="false"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
          </div>
          <div style="display:flex;flex-direction: row;align-items: center;margin-bottom: 16px;">
            <div style="color:#1D2129;font-weight: bold;margin-bottom: 8px;"><el-checkbox v-model="formData.manual_input">人工输入</el-checkbox></div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="activeStep === 0" :disabled="!allSuccess || loading" @click="nextStep">人工修改</el-button>
        <el-button type="primary" :disabled="!allSuccess || loading" @click="createSubmit">确定</el-button>
        <el-button type="info" :loading="loading" v-if="activeStep === 1 && templateId" :disabled="loading" @click="redo">重新对齐范围</el-button>
        <el-button type="info" :loading="loading" :disabled="loading" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { queryDeviceIds, queryDuiqiTags, queryTaskByTemp, queryPointsByDeviceId } from '@/api/planGenerateApi.js';
import axios from 'axios'
const cancelToken = axios.CancelToken
let source = cancelToken.source()
export default {
  name: 'ModelDialog',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    conSelect: {
      type: String,
      default: ''
    },
    editData: {
      type: Object,
      default: () => {
        return {"table":[], "tag_ids":[], manual_input: false}
      }
    },
    templateId: {
      type: String,
      default: ''
    },
    treeData: {
      type: String,
      default: ''
    },
    detailContent: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      activeStep: 0,
      showFlag: false,
      formData: {
        chooseData: [],
        cangku: false,
        api: false,
        manual_input: true,
        tag_ids: []
      },
      tablesList: [],
      rules: {
        chooseData: [{ required: true, message: '请选择数据表', trigger: 'blur' }],
      },
      loading: false,
      tagList: [],
      allTagList: [],
      allRunTasks: [], // 所有需要执行的任务
      runId: '', // 执行需要的id,
      runStreamList: [],
      allSuccess: false
    };
  },
  watch: {
    isVisible: {
      async handler(val) {
        if (val) {
          this.showFlag = val
         
          await this.queryDataInfo();
          await this.searchTags2('');
          console.log('回显数据',this.templateId, this.editData, this.editData.manual_input, this.editData.tag_ids, this.editData.table);
         
          const flag = this.editData.manual_input || (this.editData.tag_ids && this.editData.tag_ids.length) || (this.editData.table && this.editData.table.length);
          console.log('flag', flag);
          if (this.templateId && !flag) {
            this.activeStep = 0;
            this.$nextTick(() => {
              console.log('this.tab---', this.tablesList);
              this.queryStart();
            })
          } else {
            if (this.editData.ansData) {
              this.runStreamList = JSON.parse(this.editData.ansData) || []
            }
            if(this.editData.manual_input) {
              this.formData.manual_input = true
            } else {
              this.formData.manual_input = false
            }
            this.allSuccess = true;
            this.activeStep = 1;
          }
        } else {
          this.showFlag = false;
        }
      },
      immediate: true, deep: true
    }
  },
  beforeDestroy() {
    source.cancel()
    source = cancelToken.source()
  },
  methods: {
    nextStep() {
      this.activeStep = 1;
    },
    redo () {
      this.allSuccess = false;
      this.runStreamList = [];
      this.activeStep = 0;
      this.formData.api = false;
      this.formData.tag_ids = [];
      this.formData.cangku = false;
      this.formData.chooseData = [];
      this.formData.manual_input= true;
      this.queryStart();
    },
    async queryStart() {
      this.loading = true;
      console.log('this.tablesList', this.tablesList);
      await queryTaskByTemp({
        template_id: this.templateId,
        scheme_detail: this.detailContent,
        mind_tree: this.treeData,
        device_info: JSON.stringify(this.tablesList[0])
      }).then(async(res) => {
        console.log('需执行的任务列表', res.data);
        this.allRunTasks = res.data.new_tasks || [];
        this.runId = res.data.run_id || '';
        res.data.new_tasks.forEach(async (item, index) => {
          this.runStreamList.push({
            name: item.name,
            content: '',
            status: 'start',
            type: 1,
            ...item
          });
        })
      })
      const temp = [];
      if (this.runStreamList.length) {
        this.allSuccess = false;
        await this.runExcute(this.runStreamList[0].id, this.runStreamList[0].order, 0)
      }
      console.log('promise1', temp);
    },
    async runExcute(id, order, index) {
      const url = process.env.VUE_APP_AGENT_API.startsWith('/') ? window.location.origin + process.env.VUE_APP_AGENT_API + "/api/agent/v2/manual_task/execute" : process.env.VUE_APP_AGENT_API +  "/api/agent/v2/manual_task/execute"
      console.log('url', url)
      await this.$axios.post(url, {
        template_id: this.templateId,
        run_id: this.runId,
        agent_id: this.runId,
        task_id: id,
        order: order
      }, {
        responseType: "stream",
        baseURL: process.env.VUE_APP_AGENT_API,
        headers: {
          whiteuservalidate: 'False'
        },
        cancelToken:source.token,
        onDownloadProgress:(event) => {
          const xhr = event.target;
          const { responseText } = xhr;
          this.$nextTick(() => {
            // console.log('----流----',this.runStreamList[index])
            this.runStreamList[index].status = 'running';
            this.runStreamList[index].content = responseText;
            const temp = document.getElementById('taskBox');
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' });
              temp.scrollTop = temp.scrollHeight + 200;
            }
          })
        },
        onError: function(error) {
          // 处理流错误
          console.error(error);
          this.runStreamList[index].status = 'error';
          this.runStreamList[index].content = '';
        }
      }).then(async response => {
        // 关闭数据流
        console.log('数据流',response);
        this.runStreamList[index].status = 'success';
        this.$nextTick(() => {
            const temp = document.getElementById('taskBox');
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' });
              temp.scrollTop = temp.scrollHeight + 700;
            }
          })
        if (this.runStreamList[index].name === '数据仓库对齐') {
          if (this.runStreamList[index].content) {
            let valTemp = []
            try {
              valTemp = eval(this.runStreamList[index].content);
            } catch (error) {
              valTemp = this.runStreamList[index].content.split('\n') || [this.runStreamList[index].content]
            }
            console.log('valTemp---', valTemp);
            if (valTemp && valTemp.length) {
              this.formData.cangku = true;
              const filters = this.tablesList.filter(item => valTemp.includes(item.device_id)).map(item => item.device_name)
              this.formData.chooseData = filters;
              console.log('库表的选择', valTemp, filters);
              this.runStreamList[index].content = JSON.stringify(filters)
              this.$set(this.formData, 'chooseData', filters || []);
            } else {
              this.formData.cangku = false;
              this.formData.chooseData = [];
            }
          } else {
            this.formData.cangku = false;
            this.formData.chooseData = [];
          }
        }
        if (this.runStreamList[index].name === '算法API对齐') {
          if (this.runStreamList[index].content) {
            const valTemp = eval(this.runStreamList[index].content)
            if (valTemp && valTemp.length) {
              const filters = [];
              valTemp.forEach(teItem => {
                const ffilter = this.allTagList.filter(tag => tag.name === teItem)
                if(ffilter && ffilter.length) {
                  filters.push(ffilter[0].id)
                }
              })
              this.formData.api = true;
             
              // this.formData.tag_ids = [...filters] || [];
              this.$set(this.formData, 'tag_ids', filters || '');
              console.log('少选出的', filters, this.formData.tag_ids);
            } else {
              this.formData.api = false;
              this.formData.tag_ids = [];
            }
          } else {
            this.formData.api = false;
            this.formData.tag_ids = [];
          }
        }
        if (index+1 < this.runStreamList.length) {
          this.$nextTick(async () => {
            await this.runExcute(this.runStreamList[index+1].id, this.runStreamList[index+1].order, index+1)
          })
        } else {
          this.allSuccess = true;
          this.loading = false;
          console.log('最后的结果', this.runStreamList);
          this.formData.manual_input = true;
          this.$nextTick(() => {
            const temp = document.getElementById('taskBox');
            if (temp) {
              temp.scrollIntoView({ block: 'end', inline: 'nearest' });
              temp.scrollTop = temp.scrollHeight + 700;
            }
          })
        }
      }).catch(() => {
        this.allSuccess = true;
        this.loading = false;
        this.runStreamList[index].status = 'error';
        this.runStreamList[index].content = '';
      })
    },
    async queryDataInfo() {
      this.loading = true;
      const params= {scheme_id: this.$route.query.id};
      console.log('选择表数据', params);
      await queryDeviceIds(params).then((res) => {
        this.loading = false;
        if (res.status === 200 && res.data.code === 200) {
          this.tablesList = res.data?.result || [];
          console.log('0000', res.data, this.editData, this.tablesList)
          if (this.editData.table && this.editData.table.length) {
            this.formData.cangku = true;
            const filters = res.data?.result.filter(item => this.editData.table.includes(item.device_id)).map(item => item.device_name)
            this.formData.chooseData = filters;
            // this.formData.chooseData = this.editData.table;
          } else {
            this.formData.cangku = false;
            this.formData.chooseData = []
          }
          console.log('this.formData.cangku', this.formData.cangku, this.formData.chooseData);
        }
      }).finally(() => {
        this.loading = false;
      });;
    },
    searchTags(keyword) {
      queryDuiqiTags({
        keyword
      }).then(res => {
        if(res.data){
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
          }
        } else {
          this.tagList = [];
        }
      })
    },
    searchTags2(keyword) {
      queryDuiqiTags({
        keyword
      }).then(res => {
        if(res.data){
          this.tagList = res.data;
          if (keyword === '') {
            this.allTagList = res.data;
            if (this.editData && this.editData.tag_ids?.length) {
              this.formData.api = true;
              const temp = [];
              // 循环判断标签是否有被删除，被删除的就不显示了
              this.editData.tag_ids?.forEach(titem => {
                const filter = res.data.filter(item  => item.id === titem)
                if (filter.length) {
                  temp.push(filter[0].id);
                }
              });
              this.formData.tag_ids = temp;
            } else {
              this.formData.api = false;
              this.formData.tag_ids = []
            }
          }
        } else {
          this.tagList = [];
        }
      })
    },
    changeCangku(val) {
      if (!val) {
        this.formData.chooseData = []
      }
    },
    changeAPI(val) {
      if (!val) {
        this.formData.tag_ids = [];
      }
    },
    onClose() {
      this.formData.chooseData = []
      this.formData.tag_ids = [];
      this.formData.cangku = false;
      this.formData.api = false;
      this.loading = false;
      this.showFlag = false;
      this.allSuccess = false;
      this.runStreamList = [];
      this.activeStep = 0;
      this.formData.manual_input= true;
      this.$emit('close');
    },
    createSubmit() {
      console.log(this.formData);
      if (this.formData.api || this.formData.cangku) {
        if ((this.formData.api && this.formData.tag_ids.length) || (this.formData.cangku && this.formData.chooseData?.length)) {
          let temp = []
          const tableids = [];
          console.log(' this.formData.chooseData',  this.formData.chooseData, this.tablesList.filter(item => this.formData.chooseData.includes(item.device_id)));
          this.tablesList.filter(item => this.formData.chooseData.includes(item.device_name)).map(async item => {   
            const promise1 = queryPointsByDeviceId({device_id: item.device_id});
            temp.push(promise1);
            tableids.push(item.device_id);
          });
          console.log('resultF', tableids);
          let rtemp = [];
          this.formData.chooseData = tableids;
          Promise.all(temp).then((results) => {
            console.log('获取测试数据方法results==', results, tableids);
            results.forEach((resList, rindex) => {
              if (resList) {
                temp[rindex].fieldsList = resList.data?.data;
                temp[rindex].isAdd = false;
                let newarr = rtemp.concat(resList.data?.result || []);
                rtemp = newarr;
              }
            })
            this.$nextTick(() => {
              console.log('rtemp', rtemp);
              console.log('tableids---', tableids);
              if (tableids.length) {
                this.$emit('close', JSON.stringify(rtemp), tableids, this.formData.tag_ids, this.formData.manual_input, JSON.stringify(this.runStreamList));
              } else {
                this.$emit('close', 'close', tableids, this.formData.tag_ids, this.formData.manual_input, JSON.stringify(this.runStreamList));
              }
              this.allSuccess = false;
              this.runStreamList = [];
              this.activeStep = 0;
              this.formData.api = false;
              this.formData.tag_ids = [];
              this.formData.cangku = false;
              this.formData.chooseData = []
              this.formData.manual_input= true;
            })
          });
          
        }else {
          this.$message({
            type: 'error',
            message: '请选择配置并配置数据源后再进行对齐!'
          });
        }
      } else {
        if (this.formData.manual_input) {
          this.$emit('close', 'close', this.formData.chooseData, this.formData.tag_ids, this.formData.manual_input);
        } else {
          this.$message({
            type: 'error',
            message: '请选择任意一个配置后再进行对齐!'
          });
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-select-dropdown__item) {
  max-width: 700px;
  /* 设置文本溢出时的行为为省略号 */
  text-overflow: ellipsis;
  
  /* 设置超出容器的内容应该被裁剪掉 */
  overflow: hidden;
  
  /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
  white-space: nowrap;
}
.myStep{
  background: #fff;
  padding: 13px 20px;
  :deep(.el-step__arrow){
    margin:0 16px;
    &::before{
      content: '';
      position:static;
      height: 1px;
      width: 100%;
      background: #C8C9CC;;
      transform:none;
      display:block
    }
    &::after{
      display: none;
    }
  }
  :deep(.is-process){
    color:#4068D4;
    .el-step__icon{
      color:#4068D4;
      &.is-text{
        border: none;
      }
    }
  }
  :deep(.is-success){
    color:#000;
    border-color: #4068D4;
    .el-icon-check{
      color:#4068D4;
    }
    .el-step__icon{
      color:#4068D4;
      &.is-text{
        border: 1px solid #4068D4;
      }
    }
  }
  .empty-space{
    width: 100%;
    height: 100%;
  }
}
::v-deep(.el-checkbox__label) {
  font-weight: bold;
  color: #1D2129;
}
.taskBox {
  max-height: 100%;
  height: 100%;
  overflow-y: auto;
}
.taskCard {
  border: 1px solid #DCDDE0;
  border-radius: 4px;
  padding: 10px 12px;
  margin-bottom: 12px;
  .title {
    color: #323233;
    font-weight: bold;
    line-height: 20px;
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
    .subTitle {
      font-weight: normal !important;
    }
  }
  .desc{
    color: #646566;
    margin-top: 12px;
    line-height: 22px;
  }
}
.headerTip {
  display: flex;
  align-items: center;
  .tipIcon {
    width: 48px;
    height: 48px;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .tipDesc {
    flex: 1;
    background: #EFF3FF;
    margin-left: 13px;
    position: relative;
    padding: 8px 16px; 
    font-size: 14px;
    line-height: 20px;
    color: #323233;
    border-radius: 6px;
    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
      border-top: 5px solid transparent; /*左边透明*/
      border-bottom: 5px solid transparent; /*右边透明*/
      border-right: 8px solid #EFF3FF; /*底部为黑色线条*/
    }
  }
}
</style>
<style lang="scss">
.last-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0;
    .el-dialog__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 14px;
      .el-dialog__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebecf0 !important;
    .el-message-box__title {
      font-size: 16px;
      color: #323233;
      line-height: 24px;
    }
    .el-message-box__headerbtn {
      top: 14px;
      .el-message-box__close {
        font-size: 18px;
      }
    }
  }
  .el-message-box__content {
    padding: 16px 20px;
    .el-message-box__message {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  }
  .el-message-box__btns {
    padding: 0px 20px;
    button {
      width: 60px !important;
    }
    .el-button {
      line-height: 20px !important;
    }
  }

  .el-dialog__body {
    padding: 16px 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  &.small-last-dialog {
    .el-dialog__body {
      padding: 16px 20px;
      height: auto !important;
      max-height: 340px;
      overflow-y: auto;
    }
  }
  .el-dialog__footer {
    padding: 16px 20px;
    .el-button {
      line-height: 20px;
    }
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
    }
  }
  .el-input__inner {
    border-radius: 2px;
  }
}
</style>
