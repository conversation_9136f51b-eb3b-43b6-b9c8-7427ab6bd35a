<template>
    <div class="flex">
        <div class="flex_info">
        <div class="flex_sp">
            <div class="flex_div">专家生产</div>
        </div>
        <el-select v-model="dev_product_scheme_id" placeholder="请选择专家生产">
            <el-option
            v-for="item in typeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            />
        </el-select>
        </div>
    </div>
</template>
<script>
import {
    SchemeList
} from '@/api/planGenerateApi.js'
export default {

    data() {
        return {
            dev_product_scheme_id:'', 
            typeList:[]
        }
    },
    mounted(){
        this.getSchemeList()
    },
    methods: {
        getDataAll() {
            
            return {
                "dev_product_scheme_id": this.dev_product_scheme_id, //需求说明
            } 
        },
        async getSchemeList(){
          const res = await  SchemeList(
            {
                "offset": 1,
                "limit": 999,
                "name": "",
                "agent_scene": "",
                "status": "",
                "process_status": "3",
                "sort_field": "create_time",
                "order": "desc",
                "dev_mode": "scheme"
            })
            this.typeList = res.data.result.items
        }
    }
}
</script>
<style lang="postcss" scoped>
.flex {
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 20px 20px 20px 20px;
    position: relative;
}

.flex_info {
    width: 600px;
    background-color: #ffffff;
    height: auto;
    max-height: calc(100vh - 409px);
    overflow-y: auto;
    padding:20px;

    :deep(.el-form-item__label) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #646566;
        text-align: center;
        font-style: normal;
    }
}
.flex_sp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .flex_div {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #323233;
        line-height: 22px;
        text-align: left;
        padding-left: 10px;
        font-style: normal;
        position: relative;

        &::before {
            content: "";
            position: absolute;
            /* z-index: 10; */
            left: -1.5px;
            width: 4px;
            height: 12px;
            background-color: #4068d4;
            box-sizing: border-box;
            top: 6px;
        }
    }

}
</style>