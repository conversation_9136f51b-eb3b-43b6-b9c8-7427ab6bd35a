<template>
  <div class="iframe-container">
    <iframe ref="myIframe" :srcdoc="iframeContent" width="100%"
      height="100%"
      @load="setIframeSize"
      ></iframe>
  </div>
</template>

<script>
export default {
  props: {
    iframeContent: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      data: this.iframeContent
    };
  },
  mounted() {
    // 初始化时设置 iframe 的宽高
    this.setIframeSize();
    // 监听窗口调整事件，确保尺寸更新
    window.addEventListener("resize", this.setIframeSize);
  },
  destroyed() {
    // 清理事件监听器
    window.removeEventListener("resize", this.setIframeSize);
  },
  methods: {
    setIframeSize() {
      const iframe = this.$refs.myIframe;
      if (iframe) {
        // 确保 iframe 完全填满父容器
        const container = iframe.parentElement;
        iframe.style.width = "100%";
        iframe.style.height = "100%";
      }
    }
  },
  watch: {
    iframeContent: {
      handler(newVal) {
        // 更新 iframe 内容时调整大小
        this.data = newVal;
        this.$nextTick(this.setIframeSize); // 确保内容渲染后调整尺寸
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  /* 确保父容器宽度100% */
  height: 100%;
  /* 确保父容器高度100% */
}

iframe {
  border: none;
  /* 去除 iframe 默认的边框 */
  width: 100%;
  /* 填充父容器的宽度 */
  height: 100%;
  /* 填充父容器的高度 */
}
</style>
