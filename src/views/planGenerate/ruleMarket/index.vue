<template>
    <div :class="$store.state.planGenerate.isIframeHide ? 'page-container planContainerFrame' : 'page-container'">
      <div class="planSearch">
        <div class="headerTitle">业务组件</div>
        <div class="search" :style="{ height: isFilterFlag ? '80px' : 'auto' }">
          <div class="searchFlex">
            <div class="searchLeft">
              <div class="searchItem">
                <div class="searchLabel">规则名称：</div>
                <el-input v-model="formData.rule_name" clearable placeholder="请输入" />
              </div>
              <div class="searchItem">
                <div class="searchLabel">场景名称：</div>
                <el-cascader
                  v-model="formData.scene_id"
                  show-all-levels
                  empty-text="暂无数据"
                  clearable
                  :props="props"
                  style="width: 100%"
                >
                  <template #empty> 暂无数据 </template>
                </el-cascader>
              </div>
              <div class="searchItem">
                <div class="searchLabel">最近更新人：</div>
                <el-select
                  v-model="formData.updated_id"
                  placeholder="创建人"
                  :remote-method="searchUser"
                  clearable
                  filterable
                  remote
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="`${item.nickname}(${item.loginName})`"
                    :value="item.id"
                  />
                </el-select>
              </div>
              <div class="searchItem" >
                <div class="searchLabel">标签：</div>
                <el-select
                  v-model="formData.tags"
                  multiple
                  remote
                  filterable
                  :remote-method="queryAllTag"
                  resver-keyword
                  clearable
                  placeholder="请搜索标签"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in selectTags"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
            </div>
            <div v-if="isFilterFlag">
              <el-button class="button-last" type="primary" @click="handlSearch">查询</el-button>
              <el-button class="button-last" type="info" @click="handleReset">重置</el-button>
            </div>
          </div>
          <div class="searchFlex" style="margin-top: 6px">
          <div class="searchLeft searchTag">
              <div  style="flex: 1;display: flex;align-items: center;max-width:calc(100%);flex-wrap: wrap;">
                <span style="color: #646566; margin-bottom: 10px; font-weight: bold;">
                  <i style="margin-right: 5px" class="el-icon-price-tag" />标签：
                </span>
                <div v-for="item in allTags" :key="item.id" :class="checkedTags.indexOf(item.id) > -1 ? 'tag-item active' : 'tag-item'" @click="selectTag(item.id)">{{item.name}}({{item.count}})</div>
              </div>
            </div>
        </div>
          <!-- <div class="searchFlex" style="margin-top: 12px">
            <div class="searchLeft searchTag">
                <div style="flex: 1;display: flex;align-items: center;max-width:calc(100% - 140px);flex-wrap: wrap;">
                  <div v-for="item in allTags" :key="item.id" :class="checkedTags.indexOf(item.id) > -1 ? 'tag-item active' : 'tag-item'" @click="selectTag(item.id)">{{item.name}}({{item.count}})</div>
                </div>
                <div v-if="!isFilterFlag" style="margin-bottom:8px">
                  <el-button class="button-last" type="primary" @click="handlSearch">查询</el-button>
                  <el-button class="button-last" type="info" @click="handleReset">重置</el-button>
                </div>
              </div>
          </div> -->
        </div>
        <div class="searchIcon">
          <div
            class="searchIconTaggle"
            @click="
              () => {
                isFilterFlag = !isFilterFlag;
              }
            "
          >
            <i v-if="isFilterFlag" class="el-icon-arrow-down"></i>
            <i v-else class="el-icon-arrow-up"></i>
          </div>
        </div>
      </div>
      <div class="containerCard">
        <div class="page-header">
          <div style="flex: 1; min-width: 120px">
            <el-radio-group v-model="tabPosition" size="small" @input="handleChange">
              <el-radio-button label="1">全部({{ allCount || 0 }})</el-radio-button>
              <el-radio-button label="2">我的({{ mineCount || 0 }})</el-radio-button>
            </el-radio-group>
          </div>
          <div class="rg">
            <el-radio-group v-model="tabPosition2" style="min-width: 90px" size="small">
            <el-radio-button label="1">
              <i class="el-icon-menu"></i>
            </el-radio-button>
            <el-radio-button label="2">
              <i class="el-icon-s-operation"></i>
            </el-radio-button>
          </el-radio-group>
          </div>
        </div>
        <div class="page-table-items">
          <!-- 表格 -->
          <div v-if="tabPosition2 === '2' && tableData.length">
            <el-table :data="tableData" style="width: 100%" class="table-no-header">
              <el-table-column prop="name" label="规则名称" min-width="220">
                <template slot-scope="scope">
                  <el-tooltip class="item" effect="dark" :content="scope.row.name" placement="top">
                    <div
                      style="
                        font-weight: bold;
                        color: #1d2129;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ scope.row.name }}
                    </div>
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row?.description"
                    placement="top"
                  >
                    <div
                      style="
                        color: #646566;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ scope.row?.description }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="scene_type" label="场景类型">
                <template slot-scope="scope">
                  <div style="color: #646566">场景类型：</div>
                  <div
                    style="
                      color: #323233;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                  >
                    {{ scope.row.agent_scene_code_name || '--' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="tag" label="标签" width="220">
                <template slot-scope="scope">
                  <div style="max-width: calc(220px); flex: 1">
                    <selectItem
                      :array-items="
                        scope.row.tag?.map((tag) => {
                          return { key: tag.id, label: tag.name };
                        })
                      "
                      :max-length="2"
                    ></selectItem>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="update_username" label="更新人" width="220">
                <template slot-scope="scope">
                  <div style="color: #646566">更新人：</div>
                  <div style="color: #323233">
                    {{
                      scope.row.update_nickName
                        ? scope.row.update_nickName + '(' + scope.row.update_username + ')'
                        : '--'
                    }}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="update_username" label="归属人" width="220">
                <template slot-scope="scope">
                  <div style="color: #646566">归属人：</div>
                  <div style="color: #323233">
                    {{ scope.row.owner_nickName + '(' + scope.row.owner_username + ')' }}
                  </div>
                </template>
              </el-table-column> -->
              <el-table-column prop="address" label="更新时间" width="160">
                <template slot-scope="scope">
                  <div style="color: #646566">更新时间：</div>
                  <div style="color: #323233">
                    {{ scope.row.update_time || '--' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="operate" width="140px" fixed="right">
                <template slot-scope="scope">
                  <el-link class="myLink" :underline="false" @click="handleXhushou(scope.row)">智能能力研发</el-link>
                  <!-- <el-button type="text" @click="handleDetail(scope.row)"> 详情 </el-button> -->
                  <el-link class="myLink" style="margin-left: 12px" :underline="false" @click="handleDetail(scope.row)">详情</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 卡片 -->
          <template v-else>
            <div v-for="(item, index) in tableData" :key="index" class="page-table-item2">
              <div class="headerBox">
                <div class="header">
                  <el-tooltip class="item" effect="dark" :content="item.name" placement="top-start">
                    <div
                      :title="item.name"
                      style="padding: 14px; font-size: 16px; font-weight: bold"
                      class="hidden-sc"
                    >
                      {{ item.name }}
                    </div>
                  </el-tooltip>
                </div>
                <div class="el-status">
                  <div class="sence-tag">ID:{{item.id}}</div>
                  <Status
                    :text="getStatusText(item)"
                    :bg-color="statusTypeMap[item.is_online]?.bgColor"
                    :dot-color="statusTypeMap[item.is_online]?.dotColor"
                  />
                </div>
              </div>
              <div id="market-item" style="padding: 16px; width: 100%;flex:1">
                <div style="display: flex; flex-direction: row; align-items: center; width: 100%;">
                  场景类型：<span
                  :class="['scene-status',
                  item.agent_scene_code === 'device_ops_assistant_scene-v1' ? 'equi':
                  item.agent_scene_code === 'artificial_handle_scene'?'client' :
                  item.agent_scene_code === 'visit_leader_cognition_scene'?'lingdao' :
                  item.agent_scene_code === ''||item.agent_scene_code === null?'':
                   'other']"> {{ item.agent_scene_code_name }}</span>
                </div>
                <div style="display: flex;flex-direction: row;">
                  <div style="word-break: keep-all;">描述：</div>
                  <div style="max-width: calc(100%);flex:1">
                    <el-tooltip class="item" v-if="item.description" effect="dark" :content="item.description" placement="top-start">
                      <div class="hidden-sc" style="width: 100%">
                        {{ item.description || '--' }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div style="display: flex;flex-direction: row;line-height: 24px;">
                  <!-- <div style="word-break: keep-all;">标签：</div> -->
                  <div style="max-width: calc(100%);flex:1;margin-top: 8px">
                    <selectItem :array-items="item.tag?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem>
                  </div>
                </div>
              </div>
              <div class="itemFooter">
                <div class="footerLeft">
                    <el-tooltip effect="dark" :content="item.update_nickName + '(' + item.update_username + ')'" placement="top">
                        <span class="avator">{{ item.update_nickName?.slice(-2) }}</span>
                    </el-tooltip>
                    <div class="separate"></div>
                    <span>{{ item.publish_date || item.create_time }}</span>
                </div>
                <div>
                  <el-link class="myLink" :underline="false" @click="handleXhushou(item)">智能能力研发</el-link>
                  <el-link class="myLink" style="margin-left: 12px" :underline="false" @click="handleDetail(item)">详情</el-link>
                </div>
              </div>
            </div>
          </template>
          <div
            v-if="!tableData.length"
            style="
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: row;
              justify-content: center;
            "
          >
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>
        <div class="page-footer">
          <el-pagination
            prev-text="上一页"
            next-text="下一页"
            background
            style="float: right"
            :current-page="pageParams.pageNum"
            :page-size="pageParams.pageSize"
            :page-sizes="[12, 24, 36, 48, 60]"
            layout="total, prev, pager, next"
            :total="pageParams.total"
            @size-change="getQueryPageList"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import {
    queryDictConfig,
    agentSenceList,
  } from '@/api/planGenerateApi.js';
  import {
    ruleMarketListApi,
    queryUseTagsRuleMarket,
    ruleMarketCountApi,
    queryUseRuleMarketTagsWithCount
  } from '@/api/ruleMarketApi.js';
  import { isSuperAdminForCurUser } from '@/api/user';
  import Status from '@/components/Status/index.vue';
  import selectItem from '../selectItem.vue';
  import TagPopover from '@/components/tagPopover/index.vue';
  
  export default {
    components: {
      Status,
      selectItem,
      TagPopover
    },
    data() {
      return {
        userInfo: {},
        isFilterFlag: true,
        statusTypeMap: {
          false: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
          true: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
        },
        url: '',
        formData: {
          scene_id: '',
          rule_name: '',
          updated_id: '',
          tags: []
        },
        isSuperAdmin: false,
        updated_id: '',
        agent_scene: '',
        allCount: 0,
        mineCount: 0,
        isLoading: false,
        tabPosition: '1',
        tabPosition2: '1',
        tableData: [],
        userList: [],
        allTags: [],
        selectTags:[],
        checkedTags: [],// 筛选选择的标签
        pageParams: {
          pageNum: 1,
          pageSize: 12,
          name: '',
          scene_type: '',
          total: 0
        },
        sortSchemeIds: [],
        props: {
          lazy: true,
          lazyLoad(node, resolve) {
            const { level } = node;
            if (level === 0) {
              queryDictConfig({ business_type: 'scene_type' }).then((res) => {
                if (res.status === 200 && res.data.code === 200) {
                  const result = res.data.result?.config.map((item) => {
                    return {
                      value: item.code,
                      label: item.name
                    };
                  });
                  resolve(result || []);
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  });
                }
              });
            } else {
              agentSenceList({ name: '', scene_type: node.data.value }).then((res) => {
                if (res.status === 200 && res.data.code === 200) {
                  const result = res.data.result;
                  if (result && result.length > 0) {
                    const options = result.map((item) => {
                      return {
                        value: item.id,
                        label: item.name,
                        leaf: true
                      };
                    });
                    resolve(options);
                    console.log('result', options);
                  } else {
                    resolve([
                      {
                        value: '',
                        label: '暂无',
                        leaf: true,
                        disabled: true
                      }
                    ]);
                  }
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data?.msg || '接口异常!'
                  });
                }
              });
            }
          }
        }
      };
    },
    // 生命周期 - 挂载完成（访问DOM元素）
    async mounted() {
      this.userInfo = sessionStorage.getItem('USER_INFO')
        ? JSON.parse(sessionStorage.getItem('USER_INFO'))
        : {};
      isSuperAdminForCurUser().then((res) => {
        if (res.status === 200) {
          this.isSuperAdmin = res.data.data;
        }
      });
      this.getAbilityMarketCount();
      this.getQueryPageList();
    },
    methods: {
      getStatusText(item) {
        const text = this.statusTypeMap[item.is_online]?.text;
        if (item.is_online) {
          return text + `${item.online_version ? '：' + item.online_version : ''}`;
        }
        return text;
      },
      selectTag(tag) {
        // 删除
        if (this.checkedTags.indexOf(tag) > -1) {
          const temp = [];
          this.checkedTags.forEach((item) => {
            if (item != tag) {
              temp.push(item);
            }
          });
          this.checkedTags = temp;
        } else {
          // 增加
          this.showType = tag;
          this.checkedTags.push(tag);
        }
        this.handlSearch();
      },
      queryTagsWithCount(){
        this.tableLoading = true;
        queryUseRuleMarketTagsWithCount({ keyword: '' })
          .then((res) => {
            this.tableLoading = false;
            if (res.data) {
              const results =res.data
              this.allTags = results.slice(0,10)
            } else {
              this.$message({
                type: 'error',
                message: res.data?.msg || '接口异常!'
              });
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      queryAllTag(keyword) {
        this.tableLoading = true;
        queryUseTagsRuleMarket({ keyword: keyword })
          .then((res) => {
            this.tableLoading = false;
            if (res.data) {
              this.selectTags = res.data;
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      handlSearch() {
        this.pageParams.pageNum = 1;
        this.getAbilityMarketCount();
        this.getQueryPageList();
      },
      handleReset() {
        this.pageParams.pageNum = 1;
        this.formData.rule_name = '';
        this.formData.scene_id = '';
        this.formData.updated_id = '';
        this.checkedTags = [];
        this.formData.tags = []
        this.getAbilityMarketCount();
        this.getQueryPageList();
      },
      handleCurrentChange(event) {
        this.pageParams.pageNum = event;
        this.getQueryPageList();
      },
      searchUser(userName, callback) {
        this.$post('/user/getAllUserListByUserName', {
          userName: userName
        }).then((data) => {
          this.userList = data;
          if (callback) {
            this.$nextTick(callback);
          } else {
            this.createUserName = userName;
          }
        });
      },
      handleChange(event) {
        if (+event === 1) {
          this.userList = [];
          this.updated_id = '';
          this.pageParams.pageNum = 1;
        } else {
          this.userList = [
            {
              id: sessionStorage.getItem('userId'),
              nickname: sessionStorage.getItem('LOGIN_Name'),
              loginName: sessionStorage.getItem('loadItcode')
            }
          ];
          this.updated_id = sessionStorage.getItem('userId');
          this.pageParams.pageNum = 1;
        }
        this.getQueryPageList();
      },
      async getAbilityMarketCount() {
        let tags = this.formData.tags
        if (this.checkedTags != null && this.checkedTags.length > 0) {
          tags = this.formData.tags.concat(this.checkedTags);
        }
        const res = await ruleMarketCountApi({
          ...this.formData,
          scene_id: this.formData.scene_id ? this.formData.scene_id[1] : '',
          owner_id: this.userInfo.userId,
          tag_ids: tags,
          tags: tags,
        });
        const data = res?.data?.result || {};
        this.allCount = data?.total_count || 0;
        this.mineCount = data?.mine_count || 0;
      },
      async getQueryPageList() {
        let tags = this.formData.tags
        if (this.checkedTags != null && this.checkedTags.length > 0) {
          tags = this.formData.tags.concat(this.checkedTags);
        }
        const params = {
          rule_name: this.formData.rule_name,
          updated_id: this.formData.updated_id,
          scene_id: this.formData.scene_id ? this.formData.scene_id[1] : '',
          owner_id: this.tabPosition === '1' ? '' : this.userInfo.userId,
          page_num: this.pageParams.pageNum,
          page_size: this.pageParams.pageSize,
          tag_ids: tags,
          sorted_scheme_ids: this.sortSchemeIds
        };
        ruleMarketListApi(params)
          .then(async (res) => {
            this.isLoading = true;
            this.queryTagsWithCount();
            const { data = {}, status } = res;
            if (status === 200 && data.code === 200) {
              this.tableData = data.result?.items || [];
              this.pageParams.total = data.result?.total;
            } else {
              this.$message.error(res.msg || '接口异常!');
            }
          })
          .catch((_err) => {
            this.$message({
              type: 'error',
              message: _err.data?.msg || '接口异常!'
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      },
      // 规则集市详情
      handleDetail(row) {
        // this.$router.push({
        //     path: '/planGenerate/planchat',
        //     query: {
        //         ...this.$route.query,
        //         id: data.scheme_id
        //     }
        // });
        this.$router.push({
          path: '/alignmentRules/detail',
          query: {
            ...this.$route.query,
            id: row.id,
            agent_scene_code: row.agent_scene_code,
            scheme_id: row.scheme_id
          }
        });
      },
      handleXhushou(data) {
        this.$router.push({
            path: '/planGenerate/planchat',
            query: {
                ...this.$route.query,
                id: data.scheme_id
            }
        });
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .page-container {
    max-height: calc(100vh - 90px);
    height: calc(100vh - 90px);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    &.planContainerFrame {
      max-height: calc(100vh) !important;
    }
  
    .planSearch {
      background-color: #fff;
      padding: 0px 20px 16px;
      position: relative;
      .search {
        position: relative;
        overflow: hidden;
      }
      .searchIcon {
        position: absolute;
        left: 50%;
        bottom: -10px;
        transform: translateX(-50%);
        .searchIconTaggle {
          width: 40px;
          height: 20px;
          background: #d9e6f8;
          border-radius: 2px;
          position: relative;
          border: 1px solid #f2f3f5;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #4068d4;
          cursor: pointer;
          &:hover {
            background: #a1bbef;
          }
        }
      }
      .headerTitle {
        font-weight: bold;
        color: #323233;
        line-height: 26px;
        font-size: 18px;
        padding: 14px 0px;
      }
      .button-last {
        line-height: 14px;
      }
      .searchFlex {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        .searchLeft {
          display: flex;
          flex-direction: row;
          align-items: center;
          flex: 1;
          &.searchTag {
            display: flex;
            flex-direction: row;
            align-items: flex-end !important;
            margin-top: 8px;
          }
          .tag-item {
            display: inline-block;
            background: #f6f7fb;
            word-break: keep-all;
            border-radius: 2px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #323233;
            line-height: 20px;
            padding: 5px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            height: 28px;
            // max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            &:last-child {
              margin-right: 0px;
            }
            &.active {
              background-color: #eff3ff;
              color: #4068d4;
            }
            &:hover {
              background-color: #eff3ff;
              color: #4068d4;
            }
          }
          .searchItem {
            max-width: 300px;
            margin-right: 16px;
            width: 30%;
            .searchLabel {
              font-weight: 400;
              color: #646566;
              line-height: 22px;
              font-size: 14px;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
    .containerCard {
      height: calc(100vh - 164px);
      max-height: calc(100vh - 164px);
      overflow: hidden;
      margin: 16px 20px 0px 20px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      background-color: #fff;
      padding: 16px 20px;
  
      & .page-header {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        ::v-deep .el-radio-button__inner {
          height: 30px;
          line-height: 19px;
          padding: 4px 16px;
          font-size: 14px;
          border-radius: 2px 0 0 2px;
        }
        .rg {
          display: flex;
          align-items: center;
        }
      }
      & .page-table-items {
        height: calc(100% - 70px);
        margin-top: 16px;
        width: calc(100% + 12px);
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-content: flex-start;
        & .table-no-header {
          border-top: 1px solid #ebecf0;
          ::v-deep .el-table__header {
            display: none;
          }
        }
        & .page-table-item2 {
          width: calc(33.3333% - 12px);
          max-width: calc(33.3333% - 12px);
          margin-right: 12px;
          border-radius: 4px;
          // min-width: 275px;
          margin-bottom: 15px;
          border: 1px solid #dcdde0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .headerBox {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            width: 100%;
            border-bottom: 1px solid rgb(220, 221, 224);
          }
          .sence-tag {
            display: inline-flex;
            padding: 0 8px;
            margin-right: 10px;
            height: 24px;
            border-radius: 2px;
            background: #EBF9FF;
            color: #318DB8;
          }
          .el-status {
              margin-left: 16px;
              margin-bottom: 14px;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 2px;
              line-height: initial;
              font-size: 14px;
              .point {
                width: 5px;
                height: 5px;
                margin-right: 5px;
                background: #7d7e80;
                vertical-align: middle;
                display: inline-flex;
                align-items: center;
              }
              &.success {
                background: #d7eedb;
                .point {
                  background: #39ab4c;
                }
              }
            }
          .header {
            flex: 1;
            display: flex;
            align-items: center;
            line-height: 24px;
            width: 100%;
            .el-icon-warning-outline {
              color: #4068d4;
              padding: 0 8px 0 16px;
              cursor: pointer;
            }
          }
          &:hover {
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
  ::v-deep .el-card__header {
    border: none;
    padding: 16px 0px 0px;
  }
  ::v-deep .el-tooltip__popper {
    &.is-dark {
      background: rgba($color: #323233, $alpha: 0.8) !important;
    }
  }
  ::v-deep .el-button.is-disabled {
    color: rgba($color: #4068d4, $alpha: 0.4);
  }
  ::v-deep .el-button--info {
    background-color: #f2f3f5;
    color: #4068d4;
    border-color: #f2f3f5;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  ::v-deep .el-button--text {
    background-color: #fff;
    color: #4068d4;
    border-color: #fff;
    padding: 6px 16px;
    border-radius: 2px;
    &.is-disabled {
      opacity: 0.4;
      background-color: #f2f3f5 !important;
      color: #4068d4;
      border-color: #f2f3f5 !important;
    }
    &:hover {
      background-color: #ebecf0;
      border-color: #ebecf0;
      color: #4068d4;
    }
    &:active {
      background-color: #dcdde0;
      border-color: #dcdde0;
    }
  }
  .itemFooter {
    border-top: 1px solid #c8c9cc;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    max-width: 100%;
    overflow: hidden;
    & .footerLeft{
        display: flex;
        & .item{
            margin-right: 16px;
        }
        }
        > div{
        display: flex;
        align-items: center;
        img{
            height: 16px;
            width: 16px;
            margin-right: 4px;
        }
        }
        .separate {
        width: 1px;
        height: 12px;
        margin: 0 8px;
        background: #C8C9CC;
        }
        .avator {
        width: 28px;
        height: 28px;
        line-height: 28px;
        border-radius: 50%;
        font-size: 10px;
        font-weight: 600;
        text-align: center;
        background: #E6ECFF;
        color: #4068D4;

        }
  }
  .hidden-sc {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
  .scene-status {
    padding: 0 8px;
    height: 24px;
    border-radius: 2px;
    max-width: calc(100% - 80px);
    /* 设置文本溢出时的行为为省略号 */
    text-overflow: ellipsis;
  
    /* 设置超出容器的内容应该被裁剪掉 */
    overflow: hidden;
  
    /* 强制文本在一行内显示，即使这意味着它会溢出容器 */
    white-space: nowrap;
  
    &.equi {
      background: #ebf9ff;
      color: #318db8;
    }
    &.client {
      background: #fff2eb;
      color: #cc7846;
    }
    &.other {
      background: #f3eafe;
      color: #7a4db8;
    }
    &.lingdao {
      background: #e6ecff;
      color: #3455ad;
    }
  }
  ::v-deep .el-dialog__body {
    border-top: 1px solid #ebecf0;
    padding: 10px 20px;
  }
  .statistics {
    display: flex;
    align-items: center;
    background: #f6f7fb;
    > div {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;
      cursor: pointer;
      &:hover {
        background: #ebecf0;
      }
      > img {
        width: 32px;
        height: 32px;
        margin-right: 8px;
      }
    }
  }
  .myLink.el-link.el-link--default {
    color: #4068D4;
    &:hover {
      color:  #3d5597;
    }
  }
  </style>
  