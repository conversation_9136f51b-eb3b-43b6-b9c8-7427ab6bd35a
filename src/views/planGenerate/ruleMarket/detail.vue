<template>
    <!-- 应用详情 -->
    <div class="page">
      <div class="info">
        <div class="info-top">
          <div class="info-title">
            <span>{{ baseInfo?.rule_name }}</span>
            <Status
              :text="statusTypeMap[baseInfo?.is_online]?.text"
              :bg-color="statusTypeMap[baseInfo.is_online]?.bgColor"
              :dot-color="statusTypeMap[baseInfo.is_online]?.dotColor"
            />
          </div>
          <div v-if="isEdit">
            <el-button type="primary" @click="saveHandle">保存</el-button>
            <el-button type="info" @click="closeHandle">取消</el-button>
          </div>
          <el-button v-else plain @click="editHandle">编辑</el-button>
        </div>
        <el-descriptions title="" :column="3">
          <el-descriptions-item label="创建时间">{{ baseInfo?.create_date }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ baseInfo?.update_date }}</el-descriptions-item>
          <el-descriptions-item label="发布人">{{ baseInfo?.update_user_name }}</el-descriptions-item>
          <el-descriptions-item label="当前版本">{{ baseInfo?.current_version_name }}</el-descriptions-item>
          <el-descriptions-item label="标签">
            <el-select
              ref="tagsSelect"
              v-if="isEdit"
              v-model="baseForm.tag_ids"
              style="width:328px"
              multiple
              filterable
              remote
              allow-create
              placeholder="请选择标签"
              :remote-method="searchTags"
              clearable
              @change="changeTags"
            >
              <el-option
                v-for="item in tagList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <div style="width: 328px" v-else><selectItem :array-items="baseInfo.tag?.map(tag => {return {key: tag.id,label: tag.name}})" :max-length="2"></selectItem></div>
          </el-descriptions-item>
          <el-descriptions-item label="描述">
            <el-input v-if="isEdit" type="textarea" :rows="1" style="width:90%" v-model="description" placeholder="请输入描述信息"></el-input>
            <span v-else>{{ baseInfo?.description }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <el-tabs v-model="activeName">
          <el-tab-pane label="规则内容" name="interface"/>
          <el-tab-pane label="发布历史" name="publish"/>
        </el-tabs>
      </div>
      <div class="tab-content">
        <Publish v-if="activeName === 'publish'" :version-id="baseInfo.current_version_id" @updateDetail="getMarketInfo"/>
        <Interface v-if="activeName === 'interface'" :version-id="baseInfo.current_version_id"/>
      </div>
    </div>
  </template>
  
  <script type="text/javascript">
  import Publish from './component/publish.vue'
  import Interface from './component/interface.vue'
  import { ruleMarketDetail, updateRule, bindTagMarket, addTagMarket, queryTagsRuleMarket } from '@/api/ruleMarketApi.js'
  import Status from '@/components/Status/index.vue';
  import selectItem  from '../selectItem.vue';
  export default {
    name: 'RuleMarketDetail',
    components: { Publish, Interface,Status, selectItem },
    data() {
      return {
        activeName: 'interface',
        baseInfo: {
          description: ''
        },
        userList: [],
        tagList: [],
        allTagList: [],
        baseForm: {
          tag_ids: []
        },
        description: '',
        statusTypeMap: {
          false: { bgColor: '#F2F2F2', dotColor: '#7D7E80', text: '已下线' },
          true: { bgColor: '#EBF6ED', dotColor: '#39AB4C', text: '已上线' }
        },
        isEdit: false,
        abilityNames: ''
      }
    },
    created() {
      this.getMarketInfo()
    },
    mounted() {
      this.searchTags2('');
    },
    methods: {
      async getMarketInfo() {
        console.log(this.baseInfo,'111')
        await ruleMarketDetail({
          rule_id: +this.$route.query?.id || ''
        }).then(res => {
          if (res?.data?.code === 200) {
            this.baseInfo = {
              description: '',
              ...res?.data?.result
            }
            this.baseForm.tag_ids = res?.data?.result?.tag?.map(item => item.id) || [];
            this.baseInfo.status=1
            this.description = res?.data?.result?.description;
          } else {
  
            this.$message.error(res?.data?.serverErrorMsg || res?.data?.msg)
          }
        }).catch((err) => {
          console.log('err', err)
        })
      },
      editHandle() {
        this.isEdit = true;
      },
      closeHandle(){
        this.description = this.baseInfo.description;
        this.isEdit = false;
      },
      saveHandle(){
        const params= {
          rule_id:+this.$route.query?.id || '',
          description: this.description,
          tag_ids: this.baseForm.tag_ids
        }
        updateRule(params).then(async res=>{
          if (res?.data?.code === 200) {
            await bindTagMarket({
              tag_ids:this.baseForm.tag_ids, biz_id:this.$route.query?.id
            }).then((ress) => {
              console.log('绑定成功', ress.data);
            })
            this.baseInfo.description = this.description;
            this.getMarketInfo();
            this.isEdit = false;
            this.$message.success('保存成功')
          }else{
            this.$message.error('保存失败')
          }
        })
      },
      async changeTags (val) {
        console.log('改变',val);
        const temp = [];
        val.forEach(async tagid => {
          const filters = this.allTagList.filter(item => item.id === tagid)
          if (filters.length === 0) {
            if (tagid.length && tagid.length <=15) {
              await addTagMarket({
                name: tagid
              }).then(async (res) => {
                if (res.data) {
                  console.log('添加成功', res.data);
                  temp.push(res.data);
                  await queryTagsRuleMarket({
                    keyword: ''
                  }).then(res => {
                    if(res.data){
                      this.tagList = res.data;
                      this.allTagList = res.data;
                      // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = tagid
                      this.baseForm.tag_ids = temp;
                      console.log('this.baseForm.tag_ids', this.baseForm.tag_ids);
                      this.$nextTick(() => {
                        this.$set(this.baseForm, 'tag_ids', temp);
                        this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = tagid
                        // this.$refs.tagsSelect.selected[this.$refs.tagsSelect.selected.length -1].currentLabel = tagid
                      })
                    } else {
                      this.tagList = [];
                    }
                  })
                }
              })
            } else {
              this.$message({
                type: 'warning',
                message: '标签最长为15个字符，请修改!'
              });
            }

          } else {
            temp.push(tagid);
            this.baseForm.tag_ids = temp;
          }
        })
      },
      searchTags(keyword) {
        queryTagsRuleMarket({
          keyword
        }).then(res => {
          if(res.data){
            this.tagList = res.data;
            if (keyword === '') {
              this.allTagList = res.data;
            }
          } else {
            this.tagList = [];
          }
        })
      },
      searchTags2() {
        queryTagsRuleMarket({
          keyword: ''
        }).then(res => {
          if(res.data){
            this.tagList = res.data;
            this.allTagList = res.data;
            const temp = [];
            console.log('变价回显', this.baseInfo.tag);
            this.baseInfo.tag?.forEach(titem => {
              const filter = res.data.filter(item  => item.id === titem.id)
              if (filter.length) {
                temp.push(titem.id);
              }
            });
            this.baseForm.tag_ids = temp;
          } else {
            this.tagList = [];
          }
        })
      },
    }
  }
  </script>
  <style lang="less" scoped>
  :deep(.el-descriptions-item__label:not(.is-bordered-label)) {
    word-break: keep-all;
  }
  .page {
    display: flex;
    flex-direction: column;
    .info {
      width: 100%;
      background: #fff;
      border-bottom:2px solid #E4E7ED;
      // padding-left: 20px;
  
      .info-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding:14px 20px;
  
        .info-title {
          display: flex;
          justify-content: flex-start;
          align-items: center;
  
          >span {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #323233;
            line-height: 26px;
            margin-right: 12px;
          }
        }
      }
  
      .el-descriptions {
        padding-left: 20px;
        :deep(.el-descriptions-item__container) {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #323233;
          line-height: 22px;
        }
      }
  
      .el-tabs {
        padding-left: 20px;
      }
  
      :deep(.el-tabs__nav-wrap) {
        &::after {
          height: 0px;
        }
      }
    }
  
    .tab-content {
      flex: 1;
      margin: 16px 20px 0 20px;
      background: #fff;
      padding: 16px 20px;
    }
  
    .el-select {
      width: 100%;
    }
  
    :deep {
      .el-tabs__header{
         margin-bottom: 0px;
      }
  
      .el-dialog__header {
        border-bottom: 1px solid #ebecf0;
      }
    }
    :deep(.el-button--info) {
      border: none;
    }
  }
  
  </style>
  