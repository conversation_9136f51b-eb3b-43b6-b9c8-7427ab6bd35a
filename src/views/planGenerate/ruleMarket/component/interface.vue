<template>
  <div>
    <div class="search" :loading="tableLoading">
      {{ruleContent}}
    </div>
  </div>
</template>
<script type="text/javascript">
import { GetDecision } from '@/api/planGenerateApi.js'
// import { ruleMarketDetail } from '@/api/ruleMarketApi.js'

export default {
  name: 'InterfaceCom',
  props: {
    versionId: {
      type: [String, Number],
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      ruleContent: '',
      tableLoading: false,
    }
  },
  watch:{
    versionId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getDeviceBindList()
        }
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    // 绑定信息
    async getDeviceBindList() {
      this.tableLoading = true
      const params = {
        scheme_id: +this.$route.query.scheme_id,
        scheme_status: 'rule'
      }
      await GetDecision(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.ruleContent = res.data.result?.decision_making_content || '';
          this.tableLoading = false;
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          });
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  text-align: left;
  margin-bottom: 16px;

  :deep(.search-input) {
    .el-input__inner {
      height: 30px;
      line-height: 30px;
      border-color: #c8c9cc !important;
      border-right: none;
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
    }
    .el-input-group__append {
      border-color: #c8c9cc !important;
      background-color: transparent;
      padding: 0px 12px;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
  }
}

.el-link {
  margin-right: 16px;
  &:last-child {
    margin-right: 0;
  }
}


:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebecf0;
}

:deep {
  .hide-expand .el-table__expand-column .el-icon {
    visibility: hidden;
  }
}
</style>
