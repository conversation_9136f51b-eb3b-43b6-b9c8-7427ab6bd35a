<template>
  <div style="height: 100%">
    <!-- <MonacoEditor
      class="editor"
      :value="detailContent"
      language="python"
      :scrollBeyondLastLine="true"
      theme="vs-dark"
      :diff-editor="false"
      :options="options"
    /> -->
    <MyEditor v-if="ruleContent" id="MyFanganScheme1" ref='MyFanganScheme1' :mdContent="ruleContent"></MyEditor>
    <div v-else style="display:flex;flex-direction: column;align-items: center;justify-content: center;height:100%;width:100%">
      <img src="@/assets/images/planGenerater/runerror.png" style="width: 180px;height: auto"/>
      <div style="display:flex;flex-direction: row;align-items: center;justify-content: center;margin-top:16px">暂无规则内容</div>
    </div>
  </div>
</template>
<script type="text/javascript">
// import Status from '@/components/Status/index.vue'
// import MonacoEditor from '@/components/MonacoEditor'
import MyEditor from '../../mdEditor.vue';
import { queryRuleContent } from '@/api/ruleMarketApi.js'
import { GetDecision } from '@/api/planGenerateApi.js'
import mermaid from 'mermaid'
mermaid.initialize({startOnLoad: true,
  theme: 'default',
  flowchart: {
      htmlLabels: true,
      useMaxWidth: true
  },
  zoom: true,
});
export default {
  name: 'ExampleCom',
  components: {MyEditor},
  props: {
    rule_version_id:{
      type: String,
      default: '',
    }
  },
  watch: {
    rule_version_id: {
      handler(val) {
        console.log('树枝变化', val);
        if (val) {
          this.handleDetailCode(val);
        } 
      },
      immediate: true
    },
  },
  data() {
    return {
      detailContent: '',
      ruleContent: '',
      loading: false,
      options: {
          readOnly: true,
          lineNumbers: true,
          fontSize: 15,
          mouseStyle: 'default',
          colorDecorators: true,
          foldingStrategy: 'indentation', // 代码可分小段折叠
          automaticLayout: true, // 自适应布局
          overviewRulerBorder: false, // 不要滚动条的边框
          autoClosingBrackets: true,
          renderLineHighlight: 'all',
          wordWrap: 'on',
          scrollBeyondLastLine: true,
          tabSize: 4, // tab 缩进长度
          minimap: {
            enabled: true // 不要小地图
          },
          fontFamily:
            'Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif',
          folding: true
        },
    };
  },
  created() {
  },
  methods: {
    async handleDetailCode(id){
      this.loading = true;
      const params = {
        rule_detail_id: Number(id)
      }
      await queryRuleContent(params).then((res) => {
        if (res.status === 200 && res.data.code === 200) {
          this.ruleContent = res.data.result || '';
          this.laoding = false;
        } else {
          this.$message({
            type: 'error',
            message: res.data?.msg || '接口异常!'
          })
        }
      }).finally(() => {
        this.loading = false;
      });
      // queryComponentById({component_id: id, type: 'rule'})
      //   .then((res) => {
      //     this.loading = false;
      //     if (res.status === 200 && res.data.code === 200 && res.data.result) {
      //       this.detailContent = res.data.result?.decision_making_content || '';
      //     } else {
      //       this.$message({
      //         type: 'error',
      //         message: res.data?.msg || '接口异常!'
      //       });
      //     }
      //   })
      //   .catch((_err) => {
      //     this.loading = false;
      //     this.$message({
      //       type: 'error',
      //       message: _err.data?.msg || '接口异常!'
      //     });
      //   })
      //   .finally(() => {
      //     this.loading = false;
      //   });
    },
  }
}
</script>
<style lang="less" scoped>
.editor {
  width: 100%;
  height: 100%;
}
.el-link {
  &:first-child {
    margin-right: 16px;
  }
}
.PagePaging {
  margin: 16px 0px;
  text-align: right;
  margin-top: 16px;
}
</style>
