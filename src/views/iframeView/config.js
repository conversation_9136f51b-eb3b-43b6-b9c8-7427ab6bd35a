export const iframeKeyMap = (key, id, query='') => {
  const paths = {
    'ability': `${process.env.VUE_APP_RULEFLOW_ENGINEER}/developerProductionPlatform/abilityDesignCenter/abilityCenter/ability`, // 能力设计
    // 'ability': 'http://localhost:3003/developerProductionPlatform/abilityDesignCenter/abilityCenter/ability',
    'dataMapList': `${process.env.VUE_APP_RULEFLOW_ENGINEER}/developerProductionPlatform/abilityDesignCenter/abilityCenter/dataMapList`,
    electronicFence: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/operatingPlatform/operatingCenter/operatingCenter/electronicFence`, // 电子围栏
    taskMonitor: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/operatingPlatform/operatingCenter/operatingCenter/taskMonitorManagement`, // 任务监控
    businessReports: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/operatingPlatform/operatingCenter/operatingCenter/businessReportsManagement`, // 分布式作业报表
    offlineData: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/operatingPlatform/operatingCenter/operatingCenter/offlineData`, // 场站边端数据，

    developmentMechanism: `${process.env.VUE_APP_MECHANISM_URL}/mechanism/developmentMechanism?${query}`, // 规则开发
    devlopmentAlgorithm: `${process.env.VUE_APP_MECHANISM_URL}/mechanism/devlopmentAlgorithm?${query}`, // 模型开发
    knowledgeCollation: `${process.env.VUE_APP_MECHANISM_URL}/mechanism/knowledgeCollation?${query}`, // 知识开发
    designDocument: `${process.env.VUE_APP_MECHANISM_URL}/mechanism/designDocument?${query}`, // 知识手机
    knowledgeBasePortal: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/portal?${query}`, // 知识库
    knowledgeBaseKnowledgeSearch: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/AiSearch?${query}`, // 知识检索
    knowledgeBasePrivateKnowledge: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/privateKnowledge?${query}`, // 知识问答
    knowledgeBaseFeedback: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/feedback?${query}`, // 知识反馈
    knowledgeBaseSettingsManageManage: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/settingsManage/manage?${query}`, // 知识库管理
    knowledgeBaseSettingsManageTagManage: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/settingsManage/tagManage?${query}`, // 标签管理
    solutionList: `${process.env.VUE_APP_MECHANISM_URL}/knowledgeEquipment/solutionList?${query}`, // 场景知识管理
    knowledgeScene: `${process.env.VUE_APP_MECHANISM_URL}/knowledgeEquipment/sceneList?${query}`, // 设备知识管理
    model: `${process.env.VUE_APP_MECHANISM_URL}/graph?${query}`, // 能力组装
    modelMarket: `${process.env.VUE_APP_RUN_EXTENSION}/multiModel/model`, // 模型市场
    modelChat: `${process.env.VUE_APP_RUN_EXTENSION}/multiModel/chat`, // 模型市场
    modelImage: `${process.env.VUE_APP_RUN_EXTENSION}/multiModel/image`, // 模型市场
    playgroundChat: `${process.env.VUE_APP_RUN_EXTENSION}/playground/chat`, // 模型市场
    playgroundCompletions: `${process.env.VUE_APP_RUN_EXTENSION}/playground/completions`, // 模型市场
    knowledgeBaseClassify: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/classify`, // 模型市场
    knowledgeBaseProcess: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/process`, // 模型市场
    knowledgeBaseTagManage: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/tagManage`, // 方案标签
    knowledgeBaseMonitoring: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/monitoring`, // 模型市场
    systemManageApiAuthorization: `${process.env.VUE_APP_RUN_EXTENSION}/systemManage/apiAuthorization`, // 模型市场
  }

  const idPaths = {
    orchestrationEngine: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/developerProductionPlatform/abilityOrchestrationCenter/${id}/abilityArrang/chart/`, // 编排
    abilityModel: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/developerProductionPlatform/abilityOrchestrationCenter/${id}/assembly/abilityModel/`, // 组件
    assemblyTest: `${process.env.VUE_APP_RULEFLOW_ENGINEER}/developerProductionPlatform/abilityOrchestrationCenter/${id}/assemblyTest/regression/`, // 测试
    knowledgeBasePortal: `${process.env.VUE_APP_RUN_EXTENSION}/knowledgeBase/portal/${id}`, // 知识库
  }

  if (key && id) {
    return idPaths[key]
  }
  return paths[key]
}
