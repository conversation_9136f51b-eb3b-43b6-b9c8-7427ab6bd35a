<template>
  <div
    v-loading="loading"
    class="subApp-iframe height100"
  >
    <iframe
      v-if="iframeSrc"
      ref="iframe"
      :src="iframeSrc"
      frameborder="0"
      width="100%"
      height="100%"
      allow="clipboard-read; clipboard-write"
      @load="onIframeLoad"
    ></iframe>
    <div v-else class="nonactivated">
      <img :src="require('@/assets/images/image/undraw_programming.png')"/>
      <span>系统对接中，暂无内容</span>
    </div>
  </div>
</template>

<script type="text/javascript">
import { iframeKeyMap } from './config'

export default {
  name: 'IframeView',
  components: { },
  data() {
    return {
      iframeSrc: '',
      loading: false,
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(val) {
        let id = ''
        this.iframeSrc = ''
        this.loading = true
        if (val.path.includes('/ability/')) {
          id = localStorage.getItem('iframeRuleVersionId')
        }
        // 机理
        if (val.path.includes('/mechanism')) {
          const path = iframeKeyMap(val.name, id, new URLSearchParams(val.query)) + + '&workspaceId=' + val.query?.workspaceId
          this.$nextTick(() => {
          // 赋值地址，再加载
            this.iframeSrc = this.getQueryToken(path)
          })
        } else if (val?.name) {
          let path = ''
          if (val.path.includes('/multiModel') || val.path.includes('/playground') || val.path.includes('/knowledgeBase') || val.path.includes('/systemManage')) {
            if(val.query && val.query.id) {
              path = iframeKeyMap(val.name, val.query.id) + '?showHeader=0&showLeftMenu=0&showBreadcrumb=0' + '&workspaceId=' + val.query?.workspaceId
              console.log('1111111111-----',val,iframeKeyMap(val.name, val.query.id))
            }else {
              path = iframeKeyMap(val.name, id) + '?showHeader=0&showLeftMenu=0&showBreadcrumb=0' + '&workspaceId=' + val.query?.workspaceId
            }
          } else {
             path = iframeKeyMap(val.name, id) + '?' + 'workspaceId=' + val.query?.workspaceId
          }
          this.$nextTick(() => {
          // 赋值地址，再加载
            this.iframeSrc = this.getQueryToken(path)
          })
        }


      }
    },
  },
  mounted() {},
  methods: {
    // url地址上的token参数
    getQueryToken(url) {
      return this.authSdk?.transformToAuthUrl(url, 'local');
    },
    onIframeLoad(){
      this.loading = false
      const iframe = this.$refs.iframe
      let param = this.$route.query.workspaceId
      const id = this.$route.query.id
    //   // 获取 iframe 内的 URL
    //   let urls = iframe.contentWindow.location.href.split('?')
    //    const iframeUrl = `${urls[0]}/${id}`;
    //       urls[0] = iframeUrl
    // console.log('Iframe URL:',urls.join('?'),iframe.contentWindow.location.href);
      iframe.contentWindow.postMessage(param, '*')
    },
    onMessageReceived(event) {
      console.log('Received message from iframe:', event.data)
    }
  }
}
</script>
<style lang="less" scoped>
.subApp-iframe {
  width: 100%;
  height: calc(100vh - 88px);
  display: flex;
  overflow-y: auto;
  position: relative;
}

.nonactivated {
  width: 320px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  img {
    height: 233px;
  }
  span {
    width: 100%;
    display: inline-block;
    text-align: center;
    font-size: 14px;
    color: #323233;
    margin-top: 28px;
  }
}
</style>

