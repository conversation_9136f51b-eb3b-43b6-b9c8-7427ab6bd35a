server {
  listen       80;
  server_name  localhost;

  # Dynamic mode.
  gzip on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  #gzip_http_version 1.0;
  gzip_comp_level 6;
  gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
  gzip_vary off;
  gzip_disable "MSIE [1-6]\.";

  # Static mode.
  gzip_static on;

  #Brotli Compression
  brotli on;
  brotli_comp_level 6;
  brotli_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript image/svg+xml;

  location / {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods *;
    add_header Access-Control-Allow-Headers *;
    add_header Content-Security-Policy "default-src * 'unsafe-inline' 'unsafe-eval' blob: data: ;";
    add_header Referrer-Policy no-referrer;

    # 防XSS攻击
    # add_header X-XSS-Protection "1; mode=block";

    if ($request_filename ~ .*\.(htm|html)$) {
      expires -1s;
      add_header Cache-Control no-cache;
    }

    # 禁止服务器自动解析资源类型
    add_header X-Content-Type-Options nosniff;
    add_header X-Permitted-Cross-Domain-Policies none;
    add_header X-Download-Options noopen;

    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ @router;
  }

  location @router {
    rewrite ^.*$ /index.html last;
  }

  # ai-run-extension-front前端代理 proxy
  # location ^~/are/{
  #   rewrite ^/are/(.*)$ /$1 break;
  #   proxy_pass http://ai-run-extension-front.ai-runtime-ns.svc.cluster.local:80;
  # }

  # location ^~/are-assets/{
  #   proxy_pass http://ai-run-extension-front.ai-runtime-ns.svc.cluster.local:80;
  # }
  # location ^~/are-static/{
  #   proxy_pass http://ai-run-extension-front.ai-runtime-ns.svc.cluster.local:80;
  # }

  # aip-search代理
  location ^~/aip-search/ {
    proxy_pass https://aip-search.aip.ennewi.cn;
  }
  location ^~/aip-search-api/ {
    proxy_pass https://aip-search.aip.ennewi.cn;
    proxy_http_version 1.1;

    # enable streaming response
    proxy_buffering off;
    proxy_request_buffering off;
    chunked_transfer_encoding on;
  }

  # agent-gpt前端代理
  location ^~/agent/ {
    rewrite ^/agent/(.*)$ /$1 break;
    proxy_pass http://agentgpt-web.ai-maker-ns.svc.cluster.local:80;
  }

  location ^~/agent-static/ {
    proxy_pass http://agentgpt-web.ai-maker-ns.svc.cluster.local:80;
  }

  location ^~/agent-assets/ {
    proxy_pass http://agentgpt-web.ai-maker-ns.svc.cluster.local:80;
  }

  # agent-gpt后端代理，只允许生产环境使用
  location ^~/agent-api/ {
    rewrite ^/agent-api/(.*)$ /$1 break;
    proxy_pass http://aip-agentgpt-service.ai-runtime-ns.svc.cluster.local:8000;
  }
  # gpts后端代理，只允许生产环境使用
  location ^~/gpts-api/ {
    rewrite ^/gpts-api/(.*)$ /$1 break;
    proxy_pass http://aip-gpts-backend.ai-runtime-ns.svc.cluster.local:80;
  }
  # agent-gpt后端代理，只允许生产环境使用
  # location ^~/chat-api/ {
  #   rewrite ^/chat-api/(.*)$ /$1 break;
  #   proxy_pass http://ai-chat-backend.tobeconfirmed-ns.svc.cluster.local:8080;
  #   client_max_body_size 100m;
  # }

  location ^~/gpts-api/socket.io/ {
    rewrite ^/gpts-api/(.*)$ /$1 break;
    proxy_pass http://aip-gpts-backend.ai-runtime-ns.svc.cluster.local:80;
    proxy_http_version 1.1; #这里必须使用http 1.1
    #下面两个必须设置，请求头设置为ws请求方式
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
  }

  # ai-operation-assistant代理
  # location ^~/aoa-api/{
  #   rewrite ^/aoa-api/(.*)$ /$1 break;
  #   proxy_pass http://ai-operation-assistant.ai-maker-ns.svc.cluster.local:80;
  # }

  # location ^~/op-assets/{
  #   proxy_pass http://ai-operation-assistant.ai-maker-ns.svc.cluster.local:80;
  # }

}

