{"name": "ai-platform-web", "version": "3.1.0", "private": true, "enn": {"type": "pc", "adapter": "master"}, "scripts": {"serve": "vue-cli-service serve --open --mode dev", "serve:local": "vue-cli-service serve --open --mode local", "serve:alpha": "vue-cli-service serve --open --mode alpha", "serve:fat": "vue-cli-service serve --open --mode fat", "serve:task": "vue-cli-service serve --open --mode task", "serve:uat": "vue-cli-service serve --open --mode uat", "build": "vue-cli-service build", "local": "vue-cli-service serve --mode local", "build:dev": "vue-cli-service build --mode dev", "build:fat": "vue-cli-service build --mode fat", "build:uat": "vue-cli-service build --mode uat", "build:operator": "vue-cli-service build --mode operator", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "analyzer": "use_analyzer=true npm run serve"}, "dependencies": {"@enn/ennew-sdk": "^0.0.13", "@enncloud/enn-auth-sdk": "^0.2.5", "@highlightjs/vue-plugin": "^1.0.2", "@iconify-json/vscode-icons": "^1.2.22", "@iconify/vue2": "^2.1.0", "@kangc/v-md-editor": "^1.7.12", "@traptitech/markdown-it-katex": "^3.6.0", "axios": "^0.26.1", "babel-plugin-prismjs": "^2.1.0", "base64-js": "^1.5.1", "big-integer": "^1.6.51", "blockly": "^8.0.0", "calculate-size": "^1.1.1", "cherry-markdown": "^0.8.52", "clipboard": "^2.0.10", "core-js": "^3.8.3", "countup.js": "^2.2.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "docx-preview": "^0.1.15", "driver.js": "^0.9.8", "echarts": "^5.3.2", "element-ui": "^2.15.8", "esdk-obs-browserjs": "^3.23.5", "fabric": "^5.2.4", "file-saver": "2.0.5", "github-markdown-css": "^5.2.0", "highlight.js": "^10.7.3", "js-audio-recorder": "^1.0.7", "js-file-download": "^0.4.12", "jszip": "3.10.1", "katex": "^0.16.11", "lodash": "^4.17.21", "markdown-it": "^13.0.2", "markdown-it-link-attributes": "^4.0.1", "markdown-it-mermaid": "^0.2.5", "markmap-common": "^0.15.6", "markmap-lib": "^0.15.8", "markmap-view": "^0.15.8", "mermaid": "^10.9.0", "moment": "^2.29.4", "monaco-editor": "^0.33.0", "monaco-editor-webpack-plugin": "^7.0.1", "monaco-themes": "^0.4.1", "nano-assign": "^1.0.1", "nprogress": "^0.2.0", "panzoom": "^9.4.3", "prismjs": "^1.29.0", "register-service-worker": "^1.7.2", "sanitize-html": "^2.10.0", "socket.io-client": "^4.7.2", "streamsaver": "^2.0.6", "style-resources-loader": "^1.5.0", "swiper": "^8.2.4", "systemjs": "^6.9.0", "v-click-outside": "^3.2.0", "vditor": "^3.10.1", "view-design": "^4.7.0", "viewerjs": "^1.11.3", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-contextmenu": "^1.5.11", "vue-count-to": "^1.0.13", "vue-countup-v2": "^4.0.0", "vue-cropper": "^0.5.8", "vue-infinite-loading": "^2.0.0", "vue-json-viewer": "^2.2.22", "vue-markdown": "^2.2.4", "vue-pdf": "^4.3.0", "vue-resizer": "^1.1.9", "vue-router": "^3.5.1", "vuex": "^3.6.2", "skywalking-client-js": "^1.0.0", "web-streams-polyfill": "^3.2.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/test-utils": "^1.1.3", "@vue/vue2-jest": "^27.0.0-alpha.2", "babel-jest": "^27.0.6", "codemirror": "5.65.2", "error-stack-parser": "2.0.6", "eslint": "^7.32.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "hard-source-webpack-plugin": "^0.13.1", "jest": "^27.0.5", "less": "^3.0.4", "less-loader": "^6.0.0", "process": "^0.11.10", "qs": "^6.13.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "^6.0.11", "uuid": "^11.0.3", "vue-cli-plugin-pug": "^2.0.0", "vue-template-compiler": "^2.6.14", "skywalking-client-js": "^1.0.0", "webpack-bundle-analyzer": "^4.10.2"}, "resolutions": {"error-stack-parser": "2.0.6"}, "enpm": {"overrides": {"ws@^0": "^1"}}}