<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="referrer" content="no-referrer">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <script src="katex.min.js"></script>
  <script src="mermaid.min.js"></script>
  <script src="/eventsource.js"></script>
  <!-- <script defer src="https://cdn.fanneng.com/vendor/frontend-monitor-sdk/2.0/monitor.min.js"></script> -->
  <link rel="icon" href="favicon.ico"></link>
  <title>能力生产平台</title>
</head>

<body>
  <script type="systemjs-importmap" ignore>
    {
      "imports": {
        "vue": "<%= VUE_APP_VUEJS %>",
        "Vue": "<%= VUE_APP_VUEJS %>",
        "vue-router":"<%= VUE_APP_VUEROUTERJS %>",
        "element-ui":"<%= VUE_APP_ELEMENTJS %>",
        "ELEMENT":"<%= VUE_APP_ELEMENTJS %>"
      }
    }
    </script>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>

  <script src="./acorn_interpreter.js" ignore></script>
  <script src="./psychrolib.js" ignore></script>
  <script ignore>
    const psychrolib = new Psychrometrics()
    psychrolib.SetUnitSystem(psychrolib.SI)
  </script>
  <div id="app"></div>
</body>

</html>
