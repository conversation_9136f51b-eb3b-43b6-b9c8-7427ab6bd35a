NODE_ENV=production

VUE_APP_ENV=production

VUE_APP_API=https://rdfa-gateway.ennew.com/algorithm
VUE_APP_BACKEND_URL=https://ai-platform-backend.ennew.com
VUE_APP_OCR_API=https://rdfa-gateway.ennew.com/aipocr
VUE_APP_LLM_API=https://rdfa-gateway.ennew.com/ai-chat-backend/chat
VUE_APP_AGENT_API=https://algorithm.ennew.com/agent-api
VUE_APP_WEB_URL=https://algorithm.ennew.com
VUE_APP_ACCESS_URL=https://ai-llm-evaluation-front.ennew.com
VUE_APP_IMP_URL=https://air.ennew.com
VUE_APP_APM_ID=ai-platform-web

VUE_APP_AUTH_ID=ai-platform
VUE_APP_AUTH_ENV=NEW_PRO
VUE_APP_AUTH_URL=https://rdfa-gateway.ennew.com/algorithm

VUE_APP_GATEWAY_KEY=0DPhCpLwTjHPGLe88SjzLLtRXw4NGGLo
VUE_APP_GATEWAY_KEY_OCR=fVrSNtXW222ERxLYp2ImObCmvh91uhPI
VUE_APP_GATEWAY_KEY_LLM=dZl9xUDmcJlRc9eLTm68P7R8qNWRzKM1

VUE_APP_DATA_MARK_URL=https://ai-mark-web.ennew.com

# 方案生成接口地址
VUE_APP_PLAN_API=https://algorithm.ennew.com/gpts-api

VUE_APP_VUEJS=https://oss-statics.icomecloud.com/ai-platform-web/js/2.6.14-vue.min.js
VUE_APP_VUEROUTERJS=https://oss-statics.icomecloud.com/ai-platform-web/js/3.5.4.vue-router.min.js
VUE_APP_ELEMENTJS=https://oss-statics.icomecloud.com/ai-platform-web/js/2.15.8-element-ui.min.js

VUE_APP_RULEFLOW_ENGINEER=https://ai-ruleflow-engineer-front.ennew.com
VUE_APP_KA_CHAT=https://ai-ka-chat-front.ennew.com/#/solution
VUE_APP_RUN_EXTENSION=https://ai-run-extension-front.ennew.com
VUE_APP_AGENT=https://algorithm.dev.ennew.com/agent
VUE_APP_RULE_ENGINE=https://os-rule-engine-front.ennew.com
VUE_APP_MECHANISM_URL=https://ai-mechanism-front.ennew.com
VUE_APP_AI_CHAT = https://ai-chat-backend.ennew.com/chat
VUE_APP_OBS_UPLOAD_PATH = https://ai-chat-backend.ennew.com/chat/upload/uploadFileToObs

VUE_APP_KNOWLEDGE_API=https://rdfa-gateway.ennew.com/knowledge
VUE_APP_KNOWLEDGE_ACCESS_KEY=pUU2NrpnAb3TPjivhxfwdvn71XkZMzJL

VUE_APP_AGENT_URL=https://algorithm.ennew.com/agent
#外部访问 配置
VUE_APP_ENNEW_IFRAME=http://zhuanjia.juan.cn

# aip-search
VUE_APP_AIP_SEARCH_URL=https://algorithm.ennew.com/aip-search/index.html
# dify 外部接口
VUE_APP_AIP_DIFY_URL=/gpts-api