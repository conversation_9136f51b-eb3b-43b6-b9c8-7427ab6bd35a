# 算法平台 ai-platform-web

## 安装依赖

```bash
enpm i
```

### 本地开发

#### 连接dev环境接口服务
```bash
enpm serve
```

#### 连接自定义接口服务，如本地ip
先修改`.env.alpha`文件内`VUE_APP_API`变量值为对应服务地址，然后运行如下命令:

```bash
enpm serve:alpha
```

### 打包构建

```bash
dev - enpm build:dev
fat - enpm build:fat
uat - enpm build:uat
prod - enpm build
```

### 运行测试

```bash
npm run test
```

### lint

```bash
npm run lint
```

流量dev地址
https://ai-platform-backend-multimodel.dev.ennew.com/algorithm

https://rdfa-gateway.dev.ennew.com/algorithm

### 全局面包屑组件`GlobalBreadcrumb`
配置项
```javascript
const route = {
    path: '/foobar',
    name: 'foobar',
    component: foobarComponent,
    meta: {
      title: '示例',

      // 是否使用组件内自定义配置，等同于breadcrumbData=[] || null || undefined
      breadcrumbOverwrite: true,
      
      // 自定义生成
      breadcrumbGenerator () {
        const route = router.history.current

        return [
          {
            label: '算法工程'
          },
          {
            label: '训练任务',
            route: {
              path: '/trainList',
              query: {
                algorithmProjectId: route.query.algorithmProjectId,
                type: route.query.type,
                projectName: route.query.projectName
              }
            }
          },
          {
            label: '创建模型训练任务'
          }
        ]
      },
      
      // 直接通过数据生成
      breadcrumbData: [
        {
          label: 'foobarList'
        },
        {
          label: 'foobar'
        }
      ]
    }
  }
```
